# 🔧 Troubleshooting - StreamPlus España

## Problema: "moc ainda" - Player mostra vídeo mock em vez de streams reais

### 🔍 Diagnóstico Rápido

1. **Ativar modo DEBUG**:
   ```bash
   # Em .env.local
   DEBUG=true
   NEXT_PUBLIC_DEBUG=true
   ```

2. **Acessar página de debug**:
   - Abrir http://localhost:3000/debug
   - Clicar em "Test APIs" para verificar se IPTV está funcionando

3. **Verificar logs no console**:
   ```javascript
   // No browser console (F12)
   window.__logger.getHistory('player')
   ```

### 📋 Checklist de Resolução

#### 1. Verificar URLs no Player
- Procurar por logs com `player:url` no console
- Verificar se está carregando URL real `.m3u8` ou fallback

#### 2. Testar Streams Diretamente
```bash
# Rodar script de teste
pnpm tsx scripts/test-streams.ts
```

#### 3. Verificar Fluxo Completo
- API IPTV retorna dados? ✅ (423 canais)
- Canal individual carrega? Verificar em `/api/iptv/channel/[id]`
- Player recebe URL correta? Checar logs `UniversalPlayer mounted`
- HLS inicializa? Procurar `Setting up HLS for stream`

### 🚨 Problemas Comuns

#### CORS Errors
- **Sintoma**: Erro ao carregar .m3u8
- **Solução**: Alguns streams precisam de proxy CORS ou headers especiais

#### 403 Forbidden
- **Sintoma**: Stream retorna 403
- **Solução**: Stream pode precisar de Referrer ou User-Agent específico (já implementado)

#### Network Error
- **Sintoma**: ERR_NETWORK no HLS
- **Solução**: Stream pode estar offline ou geo-bloqueado

### 🛠️ Soluções Rápidas

1. **Forçar reload sem cache**:
   ```bash
   # Limpar cache e reiniciar
   rm -rf .next
   pnpm dev
   ```

2. **Testar com stream conhecido**:
   ```javascript
   // Em /debug, abrir console e testar:
   const testUrl = 'https://test-streams.mux.dev/x36xhzz/x36xhzz.m3u8'
   window.location.href = `/watch/iptv-test?url=${encodeURIComponent(testUrl)}`
   ```

3. **Verificar canal específico**:
   ```bash
   # Testar API de canal
   curl http://localhost:3000/api/iptv/channel/espn-brasil.br
   ```

### 📊 Logs Importantes

Procurar estes logs em ordem:

1. `[iptv] API data received` - Confirma que API carregou
2. `[stream] IPTV channel data loaded` - Canal foi encontrado
3. `[player] UniversalPlayer mounted` - Player inicializou
4. `[player] Loading HLS source` - HLS começou a carregar
5. `[player] HLS manifest parsed` - Stream está funcionando

### 🔄 Fluxo Esperado

```
Usuário clica no canal
    ↓
Watch page carrega
    ↓
Busca dados em /api/iptv/channel/[id]
    ↓
API retorna URL real do stream
    ↓
UniversalPlayer recebe streamUrl
    ↓
HLS.js carrega o .m3u8
    ↓
Vídeo real começa a tocar
```

### 💡 Dica Final

Se tudo mais falhar, verificar:
1. O canal tem URL válida? (não todos têm)
2. A URL está acessível do Brasil?
3. O stream está online no momento?

Use o script `test-streams.ts` para encontrar canais que definitivamente funcionam e testar com eles primeiro.