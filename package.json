{"name": "newspports", "version": "0.1.0", "private": true, "engines": {"node": ">=18.0.0"}, "scripts": {"dev": "next dev --turbopack", "build": "npm run build:next", "build:next": "next build", "start": "next start", "lint": "next lint", "reset-quotas": "tsx src/scripts/reset-quotas.ts"}, "dependencies": {"@fingerprintjs/fingerprintjs": "^4.6.2", "@hookform/resolvers": "^5.1.1", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@stackframe/stack": "^2.8.22", "@stripe/stripe-js": "^7.6.1", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.52.0", "@types/canvas-confetti": "^1.9.0", "@types/jsonwebtoken": "^9.0.10", "@types/qrcode": "^1.5.5", "@types/qrcode.react": "^3.0.0", "canvas-confetti": "^1.9.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "hls.js": "^1.6.7", "immer": "^10.1.1", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.525.0", "next": "15.4.2", "next-themes": "^0.4.6", "pg": "^8.16.3", "qrcode": "^1.5.4", "qrcode.react": "^4.2.0", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.60.0", "react-player": "^3.3.1", "sonner": "^2.0.6", "stripe": "^18.3.0", "swr": "^2.3.4", "tailwind-merge": "^3.3.1", "uuid": "^11.0.6", "zod": "^4.0.5", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/uuid": "^10.0.0", "axios": "^1.11.0", "dotenv": "^17.2.1", "eslint": "^9", "eslint-config-next": "15.4.2", "hls-parser": "^0.13.6", "node-fetch": "^3.3.2", "playwright": "^1.54.1", "puppeteer": "^24.15.0", "tailwindcss": "^4", "tsx": "^4.20.3", "tw-animate-css": "^1.3.5", "typescript": "^5"}}