# 🎯 CONFIGURAÇÃO DO GOOGLE CONSOLE OAUTH

## 📋 O QUE ADICIONAR EM CADA CAMPO:

### 1️⃣ **Origens JavaScript autorizadas**

Adicione TODAS estas URLs:

```
https://newspports-production.up.railway.app
https://api.stack-auth.com
http://localhost:3000
```

⚠️ **IMPORTANTE**: 
- SEM barra (/) no final
- Uma por linha
- Clique em "+ ADICIONAR URI" para cada uma

### 2️⃣ **URIs de redirecionamento autorizados**

Adicione TODAS estas URLs:

```
https://api.stack-auth.com/api/v1/auth/oauth/callback/google
https://newspports-production.up.railway.app/handler/oauth-callback
http://localhost:3000/handler/oauth-callback
```

⚠️ **IMPORTANTE**:
- COM o caminho completo
- Uma por linha
- A primeira é a MAIS IMPORTANTE (Stack Auth usa ela)

## 📸 VISUAL NO GOOGLE CONSOLE:

```
Origens JavaScript autorizadas
┌─────────────────────────────────────────────────┐
│ https://newspports-production.up.railway.app    │
├─────────────────────────────────────────────────┤
│ https://api.stack-auth.com                      │
├─────────────────────────────────────────────────┤
│ http://localhost:3000                           │
└─────────────────────────────────────────────────┘
[+ ADICIONAR URI]

URIs de redirecionamento autorizados
┌─────────────────────────────────────────────────┐
│ https://api.stack-auth.com/api/v1/auth/oauth... │
├─────────────────────────────────────────────────┤
│ https://newspports-production.up.railway.app... │
├─────────────────────────────────────────────────┤
│ http://localhost:3000/handler/oauth-callback    │
└─────────────────────────────────────────────────┘
[+ ADICIONAR URI]
```

## 🔍 PASSO A PASSO COMPLETO:

1. **Acesse o Google Cloud Console**
   - https://console.cloud.google.com

2. **Navegue para:**
   - APIs e serviços → Credenciais

3. **Clique no seu OAuth 2.0 Client ID**

4. **Em "Origens JavaScript autorizadas":**
   - Clique "+ ADICIONAR URI"
   - Cole: `https://newspports-production.up.railway.app`
   - Clique "+ ADICIONAR URI" novamente
   - Cole: `https://api.stack-auth.com`
   - Clique "+ ADICIONAR URI" novamente
   - Cole: `http://localhost:3000`

5. **Em "URIs de redirecionamento autorizados":**
   - Clique "+ ADICIONAR URI"
   - Cole: `https://api.stack-auth.com/api/v1/auth/oauth/callback/google`
   - Clique "+ ADICIONAR URI" novamente
   - Cole: `https://newspports-production.up.railway.app/handler/oauth-callback`
   - Clique "+ ADICIONAR URI" novamente
   - Cole: `http://localhost:3000/handler/oauth-callback`

6. **Clique em "SALVAR"**

## ⏱️ IMPORTANTE:

- Pode levar até **5 minutos** para as mudanças propagarem
- Se der erro, aguarde e tente novamente

## ✅ CHECKLIST FINAL:

- [ ] 3 origens JavaScript adicionadas
- [ ] 3 URIs de redirecionamento adicionados
- [ ] Clicou em SALVAR
- [ ] Aguardou 5 minutos

## 🎊 PRONTO!

Após salvar e aguardar a propagação, o login com Google deve funcionar tanto em produção quanto em desenvolvimento!

## 🆘 SE DER ERRO:

1. Verifique se não tem espaços extras
2. Verifique se não tem barra (/) extra no final das origens
3. Confirme que salvou as alterações
4. Tente limpar o cache do navegador