// Script simplificado para verificar o player

console.clear();
console.log('🔍 VERIFICANDO PLAYER...\n');

// 1. Verificar se o logger existe
if (typeof window.__logger === 'undefined') {
    console.log('⚠️ Logger não está disponível. Verificando diretamente...\n');
} else {
    const logs = window.__logger.getHistory('player');
    console.log('📊 Logs do player:', logs.length);
}

// 2. Verificar vídeos na página
const videos = document.querySelectorAll('video');
console.log('🎥 Vídeos encontrados:', videos.length);

if (videos.length > 0) {
    const video = videos[0];
    console.log('\n📺 Estado do vídeo:');
    console.log('- URL:', video.currentSrc || video.src || 'NENHUMA');
    console.log('- Pausado:', video.paused);
    console.log('- Muted:', video.muted);
    console.log('- Ready:', video.readyState, '(4 = pronto)');
    console.log('- Network:', video.networkState);
    console.log('- Erro:', video.error);
    console.log('- Duração:', video.duration);
    
    // Se não tem URL, há um problema
    if (!video.src && !video.currentSrc) {
        console.log('\n❌ PROBLEMA: Vídeo sem URL!');
    }
    
    // Tentar tocar
    if (video.paused && video.src) {
        console.log('\n▶️ Tentando tocar...');
        video.muted = true; // Garantir muted para autoplay
        video.play()
            .then(() => console.log('✅ Tocando!'))
            .catch(e => console.log('❌ Erro:', e.message));
    }
} else {
    console.log('\n❌ Nenhum vídeo encontrado na página!');
}

// 3. Verificar botão de play
const playButtons = document.querySelectorAll('button');
let playButton = null;

playButtons.forEach(btn => {
    if (btn.querySelector('svg') || btn.textContent.includes('play')) {
        playButton = btn;
    }
});

if (playButton) {
    console.log('\n🎯 Botão de play encontrado!');
    console.log('👉 Clique nele ou execute:');
    console.log('   playButton.click()');
    
    // Salvar referência global
    window.playButton = playButton;
} else {
    console.log('\n⚠️ Nenhum botão de play visível');
}

// 4. Verificar se há erro de CORS
console.log('\n🌐 Verificando possíveis problemas:');
console.log('- ERR_CONNECTION_REFUSED indica que o servidor parou');
console.log('- Reinicie o servidor: npm run dev');
console.log('- Ou tente: window.location.reload()');

// 5. Informações da página
console.log('\n📍 Página atual:', window.location.pathname);
console.log('🎬 Canal:', window.location.pathname.split('/').pop());