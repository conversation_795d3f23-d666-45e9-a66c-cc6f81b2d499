# 🚀 PASSO A PASSO COMPLETO - CONFIGURAR RAILWAY

## 📋 PASSO 1: Adicionar Variável PORT no Railway

1. **Acesse o Railway**
   - Entre em: https://railway.app
   - Faça login na sua conta

2. **Vá para seu projeto**
   - Clique no projeto "newspports" (ou nome do seu projeto)

3. **<PERSON><PERSON> as Variáveis de Ambiente**
   - Clique na aba **"Variables"**
   - Ou clique no serviço e depois em **"Variables"**

4. **Adicione a variável PORT**
   - Clique em **"+ New Variable"**
   - Nome: `PORT`
   - Valor: `3000`
   - Clique em **"Add"**

## 📋 PASSO 2: Atualizar NEXT_PUBLIC_APP_URL

1. **Encontre a URL do seu app no Railway**
   - Na página do projeto, procure por **"Deployment URL"**
   - Será algo como: `https://newspports-production.up.railway.app`
   - Copie essa URL

2. **Atualize a variável NEXT_PUBLIC_APP_URL**
   - <PERSON><PERSON> Variables, encontre `NEXT_PUBLIC_APP_URL`
   - Clique no ícone de editar (lápis)
   - Cole a URL que você copiou
   - Clique em **"Save"**

## 📋 PASSO 3: Configurar Stack Auth

### 3.1 - No Dashboard do Stack Auth

1. **Acesse Stack Auth**
   - Entre em: https://app.stack-auth.com
   - Faça login

2. **Vá para seu projeto**
   - Clique no projeto que você criou

3. **Configure os Redirect URIs**
   - Vá em **Settings > OAuth Providers**
   - Para cada provider (Google, GitHub, etc):
     - Clique em **"Edit"**
     - Em **"Redirect URI"**, adicione:
       ```
       https://newspports-production.up.railway.app/handler/oauth-callback
       ```
     - Em **"Allowed Origins"**, adicione:
       ```
       https://newspports-production.up.railway.app
       ```
     - Clique em **"Save"**

### 3.2 - Verificar as Chaves no Railway

1. **No Railway, verifique estas variáveis**:
   - `NEXT_PUBLIC_STACK_PROJECT_ID` = ID do projeto no Stack Auth
   - `NEXT_PUBLIC_STACK_PUBLISHABLE_CLIENT_KEY` = Publishable key do Stack Auth
   - `STACK_SECRET_SERVER_KEY` = Secret key do Stack Auth

## 📋 PASSO 4: Configurar Google OAuth (se estiver usando)

1. **Acesse o Google Cloud Console**
   - Entre em: https://console.cloud.google.com

2. **Vá para Credentials**
   - Menu lateral > APIs & Services > Credentials

3. **Edite seu OAuth Client**
   - Clique no client OAuth que você criou

4. **Adicione as URIs de produção**
   - Em **"Authorized JavaScript origins"**, adicione:
     ```
     https://newspports-production.up.railway.app
     ```
   - Em **"Authorized redirect URIs"**, adicione:
     ```
     https://newspports-production.up.railway.app/handler/oauth-callback
     ```
   - Clique em **"Save"**

## 📋 PASSO 5: Remover Variável Duplicada (Opcional)

1. **No Railway Variables**
   - Delete uma dessas (são duplicadas):
     - `ALLSPORTDB_API_KEY`
     - `ALLSPORT_DB_KEY`
   - Mantenha apenas uma

## 📋 PASSO 6: Fazer Deploy

1. **Opção A - Deploy Automático**
   - O Railway deve fazer deploy automaticamente após salvar as variáveis
   - Aguarde alguns minutos

2. **Opção B - Deploy Manual**
   - Se não iniciar automaticamente:
   - Clique em **"Deploy"** ou **"Redeploy"**
   - Ou faça um commit vazio:
     ```bash
     git commit --allow-empty -m "Trigger deploy"
     git push
     ```

## 📋 PASSO 7: Verificar se Funcionou

1. **Aguarde o deploy completar**
   - Veja o status em "Deployments"
   - Deve mostrar "Success" em verde

2. **Acesse seu site**
   - Abra: `https://newspports-production.up.railway.app`
   - Tente fazer login com Google

3. **Se houver erro, verifique os logs**
   - No Railway, clique em **"View Logs"**
   - Procure por erros relacionados a Stack Auth

## 🔧 TROUBLESHOOTING

### Se o erro de Stack Auth continuar:

1. **Verifique no browser (F12 > Console)**:
   - As variáveis NEXT_PUBLIC estão chegando?
   - Execute: `console.log(process.env.NEXT_PUBLIC_STACK_PROJECT_ID)`

2. **Limpe o cache do build**:
   - No Railway, vá em Settings
   - Clique em "Clear build cache"
   - Faça novo deploy

3. **Verifique se as variáveis estão corretas**:
   ```
   NEXT_PUBLIC_STACK_PROJECT_ID=prj_... (começa com prj_)
   NEXT_PUBLIC_STACK_PUBLISHABLE_CLIENT_KEY=pck_... (começa com pck_)
   STACK_SECRET_SERVER_KEY=ssk_... (começa com ssk_)
   ```

## ✅ CHECKLIST FINAL

- [ ] PORT=3000 adicionada
- [ ] NEXT_PUBLIC_APP_URL atualizada com URL do Railway
- [ ] Stack Auth redirect URIs configurados
- [ ] Google OAuth URIs atualizadas (se usando)
- [ ] Deploy realizado
- [ ] Site acessível
- [ ] Login funcionando

## 📞 PRECISA DE AJUDA?

Se algo não funcionar, me envie:
1. Screenshot do erro
2. Logs do Railway
3. Console do browser (F12)