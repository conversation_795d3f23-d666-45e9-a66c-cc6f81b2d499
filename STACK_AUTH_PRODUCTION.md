# Configuração do Stack Auth em Produção

## Erro Encontrado
```
INVALID_OAUTH_CLIENT_ID_OR_SECRET
```

## Causa
As variáveis de ambiente do Stack Auth não estão configuradas corretamente no Railway.

## Solução

### 1. Variáveis de Ambiente Necessárias no Railway

Adicione estas variáveis no dashboard do Railway:

```bash
# Stack Auth - ESSENCIAIS
NEXT_PUBLIC_STACK_PROJECT_ID=<seu-project-id-do-stack>
NEXT_PUBLIC_STACK_PUBLISHABLE_CLIENT_KEY=<sua-publishable-key>
STACK_SECRET_SERVER_KEY=<sua-secret-key>

# URLs da Aplicação
NEXT_PUBLIC_APP_URL=https://seu-dominio-railway.up.railway.app
```

### 2. Onde Encontrar as Chaves

1. Acesse o [Dashboard do Stack Auth](https://app.stack-auth.com)
2. Vá para seu projeto
3. Em **Settings > API Keys**:
   - `Project ID`: Use como `NEXT_PUBLIC_STACK_PROJECT_ID`
   - `Publishable client key`: Use como `NEXT_PUBLIC_STACK_PUBLISHABLE_CLIENT_KEY`
   - `Secret server key`: Use como `STACK_SECRET_SERVER_KEY`

### 3. Configurar OAuth Redirect URIs

No dashboard do Stack Auth:

1. Vá para **Settings > OAuth Providers**
2. Para cada provider (Google, etc), adicione:
   - Redirect URI: `https://seu-dominio-railway.up.railway.app/handler/oauth-callback`
   - Allowed Origins: `https://seu-dominio-railway.up.railway.app`

### 4. Verificar no Railway

1. Vá para o projeto no Railway
2. Clique em **Variables**
3. Adicione todas as variáveis mencionadas
4. Clique em **Deploy** para aplicar as mudanças

### 5. Teste Local vs Produção

O código detecta automaticamente se está em desenvolvimento e usa credenciais de teste. Em produção, sempre usa o Stack Auth real.

## Checklist de Deploy

- [ ] Todas as variáveis de ambiente configuradas no Railway
- [ ] OAuth redirect URIs configurados no Stack Auth
- [ ] NEXT_PUBLIC_APP_URL aponta para o domínio correto
- [ ] Rebuild/redeploy após adicionar variáveis

## Debugging

Se continuar com erro:
1. Verifique os logs no Railway
2. Confirme que as variáveis começam com `NEXT_PUBLIC_` estão disponíveis no cliente
3. Teste acessando: `https://seu-app.railway.app/api/auth/check`