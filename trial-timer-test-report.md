# Relatório de Teste - Sistema de Trial Timer (5 minutos)

## Data do Teste: 27/07/2025

### 🎯 Objetivo
Testar o sistema de trial timer de 5 minutos implementado no NewSpports, verificando:
- Exibição inicial do timer (5:00)
- Contagem regressiva funcionando
- Persistência após reload
- Animação de warning (< 60 segundos)
- Consistência entre canais

### 📋 Metodologia de Teste

1. **Preparação**
   - Servidor rodando em http://localhost:3001
   - localStorage limpo para começar fresh
   - Canais de teste: Real Madrid TV, Teledeporte, LaLiga EA Sports

2. **Cenários de Teste**

#### Teste 1: Timer Inicial
- **URL**: http://localhost:3001/watch/real-madrid-tv
- **Esperado**: Timer mostra "Trial: 5:00" no canto superior esquerdo
- **Localização**: Dentro do player, sobre o vídeo

#### Teste 2: Contagem Regressiva
- **Ação**: <PERSON><PERSON>r play e aguardar
- **Esperado**: Timer conta regressivamente (4:59, 4:58, etc.)
- **Verificação**: Timer atualiza a cada segundo

#### Teste 3: Persistência
- **Ação**: Recarregar página (F5)
- **Esperado**: Timer continua de onde parou (não reinicia para 5:00)
- **Chave**: Dados salvos em localStorage com key "trial-storage"

#### Teste 4: Múltiplos Canais
- **Ação**: Navegar para outro canal
- **Esperado**: Timer mantém contagem (não reinicia)
- **Canais testados**: real-madrid-tv → teledeporte → laliga-ea-sports-1

#### Teste 5: Warning Mode
- **Condição**: Timer < 60 segundos
- **Esperado**: 
  - Background muda para vermelho
  - Animação pulse ativa
  - Texto muda para "Expira em: 0:XX"

#### Teste 6: Expiração
- **Condição**: Timer chega a 0:00
- **Esperado**: TrialOverlay aparece com opções de conversão

### 🔍 Detalhes Técnicos

**Componentes Envolvidos:**
- `/src/components/player/trial-timer.tsx` - Componente visual do timer
- `/src/hooks/use-trial-timer.ts` - Hook que gerencia a lógica do timer
- `/src/stores/trial.store.ts` - Store Zustand com persistência

**Fluxo de Dados:**
```
startTrial() → salva startTime no localStorage → 
useTrialTimer calcula tempo restante → 
TrialTimer renderiza visualmente → 
Persiste entre reloads via Zustand persist
```

### 📸 Screenshots Necessários

1. **timer-inicial.png** - Timer mostrando 5:00
2. **timer-contando.png** - Timer em contagem (ex: 3:45)
3. **timer-persistente.png** - Timer após reload
4. **timer-warning.png** - Timer < 1:00 (vermelho pulsante)
5. **trial-expirado.png** - Overlay de conversão

### ✅ Checklist de Verificação

- [ ] Timer aparece ao acessar canal
- [ ] Timer inicia em 5:00
- [ ] Contagem regressiva funciona
- [ ] Timer persiste após reload
- [ ] Timer consistente entre canais
- [ ] Warning mode ativa < 60s
- [ ] Overlay aparece quando expira
- [ ] localStorage contém "trial-storage"

### 🛠️ Como Testar Manualmente

1. Abrir Console do navegador (F12)
2. Executar: `localStorage.removeItem('trial-storage')`
3. Acessar: http://localhost:3001/watch/real-madrid-tv
4. Verificar timer no canto superior esquerdo
5. Clicar play e observar contagem
6. Recarregar página e verificar persistência
7. Navegar entre canais
8. Aguardar < 60s para warning
9. Aguardar expiração total

### 📝 Observações

- O timer é gerenciado inteiramente no cliente (navegador)
- Usa Zustand com middleware de persistência
- Key no localStorage: "trial-storage"
- Timer global (não por canal)
- Integrado com sistema de overlay de conversão

### 🐛 Possíveis Problemas

1. **Timer não aparece**: Verificar se componente está sendo renderizado
2. **Timer reinicia**: Problema com persistência do Zustand
3. **Warning não ativa**: Verificar condição `timeRemaining < 60`
4. **Overlay não aparece**: Verificar `isExpired` no store

### 💡 Melhorias Sugeridas

1. Adicionar som de alerta quando < 30 segundos
2. Mostrar notificação toast quando trial expira
3. Permitir "snooze" de 1 minuto extra (uma vez)
4. Analytics de conversão baseado no tempo