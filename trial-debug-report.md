# Relatório de Debug do Sistema de Trial

## Mudanças Implementadas

### 1. Logs Extensivos Adicionados

Adicionei logs com prefixos claros em todos os pontos críticos do sistema:

- **[TRIAL-STORE]** - Store do trial (`src/stores/trial.store.ts`)
  - Log quando `checkTrialStatus` é chamado
  - Log quando `startTrial` é iniciado
  - Log quando novo sessionId é gerado
  - Log quando trial expira
  - Log na rehidratação do localStorage
  
- **[TRIAL-TIMER]** - Hook do timer (`src/hooks/use-trial-timer.ts`)
  - Log a cada atualização do timer
  - Log quando não há startTime
  - Log quando timer atinge 0

- **[TRIAL-PLAYER]** - Componentes do player
  - Log quando componente é montado
  - Log quando trial expira
  - Log de verificação de acesso

- **[ACCESS-STORE]** - Store de acesso (`src/stores/access.store.ts`)
  - Log de verificação de acesso
  - Log de resultados da API

- **[TRIAL-TIMER-COMPONENT]** - Componente visual do timer
  - Log a cada render com estado atual

### 2. Melhorias no Sistema de Trial

1. **Auto-reset após 24 horas**: Se o trial expirou há mais de 24 horas, ele será resetado automaticamente permitindo novo trial.

2. **Verificação na rehidratação**: Ao carregar dados do localStorage, o sistema agora:
   - Verifica se o trial deveria ter expirado durante o período offline
   - Atualiza o tempo restante corretamente
   - Reseta trials muito antigos

3. **Componente de Debug**: Adicionei um componente visual (`src/components/debug/trial-debug.tsx`) que mostra:
   - Estado atual do trial em tempo real
   - Estado de acesso
   - Tempo decorrido
   - Botões para limpar storage e resetar trial

### 3. Script de Debug

Criei um script (`scripts/debug-trial-system.ts`) que:
- Limpa localStorage para simular novo usuário
- Monitora mudanças no estado do trial
- Detecta expirações prematuras
- Mantém navegador aberto para análise

## Como Debugar o Problema

### 1. Executar com Console Aberto

```bash
pnpm dev
```

Abra o navegador em `http://localhost:3000` com o DevTools aberto na aba Console.

### 2. Simular Novo Usuário

1. Abra o DevTools
2. Vá para Application > Storage > Local Storage
3. Delete todas as entradas
4. Recarregue a página
5. Navegue para `/watch/[channelId]`

### 3. Observar os Logs

Procure por estes padrões nos logs:

```
[TRIAL-STORE] startTrial called
[TRIAL-STORE] Initial state set
[TRIAL-TIMER] Timer update
[TRIAL-PLAYER] Component mounted
```

### 4. Verificar Problemas Comuns

#### Problema 1: Trial expira imediatamente
Verifique se:
- `isExpired` está sendo setado como `true` logo após `startTrial`
- `startTime` está sendo definido corretamente
- Não há dados antigos no localStorage

#### Problema 2: Timer não inicia
Verifique se:
- `startTime` está `null`
- `checkTrialStatus` está retornando erro
- Hook `useTrialTimer` está sendo montado

#### Problema 3: Bloqueio prematuro
Verifique se:
- `hasAccess` está sempre `false`
- `checkAccess` está falhando
- Condição de bloqueio (`isExpired && !hasAccess`) está correta

### 5. Usar o Componente de Debug

No canto inferior direito da tela (apenas em desenvolvimento), você verá uma caixa preta com informações do trial:
- Estado atual em tempo real
- Botões para limpar storage e resetar

## Possíveis Causas do Problema

1. **localStorage com dados antigos**: Trial expirado de sessão anterior
2. **Erro na API**: `checkTrialStatus` ou `checkAccess` falhando
3. **Race condition**: Estado sendo atualizado antes do componente montar
4. **Problema de rehidratação**: Zustand carregando estado incorreto

## Próximos Passos

1. Execute o sistema com os novos logs
2. Identifique exatamente onde `isExpired` está sendo setado como `true`
3. Verifique se o problema acontece apenas com novos usuários ou também com usuários existentes
4. Compartilhe os logs capturados para análise mais profunda

## Comandos Úteis

```bash
# Limpar todo cache e storage do navegador
# No DevTools: Application > Storage > Clear site data

# Executar script de debug
pnpm tsx scripts/debug-trial-system.ts

# Ver logs filtrados no console
# Digite no console: [TRIAL-
```