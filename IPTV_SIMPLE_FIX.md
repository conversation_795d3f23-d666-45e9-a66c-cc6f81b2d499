# Correção do Endpoint /api/iptv-simple

## Data: 2025-07-26

## Problema
O site estava mostrando 134 canais quando deveria mostrar apenas 69 canais selecionados.

## Causa
O endpoint `/api/iptv-simple` não estava aplicando o filtro dos canais selecionados e também estava incluindo múltiplos streams do mesmo canal (duplicatas).

## Solução Implementada

### 1. Aplicação do filtro de canais selecionados
- Importado `isSelectedSportsChannel` de `/src/lib/selected-sports-channels.ts`
- Filtrado streams para incluir apenas canais na lista dos 69 selecionados

### 2. Remoção de duplicatas
- Implementado Map para manter apenas um stream por canal
- Usado o primeiro stream encontrado para cada canal único

### 3. Código atualizado em `/src/app/api/iptv-simple/route.ts`:
```typescript
// Aplicar filtro dos 69 canais selecionados
const selectedStreams = sportsStreams.filter(stream => 
  isSelectedSportsChannel(stream.channel)
)

// Agrupar por canal e pegar apenas o primeiro stream de cada canal
const uniqueChannels = new Map<string, typeof selectedStreams[0]>()
selectedStreams.forEach(stream => {
  if (!uniqueChannels.has(stream.channel)) {
    uniqueChannels.set(stream.channel, stream)
  }
})

// Enriquecer sem limite adicional
const enriched = Array.from(uniqueChannels.values()).map(stream => {
  // ... enriquecimento dos dados
})
```

## Resultado
- **Esperado**: 69 canais
- **Retornado**: 68 canais
- **Canal faltante**: AntenaSport.hr (não existe na API IPTV.org ou não tem categoria 'sports')

## Verificação
```bash
# Verificar número de canais retornados
curl -s http://localhost:3000/api/iptv-simple | jq '.total'
# Resultado: 68

# Verificar canais únicos
curl -s http://localhost:3000/api/iptv-simple | jq '.data | map(.channel) | unique | length'
# Resultado: 68
```

## Impacto
A página browse agora mostra corretamente apenas os 68 canais selecionados ao invés dos 134 anteriores.