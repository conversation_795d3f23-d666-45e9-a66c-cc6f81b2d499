-- Script ATUALIZADO para remover APENAS canais com erro 403 confirmado
-- Total: 31 canais (muito menos que os 165 originais!)
-- G<PERSON>do após teste com Chrome real

BEGIN;

-- Remover apenas canais com erro 403 (proteção server-side)
DELETE FROM channels
WHERE id IN (
  'ACLCornholeTV.us',
  'AlfaSport.cy',
  'beINSPORTSXTRAenEspanol.us',
  'CBCSport.az',
  'EDGEsport.uk',
  'Esport3.es',
  'FastSports.am',
  'FoxSports3.ar',
  'GAORA.jp',
  'GolaTV.es',
  'GTVSportsPlus.gh',
  'HardKnocks.ca',
  'HTVSports.vn',
  'InfinityTVSports.pt',
  'MBCSports.sa',
  'MLB.us',
  'MUTV.uk',
  'ONTime1.eg',
  'ONTime2.eg',
  'ONTime3.eg',
  'Rugby1.sk',
  'Rugby2.sk',
  'Sportal.bg',
  'SportKlub1.rs',
  'SportKlub2.rs',
  'SportKlub3.rs',
  'STIRRSports.us',
  'SuperSport1.rs',
  'SuperSport2.rs',
  'SuperSport3.rs',
  'TSports.bd'
);

-- Verificar quantos foram removidos
SELECT 'Canais removidos:' as info, COUNT(*) as total
FROM channels 
WHERE id IN (
  'ACLCornholeTV.us', 'AlfaSport.cy', 'beINSPORTSXTRAenEspanol.us',
  'CBCSport.az', 'EDGEsport.uk', 'Esport3.es', 'FastSports.am',
  'FoxSports3.ar', 'GAORA.jp', 'GolaTV.es', 'GTVSportsPlus.gh',
  'HardKnocks.ca', 'HTVSports.vn', 'InfinityTVSports.pt', 'MBCSports.sa',
  'MLB.us', 'MUTV.uk', 'ONTime1.eg', 'ONTime2.eg', 'ONTime3.eg',
  'Rugby1.sk', 'Rugby2.sk', 'Sportal.bg', 'SportKlub1.rs', 'SportKlub2.rs',
  'SportKlub3.rs', 'STIRRSports.us', 'SuperSport1.rs', 'SuperSport2.rs',
  'SuperSport3.rs', 'TSports.bd'
);

COMMIT;

-- Estatísticas após remoção
SELECT 
  'Total de canais restantes:' as info,
  COUNT(*) as total,
  COUNT(CASE WHEN category = 'sports' THEN 1 END) as esportes
FROM channels;

-- NOTA IMPORTANTE:
-- Este script remove APENAS 31 canais com erro 403 confirmado
-- Os 87 canais com "erro de codec" foram mantidos pois funcionam no Chrome real
-- Os outros 134 canais precisam ser re-testados antes de decidir removê-los