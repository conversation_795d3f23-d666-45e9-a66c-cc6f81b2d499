<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste do Trial Timer</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .warning { background: #fff3cd; color: #856404; }
        .info { background: #d1ecf1; color: #0c5460; }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            background: #007bff;
            color: white;
        }
        button:hover { background: #0056b3; }
        iframe {
            width: 100%;
            height: 600px;
            border: 2px solid #ddd;
            border-radius: 8px;
        }
        .timer-info {
            font-size: 24px;
            font-weight: bold;
            padding: 20px;
            text-align: center;
            background: #333;
            color: white;
            border-radius: 8px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>🧪 Teste do Sistema de Trial Timer - NewSpports</h1>
    
    <div class="test-section">
        <h2>📊 Status do Trial</h2>
        <div id="trial-status" class="status info">Carregando...</div>
        <div id="timer-display" class="timer-info">--:--</div>
        
        <button onclick="clearTrial()">🧹 Limpar Trial</button>
        <button onclick="checkStatus()">🔄 Atualizar Status</button>
    </div>
    
    <div class="test-section">
        <h2>🎬 Player de Teste</h2>
        <p>Canal: Real Madrid TV</p>
        <iframe id="player-frame" src="http://localhost:3001/watch/real-madrid-tv"></iframe>
        
        <div style="margin-top: 20px;">
            <button onclick="loadChannel('real-madrid-tv')">📺 Real Madrid TV</button>
            <button onclick="loadChannel('teledeporte')">📺 Teledeporte</button>
            <button onclick="loadChannel('laliga-ea-sports-1')">📺 LaLiga EA Sports</button>
            <button onclick="reloadFrame()">🔄 Recarregar Player</button>
        </div>
    </div>
    
    <div class="test-section">
        <h2>📝 Log de Testes</h2>
        <div id="test-log" style="background: #f8f9fa; padding: 15px; border-radius: 4px; font-family: monospace; font-size: 12px; max-height: 300px; overflow-y: auto;">
            Aguardando testes...
        </div>
    </div>
    
    <script>
        let testLog = [];
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString('pt-BR');
            const logEntry = `[${timestamp}] ${message}`;
            testLog.push(logEntry);
            
            const logDiv = document.getElementById('test-log');
            logDiv.innerHTML = testLog.join('<br>');
            logDiv.scrollTop = logDiv.scrollHeight;
            
            console.log(logEntry);
        }
        
        function checkStatus() {
            try {
                // Acessar localStorage do iframe
                const frame = document.getElementById('player-frame');
                const frameWindow = frame.contentWindow;
                
                // Tentar ler o estado do trial
                const trialData = frameWindow.localStorage.getItem('trial-storage');
                
                if (trialData) {
                    const trial = JSON.parse(trialData);
                    const state = trial.state;
                    
                    const statusDiv = document.getElementById('trial-status');
                    const timerDiv = document.getElementById('timer-display');
                    
                    if (state.isExpired) {
                        statusDiv.className = 'status error';
                        statusDiv.textContent = '❌ Trial Expirado';
                        timerDiv.textContent = '0:00';
                        log('Trial expirado!', 'error');
                    } else if (state.startTime) {
                        const elapsed = Math.floor((Date.now() - state.startTime) / 1000);
                        const remaining = Math.max(0, 300 - elapsed); // 5 minutos
                        const minutes = Math.floor(remaining / 60);
                        const seconds = remaining % 60;
                        
                        statusDiv.className = remaining < 60 ? 'status warning' : 'status success';
                        statusDiv.textContent = remaining < 60 ? '⚠️ Trial expirando em breve!' : '✅ Trial Ativo';
                        timerDiv.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                        
                        log(`Timer: ${minutes}:${seconds.toString().padStart(2, '0')} (${remaining < 60 ? 'WARNING' : 'OK'})`);
                    } else {
                        statusDiv.className = 'status info';
                        statusDiv.textContent = '🆕 Trial não iniciado';
                        timerDiv.textContent = '5:00';
                        log('Trial ainda não iniciado');
                    }
                } else {
                    document.getElementById('trial-status').className = 'status info';
                    document.getElementById('trial-status').textContent = '🆕 Nenhum dado de trial encontrado';
                    document.getElementById('timer-display').textContent = '--:--';
                    log('Nenhum dado de trial no localStorage');
                }
            } catch (error) {
                log(`Erro ao verificar status: ${error.message}`, 'error');
                
                // Tentar método alternativo
                fetch('http://localhost:3001/api/trial-status')
                    .then(res => res.json())
                    .then(data => {
                        log('Status via API: ' + JSON.stringify(data));
                    })
                    .catch(err => {
                        log('API não disponível', 'warning');
                    });
            }
        }
        
        function clearTrial() {
            try {
                const frame = document.getElementById('player-frame');
                const frameWindow = frame.contentWindow;
                frameWindow.localStorage.removeItem('trial-storage');
                
                log('Trial limpo com sucesso!', 'success');
                checkStatus();
                
                // Recarregar o iframe
                setTimeout(() => {
                    reloadFrame();
                }, 500);
            } catch (error) {
                log(`Erro ao limpar trial: ${error.message}`, 'error');
            }
        }
        
        function loadChannel(channelId) {
            const frame = document.getElementById('player-frame');
            frame.src = `http://localhost:3001/watch/${channelId}`;
            log(`Carregando canal: ${channelId}`);
        }
        
        function reloadFrame() {
            const frame = document.getElementById('player-frame');
            frame.src = frame.src;
            log('Player recarregado');
        }
        
        // Verificar status a cada 1 segundo
        setInterval(checkStatus, 1000);
        
        // Log inicial
        log('Sistema de teste iniciado');
        log('Aguardando carregamento do player...');
        
        // Verificar status inicial após carregar
        setTimeout(() => {
            checkStatus();
            log('Verificação inicial completa');
        }, 3000);
    </script>
</body>
</html>