# 🎯 CONFIGURAR GOOGLE OAUTH - PRÓXIMOS PASSOS

## ✅ Você está no lugar certo!

### 1️⃣ CLIQUE EM "Shared keys"

Na tela que você mostrou, clique no botão:
```
[ Shared keys ]
```

### 2️⃣ NA PRÓXIMA TELA VOCÊ VERÁ:

1. **Client ID** - Campo para colocar seu Google Client ID
2. **Client Secret** - Campo para colocar seu Google Client Secret
3. **Redirect URI** - Este é o que você precisa copiar!

### 3️⃣ O QUE FAZER:

1. **Preencha os campos:**
   - **Client ID**: Pegue do Google Cloud Console
   - **Client Secret**: Pegue do Google Cloud Console

2. **IMPORTANTE**: Ap<PERSON> preencher, o Stack Auth vai gerar o Redirect URI

3. **Copie o Redirect URI** que aparecer

4. **Clique em "Save"**

## 📋 ONDE PEGAR O CLIENT ID E SECRET DO GOOGLE:

### No Google Cloud Console:

1. Acesse: https://console.cloud.google.com
2. Menu → APIs & Services → Credentials
3. Clique no seu OAuth 2.0 Client
4. Copie:
   - **Client ID**
   - **Client Secret**

## 🔄 FLUXO COMPLETO:

```
1. Stack Auth: Clique "Shared keys"
   ↓
2. Stack Auth: Preencha Client ID e Secret do Google
   ↓
3. Stack Auth: Copie o Redirect URI gerado
   ↓
4. Google Console: Cole o Redirect URI
   ↓
5. Salve em ambos os lugares
```

## ⚠️ AVISO IMPORTANTE:

O aviso que aparece "Shared keys are created by the Stack team for development" é normal. Em produção você está usando suas próprias chaves do Google, então está tudo certo!

## 🎊 APÓS CONFIGURAR:

1. O Redirect URI será algo como:
```
https://api.stack-auth.com/api/v1/auth/oauth/callback/google?after_callback_redirect_to=https%3A%2F%2Fnewspports-production.up.railway.app%2Fhandler%2Foauth-callback
```

2. Este é o URI que você deve adicionar no Google Cloud Console!

## 💡 DICA:

Se você já tem o Client ID e Secret do Google, preencha os campos e o Stack Auth mostrará o Redirect URI automaticamente!