# Corrigir Problema de Login no Supabase

## Problema Identificado

O usuário <EMAIL> foi criado no Supabase mas não tem um perfil correspondente na tabela `profiles`, por isso não consegue fazer login.

## Solução Rápida

Execute este SQL no Supabase SQL Editor (https://supabase.com/dashboard/project/bgeecdlhnwkgdujoyont/sql/new):

```sql
-- Criar perfil para o usuário existente
INSERT INTO public.profiles (id, username, full_name, subscription_tier, trial_used)
VALUES (
  '8b9e9d0e-3788-4801-a8d0-c09a30c0563a',
  'leonardo_guimara<PERSON>',
  '<PERSON>',
  'free',
  false
);
```

## Solução Permanente

Execute este SQL para criar um trigger que cria perfis automaticamente para novos usuários:

```sql
-- 1. Criar função para criar perfil automaticamente
CREATE OR REPLACE FUNCTION public.handle_new_user()
<PERSON><PERSON><PERSON><PERSON> trigger AS $$
BEGIN
  INSERT INTO public.profiles (id, username, full_name, subscription_tier)
  VALUES (
    new.id,
    SPLIT_PART(new.email, '@', 1),
    COALESCE(new.raw_user_meta_data->>'full_name', SPLIT_PART(new.email, '@', 1)),
    'free'
  );
  RETURN new;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 2. Criar trigger para novos usuários
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- 3. Criar perfis para todos os usuários existentes sem perfil
INSERT INTO public.profiles (id, username, full_name, subscription_tier)
SELECT 
  u.id,
  SPLIT_PART(u.email, '@', 1) as username,
  COALESCE(u.raw_user_meta_data->>'full_name', SPLIT_PART(u.email, '@', 1)) as full_name,
  'free' as subscription_tier
FROM auth.users u
LEFT JOIN public.profiles p ON u.id = p.id
WHERE p.id IS NULL;
```

## Mudanças no Código

Já implementei as seguintes mudanças:

1. **Login atualizado** - Agora tenta fazer login primeiro com Supabase, depois com credenciais de teste
2. **Registro atualizado** - Usa Supabase para criar novos usuários
3. **Sincronização de perfil** - Componente que sincroniza automaticamente o perfil após login
4. **API de sincronização** - Endpoint `/api/auth/sync-profile` para criar perfis faltantes

## Como Testar

1. Execute o SQL acima no Supabase SQL Editor
2. Tente fazer <NAME_EMAIL> e sua senha
3. Deve funcionar corretamente agora

## Credenciais de Teste (ainda funcionam)

- email: <EMAIL> / senha: test123
- email: <EMAIL> / senha: admin123
- email: <EMAIL> / senha: demo123