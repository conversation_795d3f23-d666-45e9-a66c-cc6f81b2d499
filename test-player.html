<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test IPTV Player</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #000;
            color: #fff;
            font-family: Arial, sans-serif;
        }
        #player {
            width: 100%;
            max-width: 800px;
            margin: 0 auto;
        }
        video {
            width: 100%;
            height: auto;
        }
        .info {
            margin-top: 20px;
            padding: 20px;
            background: #222;
            border-radius: 8px;
        }
        .channel {
            margin: 10px 0;
            padding: 10px;
            background: #333;
            border-radius: 4px;
            cursor: pointer;
        }
        .channel:hover {
            background: #444;
        }
    </style>
</head>
<body>
    <h1>Test IPTV Streams</h1>
    
    <div id="player">
        <video id="video" controls></video>
    </div>
    
    <div class="info">
        <h2>Working Channels:</h2>
        <div id="channels"></div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/hls.js@latest"></script>
    <script>
        const channels = [
            {
                name: "San Marino RTV Sport",
                url: "https://d2hrvno5bw6tg2.cloudfront.net/smrtv-ch02/smil:ch-02.smil/master.m3u8"
            },
            {
                name: "Turkmenistan Sport",
                url: "https://alpha.tv.online.tm/hls/ch004.m3u8"
            },
            {
                name: "TSN3",
                url: "https://fl5.moveonjoy.com/TSN_3/index.m3u8"
            },
            {
                name: "SOS Kanal Plus",
                url: "https://53be5ef2d13aa.streamlock.net/soskanalplus/soskanalplus.stream/playlist.m3u8"
            }
        ];
        
        const video = document.getElementById('video');
        const channelsDiv = document.getElementById('channels');
        let hls;
        
        // Render channels
        channels.forEach(channel => {
            const div = document.createElement('div');
            div.className = 'channel';
            div.innerHTML = `<strong>${channel.name}</strong><br><small>${channel.url}</small>`;
            div.onclick = () => playChannel(channel);
            channelsDiv.appendChild(div);
        });
        
        function playChannel(channel) {
            console.log('Playing:', channel.name, channel.url);
            
            if (hls) {
                hls.destroy();
            }
            
            if (Hls.isSupported()) {
                hls = new Hls({
                    debug: true
                });
                hls.loadSource(channel.url);
                hls.attachMedia(video);
                hls.on(Hls.Events.MANIFEST_PARSED, function() {
                    video.play();
                });
                hls.on(Hls.Events.ERROR, function(event, data) {
                    console.error('HLS Error:', data);
                });
            } else if (video.canPlayType('application/vnd.apple.mpegurl')) {
                video.src = channel.url;
                video.addEventListener('loadedmetadata', function() {
                    video.play();
                });
            }
        }
        
        // Auto-play first channel
        if (channels.length > 0) {
            playChannel(channels[0]);
        }
    </script>
</body>
</html>