# 🔧 Correção do Sistema de Trial - Banco de Dados

## 📋 Resumo da Correção

O sistema de trial foi corrigido para usar o banco de dados em vez do localStorage, impedindo que usuários contornem o limite de 5 minutos simplesmente trocando de navegador.

## 🎯 O que foi feito

### 1. **Trial agora é vinculado ao usuário**
- **Antes**: Trial era salvo no localStorage (por navegador)
- **Agora**: Trial é salvo no banco de dados Supabase (por usuário)
- **Resultado**: Trocar de navegador não reseta o trial

### 2. **Componentes atualizados**
- `src/stores/trial.store.ts` - Removido localStorage, usa RPC do banco
- `src/components/player/video-player.tsx` - Passa user.id ao iniciar trial
- `src/components/player/enhanced-hls-player.tsx` - Passa user.id ao iniciar trial
- Correção do bug onde usava trialId em vez de userId ao expirar

### 3. **Funções do banco de dados**
- `get_or_create_trial_session` - Gerencia trials por usuário
- `expire_trial_session` - Marca trial como expirado
- Tabela `trial_sessions` - Armazena dados do trial

### 4. **Ferramentas de teste criadas**
- `/api/debug/test-trial` - Endpoint para verificar status do trial
- `/test-trial-system.html` - Página para testar em múltiplos navegadores

## 🧪 Como testar

1. **Acesse a página de teste**:
   ```
   http://localhost:3000/test-trial-system.html
   ```

2. **Teste em múltiplos navegadores**:
   - Faça login com a mesma conta em Chrome e Firefox
   - Inicie o trial assistindo um canal
   - Verifique que o tempo continua de onde parou
   - Quando expirar em um, expira em todos

3. **Verificar via API**:
   ```bash
   # Verificar status do trial
   curl http://localhost:3000/api/debug/test-trial -H "Cookie: [seus-cookies]"
   ```

## 🔍 Logs importantes

O sistema agora registra:
```
[TRIAL-STORE] checkTrialStatus called { userId: "..." }
[TRIAL-STORE] RPC response received: { trial_id, time_remaining, is_expired }
[TRIAL-STORE] Expiring trial in database: { userId, sessionId }
[TRIAL-STORE] Trial expirado no banco com sucesso
```

## ⚠️ Pontos importantes

1. **Usuários anônimos**: Ainda usam sessionId (não podem trocar de navegador)
2. **Usuários logados**: Trial vinculado ao user.id (persistente entre navegadores)
3. **Reset após 24h**: Permite novo trial após 24 horas
4. **Premium tem prioridade**: Se tem assinatura, trial é ignorado

## 🚀 Deploy

As migrações SQL já foram aplicadas anteriormente. O código está pronto para deploy.

## 📝 Notas técnicas

- O trial não usa mais `persist` do Zustand
- Cada verificação consulta o banco em tempo real
- RPC functions garantem consistência dos dados
- Fail-safe: em caso de erro, nega acesso por segurança