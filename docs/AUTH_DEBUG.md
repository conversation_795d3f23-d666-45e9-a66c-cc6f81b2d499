# Sistema de Debug de Autenticação

Este documento descreve o sistema de logs detalhados implementado para diagnosticar problemas de autenticação com Stack Auth.

## Ativando o Debug

Para ativar os logs detalhados de autenticação, adicione a seguinte variável ao seu `.env.local`:

```env
NEXT_PUBLIC_AUTH_DEBUG=true
```

## Logs Disponíveis

### 1. Inicialização do Stack Auth Client

```
🔐 Iniciando configuração do Stack Auth Client...
📦 Configuração Stack Auth: {
  projectId: "proj...1234",
  publishableClientKey: "pub_...5678",
  tokenStore: "cookie",
  urls: { ... }
}
✅ Stack Auth Client inicializado com sucesso
```

### 2. Processo de Login OAuth

```
🔐 Iniciando autenticação OAuth com google
📦 Provider: google
🌐 URL de callback configurada: http://localhost:3000/handler/oauth-callback
✅ Redirecionamento OAuth iniciado
```

### 3. Callback OAuth

```
🔄 OAuth Callback detectado
📍 URL: http://localhost:3000/handler/oauth-callback?code=...&state=...
🔍 Parâmetros: {
  code: "Presente",
  state: "Presente",
  error: null
}
```

### 4. Login com Credenciais

```
🔐 Iniciando autenticação com credenciais
📧 Email: <EMAIL>
🔑 Senha fornecida: Sim
✅ Login com credenciais bem-sucedido
```

### 5. Estado de Autenticação

```
👤 Estado de autenticação atual: {
  id: "user_123456",
  email: "<EMAIL>",
  displayName: "John Doe",
  emailVerified: true
}
```

### 6. Erros e Exceções

```
🚨 Erro ao autenticar: {
  message: "Invalid credentials",
  code: "INVALID_CREDENTIALS",
  stack: "Error: Invalid credentials\n    at ..."
}
```

## Estrutura dos Logs

Cada log segue o padrão:

```
[timestamp] emoji mensagem dados_opcionais
```

- **timestamp**: ISO 8601 format (ex: 2024-01-24T10:30:45.123Z)
- **emoji**: Indicador visual do tipo de log
  - 🔐 - Início de processo de autenticação
  - 📦 - Dados/configuração
  - 🌐 - Operação de rede
  - ✅ - Sucesso
  - ❌ - Falha
  - 🚨 - Erro crítico
  - ⚠️ - Aviso
  - ℹ️ - Informação
  - 🔄 - Processo em andamento
  - 🔚 - Fim de processo
  - 👤 - Informação de usuário
  - 🚪 - Logout

## Segurança

O sistema de logs implementa as seguintes medidas de segurança:

1. **Mascaramento de dados sensíveis**: Tokens, senhas e chaves são mascarados
   - Exemplo: `proj_1234...5678` (mostra apenas os primeiros e últimos 4 caracteres)

2. **Headers seguros**: Headers sensíveis são automaticamente mascarados
   - `authorization`, `x-stack-secret-server-key`, `cookie`, `set-cookie`

3. **Desativado por padrão**: Os logs só aparecem quando `NEXT_PUBLIC_AUTH_DEBUG=true`

## Analisando Problemas Comuns

### Problema: OAuth não retorna após login

Procure por:
```
🔄 OAuth Callback detectado
🔍 Parâmetros: {
  code: "Ausente",
  error: "access_denied"
}
```

### Problema: Login com credenciais falha

Procure por:
```
❌ Falha no login: {
  code: "INVALID_CREDENTIALS",
  message: "Email ou senha incorretos"
}
```

### Problema: Extensões do navegador interferindo

Procure por:
```
⚠️ Bloqueada modificação de extensão em: stackframe_auth
Erro de extensão ignorado: chrome-extension://egjidjbpglichdcondbcbdnbeeppgdph
```

## Desativando em Produção

⚠️ **IMPORTANTE**: Sempre desative os logs de debug em produção:

```env
NEXT_PUBLIC_AUTH_DEBUG=false
```

Ou simplesmente remova a variável do arquivo `.env.production`.