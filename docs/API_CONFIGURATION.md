# Configuração das APIs de Esportes

## Resumo Importante

Este documento contém a configuração crítica para o funcionamento correto das APIs de esportes. **NUNCA ESQUECER** estas regras:

### 1. API Sports (12 APIs diferentes)
- **Limite**: 100 requisições por dia POR API
- **Intervalo**: 1 requisição a cada 15 minutos POR API
- **Total**: 12 APIs × 100 req/dia = 1.200 requisições totais por dia
- **APIs incluídas**:
  - api-football (Futebol)
  - api-basketball (Basquete)
  - api-baseball (Baseball)
  - api-hockey (Hockey)
  - api-volleyball (Vôlei)
  - api-handball (Handball)
  - api-rugby (Rugby)
  - api-formula1 (Fórmula 1)
  - api-mma (MMA)
  - api-nfl (NFL/Futebol Americano)
  - api-nba (NBA)
  - api-afl (AFL)

### 2. AllSportsDB
- **Limite**: 100.000 requisições por mês
- **Cálculo**: ~3.333 requisições por dia
- **Intervalo**: 30 segundos entre requisições (conservador)
- **Uso**: Priorizar para eventos ao vivo e complementar APIs Sports

## Estratégia de Uso

### Para Eventos Ao Vivo
1. **Prioridade 1**: AllSportsDB (requisições frequentes, 30 segundos)
2. **Fallback**: API Sports (se AllSportsDB falhar ou retornar poucos dados)

### Para Próximos Eventos
1. **Prioridade 1**: API Sports (calendário completo)
2. **Complemento**: AllSportsDB (apenas esportes não cobertos)

## Implementação no Código

### Request Manager (`request-manager.ts`)
```typescript
// Intervalos mínimos por API
if (api === 'allsportdb') {
  minInterval = 30 * 1000 // 30 segundos
} else if (api.startsWith('api-')) {
  minInterval = 15 * 60 * 1000 // 15 minutos
}

// Limites diários
'allsportdb': 3333,      // ~100k/mês
'api-football': 100,     // 100/dia por API
'api-basketball': 100,   // etc...
```

### Cache
- **API Sports**: Cache de 15 minutos (mesmo intervalo das requisições)
- **AllSportsDB**: Cache de 30 segundos a 5 minutos (dependendo do tipo)

## Monitoramento

- Verificar logs para garantir intervalos corretos
- Monitorar uso diário através de `requestManager.getStats()`
- Resetar quotas automaticamente à meia-noite

## IMPORTANTE

1. **NUNCA** fazer requisições simultâneas sem respeitar os intervalos
2. **SEMPRE** usar o `makeAPIRequest` que controla os limites
3. **PRIORIZAR** AllSportsDB para eventos ao vivo (100k/mês)
4. **ECONOMIZAR** API Sports para dados essenciais (100/dia por API)