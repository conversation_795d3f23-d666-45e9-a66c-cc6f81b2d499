# Sistema de Resultados ao Vivo

## Comportamento Esperado

O sistema de "Resultados en Vivo" mostra **APENAS jogos que estão acontecendo AGORA**, em tempo real. 

### Quando não há jogos ao vivo:
- Exibe mensagem: "Nenhum jogo ao vivo"
- Mostra horário da última verificação
- Link para ver próximos jogos
- **NUNCA mostra dados fictícios ou de demonstração**

### Quando há jogos ao vivo:
- Mostra apenas partidas que estão em andamento
- Atualiza automaticamente a cada 30 segundos
- Exibe placar em tempo real
- Status do jogo (1º tempo, intervalo, etc.)

## APIs Utilizadas

### 1. AllSportDB API
- **Chave**: Configurada em `ALLSPORTDB_API_KEY`
- **Limite**: 100.000 requisições/mês
- **Uso**: API principal para eventos ao vivo

### 2. API-Sports (Football, Basketball, etc.)
- **Chave**: Configurada em `API_SPORTS_KEY`
- **Limite**: 100 requisições/dia (plano gratuito)
- **Uso**: Fallback quando AllSportDB não retorna dados

## Configuração

### Variáveis de Ambiente Necessárias:
```env
# AllSportDB - API principal
ALLSPORTDB_API_KEY=sua_chave_aqui

# API-Sports - Fallback
API_SPORTS_KEY=sua_chave_aqui
```

### Obter Chaves de API:

1. **AllSportDB**:
   - Registrar em: https://allsportdb.com
   - Plano gratuito: 100k requisições/mês
   - Documentação: https://allsportdb.com/docs

2. **API-Sports**:
   - Registrar em: https://rapidapi.com/api-sports/api/api-football
   - Plano gratuito: 100 requisições/dia
   - Documentação: https://api-sports.io/documentation

## Fluxo de Dados

1. **UnifiedSportsService.getLiveResults()**
   - Tenta buscar do AllSportDB primeiro
   - Filtra apenas eventos de curta duração (< 12h)
   - Filtra apenas esportes com partidas (futebol, basquete, etc.)

2. **SportsDataService.getLiveResultsForAPI()**
   - Fallback para APIs específicas (football, basketball)
   - Usa cache de 15 minutos para economizar requisições

3. **Componente LiveSportsResults**
   - Atualiza a cada 30 segundos
   - Mostra estado vazio quando não há jogos
   - NUNCA usa dados mock ou demonstração

## Solução de Problemas

### "Limite de requisições excedido"
- Verifique o dashboard da API
- Considere upgrade do plano
- Use cache mais agressivo

### "Nenhum jogo ao vivo" sempre
- Verifique se as chaves de API estão corretas
- Teste as APIs diretamente
- Verifique logs do servidor

### Dados incorretos
- Limpe o cache: `rm -rf .next/cache`
- Verifique timezone do servidor
- Confirme formato de data das APIs

## Importante

**NUNCA adicionar dados mock ou de demonstração em produção!**

O sistema deve sempre mostrar a realidade:
- Se não há jogos = mostra "Nenhum jogo ao vivo"
- Se APIs falham = mostra erro apropriado
- Apenas dados reais devem ser exibidos