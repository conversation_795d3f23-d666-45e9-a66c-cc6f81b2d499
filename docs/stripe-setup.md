# Configuração do Stripe para NewSpports

## 1. Produto e Preço Criados

### Produto
- **Nome**: NewSpports Premium
- **ID**: `prod_SlBxDaGOJCF0ZT`
- **Descrição**: Acesso ilimitado a todos os canais esportivos premium em HD, sem anúncios e com suporte 24/7

### Preço
- **Valor**: $20.00 USD/mês
- **Tipo**: Assinatura recorrente mensal
- **ID**: Será criado ao executar o script

## 2. Configuração Inicial

### 2.1. Variáveis de Ambiente

Adicione ao seu `.env.local`:

```env
# Stripe API Keys (obtenha em https://dashboard.stripe.com/apikeys)
STRIPE_SECRET_KEY=sk_test_xxx  # Ou sk_live_xxx para produção
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_xxx  # Ou pk_live_xxx

# IDs do Produto e Preço
STRIPE_PRODUCT_ID=prod_SlBxDaGOJCF0ZT
STRIPE_PRICE_ID=price_xxx  # Será gerado pelo script

# Webhook Secret (configurar após criar webhook)
STRIPE_WEBHOOK_SECRET=whsec_xxx
```

### 2.2. Criar Preço Recorrente

Execute o script para criar o preço mensal:

```bash
pnpm tsx scripts/setup-stripe-price.ts
```

Este script irá:
1. Criar um preço de $20/mês
2. Definir como preço padrão do produto
3. Mostrar o ID do preço para adicionar ao `.env.local`

### 2.3. Configurar Webhooks

1. Acesse: https://dashboard.stripe.com/webhooks
2. Clique em "Add endpoint"
3. Configure:
   - **Endpoint URL**: `https://seu-dominio.com/api/stripe/webhooks`
   - **Events to send**: Selecione:
     - `customer.subscription.created`
     - `customer.subscription.updated`
     - `customer.subscription.deleted`
     - `customer.subscription.trial_will_end`
     - `invoice.payment_succeeded`
     - `invoice.payment_failed`
4. Copie o "Signing secret" e adicione ao `.env.local` como `STRIPE_WEBHOOK_SECRET`

### 2.4. Configurar Customer Portal

1. Acesse: https://dashboard.stripe.com/settings/billing/portal
2. Configure:
   - **Features**: Ative todas as opções desejadas
   - **Business information**: Adicione informações da empresa
   - **Links**: Configure links de termos e privacidade
3. Salve as configurações

## 3. Banco de Dados

Execute a migration para adicionar campos do Stripe:

```bash
pnpm supabase migration up
```

Isso criará:
- Campos do Stripe na tabela `profiles`
- Tabela `payment_history` para histórico de pagamentos
- Funções helper para verificar assinaturas

## 4. Testar Integração

### 4.1. Modo Teste

Use cartões de teste do Stripe:
- **Sucesso**: `4242 4242 4242 4242`
- **Falha**: `4000 0000 0000 0002`
- **3D Secure**: `4000 0027 6000 3184`

### 4.2. Fluxo de Teste

1. Criar conta no site
2. Navegar para `/subscription`
3. Clicar em "Assinar Premium"
4. Usar cartão de teste
5. Verificar:
   - Webhook recebido
   - Perfil atualizado no banco
   - Acesso liberado

### 4.3. Simular Eventos

Use o Stripe CLI para testar webhooks localmente:

```bash
# Instalar Stripe CLI
brew install stripe/stripe-cli/stripe

# Login
stripe login

# Forward webhooks para localhost
stripe listen --forward-to localhost:3000/api/stripe/webhooks

# Trigger eventos de teste
stripe trigger customer.subscription.created
```

## 5. URLs Importantes

### Desenvolvimento
- **Dashboard**: https://dashboard.stripe.com/test
- **Logs**: https://dashboard.stripe.com/test/logs
- **Webhooks**: https://dashboard.stripe.com/test/webhooks
- **Customers**: https://dashboard.stripe.com/test/customers

### Páginas do Site
- **Assinatura**: `/subscription`
- **Sucesso**: `/subscription/success`
- **Cancelamento**: `/subscription/cancel`
- **Conta**: `/account`

## 6. Troubleshooting

### Webhook não está funcionando
1. Verifique se o endpoint está acessível publicamente
2. Confirme que o `STRIPE_WEBHOOK_SECRET` está correto
3. Verifique os logs no Stripe Dashboard

### Assinatura não aparece no site
1. Verifique se o webhook foi processado
2. Confirme que o `stripe_customer_id` foi salvo no perfil
3. Verifique a tabela `profiles` no Supabase

### Erro no checkout
1. Verifique as chaves API
2. Confirme que o `STRIPE_PRICE_ID` está correto
3. Verifique os logs do navegador e servidor

## 7. Ir para Produção

1. Troque as chaves de teste pelas de produção
2. Atualize URLs de webhook para domínio de produção
3. Configure Customer Portal em produção
4. Teste todo o fluxo com cartão real
5. Monitore logs e métricas no Stripe Dashboard