# Fluxo de Assinatura e Pagamento - NewSpports

## Visão Geral

O sistema de assinatura do NewSpports oferece:
- Trial de 5 minutos para novos usuários
- Pagamento via Stripe com renovação automática
- 1 mês grátis para quem baixa o aplicativo
- Suporte para Stack Auth e Supabase Auth

## 1. Trial de 5 Minutos

### Como funciona:
- Novo usuário acessa qualquer canal
- Timer de 5 minutos inicia automaticamente
- Após 5 minutos, o vídeo é bloqueado com overlay de conversão

### Arquivos envolvidos:
- `/src/stores/trial.store.ts` - Gerencia estado do trial
- `/src/components/player/trial-overlay.tsx` - Overlay de bloqueio
- `/src/components/player/trial-timer.tsx` - Timer visual
- `/src/hooks/use-trial-timer.ts` - Hook para gerenciar timer

## 2. Sistema de Pagamento (Stripe)

### Fluxo de pagamento:
1. Usuário clica em "Assinar"
2. Redirecionado para Stripe Checkout
3. <PERSON><PERSON><PERSON> pagamento, webhook é acionado
4. Sistema atualiza status para premium

### Arquivos principais:
- `/src/app/api/stripe/webhook/route.ts` - **Webhook principal (CRÍTICO)**
- `/src/app/api/stripe/checkout/route.ts` - Cria sessão de checkout
- `/src/app/api/stripe/subscription-status/route.ts` - Verifica status

### Problema Resolvido:
O webhook agora atualiza DUAS tabelas:
- `profiles` - Para usuários Supabase Auth
- `stack_profiles` - Para usuários Stack Auth

```typescript
// Webhook atualiza ambas as tabelas
await syncProfileTables(userId, updateData)
```

## 3. Ativação via App (1 Mês Grátis)

### Como funciona:
- Usuário baixa o app e clica no link de ativação
- Sistema concede 30 dias de acesso premium
- Funciona para usuários autenticados ou anônimos

### Endpoints:
- `/api/app/activate-simple` - Ativação simplificada
- `/activar` - Página de ativação

### Fluxo:
1. App envia request com deviceId
2. Se usuário autenticado: atualiza perfil para premium
3. Se anônimo: cria sessão temporária de 30 dias

## 4. Renovação Automática

### Sistema de verificação:
- Endpoint cron: `/api/cron/check-subscriptions`
- Deve ser executado diariamente
- Verifica assinaturas próximas do vencimento
- Consulta Stripe para verificar renovação
- Bloqueia acesso se não houver pagamento

### Configuração recomendada:
```bash
# Adicionar ao cron (executar diariamente às 3AM)
0 3 * * * curl -H "Authorization: Bearer $CRON_SECRET" https://seu-site.com/api/cron/check-subscriptions
```

## 5. Verificação de Acesso

### Função RPC unificada:
`check_user_access_unified` verifica em ordem:
1. `stack_profiles` - Usuários Stack Auth
2. `profiles` - Usuários Supabase Auth  
3. `user_access` - Acessos por cupom
4. `anonymous_access` - Sessões anônimas

### Store de acesso:
- `/src/stores/access.store.ts` - Gerencia estado de acesso
- Cache de 5 minutos para evitar consultas excessivas

## 6. Logs e Debug

### Todos os endpoints têm logs detalhados:
```
[STRIPE-WEBHOOK] 2025-07-29 10:30:00 - Event type: checkout.session.completed
[STRIPE-WEBHOOK] 2025-07-29 10:30:00 - Customer ID: cus_xxx
[STRIPE-WEBHOOK] 2025-07-29 10:30:00 - Updating profile...
```

### Endpoints de debug:
- `/api/debug/subscription-check` - Verifica status completo
- `/api/test/activate-premium` - Ativa premium para teste
- `/test-subscription` - Página de teste visual

## 7. Tabelas do Banco de Dados

### profiles (Supabase Auth)
```sql
- id: UUID (user ID)
- email: TEXT
- subscription_tier: TEXT ('free', 'premium')
- subscription_status: TEXT ('active', 'inactive', 'past_due', 'canceled')
- subscription_current_period_end: TIMESTAMPTZ
- stripe_customer_id: TEXT
- stripe_subscription_id: TEXT
```

### stack_profiles (Stack Auth)
```sql
- id: TEXT (Stack Auth ID)
- email: TEXT
- (mesmos campos de assinatura que profiles)
```

### anonymous_access
```sql
- session_id: TEXT
- expires_at: TIMESTAMPTZ
- ip_address: TEXT
- metadata: JSONB
```

## 8. Variáveis de Ambiente Necessárias

```env
# Stripe
STRIPE_SECRET_KEY=sk_live_xxx
STRIPE_WEBHOOK_SECRET=whsec_xxx
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_live_xxx
NEXT_PUBLIC_STRIPE_PRICE_ID=price_xxx

# Cron
CRON_SECRET=seu_secret_aqui

# Stack Auth
NEXT_PUBLIC_STACK_PROJECT_ID=xxx
NEXT_PUBLIC_STACK_PUBLISHABLE_CLIENT_KEY=xxx
STACK_SECRET_SERVER_KEY=xxx
```

## 9. Testes Recomendados

1. **Teste de Trial**: 
   - Limpar localStorage
   - Assistir canal por 5 minutos
   - Verificar se bloqueia

2. **Teste de Pagamento**:
   - Usar `/test-subscription` 
   - Clicar em "Ir para Checkout Stripe"
   - Usar cartão de teste: 4242 4242 4242 4242

3. **Teste de Ativação App**:
   - Acessar `/activar`
   - Clicar em "Activar Ahora"
   - Verificar se libera acesso

4. **Teste de Renovação**:
   - Executar endpoint cron manualmente
   - Verificar logs de processamento

## 10. Problemas Comuns e Soluções

### "Cliente paga mas continua limitado"
**Causa**: Webhook não atualizava stack_profiles
**Solução**: Implementada sincronização dupla no webhook

### "Erro 42P01 - table does not exist"
**Causa**: Tabela stack_profiles não existe no banco
**Solução**: Executar migração 008_create_stack_profiles.sql

### "Trial não reseta para novo usuário"
**Causa**: Estado persistente no localStorage
**Solução**: Limpar localStorage ou usar botão "Reset Trial"

### "Assinatura não renova automaticamente"
**Causa**: Cron não está configurado
**Solução**: Configurar cron job diário

## Monitoramento

Verificar regularmente:
- Logs do webhook Stripe
- Tabela payment_history
- Logs de erro no Supabase
- Métricas de conversão trial → pago