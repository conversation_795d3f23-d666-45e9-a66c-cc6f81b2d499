This is a [Next.js](https://nextjs.org) project bootstrapped with [`create-next-app`](https://nextjs.org/docs/app/api-reference/cli/create-next-app).

## Getting Started

First, run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

You can start editing the page by modifying `app/page.tsx`. The page auto-updates as you edit the file.

This project uses [`next/font`](https://nextjs.org/docs/app/building-your-application/optimizing/fonts) to automatically optimize and load [Geist](https://vercel.com/font), a new font family for Vercel.

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js) - your feedback and contributions are welcome!

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.

## 🔍 Debug Mode

### Enabling Debug Mode

To enable detailed logging throughout the application:

```bash
# Set in .env.local
DEBUG=true
NEXT_PUBLIC_DEBUG=true

# Or run with environment variable
DEBUG=true pnpm dev
```

### Debug Console

Access the debug console at `/debug` to:
- Toggle debug mode on/off
- View recent logs
- Test API endpoints
- Export logs for analysis
- Check environment status

### Accessing Logs

```javascript
// In browser console
window.__logger.getHistory()          // Get all logs
window.__logger.getHistory('player')  // Filter by tag
window.__logger.export()              // Export as text
```

### Log Tags

- `http` - HTTP requests/responses
- `player` - Video player events
- `iptv` - IPTV API calls
- `sports` - Sports API calls
- `auth` - Authentication events
- `stream` - Streaming events
- `env` - Environment checks

### Troubleshooting

#### "Video Mock" Issue

If the player shows a mock video instead of real content:

1. Check player logs:
   ```javascript
   window.__logger.getHistory('player')
   ```

2. Look for `player:url` entries to see what URL is being loaded
3. Verify the URL is a valid `.m3u8` file
4. Check for CORS or 403/404 errors

#### API Errors

If APIs are failing:

1. Check environment variables:
   - Visit `/debug` page
   - Verify "API Status" section shows ✅ for required APIs

2. Test APIs directly:
   - Click "Test APIs" button on `/debug` page
   - Check logs for detailed error messages

3. Check IAM permissions (if using cloud services):
   ```bash
   # Google Cloud
   gcloud auth list
   gcloud projects get-iam-policy $PROJECT_ID
   
   # AWS
   aws sts get-caller-identity
   aws iam list-attached-role-policies --role-name <ROLE>
   ```

### Common Issues

- **No stream URLs**: Set `NEXT_PUBLIC_STREAM_URL` in `.env.local`
- **CORS errors**: APIs must be called from server-side (use API routes)
- **403 Forbidden**: Check API keys and rate limits
- **Empty responses**: Enable debug mode and check `http` tag logs
