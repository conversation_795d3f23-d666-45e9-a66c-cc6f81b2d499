import { allSportDBClient } from './src/services/api/sports/allsport-db.client'

async function testAllSportDB() {
  console.log('🧪 Testando AllSportDB...\n')
  
  try {
    console.log('1️⃣ Testando getTodayEvents()...')
    const todayEvents = await allSportDBClient.getTodayEvents()
    console.log(`✅ Eventos de hoje: ${todayEvents.length} encontrados`)
    if (todayEvents.length > 0) {
      console.log('📋 Primeiros 3 eventos:')
      todayEvents.slice(0, 3).forEach((event, i) => {
        console.log(`  ${i + 1}. ${event.name} - ${event.sport} (${event.dateFrom})`)
      })
    }
    
    console.log('\n2️⃣ Testando getUpcomingEvents()...')
    const upcomingEvents = await allSportDBClient.getUpcomingEvents()
    console.log(`✅ Próximos eventos: ${upcomingEvents.length} encontrados`)
    if (upcomingEvents.length > 0) {
      console.log('📋 Primeiros 3 eventos:')
      upcomingEvents.slice(0, 3).forEach((event, i) => {
        console.log(`  ${i + 1}. ${event.name} - ${event.sport} (${event.dateFrom})`)
      })
    }
    
    console.log('\n3️⃣ Verificando eventos ao vivo (filtrando de hoje)...')
    const now = new Date()
    const liveEvents = todayEvents.filter(event => {
      const start = new Date(event.dateFrom)
      const end = new Date(event.dateTo || new Date(start.getTime() + 3 * 60 * 60 * 1000))
      return now >= start && now <= end
    })
    console.log(`✅ Eventos ao vivo agora: ${liveEvents.length} encontrados`)
    if (liveEvents.length > 0) {
      console.log('📋 Eventos ao vivo:')
      liveEvents.forEach((event, i) => {
        console.log(`  ${i + 1}. ${event.name} - ${event.sport}`)
      })
    }
    
    console.log('\n4️⃣ Testando getEventsBySport("football")...')
    const footballEvents = await allSportDBClient.getEventsBySport('football')
    console.log(`✅ Eventos de futebol: ${footballEvents.length} encontrados`)
    
    console.log('\n✅ AllSportDB está funcionando corretamente!')
    
  } catch (error) {
    console.error('\n❌ Erro ao testar AllSportDB:', error)
    if (error instanceof Error) {
      console.error('Mensagem:', error.message)
      console.error('Stack:', error.stack)
    }
  }
}

// Executar teste
testAllSportDB()