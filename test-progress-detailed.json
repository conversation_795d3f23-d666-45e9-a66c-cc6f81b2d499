{"working": [], "fixable": [], "broken": [{"id": "30AGolfKingdom.us", "name": "30A Golf Kingdom", "status": "broken", "reason": "page.waitForSelector: Timeout 15000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('video') to be visible\u001b[22m\n", "streamUrl": "https://30a-tv.com/feeds/vidaa/golf.m3u8", "duration": 18818, "timestamp": "2025-07-26T07:39:28.223Z"}, {"id": "AbuDhabiSports1.ae", "name": "Abu Dhabi Sports 1", "status": "broken", "reason": "page.waitForSelector: Timeout 15000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('video') to be visible\u001b[22m\n", "streamUrl": "https://vo-live-media.cdb.cdn.orange.com/Content/Channel/AbuDhabiSportsChannel1/HLS/index.m3u8", "duration": 15770, "timestamp": "2025-07-26T07:39:45.994Z"}, {"id": "AbuDhabiSports2.ae", "name": "Abu Dhabi Sports 2", "status": "broken", "reason": "page.waitForSelector: Timeout 15000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('video') to be visible\u001b[22m\n", "streamUrl": "https://vo-live-media.cdb.cdn.orange.com/Content/Channel/AbuDhabiSportsChannel2/HLS/index.m3u8", "duration": 15766, "timestamp": "2025-07-26T07:40:03.762Z"}, {"id": "ACCNetwork.us", "name": "ACC Network", "status": "broken", "reason": "page.waitForSelector: Timeout 15000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('video') to be visible\u001b[22m\n", "streamUrl": "https://fl3.moveonjoy.com/ACC_NETWORK/index.m3u8", "duration": 15798, "timestamp": "2025-07-26T07:40:21.563Z"}, {"id": "ACISportTV.it", "name": "ACI Sport TV", "status": "broken", "reason": "page.waitForSelector: Timeout 15000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('video') to be visible\u001b[22m\n", "streamUrl": "https://g4wlk6xpy23a-hls-live.mariatvcdn.it/acisporttv/b7a3464f8e2001314de9fefebf11229d.sdp/playlist.m3u8", "duration": 15831, "timestamp": "2025-07-26T07:40:39.395Z"}, {"id": "ACLCornholeTV.us", "name": "ACL Cornhole TV", "status": "broken", "reason": "page.waitForSelector: Timeout 15000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('video') to be visible\u001b[22m\n", "streamUrl": "https://1815337252.rsc.cdn77.org/HLS/CORNHOLETV.m3u8", "duration": 15777, "timestamp": "2025-07-26T07:40:57.174Z"}, {"id": "ActionSports.us", "name": "Action Sports", "status": "broken", "reason": "page.waitForSelector: Timeout 15000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('video') to be visible\u001b[22m\n", "streamUrl": "http://cfd-v4-service-channel-stitcher-use1-1.prd.pluto.tv/stitch/hls/channel/5be1be871843b56328bc3ef1/master.m3u8?appName=web&appVersion=unknown&clientTime=0&deviceDNT=0&deviceId=1b1c5241-4b81-11ef-a8ac-e146e4e7be02&deviceMake=Chrome&deviceModel=web&deviceType=web&deviceVersion=unknown&includeExtendedEvents=false&serverSideAds=false&sid=5f4b1024-61ca-4918-b727-2ee2d9fe6398", "duration": 15783, "timestamp": "2025-07-26T07:41:14.959Z"}], "tested": 252, "total": 252, "startTime": 1753515548096, "duration": 622154, "timestamp": "2025-07-26T07:49:30.250Z"}