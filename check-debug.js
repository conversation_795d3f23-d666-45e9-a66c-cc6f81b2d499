// Script para verificar sistema de debug
// Rodar no console do browser em http://localhost:3000/debug

console.log('=== DEBUG STATUS ===');
console.log('Logger disponível:', typeof window.__logger !== 'undefined');

if (window.__logger) {
    const history = window.__logger.getHistory();
    console.log('Total de logs:', history.length);
    
    // Filtrar logs do player
    const playerLogs = history.filter(log => log.tag === 'player');
    console.log('Logs do player:', playerLogs.length);
    
    // Mostrar últimos 5 logs do player
    console.log('\n=== ÚLTIMOS LOGS DO PLAYER ===');
    playerLogs.slice(-5).forEach(log => {
        console.log(`[${log.timestamp}] ${log.message}`, log.data || '');
    });
    
    // Verificar URLs carregadas
    const urlLogs = playerLogs.filter(log => log.message.includes('url') || log.message.includes('URL'));
    console.log('\n=== URLs CARREGADAS ===');
    urlLogs.forEach(log => {
        console.log(log.message, log.data?.url || log.data?.streamUrl || '');
    });
}

// Testar um canal específico
console.log('\n=== TESTANDO CANAL ===');
console.log('Para testar um canal, use:');
console.log('window.location.href = "/watch/iptv-SanMarinoRTVSport.sm"');