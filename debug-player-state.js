// Execute isso no console agora

console.clear();
console.log('🔍 VERIFICANDO PLAYER...\n');

// 1. Verificar logs do player
const logs = window.__logger.getHistory('player');
console.log('📊 Total de logs do player:', logs.length);

// Mostrar logs importantes
console.log('\n📋 Logs recentes:');
logs.slice(-10).forEach((log, i) => {
    console.log(`${i+1}. [${log.level}] ${log.message}`);
    if (log.data?.streamUrl || log.data?.playbackUrl) {
        console.log('   URL:', log.data.streamUrl || log.data.playbackUrl);
    }
});

// 2. Verificar vídeos na página
const videos = document.querySelectorAll('video');
console.log('\n🎥 Vídeos encontrados:', videos.length);

if (videos.length > 0) {
    const video = videos[0];
    console.log('\n📺 Estado do vídeo:');
    console.log('- URL atual:', video.currentSrc || video.src || 'NENHUMA');
    console.log('- Pausado:', video.paused);
    console.log('- Muted:', video.muted);
    console.log('- ReadyState:', video.readyState, '(4 = pode tocar)');
    console.log('- NetworkState:', video.networkState, '(1 = carregando, 2 = carregado)');
    console.log('- Erro:', video.error);
    
    // Tentar tocar se pausado
    if (video.paused) {
        console.log('\n🎮 Vídeo pausado. Tentando tocar...');
        video.play().catch(e => console.log('❌ Erro ao tocar:', e.message));
    }
}

// 3. Verificar botão de play
const playBtn = document.querySelector('button[class*="rounded-full"]');
if (playBtn) {
    console.log('\n🎯 Botão de play encontrado!');
    console.log('👉 Clique no botão ou execute: ');
    console.log('   document.querySelector("button[class*=rounded-full]").click()');
}

// 4. Verificar ReactPlayer
const reactPlayerDiv = document.querySelector('div > video')?.parentElement;
if (reactPlayerDiv) {
    console.log('\n⚛️ ReactPlayer encontrado');
    console.log('- Largura:', reactPlayerDiv.style.width);
    console.log('- Altura:', reactPlayerDiv.style.height);
}

console.log('\n💡 DICAS:');
console.log('1. Se há um botão de play, clique nele');
console.log('2. Se o vídeo está sem URL, verifique os logs');
console.log('3. Tente outro canal: window.location.href = "/watch/iptv-SanMarinoRTVSport.sm"');