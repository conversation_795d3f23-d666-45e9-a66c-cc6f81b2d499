<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Trial Coupon - NewSpports</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #0a0a0a;
            color: #ffffff;
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .video-container {
            position: relative;
            width: 100%;
            max-width: 1200px;
            background-color: #000;
        }
        
        .video-placeholder {
            width: 100%;
            aspect-ratio: 16/9;
            background-color: #1a1a1a;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 24px;
            color: #666;
        }
        
        .trial-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.95);
            display: flex;
            justify-content: center;
            align-items: center;
            backdrop-filter: blur(10px);
        }
        
        .trial-content {
            background: linear-gradient(to bottom, #1a1a1a, #0f0f0f);
            border-radius: 16px;
            padding: 48px;
            max-width: 500px;
            width: 90%;
            text-align: center;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
        }
        
        h1 {
            font-size: 32px;
            margin-bottom: 16px;
            background: linear-gradient(135deg, #ff4757, #ff6348);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        
        p {
            font-size: 18px;
            margin-bottom: 32px;
            color: #999;
            line-height: 1.6;
        }
        
        .buttons {
            display: flex;
            flex-direction: column;
            gap: 16px;
        }
        
        button {
            padding: 16px 32px;
            font-size: 18px;
            font-weight: 600;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .primary-btn {
            background: linear-gradient(135deg, #ff4757, #ff6348);
            color: white;
        }
        
        .primary-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(255, 71, 87, 0.3);
        }
        
        .secondary-btn {
            background: transparent;
            color: #ff6348;
            border: 2px solid #ff6348;
        }
        
        .secondary-btn:hover {
            background: rgba(255, 99, 72, 0.1);
        }
        
        .coupon-section {
            margin-top: 24px;
            padding-top: 24px;
            border-top: 1px solid #333;
        }
        
        .coupon-form {
            display: flex;
            gap: 8px;
            margin-top: 16px;
        }
        
        input {
            flex: 1;
            padding: 12px 16px;
            background: #0a0a0a;
            border: 2px solid #333;
            border-radius: 8px;
            color: white;
            font-size: 16px;
            text-transform: uppercase;
        }
        
        input:focus {
            outline: none;
            border-color: #ff6348;
        }
        
        .coupon-btn {
            padding: 12px 24px;
            background: #333;
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .coupon-btn:hover {
            background: #444;
        }
        
        .coupon-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        
        .message {
            margin-top: 16px;
            padding: 12px;
            border-radius: 8px;
            font-size: 14px;
        }
        
        .error {
            background: rgba(255, 71, 87, 0.1);
            border: 1px solid rgba(255, 71, 87, 0.3);
            color: #ff4757;
        }
        
        .success {
            background: rgba(46, 213, 115, 0.1);
            border: 1px solid rgba(46, 213, 115, 0.3);
            color: #2ed573;
        }
        
        .login-status {
            position: absolute;
            top: 20px;
            right: 20px;
            padding: 8px 16px;
            background: rgba(0, 0, 0, 0.8);
            border-radius: 8px;
            font-size: 14px;
        }
        
        .logged-in {
            color: #2ed573;
        }
        
        .logged-out {
            color: #ff4757;
        }
    </style>
</head>
<body>
    <div class="login-status" id="loginStatus">
        <span class="logged-out">❌ No autenticado</span>
    </div>
    
    <div class="video-container">
        <div class="video-placeholder">
            Video Player (Trial Expirado)
        </div>
        
        <div class="trial-overlay" id="trialOverlay">
            <div class="trial-content">
                <h1>Tu periodo de prueba ha terminado</h1>
                <p>Suscríbete ahora para continuar disfrutando de contenido deportivo premium sin límites.</p>
                
                <div class="buttons">
                    <button class="primary-btn" onclick="handleSubscribe()">
                        Suscribirse ahora
                    </button>
                    
                    <div class="coupon-section">
                        <button class="secondary-btn" onclick="toggleCouponForm()">
                            ¿Ya tienes un código?
                        </button>
                        
                        <div id="couponForm" style="display: none;">
                            <div class="coupon-form">
                                <input 
                                    type="text" 
                                    id="couponCode" 
                                    placeholder="CÓDIGO" 
                                    maxlength="20"
                                />
                                <button class="coupon-btn" onclick="applyCoupon()">
                                    Usar Código
                                </button>
                            </div>
                            <div id="message"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // Simular estado de login
        let isLoggedIn = false;
        
        function updateLoginStatus() {
            const statusEl = document.getElementById('loginStatus');
            if (isLoggedIn) {
                statusEl.innerHTML = '<span class="logged-in">✅ Autenticado como: <EMAIL></span>';
            } else {
                statusEl.innerHTML = '<span class="logged-out">❌ No autenticado</span>';
            }
        }
        
        function toggleLogin() {
            isLoggedIn = !isLoggedIn;
            updateLoginStatus();
        }
        
        function handleSubscribe() {
            alert('Redirigiendo a la página de suscripción...');
        }
        
        function toggleCouponForm() {
            const form = document.getElementById('couponForm');
            const isVisible = form.style.display !== 'none';
            form.style.display = isVisible ? 'none' : 'block';
            
            if (!isVisible) {
                document.getElementById('couponCode').focus();
            }
        }
        
        async function applyCoupon() {
            const code = document.getElementById('couponCode').value.trim();
            const messageEl = document.getElementById('message');
            
            if (!code) {
                showMessage('Por favor, ingresa un código', 'error');
                return;
            }
            
            // Simular llamada a la API
            messageEl.innerHTML = 'Verificando código...';
            
            // Simular delay de red
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            if (!isLoggedIn) {
                showMessage('Debes iniciar sesión para usar un código de cupón', 'error');
            } else {
                // Simular validación del código
                if (code === 'NEWSPPORTS30' || code === 'TRIAL30' || code === 'WELCOME50') {
                    showMessage('¡Código aplicado exitosamente! Disfruta de tu descuento.', 'success');
                    setTimeout(() => {
                        document.getElementById('trialOverlay').style.display = 'none';
                    }, 2000);
                } else {
                    showMessage('Código inválido o expirado', 'error');
                }
            }
        }
        
        function showMessage(text, type) {
            const messageEl = document.getElementById('message');
            messageEl.innerHTML = `<div class="message ${type}">${text}</div>`;
        }
        
        // Permitir cambiar estado de login con tecla 'L'
        document.addEventListener('keydown', (e) => {
            if (e.key === 'l' || e.key === 'L') {
                toggleLogin();
            }
        });
        
        // Mensaje inicial
        console.log('Presiona "L" para alternar el estado de login');
    </script>
</body>
</html>