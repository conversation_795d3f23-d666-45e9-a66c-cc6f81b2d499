#!/bin/bash

echo "🚀 Preparando para push no GitHub..."

# Verificar se já existe remote
if git remote | grep -q origin; then
    echo "✅ Remote 'origin' já existe"
else
    echo "➕ Adicionando remote origin..."
    git remote add origin https://github.com/SEU-USUARIO/streamplus-espana.git
fi

echo ""
echo "📝 Para fazer o push, execute os seguintes comandos:"
echo ""
echo "1. Primeiro, crie o repositório no GitHub:"
echo "   - Acesse: https://github.com/new"
echo "   - Nome: streamplus-espana"
echo "   - Descrição: StreamPlus España - Premium streaming platform"
echo "   - Privado: Sim (recomendado)"
echo "   - NÃO inicialize com README"
echo ""
echo "2. De<PERSON><PERSON> de criar, execute:"
echo "   git remote set-url origin https://github.com/SEU-USUARIO/streamplus-espana.git"
echo "   git push -u origin main"
echo ""
echo "3. Se pedir autenticação, use:"
echo "   - Username: seu-usuario-github"
echo "   - Password: seu-personal-access-token (não a senha)"
echo ""
echo "💡 Para criar um Personal Access Token:"
echo "   https://github.com/settings/tokens/new"
echo "   - Selecione: repo (full control)"