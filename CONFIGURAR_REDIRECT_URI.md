# 🎯 CONFIGURAR REDIRECT URI - ÚLTIMO PASSO!

## ✅ Progresso:
- ✓ Chaves configuradas corretamente
- ✓ Stack Auth conectando
- ❌ Falta apenas: Redirect URI

## 📋 PASSO A PASSO:

### 1️⃣ PEGAR O REDIRECT URI DO STACK AUTH

1. **Entre no Stack Auth Dashboard**
   - https://app.stack-auth.com

2. **Vá em Authentication > OAuth Providers**

3. **Clique em "Google" (ou no provider que está usando)**

4. **COPIE o Redirect URI mostrado**
   - Será algo como:
   ```
   https://api.stack-auth.com/api/v1/auth/oauth/callback/google?after_callback_redirect_to=https%3A%2F%2Fnewspports-production.up.railway.app%2Fhandler%2Foauth-callback
   ```
   - **COPIE EXATAMENTE COMO ESTÁ!**

### 2️⃣ ADICIONAR NO GOOGLE CLOUD CONSOLE

1. **Acesse o Google Cloud Console**
   - https://console.cloud.google.com

2. **Navegue para:**
   - Menu ☰ > APIs & Services > Credentials

3. **Encontre seu OAuth 2.0 Client**
   - Clique no nome do client (geralmente "Web client 1" ou similar)

4. **Em "Authorized redirect URIs"**
   - Clique em **"+ ADD URI"**
   - **COLE** o URI que você copiou do Stack Auth
   - ⚠️ IMPORTANTE: Cole EXATAMENTE como está, incluindo todos os parâmetros!

5. **Adicione também (se ainda não tiver)**:
   ```
   https://newspports-production.up.railway.app/handler/oauth-callback
   ```

6. **Em "Authorized JavaScript origins"** (se ainda não tiver):
   ```
   https://newspports-production.up.railway.app
   ```

7. **Clique em "SAVE"**

### 3️⃣ AGUARDAR PROPAGAÇÃO

⏱️ **IMPORTANTE**: As mudanças no Google podem levar até 5 minutos para propagar!

### 4️⃣ TESTAR

1. **Acesse seu site**
   - https://newspports-production.up.railway.app

2. **Tente fazer login com Google**

3. **Deve funcionar agora! 🎉**

## 🔍 SE AINDA DER ERRO:

### Verifique no Stack Auth:

1. **Em Authentication > OAuth Providers > Google**
2. **Confirme que:**
   - Status: Enabled ✓
   - Client ID: [seu-google-client-id]
   - Client Secret: [seu-google-client-secret]

### Verifique no Google Cloud:

1. **O OAuth consent screen está configurado?**
   - APIs & Services > OAuth consent screen
   - Deve estar em "Production" ou "Testing"

2. **Adicionou os URIs corretos?**
   - Sem espaços extras
   - Sem / no final (a menos que o Stack Auth inclua)

## 📸 EXEMPLO VISUAL:

### No Stack Auth você verá:
```
Google OAuth Settings
─────────────────────
Status: Enabled
Redirect URI: https://api.stack-auth.com/api/v1/auth/oauth/callback/google?after_callback_redirect_to=https%3A%2F%2Fnewspports-production.up.railway.app%2Fhandler%2Foauth-callback
```

### No Google Cloud você deve adicionar:
```
Authorized redirect URIs:
1. https://api.stack-auth.com/api/v1/auth/oauth/callback/google?after_callback_redirect_to=https%3A%2F%2Fnewspports-production.up.railway.app%2Fhandler%2Foauth-callback
2. https://newspports-production.up.railway.app/handler/oauth-callback
```

## 🎊 QUASE LÁ!

Este é literalmente o último passo! Após configurar o redirect URI corretamente, o login deve funcionar perfeitamente!

## 💡 DICA PRO:

Se o domínio do Railway mudar no futuro, você precisará:
1. Atualizar o redirect URI no Stack Auth
2. Copiar o novo URI
3. Atualizar no Google Cloud Console