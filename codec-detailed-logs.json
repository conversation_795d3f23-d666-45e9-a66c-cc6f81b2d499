{"channel": {"id": "30AGolfKingdom.us", "name": "30A Golf Kingdom"}, "hlsInfo": {"hasHls": false}, "videoInfo": {"paused": true, "currentTime": 0, "duration": null, "readyState": 0, "networkState": 2, "error": null, "videoHeight": 0, "videoWidth": 0, "buffered": null}, "networkResponses": [{"url": "http://localhost:3000/api/proxy/stream?url=https%3A%2F%2F30a-tv.com%2Ffeeds%2Fvidaa%2Fgolf.m3u8&channel=30A%20Golf%20Kingdom&channelId=30AGolfKingdom.us", "status": 200, "headers": {"x-content-type-options": "nosniff", "access-control-allow-methods": "GET, OPTIONS", "keep-alive": "timeout=5", "date": "Sat, 26 Jul 2025 09:22:40 GMT", "content-type": "application/vnd.apple.mpegurl", "vary": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch", "access-control-allow-headers": "Content-Type", "transfer-encoding": "chunked", "x-frame-options": "DENY", "content-security-policy": "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net; style-src 'self' 'unsafe-inline'; img-src 'self' data: https: blob:; font-src 'self' data:; connect-src 'self' http://localhost:* https://api.stack-auth.com wss://*.supabase.co wss://*.stack-auth.com https://*.supabase.co https://iptv-org.github.io; media-src 'self' https: blob:; object-src 'none'; frame-ancestors 'none'", "cache-control": "no-cache, no-store, must-revalidate", "connection": "keep-alive", "referrer-policy": "strict-origin-when-cross-origin", "permissions-policy": "camera=(), microphone=(), geolocation=()", "access-control-allow-origin": "*", "x-xss-protection": "1; mode=block"}}], "consoleLogs": ["[info] %cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold", "[info] %c%s%c [2025-07-26T09:22:39.873Z] [INFO] [stream] Watch page loaded {\n  \"channelId\": \"iptv-30AGolfKingdom.us\"\n} background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server  ", "[info] %c%s%c [2025-07-26T09:22:39.873Z] [INFO] [stream] Fetching from API {\n  \"apiUrl\": \"http://localhost:3000/api/iptv/channel/30AGolfKingdom.us\"\n} background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server  ", "[info] %c%s%c [2025-07-26T09:22:39.977Z] [INFO] [stream] IPTV channel data loaded {\n  \"id\": \"30AGolfKingdom.us\",\n  \"name\": \"30A Golf Kingdom\",\n  \"url\": \"https://30a-tv.com/feeds/vidaa/golf.m3u8\"\n} background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server  ", "[info] %c%s%c [2025-07-26T09:22:39.978Z] [INFO] [stream] Final channel data {\n  \"channelId\": \"iptv-30AGolfKingdom.us\",\n  \"hasChannelData\": true,\n  \"channelName\": \"30A Golf Kingdom\",\n  \"channelUrl\": \"https://30a-tv.com/feeds/vidaa/golf.m3u8\",\n  \"isLiveGame\": false,\n  \"usingDefault\": false\n} background: #e6e6e6;background: light-dark(rgba(0,0,0,0.1), rgba(255,255,255,0.25));color: #000000;color: light-dark(#000000, #ffffff);border-radius: 2px  Server  ", "[log] [2025-07-26T09:22:40.064Z] 🔐 Iniciando configuração do Stack Auth Client... ", "[debug] [2025-07-26T09:22:40.065Z] 🌐 Ambiente: Browser detectado ", "[log] 🔐 [StackClient] Verificando variáveis de ambiente...", "[log]   - NEXT_PUBLIC_STACK_PROJECT_ID: ✅ Definida", "[log]   - NEXT_PUBLIC_STACK_PUBLISHABLE_CLIENT_KEY: ✅ Definida", "[log]   - NEXT_PUBLIC_APP_URL: http://localhost:3000", "[debug] [2025-07-26T09:22:40.066Z] 📦 Configuração Stack Auth: {projectId: b5d6...9343, publishableClientKey: pck_...2pbr, tokenStore: cookie, urls: Object}", "[log] 🔐 [StackClient] Criando instância do StackClientApp...", "[log] [2025-07-26T09:22:40.067Z] ✅ Stack Auth Client inicializado com sucesso ", "[log] [2025-07-26T09:22:40.067Z] 🔚 Processo de configuração do Stack Auth Client encerrado ", "[log] [2025-07-26T09:22:40.076Z] 🔐 useAuthOptional hook chamado - modo: opcional ", "[log] [2025-07-26T09:22:40.084Z] 🔐 useAuthOptional hook chamado - modo: opcional ", "[log] [2025-07-26T09:22:40.084Z] 🔐 useAuthOptional hook chamado - modo: opcional ", "[log] [2025-07-26T09:22:40.092Z] ℹ️ Usu<PERSON>rio não autenticado (opcional) ", "[log] 🚀 SportsSyncProvider: Preparando serviço...", "[log] 🛑 Serviço de sincronização parado", "[log] [2025-07-26T09:22:40.097Z] ℹ️ Usu<PERSON>rio não autenticado (opcional) ", "[log] 🚀 SportsSyncProvider: Preparando serviço...", "[log] [2025-07-26T09:22:40.380Z] 🔐 useAuthOptional hook chamado - modo: opcional ", "[log] [2025-07-26T09:22:40.380Z] 🔐 useAuthOptional hook chamado - modo: opcional ", "[log] [2025-07-26T09:22:40.381Z] 🔐 useAuthOptional hook chamado - modo: opcional ", "[log] [2025-07-26T09:22:40.381Z] 🔐 useAuthOptional hook chamado - modo: opcional ", "[info] [2025-07-26T09:22:40.388Z] [INFO] [player] EnhancedHlsPlayer mounted {\n  \"channelName\": \"30A Golf Kingdom\",\n  \"streamUrl\": \"https://30a-tv.com/feeds/vidaa/golf.m3u8\",\n  \"isLive\": true,\n  \"hasHeaders\": true,\n  \"hasUserAgent\": false\n}", "[info] [2025-07-26T09:22:40.389Z] [INFO] [player] Attempting proxy playback {\n  \"url\": \"/api/proxy/stream?url=https%3A%2F%2F30a-tv.com%2Ffeeds%2Fvidaa%2Fgolf.m3u8&channel=30A%20Golf%20Kingdom&channelId=30AGolfKingdom.us\",\n  \"retryCount\": 0\n}", "[info] [2025-07-26T09:22:40.394Z] [INFO] [player] EnhancedHlsPlayer mounted {\n  \"channelName\": \"30A Golf Kingdom\",\n  \"streamUrl\": \"https://30a-tv.com/feeds/vidaa/golf.m3u8\",\n  \"isLive\": true,\n  \"hasHeaders\": true,\n  \"hasUserAgent\": false\n}", "[info] [2025-07-26T09:22:40.394Z] [INFO] [player] Attempting proxy playback {\n  \"url\": \"/api/proxy/stream?url=https%3A%2F%2F30a-tv.com%2Ffeeds%2Fvidaa%2Fgolf.m3u8&channel=30A%20Golf%20Kingdom&channelId=30AGolfKingdom.us\",\n  \"retryCount\": 0\n}", "[error] [2025-07-26T09:22:40.653Z] [ERROR] [player] HLS error {\n  \"type\": \"mediaError\",\n  \"details\": \"manifestIncompatibleCodecsError\",\n  \"fatal\": true,\n  \"errorType\": \"unknown\",\n  \"url\": \"http://localhost:3000/api/proxy/stream?url=https%3A%2F%2F30a-tv.com%2Ffeeds%2Fvidaa%2Fgolf.m3u8&channel=30A%20Golf%20Kingdom&channelId=30AGolfKingdom.us\",\n  \"channelName\": \"30A Golf Kingdom\",\n  \"usingProxy\": false\n}", "[log] 🚀 Serviço de sincronização iniciado (simplificado)", "[log] ✅ Serviço iniciado com sucesso", "[log] 🚀 Fazendo primeira chamada para todas as APIs...", "[log] ✅ Primeira chamada concluída!", "[log] 📊 12/12 APIs responderam com sucesso", "[log] 📋 Total de 63 fixtures ao vivo encontrados"]}