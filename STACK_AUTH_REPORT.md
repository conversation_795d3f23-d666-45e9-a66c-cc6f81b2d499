# Relatório Stack Auth - StreamPlus España

## 1. Logs Adicionados

### Middleware (`src/middleware.ts`)
```typescript
console.log(`\n🎯 [Middleware] Processando: ${pathname}`)
console.log(`🎯 [Middleware] Método: ${request.method}`)
console.log(`🎯 [Middleware] Rota pública? ${isPublicRoute}`)
console.log(`🔒 [Middleware] Rota protegida detectada: ${pathname}`)
console.log(`🍪 [Middleware] Cookies:`, {
  'streamplus-test-user': testUserCookie ? '✅ Presente' : '❌ Ausente',
  'stack-auth-token': stackAuthCookie ? '✅ Presente' : '❌ Ausente'
})
```

### Stack Server (`src/lib/auth/stack-server.ts`)
```typescript
console.log("\n🔐 [StackServerApp] Inicializando Stack Auth Server...")
console.log("🔐 [StackServerApp] Variáveis de ambiente:")
console.log("  - NEXT_PUBLIC_STACK_PROJECT_ID:", projectId ? "✅ Definida" : "❌ FALTANDO")
console.log("  - NEXT_PUBLIC_STACK_PUBLISHABLE_CLIENT_KEY:", publishableKey ? "✅ Definida" : "❌ FALTANDO")
console.log("  - STACK_SECRET_SERVER_KEY:", secretKey ? "✅ Definida" : "❌ FALTANDO")
```

### Stack Client (`src/lib/auth/stack-client.tsx`)
```typescript
AuthLogger.log('🔐 Iniciando configuração do Stack Auth Client...')
AuthLogger.debug('🌐 Ambiente: Browser detectado')
AuthLogger.debug('📦 Configuração Stack Auth:', {
  projectId: AuthLogger.maskSensitive(config.projectId),
  publishableClientKey: AuthLogger.maskSensitive(config.publishableClientKey),
  tokenStore: config.tokenStore,
  urls: config.urls
})
```

### Hooks de Auth (`src/hooks/use-auth.ts`)
```typescript
AuthLogger.log('🔐 useAuth hook chamado - modo: redirect')
AuthLogger.log('✅ Usuário autenticado:', {
  id: user.id,
  email: AuthLogger.maskSensitive(user.primaryEmail || 'sem email'),
  displayName: user.displayName
})
```

## 2. Estrutura do Stack Auth

### Configuração do Servidor
**Arquivo:** `src/lib/auth/stack-server.ts`
- Cria instância do StackServerApp
- Usa cookies Next.js para armazenar tokens
- Define URLs de redirecionamento
- Requer 3 variáveis de ambiente:
  - `NEXT_PUBLIC_STACK_PROJECT_ID`
  - `NEXT_PUBLIC_STACK_PUBLISHABLE_CLIENT_KEY`
  - `STACK_SECRET_SERVER_KEY`

### Configuração do Cliente
**Arquivo:** `src/lib/auth/stack-client.tsx`
- Inicializa StackClientApp no browser
- Protege contra erros de extensões do navegador
- Usa o mesmo conjunto de URLs do servidor
- Renderiza StackProvider e StackTheme

### Hooks de Autenticação
**Arquivo:** `src/hooks/use-auth.ts`
- `useAuth()`: Redireciona se não autenticado
- `useAuthOptional()`: Não redireciona
- Integração com usuários de teste do localStorage

### Sistema de Usuários de Teste
**Arquivo:** `src/lib/auth/test-users.ts`
- Define 3 usuários de teste:
  - <EMAIL> (admin)
  - <EMAIL> (user)
  - <EMAIL> (user)
- Cada usuário tem ID, role e subscription

### Sincronização de Sessão
**Arquivo:** `src/app/api/auth/sync-test-user/route.ts`
- Sincroniza usuários de teste com cookies
- Permite que middleware valide autenticação

## 3. Fluxo de Autenticação

1. **Login Page** (`/login`)
   - Tenta Stack Auth primeiro
   - Se falhar, verifica credenciais de teste
   - Salva no localStorage E cookie via API

2. **Middleware**
   - Verifica cookies `streamplus-test-user` ou `stack-auth-token`
   - Redireciona para login se nenhum presente

3. **useAuth Hook**
   - Verifica Stack Auth primeiro
   - Depois verifica localStorage para test users
   - Sincroniza com cookies automaticamente

## 4. Problemas Potenciais

### Erro de Extensão
```
TypeError: Cannot read properties of null (reading 'type')
```
- Ocorre em extensões do navegador (MetaMask, Trust Wallet)
- Já adicionada proteção no layout principal

### Configuração Stack Auth
- Precisa de conta em https://app.stack-auth.com
- Configurar OAuth providers (Google, GitHub)
- Adicionar URLs de callback corretas

### Variáveis de Ambiente
Verificar se `.env.local` contém:
```env
NEXT_PUBLIC_STACK_PROJECT_ID=seu-project-id
NEXT_PUBLIC_STACK_PUBLISHABLE_CLIENT_KEY=sua-key-publica
STACK_SECRET_SERVER_KEY=sua-key-secreta
```

## 5. Como Debugar

1. **Verificar Console do Browser**
   - Filtrar por "Auth", "Stack", "Login"
   - Procurar erros de extensão

2. **Verificar Logs do Servidor**
   - Logs do middleware mostram fluxo de requisições
   - Logs do Stack Auth mostram inicialização

3. **Testar Login**
   - Use credenciais de teste primeiro
   - Verifique cookies no DevTools
   - Verifique localStorage

4. **Verificar Redirecionamentos**
   - `/browse` é área protegida principal
   - Deve redirecionar para `/login` se não autenticado