#!/bin/bash

# Lista de arquivos para atualizar
files=(
  "/Users/<USER>/Documents/iptv/streamplus-espana/src/app/about/page.tsx"
  "/Users/<USER>/Documents/iptv/streamplus-espana/src/app/terms/page.tsx"
  "/Users/<USER>/Documents/iptv/streamplus-espana/src/app/privacy/page.tsx"
  "/Users/<USER>/Documents/iptv/streamplus-espana/src/app/cookies/page.tsx"
  "/Users/<USER>/Documents/iptv/streamplus-espana/src/app/help/page.tsx"
  "/Users/<USER>/Documents/iptv/streamplus-espana/src/app/faq/page.tsx"
  "/Users/<USER>/Documents/iptv/streamplus-espana/src/app/status/page.tsx"
  "/Users/<USER>/Documents/iptv/streamplus-espana/src/app/careers/page.tsx"
  "/Users/<USER>/Documents/iptv/streamplus-espana/src/app/sitemap/page.tsx"
  "/Users/<USER>/Documents/iptv/streamplus-espana/src/app/gdpr/page.tsx"
  "/Users/<USER>/Documents/iptv/streamplus-espana/src/app/press/page.tsx"
  "/Users/<USER>/Documents/iptv/streamplus-espana/src/app/partners/page.tsx"
)

for file in "${files[@]}"; do
  echo "Processando: $file"
  
  # Adicionar import se não existir
  if ! grep -q "BackToHome" "$file"; then
    # Adicionar import após o import do Footer
    sed -i '' "/import { Footer } from '@\/components\/footer'/a\\
import { BackToHome } from '@/components/back-to-home'" "$file"
  fi
  
  # Adicionar componente no início da section principal
  # Procurar pelo primeiro max-w-7xl ou max-w-4xl e adicionar BackToHome
  if ! grep -q "<BackToHome" "$file"; then
    sed -i '' '/<div className=".*max-w-[47]xl mx-auto px-4/{
      N
      s/\(<div className=".*max-w-[47]xl mx-auto px-4.*>\)/\1\
          <BackToHome \/>/
    }' "$file"
  fi
done

echo "✅ Concluído!"