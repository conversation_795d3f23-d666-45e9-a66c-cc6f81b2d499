# Fluxo de Pagamento Stripe - NewSpports

## ✅ Sistema Implementado

### 1. **Produto e Preço**
- Produto: NewSpports Premium España
- Preço: €20/mês (recorrente mensal)
- ID do Preço: `price_1RpjBgDMzmKMrtWZnEISCCIy`

### 2. **Fluxo de Pagamento**

```
Usuário clica "Suscribirse Ahora"
    ↓
Cria sessão de checkout no Stripe
    ↓
Redireciona para página de pagamento Stripe
    ↓
Usuário completa pagamento
    ↓
Stripe envia webhook para /api/stripe/webhook
    ↓
Sistema atualiza perfil do usuário:
  - subscription_status = 'active'
  - subscription_tier = 'premium'
  - subscription_current_period_end = data + 30 dias
    ↓
Usuário redirecionado para /subscription/success
    ↓
Acesso liberado a todos os canais por 1 mês
```

### 3. **Verificação de Acesso aos Canais**

Quando usuário tenta acessar um canal:
1. API verifica se tem assinatura ativa
2. Se sim → libera stream
3. Se n<PERSON> → retorna erro 403 "Assinatura premium necessária"

### 4. **Renovação Automática**

- Stripe cobra automaticamente a cada mês
- Webhook atualiza período da assinatura
- Acesso continua sem interrupção

### 5. **Cancelamento**

- Usuário pode cancelar via portal do cliente
- Acesso continua até fim do período pago
- Após expirar, volta para tier 'free'

## 🔧 Configuração no Stripe Dashboard

### Webhook Endpoint
Adicionar webhook em: https://dashboard.stripe.com/webhooks

URL: `https://seu-dominio.com/api/stripe/webhook`

Eventos necessários:
- `checkout.session.completed`
- `invoice.payment_succeeded`
- `invoice.payment_failed`
- `customer.subscription.deleted`
- `customer.subscription.updated`

### Variáveis de Ambiente em Produção

```env
STRIPE_SECRET_KEY=sk_live_...
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_live_...
STRIPE_WEBHOOK_SECRET=whsec_...
STRIPE_PRODUCT_ID=prod_SlBxDaGOJCF0ZT
STRIPE_PRICE_ID=price_1RpjBgDMzmKMrtWZnEISCCIy
```

## 📱 Teste do Sistema

1. Criar conta de teste
2. Clicar em "Suscribirse Ahora - €20/mes"
3. Usar cartão de teste: 4242 4242 4242 4242
4. Verificar se foi redirecionado para /subscription/success
5. Tentar acessar canais premium
6. Verificar no banco se subscription_tier = 'premium'

## ⚠️ Importante

- O webhook DEVE estar configurado em produção
- Sem webhook, pagamentos não ativarão assinaturas
- Verificar logs do webhook no Stripe Dashboard
- Testar com cartões reais apenas em produção