API FREE EPG

````markdown
# 📺 Integração com API da EPG.pw (Guia de TV)

## 🎯 Objetivo
Criar um componente em React (ou Next.js) que consome a API pública da [EPG.pw](https://epg.pw) e exibe a **grade de programação diária** de um canal de TV (como "BBC R n Gael"), com horário e nome do programa. O foco inicial é exibir o dia atual, mas o código deve estar preparado para filtrar por data.

---

## 🔗 API utilizada

A EPG.pw fornece programação em **JSON** com base no `channel_id`.

### ✅ Endpoint base:
```http
GET https://epg.pw/api/epg.json?channel_id={ID}
````

### ▶️ Exemplo real:

```http
GET https://epg.pw/api/epg.json?channel_id=12476
```

Este exemplo traz a grade da **BBC R n Gael**.

---

## 🔄 Estrutura da resposta JSON

A resposta será um array de objetos como:

```json
[
  {
    "start": "2025-07-20T11:00:00+00:00",
    "title": "Dèanamaid Adhradh"
  },
  {
    "start": "2025-07-20T11:30:00+00:00",
    "title": "Alleluia"
  },
  ...
]
```

> O campo `start` vem em formato **ISO 8601**, que deve ser convertido para horário local do usuário.

---

## 🛠️ O que você precisa construir

### 1. Um componente/função que:

* Faz **requisição HTTP GET** ao endpoint da API
* Recebe os dados da grade de programação
* Exibe os dados em **lista formatada** com:

  * 🕒 Hora (`start`)
  * 📺 Título (`title`)

### 2. Recursos adicionais (opcional):

* Seleção de **canal** por `channel_id`
* Seleção de **data** para exibir dias futuros
* Conversão automática de fuso horário
* Filtro de resultados por data no frontend (usando `.filter()` no array)

---

## 💻 Sugestão de Stack

* **Next.js** com `useEffect` e `fetch` ou `axios`
* **TailwindCSS** ou `shadcn/ui` para UI simples
* **dayjs** ou `date-fns` para formatar o horário ISO

---

## 📦 Exemplo de implementação (Next.js)

```tsx
// components/TVGuide.tsx
import React, { useEffect, useState } from 'react'
import dayjs from 'dayjs'

const CHANNEL_ID = 12476 // BBC R n Gael

export default function TVGuide() {
  const [programs, setPrograms] = useState([])

  useEffect(() => {
    fetch(`https://epg.pw/api/epg.json?channel_id=${CHANNEL_ID}`)
      .then(res => res.json())
      .then(data => setPrograms(data))
  }, [])

  return (
    <div className="p-4">
      <h2 className="text-xl font-bold mb-2">Programação de hoje</h2>
      <ul className="space-y-2">
        {programs.map((item: any, idx: number) => (
          <li key={idx} className="flex gap-2">
            <span className="font-mono w-20 text-gray-700">
              {dayjs(item.start).format('HH:mm')}
            </span>
            <span>{item.title}</span>
          </li>
        ))}
      </ul>
    </div>
  )
}
```

---

## 🧪 Teste local

1. Crie um projeto com:

```bash
npx create-next-app@latest epg-tv
cd epg-tv
npm install dayjs
```

2. Adicione o componente acima em `components/TVGuide.tsx`
3. Use ele em `pages/index.tsx`:

```tsx
import TVGuide from '@/components/TVGuide'

export default function Home() {
  return (
    <main className="p-6">
      <TVGuide />
    </main>
  )
}
```

---

## ⚙️ Extras (opcional)

* Trocar o canal dinamicamente com um `<select>` alterando o `channel_id`
* Adicionar filtro por data no frontend:

```tsx
programs.filter(p => p.start.startsWith("2025-07-20"))
```

* Trocar fuso horário para o do navegador:

```tsx
dayjs.utc(item.start).local().format('HH:mm')
```

---

## ✅ Conclusão

A EPG.pw permite criar facilmente um **guia de TV dinâmico e gratuito**, ideal para projetos como:

* Sites de streaming
* Portais de programação de canais
* Páginas com destaque de conteúdo diário

Utilize esse modelo como base para escalar e integrar com múltiplos canais.

```

---

Se quiser, posso adaptar esse tutorial para PHP, Node.js, React puro ou qualquer outro stack que esteja usando. Deseja uma versão alternativa?
```
