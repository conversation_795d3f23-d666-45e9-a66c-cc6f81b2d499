Api Hub

https://app.swaggerhub.com/apis-docs/allsportdb/AllSportDBAPI/3.0.0-oas3#/default/GetLocations


https://api.allsportdb.com/v3/

API KEY: 20d8be22-0948-4131-ae61-37dd981028bd


default


GET
/calendar
Get list of events




Get list of events. If week or dateFrom and dateTo not provided then today's date will be used. To query data by parameters other than week - use large date range (for example, dateFrom = '2010-01-01' and dateTo = '2030-01-01').

Parameters
Name	Description
week
integer
(query)
pass an optional week index parameter (for example, -1 = last week, 0 = this week, 1 = next week, n = n-th week; dateFrom and dateTo period used by default)

week
dateFrom
string($date)
(query)
pass an optional period start date (today's date used by default)

dateFrom
dateTo
string($date)
(query)
pass an optional period end date (today's date used by default)

dateTo
dateToday
string($date)
(query)
pass an optional today's date to account for different time zones (can only have value of yesterday, today or tomorrow)

dateToday
year
integer
(query)
pass an optional year parameter

year
eventId
integer
(query)
pass an optional event Id parameter

eventId
event
string
(query)
pass an optional event Name parameter

event
locationId
integer
(query)
pass an optional location Id parameter

locationId
location
string
(query)
pass an optional location Name parameter

location
competitionId
integer
(query)
pass an optional competition Id parameter

competitionId
competition
string
(query)
pass an optional competition Name parameter

competition
sportId
integer
(query)
pass an optional sport Id parameter

sportId
sport
string
(query)
pass an optional sport Name parameter

sport
countryId
integer
(query)
pass an optional country Id parameter

countryId
country
string
(query)
pass an optional country Name parameter

country
regionId
integer
(query)
pass an optional region Id parameter

regionId
region
string
(query)
pass an optional region Name parameter

region
continentId
integer
(query)
pass an optional competition continent Id parameter

continentId
continent
string
(query)
pass an optional competition continent Name parameter

continent
locationContinentId
integer
(query)
pass an optional location continent Id parameter

locationContinentId
locationContinent
string
(query)
pass an optional location continent Name parameter

locationContinent
liveUrlExists
integer
(query)
pass an optional parameter to filter events with liveUrl value set (0 = false, 1 = true)

liveUrlExists
filter
string
(query)
pass an optional search filter parameter

filter
page
integer
(query)
pass an optional page parameter (default page = 1)

page
Responses
Code	Description	Links
200	
search results matching criteria

Media type

application/json
Controls Accept header.
Example Value
Schema
[
  {
    "id": 8991,
    "name": "2021 Tennis Grand Slam - Wimbledon",
    "date": "28 June - 11 July 2021",
    "dateFrom": "2021-06-28T00:00:00",
    "dateTo": "2021-07-11T00:00:00",
    "sportId": 17,
    "sport": "Tennis",
    "emoji": "🎾",
    "competitionId": 316,
    "competition": "Tennis Grand Slam",
    "continentId": 2,
    "continent": "World",
    "url": "https://allsportdb.com/Events/2021-Tennis-Grand-Slam-Wimbledon-8991",
    "webUrl": "https://www.wimbledon.com",
    "wikiUrl": "https://en.wikipedia.org/wiki/The_Championships_Wimbledon",
    "ticketsUrl": "https://www.wimbledon.com/en_GB/tickets/index.html",
    "facebookUrl": "https://www.facebook.com/wimbledon",
    "twitterUrl": "https://twitter.com/search?q=%23GrandSlam%20OR%20%40Wimbledon%20OR%20%23Wimbledon2021",
    "liveUrl": "https://www.wimbledon.com",
    "liveUrlPaid": false,
    "logoUrl": "https://storage.googleapis.com/allsportdb-logo-storage/orig/event/85be32cd-dbe3-499f-9e3a-f23f90dc0edf",
    "logoThumbnailUrl": "https://storage.googleapis.com/allsportdb-logo-storage/thumb/event/85be32cd-dbe3-499f-9e3a-f23f90dc0edf",
    "logoSmallUrl": "https://storage.googleapis.com/allsportdb-logo-storage/large/event/85be32cd-dbe3-499f-9e3a-f23f90dc0edf",
    "dateModified": "2020-05-09T13:32:36.997",
    "location": [
      {
        "id": 34,
        "regionId": null,
        "name": "United Kingdom",
        "continent": "Europe",
        "continentId": 3,
        "code": "gb",
        "emoji": "🇬🇧",
        "flagUrl": "https://storage.googleapis.com/allsportdb-logo-storage/flags/flag-gb.png",
        "locations": [
          {
            "id": 3423,
            "name": "London",
            "lat": 51.51,
            "lng": -0.13
          }
        ]
      }
    ]
  }
]
No links
400	
bad input parameter

No links
401	
access token is missing or invalid

No links
500	
internal server error

No links

GET
/sports
Get list of sports




Get list of sports. If no parameters provided then all sports will be returned.

Parameters
Name	Description
id
integer
(query)
pass an optional sport Id parameter

id
name
string
(query)
pass an optional sport Name parameter

name
season
string
(query)
pass an optional Season parameter

Available values : summer, winter, other


--
page
integer
(query)
pass an optional page parameter (default page = 1)

page
Responses
Code	Description	Links
200	
search results matching criteria

Media type

application/json
Controls Accept header.
Example Value
Schema
[
  {
    "id": 1,
    "name": "Alpine Skiing",
    "season": "Winter",
    "emoji": "⛷",
    "url": "https://allsportdb.com/Sports/Alpine-Skiing"
  }
]
No links
400	
bad input parameter

No links
401	
access token is missing or invalid

No links
500	
internal server error

No links

GET
/competitions
Get list of competitions




Get list of competitions. If no parameters provided then empty list will be returned.

Parameters
Name	Description
id
integer
(query)
pass an optional competition Id parameter

id
name
string
(query)
pass an optional competition Name parameter

name
sportId
integer
(query)
pass an optional sport Id parameter

sportId
sport
string
(query)
pass an optional sport Name parameter

sport
continentId
integer
(query)
pass an optional continent Id parameter

continentId
continent
string
(query)
pass an optional continent Name parameter

continent
page
integer
(query)
pass an optional page parameter (default page = 1)

page
Responses
Code	Description	Links
200	
search results matching criteria

Media type

application/json
Controls Accept header.
Example Value
Schema
[
  {
    "id": 43,
    "name": "UEFA Champions League",
    "ageGroup": "Senior",
    "gender": "Men",
    "emoji": "⚽",
    "continentId": 3,
    "continent": "Europe",
    "sportId": 1,
    "sport": "Football",
    "url": "https://allsportdb.com/Competitions/UEFA-Champions-League-43",
    "logoUrl": "https://storage.googleapis.com/allsportdb-logo-storage/orig/comp/fdc8814f-1ea5-443c-9e49-93509556a448",
    "logoSmallUrl": "https://storage.googleapis.com/allsportdb-logo-storage/large/comp/fdc8814f-1ea5-443c-9e49-93509556a448",
    "logoThumbnailUrl": "https://storage.googleapis.com/allsportdb-logo-storage/thumb/comp/fdc8814f-1ea5-443c-9e49-93509556a448",
    "dateModified": "2020-04-27T17:12:24.12"
  }
]
No links
400	
bad input parameter

No links
401	
access token is missing or invalid

No links
500	
internal server error

No links

GET
/countries
Get list of countries




Get list of countries. If no parameters provided then all countries will be returned.

Parameters
Name	Description
id
integer
(query)
pass an optional country Id parameter

id
name
string
(query)
pass an optional country Name parameter

name
continentId
integer
(query)
pass an optional continent Id parameter

continentId
continent
string
(query)
pass an optional continent Name parameter

continent
page
integer
(query)
pass an optional page parameter (default page = 1)

page
Responses
Code	Description	Links
200	
search results matching criteria

Media type

application/json
Controls Accept header.
Example Value
Schema
[
  {
    "id": 10,
    "name": "France",
    "code": "fr",
    "emoji": "🇫🇷",
    "continentId": 3,
    "continent": "Europe",
    "url": "https://allsportdb.com/Countries/France",
    "flagUrl": "https://storage.googleapis.com/allsportdb-logo-storage/flags/flag-fr.png"
  }
]
No links
400	
bad input parameter

No links
401	
access token is missing or invalid

No links
500	
internal server error

No links

GET
/continents
Get list of continents




Get list of continents. If no parameters provided then all continents will be returned.

Parameters
Name	Description
id
integer
(query)
pass an optional continent Id parameter

id
name
string
(query)
pass an optional continent Name parameter

name
page
integer
(query)
pass an optional page parameter (default page = 1)

page
Responses
Code	Description	Links
200	
search results matching criteria

Media type

application/json
Controls Accept header.
Example Value
Schema
[
  {
    "id": 3,
    "name": "Europe"
  }
]
No links
400	
bad input parameter

No links
401	
access token is missing or invalid

No links
500	
internal server error

No links

GET
/regions
Get list of regions




Get list of regions (United Kingdom only). If no parameters provided then all regions will be returned.

Parameters
Name	Description
id
integer
(query)
pass an optional region Id parameter

id
name
string
(query)
pass an optional region Name parameter

name
countryId
integer
(query)
pass an optional country Id parameter

countryId
country
string
(query)
pass an optional country Name parameter

country
continentId
integer
(query)
pass an optional continent Id parameter

continentId
continent
string
(query)
pass an optional continent Name parameter

continent
page
integer
(query)
pass an optional page parameter (default page = 1)

page
Responses
Code	Description	Links
200	
search results matching criteria

Media type

application/json
Controls Accept header.
Example Value
Schema
[
  {
    "id": 1,
    "name": "England",
    "code": "eng",
    "emoji": "🏴",
    "countryId": 34,
    "country": "United Kingdom",
    "continentId": 3,
    "continent": "Europe",
    "url": "https://allsportdb.com/Regions/England",
    "flagUrl": "https://storage.googleapis.com/allsportdb-logo-storage/flags/flag-eng.png"
  }
]
No links
400	
bad input parameter

No links
401	
access token is missing or invalid

No links
500	
internal server error

No links

GET
/locations
Get list of locations




Get list of locations. If no parameters provided then empty list will be returned.

Parameters
Name	Description
id
integer
(query)
pass an optional location Id parameter

id
name
string
(query)
pass an optional location Name parameter

name
countryId
integer
(query)
pass an optional country Id parameter

countryId
country
string
(query)
pass an optional country Name parameter

country
regionId
integer
(query)
pass an optional region Id parameter

regionId
region
string
(query)
pass an optional region Name parameter

region
Responses
Code	Description	Links
200	
search results matching criteria

Media type

application/json
Controls Accept header.
Example Value
Schema
[
  {
    "id": 1,
    "name": "London",
    "url": "https://allsportdb.com/Locations/London-3423",
    "lat": 51.51,
    "lng": -0.13,
    "dateModified": "2020-04-27T17:12:41.557",
    "countryId": 34,
    "country": "United Kingdom",
    "countryCode": "gb",
    "countryEmoji": "🇬🇧",
    "countryFlagUrl": "https://storage.googleapis.com/allsportdb-logo-storage/flags/flag-gb.png",
    "regionId": 1,
    "region": "England",
    "regionCode": "eng",
    "regionEmoji": "🏴",
    "regionFlagUrl": "https://storage.googleapis.com/allsportdb-logo-storage/flags/flag-eng.png",
    "continentId": 3,
    "continent": "Europe"
  }
]
No links
400	
bad input parameter

No links
401	
access token is missing or invalid

No links
500	
internal server error

No links

Schemas

Event{
id	[...]
name	[...]
date	[...]
dateFrom	[...]
dateTo	[...]
sportId	[...]
sport	[...]
emoji	[...]
competitionId	[...]
competition	[...]
continentId	[...]
continent	[...]
url	[...]
webUrl	[...]
wikiUrl	[...]
ticketsUrl	[...]
facebookUrl	[...]
twitterUrl	[...]
liveUrl	[...]
liveUrlPaid	[...]
logoUrl	[...]
logoThumbnailUrl	[...]
logoSmallUrl	[...]
dateModified	[...]
location	[...]
}
Sport{
id	[...]
name	[...]
season	[...]
emoji	[...]
url	[...]
}
Competition{
id	[...]
name	[...]
ageGroup	[...]
gender	[...]
emoji	[...]
continentId	[...]
continent	[...]
sportId	[...]
sport	[...]
url	[...]
logoUrl	[...]
logoSmallUrl	[...]
logoThumbnailUrl	[...]
dateModified	[...]
}
Country{
id	[...]
name	[...]
code	[...]
emoji	[...]
continentId	[...]
continent	[...]
url	[...]
flagUrl	[...]
}
Continent{
id*	[...]
name*	[...]
}
Region{
id	[...]
name	[...]
code	[...]
emoji	[...]
countryId	[...]
country	[...]
continentId	[...]
continent	[...]
url	[...]
flagUrl	[...]
}
Location{
id	[...]
name	[...]
url	[...]
lat	[...]
lng	[...]
dateModified	[...]
countryId	[...]
country	[...]
countryCode	[...]
countryEmoji	[...]
countryFlagUrl	[...]
regionId	[...]
region	[...]
regionCode	[...]
regionEmoji	[...]
regionFlagUrl	[...]
continentId	[...]
continent	[...]
}



