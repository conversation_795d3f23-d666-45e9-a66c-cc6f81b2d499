# Configuração de Autenticação - StreamPlus España

## Status Atual ✅

- Login com email/senha: Configurado com Stack Auth
- Login com Google (Gmail): Configurado com Stack Auth  
- Todos os arquivos de teste removidos
- Pronto para produção

## Instruções para Deploy

### 1. Verificar Variáveis de Ambiente

As seguintes variáveis devem estar configuradas no ambiente de produção:

```env
# Stack Auth (obrigatório)
NEXT_PUBLIC_STACK_PROJECT_ID=b5d6eebd-aa16-4101-9cad-8717743c9343
NEXT_PUBLIC_STACK_PUBLISHABLE_CLIENT_KEY=pck_m988zs8n54pjh8rjgdv70b9crgspcpy3vq0zwwgy72pbr
STACK_SECRET_SERVER_KEY=ssk_g73pa3fw1s8hp7w21ha6zyjngcbg8mz60ccx0mkvmnkm0

# URL da aplicação
NEXT_PUBLIC_APP_URL=https://seu-dominio.com
```

### 2. Configurar Stack Auth Dashboard

1. Acesse: https://app.stack-auth.com
2. Configure o OAuth do Google:
   - Authorized redirect URIs: `https://seu-dominio.com/handler/oauth-callback`
   - Authorized JavaScript origins: `https://seu-dominio.com`

### 3. Build e Deploy

```bash
# Build de produção
pnpm build

# Iniciar servidor de produção
pnpm start
```

### 4. Testar Autenticação

Após o deploy, teste:
1. Criar conta com email/senha
2. Login com email/senha
3. Login com Google
4. Verificar redirecionamento para /browse

## Troubleshooting

### Erro: "Stack Auth não configurado"
- Verifique se as variáveis de ambiente estão configuradas
- Reinicie o servidor após adicionar as variáveis

### Erro ao fazer login com Google
- Verifique as URLs de callback no Stack Auth dashboard
- Certifique-se que o domínio está autorizado no Google Console

### Usuários não aparecem no Stack Auth
- Verifique se o PROJECT_ID está correto
- Confirme que está usando o ambiente correto (dev/prod)

## Página de Teste

Para verificar se tudo está funcionando:
- Acesse: `/test-auth` (disponível apenas em desenvolvimento)

Esta página mostra:
- Status das variáveis de ambiente
- Estado da autenticação
- Informações do usuário logado