<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste de Expiração do Trial</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        .status {
            background: #2a2a2a;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
        }
        .success { border-left: 4px solid #4CAF50; }
        .warning { border-left: 4px solid #ff9800; }
        .error { border-left: 4px solid #f44336; }
        .info { border-left: 4px solid #2196F3; }
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #45a049; }
        #timer {
            font-size: 48px;
            font-weight: bold;
            text-align: center;
            margin: 20px 0;
        }
        .blocked {
            background: #f44336;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            font-size: 24px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <h1>Teste de Expiração do Trial</h1>
    
    <div id="timer">5:00</div>
    
    <div class="status info">
        <h3>Estado Atual</h3>
        <p>Status: <span id="status">Iniciando...</span></p>
        <p>Trial Expirado: <span id="expired">Não</span></p>
        <p>Tem Acesso: <span id="hasAccess">Verificando...</span></p>
        <p>Bloqueado: <span id="blocked">Não</span></p>
    </div>
    
    <div id="blockMessage" class="blocked" style="display: none;">
        🚫 CONTEÚDO BLOQUEADO - TRIAL EXPIRADO
    </div>
    
    <div class="status warning">
        <h3>Controles de Teste</h3>
        <button onclick="simulateTrial(300)">Simular Trial Completo (5 min)</button>
        <button onclick="simulateTrial(10)">Simular Trial Rápido (10 seg)</button>
        <button onclick="expireNow()">Expirar Agora</button>
        <button onclick="clearAll()">Limpar Tudo</button>
    </div>
    
    <div class="status">
        <h3>Logs</h3>
        <div id="logs" style="max-height: 300px; overflow-y: auto; font-family: monospace; font-size: 12px;">
        </div>
    </div>

    <script>
        let intervalId = null;
        
        function log(message, type = 'info') {
            const logs = document.getElementById('logs');
            const time = new Date().toLocaleTimeString();
            const color = {
                info: '#2196F3',
                success: '#4CAF50',
                warning: '#ff9800',
                error: '#f44336'
            }[type] || '#fff';
            
            logs.innerHTML += `<div style="color: ${color}">[${time}] ${message}</div>`;
            logs.scrollTop = logs.scrollHeight;
        }
        
        function updateDisplay(timeRemaining, isExpired, hasAccess) {
            // Atualizar timer
            const minutes = Math.floor(timeRemaining / 60);
            const seconds = timeRemaining % 60;
            document.getElementById('timer').textContent = 
                `${minutes}:${seconds.toString().padStart(2, '0')}`;
            
            // Atualizar status
            document.getElementById('status').textContent = 
                isExpired ? 'Trial Expirado' : 'Trial Ativo';
            document.getElementById('expired').textContent = isExpired ? 'Sim' : 'Não';
            document.getElementById('hasAccess').textContent = hasAccess ? 'Sim' : 'Não';
            
            // Calcular se deve bloquear
            const shouldBlock = isExpired && !hasAccess;
            document.getElementById('blocked').textContent = shouldBlock ? 'SIM' : 'Não';
            
            // Mostrar/esconder mensagem de bloqueio
            document.getElementById('blockMessage').style.display = 
                shouldBlock ? 'block' : 'none';
            
            // Mudar cor do timer
            if (isExpired) {
                document.getElementById('timer').style.color = '#f44336';
            } else if (timeRemaining < 60) {
                document.getElementById('timer').style.color = '#ff9800';
            } else {
                document.getElementById('timer').style.color = '#4CAF50';
            }
        }
        
        function simulateTrial(duration) {
            clearInterval(intervalId);
            log(`Iniciando trial de ${duration} segundos`, 'info');
            
            const startTime = Date.now();
            let isExpired = false;
            const hasAccess = false; // Simular usuário sem acesso premium
            
            // Simular armazenamento no localStorage
            const trialData = {
                startTime: startTime,
                timeRemaining: duration,
                isExpired: false
            };
            localStorage.setItem('trial-test', JSON.stringify(trialData));
            
            function update() {
                const elapsed = Math.floor((Date.now() - startTime) / 1000);
                const remaining = Math.max(0, duration - elapsed);
                
                if (remaining <= 0 && !isExpired) {
                    isExpired = true;
                    log('⏰ TRIAL EXPIRADO!', 'error');
                    log('🚫 Conteúdo deve estar BLOQUEADO agora', 'warning');
                    
                    // Atualizar localStorage
                    trialData.timeRemaining = 0;
                    trialData.isExpired = true;
                    localStorage.setItem('trial-test', JSON.stringify(trialData));
                }
                
                updateDisplay(remaining, isExpired, hasAccess);
                
                if (remaining > 0) {
                    log(`Tempo restante: ${remaining}s`, 'info');
                }
            }
            
            update();
            intervalId = setInterval(update, 1000);
        }
        
        function expireNow() {
            clearInterval(intervalId);
            log('⚡ Expirando trial imediatamente', 'warning');
            updateDisplay(0, true, false);
            
            // Atualizar localStorage
            const trialData = {
                startTime: Date.now() - 301000, // 5+ minutos atrás
                timeRemaining: 0,
                isExpired: true
            };
            localStorage.setItem('trial-test', JSON.stringify(trialData));
        }
        
        function clearAll() {
            clearInterval(intervalId);
            localStorage.removeItem('trial-test');
            log('🗑️ LocalStorage limpo', 'success');
            updateDisplay(300, false, false);
        }
        
        // Verificar estado inicial
        const saved = localStorage.getItem('trial-test');
        if (saved) {
            const data = JSON.parse(saved);
            log('📦 Estado recuperado do localStorage', 'info');
            updateDisplay(data.timeRemaining, data.isExpired, false);
        } else {
            log('🆕 Nenhum estado salvo - novo usuário', 'info');
            updateDisplay(300, false, false);
        }
    </script>
</body>
</html>