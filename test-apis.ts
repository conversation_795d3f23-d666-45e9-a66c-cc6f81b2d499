import { APISportsClient } from './src/services/api/sports/api-sports.client'
import { allSportDBClient } from './src/services/api/sports/allsport-db.client'

// Função para testar uma API Sports
async function testAPISports(sport: string) {
  console.log(`\n🧪 Testando ${sport.toUpperCase()}...`)
  try {
    const client = new APISportsClient(sport)
    const events = await client.getLiveEvents()
    console.log(`✅ ${sport}: ${events.length} eventos ao vivo`)
    if (events.length > 0) {
      console.log(`   Exemplo: ${JSON.stringify(events[0], null, 2).slice(0, 200)}...`)
    }
    return { sport, count: events.length, success: true }
  } catch (error: any) {
    console.log(`❌ ${sport}: ${error.message}`)
    return { sport, count: 0, success: false, error: error.message }
  }
}

// Função para testar AllSportDB
async function testAllSportDB() {
  console.log(`\n🧪 Testando ALLSPORTDB...`)
  try {
    const today = new Date().toISOString().split('T')[0]
    const events = await allSportDBClient.getEvents({
      dateFrom: today,
      dateTo: today
    })
    console.log(`✅ AllSportDB: ${events.length} eventos hoje`)
    if (events.length > 0) {
      console.log(`   Exemplo: ${JSON.stringify(events[0], null, 2).slice(0, 200)}...`)
    }
    return { api: 'allsportdb', count: events.length, success: true }
  } catch (error: any) {
    console.log(`❌ AllSportDB: ${error.message}`)
    return { api: 'allsportdb', count: 0, success: false, error: error.message }
  }
}

// Executar testes
async function runTests() {
  console.log('🚀 TESTANDO TODAS AS APIs DIRETAMENTE (SEM CONTROLE DE QUOTA)\n')
  
  const sports = [
    'football', 'basketball', 'baseball', 'hockey',
    'volleyball', 'handball', 'rugby', 'formula1',
    'mma', 'nfl', 'nba', 'afl'
  ]
  
  const results = []
  
  // Testar cada API Sports sequencialmente
  for (const sport of sports) {
    const result = await testAPISports(sport)
    results.push(result)
    // Aguardar 1 segundo entre requisições
    await new Promise(resolve => setTimeout(resolve, 1000))
  }
  
  // Testar AllSportDB
  const allSportResult = await testAllSportDB()
  results.push(allSportResult)
  
  // Resumo
  console.log('\n📊 RESUMO DOS TESTES:')
  console.log('='.repeat(50))
  
  let totalEvents = 0
  results.forEach(r => {
    if ('sport' in r) {
      console.log(`${r.sport.padEnd(12)} : ${r.success ? '✅' : '❌'} ${r.count} eventos ${r.error ? `(${r.error})` : ''}`)
    } else {
      console.log(`${r.api.padEnd(12)} : ${r.success ? '✅' : '❌'} ${r.count} eventos ${r.error ? `(${r.error})` : ''}`)
    }
    totalEvents += r.count
  })
  
  console.log('='.repeat(50))
  console.log(`TOTAL: ${totalEvents} eventos encontrados`)
}

// Executar
runTests().catch(console.error)