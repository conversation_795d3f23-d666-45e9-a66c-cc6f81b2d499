# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

NewSpports is a premium Netflix-style streaming platform focusing on sports and news content. It features a 5-minute trial system with intelligent conversion, deep linking for mobile app integration, and multi-language support.

The platform includes real-time sports data integration with multiple API providers, EPG (Electronic Program Guide) functionality, and advanced sports analytics capabilities.

## Tech Stack

- **Frontend**: Next.js 15.4.2 (App Router), React 19.1.0, TypeScript 5.8.3, Shadcn/ui, Tailwind CSS 4
- **Streaming**: React Player 3.3.1 + HLS.js 1.6.7
- **State**: Zustand 5.0.6 with persistence (immer middleware)
- **Backend**: Supabase (PostgreSQL + Auth + Realtime), Edge Functions (Deno)
- **Auth**: Stack Auth 2.8.22 (OAuth providers, JWT tokens)
- **Package Manager**: npm (note: package.json shows pnpm commands but uses npm)
- **Forms**: React Hook Form 7.60.0 + Zod 4.0.5
- **Icons**: Lucide React
- **Notifications**: Sonner
- **Theme**: next-themes 0.4.6
- **Sports APIs**: API-Sports, AllSportDB, custom EPG integration
- **Payments**: Stripe 18.3.0

## Essential Commands

```bash
# Development
npm run dev                 # Start dev server on http://localhost:3000 (Turbopack)
npm run build               # Build for production (runs build:next)
npm start                   # Start production server
npm run lint                # Run ESLint

# Scripts
npm run reset-quotas        # Reset API quotas
```

## Project Structure

```
streamplus-espana/
├── src/
│   ├── app/                      # Next.js App Router
│   │   ├── (auth)/              # Auth routes group (login, register)
│   │   ├── (platform)/          # Platform routes group (browse, sports)
│   │   ├── api/                 # API routes
│   │   │   ├── sports/          # Sports data endpoints
│   │   │   ├── auth/            # Auth endpoints
│   │   │   ├── stripe/          # Stripe payment endpoints
│   │   │   └── generate-token/  # Mobile app token generation
│   │   ├── watch/               # Watch pages
│   │   ├── mobile-app/          # Mobile app download page
│   │   ├── tutorial/            # Tutorial pages
│   │   └── admin/               # Admin panel
│   ├── components/
│   │   ├── auth/                # Auth components
│   │   ├── player/              # Video player components
│   │   ├── sports/              # Sports components (EPG, fixtures)
│   │   ├── security/            # Security components (audit logs)
│   │   ├── ui/                  # Shadcn/ui components
│   │   └── theme-provider.tsx   # Theme provider wrapper
│   ├── hooks/
│   │   ├── use-auth.ts          # Auth hooks (simple, stack)
│   │   └── use-sports-data.ts   # Sports data hooks
│   ├── lib/
│   │   ├── auth/                # Auth utilities (Stack Auth)
│   │   ├── security/            # Security utilities (CSRF, rate limit)
│   │   ├── channel-logos.ts     # Channel logo mappings
│   │   └── supabase/
│   │       ├── client.ts        # Browser client
│   │       └── server.ts        # Server client
│   ├── services/
│   │   ├── api/sports/          # Sports API integrations
│   │   ├── background/          # Background services
│   │   └── cache/               # Caching services
│   ├── stores/
│   │   ├── player.store.ts      # Player state
│   │   ├── trial.store.ts       # Trial state (persisted)
│   │   └── sports.store.ts      # Sports data state
│   └── types/                   # TypeScript types
├── database/                    # Database schemas
├── scripts/                     # Utility scripts
└── supabase/migrations/         # Database migrations
```

## Architecture Overview

### Key Architectural Decisions

1. **App Router Pattern**: Using Next.js 15's App Router for:
   - Server Components by default
   - Nested layouts with `(auth)` and `(platform)` route groups
   - API routes in `app/api/`
   - Async params in dynamic routes

2. **State Management**: Zustand stores with persistence for:
   - Trial state (`trial.store.ts`) - manages 5-minute countdown with localStorage persistence
   - Player state (`player.store.ts`) - video player controls
   - Sports state (`sports.store.ts`) - sports data and fixtures
   - Using Immer middleware for immutable updates

3. **Video Streaming Architecture**:
   - HLS.js for adaptive bitrate streaming
   - Fallback URLs for redundancy
   - Quality selection with automatic/manual modes
   - Trial overlay system after 5 minutes

4. **Authentication Flow**:
   - Stack Auth for OAuth providers (Google, Apple, GitHub)
   - Simple auth with Supabase for email/password
   - JWT tokens for mobile app verification
   - Deep linking: `streamplus://verify?token=xxx`
   - CSRF protection and rate limiting
   - Admin authentication via cookies

5. **Database Design**:
   - RLS policies for security
   - Custom types: `channel_category`, `subscription_tier`, `content_rating`
   - Viewing sessions tracking
   - App verification tokens for mobile rewards
   - Trial sessions management

### Critical Components

1. **VideoPlayer** (`components/player/video-player.tsx`):
   - Integrates React Player with HLS.js
   - Manages trial countdown
   - Handles quality switching
   - Fallback stream support

2. **TrialOverlay** (`components/player/trial-overlay.tsx`):
   - Conversion-optimized overlay after trial expires
   - Deep link generation for mobile apps
   - Platform detection (iOS/Android/Desktop)

3. **Sports Integration** (`services/api/sports/`):
   - `unified-sports.service.ts`: Unified API for multiple providers
   - `request-manager.ts`: Request pooling and rate limiting
   - `sports-cache.service.ts`: Intelligent caching system
   - `auto-update.service.ts`: Background data updates

4. **Middleware** (`src/middleware.ts`):
   - Security headers application
   - Authentication checks for protected routes
   - Admin route protection
   - Public route allowlist

### Data Flow

1. **Trial System**:
   ```
   User starts watching → Trial timer begins (5 min) → 
   Timer expires → Show conversion overlay → 
   Generate token → Redirect to app store → 
   User opens app → Verify token → Grant 1 month free
   ```

2. **Authentication**:
   ```
   User login → Stack Auth/Supabase → Create/update profile → 
   Check subscription status → Grant access based on tier
   ```

3. **Sports Data**:
   ```
   Request → Cache Check → Request Manager → 
   API Provider (round-robin) → Transform → Cache → Response
   ```

## Development Patterns

### Component Structure
- Use Shadcn/ui components as base (`components/ui/`)
- Compose complex components in feature folders
- Keep components focused and use composition

### State Management
- Use Zustand stores for global state
- Persist critical state (trial, auth)
- Use Immer for immutable updates

### API Routes
- Place in `app/api/` directory
- Use Edge Runtime when possible
- Return consistent error responses

### Database Queries
- Always use RLS policies
- Create indexes for frequently queried fields
- Use database functions for complex operations

### Styling
- Tailwind CSS 4 with custom theme
- Use `cn()` utility for conditional classes
- Follow mobile-first responsive design

## Environment Variables

Required in `.env.local`:
```
# Supabase
NEXT_PUBLIC_SUPABASE_URL=
NEXT_PUBLIC_SUPABASE_ANON_KEY=
SUPABASE_SERVICE_ROLE_KEY=

# App URLs
NEXT_PUBLIC_APP_URL=
NEXT_PUBLIC_ANDROID_APP_URL=
NEXT_PUBLIC_IOS_APP_URL=

# Streaming
NEXT_PUBLIC_STREAM_URL=
NEXT_PUBLIC_FALLBACK_STREAM_URL=

# Auth
JWT_SECRET=
NEXT_PUBLIC_STACK_PROJECT_ID=
NEXT_PUBLIC_STACK_PUBLISHABLE_CLIENT_KEY=
STACK_SECRET_SERVER_KEY=

# Sports APIs
SPORTS_API_KEY=
ALLSPORTDB_API_KEY=

# Stripe
STRIPE_SECRET_KEY=
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=
STRIPE_WEBHOOK_SECRET=

# Debug
DEBUG=true
NEXT_PUBLIC_DEBUG=true
```

## Key Features

### Sports Data Integration
- **Multiple API Providers**: API-Sports, AllSportDB with automatic failover
- **Request Management**: Pooling, rate limiting, and round-robin distribution
- **Caching Strategy**: Multi-layer cache with TTL based on data type
- **EPG Support**: Electronic Program Guide for sports schedules
- **Live Updates**: Real-time score updates and match statistics

### Security Features
- **CSRF Protection**: Token-based protection for state-changing operations
- **Rate Limiting**: IP-based and user-based limits
- **Audit Logging**: Track security events and user actions
- **Input Validation**: Zod schemas for all user inputs
- **Middleware Protection**: Route-level authentication checks

### Payment Integration
- **Stripe Checkout**: Subscription management
- **Webhook Handling**: Secure payment event processing
- **Customer Portal**: Self-service subscription management
- **EUR Currency**: Configured for Spanish market

## Performance Considerations

1. Use dynamic imports for heavy components
2. Implement image optimization with Next.js Image
3. Cache API responses with SWR
4. Use React.memo for expensive components
5. Monitor bundle size

## Security Notes

- All user data access goes through RLS policies
- Tokens expire after 24 hours
- Service role key only in server-side code
- Validate all user inputs with Zod schemas
- Use HTTPS-only cookies for sensitive data
- CSP headers configured in next.config.ts

## Debug Mode

Enable detailed logging:
```bash
DEBUG=true NEXT_PUBLIC_DEBUG=true npm run dev
```

Access debug console at `/debug` for:
- Log viewing and filtering
- API endpoint testing
- Environment status checks
- Log export functionality

## Build Configuration

- **ESLint**: Errors ignored during build (`ignoreDuringBuilds: true`)
- **TypeScript**: Build errors ignored (`ignoreBuildErrors: true`)
- **Turbopack**: Enabled for development server

## Critical Rules - NEVER BREAK THESE

1. **NUNCA USAR DADOS MOCKADOS OU FAKE**
   - O site está em produção
   - Sempre usar apenas dados reais das APIs
   - Se não houver dados, não mostrar nada
   - NUNCA mentir para o cliente com dados falsos

2. **Preferências de Código**
   - Não adicionar comentários desnecessários
   - Seguir padrões existentes do projeto
   - Usar componentes Shadcn/ui quando possível

## Interaction Preferences

- sempre fale comigo em pt-br