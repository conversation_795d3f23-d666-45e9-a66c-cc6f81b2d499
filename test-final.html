<!DOCTYPE html>
<html>
<head>
  <title>Teste Final - StreamPlus</title>
  <script>
    console.log('🧪 Iniciando teste final...');
    
    // Simula o erro da Trust Wallet
    setTimeout(() => {
      console.log('⚡ Simulando erro da Trust Wallet...');
      try {
        const obj = null;
        const type = obj.type; // Isso deve causar o erro
      } catch (e) {
        console.error('❌ Erro capturado:', e.message);
      }
    }, 1000);
    
    // Verifica se a proteção está funcionando
    window.onerror = function(msg, src) {
      console.log('🛡️ window.onerror capturou:', { msg, src });
      return true;
    };
  </script>
</head>
<body>
  <h1>Teste Final StreamPlus</h1>
  <p>Abra o console para ver os logs</p>
  <a href="http://localhost:3000">Ir para o site</a>
</body>
</html>