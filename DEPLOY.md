# Deploy Instructions for StreamPlus España

## 🚀 Deploy no Railway

### 1. Preparação do Banco de Dados (Supabase)

Primeiro, você precisa criar as tabelas no Supabase:

```bash
# Opção 1: Usando o Supabase CLI (recomendado)
supabase db push

# Opção 2: Executar manualmente no SQL Editor do Supabase
# Copie o conteúdo de supabase/migrations/001_initial_schema.sql
# e execute no SQL Editor do seu projeto Supabase
```

### 2. Variáveis de Ambiente no Railway

Configure as seguintes variáveis de ambiente no Railway:

```env
# Supabase
NEXT_PUBLIC_SUPABASE_URL=https://seu-projeto.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=sua-anon-key
SUPABASE_SERVICE_ROLE_KEY=sua-service-role-key

# App URLs
NEXT_PUBLIC_APP_URL=https://seu-app.railway.app
NEXT_PUBLIC_ANDROID_APP_URL=https://play.google.com/store/apps/details?id=com.streamplus
NEXT_PUBLIC_IOS_APP_URL=https://apps.apple.com/app/streamplus/id123456789

# Streaming
NEXT_PUBLIC_STREAM_URL=https://seu-stream-principal.m3u8
NEXT_PUBLIC_FALLBACK_STREAM_URL=https://seu-stream-backup.m3u8

# Auth
JWT_SECRET=gere-um-secret-seguro-aqui
NEXT_PUBLIC_STACK_PROJECT_ID=seu-stack-project-id
NEXT_PUBLIC_STACK_PUBLISHABLE_CLIENT_KEY=sua-stack-key
STACK_SECRET_SERVER_KEY=sua-stack-secret-key

# Sports APIs
SPORTS_API_KEY=sua-api-sports-key
ALLSPORTDB_API_KEY=20d8be22-0948-4131-ae61-37dd981028bd
```

### 3. Deploy via Railway CLI

```bash
# Instalar Railway CLI
npm install -g @railway/cli

# Login no Railway
railway login

# Inicializar projeto
railway init

# Linkar com projeto existente (se aplicável)
railway link

# Deploy
railway up
```

### 4. Deploy via GitHub

1. Faça push do código para o GitHub
2. No Railway, crie um novo projeto
3. Conecte com seu repositório GitHub
4. Railway irá automaticamente detectar o projeto Next.js
5. Configure as variáveis de ambiente
6. Deploy será iniciado automaticamente

## 📋 Checklist de Deploy

- [ ] Banco de dados Supabase criado e configurado
- [ ] Tabelas criadas no Supabase
- [ ] Variáveis de ambiente configuradas no Railway
- [ ] Domínio customizado configurado (opcional)
- [ ] SSL certificado ativo
- [ ] APIs de esportes testadas e funcionando

## 🔧 Configurações Importantes

### Supabase RLS (Row Level Security)
As políticas RLS já estão configuradas na migration. Certifique-se de que estão ativas.

### Rate Limiting
O sistema possui rate limiting configurado para as APIs de esportes:
- API Sports: 100 requisições/dia por API
- AllSportsDB: 100,000 requisições/mês

### Cache
O sistema possui cache multi-camadas para otimizar performance:
- Live events: 30 segundos
- Upcoming events: 15 minutos
- Finished events: 1 hora

## 🚨 Troubleshooting

### Erro de build
```bash
# Limpar cache e reinstalar
rm -rf node_modules .next
pnpm install
pnpm build
```

### Erro de banco de dados
- Verifique se as tabelas foram criadas corretamente
- Confirme que as variáveis de ambiente estão corretas
- Teste a conexão usando o Supabase Dashboard

### APIs não funcionando
- Verifique os limites de rate das APIs
- Confirme que as API keys estão corretas
- Use o endpoint `/api/sports/test-all` para testar todas as APIs

## 📊 Monitoramento

### Endpoints de Status
- `/api/health` - Health check geral
- `/api/sports/api-status` - Status das APIs de esportes
- `/api/sports/rotation-status` - Status da rotação de APIs

### Logs
Railway fornece logs em tempo real. Acesse através do dashboard ou CLI:
```bash
railway logs
```

## 🔐 Segurança

- Nunca commite secrets no código
- Use sempre HTTPS
- Mantenha as dependências atualizadas
- Configure CORS apropriadamente
- Implemente rate limiting para APIs públicas