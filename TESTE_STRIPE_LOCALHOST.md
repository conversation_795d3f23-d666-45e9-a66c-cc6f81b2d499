# Guia de Teste do Sistema Stripe no Localhost

## 1. Verificações Iniciais

### A. Verificar se as tabelas foram criadas no Supabase

Execute estas queries no Supabase SQL Editor:

```sql
-- 1. Verificar tabelas criadas
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('stripe_subscriptions', 'stripe_webhook_events', 'payment_history')
ORDER BY table_name;

-- 2. Verificar campos Stripe nas tabelas de perfil
SELECT column_name, data_type, is_nullable
FROM information_schema.columns
WHERE table_schema = 'public' 
AND table_name IN ('profiles', 'stack_profiles')
AND (column_name LIKE '%stripe%' OR column_name LIKE '%subscription%')
ORDER BY table_name, column_name;

-- 3. Verificar funções criadas
SELECT routine_name, routine_type
FROM information_schema.routines
WHERE routine_schema = 'public'
AND routine_name IN ('sync_stripe_subscription', 'process_stripe_webhook');
```

### B. Se faltarem tabelas, execute as migrações:

```sql
-- Co<PERSON> e cole o conteúdo dos arquivos:
-- supabase/migrations/create_stripe_subscription_system.sql
-- supabase/migrations/create_stripe_sync_function.sql
-- supabase/migrations/add_stripe_fields_to_profiles.sql
```

## 2. Testar o Sistema no Localhost

### A. Iniciar o servidor de desenvolvimento
```bash
npm run dev
```

### B. Acessar páginas de teste:

1. **Debug Panel Visual**: http://localhost:3000/browse
   - Procure o painel "Stripe Debug" no canto inferior direito
   - Ele mostra o status da assinatura em tempo real

2. **API de Debug**: http://localhost:3000/api/debug/stripe-status
   - Mostra JSON detalhado com status da assinatura
   - Verifica todas as tabelas relevantes

3. **Página de Assinatura**: http://localhost:3000/subscription
   - Teste o fluxo de pagamento

## 3. Testar Webhooks Localmente

### A. Instalar Stripe CLI (se não tiver):
```bash
brew install stripe/stripe-cli/stripe
```

### B. Fazer login:
```bash
stripe login
```

### C. Em um terminal, encaminhar webhooks:
```bash
stripe listen --forward-to localhost:3000/api/stripe/webhook
```

### D. Em outro terminal, disparar evento de teste:
```bash
# Simular pagamento bem-sucedido
stripe trigger checkout.session.completed

# Simular renovação
stripe trigger invoice.payment_succeeded

# Simular cancelamento
stripe trigger customer.subscription.deleted
```

## 4. Monitorar Logs

Procure por logs com prefixo `[STRIPE-WEBHOOK]` no console do npm run dev.

Logs importantes:
- `[STRIPE-WEBHOOK] ===== WEBHOOK RECEIVED =====`
- `[STRIPE-WEBHOOK] ✅ Subscription synced successfully`
- `[STRIPE-WEBHOOK] ❌ Error`

## 5. Verificar no Banco de Dados

Após testar webhook, verifique:

```sql
-- Ver eventos de webhook registrados
SELECT * FROM stripe_webhook_events 
ORDER BY created_at DESC 
LIMIT 5;

-- Ver assinaturas Stripe
SELECT * FROM stripe_subscriptions 
WHERE user_id = 'SEU_USER_ID';

-- Ver status do perfil
SELECT 
  id,
  email,
  subscription_tier,
  subscription_status,
  subscription_current_period_end,
  stripe_customer_id,
  stripe_subscription_id
FROM profiles
WHERE email = 'SEU_EMAIL';
```

## 6. Testar Pagamento Real (Opcional)

Use cartão de teste do Stripe:
- Número: 4242 4242 4242 4242
- Validade: Qualquer data futura
- CVC: Qualquer 3 dígitos
- CEP: Qualquer

## 7. Se Algo Não Funcionar

### A. Forçar acesso premium manualmente:
```sql
-- Substitua o email
UPDATE profiles
SET 
  subscription_tier = 'premium',
  subscription_status = 'active',
  subscription_current_period_end = NOW() + INTERVAL '30 days'
WHERE email = '<EMAIL>';
```

### B. Verificar logs de erro:
```sql
SELECT * FROM stripe_webhook_events 
WHERE status = 'error' 
ORDER BY created_at DESC;
```

## Status Esperado Após Pagamento

1. Debug panel deve mostrar:
   - ✅ Assinatura Stripe
   - ✅ Assinatura Ativa
   - ✅ Profile Premium

2. Na página /browse:
   - Badge "PREMIUM" deve aparecer ao lado do nome
   - Não deve mostrar "Trial 5:00"

3. Ao assistir um canal:
   - Não deve aparecer overlay de trial
   - Acesso completo sem limitações