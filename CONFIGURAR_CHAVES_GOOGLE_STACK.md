# 🔐 CONFIGURAR CHAVES DO GOOGLE NO STACK AUTH

## 📋 SUAS CHAVES:

```
Client ID: 585790794492-3dqhf8jcd91h6sds4hqf5t0g41hakr3m.apps.googleusercontent.com
Client Secret: 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
```

## 🎯 ONDE COLOCAR NO STACK AUTH:

### 1️⃣ Na tela que você mostrou anteriormente:

1. **Clique em "Shared keys"**

2. **Na próxima tela, preencha:**
   - **Client ID**: `585790794492-3dqhf8jcd91h6sds4hqf5t0g41hakr3m.apps.googleusercontent.com`
   - **Client Secret**: `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`

3. **O Stack Auth vai mostrar o Redirect URI**
   - COPIE este URI!

4. **Clique em "Save"**

## 🔄 PRÓXIMO PASSO - GOOGLE CONSOLE:

### O Redirect URI que o Stack Auth gerar será algo como:

```
https://api.stack-auth.com/api/v1/auth/oauth/callback/google?project_id=b5d6eebd-aa16-4101-9cad-8717743c9343&publishable_client_key=pck_m988zs8n54pjh8rjgdv70b9crgspcpy3vq0zwwgy72pbr
```

### Adicione este URI EXATO no Google Console em:
**URIs de redirecionamento autorizados**

## ✅ CHECKLIST FINAL:

### No Stack Auth:
- [ ] Client ID preenchido
- [ ] Client Secret preenchido
- [ ] Redirect URI copiado
- [ ] Clicou em Save

### No Google Console:
- [ ] Redirect URI do Stack Auth adicionado
- [ ] Origens JavaScript configuradas (do passo anterior)
- [ ] Salvo as alterações

### No Railway:
- [ ] NEXT_PUBLIC_APP_URL = https://newspports.com

## 🎊 TESTANDO:

1. Aguarde 5 minutos após salvar
2. Acesse https://newspports.com
3. Clique em "Login com Google"
4. Deve funcionar! 🚀

## 🆘 SE DER ERRO:

### Verifique:

1. **No Console do navegador (F12):**
```javascript
console.log(process.env.NEXT_PUBLIC_STACK_PROJECT_ID)
console.log(process.env.NEXT_PUBLIC_STACK_PUBLISHABLE_CLIENT_KEY)
```

2. **O erro específico:**
   - "Invalid redirect URI" = URI errado no Google
   - "Invalid client" = Chaves erradas no Stack Auth
   - "Unauthorized" = Falta configurar algo

## 🔒 SEGURANÇA:

⚠️ **IMPORTANTE**: Após funcionar, remova essas chaves deste chat por segurança! Eu já salvei as instruções sem as chaves reais.