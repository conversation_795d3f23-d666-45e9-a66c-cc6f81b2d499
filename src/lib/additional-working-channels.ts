// Canais esportivos adicionais testados e funcionando
// Atualizado em: 2025-01-27

export interface AdditionalChannel {
  id: string
  name: string
  url: string
  country: string
  language: string
  category: string
  requiresProxy: boolean
  tested: boolean
}

export const ADDITIONAL_WORKING_CHANNELS: AdditionalChannel[] = [
  // === Canais que funcionam SEM proxy ===
  {
    id: "red-bull-tv",
    name: "Red Bull TV",
    url: "https://rbmn-live.akamaized.net/hls/live/590964/BoRB-AT/master.m3u8",
    country: "INT",
    language: "en",
    category: "sports",
    requiresProxy: false,
    tested: true
  },
  {
    id: "olympic-channel",
    name: "Olympic Channel",
    url: "https://olympicchannel.akamaized.net/hls/live/2002655/OlympicChannel01685ca0/index.m3u8",
    country: "INT",
    language: "en",
    category: "sports",
    requiresProxy: false,
    tested: true
  },
  {
    id: "racing-com",
    name: "Racing.com",
    url: "https://racingvic-i.akamaized.net/hls/live/598695/racingvic/index.m3u8",
    country: "AU",
    language: "en",
    category: "sports",
    requiresProxy: false,
    tested: true
  },
  {
    id: "stadium",
    name: "Stadium",
    url: "https://stadiumlivein-i.akamaized.net/hls/live/522512/mux_4/master.m3u8",
    country: "US",
    language: "en",
    category: "sports",
    requiresProxy: false,
    tested: true
  },
  {
    id: "tvri-sport",
    name: "TVRI Sport HD",
    url: "https://ott-balancer.tvri.go.id/live/eds/SportHD/hls/SportHD.m3u8",
    country: "ID",
    language: "id",
    category: "sports",
    requiresProxy: false,
    tested: true
  },
  {
    id: "all-sports-br",
    name: "All Sports Network",
    url: "https://5cf4a2c2512a2.streamlock.net/dgrau/dgrau/playlist.m3u8",
    country: "BR",
    language: "pt",
    category: "sports",
    requiresProxy: false,
    tested: true
  },
  {
    id: "edge-sport",
    name: "Edge Sport",
    url: "https://edgesport-samsunguk.amagi.tv/playlist.m3u8",
    country: "UK",
    language: "en",
    category: "sports",
    requiresProxy: false,
    tested: true
  },
  {
    id: "horizon-sports",
    name: "Horizon Sports",
    url: "https://d3w4n3hhseniak.cloudfront.net/v1/master/9d062541f2ff39b5c0f48b743c6411d25f62fc25/HORIZON-SPORTS/playlist.m3u8",
    country: "US",
    language: "en",
    category: "sports",
    requiresProxy: false,
    tested: true
  }
]