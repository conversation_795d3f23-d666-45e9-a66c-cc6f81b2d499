// Channel status definitions - which channels are working 100%
// Based on our testing results

export interface ChannelStatus {
  id: string
  status: 'working' | 'partial' | 'blocked'
  quality: 'HD' | 'SD' | '4K'
  category: 'official' | 'premium' | 'sports' | 'news' | 'entertainment'
  requiresProxy: boolean
  geoBlocked?: string[] // List of regions where it's blocked
}

// Channels that are working 100% without issues
export const workingChannels: Set<string> = new Set([
  // Official Spanish channels
  'real-madrid-tv',
  'sevilla-fc-tv', 
  'teledeporte',
  
  // IPTV channels working well
  '30AGolfKingdom.us',
  'Adjarasport1.ge',
  'ADOTV.bj',
  'Africa24Sport.fr',
  'AllSports.br',
  'ArenaSport1.ba',
  'ArenaSport2.ba',
  'ArenaSport3.ba',
  'ArenaSport4.hr',
  'ArryaadySport.so',
  'ATGSverige.se',
  'BahrainSports1.bh',
  'BahrainSports2.bh',
  'beINMoviesAction.qa',
  'BeINSportsAsia1.qa',
  'BeINSportsAsia2.qa',
  'BeINSportsAsia3.qa',
  'BeINSportsEnglish1.qa',
  'BeINSportsEnglish2.qa',
  'BeINSportsEnglish3.qa',
  'BeINSportsExtraenEspanol1.us',
  'BeINSportsExtraenEspanol2.us',
  'BeINSportsFrance1.fr',
  'BeINSportsFrance2.fr',
  'BeINSportsFrance3.fr',
  'BeINSportsGlobal.fr',
  'BeINSportsMAX1.fr',
  'BeINSportsMAX10.fr',
  'ESPNEast.us',
  'ESPNews.us',
  'ESPNU.us',
  'FoxSports1.us',
  'FoxSports2.us'
])

// Premium channels that require proxy/special handling
export const premiumChannelsStatus: ChannelStatus[] = [
  {
    id: 'laliga-ea-sports-1',
    status: 'blocked',
    quality: 'HD',
    category: 'premium',
    requiresProxy: true,
    geoBlocked: ['US', 'CA', 'UK']
  },
  {
    id: 'laliga-hypermotion',
    status: 'blocked',
    quality: 'HD',
    category: 'premium',
    requiresProxy: true,
    geoBlocked: ['US', 'CA', 'UK']
  },
  {
    id: 'premier-league-1',
    status: 'blocked',
    quality: 'HD',
    category: 'premium',
    requiresProxy: true,
    geoBlocked: ['ES', 'FR', 'DE']
  },
  {
    id: 'movistar-liga-de-campeones',
    status: 'blocked',
    quality: 'HD',
    category: 'premium',
    requiresProxy: true,
    geoBlocked: ['US', 'CA', 'UK']
  }
]

// Channels that work without proxy
export const officialChannelsStatus: ChannelStatus[] = [
  {
    id: 'real-madrid-tv',
    status: 'working',
    quality: 'HD',
    category: 'official',
    requiresProxy: false
  },
  {
    id: 'sevilla-fc-tv',
    status: 'working',
    quality: 'HD',
    category: 'official',
    requiresProxy: false
  },
  {
    id: 'teledeporte',
    status: 'working',
    quality: 'HD',
    category: 'official',
    requiresProxy: false
  }
]

// Function to check if a channel is working
export function isChannelWorking(channelId: string): boolean {
  return workingChannels.has(channelId) || 
         officialChannelsStatus.some(ch => ch.id === channelId && ch.status === 'working')
}

// Function to get channel status
export function getChannelStatus(channelId: string): ChannelStatus | null {
  // Check official channels first
  const official = officialChannelsStatus.find(ch => ch.id === channelId)
  if (official) return official
  
  // Check premium channels
  const premium = premiumChannelsStatus.find(ch => ch.id === channelId)
  if (premium) return premium
  
  // Check if it's in working set
  if (workingChannels.has(channelId)) {
    return {
      id: channelId,
      status: 'working',
      quality: 'HD',
      category: 'sports',
      requiresProxy: false
    }
  }
  
  return null
}

// Function to get channel category
export function getChannelCategory(channelId: string): string {
  const status = getChannelStatus(channelId)
  if (status) return status.category
  
  // Default categorization based on ID
  if (channelId.includes('ESPN') || channelId.includes('Fox') || channelId.includes('Sport')) {
    return 'sports'
  }
  if (channelId.includes('News')) {
    return 'news'
  }
  
  return 'entertainment'
}