// Lista dos 69 canais de esportes selecionados para manter
// Data: 2025-07-26

export const SELECTED_SPORTS_CHANNELS = new Set<string>([
  '30AGolfKingdom.us',
  'ACCDigitalNetwork.us',
  'Adjarasport1.ge',
  'ADOTV.bj',
  'Africa24Sport.fr',
  'AfroSportNigeria.ng',
  'AntenaSport.hr',
  'AntenaSport.ro',
  'Arryadia.ma',
  'AstrahanRuSport.ru',
  'BahrainSports1.bh',
  'BahrainSports2.bh',
  'Belarus5.by',
  'BellatorMMA.us',
  'BilliardTV.us',
  'CanaldoInter.br',
  'CBSSportsNetworkUSA.us',
  'CTSport.cz',
  'DongNaiTV2.vn',
  'DubaiRacing.ae',
  'DubaiRacing2.ae',
  'DubaiRacing3.ae',
  'DubaiSports1.ae',
  'DubaiSports2.ae',
  'DubaiSports3.ae',
  'ElevenSports1.pl',
  'ESPNews.us',
  'ESPNU.us',
  'FanDuelSportsNetwork.us',
  'FanDuelTV.us',
  'FIFAPlus.pl',
  'FITE247.us',
  'GloryKickboxing.us',
  'InsightTV.nl',
  'InterTV.it',
  'IRIB3.ir',
  'ITVDeportes.mx',
  'K19.at',
  'KHLPrime.ru',
  'KSASports1.sa',
  'LacrosseTV.us',
  'MadeinBOTV.it',
  'MatchArena.ru',
  'MAVTVSelect.us',
  'MLBNetwork.us',
  'MoreThanSportsTV.de',
  'MSG.us',
  'NFLNetwork.us',
  'NFLRedZone.us',
  'NHLNetwork.us',
  'NitroCircus.us',
  'ONFootball.vn',
  'RacingAmerica.us',
  'RedBullTV.at',
  'SOSKanalPlus.rs',
  'SportsGrid.us',
  'SportsmanChannel.us',
  'SportsNetNewYork.us',
  'StarSports1Tamil.in',
  'SwerveSports.us',
  'talkSPORT.uk',
  'TDMSports.mo',
  'TRSport.it',
  'TraceSportStars.fr',
  'TSNTheOcho.ca',
  'TSN1.ca',
  'TSN2.ca',
  'TSN3.ca',
  'TSN5.ca'
])

// Função para verificar se um canal está na lista selecionada
export function isSelectedSportsChannel(channelId: string): boolean {
  return SELECTED_SPORTS_CHANNELS.has(channelId)
}

// Função para filtrar apenas os canais selecionados
export function filterSelectedSportsChannels<T extends { id: string }>(channels: T[]): T[] {
  return channels.filter(channel => isSelectedSportsChannel(channel.id))
}

// Exportar o tamanho da lista para validação
export const SELECTED_CHANNELS_COUNT = SELECTED_SPORTS_CHANNELS.size