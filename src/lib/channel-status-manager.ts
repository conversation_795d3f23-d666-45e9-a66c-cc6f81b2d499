// Gerenciador de status dos canais baseado nos testes manuais

export interface ChannelStatus {
  id: string
  name: string
  status: 'working' | 'loading_error' | 'infinite_loading' | 'error_404' | 'black_screen'
  notes?: string
}

// Canais funcionando corretamente (24 confirmados)
export const WORKING_CHANNELS = [
  '30A Golf Kingdom',
  'ACC Digital Network',
  'Adjarasport1',
  'ADO TV',
  'Africa 24 Sport',
  'Afro Sport Nigeria',
  'Antena Sport',
  'Arryadia',
  'Astrahan.Ru Sport',
  'Bahrain Sports 1',
  'Bahrain Sports 2',
  'Belarus-5',
  'Bellator MMA',
  'Billiard TV',
  'Canal do Inter',
  'CBS Sports Network USA',
  'CT Sport',
  'Dong Nai TV2',
  'Dubai Racing',
  'Dubai Racing 2',
  'Dubai Racing 3',
  'Dubai Sports 1',
  'Dubai Sports 2',
  'Dubai Sports 3',
  'Eleven Sports 1'
]

// Canais com erro ao carregar conteúdo (42 canais)
export const LOADING_ERROR_CHANNELS = [
  'ACI Sport TV',
  'ACL Cornhole TV',
  '3 South Australia NPL',
  'Afizzionados',
  'Alfa Sport',
  'Alkass Five',
  'Alkass Four',
  'Alkass One',
  'Alkass Shoof',
  'Alkass Six',
  'Alkass Three',
  'Alkass Two',
  'Victoria NPL',
  'Arena Sport 1',
  'ATG',
  'Awapa Sports TV',
  'BA CoE Canberra',
  'beiN Sports Haber',
  'beiN Sports XTRA',
  'beiN Sports XTRA em Spanol',
  'Blue Sport 2',
  'CampusLore',
  'CBS Sports HQ',
  'CCTV-5',
  'Champion TV',
  'CRTV Sports and Entertainment',
  'DAZN Combate',
  'Dubai Games TV',
  'Capital Territory NPL 2',
  'CBC Sports',
  'EDGEsports',
  'Eleven Sports 2',
  'Eleven Sports 3',
  'ERT Sports 1',
  'Chatham Cup ASB',
  'Vitorino NPL',
  'Vitorino NPL 2',
  'Capital Territory NPL',
  'Queensland Premier League',
  'South Australia State League 1',
  'Tasmania NPL',
  'New South Wales NPL',
  'National League - Northern'
]

// Canais que carregam infinitamente (11 canais)
export const INFINITE_LOADING_CHANNELS = [
  'A Sport',
  'Abu Dhabi Sports 1',
  'Abu Dhabi Sports 2',
  'Canal + Sport 2',
  'CBS Sport Golazo Network',
  'CCTV-Billiards',
  'CCTV-STORM Football',
  'Combate',
  'CBS Sports Golazo Network',
  'eGG Network',
  'Equidia'
]

// Canais com erro 404 (5 canais)
export const ERROR_404_CHANNELS = [
  'ACC Network',
  'Action Sports',
  'Adventure Sports TV',
  'Barca TV',
  'BEK Sports'
]

// Canal com tela preta sem som (problema de codec/formato)
export const BLACK_SCREEN_CHANNELS = [
  'CDN Deportes'
]

export function isChannelWorking(channelName: string): boolean {
  return WORKING_CHANNELS.some(name => 
    channelName.toLowerCase().includes(name.toLowerCase()) ||
    name.toLowerCase().includes(channelName.toLowerCase())
  )
}

export function shouldFilterChannel(channelName: string): boolean {
  const allProblematicChannels = [
    ...LOADING_ERROR_CHANNELS,
    ...ERROR_404_CHANNELS,
    ...BLACK_SCREEN_CHANNELS
    // Não incluir INFINITE_LOADING_CHANNELS pois vamos tentar com proxy
  ]
  
  return allProblematicChannels.some(name => 
    channelName.toLowerCase().includes(name.toLowerCase()) ||
    name.toLowerCase().includes(channelName.toLowerCase())
  )
}

export function shouldUseProxy(channelName: string): boolean {
  // Canais que carregam infinitamente devem usar proxy
  return INFINITE_LOADING_CHANNELS.some(name => 
    channelName.toLowerCase().includes(name.toLowerCase()) ||
    name.toLowerCase().includes(channelName.toLowerCase())
  )
}

// Função para determinar possíveis soluções baseado no tipo de erro
export function getErrorSolution(errorType: string): string {
  switch (errorType) {
    case 'loading_error':
      return 'Servidor offline ou URL expirada. Necessário nova fonte.'
    case 'infinite_loading':
      return 'Possível problema de CORS ou formato incompatível. Testar com proxy.'
    case 'error_404':
      return 'URL incorreta ou canal removido. Buscar fonte alternativa.'
    case 'black_screen':
      return 'Codec não suportado ou problema de decodificação. Verificar formato.'
    default:
      return 'Problema desconhecido.'
  }
}