// Configuração do proxy para streams
export function getProxyUrl(originalUrl: string, options?: {
  channelId?: string
  channelName?: string
  headers?: Record<string, string>
  useSimpleProxy?: boolean
}): string {
  // Para canais ESPN da moveonjoy, usar proxy simples que não modifica fragmentos
  if (originalUrl.includes('moveonjoy.com')) {
    // Usar o proxy original que não modifica os fragmentos
    let proxyUrl = `/api/proxy/stream?url=${encodeURIComponent(originalUrl)}`
    
    if (options?.headers?.Referer) {
      proxyUrl += `&referer=${encodeURIComponent(options.headers.Referer)}`
    }
    
    if (options?.headers?.['User-Agent']) {
      proxyUrl += `&userAgent=${encodeURIComponent(options.headers['User-Agent'])}`
    }
    
    if (options?.channelId) {
      proxyUrl += `&channelId=${encodeURIComponent(options.channelId)}`
    }
    
    if (options?.channelName) {
      proxyUrl += `&channel=${encodeURIComponent(options.channelName)}`
    }
    
    return proxyUrl
  }
  
  // Para outros streams, usar o novo proxy de streaming
  return `/api/stream-proxy?url=${encodeURIComponent(originalUrl)}`
}

// Headers específicos por domínio
export function getStreamHeaders(url: string): Record<string, string> {
  const headers: Record<string, string> = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
  }
  
  if (url.includes('moveonjoy.com')) {
    headers['Referer'] = 'https://moveonjoy.com/'
    headers['Origin'] = 'https://moveonjoy.com'
  } else if (url.includes('akamaized.net')) {
    headers['Referer'] = 'https://www.rtve.es/'
  }
  
  return headers
}