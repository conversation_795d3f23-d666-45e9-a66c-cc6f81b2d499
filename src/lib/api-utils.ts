import { headers } from 'next/headers'

/**
 * Constrói a URL base da API de forma segura
 * Funciona tanto em desenvolvimento quanto em produção
 */
export async function getApiBaseUrl(): Promise<string> {
  // Em componentes server, temos acesso aos headers
  const headersList = await headers()
  const host = headersList.get('host')
  const protocol = headersList.get('x-forwarded-proto') || 'http'
  
  if (host) {
    return `${protocol}://${host}`
  }
  
  // Fallback para desenvolvimento
  return 'http://localhost:3000'
}

/**
 * Constrói uma URL de API completa
 */
export async function buildApiUrl(path: string): Promise<string> {
  const baseUrl = await getApiBaseUrl()
  // Garantir que path começa com /
  const normalizedPath = path.startsWith('/') ? path : `/${path}`
  return `${baseUrl}${normalizedPath}`
}