// Canais da lista selecionada que precisam ser adicionados ao real-working-channels.ts
// Estes canais estão na API IPTV e funcionando mas não foram incluídos no arquivo principal

export const MISSING_CHANNELS = [
  {
    id: "AntenaSport.ro",
    name: "AntenaSport",
    url: "https://stream1.antenaplay.ro/as/asrolive1/playlist.m3u8",
    category: "sports",
    country: "RO",
    language: "ro",
    requiresProxy: false,
    lastTested: "2025-07-27",
    successRate: 100
  },
  {
    id: "ADOTV.bj",
    name: "ADO TV",
    url: "https://strhls.streamakaci.tv/ortb/ortb2-multi/playlist.m3u8",
    category: "sports",
    country: "BJ",
    language: "fr",
    requiresProxy: false,
    lastTested: "2025-07-27",
    successRate: 100
  },
  {
    id: "AfroSportNigeria.ng",
    name: "AfroSport Nigeria",
    url: "https://newproxy3.vidivu.tv/vidivu_afrosport/index.m3u8",
    category: "sports",
    country: "NG",
    language: "en",
    requiresProxy: false,
    lastTested: "2025-07-27",
    successRate: 100
  },
  {
    id: "Arryadia.ma",
    name: "Arryadia",
    url: "https://cdn.live.easybroadcast.io/abr_corp/73_arryadia_k2tgcj0/corp/73_arryadia_k2tgcj0_480p/chunks_dvr.m3u8",
    category: "sports",
    country: "MA",
    language: "ar",
    requiresProxy: false,
    lastTested: "2025-07-27",
    successRate: 100
  },
  {
    id: "Belarus5.by",
    name: "Belarus-5",
    url: "https://ngtrk.dc.beltelecom.by/ngtrk/smil:belarus5.smil/playlist.m3u8",
    category: "sports",
    country: "BY",
    language: "be",
    requiresProxy: false,
    lastTested: "2025-07-27",
    successRate: 100
  },
  {
    id: "BellatorMMA.us",
    name: "Bellator MMA",
    url: "http://cfd-v4-service-channel-stitcher-use1-1.prd.pluto.tv/stitch/hls/channel/5ebc8688f3697d00072f7cf8/master.m3u8?appName=web&appVersion=unknown&clientTime=0&deviceDNT=0&deviceId=6c26f5a6-30d3-11ef-9cf5-e9ddff8ff496&deviceMake=Chrome&deviceModel=web&deviceType=web&deviceVersion=unknown&includeExtendedEvents=false&serverSideAds=false&sid=0e97a69e-3355-4217-b6fb-8952f0ad1803",
    category: "sports",
    country: "US",
    language: "en",
    requiresProxy: false,
    lastTested: "2025-07-27",
    successRate: 100
  },
  {
    id: "BilliardTV.us",
    name: "Billiard TV",
    url: "https://1621590671.rsc.cdn77.org/HLS/BILLIARDTV.m3u8",
    category: "sports",
    country: "US",
    language: "en",
    requiresProxy: false,
    lastTested: "2025-07-27",
    successRate: 100
  },
  {
    id: "CanaldoInter.br",
    name: "Canal do Inter",
    url: "https://video01.soultv.com.br/internacional/internacional/playlist.m3u8",
    category: "sports",
    country: "BR",
    language: "pt",
    requiresProxy: false,
    lastTested: "2025-07-27",
    successRate: 100
  },
  {
    id: "CTSport.cz",
    name: "CT Sport",
    url: "http://88.212.15.19/live/test_ctsport_25p/playlist.m3u8",
    category: "sports",
    country: "CZ",
    language: "cs",
    requiresProxy: false,
    lastTested: "2025-07-27",
    successRate: 100
  },
  {
    id: "DongNaiTV2.vn",
    name: "Dong Nai TV 2",
    url: "http://118.107.85.4:1935/live/smil:DNTV2.smil/playlist.m3u8",
    category: "sports",
    country: "VN",
    language: "vi",
    requiresProxy: false,
    lastTested: "2025-07-27",
    successRate: 100
  },
  // E muitos outros que precisam ser adicionados...
]