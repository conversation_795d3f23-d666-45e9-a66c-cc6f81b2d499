import { createClient } from '@supabase/supabase-js'

// Cliente Supabase APENAS para banco de dados - sem nenhuma funcionalidade de auth
export function createDatabaseClient() {
  if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
    throw new Error('Variáveis do Supabase não configuradas')
  }

  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
    {
      auth: {
        persistSession: false,
        autoRefreshToken: false,
        detectSessionInUrl: false,
        storage: {
          getItem: () => null,
          setItem: () => {},
          removeItem: () => {}
        }
      },
      global: {
        headers: {
          'X-Client-Info': 'streamplus-database-only'
        }
      }
    }
  )
}

// Versão para servidor
export async function createServerDatabaseClient() {
  return createDatabaseClient()
}