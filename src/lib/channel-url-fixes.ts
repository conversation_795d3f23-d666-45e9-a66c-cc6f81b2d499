// URLs alternativas para canais com erro 404
// Atualizado em: 2025-01-27

import { getAlternativeUrls } from './working-alternative-urls'

export const CHANNEL_URL_FIXES: Record<string, string> = {
  // Canais com erro 404 que precisam de novas URLs
  "ElevenSports1.pl": "https://elevensports.com/view/live-stream", // Necessita atualização
  "FIFAPlus.pl": "https://www.fifa.com/fifaplus/en/live", // Stream oficial FIFA
  "FITE247.us": "https://www.fite.tv/", // Necessita autenticação
  "GloryKickboxing.us": "https://www.glorykickboxing.com/", // Necessita atualização
  "InterTV.it": "https://www.inter.it/en/inter-tv", // Canal oficial Inter Milan
  "MAVTVSelect.us": "https://www.mavtv.com/", // Necessita atualização
  "RedBullTV.at": "https://www.redbull.com/int-en/tv", // Canal oficial Red Bull
  "talkSPORT.uk": "https://talksport.com/radioplayer/live/talksport.html", // Rádio ao vivo
  "TSN1.ca": "https://www.tsn.ca/live", // Necessita geo-block bypass
  "TSNTheOcho.ca": "https://www.tsn.ca/live", // Necessita geo-block bypass
  
  // Canal com erro 400 (Bad Request)
  "IRIB3.ir": "https://telewebion.com/live/tv3", // Site oficial IRIB
}

// URLs de fallback genéricas para teste
export const GENERIC_TEST_STREAMS = [
  "https://test-streams.mux.dev/x36xhzz/x36xhzz.m3u8", // Big Buck Bunny
  "https://demo.unified-streaming.com/k8s/features/stable/video/tears-of-steel/tears-of-steel.ism/.m3u8", // Tears of Steel
]

// Função para obter URL corrigida
export function getFixedChannelUrl(channelId: string, originalUrl: string): string {
  // Se temos uma correção específica, usar ela
  if (CHANNEL_URL_FIXES[channelId]) {
    console.log(`[CHANNEL-FIX] Aplicando correção de URL para ${channelId}`);
    return CHANNEL_URL_FIXES[channelId];
  }
  
  // Caso contrário, retornar a URL original
  return originalUrl;
}

// Função para verificar se canal precisa de proxy baseado no erro
export function shouldUseProxy(channelId: string, statusCode?: number): boolean {
  // Canais que sempre precisam de proxy
  const alwaysNeedProxy = [
    "TSN1.ca",
    "TSN2.ca", 
    "TSN3.ca",
    "TSN5.ca",
    "TSNTheOcho.ca",
    "IRIB3.ir"
  ];
  
  if (alwaysNeedProxy.includes(channelId)) {
    return true;
  }
  
  // Se recebeu 403 ou 451, provavelmente é geo-block
  if (statusCode === 403 || statusCode === 451) {
    return true;
  }
  
  return false;
}

// Mapeamento de canais para URLs alternativas conhecidas
export const ALTERNATIVE_CHANNEL_URLS: Record<string, string[]> = {
  "RedBullTV.at": [
    "https://rbmn-live.akamaized.net/hls/live/590964/BoRB-AT/master.m3u8",
    "https://rbmn-live.akamaized.net/hls/live/590964/BoRB-US/master.m3u8"
  ],
  "TSN1.ca": [
    "https://live.tsn.ca/hls/tsn1-hd.m3u8",
    "https://live.tsn.ca/hls/tsn1-4k.m3u8"
  ],
  "TSN2.ca": [
    "https://live.tsn.ca/hls/tsn2-hd.m3u8",
    "https://live.tsn.ca/hls/tsn2-4k.m3u8"
  ],
  "TSN3.ca": [
    "https://live.tsn.ca/hls/tsn3-hd.m3u8",
    "https://live.tsn.ca/hls/tsn3-4k.m3u8"
  ],
  "TSN5.ca": [
    "https://live.tsn.ca/hls/tsn5-hd.m3u8", 
    "https://live.tsn.ca/hls/tsn5-4k.m3u8"
  ]
}

// Função para obter todas as URLs possíveis para um canal
export function getAllPossibleUrls(channelId: string, originalUrl: string): string[] {
  const urls: string[] = [];
  
  // Primeiro adicionar URLs alternativas funcionais do working-alternative-urls
  const workingAlternatives = getAlternativeUrls(channelId);
  if (workingAlternatives.length > 0) {
    urls.push(...workingAlternatives);
  }
  
  // Depois adicionar a URL original
  if (!urls.includes(originalUrl)) {
    urls.push(originalUrl);
  }
  
  // Adicionar URL corrigida se existir
  const fixedUrl = CHANNEL_URL_FIXES[channelId];
  if (fixedUrl && !urls.includes(fixedUrl)) {
    urls.push(fixedUrl);
  }
  
  // Adicionar URLs alternativas antigas se existirem
  const alternatives = ALTERNATIVE_CHANNEL_URLS[channelId];
  if (alternatives) {
    alternatives.forEach(url => {
      if (!urls.includes(url)) {
        urls.push(url);
      }
    });
  }
  
  return urls;
}