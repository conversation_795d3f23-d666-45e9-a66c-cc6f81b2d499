// Lista curada dos principais canais funcionais
// Apenas canais testados e confirmados

export interface MainChannel {
  id: string
  name: string
  category: 'sports' | 'news' | 'entertainment'
  url: string
  fallbackUrls?: string[]
  logo?: string
  country: string
  language: string
  requiresProxy: boolean
  priority: number // 1-10, onde 10 é máxima prioridade
  successRate: number // Porcentagem de sucesso nos testes
}

export const MAIN_CHANNELS: MainChannel[] = [
  // === CANAIS PREMIUM LALIGA/PREMIER (Prioridade máxima) ===
  {
    id: "premium-real-madrid-tv",
    name: "Real Madrid TV",
    category: "sports",
    url: "https://rmtv.akamaized.net/hls/live/2043153/rmtv-es-web/master.m3u8",
    logo: "https://upload.wikimedia.org/wikipedia/en/thumb/5/56/Real_Madrid_CF.svg/1200px-Real_Madrid_CF.svg.png",
    country: "ES",
    language: "es",
    requiresProxy: false,
    priority: 10,
    successRate: 100
  },
  {
    id: "premium-sevilla-fc-tv",
    name: "Sevilla FC TV",
    category: "sports",
    url: "https://open.http.mp.streamamg.com/p/3001314/sp/300131400/playManifest/entryId/0_ye0b8tc0/format/applehttp/protocol/https/uiConfId/30026292/a.m3u8",
    logo: "https://upload.wikimedia.org/wikipedia/en/thumb/3/3b/Sevilla_FC_logo.svg/200px-Sevilla_FC_logo.svg.png",
    country: "ES",
    language: "es",
    requiresProxy: false,
    priority: 10,
    successRate: 100
  },
  
  // === ESPN - Principais canais esportivos ===
  {
    id: "ESPNews.us",
    name: "ESPN News",
    category: "sports",
    url: "https://fl3.moveonjoy.com/ESPN_NEWS/index.m3u8",
    fallbackUrls: [
      "http://fl2.moveonjoy.com/ESPN_NEWS/index.m3u8",
      "http://fl1.moveonjoy.com/ESPN_NEWS/index.m3u8"
    ],
    logo: "https://upload.wikimedia.org/wikipedia/commons/thumb/1/1b/ESPNews.svg/200px-ESPNews.svg.png",
    country: "US",
    language: "en",
    requiresProxy: true,
    priority: 9,
    successRate: 95
  },
  {
    id: "ESPNU.us",
    name: "ESPN U",
    category: "sports",
    url: "https://fl3.moveonjoy.com/ESPN_U/index.m3u8",
    fallbackUrls: [
      "http://fl2.moveonjoy.com/ESPN_U/index.m3u8",
      "http://fl1.moveonjoy.com/ESPN_U/index.m3u8"
    ],
    logo: "https://upload.wikimedia.org/wikipedia/commons/thumb/c/ca/ESPN_U_logo.svg/200px-ESPN_U_logo.svg.png",
    country: "US",
    language: "en",
    requiresProxy: true,
    priority: 9,
    successRate: 95
  },
  
  // === Canais esportivos internacionais funcionando ===
  {
    id: "BahrainSports1.bh",
    name: "Bahrain Sports 1",
    category: "sports",
    url: "https://5c7b683162943.streamlock.net/live/ngrp:sportsone_all/playlist.m3u8",
    logo: "https://i.imgur.com/fL1N0rr.png",
    country: "BH",
    language: "ar",
    requiresProxy: false,
    priority: 7,
    successRate: 85
  },
  
  // === CBS Sports ===
  {
    id: "CBSSportsNetwork.us",
    name: "CBS Sports Network",
    category: "sports",
    url: "https://fl2.moveonjoy.com/CBS_SPORTS_NETWORK/index.m3u8",
    logo: "https://upload.wikimedia.org/wikipedia/commons/thumb/b/b3/CBS_Sports_Network_logo.png/200px-CBS_Sports_Network_logo.png",
    country: "US",
    language: "en",
    requiresProxy: true,
    priority: 7,
    successRate: 80
  },
  
  // === Canais de esportes específicos ===
  {
    id: "30AGolfKingdom.us",
    name: "30A Golf Kingdom",
    category: "sports",
    url: "https://30a-tv.com/feeds/vidaa/golf.m3u8",
    logo: "https://upload.wikimedia.org/wikipedia/commons/thumb/d/d0/Golf_Channel_logo.svg/200px-Golf_Channel_logo.svg.png",
    country: "US",
    language: "en",
    requiresProxy: false,
    priority: 6,
    successRate: 95
  },
  {
    id: "ACCDigitalNetwork.us",
    name: "ACC Digital Network",
    category: "sports",
    url: "https://raycom-accdn-firetv.amagi.tv/playlist.m3u8",
    logo: "https://upload.wikimedia.org/wikipedia/commons/thumb/1/1a/Atlantic_Coast_Conference_logo.svg/200px-Atlantic_Coast_Conference_logo.svg.png",
    country: "US",
    language: "en",
    requiresProxy: false,
    priority: 6,
    successRate: 90
  },
  {
    id: "Africa24Sport.fr",
    name: "Africa 24 Sport",
    category: "sports",
    url: "https://africa24.vedge.infomaniak.com/livecast/ik:africa24sport/manifest.m3u8",
    logo: "https://i.imgur.com/2JDRnfH.png",
    country: "FR",
    language: "fr",
    requiresProxy: false,
    priority: 6,
    successRate: 95
  }
]

// Função para obter apenas os canais principais
export function getMainChannels(): MainChannel[] {
  return MAIN_CHANNELS.sort((a, b) => {
    // Ordenar por prioridade (maior primeiro)
    if (a.priority !== b.priority) return b.priority - a.priority
    // Depois por taxa de sucesso
    if (a.successRate !== b.successRate) return b.successRate - a.successRate
    // Por último, alfabeticamente
    return a.name.localeCompare(b.name)
  })
}

// Função para obter canais por categoria
export function getMainChannelsByCategory(category: string): MainChannel[] {
  return MAIN_CHANNELS
    .filter(ch => ch.category === category)
    .sort((a, b) => b.priority - a.priority)
}

// Função para obter apenas canais de alta qualidade (successRate >= 85%)
export function getHighQualityChannels(): MainChannel[] {
  return MAIN_CHANNELS
    .filter(ch => ch.successRate >= 85)
    .sort((a, b) => b.successRate - a.successRate)
}

// Função para obter apenas canais que funcionam sem proxy
export function getDirectChannels(): MainChannel[] {
  return MAIN_CHANNELS
    .filter(ch => !ch.requiresProxy)
    .sort((a, b) => b.priority - a.priority)
}

// Função para obter canais que precisam de proxy
export function getProxyChannels(): MainChannel[] {
  return MAIN_CHANNELS
    .filter(ch => ch.requiresProxy)
    .sort((a, b) => b.priority - a.priority)
}