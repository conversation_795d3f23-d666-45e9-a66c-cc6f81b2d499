import Stripe from 'stripe'

export const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2025-01-27.acacia',
  typescript: true,
})

// Configuração do produto e preço
export const STRIPE_CONFIG = {
  // Produto criado via MCP Docker Stripe
  productId: process.env.STRIPE_PRODUCT_ID || 'prod_SlBxDaGOJCF0ZT',
  // Preço mensal de €20
  priceId: process.env.STRIPE_PRICE_ID || 'price_1RpjBgDMzmKMrtWZnEISCCIy',
  // URLs de redirecionamento - sempre usar URLs absolutas corretas
  successUrl: process.env.NODE_ENV === 'production' 
    ? 'https://newspports.com/subscription/success'
    : `${process.env.NEXT_PUBLIC_APP_URL}/subscription/success`,
  cancelUrl: process.env.NODE_ENV === 'production'
    ? 'https://newspports.com/subscription/cancel'
    : `${process.env.NEXT_PUBLIC_APP_URL}/subscription/cancel`,
  // Portal do cliente
  customerPortalUrl: process.env.NODE_ENV === 'production'
    ? 'https://newspports.com/api/stripe/customer-portal'
    : `${process.env.NEXT_PUBLIC_APP_URL}/api/stripe/customer-portal`,
}

// Função para criar sessão de checkout
export async function createCheckoutSession({
  customerId,
  customerEmail,
  priceId = STRIPE_CONFIG.priceId,
  successUrl = STRIPE_CONFIG.successUrl,
  cancelUrl = STRIPE_CONFIG.cancelUrl,
  trialDays,
  couponId,
}: {
  customerId?: string
  customerEmail?: string
  priceId?: string
  successUrl?: string
  cancelUrl?: string
  trialDays?: number
  couponId?: string
}) {
  const sessionData: Stripe.Checkout.SessionCreateParams = {
    mode: 'subscription',
    payment_method_types: ['card'],
    line_items: [
      {
        price: priceId,
        quantity: 1,
      },
    ],
    success_url: successUrl,
    cancel_url: cancelUrl,
    allow_promotion_codes: true,
    billing_address_collection: 'required',
    subscription_data: {
      metadata: {
        productId: STRIPE_CONFIG.productId,
      },
    },
  }

  // Adicionar cliente ou email
  if (customerId) {
    sessionData.customer = customerId
  } else if (customerEmail) {
    sessionData.customer_email = customerEmail
  }

  // Adicionar período de trial
  if (trialDays) {
    sessionData.subscription_data!.trial_period_days = trialDays
  }

  // Adicionar cupom
  if (couponId) {
    sessionData.discounts = [{ coupon: couponId }]
  }

  const session = await stripe.checkout.sessions.create(sessionData)
  return session
}

// Função para criar portal do cliente
export async function createCustomerPortalSession({
  customerId,
  returnUrl,
}: {
  customerId: string
  returnUrl: string
}) {
  const session = await stripe.billingPortal.sessions.create({
    customer: customerId,
    return_url: returnUrl,
  })
  return session
}

// Função para criar ou obter cliente
export async function createOrGetStripeCustomer({
  email,
  name,
  userId,
}: {
  email: string
  name?: string
  userId?: string
}) {
  // Buscar cliente existente
  const existingCustomers = await stripe.customers.list({
    email,
    limit: 1,
  })

  if (existingCustomers.data.length > 0) {
    return existingCustomers.data[0]
  }

  // Criar novo cliente
  const customer = await stripe.customers.create({
    email,
    name,
    metadata: {
      userId,
    },
  })

  return customer
}

// Função para verificar status da assinatura
export async function getSubscriptionStatus(customerId: string) {
  const subscriptions = await stripe.subscriptions.list({
    customer: customerId,
    status: 'active',
    limit: 1,
  })

  if (subscriptions.data.length === 0) {
    return null
  }

  const subscription = subscriptions.data[0]
  
  return {
    id: subscription.id,
    status: subscription.status,
    currentPeriodEnd: new Date(subscription.current_period_end * 1000),
    cancelAtPeriodEnd: subscription.cancel_at_period_end,
    trialEnd: subscription.trial_end ? new Date(subscription.trial_end * 1000) : null,
  }
}

// Função para cancelar assinatura
export async function cancelSubscription(subscriptionId: string) {
  const subscription = await stripe.subscriptions.update(subscriptionId, {
    cancel_at_period_end: true,
  })
  return subscription
}

// Função para reativar assinatura
export async function reactivateSubscription(subscriptionId: string) {
  const subscription = await stripe.subscriptions.update(subscriptionId, {
    cancel_at_period_end: false,
  })
  return subscription
}

// Função helper para criar preço recorrente mensal
export async function createMonthlyPrice(productId: string, amount: number) {
  const price = await stripe.prices.create({
    product: productId,
    unit_amount: amount * 100, // Converter para centavos
    currency: 'usd',
    recurring: {
      interval: 'month',
      interval_count: 1,
    },
  })
  return price
}