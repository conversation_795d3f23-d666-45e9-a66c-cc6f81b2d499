// DESABILITADO - Causando conflitos com Object.defineProperty
// Este interceptador foi temporariamente desabilitado devido a problemas de compatibilidade

/*
// Interceptador específico para o erro "Cannot read properties of null (reading 'type')"
export class TypeErrorInterceptor {
  private static originalTypeGetter: any = null
  private static interceptedObjects = new Set<any>()
  
  static init() {
    if (typeof window === 'undefined') return
    
    console.log('🎯 TypeErrorInterceptor: Iniciando interceptação...')
    
    // Intercepta Object.defineProperty para detectar definições de 'type'
    this.interceptObjectDefineProperty()
    
    // Intercepta acessos a propriedades 'type'
    this.interceptTypeAccess()
    
    // Monitora objetos conhecidos
    this.monitorKnownObjects()
  }
  
  private static interceptObjectDefineProperty() {
    const original = Object.defineProperty
    
    Object.defineProperty = function(obj: any, prop: string, descriptor: PropertyDescriptor) {
      // Log quando 'type' é definido
      if (prop === 'type') {
        console.log('📝 TypeErrorInterceptor: Definindo propriedade "type":', {
          object: obj,
          descriptor: descriptor,
          stack: new Error().stack
        })
        
        // Se está definindo getter/setter, intercepta
        if (descriptor.get) {
          const originalGetter = descriptor.get
          descriptor.get = function() {
            const value = originalGetter.call(this)
            
            if (value === null || value === undefined) {
              console.warn('⚠️ TypeErrorInterceptor: getter "type" retornou null/undefined:', {
                object: this,
                value: value,
                stack: new Error().stack
              })
            }
            
            return value
          }
        }
      }
      
      return original.call(this, obj, prop, descriptor)
    }
  }
  
  private static interceptTypeAccess() {
    // Cria um Proxy handler para interceptar acessos
    const handler = {
      get(target: any, prop: string) {
        if (prop === 'type') {
          console.log('🔍 TypeErrorInterceptor: Acesso a propriedade "type":', {
            target: target,
            hasType: 'type' in target,
            typeValue: target.type,
            isNull: target.type === null
          })
          
          // Se type é null, loga o stack trace
          if (target.type === null) {
            console.error('❌ TypeErrorInterceptor: Acesso a type null detectado!', {
              stack: new Error().stack
            })
          }
        }
        
        return target[prop]
      }
    }
    
    // Função para wrappar objetos suspeitos
    (window as any).__wrapWithTypeCheck = (obj: any) => {
      if (!obj || typeof obj !== 'object') return obj
      
      try {
        return new Proxy(obj, handler)
      } catch (e) {
        console.warn('⚠️ TypeErrorInterceptor: Não foi possível criar Proxy:', e)
        return obj
      }
    };
  }
  
  private static monitorKnownObjects() {
    // Lista de objetos que comumente têm propriedade 'type'
    const objectsToMonitor = [
      { name: 'ethereum', path: () => (window as any).ethereum },
      { name: 'ethereum.request', path: () => (window as any).ethereum?.request },
      { name: 'trustwallet', path: () => (window as any).trustwallet },
      { name: 'web3', path: () => (window as any).web3 },
      { name: 'Web3Provider', path: () => (window as any).Web3Provider },
    ]
    
    objectsToMonitor.forEach(({ name, path }) => {
      try {
        const obj = path()
        if (obj) {
          console.log(`🔍 TypeErrorInterceptor: Monitorando ${name}`)
          
          // Verifica se tem propriedade type
          if ('type' in obj) {
            console.log(`📌 ${name} tem propriedade "type":`, obj.type)
            
            // Se type é acessível via getter, tenta interceptar
            const descriptor = Object.getOwnPropertyDescriptor(obj, 'type')
            if (descriptor?.get) {
              console.log(`🎯 ${name}.type tem getter`)
            }
          }
          
          // Adiciona à lista de objetos interceptados
          this.interceptedObjects.add(obj)
        }
      } catch (e) {
        console.warn(`⚠️ TypeErrorInterceptor: Erro ao monitorar ${name}:`, e)
      }
    })
    
    // Monitora mudanças no window
    this.watchForNewObjects()
  }
  
  private static watchForNewObjects() {
    // Usa um MutationObserver para detectar quando novos scripts são carregados
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList') {
          mutation.addedNodes.forEach((node) => {
            if (node instanceof HTMLScriptElement) {
              console.log('📜 TypeErrorInterceptor: Novo script adicionado:', node.src || 'inline')
              
              // Re-verifica objetos após novo script
              setTimeout(() => this.monitorKnownObjects(), 100)
            }
          })
        }
      })
    })
    
    observer.observe(document.head, {
      childList: true,
      subtree: true
    })
    
    observer.observe(document.body, {
      childList: true,
      subtree: true
    })
  }
  
  static diagnose() {
    console.log('🏥 TypeErrorInterceptor: Executando diagnóstico...')
    
    // Procura por todos os objetos com propriedade 'type'
    const objectsWithType: any[] = []
    
    const checkObject = (obj: any, path: string, visited = new WeakSet()) => {
      if (!obj || typeof obj !== 'object' || visited.has(obj)) return
      visited.add(obj)
      
      try {
        if ('type' in obj) {
          const typeValue = obj.type
          objectsWithType.push({
            path,
            object: obj,
            typeValue,
            isNull: typeValue === null,
            isUndefined: typeValue === undefined
          })
        }
        
        // Recursivamente verifica propriedades (limitado)
        Object.keys(obj).slice(0, 10).forEach(key => {
          try {
            checkObject(obj[key], `${path}.${key}`, visited)
          } catch (e) {
            // Ignora erros de acesso
          }
        })
      } catch (e) {
        // Ignora erros
      }
    }
    
    // Verifica window e objetos conhecidos
    checkObject(window, 'window')
    checkObject(document, 'document')
    
    console.table(objectsWithType.filter(o => o.isNull || o.isUndefined))
    
    return objectsWithType
  }
}
*/

// Classe vazia para evitar erros de importação
export class TypeErrorInterceptor {
  static init() {
    // Não faz nada
  }
  
  static diagnose() {
    console.log('TypeErrorInterceptor está desabilitado')
    return []
  }
}