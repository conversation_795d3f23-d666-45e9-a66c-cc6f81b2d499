// Monitor detalhado de erros de extensões do navegador
export class ExtensionMonitor {
  private static errorCount = 0
  private static errors: any[] = []
  
  static init() {
    if (typeof window === 'undefined') return
    
    console.log('🔍 ExtensionMonitor: Iniciando monitoramento de extensões...')
    
    // Detecta extensões instaladas
    this.detectExtensions()
    
    // Monitora erros globais
    this.setupErrorHandlers()
    
    // Monitora mudanças no DOM que podem ser causadas por extensões
    this.setupMutationObserver()
    
    // Verifica propriedades injetadas por extensões
    this.checkInjectedProperties()
  }
  
  private static detectExtensions() {
    console.log('🔍 ExtensionMonitor: Detectando extensões instaladas...')
    
    const extensionChecks = [
      { name: 'MetaMask', check: () => (window as any).ethereum },
      { name: 'Trust Wallet', check: () => (window as any).trustwallet },
      { name: 'Phantom', check: () => (window as any).solana || (window as any).phantom },
      { name: 'Coinbase', check: () => (window as any).coinbaseWalletExtension },
      { name: 'Binance', check: () => (window as any).BinanceChain },
      { name: 'Brave Wallet', check: () => (window as any).brave?.wallet },
      { name: 'Rabby', check: () => (window as any).rabby },
      { name: 'OKX Wallet', check: () => (window as any).okxwallet },
      { name: 'WalletConnect', check: () => (window as any).WalletConnect }
    ]
    
    const detected: string[] = []
    
    extensionChecks.forEach(({ name, check }) => {
      try {
        const result = check()
        if (result) {
          detected.push(name)
          console.log(`✅ ExtensionMonitor: ${name} detectado:`, result)
        }
      } catch (e) {
        console.warn(`⚠️ ExtensionMonitor: Erro ao verificar ${name}:`, e)
      }
    })
    
    if (detected.length > 0) {
      console.log('📋 ExtensionMonitor: Extensões detectadas:', detected)
    } else {
      console.log('ℹ️ ExtensionMonitor: Nenhuma extensão de carteira detectada')
    }
  }
  
  private static setupErrorHandlers() {
    // Captura erros síncronos
    const originalError = window.onerror
    window.onerror = (message, source, lineno, colno, error) => {
      if (source && source.includes('chrome-extension://')) {
        this.logExtensionError({
          type: 'sync',
          message,
          source,
          lineno,
          colno,
          error,
          extensionId: this.extractExtensionId(source)
        })
        return true // Previne propagação
      }
      
      // Se o erro menciona 'type' e null, pode ser relacionado
      if (message && message.toString().includes("Cannot read properties of null (reading 'type')")) {
        console.error('🚨 ExtensionMonitor: Erro específico detectado:', {
          message,
          source,
          error,
          stack: error?.stack
        })
        
        // Tenta identificar a origem
        if (error?.stack) {
          this.analyzeStackTrace(error.stack)
        }
      }
      
      return originalError ? originalError(message, source, lineno, colno, error) : false
    }
    
    // Captura promises rejeitadas
    window.addEventListener('unhandledrejection', (event) => {
      const error = event.reason
      
      if (error?.stack && error.stack.includes('chrome-extension://')) {
        this.logExtensionError({
          type: 'promise',
          error,
          extensionId: this.extractExtensionId(error.stack)
        })
        event.preventDefault()
      }
      
      // Verifica se é o erro específico
      if (error?.message?.includes("Cannot read properties of null (reading 'type')")) {
        console.error('🚨 ExtensionMonitor: Promise rejeitada com erro específico:', {
          error,
          stack: error?.stack
        })
        this.analyzeStackTrace(error?.stack)
      }
    })
    
    console.log('✅ ExtensionMonitor: Handlers de erro configurados')
  }
  
  private static setupMutationObserver() {
    // Observa mudanças no DOM que podem ser causadas por extensões
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList') {
          mutation.addedNodes.forEach((node) => {
            if (node instanceof HTMLElement) {
              // Verifica se é um elemento injetado por extensão
              const src = node.getAttribute('src')
              if (src && src.includes('chrome-extension://')) {
                console.log('🔍 ExtensionMonitor: Elemento injetado por extensão:', {
                  tag: node.tagName,
                  src,
                  extensionId: this.extractExtensionId(src)
                })
              }
            }
          })
        }
      })
    })
    
    // Inicia observação após um pequeno delay
    setTimeout(() => {
      observer.observe(document.body, {
        childList: true,
        subtree: true
      })
      console.log('✅ ExtensionMonitor: MutationObserver configurado')
    }, 1000)
  }
  
  private static checkInjectedProperties() {
    console.log('🔍 ExtensionMonitor: Verificando propriedades injetadas...')
    
    // Lista de propriedades comumente injetadas por extensões
    const suspiciousProps = [
      'ethereum',
      'web3',
      'tronWeb',
      'solana',
      'phantom',
      'trustwallet',
      'metamask',
      '__REACT_DEVTOOLS_GLOBAL_HOOK__',
      '__REDUX_DEVTOOLS_EXTENSION__'
    ]
    
    suspiciousProps.forEach(prop => {
      if ((window as any)[prop]) {
        console.log(`📌 ExtensionMonitor: Propriedade global '${prop}' detectada:`, {
          type: typeof (window as any)[prop],
          value: (window as any)[prop]
        })
        
        // Tenta acessar propriedades do objeto para ver se causa erro
        try {
          const obj = (window as any)[prop]
          if (obj && typeof obj === 'object') {
            Object.keys(obj).forEach(key => {
              try {
                const value = obj[key]
                // Apenas loga, não faz nada com o valor
              } catch (e) {
                console.warn(`⚠️ ExtensionMonitor: Erro ao acessar ${prop}.${key}:`, e)
              }
            })
          }
        } catch (e) {
          console.error(`🚨 ExtensionMonitor: Erro ao inspecionar ${prop}:`, e)
        }
      }
    })
  }
  
  private static extractExtensionId(url: string): string {
    const match = url.match(/chrome-extension:\/\/([a-z0-9]+)/)
    return match ? match[1] : 'unknown'
  }
  
  private static analyzeStackTrace(stack?: string) {
    if (!stack) return
    
    console.log('🔍 ExtensionMonitor: Analisando stack trace...')
    
    // Procura por padrões específicos
    if (stack.includes('inpage.js')) {
      console.log('⚠️ ExtensionMonitor: Erro originado de inpage.js (comum em carteiras crypto)')
    }
    
    if (stack.includes('mr.<anonymous>')) {
      console.log('⚠️ ExtensionMonitor: Código minificado/ofuscado detectado')
    }
    
    // Extrai linhas relevantes
    const lines = stack.split('\n').slice(0, 5)
    console.log('📋 ExtensionMonitor: Primeiras 5 linhas do stack:', lines)
  }
  
  private static logExtensionError(errorInfo: any) {
    this.errorCount++
    this.errors.push({
      ...errorInfo,
      timestamp: new Date().toISOString()
    })
    
    console.warn('🔌 ExtensionMonitor: Erro de extensão capturado:', {
      count: this.errorCount,
      ...errorInfo
    })
    
    // Limita o array de erros
    if (this.errors.length > 10) {
      this.errors = this.errors.slice(-10)
    }
  }
  
  static getErrorSummary() {
    return {
      totalErrors: this.errorCount,
      recentErrors: this.errors
    }
  }
}