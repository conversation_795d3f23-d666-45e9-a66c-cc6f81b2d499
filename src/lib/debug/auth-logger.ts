// Sistema de logs para debug de autenticação
export class AuthLogger {
  private static isDebugEnabled = process.env.NEXT_PUBLIC_AUTH_DEBUG === 'true'
  
  static log(message: string, data?: any) {
    if (!this.isDebugEnabled) return
    
    const timestamp = new Date().toISOString()
    console.log(`[${timestamp}] ${message}`, data || '')
  }
  
  static error(message: string, error?: any) {
    if (!this.isDebugEnabled) return
    
    const timestamp = new Date().toISOString()
    console.error(`[${timestamp}] ${message}`, error || '')
    
    if (error?.stack) {
      console.error('Stack trace:', error.stack)
    }
  }
  
  static debug(message: string, data?: any) {
    if (!this.isDebugEnabled) return
    
    const timestamp = new Date().toISOString()
    console.debug(`[${timestamp}] ${message}`, data || '')
  }
  
  static warn(message: string, data?: any) {
    if (!this.isDebugEnabled) return
    
    const timestamp = new Date().toISOString()
    console.warn(`[${timestamp}] ${message}`, data || '')
  }
  
  static group(label: string) {
    if (!this.isDebugEnabled) return
    console.group(label)
  }
  
  static groupEnd() {
    if (!this.isDebugEnabled) return
    console.groupEnd()
  }
  
  // Função para mascarar valores sensíveis
  static maskSensitive(value: string, showChars = 4): string {
    if (!value) return 'null'
    if (value.length <= showChars * 2) return '***'
    return value.substring(0, showChars) + '...' + value.substring(value.length - showChars)
  }
  
  // Função para logar headers de forma segura
  static logHeaders(headers: Record<string, string>) {
    if (!this.isDebugEnabled) return
    
    const safeHeaders = { ...headers }
    
    // Mascara valores sensíveis
    const sensitiveKeys = ['authorization', 'x-stack-secret-server-key', 'cookie', 'set-cookie']
    
    Object.keys(safeHeaders).forEach(key => {
      if (sensitiveKeys.includes(key.toLowerCase())) {
        safeHeaders[key] = this.maskSensitive(safeHeaders[key])
      }
    })
    
    this.debug('Headers:', safeHeaders)
  }
}