export interface WorkingSportsChannel {
  id: string
  name: string
  url: string
  category: string
  logo?: string
}

export const WORKING_SPORTS_CHANNELS: WorkingSportsChannel[] = [
  {
    id: 'golf-30a',
    name: '30A Golf',
    url: 'https://30a-tv.com/feeds/vidaa/golf.m3u8',
    category: 'Deportes',
    logo: 'https://i.imgur.com/golf.png'
  },
  {
    id: 'aspor-tr',
    name: 'A Spor',
    url: 'https://trkvz-live.daioncdn.net/aspor/aspor.m3u8',
    category: 'Deportes',
    logo: 'https://i.imgur.com/aspor.png'
  },
  {
    id: 'abu-dhabi-sports-1',
    name: 'Abu Dhabi Sports 1',
    url: 'https://vo-live-media.cdb.cdn.orange.com/Content/Channel/AbuDhabiSportsChannel1/HLS/index.m3u8',
    category: 'Deportes',
    logo: 'https://i.imgur.com/abudhabi1.png'
  },
  {
    id: 'abu-dhabi-sports-2',
    name: 'Abu Dhabi Sports 2',
    url: 'https://vo-live-media.cdb.cdn.orange.com/Content/Channel/AbuDhabiSportsChannel2/HLS/index.m3u8',
    category: 'Deportes',
    logo: 'https://i.imgur.com/abudhabi2.png'
  },
  {
    id: 'acc-network',
    name: 'ACC Network',
    url: 'https://fl3.moveonjoy.com/ACC_NETWORK/index.m3u8',
    category: 'Deportes',
    logo: 'https://i.imgur.com/acc.png'
  },
  {
    id: 'adjarasport',
    name: 'Adjarasport',
    url: 'https://live20.bozztv.com/dvrfl05/gin-adjara/index.m3u8',
    category: 'Deportes',
    logo: 'https://i.imgur.com/adjara.png'
  },
  {
    id: 'afrosport',
    name: 'AfroSport',
    url: 'https://newproxy3.vidivu.tv/vidivu_afrosport/index.m3u8',
    category: 'Deportes',
    logo: 'https://i.imgur.com/afro.png'
  },
  {
    id: 'alkass-one',
    name: 'Alkass One',
    url: 'https://liveakgr.alkassdigital.net/hls/live/2097037/Alkass1muytrdc/master.m3u8',
    category: 'Deportes',
    logo: 'https://i.imgur.com/alkass1.png'
  },
  {
    id: 'alkass-two',
    name: 'Alkass Two',
    url: 'https://liveakgr.alkassdigital.net/hls/live/2097037/Alkass2hefazq/master.m3u8',
    category: 'Deportes',
    logo: 'https://i.imgur.com/alkass2.png'
  },
  {
    id: 'alkass-three',
    name: 'Alkass Three',
    url: 'https://liveakgr.alkassdigital.net/hls/live/2097037/Alkass3vakazq/master.m3u8',
    category: 'Deportes',
    logo: 'https://i.imgur.com/alkass3.png'
  },
  {
    id: 'alkass-four',
    name: 'Alkass Four',
    url: 'https://liveakgr.alkassdigital.net/hls/live/2097037/Alkass4azq/master.m3u8',
    category: 'Deportes',
    logo: 'https://i.imgur.com/alkass4.png'
  },
  {
    id: 'alkass-five',
    name: 'Alkass Five',
    url: 'https://liveakgr.alkassdigital.net/hls/live/2097037/Alkass5azq/master.m3u8',
    category: 'Deportes',
    logo: 'https://i.imgur.com/alkass5.png'
  },
  {
    id: 'alkass-six',
    name: 'Alkass Six',
    url: 'https://liveakgr.alkassdigital.net/hls/live/2097037/Alkass6buzay/master.m3u8',
    category: 'Deportes',
    logo: 'https://i.imgur.com/alkass6.png'
  },
  {
    id: 'antena-sport',
    name: 'Antena Sport',
    url: 'https://stream1.antenaplay.ro/as/asrolive1/playlist.m3u8',
    category: 'Deportes',
    logo: 'https://i.imgur.com/antena.png'
  },
  {
    id: 'bahrain-sports-1',
    name: 'Bahrain Sports 1',
    url: 'https://5c7b683162943.streamlock.net/live/ngrp:sportsone_all/playlist.m3u8',
    category: 'Deportes',
    logo: 'https://i.imgur.com/bahrain1.png'
  },
  {
    id: 'bahrain-sports-2',
    name: 'Bahrain Sports 2',
    url: 'https://5c7b683162943.streamlock.net/live/ngrp:bahrainsportstwo_all/playlist.m3u8',
    category: 'Deportes',
    logo: 'https://i.imgur.com/bahrain2.png'
  },
  {
    id: 'bek-sports',
    name: 'BEK Sports',
    url: 'https://cdn3.wowza.com/5/ZWQ1K2NYTmpFbGsr/BEK-WOWZA-1/smil:BEKPRIMEW.smil/playlist.m3u8',
    category: 'Deportes',
    logo: 'https://i.imgur.com/bek.png'
  },
  {
    id: 'internacional-sports',
    name: 'Internacional Sports',
    url: 'https://video01.soultv.com.br/internacional/internacional/playlist.m3u8',
    category: 'Deportes',
    logo: 'https://i.imgur.com/inter.png'
  },
  {
    id: 'cbs-sports',
    name: 'CBS Sports Network',
    url: 'https://fl3.moveonjoy.com/CBS_SPORTS_NETWORK/index.m3u8',
    category: 'Deportes',
    logo: 'https://i.imgur.com/cbs.png'
  },
  {
    id: 'ortb-sports',
    name: 'ORTB Sports',
    url: 'https://strhls.streamakaci.tv/ortb/ortb2-multi/playlist.m3u8',
    category: 'Deportes',
    logo: 'https://i.imgur.com/ortb.png'
  },
  {
    id: 'raycom-sports',
    name: 'Raycom Sports',
    url: 'https://raycom-accdn-firetv.amagi.tv/playlist.m3u8',
    category: 'Deportes',
    logo: 'https://i.imgur.com/raycom.png'
  },
  {
    id: 'alkass-shoof',
    name: 'Alkass SHOOF',
    url: 'https://liveakgr.alkassdigital.net/hls/live/2097037/Alkass6Shoof1ab/master.m3u8',
    category: 'Deportes',
    logo: 'https://i.imgur.com/alkass-shoof.png'
  }
]

// Função para obter todos os canais funcionando
export function getWorkingSportsChannels(): WorkingSportsChannel[] {
  return WORKING_SPORTS_CHANNELS
}

// Função para obter canal por ID
export function getSportsChannelById(id: string): WorkingSportsChannel | undefined {
  return WORKING_SPORTS_CHANNELS.find(channel => channel.id === id)
}

// Função para obter canais por categoria
export function getSportsChannelsByCategory(category: string): WorkingSportsChannel[] {
  return WORKING_SPORTS_CHANNELS.filter(channel => 
    channel.category.toLowerCase() === category.toLowerCase()
  )
}