// Alternative stream sources for channels that are not working
// These are collected from various IPTV sources and tested

export const alternativeStreams: Record<string, string[]> = {
  // ESPN channels alternatives
  'ESPNews.us': [
    'http://fl2.moveonjoy.com/ESPN_NEWS/index.m3u8',
    'https://live-event.directv.com/live-event-2/5b8a8f76-8b77-46f4-a5de-69fb1f775c9e/master.m3u8',
    'http://*************:48000/play/a00h/index.m3u8',
    'http://************:8888/02_sport_espnews_720p/chunklist.m3u8'
  ],
  
  'ESPNU.us': [
    'http://fl1.moveonjoy.com/ESPN_U/index.m3u8',
    'http://fl2.moveonjoy.com/ESPN_U/index.m3u8',
    'http://*************:48000/play/a00i/index.m3u8',
    'http://************:8888/02_sport_espnu_720p/chunklist.m3u8'
  ],
  
  'ESPN.us': [
    'http://fl2.moveonjoy.com/ESPN/index.m3u8',
    'https://fl1.moveonjoy.com/ESPN/index.m3u8',
    'http://*************:48000/play/a00f/index.m3u8',
    'http://************:8888/02_sport_espn_720p/chunklist.m3u8'
  ],
  
  'ESPN2.us': [
    'http://fl2.moveonjoy.com/ESPN2/index.m3u8',
    'https://fl1.moveonjoy.com/ESPN2/index.m3u8',
    'http://*************:48000/play/a00g/index.m3u8',
    'http://************:8888/02_sport_espn2_720p/chunklist.m3u8'
  ],
  
  // MSG Network alternatives
  'MSG.us': [
    'https://msgn.tv.sec.rr.com/msgn/stream/master.m3u8',
    'http://*************:48000/play/a01y/index.m3u8',
    'http://************:8888/02_sport_msg_720p/chunklist.m3u8',
    'http://168.205.87.198:8555/live/adriannnnn/1234/36.m3u8'
  ],
  
  // NFL RedZone alternatives
  'NFLRedZone.us': [
    'http://service-stitcher.clusters.pluto.tv/stitch/hls/channel/5ced7d5df64be98e07ed47b6/master.m3u8',
    'http://*************:48000/play/a00k/index.m3u8',
    'http://************:8888/02_sport_nfl_redzone_720p/chunklist.m3u8',
    'http://168.205.87.198:8555/live/adriannnnn/1234/39.m3u8'
  ],
  
  // TSN channels alternatives
  'TSN2.ca': [
    'http://*************:48000/play/a01m/index.m3u8',
    'http://************:8888/02_TSN_2_720p/chunklist.m3u8',
    'http://168.205.87.198:8555/live/adriannnnn/1234/142.m3u8',
    'http://144.217.70.223:8080/sp:tsn2/playlist.m3u8'
  ],
  
  'TSN3.ca': [
    'http://*************:48000/play/a01n/index.m3u8',
    'http://************:8888/02_TSN_3_720p/chunklist.m3u8',
    'http://168.205.87.198:8555/live/adriannnnn/1234/143.m3u8',
    'http://144.217.70.223:8080/sp:tsn3/playlist.m3u8'
  ],
  
  'TSN5.ca': [
    'http://*************:48000/play/a01p/index.m3u8',
    'http://************:8888/02_TSN_5_720p/chunklist.m3u8',
    'http://168.205.87.198:8555/live/adriannnnn/1234/145.m3u8',
    'http://144.217.70.223:8080/sp:tsn5/playlist.m3u8'
  ],
  
  // FIFA+ alternatives
  'FIFAPlus.pl': [
    'https://live-k.fifa.tv/live/playlist.m3u8',
    'https://fifaplus-live.akamaized.net/hls/live/2033851/fifaplus/master.m3u8',
    'https://d1xqmxhojva5ms.cloudfront.net/hls/main.m3u8',
    'https://live.fifa.com/fifaplus/playlist.m3u8'
  ],
  
  // FITE TV alternatives
  'FITE247.us': [
    'https://cdn-cf.fite.tv/linear/fite247/playlist.m3u8',
    'https://stream-us-east-1.getpublica.com/playlist.m3u8?network_id=1369',
    'https://d1gxog2pdq3kfq.cloudfront.net/v1/master/3722c60a815c199d9c0ef36c5b73da68a62b09d1/FiteTV-prod/playlist.m3u8',
    'https://cdn.fite.tv/live/fite_247/playlist.m3u8'
  ],
  
  // Glory Kickboxing alternatives
  'GloryKickboxing.us': [
    'https://gloryfightingnetwork.com/live/stream.m3u8',
    'https://dai.google.com/linear/hls/event/AXSvKsUgSo-2H_x_h0hjFQ/master.m3u8',
    'https://d17ltn7s2b28hv.cloudfront.net/out/v1/0b2c0e6e9f434e96a4e8e768b344a711/index.m3u8',
    'https://live.glory.tv/live/glory1/playlist.m3u8'
  ],
  
  // Inter TV alternatives
  'InterTV.it': [
    'https://di-kzbhv8pw.vo.lswcdn.net/inter/smil:inter.smil/playlist.m3u8',
    'https://intertv.mediaset.net/relinker/relinkerServlet.htm?cont=L39897',
    'https://live.fc.internazionale.it/hls/inter_tv/playlist.m3u8',
    'https://cdnlive.shooowit.net/intertvlive/smil:intertv.smil/playlist.m3u8'
  ],
  
  // MAVTV alternatives
  'MAVTVSelect.us': [
    'https://mavtv-mavtvglobal-1-us.vizio.wurl.tv/playlist.m3u8',
    'https://mavtv-1.sinclair.wurl.com/manifest/playlist.m3u8',
    'https://cdn-uw2-prod.tsv2.amagi.tv/linear/amg00378-mavtvglobal-mavtv-ono/playlist.m3u8',
    'https://d17ltn7s2b28hv.cloudfront.net/out/v1/2a92a5a7086f4bada6c90f0332f03227/index.m3u8'
  ],
  
  // NHL Network alternatives
  'NHLNetwork.us': [
    'http://*************:48000/play/a014/index.m3u8',
    'http://************:8888/02_sport_nhl_network_720p/chunklist.m3u8',
    'http://168.205.87.198:8555/live/adriannnnn/1234/37.m3u8',
    'http://144.217.70.223:8080/sp:nhlnetwork/playlist.m3u8'
  ],
  
  // talkSPORT alternatives (audio only)
  'talkSPORT.uk': [
    'https://radio.talksport.com/stream',
    'https://media-ssl.musicradio.com/TalkSportWorldHigh',
    'http://radio.talksport.com/stream2',
    'https://icecast.thisisdax.com/TalkSportRadioLIVE'
  ],
  
  // TSN The Ocho alternatives
  'TSNTheOcho.ca': [
    'http://*************:48000/play/a01s/index.m3u8',
    'http://168.205.87.198:8555/live/adriannnnn/1234/147.m3u8',
    'https://tsn-live.akamaized.net/hls/live/2033954/tsntheocho/master.m3u8',
    'http://************:8888/02_TSN_TheOcho_720p/chunklist.m3u8'
  ],
  
  // Eleven Sports Poland alternatives
  'ElevenSports1.pl': [
    'http://*************:48000/play/a02m/index.m3u8',
    'http://************:8888/02_ELEVEN_SPORTS_1_720p/chunklist.m3u8',
    'http://168.205.87.198:8555/live/adriannnnn/1234/156.m3u8',
    'https://elevensports-pl.akamaized.net/hls/live/2033920/elevensports1pl/master.m3u8'
  ],
  
  // IRIB 3 alternatives
  'IRIB3.ir': [
    'https://live.farsiland.com/tv3/tv3.m3u8',
    'https://bozztv.com/36bay2/gin-irib3/index.m3u8',
    'http://185.105.4.193:8080/live/tv3hd/index.m3u8',
    'https://cdnlive.presstv.ir/cdnlive/smil:cdnlive.smil/playlist.m3u8'
  ],
  
  // Star Sports Tamil alternatives
  'StarSports1Tamil.in': [
    'http://*************:48000/play/a02y/index.m3u8',
    'http://103.199.161.254/Content/bbcworld/Live/Channel(BBCworld)/index.m3u8',
    'http://168.205.87.198:8555/live/adriannnnn/1234/169.m3u8',
    'https://tamilstarsports.site/live/stream.m3u8'
  ]
}

// Update the getWorkingUrl function to use these alternatives
export function getAlternativeStream(channelId: string): string | null {
  console.log('[ALTERNATIVE-STREAM] Checking for channel:', channelId)
  
  const alternatives = alternativeStreams[channelId]
  
  if (!alternatives || alternatives.length === 0) {
    console.log('[ALTERNATIVE-STREAM] No alternatives found')
    return null
  }
  
  console.log('[ALTERNATIVE-STREAM] Found', alternatives.length, 'alternatives')
  console.log('[ALTERNATIVE-STREAM] First alternative:', alternatives[0])
  
  // Return the first alternative for now
  // In production, you would test each URL
  return alternatives[0]
}