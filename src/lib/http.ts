/**
 * Cliente HTTP centralizado com logging
 */

import { logger } from './logger';
import { LOG_TAGS } from '@/config/debug';

interface HttpOptions extends RequestInit {
  tag?: string;
  timeout?: number;
}

export class HttpError extends <PERSON>rror {
  constructor(
    public status: number,
    public statusText: string,
    public url: string,
    public response?: any
  ) {
    super(`HTTP ${status}: ${statusText}`);
    this.name = 'HttpError';
  }
}

export async function httpGet<T = any>(url: string, options: HttpOptions = {}): Promise<T> {
  const { tag = LOG_TAGS.HTTP, timeout = 30000, ...fetchOptions } = options;
  const startTime = Date.now();

  logger.debug(tag, `Request started`, {
    url,
    method: 'GET',
    headers: fetchOptions.headers
  });

  try {
    // Criar AbortController para timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);

    const response = await fetch(url, {
      ...fetchOptions,
      signal: controller.signal,
      method: 'GET'
    });

    clearTimeout(timeoutId);

    const responseTime = Date.now() - startTime;

    logger.debug(tag, `Response received`, {
      url,
      status: response.status,
      statusText: response.statusText,
      responseTime: `${responseTime}ms`,
      headers: Object.fromEntries(response.headers.entries())
    });

    if (!response.ok) {
      const errorBody = await response.text().catch(() => 'No error body');
      throw new HttpError(response.status, response.statusText, url, errorBody);
    }

    const data = await response.json();
    
    logger.trace(tag, `Response data`, {
      url,
      dataSize: JSON.stringify(data).length,
      sampleData: Array.isArray(data) ? data.slice(0, 2) : data
    });

    return data as T;
  } catch (error: any) {
    const responseTime = Date.now() - startTime;

    if (error.name === 'AbortError') {
      logger.error(tag, `Request timeout after ${timeout}ms`, { url });
      throw new Error(`Request timeout: ${url}`);
    }

    logger.error(tag, `Request failed`, {
      url,
      error: error.message,
      responseTime: `${responseTime}ms`,
      stack: error.stack
    });

    throw error;
  }
}

export async function httpPost<T = any>(url: string, body: any, options: HttpOptions = {}): Promise<T> {
  const { tag = LOG_TAGS.HTTP, timeout = 30000, ...fetchOptions } = options;
  const startTime = Date.now();

  logger.debug(tag, `POST request started`, {
    url,
    bodySize: JSON.stringify(body).length
  });

  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);

    const response = await fetch(url, {
      ...fetchOptions,
      signal: controller.signal,
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...fetchOptions.headers
      },
      body: JSON.stringify(body)
    });

    clearTimeout(timeoutId);

    const responseTime = Date.now() - startTime;

    logger.debug(tag, `POST response received`, {
      url,
      status: response.status,
      responseTime: `${responseTime}ms`
    });

    if (!response.ok) {
      const errorBody = await response.text().catch(() => 'No error body');
      throw new HttpError(response.status, response.statusText, url, errorBody);
    }

    const data = await response.json();
    return data as T;
  } catch (error: any) {
    const responseTime = Date.now() - startTime;

    logger.error(tag, `POST request failed`, {
      url,
      error: error.message,
      responseTime: `${responseTime}ms`
    });

    throw error;
  }
}