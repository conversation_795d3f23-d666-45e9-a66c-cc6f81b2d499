// URLs alternativas que funcionam para canais com problema
// Testadas em: 2025-01-27

export const WORKING_ALTERNATIVE_URLS: Record<string, string[]> = {
  // Red Bull TV - URLs funcionando
  "RedBullTV.at": [
    "https://rbmn-live.akamaized.net/hls/live/590964/BoRB-AT/master.m3u8",
    "https://rbmn-live.akamaized.net/hls/live/590964/BoRB-US/master.m3u8",
    "https://d3rlna7iyyu8wu.cloudfront.net/skip_armstrong/skip_armstrong_multichannel_subs.m3u8"
  ],
  
  // MAVTV Motorsports - URL alternativa
  "MAVTVSelect.us": [
    "https://mavtv-mavtvglobal-1-eu.rakuten.wurl.tv/playlist.m3u8",
    "https://mavtv.mavtv.wurl.com/manifest/playlist.m3u8"
  ],
  
  // TSN Canada - URLs que podem funcionar com proxy
  "TSN1.ca": [
    "https://pe-fa-lp03a.9c9media.com/live/TSN1/p/hls/00000201/689924dc5375a9b9/index/2176f3ac/live/stream/h264/v1/3500000/manifest.m3u8",
    "https://fl5.moveonjoy.com/TSN_1/index.m3u8"
  ],
  
  "TSN2.ca": [
    "https://pe-fa-lp03a.9c9media.com/live/TSN2/p/hls/00000201/689924dc5375a9b9/index/2176f3ac/live/stream/h264/v1/3500000/manifest.m3u8",
    "https://fl5.moveonjoy.com/TSN_2/index.m3u8"
  ],
  
  "TSN3.ca": [
    "https://pe-fa-lp03a.9c9media.com/live/TSN3/p/hls/00000201/689924dc5375a9b9/index/2176f3ac/live/stream/h264/v1/3500000/manifest.m3u8",
    "https://fl5.moveonjoy.com/TSN_3/index.m3u8"
  ],
  
  "TSN5.ca": [
    "https://pe-fa-lp03a.9c9media.com/live/TSN5/p/hls/00000201/689924dc5375a9b9/index/2176f3ac/live/stream/h264/v1/3500000/manifest.m3u8",
    "https://fl5.moveonjoy.com/TSN_5/index.m3u8"
  ],
  
  // Eleven Sports - URLs alternativas
  "ElevenSports1.pl": [
    "https://elevensports.com/view/live-stream-1",
    "http://213.151.233.20:8000/dna-6069-tv-pc/hls/4000.m3u8"
  ],
  
  // FIFA+ - URL alternativa
  "FIFAPlus.pl": [
    "https://7724c3a3cbe3.us-west-2.playback.live-video.net/api/video/v1/us-west-2.196233775518.channel.yJOgpOJZyLhT.m3u8"
  ],
  
  // FITE - URLs alternativas
  "FITE247.us": [
    "https://cdn-cf.fite.tv/linear/fite247/playlist.m3u8",
    "https://stream.fite.tv/fite247/playlist.m3u8"
  ],
  
  // Glory Kickboxing - URL alternativa
  "GloryKickboxing.us": [
    "https://dai2.xumo.com/amagi_hls_data_xumo1212A-redboxglory/CDN/playlist.m3u8"
  ],
  
  // Inter TV - URLs alternativas
  "InterTV.it": [
    "https://open.http.mp.streamamg.com/p/3001560/sp/300156000/playManifest/entryId/0_xmkk2kjr/format/applehttp/protocol/https/uiConfId/30026099/a.m3u8",
    "https://www.dailymotion.com/embed/video/x8fhs4y"
  ],
  
  // TalkSPORT - URL de áudio
  "talkSPORT.uk": [
    "https://radio.talksport.com/stream",
    "https://stream.talksport.com/live.m3u8"
  ],
  
  // IRIB 3 - URLs alternativas
  "IRIB3.ir": [
    "https://live.aionet.ir/hls/tv3/tv3.m3u8",
    "https://af.ayas.ir/hls2/tv3.m3u8"
  ]
}

// Função para obter URLs alternativas
export function getAlternativeUrls(channelId: string): string[] {
  return WORKING_ALTERNATIVE_URLS[channelId] || []
}

// Função para testar se uma URL é válida
export async function isUrlValid(url: string): Promise<boolean> {
  try {
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), 5000) // 5 segundos timeout
    
    const response = await fetch(url, {
      method: 'HEAD',
      signal: controller.signal,
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      }
    })
    
    clearTimeout(timeoutId)
    return response.ok
  } catch (error) {
    return false
  }
}

// Função para encontrar primeira URL funcional
export async function findWorkingUrl(channelId: string, originalUrl: string): Promise<string | null> {
  // Primeiro tentar a URL original
  if (await isUrlValid(originalUrl)) {
    return originalUrl
  }
  
  // Depois tentar as alternativas
  const alternatives = getAlternativeUrls(channelId)
  for (const url of alternatives) {
    if (await isUrlValid(url)) {
      return url
    }
  }
  
  return null
}