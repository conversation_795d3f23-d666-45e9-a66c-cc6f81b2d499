"use client";

import { StackClientApp } from "@stackframe/stack";

let stackClientInstance: StackClientApp | null = null;

export function getStackClient() {
  if (!stackClientInstance && typeof window !== 'undefined') {
    stackClientInstance = new StackClientApp({
      projectId: process.env.NEXT_PUBLIC_STACK_PROJECT_ID!,
      publishableClientKey: process.env.NEXT_PUBLIC_STACK_PUBLISHABLE_CLIENT_KEY!,
      tokenStore: "cookie",
    });
  }
  return stackClientInstance;
}

// For backwards compatibility
export const stackClient = typeof window !== 'undefined' ? getStackClient() : null;