"use client";

// Client-side Stack Auth configuration
import { Stack<PERSON>rovider, StackTheme } from "@stackframe/stack";
import { StackClientApp } from "@stackframe/stack";
import { useState, useEffect } from "react";
import { AuthLogger } from "@/lib/debug/auth-logger";

export function StackAuthProvider({ children }: { children: React.ReactNode }) {
  const [client, setClient] = useState<StackClientApp | null>(null);
  
  useEffect(() => {
    AuthLogger.log('🔐 Iniciando configuração do Stack Auth Client...')
    
    // Initialize client only on the client side
    try {
      // Adiciona proteção contra erros de extensões antes de inicializar
      if (typeof window !== 'undefined') {
        AuthLogger.debug('🌐 Ambiente: Browser detectado')
        
        // Temporariamente desabilita modificações de objetos globais
        const originalDefineProperty = Object.defineProperty;
        Object.defineProperty = function(obj, prop, descriptor) {
          // Ignora modificações de extensões em objetos do Stack Auth
          if (prop && typeof prop === 'string' && prop.includes('stackframe')) {
            AuthLogger.warn('⚠️ Bloqueada modificação de extensão em:', prop);
            return obj;
          }
          return originalDefineProperty.call(this, obj, prop, descriptor);
        };

        // Restaura após um pequeno delay
        setTimeout(() => {
          Object.defineProperty = originalDefineProperty;
        }, 100);
      }

      console.log('🔐 [StackClient] Verificando variáveis de ambiente...');
      console.log('  - NEXT_PUBLIC_STACK_PROJECT_ID:', process.env.NEXT_PUBLIC_STACK_PROJECT_ID ? '✅ Definida' : '❌ FALTANDO');
      console.log('  - NEXT_PUBLIC_STACK_PUBLISHABLE_CLIENT_KEY:', process.env.NEXT_PUBLIC_STACK_PUBLISHABLE_CLIENT_KEY ? '✅ Definida' : '❌ FALTANDO');
      console.log('  - NEXT_PUBLIC_APP_URL:', process.env.NEXT_PUBLIC_APP_URL || 'Usando localhost:3000');
      
      if (!process.env.NEXT_PUBLIC_STACK_PROJECT_ID || !process.env.NEXT_PUBLIC_STACK_PUBLISHABLE_CLIENT_KEY) {
        console.error('❌ [StackClient] ERRO: Variáveis de ambiente Stack Auth não configuradas!');
        console.error('Por favor, configure as seguintes variáveis no .env.local:');
        console.error('- NEXT_PUBLIC_STACK_PROJECT_ID');
        console.error('- NEXT_PUBLIC_STACK_PUBLISHABLE_CLIENT_KEY');
        throw new Error('Stack Auth environment variables missing');
      }
      
      const config = {
        projectId: process.env.NEXT_PUBLIC_STACK_PROJECT_ID!,
        publishableClientKey: process.env.NEXT_PUBLIC_STACK_PUBLISHABLE_CLIENT_KEY!,
        tokenStore: "cookie" as const,
        urls: {
          home: process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000",
          signIn: "/login",
          afterSignIn: "/browse",
          afterSignUp: "/browse",
          afterSignOut: "/",
          handler: "/handler",
        },
      }
      
      AuthLogger.debug('📦 Configuração Stack Auth:', {
        projectId: AuthLogger.maskSensitive(config.projectId),
        publishableClientKey: AuthLogger.maskSensitive(config.publishableClientKey),
        tokenStore: config.tokenStore,
        urls: config.urls
      })

      console.log('🔐 [StackClient] Criando instância do StackClientApp...');
      const stackClient = new StackClientApp(config);
      
      AuthLogger.log('✅ Stack Auth Client inicializado com sucesso')
      setClient(stackClient);
      
    } catch (error) {
      AuthLogger.error('🚨 Erro ao inicializar Stack Auth:', error);
      
      // Tenta novamente após um delay
      AuthLogger.log('🔄 Tentando reinicializar após 500ms...')
      setTimeout(() => {
        try {
          const stackClient = new StackClientApp({
            projectId: process.env.NEXT_PUBLIC_STACK_PROJECT_ID!,
            publishableClientKey: process.env.NEXT_PUBLIC_STACK_PUBLISHABLE_CLIENT_KEY!,
            tokenStore: "cookie",
            urls: {
              home: process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000",
              signIn: "/login",
              afterSignIn: "/browse",
              afterSignUp: "/browse",
              afterSignOut: "/",
              handler: "/handler",
            },
          });
          AuthLogger.log('✅ Stack Auth Client reinicializado com sucesso')
          setClient(stackClient);
        } catch (retryError) {
          AuthLogger.error('❌ Falha ao reinicializar Stack Auth:', retryError)
        }
      }, 500);
    }
    
    AuthLogger.log('🔚 Processo de configuração do Stack Auth Client encerrado')
  }, []);
  
  if (!client) {
    // During SSR or initial load, show a loading state
    return <div className="min-h-screen flex items-center justify-center">
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
    </div>;
  }
  
  return (
    <StackProvider app={client}>
      <StackTheme>
        {children}
      </StackTheme>
    </StackProvider>
  );
}