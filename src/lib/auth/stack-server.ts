// Server-side Stack Auth configuration
import "server-only";
import { StackServerApp } from "@stackframe/stack";

// Verificar variáveis de ambiente
const projectId = process.env.NEXT_PUBLIC_STACK_PROJECT_ID;
const publishableKey = process.env.NEXT_PUBLIC_STACK_PUBLISHABLE_CLIENT_KEY;
const secretKey = process.env.STACK_SECRET_SERVER_KEY;

console.log("\n🔐 [StackServerApp] Inicializando Stack Auth Server...");
console.log("🔐 [StackServerApp] Variáveis de ambiente:");
console.log("  - NEXT_PUBLIC_STACK_PROJECT_ID:", projectId ? "✅ Definida" : "❌ FALTANDO");
console.log("  - NEXT_PUBLIC_STACK_PUBLISHABLE_CLIENT_KEY:", publishableKey ? "✅ Definida" : "❌ FALTANDO");
console.log("  - STACK_SECRET_SERVER_KEY:", secretKey ? "✅ Definida" : "❌ FALTANDO");
console.log("  - NEXT_PUBLIC_APP_URL:", process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000");

if (!projectId || !publishableKey || !secretKey) {
  console.error("\n⚠️ Stack Auth environment variables missing:");
  console.error("- NEXT_PUBLIC_STACK_PROJECT_ID:", projectId ? "✓" : "✗");
  console.error("- NEXT_PUBLIC_STACK_PUBLISHABLE_CLIENT_KEY:", publishableKey ? "✓" : "✗");
  console.error("- STACK_SECRET_SERVER_KEY:", secretKey ? "✓" : "✗");
  console.error("");
  console.error("Please check your .env.local file and ensure all Stack Auth variables are set.");
  console.error("You can get these values from: https://app.stack-auth.com\n");
}

let stackServerApp;

try {
  stackServerApp = new StackServerApp({
    projectId: projectId || "dummy-project-id",
    publishableClientKey: publishableKey || "dummy-publishable-key",
    secretServerKey: secretKey || "dummy-secret-key",
    tokenStore: "nextjs-cookie",
    urls: {
      home: process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000",
      signIn: "/login",
      afterSignIn: "/browse",
      afterSignUp: "/browse",
      afterSignOut: "/",
      handler: "/handler",
    },
  });
  
  console.log("✅ [StackServerApp] Stack Auth Server inicializado com sucesso!");
  console.log("🔐 [StackServerApp] Configuração:", {
    tokenStore: "nextjs-cookie",
    urls: {
      home: process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000",
      signIn: "/login",
      afterSignIn: "/browse",
      afterSignUp: "/browse",
      afterSignOut: "/",
      handler: "/handler",
    }
  });
} catch (error) {
  console.error("❌ [StackServerApp] Erro ao inicializar Stack Auth:", error);
  console.error("Stack trace:", error instanceof Error ? error.stack : "N/A");
  
  // Criar uma instância dummy para evitar erros
  stackServerApp = new StackServerApp({
    projectId: "dummy-project-id",
    publishableClientKey: "dummy-publishable-key",
    secretServerKey: "dummy-secret-key",
    tokenStore: "nextjs-cookie",
    urls: {
      home: "http://localhost:3000",
      signIn: "/login",
      afterSignIn: "/browse",
      afterSignUp: "/browse",
      afterSignOut: "/",
      handler: "/handler",
    },
  });
}

export { stackServerApp };