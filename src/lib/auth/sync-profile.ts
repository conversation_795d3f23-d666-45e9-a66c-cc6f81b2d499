// Função para sincronizar perfil após login
export async function syncUserProfile(userId?: string) {
  try {
    const response = await fetch('/api/auth/sync-profile', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ userId })
    });
    
    if (!response.ok) {
      console.error('Failed to sync profile:', await response.text());
      return false;
    }
    
    const data = await response.json();
    console.log('Profile sync result:', data);
    return true;
  } catch (error) {
    console.error('Error syncing profile:', error);
    return false;
  }
}