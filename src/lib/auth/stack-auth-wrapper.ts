"use client";

import { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useStackApp, CurrentUser } from '@stackframe/stack';
import { validateTestCredentials, TEST_USERS } from './test-users';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';

interface MockUser {
  id: string;
  primaryEmail: string;
  displayName: string;
  emailVerified: boolean;
  hasPassword: boolean;
  signedUpAt: Date;
  clientMetadata: any;
  update: (data: any) => Promise<void>;
}

interface AuthContextType {
  user: CurrentUser | MockUser | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  signInWithTestCredentials: (email: string, password: string) => Promise<boolean>;
  signOut: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | null>(null);

export function StackAuthWrapper({ children }: { children: ReactNode }) {
  const stackApp = useStackApp();
  const router = useRouter();
  const [mockUser, setMockUser] = useState<MockUser | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Check for existing test session on mount
  useEffect(() => {
    const checkTestSession = () => {
      const storedUser = localStorage.getItem('streamplus-test-user');
      if (storedUser) {
        try {
          const userData = JSON.parse(storedUser);
          const testUser = TEST_USERS.find(u => u.profile.id === userData.id);
          if (testUser) {
            setMockUser({
              id: testUser.profile.id,
              primaryEmail: testUser.email,
              displayName: testUser.profile.displayName,
              emailVerified: true,
              hasPassword: true,
              signedUpAt: new Date(),
              clientMetadata: {
                role: testUser.profile.role,
                subscription: testUser.profile.subscription
              },
              update: async (data: any) => {
                console.log('Updating mock user:', data);
              }
            });
          }
        } catch (error) {
          console.error('Error loading test session:', error);
          localStorage.removeItem('streamplus-test-user');
        }
      }
      setIsLoading(false);
    };

    checkTestSession();
  }, []);

  const signInWithTestCredentials = async (email: string, password: string): Promise<boolean> => {
    const profile = validateTestCredentials(email, password);
    
    if (profile) {
      // Create mock user session
      const user: MockUser = {
        id: profile.id,
        primaryEmail: email,
        displayName: profile.displayName,
        emailVerified: true,
        hasPassword: true,
        signedUpAt: new Date(),
        clientMetadata: {
          role: profile.role,
          subscription: profile.subscription
        },
        update: async (data: any) => {
          console.log('Updating mock user:', data);
        }
      };
      
      // Store in localStorage
      localStorage.setItem('streamplus-test-user', JSON.stringify(profile));
      setMockUser(user);
      
      return true;
    }
    
    return false;
  };

  const signOut = async () => {
    // Clear test user session
    localStorage.removeItem('streamplus-test-user');
    setMockUser(null);
    
    // Try to sign out from Stack Auth if there's a real session
    try {
      await stackApp.signOut();
    } catch (error) {
      console.log('No Stack Auth session to sign out');
    }
    
    router.push('/');
  };

  const value: AuthContextType = {
    user: mockUser,
    isAuthenticated: !!mockUser,
    isLoading,
    signInWithTestCredentials,
    signOut
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useTestAuth() {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useTestAuth must be used within StackAuthWrapper');
  }
  return context;
}