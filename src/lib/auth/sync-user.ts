import { createServiceClient } from '@/lib/supabase/server'
import { CurrentUser } from '@stackframe/stack'

/**
 * Sincroniza usuário do Stack Auth com perfil no Supabase
 */
export async function syncStackUserToSupabase(stackUser: CurrentUser) {
  const timestamp = new Date().toISOString()
  console.log(`[SYNC-USER] ${timestamp} - ===== SYNCING USER =====`)
  console.log(`[SYNC-USER] ${timestamp} - Stack User ID:`, stackUser.id)
  console.log(`[SYNC-USER] ${timestamp} - Email:`, stackUser.primaryEmail)
  console.log(`[SYNC-USER] ${timestamp} - Display Name:`, stackUser.displayName)
  
  try {
    const supabase = createServiceClient()
    
    // Verificar se já existe perfil
    const { data: existingProfile, error: fetchError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', stackUser.id)
      .single()
    
    if (fetchError && fetchError.code !== 'PGRST116') { // PGRST116 = não encontrado
      console.error(`[SYNC-USER] ${timestamp} - ❌ Erro ao buscar perfil:`, fetchError)
      throw fetchError
    }
    
    const profileData = {
      id: stackUser.id,
      email: stackUser.primaryEmail,
      full_name: stackUser.displayName || stackUser.primaryEmail?.split('@')[0] || 'User',
      avatar_url: stackUser.profileImageUrl,
      // Preservar dados de assinatura se existirem
      subscription_tier: existingProfile?.subscription_tier || 'free',
      subscription_status: existingProfile?.subscription_status || 'inactive',
      subscription_current_period_end: existingProfile?.subscription_current_period_end,
      subscription_start_date: existingProfile?.subscription_start_date,
      stripe_customer_id: existingProfile?.stripe_customer_id,
      stripe_subscription_id: existingProfile?.stripe_subscription_id,
      updated_at: new Date().toISOString()
    }
    
    let result
    
    if (existingProfile) {
      console.log(`[SYNC-USER] ${timestamp} - Atualizando perfil existente...`)
      
      // Só atualizar campos básicos, preservar dados de assinatura
      const { data, error } = await supabase
        .from('profiles')
        .update({
          email: profileData.email,
          full_name: profileData.full_name,
          avatar_url: profileData.avatar_url,
          updated_at: profileData.updated_at
        })
        .eq('id', stackUser.id)
        .select()
        .single()
      
      if (error) {
        console.error(`[SYNC-USER] ${timestamp} - ❌ Erro ao atualizar perfil:`, error)
        throw error
      }
      
      result = data
      console.log(`[SYNC-USER] ${timestamp} - ✅ Perfil atualizado`)
    } else {
      console.log(`[SYNC-USER] ${timestamp} - Criando novo perfil...`)
      
      // Criar novo perfil
      const { data, error } = await supabase
        .from('profiles')
        .insert([profileData])
        .select()
        .single()
      
      if (error) {
        console.error(`[SYNC-USER] ${timestamp} - ❌ Erro ao criar perfil:`, error)
        throw error
      }
      
      result = data
      console.log(`[SYNC-USER] ${timestamp} - ✅ Perfil criado`)
    }
    
    // Criar/atualizar usuário no auth.users do Supabase também
    // Isso permite que as RLS policies funcionem corretamente
    console.log(`[SYNC-USER] ${timestamp} - Sincronizando com auth.users...`)
    
    // Verificar se existe no auth.users
    const { data: { users }, error: authListError } = await supabase.auth.admin.listUsers()
    
    if (!authListError) {
      const authUser = users.find(u => u.id === stackUser.id)
      
      if (!authUser && stackUser.primaryEmail) {
        // Criar usuário no auth.users
        console.log(`[SYNC-USER] ${timestamp} - Criando usuário no auth.users...`)
        
        const { data: newAuthUser, error: createError } = await supabase.auth.admin.createUser({
          email: stackUser.primaryEmail,
          email_confirm: true,
          user_metadata: {
            full_name: stackUser.displayName,
            avatar_url: stackUser.profileImageUrl,
            stack_user: true,
            synced_at: new Date().toISOString()
          }
        })
        
        if (createError) {
          console.error(`[SYNC-USER] ${timestamp} - ⚠️ Erro ao criar auth.user:`, createError)
          // Não falhar se não conseguir criar no auth.users
        } else {
          console.log(`[SYNC-USER] ${timestamp} - ✅ Usuário criado no auth.users`)
          
          // Atualizar o ID se for diferente
          if (newAuthUser.user && newAuthUser.user.id !== stackUser.id) {
            console.log(`[SYNC-USER] ${timestamp} - Atualizando ID do perfil...`)
            await supabase
              .from('profiles')
              .update({ id: newAuthUser.user.id })
              .eq('id', stackUser.id)
          }
        }
      } else if (authUser) {
        console.log(`[SYNC-USER] ${timestamp} - ✅ Usuário já existe no auth.users`)
      }
    }
    
    return {
      success: true,
      profile: result
    }
    
  } catch (error) {
    console.error(`[SYNC-USER] ${timestamp} - ❌ Erro geral na sincronização:`, error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

/**
 * Verifica se usuário tem premium ativo
 */
export async function checkUserPremiumStatus(userId: string): Promise<{
  hasPremium: boolean
  tier?: string
  expiresAt?: string
}> {
  const timestamp = new Date().toISOString()
  console.log(`[CHECK-PREMIUM] ${timestamp} - Checking premium for user:`, userId)
  
  try {
    const supabase = createServiceClient()
    
    // Buscar perfil
    const { data: profile, error } = await supabase
      .from('profiles')
      .select('subscription_tier, subscription_status, subscription_current_period_end')
      .eq('id', userId)
      .single()
    
    if (error) {
      console.error(`[CHECK-PREMIUM] ${timestamp} - ❌ Erro ao buscar perfil:`, error)
      return { hasPremium: false }
    }
    
    const now = new Date()
    const endDate = profile?.subscription_current_period_end 
      ? new Date(profile.subscription_current_period_end)
      : null
    
    const hasPremium = profile?.subscription_tier === 'premium' && 
                      profile?.subscription_status === 'active' &&
                      (!endDate || endDate > now)
    
    console.log(`[CHECK-PREMIUM] ${timestamp} - Premium status:`, {
      hasPremium,
      tier: profile?.subscription_tier,
      status: profile?.subscription_status,
      expiresAt: profile?.subscription_current_period_end
    })
    
    return {
      hasPremium,
      tier: profile?.subscription_tier,
      expiresAt: profile?.subscription_current_period_end
    }
    
  } catch (error) {
    console.error(`[CHECK-PREMIUM] ${timestamp} - ❌ Erro geral:`, error)
    return { hasPremium: false }
  }
}