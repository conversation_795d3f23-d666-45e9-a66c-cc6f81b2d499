"use client";

import { StackAuthProvider } from "./stack-client";
import { StackAuthSetup } from "@/components/auth/stack-auth-setup";

// Verificar se as variáveis estão configuradas
const isConfigured = !!(
  process.env.NEXT_PUBLIC_STACK_PROJECT_ID &&
  process.env.NEXT_PUBLIC_STACK_PUBLISHABLE_CLIENT_KEY &&
  process.env.NEXT_PUBLIC_STACK_PROJECT_ID !== "your-stack-project-id"
);

export function StackProviderWrapper({ 
  children
}: { 
  children: React.ReactNode;
}) {
  // Se não estiver configurado, mostrar instruções
  if (!isConfigured) {
    return <StackAuthSetup />;
  }

  return (
    <StackAuthProvider>
      {children}
    </StackAuthProvider>
  );
}