// Mapeamento de logos para canais conhecidos
// Quando um canal não tem logo na API IPTV-org, usamos estes logos

export const CHANNEL_LOGOS: Record<string, string> = {
  // ESPN
  'ESPNews.us': 'https://upload.wikimedia.org/wikipedia/commons/thumb/1/1b/ESPNews.svg/2560px-ESPNews.svg.png',
  'ESPN.us': 'https://upload.wikimedia.org/wikipedia/commons/thumb/2/2f/ESPN_wordmark.svg/2560px-ESPN_wordmark.svg.png',
  'ESPN2.us': 'https://upload.wikimedia.org/wikipedia/commons/thumb/b/bf/ESPN2_logo.svg/2560px-ESPN2_logo.svg.png',
  'ESPNU.us': 'https://upload.wikimedia.org/wikipedia/commons/thumb/4/4b/ESPNU_logo.svg/2560px-ESPNU_logo.svg.png',
  
  // Fox Sports
  'FoxSports1.us': 'https://upload.wikimedia.org/wikipedia/commons/thumb/3/3a/Fox_Sports_1_logo.svg/2560px-Fox_Sports_1_logo.svg.png',
  'FoxSports2.us': 'https://upload.wikimedia.org/wikipedia/commons/thumb/3/38/FS2_logo_2015.svg/2560px-FS2_logo_2015.svg.png',
  'FoxSoccer.us': 'https://upload.wikimedia.org/wikipedia/en/thumb/0/0c/Fox_Soccer_Plus_logo.svg/2560px-Fox_Soccer_Plus_logo.svg.png',
  
  // NBC Sports
  'NBCSports.us': 'https://upload.wikimedia.org/wikipedia/commons/thumb/f/f2/NBC_Sports_2012.svg/2560px-NBC_Sports_2012.svg.png',
  'NBCSportsChicago.us': 'https://upload.wikimedia.org/wikipedia/commons/thumb/8/87/NBC_Sports_Chicago_logo.svg/2560px-NBC_Sports_Chicago_logo.svg.png',
  'NBCSportsBoston.us': 'https://upload.wikimedia.org/wikipedia/commons/thumb/0/04/NBC_Sports_Boston_logo.svg/2560px-NBC_Sports_Boston_logo.svg.png',
  
  // Bally Sports
  'BallySportsNorth.us': 'https://upload.wikimedia.org/wikipedia/commons/thumb/1/13/Bally_Sports_North_2021.svg/2560px-Bally_Sports_North_2021.svg.png',
  'BallySportsSouth.us': 'https://upload.wikimedia.org/wikipedia/commons/thumb/0/0f/Bally_Sports_South_2021.svg/2560px-Bally_Sports_South_2021.svg.png',
  'BallySportsWest.us': 'https://upload.wikimedia.org/wikipedia/commons/thumb/8/88/Bally_Sports_West_logo.svg/2560px-Bally_Sports_West_logo.svg.png',
  
  // MLB Network
  'MLBNetwork.us': 'https://upload.wikimedia.org/wikipedia/commons/thumb/e/e2/MLB_Network_logo.svg/2560px-MLB_Network_logo.svg.png',
  
  // NBA TV
  'NBATV.us': 'https://upload.wikimedia.org/wikipedia/en/thumb/d/d2/NBA_TV.svg/2560px-NBA_TV.svg.png',
  
  // NFL
  'NFLNetwork.us': 'https://upload.wikimedia.org/wikipedia/en/thumb/8/8f/NFL_Network_logo.svg/2560px-NFL_Network_logo.svg.png',
  
  // Golf
  'GolfChannel.us': 'https://upload.wikimedia.org/wikipedia/commons/thumb/0/08/Golf_Channel_Logo.png/2560px-Golf_Channel_Logo.png',
  '30AGolfKingdom.us': 'https://upload.wikimedia.org/wikipedia/commons/thumb/0/08/Golf_Channel_Logo.png/1280px-Golf_Channel_Logo.png',
  
  // Tennis
  'TennisChannel.us': 'https://upload.wikimedia.org/wikipedia/commons/thumb/8/8c/Tennis_Channel_logo.svg/2560px-Tennis_Channel_logo.svg.png',
  'TennisChannelPlus1.us': 'https://upload.wikimedia.org/wikipedia/commons/thumb/8/8c/Tennis_Channel_logo.svg/2560px-Tennis_Channel_logo.svg.png',
  'TennisChannelPlus2.us': 'https://upload.wikimedia.org/wikipedia/commons/thumb/8/8c/Tennis_Channel_logo.svg/2560px-Tennis_Channel_logo.svg.png',
  
  // Racing
  'MotorvisionTV.us': 'https://www.motorvision.tv/img/motorvision_logo.png',
  'MavTV.us': 'https://upload.wikimedia.org/wikipedia/commons/thumb/1/16/Mavtv_2018_logo.png/1280px-Mavtv_2018_logo.png',
  
  // International Sports
  'SkySportsFootball.uk': 'https://upload.wikimedia.org/wikipedia/en/thumb/c/c9/Sky_Sports_Football_SD.svg/2560px-Sky_Sports_Football_SD.svg.png',
  'SkySportsPremierLeague.uk': 'https://upload.wikimedia.org/wikipedia/en/thumb/d/d5/Sky_Sports_Premier_League_SD.svg/2560px-Sky_Sports_Premier_League_SD.svg.png',
  'SkySportsMainEvent.uk': 'https://upload.wikimedia.org/wikipedia/en/thumb/4/4f/Sky_Sports_Main_Event_SD.png/2560px-Sky_Sports_Main_Event_SD.png',
  'SkySportsArena.uk': 'https://upload.wikimedia.org/wikipedia/en/thumb/7/79/Sky_Sports_Arena_SD.svg/2560px-Sky_Sports_Arena_SD.svg.png',
  
  // BeIN Sports
  'beINSportsEnglish.us': 'https://upload.wikimedia.org/wikipedia/commons/thumb/e/e5/Logo_bein_sports_2014.png/2560px-Logo_bein_sports_2014.png',
  'beINSportsSpanish.us': 'https://upload.wikimedia.org/wikipedia/commons/thumb/e/e5/Logo_bein_sports_2014.png/2560px-Logo_bein_sports_2014.png',
  
  // TUDN
  'TUDN.mx': 'https://upload.wikimedia.org/wikipedia/commons/thumb/6/6c/TUDN_Logo.svg/2560px-TUDN_Logo.svg.png',
  'TUDN.us': 'https://upload.wikimedia.org/wikipedia/commons/thumb/6/6c/TUDN_Logo.svg/2560px-TUDN_Logo.svg.png',
  
  // Univision
  'Univision.us': 'https://upload.wikimedia.org/wikipedia/commons/thumb/a/af/Logo_Univision_2019.svg/2560px-Logo_Univision_2019.svg.png',
  'UnivisionTDN.us': 'https://upload.wikimedia.org/wikipedia/commons/thumb/6/6c/TUDN_Logo.svg/2560px-TUDN_Logo.svg.png',
  
  // CBS Sports
  'CBSSportsNetwork.us': 'https://upload.wikimedia.org/wikipedia/en/thumb/3/30/CBS_Sports_Network_logo.png/2560px-CBS_Sports_Network_logo.png',
  
  // Stadium
  'Stadium.us': 'https://upload.wikimedia.org/wikipedia/en/thumb/2/2f/Stadium_logo.svg/2560px-Stadium_logo.svg.png',
  
  // Pac-12
  'Pac12Arizona.us': 'https://upload.wikimedia.org/wikipedia/commons/thumb/f/f9/Pac-12_logo.svg/2560px-Pac-12_logo.svg.png',
  'Pac12BayArea.us': 'https://upload.wikimedia.org/wikipedia/commons/thumb/f/f9/Pac-12_logo.svg/2560px-Pac-12_logo.svg.png',
  'Pac12LosAngeles.us': 'https://upload.wikimedia.org/wikipedia/commons/thumb/f/f9/Pac-12_logo.svg/2560px-Pac-12_logo.svg.png',
  'Pac12Mountain.us': 'https://upload.wikimedia.org/wikipedia/commons/thumb/f/f9/Pac-12_logo.svg/2560px-Pac-12_logo.svg.png',
  'Pac12Oregon.us': 'https://upload.wikimedia.org/wikipedia/commons/thumb/f/f9/Pac-12_logo.svg/2560px-Pac-12_logo.svg.png',
  'Pac12Washington.us': 'https://upload.wikimedia.org/wikipedia/commons/thumb/f/f9/Pac-12_logo.svg/2560px-Pac-12_logo.svg.png',
  
  // Yes Network
  'YESNetwork.us': 'https://upload.wikimedia.org/wikipedia/commons/thumb/f/f0/YES_Network_logo.svg/2560px-YES_Network_logo.svg.png',
  
  // MSG
  'MSGNetwork.us': 'https://upload.wikimedia.org/wikipedia/commons/thumb/d/de/MSG_Network.svg/2560px-MSG_Network.svg.png',
  'MSGPlus.us': 'https://upload.wikimedia.org/wikipedia/commons/thumb/6/6f/MSG_Plus_2023.svg/2560px-MSG_Plus_2023.svg.png',
  
  // Sportsnet
  'SportsnetOne.ca': 'https://upload.wikimedia.org/wikipedia/en/thumb/9/90/Sportsnet_One_logo.svg/2560px-Sportsnet_One_logo.svg.png',
  
  // TSN
  'TSN1.ca': 'https://upload.wikimedia.org/wikipedia/commons/thumb/3/39/TSN_Logo.svg/2560px-TSN_Logo.svg.png',
  'TSN2.ca': 'https://upload.wikimedia.org/wikipedia/commons/thumb/3/39/TSN_Logo.svg/2560px-TSN_Logo.svg.png',
  'TSN3.ca': 'https://upload.wikimedia.org/wikipedia/commons/thumb/3/39/TSN_Logo.svg/2560px-TSN_Logo.svg.png',
  'TSN4.ca': 'https://upload.wikimedia.org/wikipedia/commons/thumb/3/39/TSN_Logo.svg/2560px-TSN_Logo.svg.png',
  'TSN5.ca': 'https://upload.wikimedia.org/wikipedia/commons/thumb/3/39/TSN_Logo.svg/2560px-TSN_Logo.svg.png',
  
  // SEC Network
  'SECNetwork.us': 'https://upload.wikimedia.org/wikipedia/commons/thumb/b/b9/SEC_Network_logo.svg/2560px-SEC_Network_logo.svg.png',
  
  // Big Ten Network
  'BigTenNetwork.us': 'https://upload.wikimedia.org/wikipedia/commons/thumb/f/fc/Big_Ten_Network_logo.svg/2560px-Big_Ten_Network_logo.svg.png',
  
  // ACC Network
  'ACCNetwork.us': 'https://upload.wikimedia.org/wikipedia/commons/thumb/1/13/ACC_Network_logo.svg/2560px-ACC_Network_logo.svg.png',
  
  // Olympic Channel
  'OlympicChannel.us': 'https://upload.wikimedia.org/wikipedia/commons/thumb/a/a4/Olympic_Channel_logo.png/2560px-Olympic_Channel_logo.png',
  
  // RedBull TV
  'RedBullTV.us': 'https://upload.wikimedia.org/wikipedia/commons/thumb/0/01/Red_Bull_TV_logo_%282016%29.svg/2560px-Red_Bull_TV_logo_%282016%29.svg.png',
  'RedBullTV.at': 'https://upload.wikimedia.org/wikipedia/commons/thumb/0/01/Red_Bull_TV_logo_%282016%29.svg/2560px-Red_Bull_TV_logo_%282016%29.svg.png',
  
  // Fight Network
  'FightNetwork.ca': 'https://upload.wikimedia.org/wikipedia/en/thumb/d/d6/Fight_Network_logo.svg/2560px-Fight_Network_logo.svg.png',
  
  // ATT SportsNet
  'ATTSportsNetPittsburgh.us': 'https://upload.wikimedia.org/wikipedia/commons/thumb/c/ca/AT%26T_SportsNet_logo.svg/2560px-AT%26T_SportsNet_logo.svg.png',
  
  // TVG
  'TVG.us': 'https://upload.wikimedia.org/wikipedia/commons/thumb/b/b4/TVG_logo.svg/2560px-TVG_logo.svg.png',
  'TVG2.us': 'https://upload.wikimedia.org/wikipedia/commons/thumb/b/b4/TVG_logo.svg/2560px-TVG_logo.svg.png',
  
  // ACC Digital Network
  'ACCDigitalNetwork.us': 'https://upload.wikimedia.org/wikipedia/commons/thumb/1/13/ACC_Network_logo.svg/2560px-ACC_Network_logo.svg.png',
  
  // International Channels
  'ADOTV.bj': 'https://i.imgur.com/1NCoGlk.png',
  'Adjarasport1.ge': 'https://i.imgur.com/mcgbRJJ.png',
  'Africa24Sport.fr': 'https://i.imgur.com/4fP5P4y.png',
  'AfroSportNigeria.ng': 'https://i.imgur.com/HzvjQkP.png',
  'AntenaSport.ro': 'https://i.imgur.com/5V6VBtc.png',
  'AntenaSport.hr': 'https://i.imgur.com/5V6VBtc.png',
  'Arryadia.ma': 'https://i.imgur.com/XjzK7gZ.png',
  'AstrahanRuSport.ru': 'https://i.imgur.com/P1r9QNa.png',
  
  // Bahrain Sports
  'BahrainSports1.bh': 'https://www.lyngsat.com/logo/tv/bb/bahrain_sports_1.png',
  'BahrainSports2.bh': 'https://www.lyngsat.com/logo/tv/bb/bahrain_sports_2.png',
  
  // Belarus
  'Belarus5.by': 'https://upload.wikimedia.org/wikipedia/commons/thumb/0/0e/Belarus_5_logo.svg/200px-Belarus_5_logo.svg.png',
  
  // MMA/Fighting
  'BellatorMMA.us': 'https://upload.wikimedia.org/wikipedia/en/thumb/c/c3/Bellator_MMA_logo.svg/2560px-Bellator_MMA_logo.svg.png',
  'FITE247.us': 'https://www.fite.tv/images/fite-logo.svg',
  'GloryKickboxing.us': 'https://upload.wikimedia.org/wikipedia/en/thumb/e/e0/Glory_kickboxing_logo.svg/2560px-Glory_kickboxing_logo.svg.png',
  
  // Billiards
  'BilliardTV.us': 'https://www.billiardtv.com/images/logo.png',
  
  // CBS Sports
  'CBSSportsNetworkUSA.us': 'https://upload.wikimedia.org/wikipedia/en/thumb/3/30/CBS_Sports_Network_logo.png/2560px-CBS_Sports_Network_logo.png',
  
  // Czech
  'CTSport.cz': 'https://upload.wikimedia.org/wikipedia/commons/thumb/d/de/CT_sport_logo_2012.png/200px-CT_sport_logo_2012.png',
  
  // Brazilian
  'CanaldoInter.br': 'https://upload.wikimedia.org/wikipedia/commons/thumb/2/2b/Internazionale.svg/200px-Internazionale.svg.png',
  
  // Vietnamese
  'DongNaiTV2.vn': 'https://upload.wikimedia.org/wikipedia/vi/6/69/DNTV2_logo.png',
  'ONFootball.vn': 'https://upload.wikimedia.org/wikipedia/commons/thumb/b/be/ON_Football_logo.svg/200px-ON_Football_logo.svg.png',
  
  // Dubai
  'DubaiRacing.ae': 'https://www.lyngsat.com/logo/tv/dd/dubai-racing-ae.png',
  'DubaiRacing2.ae': 'https://www.lyngsat.com/logo/tv/dd/dubai-racing-2-ae.png',
  'DubaiRacing3.ae': 'https://www.lyngsat.com/logo/tv/dd/dubai-racing-3-ae.png',
  'DubaiSports1.ae': 'https://www.lyngsat.com/logo/tv/dd/dubai_sports.png',
  'DubaiSports2.ae': 'https://www.lyngsat.com/logo/tv/dd/dubai_sports_2.png',
  'DubaiSports3.ae': 'https://www.lyngsat.com/logo/tv/dd/dubai_sports_3.png',
  
  // Eleven Sports
  'ElevenSports1.pl': 'https://upload.wikimedia.org/wikipedia/commons/thumb/f/f6/Eleven_Sports_2018_logo.png/200px-Eleven_Sports_2018_logo.png',
  
  // FIFA
  'FIFAPlus.pl': 'https://upload.wikimedia.org/wikipedia/commons/thumb/7/73/FIFA%2B_logo.svg/200px-FIFA%2B_logo.svg.png',
  
  // FanDuel
  'FanDuelSportsNetwork.us': 'https://upload.wikimedia.org/wikipedia/commons/thumb/4/4f/FanDuel_Sports_Network_logo.svg/200px-FanDuel_Sports_Network_logo.svg.png',
  'FanDuelTV.us': 'https://upload.wikimedia.org/wikipedia/commons/thumb/e/e4/FanDuel_logo.svg/200px-FanDuel_logo.svg.png',
  
  // Iran
  'IRIB3.ir': 'https://upload.wikimedia.org/wikipedia/commons/thumb/2/25/IRIB_TV3_Logo.svg/200px-IRIB_TV3_Logo.svg.png',
  
  // Mexico
  'ITVDeportes.mx': 'https://upload.wikimedia.org/wikipedia/commons/thumb/e/e3/ITV_Deportes_logo.png/200px-ITV_Deportes_logo.png',
  
  // Insight TV
  'InsightTV.nl': 'https://upload.wikimedia.org/wikipedia/commons/thumb/7/79/Insight_TV_logo.svg/200px-Insight_TV_logo.svg.png',
  
  // Inter Milan
  'InterTV.it': 'https://upload.wikimedia.org/wikipedia/commons/thumb/0/05/FC_Internazionale_Milano_2021.svg/200px-FC_Internazionale_Milano_2021.svg.png',
  
  // Austria
  'K19.at': 'https://www.lyngsat.com/logo/tv/kk/k19-at.png',
  
  // Russia
  'KHLPrime.ru': 'https://upload.wikimedia.org/wikipedia/en/thumb/9/9f/Kontinental_Hockey_League_logo.svg/200px-Kontinental_Hockey_League_logo.svg.png',
  'MatchArena.ru': 'https://www.lyngsat.com/logo/tv/mm/match-arena-ru.png',
  
  // Saudi Arabia
  'KSASports1.sa': 'https://www.lyngsat.com/logo/tv/kk/ksa-sports-1-sa.png',
  
  // Lacrosse
  'LacrosseTV.us': 'https://www.lyngsat.com/logo/tv/ll/lacrosse-tv-us.png',
  
  // MAVTV
  'MAVTVSelect.us': 'https://upload.wikimedia.org/wikipedia/commons/thumb/1/16/Mavtv_2018_logo.png/200px-Mavtv_2018_logo.png',
  
  // MSG
  'MSG.us': 'https://upload.wikimedia.org/wikipedia/commons/thumb/d/de/MSG_Network.svg/2560px-MSG_Network.svg.png',
  
  // Italy
  'MadeinBOTV.it': 'https://www.lyngsat.com/logo/tv/mm/made-in-bo-tv-it.png',
  'TRSport.it': 'https://www.lyngsat.com/logo/tv/tt/tr-sport-it.png',
  
  // Germany
  'MoreThanSportsTV.de': 'https://www.lyngsat.com/logo/tv/mm/more-than-sports-tv-de.png',
  
  // NHL
  'NHLNetwork.us': 'https://upload.wikimedia.org/wikipedia/commons/thumb/3/3a/NHL_Network_2012.svg/200px-NHL_Network_2012.svg.png',
  
  // Nitro Circus
  'NitroCircus.us': 'https://upload.wikimedia.org/wikipedia/en/8/82/Nitro_circus_logo.jpg',
  
  // Racing
  'RacingAmerica.us': 'https://www.lyngsat.com/logo/tv/rr/racing-america-us.png',
  
  // Serbia
  'SOSKanalPlus.rs': 'https://www.lyngsat.com/logo/tv/ss/sos-kanal-plus-rs.png',
  
  // Sports Grid
  'SportsGrid.us': 'https://upload.wikimedia.org/wikipedia/commons/thumb/1/10/SportsGrid_logo.svg/200px-SportsGrid_logo.svg.png',
  
  // SNY
  'SportsNetNewYork.us': 'https://upload.wikimedia.org/wikipedia/commons/thumb/f/f8/SNY_logo.svg/200px-SNY_logo.svg.png',
  
  // Sportsman Channel
  'SportsmanChannel.us': 'https://upload.wikimedia.org/wikipedia/en/0/09/Sportsman_Channel_logo.png',
  
  // Star Sports Tamil
  'StarSports1Tamil.in': 'https://upload.wikimedia.org/wikipedia/commons/thumb/5/52/Star_Sports_India_logo.png/200px-Star_Sports_India_logo.png',
  
  // Swerve
  'SwerveSports.us': 'https://www.lyngsat.com/logo/tv/ss/swerve-sports-us.png',
  
  // Macau
  'TDMSports.mo': 'https://www.lyngsat.com/logo/tv/tt/tdm-ou-mun-mo.png',
  
  // TSN The Ocho
  'TSNTheOcho.ca': 'https://upload.wikimedia.org/wikipedia/commons/thumb/3/39/TSN_Logo.svg/200px-TSN_Logo.svg.png',
  
  // Trace Sports
  'TraceSportStars.fr': 'https://upload.wikimedia.org/wikipedia/commons/thumb/f/fa/TraceSportsStarsLogo2019.png/200px-TraceSportsStarsLogo2019.png',
  
  // talkSPORT
  'talkSPORT.uk': 'https://upload.wikimedia.org/wikipedia/en/thumb/b/b7/TalkSPORT_logo_2024.svg/200px-TalkSPORT_logo_2024.svg.png',
  
  // NFL RedZone
  'NFLRedZone.us': 'https://upload.wikimedia.org/wikipedia/commons/thumb/3/36/NFL_RedZone_logo.svg/200px-NFL_RedZone_logo.svg.png'
}

// Função para obter logo de um canal
export function getChannelLogo(channelId: string, apiLogo?: string): string {
  // Debug para verificar o que está sendo passado
  if (channelId === '30AGolfKingdom.us' || channelId === 'ESPNews.us') {
    console.log(`[CHANNEL-LOGOS] Getting logo for ${channelId}, apiLogo:`, apiLogo)
  }
  
  // Se a API já forneceu um logo válido, use-o
  if (apiLogo && apiLogo.startsWith('http')) {
    return apiLogo
  }
  
  // Caso contrário, procure no mapeamento
  const mappedLogo = CHANNEL_LOGOS[channelId]
  if (mappedLogo) {
    console.log(`[CHANNEL-LOGOS] Found mapped logo for ${channelId}`)
    return mappedLogo
  }
  
  // Se ainda não tiver logo, retorne um placeholder baseado no tipo de canal
  const channelLower = channelId.toLowerCase()
  
  if (channelLower.includes('espn')) {
    return 'https://upload.wikimedia.org/wikipedia/commons/thumb/2/2f/ESPN_wordmark.svg/2560px-ESPN_wordmark.svg.png'
  }
  
  if (channelLower.includes('fox')) {
    return 'https://upload.wikimedia.org/wikipedia/commons/thumb/3/3a/Fox_Sports_1_logo.svg/2560px-Fox_Sports_1_logo.svg.png'
  }
  
  if (channelLower.includes('nbc')) {
    return 'https://upload.wikimedia.org/wikipedia/commons/thumb/f/f2/NBC_Sports_2012.svg/2560px-NBC_Sports_2012.svg.png'
  }
  
  if (channelLower.includes('sky')) {
    return 'https://upload.wikimedia.org/wikipedia/en/thumb/c/c9/Sky_Sports_Football_SD.svg/2560px-Sky_Sports_Football_SD.svg.png'
  }
  
  if (channelLower.includes('tsn')) {
    return 'https://upload.wikimedia.org/wikipedia/commons/thumb/3/39/TSN_Logo.svg/2560px-TSN_Logo.svg.png'
  }
  
  if (channelLower.includes('bein')) {
    return 'https://upload.wikimedia.org/wikipedia/commons/thumb/e/e5/Logo_bein_sports_2014.png/2560px-Logo_bein_sports_2014.png'
  }
  
  if (channelLower.includes('dazn')) {
    return 'https://upload.wikimedia.org/wikipedia/commons/thumb/a/a2/DAZN_logo.svg/2560px-DAZN_logo.svg.png'
  }
  
  // Logo genérico de esportes se nada mais funcionar
  console.log(`[CHANNEL-LOGOS] No logo found for ${channelId}, using generic sports image`)
  
  // Usar um logo de rede de TV genérico ao invés de imagem de esporte
  return 'https://upload.wikimedia.org/wikipedia/commons/thumb/a/ac/No_image_available.svg/600px-No_image_available.svg.png'
}