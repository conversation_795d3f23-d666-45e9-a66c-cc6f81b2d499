// Sistema de Rate Limiting para proteção contra força bruta
// Usa Map em memória para desenvolvimento, migrar para Redis em produção

type RateLimitOptions = {
  uniqueTokenPerInterval?: number;
  interval?: number; // em milissegundos
}

type RateLimitStore = {
  count: number;
  resetTime: number;
}

class RateLimiter {
  private store: Map<string, RateLimitStore>;
  private readonly maxRequests: number;
  private readonly windowMs: number;

  constructor(options: RateLimitOptions = {}) {
    this.store = new Map();
    this.maxRequests = options.uniqueTokenPerInterval || 5;
    this.windowMs = options.interval || 60000; // 1 minuto padrão
    
    // Limpar entradas expiradas a cada 5 minutos
    setInterval(() => this.cleanup(), 5 * 60 * 1000);
  }

  async check(identifier: string, limit?: number): Promise<{ success: boolean; remaining: number; resetAt: Date }> {
    const now = Date.now();
    const maxAllowed = limit || this.maxRequests;
    
    let record = this.store.get(identifier);
    
    // Se não existe registro ou expirou, criar novo
    if (!record || now > record.resetTime) {
      record = {
        count: 0,
        resetTime: now + this.windowMs
      };
    }
    
    record.count++;
    this.store.set(identifier, record);
    
    const remaining = Math.max(0, maxAllowed - record.count);
    const success = record.count <= maxAllowed;
    
    return {
      success,
      remaining,
      resetAt: new Date(record.resetTime)
    };
  }

  reset(identifier: string): void {
    this.store.delete(identifier);
  }

  private cleanup(): void {
    const now = Date.now();
    for (const [key, value] of this.store.entries()) {
      if (now > value.resetTime) {
        this.store.delete(key);
      }
    }
  }
}

// Instâncias específicas para diferentes tipos de limite
export const loginRateLimiter = new RateLimiter({
  uniqueTokenPerInterval: 5, // 5 tentativas
  interval: 15 * 60 * 1000   // por 15 minutos
});

export const apiRateLimiter = new RateLimiter({
  uniqueTokenPerInterval: 100, // 100 requisições
  interval: 60 * 1000         // por minuto
});

export const passwordResetRateLimiter = new RateLimiter({
  uniqueTokenPerInterval: 3,   // 3 tentativas
  interval: 60 * 60 * 1000    // por hora
});

// Função helper para usar em API routes
export async function checkRateLimit(
  request: Request,
  limiter: RateLimiter = apiRateLimiter,
  customLimit?: number
): Promise<{ success: boolean; headers: Headers }> {
  const identifier = request.headers.get('x-forwarded-for') || 
                    request.headers.get('x-real-ip') || 
                    'anonymous';
  
  const result = await limiter.check(identifier, customLimit);
  
  const headers = new Headers();
  headers.set('X-RateLimit-Limit', String(customLimit || 100));
  headers.set('X-RateLimit-Remaining', String(result.remaining));
  headers.set('X-RateLimit-Reset', result.resetAt.toISOString());
  
  if (!result.success) {
    headers.set('Retry-After', String(Math.ceil((result.resetAt.getTime() - Date.now()) / 1000)));
  }
  
  return { success: result.success, headers };
}