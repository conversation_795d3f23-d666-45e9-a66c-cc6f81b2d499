// Sistema de Auditoria e Logs de Segurança
import { EventEmitter } from 'events';

export type AuditAction = 
  | 'login'
  | 'logout'
  | 'login_failed'
  | 'password_reset'
  | 'password_changed'
  | 'profile_updated'
  | 'access_denied'
  | 'data_accessed'
  | 'data_created'
  | 'data_updated'
  | 'data_deleted'
  | 'suspicious_activity'
  | 'rate_limit_exceeded';

export interface AuditLog {
  id?: string;
  userId: string;
  action: AuditAction;
  path?: string;
  method?: string;
  data?: any;
  ipAddress?: string;
  userAgent?: string;
  timestamp: Date;
  severity: 'info' | 'warning' | 'error' | 'critical';
  metadata?: Record<string, any>;
}

// Event emitter para processar logs de forma assíncrona
class AuditLogger extends EventEmitter {
  private queue: AuditLog[] = [];
  private isProcessing = false;
  
  constructor() {
    super();
    
    // Processar fila a cada 5 segundos
    setInterval(() => this.processQueue(), 5000);
    
    // Listener para logs críticos
    this.on('critical', (log) => {
      this.immediateSave(log);
    });
  }
  
  /**
   * Registra um log de auditoria
   */
  async log(log: Omit<AuditLog, 'timestamp'>): Promise<void> {
    const auditLog: AuditLog = {
      ...log,
      timestamp: new Date(),
      id: this.generateId(),
    };
    
    // Emitir evento para processamento em tempo real se crítico
    if (auditLog.severity === 'critical') {
      this.emit('critical', auditLog);
    }
    
    // Adicionar à fila para processamento em batch
    this.queue.push(auditLog);
    
    // Log no console em desenvolvimento
    if (process.env.NODE_ENV === 'development') {
      console.log(`[AUDIT] ${auditLog.severity.toUpperCase()}:`, {
        action: auditLog.action,
        userId: auditLog.userId,
        path: auditLog.path,
        ip: auditLog.ipAddress,
      });
    }
  }
  
  /**
   * Salva log imediatamente (para eventos críticos)
   */
  private async immediateSave(log: AuditLog): Promise<void> {
    try {
      // TODO: Implementar salvamento no banco quando tivermos conexão
      // await sql`
      //   INSERT INTO audit_logs (id, user_id, action, path, method, data, ip_address, user_agent, timestamp, severity, metadata)
      //   VALUES (${log.id}, ${log.userId}, ${log.action}, ${log.path}, ${log.method}, ${JSON.stringify(log.data)}, 
      //           ${log.ipAddress}, ${log.userAgent}, ${log.timestamp}, ${log.severity}, ${JSON.stringify(log.metadata)})
      // `;
      
      // Por enquanto, apenas log
      console.error(`[CRITICAL AUDIT LOG]`, log);
    } catch (error) {
      console.error('Erro ao salvar log crítico:', error);
    }
  }
  
  /**
   * Processa fila de logs em batch
   */
  private async processQueue(): Promise<void> {
    if (this.isProcessing || this.queue.length === 0) return;
    
    this.isProcessing = true;
    const logsToProcess = [...this.queue];
    this.queue = [];
    
    try {
      // TODO: Implementar salvamento em batch no banco
      // await sql`
      //   INSERT INTO audit_logs (id, user_id, action, path, method, data, ip_address, user_agent, timestamp, severity, metadata)
      //   SELECT * FROM ${sql.values(logsToProcess.map(log => [
      //     log.id, log.userId, log.action, log.path, log.method, JSON.stringify(log.data),
      //     log.ipAddress, log.userAgent, log.timestamp, log.severity, JSON.stringify(log.metadata)
      //   ]))}
      // `;
      
      // Por enquanto, salvar em arquivo local
      if (process.env.NODE_ENV === 'development') {
        const fs = await import('fs/promises');
        const logsPath = './logs/audit.log';
        
        try {
          await fs.mkdir('./logs', { recursive: true });
          const logLines = logsToProcess.map(log => JSON.stringify(log)).join('\n') + '\n';
          await fs.appendFile(logsPath, logLines);
        } catch (error) {
          console.error('Erro ao salvar logs em arquivo:', error);
        }
      }
    } catch (error) {
      console.error('Erro ao processar fila de logs:', error);
      // Re-adicionar logs à fila em caso de erro
      this.queue.unshift(...logsToProcess);
    } finally {
      this.isProcessing = false;
    }
  }
  
  /**
   * Gera ID único para o log
   */
  private generateId(): string {
    return `audit_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

// Instância singleton do logger
export const auditLogger = new AuditLogger();

/**
 * Helper functions para logging
 */
export const audit = {
  login: (userId: string, ipAddress?: string, metadata?: any) => {
    auditLogger.log({
      userId,
      action: 'login',
      ipAddress,
      severity: 'info',
      metadata,
    });
  },
  
  loginFailed: (email: string, ipAddress?: string, reason?: string) => {
    auditLogger.log({
      userId: email,
      action: 'login_failed',
      ipAddress,
      severity: 'warning',
      metadata: { reason },
    });
  },
  
  logout: (userId: string) => {
    auditLogger.log({
      userId,
      action: 'logout',
      severity: 'info',
    });
  },
  
  accessDenied: (userId: string, path: string, ipAddress?: string) => {
    auditLogger.log({
      userId,
      action: 'access_denied',
      path,
      ipAddress,
      severity: 'warning',
    });
  },
  
  dataAccess: (userId: string, resource: string, method: string = 'GET') => {
    auditLogger.log({
      userId,
      action: 'data_accessed',
      path: resource,
      method,
      severity: 'info',
    });
  },
  
  dataModification: (
    userId: string, 
    action: 'data_created' | 'data_updated' | 'data_deleted',
    resource: string,
    data?: any
  ) => {
    auditLogger.log({
      userId,
      action,
      path: resource,
      data,
      severity: action === 'data_deleted' ? 'warning' : 'info',
    });
  },
  
  suspiciousActivity: (userId: string, description: string, metadata?: any) => {
    auditLogger.log({
      userId,
      action: 'suspicious_activity',
      severity: 'critical',
      metadata: { description, ...metadata },
    });
  },
  
  rateLimitExceeded: (identifier: string, endpoint: string, ipAddress?: string) => {
    auditLogger.log({
      userId: identifier,
      action: 'rate_limit_exceeded',
      path: endpoint,
      ipAddress,
      severity: 'warning',
    });
  },
};

/**
 * Middleware helper para logging automático em APIs
 */
export function withAuditLogging(
  handler: (req: Request) => Promise<Response>,
  options?: { requireAuth?: boolean }
) {
  return async (req: Request): Promise<Response> => {
    const startTime = Date.now();
    const { pathname } = new URL(req.url);
    
    try {
      const response = await handler(req);
      
      // Log de sucesso
      if (options?.requireAuth) {
        // TODO: Pegar userId do contexto de autenticação
        audit.dataAccess('system', pathname, req.method);
      }
      
      return response;
    } catch (error) {
      // Log de erro
      console.error(`API Error at ${pathname}:`, error);
      
      // Retornar resposta de erro
      return new Response(
        JSON.stringify({ error: 'Internal server error' }),
        { 
          status: 500,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    } finally {
      const duration = Date.now() - startTime;
      if (duration > 1000) {
        console.warn(`Slow API call: ${pathname} took ${duration}ms`);
      }
    }
  };
}