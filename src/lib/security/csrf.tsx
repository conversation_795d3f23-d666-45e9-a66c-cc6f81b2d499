import { randomBytes } from 'crypto';

// Store de tokens CSRF - Em produção, usar Redis ou banco de dados
const csrfTokenStore = new Map<string, { expiry: number; userId?: string }>();

// Configurações
const TOKEN_LENGTH = 32;
const TOKEN_EXPIRY_MS = 60 * 60 * 1000; // 1 hora

// Limpar tokens expirados a cada 10 minutos
setInterval(() => {
  const now = Date.now();
  for (const [token, data] of csrfTokenStore.entries()) {
    if (now > data.expiry) {
      csrfTokenStore.delete(token);
    }
  }
}, 10 * 60 * 1000);

/**
 * Gera um novo token CSRF
 * @param userId - ID do usuário (opcional)
 * @returns Token CSRF em formato hex
 */
export function generateCSRFToken(userId?: string): string {
  const token = randomBytes(TOKEN_LENGTH).toString('hex');
  const expiry = Date.now() + TOKEN_EXPIRY_MS;
  
  csrfTokenStore.set(token, { expiry, userId });
  
  return token;
}

/**
 * Valida um token CSRF
 * @param token - Token a ser validado
 * @param userId - ID do usuário para validação adicional (opcional)
 * @returns true se válido, false caso contrário
 */
export function validateCSRFToken(token: string, userId?: string): boolean {
  if (!token) return false;
  
  const tokenData = csrfTokenStore.get(token);
  
  if (!tokenData) return false;
  
  // Verificar se expirou
  if (Date.now() > tokenData.expiry) {
    csrfTokenStore.delete(token);
    return false;
  }
  
  // Se userId foi fornecido, verificar se corresponde
  if (userId && tokenData.userId && tokenData.userId !== userId) {
    return false;
  }
  
  // Token é de uso único - remover após validação
  csrfTokenStore.delete(token);
  
  return true;
}

/**
 * Componente React para incluir token CSRF em formulários
 */
export function CSRFTokenInput({ userId }: { userId?: string }) {
  const token = generateCSRFToken(userId);
  
  return (
    <input 
      type="hidden" 
      name="csrf_token" 
      value={token}
      readOnly
    />
  );
}

/**
 * Hook para usar em formulários client-side
 */
export function useCSRFToken(userId?: string): string {
  // Em produção, fazer fetch do token do servidor
  return generateCSRFToken(userId);
}

/**
 * Middleware helper para validar CSRF em API routes
 */
export async function validateCSRFMiddleware(
  request: Request,
  userId?: string
): Promise<boolean> {
  // Pular validação CSRF para métodos seguros
  if (['GET', 'HEAD', 'OPTIONS'].includes(request.method)) {
    return true;
  }
  
  let token: string | null = null;
  
  // Tentar pegar token do header
  token = request.headers.get('X-CSRF-Token');
  
  // Se não estiver no header, tentar pegar do body
  if (!token && request.headers.get('content-type')?.includes('application/json')) {
    try {
      const body = await request.clone().json();
      token = body.csrf_token;
    } catch {
      // Ignorar erro de parse
    }
  }
  
  // Se não estiver no body, tentar pegar de form data
  if (!token && request.headers.get('content-type')?.includes('form-data')) {
    try {
      const formData = await request.clone().formData();
      token = formData.get('csrf_token') as string;
    } catch {
      // Ignorar erro de parse
    }
  }
  
  if (!token) return false;
  
  return validateCSRFToken(token, userId);
}