/**
 * Verificador de variáveis de ambiente e IAM
 */

import { logger } from './logger';
import { LOG_TAGS } from '@/config/debug';

export function checkEnvironment() {
  logger.info(LOG_TAGS.ENV, 'Environment check started');

  const envVars = {
    // Ambiente
    NODE_ENV: process.env.NODE_ENV,
    NEXT_PUBLIC_DEBUG: process.env.NEXT_PUBLIC_DEBUG,
    
    // URLs
    NEXT_PUBLIC_APP_URL: process.env.NEXT_PUBLIC_APP_URL,
    NEXT_PUBLIC_STREAM_URL: !!process.env.NEXT_PUBLIC_STREAM_URL,
    NEXT_PUBLIC_FALLBACK_STREAM_URL: !!process.env.NEXT_PUBLIC_FALLBACK_STREAM_URL,
    
    // Supabase
    NEXT_PUBLIC_SUPABASE_URL: !!process.env.NEXT_PUBLIC_SUPABASE_URL,
    NEXT_PUBLIC_SUPABASE_ANON_KEY: !!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
    SUPABASE_SERVICE_ROLE_KEY: !!process.env.SUPABASE_SERVICE_ROLE_KEY,
    
    // Auth
    JWT_SECRET: !!process.env.JWT_SECRET,
    NEXT_PUBLIC_STACK_PROJECT_ID: !!process.env.NEXT_PUBLIC_STACK_PROJECT_ID,
    STACK_SECRET_SERVER_KEY: !!process.env.STACK_SECRET_SERVER_KEY,
    
    // APIs
    SPORTS_API_KEY: !!process.env.SPORTS_API_KEY,
    ALLSPORT_DB_KEY: !!process.env.ALLSPORT_DB_KEY,
    ALLSPORTDB_API_KEY: !!process.env.ALLSPORTDB_API_KEY,
    
    // Cloud IAM (se aplicável)
    GOOGLE_APPLICATION_CREDENTIALS: !!process.env.GOOGLE_APPLICATION_CREDENTIALS,
    AWS_PROFILE: process.env.AWS_PROFILE || null,
    AWS_REGION: process.env.AWS_REGION || null,
  };

  logger.info(LOG_TAGS.ENV, 'Environment variables', envVars);

  // Verificar APIs críticas
  const criticalAPIs = {
    hasSupabase: envVars.NEXT_PUBLIC_SUPABASE_URL && envVars.NEXT_PUBLIC_SUPABASE_ANON_KEY,
    hasAuth: envVars.NEXT_PUBLIC_STACK_PROJECT_ID && envVars.JWT_SECRET,
    hasSportsAPI: envVars.SPORTS_API_KEY || envVars.ALLSPORT_DB_KEY || envVars.ALLSPORTDB_API_KEY,
    hasStreams: envVars.NEXT_PUBLIC_STREAM_URL || envVars.NEXT_PUBLIC_FALLBACK_STREAM_URL
  };

  logger.info(LOG_TAGS.ENV, 'Critical APIs status', criticalAPIs);

  // Avisos
  if (!criticalAPIs.hasSportsAPI) {
    logger.warn(LOG_TAGS.ENV, 'No sports API keys found - will use public APIs only');
  }

  if (!criticalAPIs.hasStreams) {
    logger.warn(LOG_TAGS.ENV, 'No stream URLs configured - player may show mock video');
  }

  return {
    envVars,
    criticalAPIs
  };
}

// Executar verificação automática no servidor
if (typeof window === 'undefined') {
  checkEnvironment();
}