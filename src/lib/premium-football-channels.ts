// Premium Football Channels - LaLiga, Premier League, Champions League
// These are alternative sources collected from various IPTV providers

export interface PremiumChannel {
  id: string
  name: string
  category: string
  country: string
  language: string
  logo: string
  streams: {
    url: string
    quality?: string
    requiresProxy?: boolean
    headers?: Record<string, string>
  }[]
}

export const premiumFootballChannels: PremiumChannel[] = [
  // Canais Oficiais da Espanha
  {
    id: 'real-madrid-tv',
    name: 'Real Madrid TV',
    category: 'LaLiga',
    country: 'ES',
    language: 'es',
    logo: 'https://upload.wikimedia.org/wikipedia/en/thumb/5/56/Real_Madrid_CF.svg/1200px-Real_Madrid_CF.svg.png',
    streams: [
      {
        url: 'https://rmtv.akamaized.net/hls/live/2043153/rmtv-es-web/master.m3u8',
        quality: 'HD',
        requiresProxy: false
      }
    ]
  },
  {
    id: 'sevilla-fc-tv',
    name: 'Sevilla FC TV',
    category: 'LaLiga',
    country: 'ES',
    language: 'es',
    logo: 'https://upload.wikimedia.org/wikipedia/en/thumb/3/3b/Sevilla_FC_logo.svg/1200px-Sevilla_FC_logo.svg.png',
    streams: [
      {
        url: 'https://open.http.mp.streamamg.com/p/3001314/sp/300131400/playManifest/entryId/0_ye0b8tc0/format/applehttp/protocol/https/uiConfId/30026292/a.m3u8',
        quality: 'HD',
        requiresProxy: false
      }
    ]
  },
  {
    id: 'teledeporte',
    name: 'Teledeporte',
    category: 'Sports',
    country: 'ES',
    language: 'es',
    logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/8/88/Logo_Teledeporte.svg/1200px-Logo_Teledeporte.svg.png',
    streams: [
      {
        url: 'https://ztnr.rtve.es/ztnr/1694255.m3u8',
        quality: 'HD',
        requiresProxy: false
      },
      {
        url: 'https://d2a02gfcid1k4a.cloudfront.net/v1/master/3722c60a815c199d9c0ef36c5b73da68a62b09d1/cc-clihr3vf54f9j/Teledeporte_ES.m3u8',
        quality: 'HD HEVC',
        requiresProxy: false
      }
    ]
  },
  // LaLiga Channels (Alternative streams)
  {
    id: 'laliga-ea-sports-1',
    name: 'LaLiga EA Sports',
    category: 'LaLiga',
    country: 'ES',
    language: 'es',
    logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/0/0f/LaLiga_EA_Sports_2023_Vertical_Logo.svg/1200px-LaLiga_EA_Sports_2023_Vertical_Logo.svg.png',
    streams: [
      {
        url: 'http://**************:8555/live/adriannnnn/1234/155.m3u8',
        quality: 'HD',
        requiresProxy: true
      }
    ]
  },
  {
    id: 'laliga-hypermotion',
    name: 'LaLiga Hypermotion',
    category: 'LaLiga',
    country: 'ES',
    language: 'es',
    logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/e/e0/LaLiga_Hypermotion_2023_Vertical_Logo.svg/1200px-LaLiga_Hypermotion_2023_Vertical_Logo.svg.png',
    streams: [
      {
        url: 'http://**************:8555/live/adriannnnn/1234/157.m3u8',
        quality: 'HD',
        requiresProxy: true
      }
    ]
  },
  
  // Premier League Channels
  {
    id: 'premier-league-1',
    name: 'Premier League TV',
    category: 'Premier League',
    country: 'UK',
    language: 'en',
    logo: 'https://upload.wikimedia.org/wikipedia/en/thumb/f/f2/Premier_League_Logo.svg/1200px-Premier_League_Logo.svg.png',
    streams: [
      {
        url: 'http://**************:8555/live/adriannnnn/1234/123.m3u8',
        quality: 'HD',
        requiresProxy: true
      }
    ]
  },
  
  // Champions League Channels
  {
    id: 'movistar-liga-de-campeones',
    name: 'Movistar Liga de Campeones',
    category: 'Champions League',
    country: 'ES',
    language: 'es',
    logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/8/83/Movistar_Liga_de_Campeones.svg/1200px-Movistar_Liga_de_Campeones.svg.png',
    streams: [
      {
        url: 'http://**************:8555/live/adriannnnn/1234/150.m3u8',
        quality: 'HD',
        requiresProxy: true
      }
    ]
  }
]

// Function to get premium channels by category
export function getPremiumChannelsByCategory(category: string): PremiumChannel[] {
  return premiumFootballChannels.filter(channel => channel.category === category)
}

// Function to get all premium channels
export function getAllPremiumChannels(): PremiumChannel[] {
  return premiumFootballChannels
}

// Function to get channel by ID
export function getPremiumChannelById(id: string): PremiumChannel | undefined {
  return premiumFootballChannels.find(channel => channel.id === id)
}