/**
 * Serviço server-side para integração com IPTV-ORG API
 * Este arquivo deve ser usado apenas em componentes server-side
 */

import { isSelectedSportsChannel, SELECTED_SPORTS_CHANNELS } from '@/lib/selected-sports-channels'

interface IPTVStream {
  channel: string
  url: string
  quality?: string
  referrer?: string
  user_agent?: string
  http_referrer?: string
  http_user_agent?: string
}

interface IPTVChannel {
  id: string
  name: string
  alt_names?: string[]
  network?: string
  owners?: string[]
  country?: string
  subdivision?: string
  city?: string
  broadcast_area?: string[]
  languages?: string[]
  categories?: string[]
  is_nsfw?: boolean
  launched?: string
  closed?: string
  replaced_by?: string
  website?: string
  logo?: string
}

const IPTV_BASE_URL = 'https://iptv-org.github.io/api'

// Cache em memória para o servidor
const serverCache = new Map<string, { data: any; timestamp: number }>()
const CACHE_TIME = 30 * 60 * 1000 // 30 minutos

function getFromCache(key: string): any {
  const cached = serverCache.get(key)
  if (!cached) return null
  
  if (Date.now() - cached.timestamp > CACHE_TIME) {
    serverCache.delete(key)
    return null
  }
  
  return cached.data
}

function setCache(key: string, data: any): void {
  serverCache.set(key, {
    data,
    timestamp: Date.now()
  })
}

export async function fetchIPTVStreams(): Promise<IPTVStream[]> {
  const cacheKey = 'streams'
  const cached = getFromCache(cacheKey)
  if (cached) {
    console.log('[IPTV-API] ✅ Streams obtained from cache')
    console.log('[IPTV-API] Cached streams count:', cached.length)
    return cached
  }

  try {
    console.log('[IPTV-API] ===== FETCHING IPTV STREAMS =====')
    console.log('[IPTV-API] URL:', `${IPTV_BASE_URL}/streams.json`)
    console.log('[IPTV-API] Cache miss - fetching from server...')
    
    const startTime = Date.now()
    const response = await fetch(`${IPTV_BASE_URL}/streams.json`, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (compatible; StreamPlus/1.0)',
      },
      next: { revalidate: 1800 } // Cache Next.js por 30 minutos
    })
    const fetchTime = Date.now() - startTime
    
    console.log('[IPTV-API] Response status:', response.status)
    console.log('[IPTV-API] Response time:', fetchTime, 'ms')
    console.log('[IPTV-API] Content-Type:', response.headers.get('content-type'))
    console.log('[IPTV-API] Content-Length:', response.headers.get('content-length'))
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    
    const data = await response.json()
    console.log('[IPTV-API] ✅ Streams received:', data.length)
    console.log('[IPTV-API] Sample stream:', data[0])
    console.log('[IPTV-API] Caching data for 30 minutes')
    console.log('[IPTV-API] ===================================')
    
    setCache(cacheKey, data)
    return data
  } catch (error) {
    console.error('[IPTV-API] ❌ ERROR FETCHING STREAMS')
    console.error('[IPTV-API] Error:', error instanceof Error ? error.message : String(error))
    console.error('[IPTV-API] Stack:', error instanceof Error ? error.stack : 'N/A')
    console.error('[IPTV-API] Returning empty array')
    console.error('[IPTV-API] ===================================')
    return []
  }
}

export async function fetchIPTVChannels(): Promise<IPTVChannel[]> {
  const cacheKey = 'channels'
  const cached = getFromCache(cacheKey)
  if (cached) {
    console.log('[IPTV-API] ✅ Channels obtained from cache')
    console.log('[IPTV-API] Cached channels count:', cached.length)
    return cached
  }

  try {
    console.log('[IPTV-API] ===== FETCHING IPTV CHANNELS =====')
    console.log('[IPTV-API] URL:', `${IPTV_BASE_URL}/channels.json`)
    console.log('[IPTV-API] Cache miss - fetching from server...')
    
    const startTime = Date.now()
    const response = await fetch(`${IPTV_BASE_URL}/channels.json`, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (compatible; StreamPlus/1.0)',
      },
      next: { revalidate: 1800 }
    })
    const fetchTime = Date.now() - startTime
    
    console.log('[IPTV-API] Response status:', response.status)
    console.log('[IPTV-API] Response time:', fetchTime, 'ms')
    console.log('[IPTV-API] Content-Type:', response.headers.get('content-type'))
    console.log('[IPTV-API] Content-Length:', response.headers.get('content-length'))
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    
    const data = await response.json()
    console.log('[IPTV-API] ✅ Channels received:', data.length)
    console.log('[IPTV-API] Sample channel:', data[0])
    console.log('[IPTV-API] Caching data for 30 minutes')
    console.log('[IPTV-API] ====================================')
    
    setCache(cacheKey, data)
    return data
  } catch (error) {
    console.error('[IPTV-API] ❌ ERROR FETCHING CHANNELS')
    console.error('[IPTV-API] Error:', error instanceof Error ? error.message : String(error))
    console.error('[IPTV-API] Stack:', error instanceof Error ? error.stack : 'N/A')
    console.error('[IPTV-API] Returning empty array')
    console.error('[IPTV-API] ====================================')
    return []
  }
}

export async function fetchSportsStreams(): Promise<IPTVStream[]> {
  console.log('[IPTV-API] ===== FETCHING SPORTS STREAMS =====')
  console.log('[IPTV-API] Starting sports streams search...')
  
  const [streams, channels] = await Promise.all([
    fetchIPTVStreams(),
    fetchIPTVChannels()
  ])

  console.log('[IPTV-API] Total streams available:', streams.length)
  console.log('[IPTV-API] Total channels available:', channels.length)

  // Criar mapa de canais por ID
  const channelsMap = new Map<string, IPTVChannel>()
  channels.forEach(ch => channelsMap.set(ch.id, ch))

  // Filtrar streams que pertencem a canais esportivos E estão na lista selecionada
  console.log('[IPTV-API] Filtering sports streams...')
  let debugCount = 0
  const sportsStreams = streams.filter(stream => {
    if (!stream.channel) return false
    const channel = channelsMap.get(stream.channel)
    const hasCategory = channel?.categories?.includes('sports')
    const isSelected = isSelectedSportsChannel(stream.channel)
    
    // Debug para ver o que está acontecendo
    if (stream.channel && (stream.channel.toLowerCase().includes('espn') || stream.channel.toLowerCase().includes('sport'))) {
      debugCount++
      if (debugCount <= 5) { // Limit debug output
        console.log(`[IPTV-API] Debug channel ${stream.channel}:`, {
          hasChannel: !!channel,
          categories: channel?.categories,
          hasCategory,
          isSelected,
          streamUrl: stream.url
        })
      }
    }
    
    return hasCategory && isSelected
  })

  console.log('[IPTV-API] Sports streams found:', sportsStreams.length)
  console.log('[IPTV-API] Selected channels allowed:', SELECTED_SPORTS_CHANNELS.size)

  // Remover duplicatas, mantendo o stream de melhor qualidade
  console.log('[IPTV-API] Removing duplicates and keeping best quality...')
  const streamsByChannel = new Map<string, IPTVStream>()
  
  sportsStreams.forEach(stream => {
    const existing = streamsByChannel.get(stream.channel)
    
    // Se não existe ou o novo tem melhor qualidade, substituir
    if (!existing || compareQuality(stream.quality, existing.quality) > 0) {
      streamsByChannel.set(stream.channel, stream)
    }
  })

  const uniqueStreams = Array.from(streamsByChannel.values())
  console.log('[IPTV-API] Unique streams after deduplication:', uniqueStreams.length)
  
  // Log dos primeiros 5 canais
  if (uniqueStreams.length > 0) {
    console.log('[IPTV-API] Sample of first 5 channels:')
    uniqueStreams.slice(0, 5).forEach((stream, i) => {
      const channel = channelsMap.get(stream.channel)
      console.log(`[IPTV-API]   ${i + 1}. ${channel?.name || stream.channel}`)
      console.log(`[IPTV-API]      - Quality: ${stream.quality || 'SD'}`)
      console.log(`[IPTV-API]      - Country: ${channel?.country || 'N/A'}`)
      console.log(`[IPTV-API]      - URL: ${stream.url}`)
    })
  } else {
    console.log('[IPTV-API] ⚠️ No sports streams found!')
  }

  console.log('[IPTV-API] =====================================')
  return uniqueStreams
}

function compareQuality(a?: string, b?: string): number {
  const qualityOrder = ['4k', '1080p', '720p', '480p', '360p', '240p']
  const aIndex = a ? qualityOrder.findIndex(q => q === a.toLowerCase()) : 999
  const bIndex = b ? qualityOrder.findIndex(q => q === b.toLowerCase()) : 999
  
  if (aIndex === -1) return bIndex === -1 ? 0 : -1
  if (bIndex === -1) return 1
  
  return bIndex - aIndex
}

export async function getPopularSportsChannels(): Promise<IPTVChannel[]> {
  const channels = await fetchIPTVChannels()
  const sportsChannels = channels.filter(ch => 
    ch.categories?.includes('sports') && isSelectedSportsChannel(ch.id)
  )
  
  const popularNetworks = [
    'ESPN', 'Fox Sports', 'beIN Sports', 'Sky Sports', 
    'TNT Sports', 'DAZN', 'Eurosport', 'Sport TV',
    'Premier Sports', 'BT Sport', 'NBC Sports'
  ]
  
  // Filtrar primeiro pelos canais selecionados, depois pelos populares
  const popularChannels = sportsChannels.filter(ch => 
    ch.name && popularNetworks.some(network => 
      ch.name.toLowerCase().includes(network.toLowerCase()) ||
      (ch.network && ch.network.toLowerCase().includes(network.toLowerCase()))
    )
  )
  
  // Se não houver canais populares suficientes, retornar todos os selecionados
  return popularChannels.length > 0 ? popularChannels : sportsChannels
}