import { createClient } from '@/lib/supabase/server'

export async function hasActiveSubscription(userId?: string): Promise<boolean> {
  const timestamp = new Date().toISOString()
  console.log(`[SUBSCRIPTION-CHECK] ${timestamp} - ===== CHECKING SUBSCRIPTION =====`)
  console.log(`[SUBSCRIPTION-CHECK] ${timestamp} - User ID:`, userId)
  
  if (!userId) {
    console.log(`[SUBSCRIPTION-CHECK] ${timestamp} - ❌ No user ID provided`)
    return false
  }
  
  const supabase = await createClient()
  
  console.log(`[SUBSCRIPTION-CHECK] ${timestamp} - Fetching profile from database...`)
  const { data: profile, error } = await supabase
    .from('profiles')
    .select('subscription_tier, subscription_status, subscription_current_period_end, trial_end_date')
    .eq('id', userId)
    .single()
  
  if (error) {
    console.error(`[SUBSCRIPTION-CHECK] ${timestamp} - ❌ Error fetching profile:`, error)
    return false
  }
  
  if (!profile) {
    console.log(`[SUBSCRIPTION-CHECK] ${timestamp} - ❌ No profile found for user`)
    return false
  }
  
  console.log(`[SUBSCRIPTION-CHECK] ${timestamp} - Profile data:`)
  console.log(`[SUBSCRIPTION-CHECK] ${timestamp} -   subscription_tier:`, profile.subscription_tier)
  console.log(`[SUBSCRIPTION-CHECK] ${timestamp} -   subscription_status:`, profile.subscription_status)
  console.log(`[SUBSCRIPTION-CHECK] ${timestamp} -   subscription_current_period_end:`, profile.subscription_current_period_end)
  console.log(`[SUBSCRIPTION-CHECK] ${timestamp} -   trial_end_date:`, profile.trial_end_date)
  
  // Verificar se tem tier premium
  if (profile.subscription_tier === 'premium') {
    console.log(`[SUBSCRIPTION-CHECK] ${timestamp} - User has premium tier`)
    
    // Se tem subscription_current_period_end, verificar se ainda é válido
    if (profile.subscription_current_period_end) {
      const currentPeriodEnd = new Date(profile.subscription_current_period_end)
      const now = new Date()
      const isValid = currentPeriodEnd > now
      
      console.log(`[SUBSCRIPTION-CHECK] ${timestamp} - Checking period end:`)
      console.log(`[SUBSCRIPTION-CHECK] ${timestamp} -   Current period end:`, currentPeriodEnd.toISOString())
      console.log(`[SUBSCRIPTION-CHECK] ${timestamp} -   Current time:`, now.toISOString())
      console.log(`[SUBSCRIPTION-CHECK] ${timestamp} -   Is valid:`, isValid)
      console.log(`[SUBSCRIPTION-CHECK] ${timestamp} -   Time remaining:`, isValid ? `${Math.floor((currentPeriodEnd.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))} days` : 'Expired')
      
      console.log(`[SUBSCRIPTION-CHECK] ${timestamp} - ✅ Result: ${isValid ? 'ACTIVE SUBSCRIPTION' : 'EXPIRED SUBSCRIPTION'}`)
      return isValid
    }
    // Se não tem current_period_end mas tem tier premium, considerar ativo
    console.log(`[SUBSCRIPTION-CHECK] ${timestamp} - No period end date but has premium tier`)
    console.log(`[SUBSCRIPTION-CHECK] ${timestamp} - ✅ Result: ACTIVE SUBSCRIPTION (no expiry)`)
    return true
  }
  
  console.log(`[SUBSCRIPTION-CHECK] ${timestamp} - User does not have premium tier`)
  
  // Verificar trial ativo (campo trial_end_date)
  if (profile.trial_end_date) {
    const trialEnd = new Date(profile.trial_end_date)
    const now = new Date()
    const isValid = trialEnd > now
    
    console.log(`[SUBSCRIPTION-CHECK] ${timestamp} - Checking trial:`)
    console.log(`[SUBSCRIPTION-CHECK] ${timestamp} -   Trial end:`, trialEnd.toISOString())
    console.log(`[SUBSCRIPTION-CHECK] ${timestamp} -   Current time:`, now.toISOString())
    console.log(`[SUBSCRIPTION-CHECK] ${timestamp} -   Is valid:`, isValid)
    console.log(`[SUBSCRIPTION-CHECK] ${timestamp} -   Time remaining:`, isValid ? `${Math.floor((trialEnd.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))} days` : 'Expired')
    
    console.log(`[SUBSCRIPTION-CHECK] ${timestamp} - ✅ Result: ${isValid ? 'ACTIVE TRIAL' : 'EXPIRED TRIAL'}`)
    return isValid
  }
  
  console.log(`[SUBSCRIPTION-CHECK] ${timestamp} - ❌ Result: NO ACTIVE SUBSCRIPTION OR TRIAL`)
  return false
}

export async function checkChannelAccess(userId?: string, channelId?: string): Promise<{
  hasAccess: boolean
  reason?: string
}> {
  // Canais de demonstração sempre liberados
  if (channelId?.startsWith('demo-') || channelId === 'test-stream') {
    return { hasAccess: true }
  }
  
  // Verificar se tem assinatura ativa
  const hasSubscription = await hasActiveSubscription(userId)
  
  if (!hasSubscription) {
    return {
      hasAccess: false,
      reason: 'subscription_required'
    }
  }
  
  return { hasAccess: true }
}