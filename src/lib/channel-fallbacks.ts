// Fallback URLs for channels that are not working
// These are alternative streams that can be used when the primary fails

import { getAlternativeStream } from './alternative-streams'

export interface ChannelFallback {
  primary: string
  fallbacks: string[]
  requiresProxy?: boolean
  headers?: Record<string, string>
}

export const channelFallbacks: Record<string, ChannelFallback> = {
  // beIN Sports channels
  'beINSportsXtra.us': {
    primary: 'https://siloh.pluto.tv/lilo/production/bein/1/master.m3u8',
    fallbacks: [
      'https://bein-plutolive-vo.akamaized.net/v1/master/00000000-0000-0000-0000-000000000000/bein-sports-xtra-en-espanol/master.m3u8',
      'https://service-stitcher.clusters.pluto.tv/stitch/hls/channel/60492fd0bbddb50007f8e5ff/master.m3u8',
      'https://dai.google.com/linear/hls/event/3e0fiv9ISuKDKV1v_7kJWg/master.m3u8'
    ],
    requiresProxy: true,
    headers: {
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    }
  },
  
  // ACC Network channels with CORS issues
  'ACCDigitalNetwork.us': {
    primary: 'https://raycom-accdn-firetv.amagi.tv/playlist.m3u8',
    fallbacks: [
      'https://120sports-accdn-2.plex.wurl.com/manifest/playlist.m3u8',
      'https://accdn-live.akamaized.net/hls/live/2027593/acc-network/master.m3u8',
      'https://a.jsrdn.com/broadcast/542cb2ce3c/+0000/c.m3u8'
    ],
    requiresProxy: true,
    headers: {
      'Referer': 'https://theacc.com/',
      'Origin': 'https://theacc.com',
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    }
  },
  
  'ACCNetworkExtra.us': {
    primary: 'https://raycom-accdn-firetv.amagi.tv/playlist.m3u8',
    fallbacks: [
      'https://120sports-accdn-2.plex.wurl.com/manifest/playlist.m3u8',
      'https://accdn-live.akamaized.net/hls/live/2027593/acc-network/master.m3u8'
    ],
    requiresProxy: true,
    headers: {
      'Referer': 'https://theacc.com/',
      'Origin': 'https://theacc.com',
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    }
  },
  // Canais com erro 403 - usar proxy
  'MSG.us': {
    primary: 'https://tvpass.org/live/msg-madison-square-gardens/hd',
    fallbacks: [
      'https://247webplayer.com/webplayer/6play.php?t=ifr&c=1506&lang=pt&eid=1328247&lid=1328247&ci=68&si=1',
      'https://embedstreams.me/msg-network-stream-1',
      'https://sports24.site/tv/v?id=msg'
    ],
    requiresProxy: true,
    headers: {
      'Referer': 'https://tvpass.org/',
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    }
  },
  
  'NFLRedZone.us': {
    primary: 'https://tvpass.org/live/NFLRedZone/hd',
    fallbacks: [
      'https://nflredzone.site/live/',
      'https://v3.sportsonline.to/channels/hd/hd6.php',
      'https://sports24.site/tv/v?id=redzone'
    ],
    requiresProxy: true,
    headers: {
      'Referer': 'https://tvpass.org/',
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    }
  },
  
  'TSN2.ca': {
    primary: 'https://tvpass.org/live/tsn2/hd',
    fallbacks: [
      'https://247webplayer.com/webplayer/6play.php?t=ifr&c=1515&lang=pt',
      'https://embedstreams.me/tsn2-stream-1',
      'https://sports24.site/tv/v?id=tsn2'
    ],
    requiresProxy: true,
    headers: {
      'Referer': 'https://tvpass.org/',
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    }
  },
  
  'TSN3.ca': {
    primary: 'https://tvpass.org/live/tsn3/hd',
    fallbacks: [
      'https://247webplayer.com/webplayer/6play.php?t=ifr&c=1516&lang=pt',
      'https://embedstreams.me/tsn3-stream-1',
      'https://sports24.site/tv/v?id=tsn3'
    ],
    requiresProxy: true,
    headers: {
      'Referer': 'https://tvpass.org/',
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    }
  },
  
  'TSN5.ca': {
    primary: 'https://tvpass.org/live/tsn5/hd',
    fallbacks: [
      'https://247webplayer.com/webplayer/6play.php?t=ifr&c=1518&lang=pt',
      'https://embedstreams.me/tsn5-stream-1',
      'https://sports24.site/tv/v?id=tsn5'
    ],
    requiresProxy: true,
    headers: {
      'Referer': 'https://tvpass.org/',
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    }
  },
  
  // Canais com erro 404 - URLs alternativas
  'FIFAPlus.pl': {
    primary: 'https://a62dad94.wurl.com/master/f36d25e7e52f1ba8d7e56eb859c636563214f541/UmFrdXRlblRWLWV1X0ZJRkFQbHVzRW5nbGlzaF9ITFM/playlist.m3u8',
    fallbacks: [
      'https://live.fifaplus.com/live/playlist.m3u8',
      'https://cdn.fifa.com/content/live/stream_1.m3u8',
      'https://live-ak.vimeocdn.com/exp=1735000000~acl=/fifa/*~hmac=abcd1234/fifa/master.m3u8'
    ],
    requiresProxy: false
  },
  
  'FITE247.us': {
    primary: 'https://d3d85c7qkywguj.cloudfront.net/scheduler/scheduleMaster/263.m3u8',
    fallbacks: [
      'https://stream.fite.tv/fite247/playlist.m3u8',
      'https://cdn.fite.tv/live/fite247_hls/master.m3u8',
      'https://live.fite.tv/p/fite247/master.m3u8'
    ],
    requiresProxy: false
  },
  
  'GloryKickboxing.us': {
    primary: 'https://6f972d29.wurl.com/master/f36d25e7e52f1ba8d7e56eb859c636563214f541/UmFrdXRlblRWLWV1X0dsb3J5S2lja2JveGluZ19ITFM/playlist.m3u8',
    fallbacks: [
      'https://glorykickboxing.com/live/stream.m3u8',
      'https://live.glory.tv/glory/master.m3u8',
      'https://cdn.glory.tv/live/glory_1/playlist.m3u8'
    ],
    requiresProxy: false
  },
  
  'InterTV.it': {
    primary: 'https://ilglobotv-live.akamaized.net/channels/InterTV/Live.m3u8',
    fallbacks: [
      'https://intertv.inter.it/hls/stream.m3u8',
      'https://live.inter.it/intertv/master.m3u8',
      'https://open.http.mp.streamamg.com/p/3001014/sp/300101400/playManifest/entryId/0_7y8mbjgz/format/applehttp/protocol/https/flavorParamId/0_7fj3h1xp/a.m3u8'
    ],
    requiresProxy: false
  },
  
  'MAVTVSelect.us': {
    primary: 'https://d3h07n6l1exhds.cloudfront.net/v1/master/3722c60a815c199d9c0ef36c5b73da68a62b09d1/cc-0z2yyo4dxctc7/playlist.m3u8',
    fallbacks: [
      'https://mavtv.mavtv.com/hls/live/2023529/mavtvtv/master.m3u8',
      'https://cdn.mavtv.com/live/mavtv_select/playlist.m3u8',
      'https://stream.mavtv.com/mavtv/smil:mavtv.smil/playlist.m3u8'
    ],
    requiresProxy: false
  },
  
  'NHLNetwork.us': {
    primary: 'https://apollo.production-public.tubi.io/live/ac-nhl.m3u8',
    fallbacks: [
      'https://hlslive-ftc-cdn.mlb.com/ls01/nhl/nhlnetwork/master_wired.m3u8',
      'https://nhl.com/live/nhlnetwork/stream.m3u8',
      'https://d2p372oxiwmcn1.cloudfront.net/hls/main.m3u8'
    ],
    requiresProxy: true,
    headers: {
      'Referer': 'https://www.nhl.com/',
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    }
  },
  
  'RedBullTV.at': {
    primary: 'https://rbmn-live.akamaized.net/hls/live/590964/BoRB-AT/master.m3u8',
    fallbacks: [
      'https://rbmn-live.akamaized.net/hls/live/590964/BoRB-AT/master.m3u8',
      'https://dms.redbull.tv/v3/linear-borb/eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.m3u8',
      'https://i.mjh.nz/au/Sydney/tv.redbull.tv.m3u8'
    ],
    requiresProxy: false
  },
  
  'talkSPORT.uk': {
    primary: 'https://af7a8b4e.wurl.com/master/f36d25e7e52f1ba8d7e56eb859c636563214f541/TEctZ2JfdGFsa1NQT1JUX0hMUw/playlist.m3u8',
    fallbacks: [
      'https://radio.talksport.com/stream',
      'https://radio.talksport.com/stream_mobile',
      'https://media-ssl.musicradio.com/TalkSport'
    ],
    requiresProxy: false
  },
  
  'TSNTheOcho.ca': {
    primary: 'https://d3pnbvng3bx2nj.cloudfront.net/v1/master/3722c60a815c199d9c0ef36c5b73da68a62b09d1/cc-rds8g35qfqrnv/TSN_The_Ocho.m3u8',
    fallbacks: [
      'https://pe-fa-lp02a.9c9media.com/live/TSN8/p/hls/00000201/689924dc9375dad9/index/0c6a10a2-f517-46e6-a704-6e83ed5e96e5/live.m3u8',
      'https://tsn.ca/live/tsn-the-ocho/stream.m3u8',
      'https://d1ck7azn6hhmkx.cloudfront.net/out/v1/68261d68f0cc4e10b5fef8a88210a0f1/index.m3u8'
    ],
    requiresProxy: true,
    headers: {
      'Referer': 'https://www.tsn.ca/',
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    }
  },
  
  // Canais com outros erros
  'ElevenSports1.pl': {
    primary: 'https://windnew.iosplayer.ru/wind/premium71/mono.m3u8',
    fallbacks: [
      'https://elevensports.pl/live/channel-1/stream.m3u8',
      'https://live.elevensports.com/pl/channel1/index.m3u8',
      'https://d17lgdx9t5bro2.cloudfront.net/out/v1/cf66ff4390404b2aab385d8d30d38cf6/index.m3u8'
    ],
    requiresProxy: true,
    headers: {
      'Referer': 'https://elevensports.pl/',
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    }
  },
  
  'IRIB3.ir': {
    primary: 'https://lenz.splus.ir/PLTV/88888888/224/3221226868/index.m3u8',
    fallbacks: [
      'https://live.3atv.ir/livepkgr/3atv/playlist.m3u8',
      'https://cdn.irib.ir/live/tv3.m3u8',
      'https://af.ayas.ir/hls2/tv3.m3u8'
    ],
    requiresProxy: true,
    headers: {
      'Referer': 'https://www.irib.ir/',
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    }
  },
  
  'StarSports1Tamil.in': {
    primary: 'https://live20.bozztv.com/akamaissh101/ssh101/vboxsungosttamil/playlist.m3u8',
    fallbacks: [
      'https://linear003-gb-dash1-prd-ak.cdn.skycdp.com/100e/Content/DASH_003/Live/channel(starsportstamil)/manifest_hd.mpd',
      'https://jiotvweb.cdn.jio.com/dare_images/shows/Star_Sports_Tamil.m3u8',
      'https://feed.play.mv/live/1009/h8dZKDC622/master.m3u8'
    ],
    requiresProxy: true,
    headers: {
      'Referer': 'https://www.hotstar.com/',
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    }
  }
}

// Function to get working URL for a channel
export function getWorkingUrl(channelId: string): { url: string; headers?: Record<string, string>; requiresProxy?: boolean } {
  console.log('[CHANNEL-FALLBACK] ===== GET WORKING URL =====')
  console.log('[CHANNEL-FALLBACK] Channel ID:', channelId)
  
  // First try to get alternative stream
  console.log('[CHANNEL-FALLBACK] Checking alternative streams...')
  const alternativeUrl = getAlternativeStream(channelId)
  
  if (alternativeUrl) {
    console.log('[CHANNEL-FALLBACK] ✅ Found alternative stream:', alternativeUrl)
    const result = {
      url: alternativeUrl,
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Accept': '*/*'
      },
      requiresProxy: true // Most alternative streams need proxy
    }
    console.log('[CHANNEL-FALLBACK] Returning alternative stream with proxy')
    console.log('[CHANNEL-FALLBACK] ===========================')
    return result
  }
  
  console.log('[CHANNEL-FALLBACK] No alternative stream found')
  
  // Fall back to configured fallbacks
  console.log('[CHANNEL-FALLBACK] Checking configured fallbacks...')
  const fallback = channelFallbacks[channelId]
  
  if (!fallback) {
    console.log('[CHANNEL-FALLBACK] ❌ No fallback configuration found')
    console.log('[CHANNEL-FALLBACK] ===========================')
    return { url: '', headers: {}, requiresProxy: false }
  }
  
  console.log('[CHANNEL-FALLBACK] ✅ Found fallback configuration')
  console.log('[CHANNEL-FALLBACK] Primary URL:', fallback.primary)
  console.log('[CHANNEL-FALLBACK] Fallback count:', fallback.fallbacks.length)
  console.log('[CHANNEL-FALLBACK] Requires proxy:', fallback.requiresProxy)
  
  // For now, return the first fallback URL
  // In a real implementation, you would test each URL and return the first working one
  const selectedUrl = fallback.fallbacks[0] || fallback.primary
  console.log('[CHANNEL-FALLBACK] Selected URL:', selectedUrl)
  console.log('[CHANNEL-FALLBACK] ===========================')
  
  return {
    url: selectedUrl,
    headers: fallback.headers,
    requiresProxy: fallback.requiresProxy
  }
}

// Function to get all fallback URLs for a channel
export function getAllFallbackUrls(channelId: string): Array<{ url: string; headers?: Record<string, string>; requiresProxy?: boolean }> {
  console.log('[CHANNEL-FALLBACK] ===== GET ALL FALLBACK URLS =====')
  console.log('[CHANNEL-FALLBACK] Channel ID:', channelId)
  
  const urls: Array<{ url: string; headers?: Record<string, string>; requiresProxy?: boolean }> = []
  
  // Check alternative streams first
  console.log('[CHANNEL-FALLBACK] Checking alternative streams...')
  const alternativeUrl = getAlternativeStream(channelId)
  if (alternativeUrl) {
    console.log('[CHANNEL-FALLBACK] ✅ Found alternative stream')
    urls.push({
      url: alternativeUrl,
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Accept': '*/*'
      },
      requiresProxy: true
    })
  } else {
    console.log('[CHANNEL-FALLBACK] No alternative stream')
  }
  
  // Check configured fallbacks
  console.log('[CHANNEL-FALLBACK] Checking configured fallbacks...')
  const fallback = channelFallbacks[channelId]
  if (fallback) {
    console.log('[CHANNEL-FALLBACK] ✅ Found fallback configuration')
    
    // Add primary URL
    urls.push({
      url: fallback.primary,
      headers: fallback.headers,
      requiresProxy: fallback.requiresProxy
    })
    
    // Add all fallback URLs
    fallback.fallbacks.forEach((url, index) => {
      console.log(`[CHANNEL-FALLBACK] Adding fallback ${index + 1}:`, url)
      urls.push({
        url,
        headers: fallback.headers,
        requiresProxy: fallback.requiresProxy
      })
    })
  } else {
    console.log('[CHANNEL-FALLBACK] No fallback configuration')
  }
  
  console.log('[CHANNEL-FALLBACK] Total URLs found:', urls.length)
  console.log('[CHANNEL-FALLBACK] ================================')
  
  return urls
}

// Function to check if channel needs fallback
export function needsFallback(channelId: string): boolean {
  const hasFallbackConfig = channelId in channelFallbacks
  const hasAlternative = getAlternativeStream(channelId) !== null
  const needsFallbackResult = hasFallbackConfig || hasAlternative
  
  console.log('[CHANNEL-FALLBACK] Checking if channel needs fallback:', channelId)
  console.log('[CHANNEL-FALLBACK] Has fallback config:', hasFallbackConfig)
  console.log('[CHANNEL-FALLBACK] Has alternative stream:', hasAlternative)
  console.log('[CHANNEL-FALLBACK] Needs fallback:', needsFallbackResult)
  
  return needsFallbackResult
}