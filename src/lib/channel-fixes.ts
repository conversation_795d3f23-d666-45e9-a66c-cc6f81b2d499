// Channel ID mapping to fix 404 errors
export const channelIdMapping: Record<string, string> = {
  // beIN Sports variations
  'BEINSportsXtra.us': 'beINSportsXtra.us',
  'beINSportsXtra.us': 'beINSportsXtra.us',
  'BEINSportsXtra2.us': 'beINSportsXtra.us',
  'beINSPORTSenEspanol.us': 'beINSPORTSenEspanol.us',
  'beINSPORTSXTRAenEspanol.us': 'beINSPORTSXTRAenEspanol.us',
  
  // ACC Network
  'ACCNetwork.us': 'ACCNetworkExtra.us',
  'ACCDigitalNetwork.us': 'ACCDigitalNetwork.us',
  'ACC Digital Network': 'ACCDigitalNetwork.us',
  
  // Action Sports
  'ActionSports.us': 'ActionSportsNetwork.us',
  'ActionSports2.us': 'ActionSportsPlus.us',
  
  // Adventure Sports
  'AdventureSportsTV.us': 'AdventureSportsNetwork.us',
  
  // Arena Sport
  'ArenaSport1.hr': 'ArenaSport1Croatia.ba',
  'ArenaSport2.hr': 'ArenaSport2Croatia.ba',
  'ArenaSport3.hr': 'ArenaSport3Croatia.ba',
  'ArenaSport4.hr': 'ArenaSport4Croatia.ba',
  'ArenaSport5.hr': 'ArenaSport5Croatia.ba',
  
  // Astro
  'AstroArena.my': 'AstroArena.my',
  'AstroArena2.my': 'AstroArena2.my',
  'AstroSupersport.my': 'AstroSuperSport.my',
  'AstroSupersport2.my': 'AstroSuperSport2.my',
  
  // BEK Sports
  'BEKSports.us': 'BEKSportsPlus.us',
  
  // Others
  'BarcaTV.es': 'BarçaTV.es',
  'ChampionTV.us': 'ChampionshipTV.us',
  'ElevenSports1.pt': 'ElevenSports1Portugal.pt',
  'ElevenSports1.pl': 'ElevenSports1.pl',
  'BeinSportsMax2.us': 'beINSportsXtra.us',
  'BlazeTV.us': 'TheBlaze.us',
  
  // CCTV
  'CCTV5.cn': 'CCTV5.cn',
  'CCTV5Plus.cn': 'CCTV5Plus.cn',
  
  // 30A channels
  '30a-golf-kingdom': '30AGolfKingdom.us',
  '30A Golf Kingdom': '30AGolfKingdom.us',
  
  // Adjarasport
  'AdjardSport1.ge': 'Adjarasport1.ge',
  'Adjarasport 1': 'Adjarasport1.ge',
  
  // Fox Sports
  'FoxDeportes.us': 'FoxDeportes.us',
  'FoxSports1.us': 'FS1.us',
  'FoxSports2.us': 'FS2.us',
}

// Geo-restricted channels that need special handling
export const geoRestrictedChannels = [
  'ACCDigitalNetwork.us',
  'ACCNetworkExtra.us',
  'beINSPORTSXTRAenEspanol.us',
  'AlfaSport.bg',
  'CBCSport.az',
  'BulgariaSport.bg',
  'CBCSport.az',
  'ArenaSport1Croatia.ba',
  'ArenaSport2Croatia.ba',
  'ArenaSport3Croatia.ba',
  'ArenaSport4Croatia.ba',
  'ArenaSport5Croatia.ba',
  'CCTV5.cn',
  'CCTV5Plus.cn',
  'DAZNLaLiga.es',
  'DAZNCombat.es',
]

// Channels requiring authentication
export const authRequiredChannels = [
  'DAZNCombat.es',
  'DAZNLaLiga.es',
  'Combate.br',
  'DAZN1Germany.de',
  'DAZN2Germany.de',
  'beINSportsConnect.us',
  'beINSportsConnectEnEspanol.us',
]

// Headers for specific providers
export const providerHeaders: Record<string, Record<string, string>> = {
  'cctv': {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    'Referer': 'https://tv.cctv.com/',
  },
  'bein': {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    'Origin': 'https://www.bein.com',
  },
  'eleven': {
    'User-Agent': 'Mozilla/5.0 (Smart TV; Linux) AppleWebKit/537.36',
    'X-Requested-With': 'XMLHttpRequest',
  },
}

// Get fixed channel ID
export function getFixedChannelId(channelId: string): string {
  console.log('[CHANNEL-FIX] ===== FIXING CHANNEL ID =====')
  console.log('[CHANNEL-FIX] Original channelId:', channelId)
  
  // Remove iptv- prefix if present
  let cleanId = channelId.startsWith('iptv-') 
    ? channelId.replace('iptv-', '') 
    : channelId
  
  console.log('[CHANNEL-FIX] After removing prefix:', cleanId)
  
  // Remove timestamp suffix (e.g., -1753602777123) if present
  const timestampPattern = /-\d{10,}$/
  if (timestampPattern.test(cleanId)) {
    const timestampMatch = cleanId.match(timestampPattern)
    console.log('[CHANNEL-FIX] Found timestamp suffix:', timestampMatch?.[0])
    cleanId = cleanId.replace(timestampPattern, '')
    console.log('[CHANNEL-FIX] After removing timestamp:', cleanId)
  }
  
  // Check if there's a mapping
  const mappedId = channelIdMapping[cleanId] || cleanId
  const isMapped = channelIdMapping[cleanId] !== undefined
  console.log('[CHANNEL-FIX] Checking mapping...')
  console.log('[CHANNEL-FIX] Has mapping:', isMapped)
  if (isMapped) {
    console.log('[CHANNEL-FIX] Mapped from:', cleanId, 'to:', mappedId)
  }
  console.log('[CHANNEL-FIX] Final ID:', mappedId)
  console.log('[CHANNEL-FIX] ==============================')
  
  return mappedId
}

// Get headers for channel
export function getChannelHeaders(channelId: string): Record<string, string> | undefined {
  const cleanId = channelId.toLowerCase()
  
  console.log('[CHANNEL-FIX] Getting headers for channel:', channelId)
  
  let headers: Record<string, string> | undefined
  let provider: string | undefined
  
  if (cleanId.includes('cctv')) {
    headers = providerHeaders.cctv
    provider = 'cctv'
  } else if (cleanId.includes('bein')) {
    headers = providerHeaders.bein
    provider = 'bein'
  } else if (cleanId.includes('eleven')) {
    headers = providerHeaders.eleven
    provider = 'eleven'
  }
  
  console.log('[CHANNEL-FIX] Provider detected:', provider || 'none')
  console.log('[CHANNEL-FIX] Headers:', headers || 'none')
  
  return headers
}

// Check if channel is geo-restricted
export function isGeoRestricted(channelId: string): boolean {
  const cleanId = channelId.replace('iptv-', '')
  const isRestricted = geoRestrictedChannels.includes(cleanId)
  
  console.log('[CHANNEL-FIX] Checking geo-restriction for:', cleanId)
  console.log('[CHANNEL-FIX] Is geo-restricted:', isRestricted)
  
  return isRestricted
}

// Check if channel requires auth
export function requiresAuth(channelId: string): boolean {
  const cleanId = channelId.replace('iptv-', '')
  const needsAuth = authRequiredChannels.includes(cleanId)
  
  console.log('[CHANNEL-FIX] Checking auth requirement for:', cleanId)
  console.log('[CHANNEL-FIX] Requires auth:', needsAuth)
  
  return needsAuth
}