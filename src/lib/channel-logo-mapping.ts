// Mapeamento de nomes de canais para arquivos de logo
export const channelLogoMapping: Record<string, string> = {
  // Canais com logos disponíveis
  '30A Golf Kingdom': '30A Golf Kingdom.png',
  'ACC Digital Network': 'ACC Digital Network.png',
  'ADO TV': 'ADO TV.png',
  'Adjarasport 1': 'Adjarasport1.png',
  'Adjarasport1': 'Adjarasport1.png',
  'Africa 24 Sport': 'Africa 24 Sport.png',
  'AfroSport Nigeria': 'AfroSport Nigeria.png',
  'Antena Sport': 'AntenaSport.jpg',
  'AntenaSport': 'AntenaSport.jpg',
  'Arryadia': 'Arryadia.jpg',
  'Astrahan.Ru Sport': 'Astrahan.Ru Sport.jpg',
  'Bahrain Sports 1': 'Bahrain Sports 1.png',
  'Bahrain Sports 2': 'Bahrain Sports 2.png',
  'Belarus 5': 'Belarus-5.jpg',
  'Belarus-5': 'Belarus-5.jpg',
  'Bellator MMA': 'Bellator MMA.png',
  'Billiard TV': 'BilliardTV.png',
  'BilliardTV': 'BilliardTV.png',
  'CBS Sports Network': 'CBS Sports Network USA.jpg',
  'CBS Sports Network USA': 'CBS Sports Network USA.jpg',
  'CT Sport': 'CT Sport.png',
  'Canal do Inter': 'Canal do Inter.png',
  'Dong Nai TV2': 'Dong Nai TV2.png',
  'Dubai Racing': 'Dubai Racing.jpg',
  'Dubai Racing 2': 'Dubai Racing 2.jpg',
  'Dubai Racing 3': 'Dubai Racing 3.jpg',
  'Dubai Sports 1': 'Dubai Sports 1.jpg',
  'Dubai Sports 2': 'Dubai sports2.jpg',
  'Dubai Sports 3': 'Dubai Sports3.jpg',
  'ESPN News': 'ESPNEWS.png',
  'ESPNEWS': 'ESPNEWS.png',
  'ESPN U': 'ESPNU.png',
  'ESPNU': 'ESPNU.png',
  'Eleven Sports 1': 'Eleven Sports 1.jpg',
  'FIFA+': 'FIFA+.png',
  'FIFA Plus': 'FIFA+.png',
  'FanDuel Sports Network': 'FanDuel Sports Network.jpg',
  'FanDuel TV': 'FanDuelTV.jpg',
  'FanDuelTV': 'FanDuelTV.jpg',
  'Fite 24/7': 'Fite24-7.png',
  'Fite24-7': 'Fite24-7.png',
  'Glory Kickboxing': 'Glory Kickboxing.jpg',
  'ITV Deportes': 'ITV Deportes.jpg',
  'Insight TV': 'Insight TV.png',
  'Inter TV': 'InterTV.jpg',
  'InterTV': 'InterTV.jpg',
  'Irib 3': 'Irib 3.png',
  'K19 TV': 'K19TV.png',
  'K19TV': 'K19TV.png',
  'KHL Prime': 'KHL Prime.png',
  'KSA Sports 1': 'KSA Sports 1.jpg',
  'Lacrosse TV': 'Lacrose TV.jpg',
  'MAVTV Select': 'MAVTV Select.png',
  'MLB Network': 'MLB Network.png',
  'MSG': 'MSGpng.png',
  'Made in BO TV': 'MadeinBO TV.png',
  'MadeinBO TV': 'MadeinBO TV.png',
  'Math Arena': 'Math! Arena.png',
  'Math! Arena': 'Math! Arena.png',
  'More Than Sports TV': 'More Than Sports TV.jpg',
  'NFL Network': 'NFL Network.png',
  'NFL RedZone': 'NFLRedZone .png',
  'NFLRedZone': 'NFLRedZone .png',
  'NHL Network': 'NHL Network.jpg',
  'Nitro Circus': 'Nitro Circus.png',
  'ON Football': 'ON Football.png',
  'Racing America': 'Racing America.jpg',
  'Red Bull TV': 'RedBullTV.png',
  'RedBullTV': 'RedBullTV.png',
  'SOS Kanall Plus': 'SOS Kanall Plus.png',
  'Sport 1 Tamil': 'Sport 1 Tamil.jpg',
  'Sports Channel': 'Sports Channel.png',
  'SportsGrid': 'SportsGrid.png',
  'SportsNet New York': 'SportsNet New York.png',
  'Swerve Sports': 'Swerve Sports.jpg',
  'TDM Sports': 'TDM Sports.jpg',
  'TR Sport': 'TR Sport.jpg',
  'TSN The Ocho': 'TSN The Ocho.jpg',
  'TSN 1': 'TSN1.png',
  'TSN1': 'TSN1.png',
  'TSN 2': 'TSN2.png',
  'TSN2': 'TSN2.png',
  'TSN 3': 'TSN3.png',
  'TSN3': 'TSN3.png',
  'TSN 5': 'TSN5.png',
  'TSN5': 'TSN5.png',
  'TalkSPORT': 'TalkSPORT.png',
  'Trace Sports Stars': 'Trace Sports Stars.png',
  'Trace Sport Stars': 'Trace Sports Stars.png', // Variação sem 's'
  'Star Sports 1 Tamil': 'Sport 1 Tamil.jpg', // Correção do nome
};

// Função para obter o caminho da logo do canal
export function getChannelLogoPath(channelName: string): string | null {
  // Tentar encontrar correspondência exata
  if (channelLogoMapping[channelName]) {
    return `/logos/${channelLogoMapping[channelName]}`;
  }
  
  // Tentar encontrar correspondência parcial (case insensitive)
  const lowerChannelName = channelName.toLowerCase();
  for (const [key, value] of Object.entries(channelLogoMapping)) {
    if (key.toLowerCase() === lowerChannelName) {
      return `/logos/${value}`;
    }
  }
  
  // Normalizar o nome do canal removendo caracteres especiais
  const normalizedChannelName = channelName.replace(/[\s\-_]/g, '').toLowerCase();
  
  // Tentar encontrar correspondência com nome normalizado
  for (const [key, value] of Object.entries(channelLogoMapping)) {
    const normalizedKey = key.replace(/[\s\-_]/g, '').toLowerCase();
    if (normalizedKey === normalizedChannelName) {
      return `/logos/${value}`;
    }
  }
  
  // Tentar encontrar correspondência parcial (contém)
  for (const [key, value] of Object.entries(channelLogoMapping)) {
    if (channelName.toLowerCase().includes(key.toLowerCase()) || 
        key.toLowerCase().includes(channelName.toLowerCase())) {
      return `/logos/${value}`;
    }
  }
  
  // Mapeamentos especiais para variações comuns
  if (channelName.includes('ESPN') && !channelName.includes('NEWS') && !channelName.includes('U')) {
    // Para canais ESPN genéricos, tentar usar ESPNEWS como fallback
    return `/logos/ESPNEWS.png`;
  }
  
  return null;
}

// Função para verificar se a logo existe
export function hasChannelLogo(channelName: string): boolean {
  return getChannelLogoPath(channelName) !== null;
}