/**
 * Logger customizado para debug
 */

import { DEBUG, LOG_LEVELS, CURRENT_LOG_LEVEL, type LogTag } from '@/config/debug';

interface LogEntry {
  timestamp: string;
  level: keyof typeof LOG_LEVELS;
  tag: string;
  message: string;
  data?: any;
}

class Logger {
  private logs: LogEntry[] = [];
  private maxLogs = 1000;

  private formatTimestamp(): string {
    return new Date().toISOString();
  }

  private shouldLog(level: keyof typeof LOG_LEVELS): boolean {
    return LOG_LEVELS[level] <= CURRENT_LOG_LEVEL;
  }

  private formatMessage(level: string, tag: string, message: string, data?: any): string {
    const timestamp = this.formatTimestamp();
    const prefix = `[${timestamp}] [${level}] [${tag}]`;
    
    if (data) {
      return `${prefix} ${message} ${JSON.stringify(data, null, 2)}`;
    }
    return `${prefix} ${message}`;
  }

  private log(level: keyof typeof LOG_LEVELS, tag: string, message: string, data?: any) {
    if (!this.shouldLog(level)) return;

    const entry: LogEntry = {
      timestamp: this.formatTimestamp(),
      level,
      tag,
      message,
      data
    };

    // Armazenar no histórico
    this.logs.push(entry);
    if (this.logs.length > this.maxLogs) {
      this.logs = this.logs.slice(-this.maxLogs);
    }

    // Log no console
    const formattedMessage = this.formatMessage(level, tag, message, data);
    
    switch (level) {
      case 'ERROR':
        console.error(formattedMessage);
        break;
      case 'WARN':
        console.warn(formattedMessage);
        break;
      case 'INFO':
        console.info(formattedMessage);
        break;
      case 'DEBUG':
      case 'TRACE':
        console.log(formattedMessage);
        break;
    }
  }

  error(tag: string, message: string, data?: any) {
    this.log('ERROR', tag, message, data);
  }

  warn(tag: string, message: string, data?: any) {
    this.log('WARN', tag, message, data);
  }

  info(tag: string, message: string, data?: any) {
    this.log('INFO', tag, message, data);
  }

  debug(tag: string, message: string, data?: any) {
    this.log('DEBUG', tag, message, data);
  }

  trace(tag: string, message: string, data?: any) {
    this.log('TRACE', tag, message, data);
  }

  // Obter histórico de logs
  getHistory(filterTag?: string): LogEntry[] {
    if (filterTag) {
      return this.logs.filter(log => log.tag === filterTag);
    }
    return this.logs;
  }

  // Limpar logs
  clear() {
    this.logs = [];
  }

  // Exportar logs
  export(): string {
    return this.logs.map(log => {
      const { timestamp, level, tag, message, data } = log;
      return `[${timestamp}] [${level}] [${tag}] ${message}${data ? ' ' + JSON.stringify(data) : ''}`;
    }).join('\n');
  }
}

// Singleton
export const logger = new Logger();

// Expor globalmente para debug
if (typeof window !== 'undefined') {
  (window as any).__logger = logger;
  if (DEBUG) {
    console.log('🔍 DEBUG MODE ENABLED - Logger disponível em window.__logger');
  }
}