// Lista de canais que devem ser removidos (não funcionam e não podem ser corrigidos)
export const BLOCKED_CHANNELS = new Set<string>([
  // Esta lista será preenchida após os testes
])

// Lista de canais que precisam de proxy
export const PROXY_REQUIRED_CHANNELS = new Set<string>([
  // Esta lista será preenchida após os testes
])

// Função para filtrar canais
export function filterWorkingChannels(channels: any[]): any[] {
  return channels.filter(channel => {
    const channelId = channel.id?.replace('iptv-', '')
    return !BLOCKED_CHANNELS.has(channelId) && !BLOCKED_CHANNELS.has(channel.id)
  })
}

// Função para verificar se canal precisa de proxy
export function needsProxy(channelId: string): boolean {
  const id = channelId.replace('iptv-', '')
  return PROXY_REQUIRED_CHANNELS.has(id) || PROXY_REQUIRED_CHANNELS.has(channelId)
}

// Função para atualizar listas baseado nos resultados dos testes
export function updateChannelLists(testResults: any) {
  const { unfixableChannels, fixableChannels } = testResults
  
  // Limpar listas atuais
  BLOCKED_CHANNELS.clear()
  PROXY_REQUIRED_CHANNELS.clear()
  
  // Adicionar canais que não funcionam
  unfixableChannels?.forEach((channel: any) => {
    BLOCKED_CHANNELS.add(channel.channelId.replace('iptv-', ''))
  })
  
  // Adicionar canais que precisam de proxy
  fixableChannels?.forEach((channel: any) => {
    if (channel.solution?.includes('proxy') || channel.solution?.includes('Proxy')) {
      PROXY_REQUIRED_CHANNELS.add(channel.channelId.replace('iptv-', ''))
    }
  })
  
  return {
    blocked: Array.from(BLOCKED_CHANNELS),
    proxyRequired: Array.from(PROXY_REQUIRED_CHANNELS)
  }
}