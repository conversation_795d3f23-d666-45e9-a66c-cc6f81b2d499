// Protege contra erros causados por extensões do navegador
// Especialmente MetaMask, Trust Wallet e outras extensões de carteira

export function setupBrowserExtensionGuard() {
  if (typeof window === 'undefined') return

  // Captura erros globais causados por extensões
  const originalError = window.onerror
  window.onerror = function(message, source, lineno, colno, error) {
    // Verifica se o erro vem de uma extensão
    if (source && source.includes('chrome-extension://')) {
      console.warn('🔌 Erro de extensão do navegador ignorado:', {
        extension: source.split('/')[2], // ID da extensão
        message,
        error
      })
      
      // Previne que o erro se propague
      return true
    }
    
    // Para outros erros, usa o handler original
    if (originalError) {
      return originalError.call(window, message, source, lineno, colno, error)
    }
    return false
  }

  // Adiciona listener para promises rejeitadas
  window.addEventListener('unhandledrejection', (event) => {
    const error = event.reason
    
    // Verifica se é um erro de extensão
    if (error && error.stack && error.stack.includes('chrome-extension://')) {
      console.warn('🔌 Promise rejeitada por extensão ignorada:', error)
      event.preventDefault()
    }
  })

  // Protege contra modificações de objetos globais por extensões
  const protectGlobalObject = (obj: any, name: string) => {
    if (!obj) return
    
    try {
      Object.keys(obj).forEach(key => {
        const descriptor = Object.getOwnPropertyDescriptor(obj, key)
        if (descriptor && descriptor.configurable) {
          Object.defineProperty(obj, key, {
            ...descriptor,
            configurable: false
          })
        }
      })
    } catch (e) {
      console.warn(`⚠️ Não foi possível proteger ${name}:`, e)
    }
  }

  // Lista de objetos globais que extensões costumam modificar
  const globalObjectsToProtect = [
    'ethereum',
    'solana',
    'phantom',
    'trustwallet',
    'metamask'
  ]

  globalObjectsToProtect.forEach(name => {
    if ((window as any)[name]) {
      protectGlobalObject((window as any)[name], name)
    }
  })

  console.log('🛡️ Proteção contra extensões do navegador ativada')
}

// Função para verificar se há extensões problemáticas
export function checkProblematicExtensions(): string[] {
  const problematicExtensions: string[] = []
  
  // Verifica extensões conhecidas
  const extensionChecks = [
    { name: 'MetaMask', check: () => !!(window as any).ethereum },
    { name: 'Trust Wallet', check: () => !!(window as any).trustwallet },
    { name: 'Phantom', check: () => !!(window as any).phantom },
    { name: 'Coinbase Wallet', check: () => !!(window as any).coinbaseWalletExtension },
    { name: 'Binance Wallet', check: () => !!(window as any).BinanceChain }
  ]

  extensionChecks.forEach(({ name, check }) => {
    try {
      if (check()) {
        problematicExtensions.push(name)
      }
    } catch (e) {
      // Ignora erros ao verificar
    }
  })

  return problematicExtensions
}