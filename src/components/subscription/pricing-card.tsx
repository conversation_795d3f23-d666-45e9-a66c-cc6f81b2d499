'use client'

import { useState } from 'react'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Check, X, Loader2, Crown, Zap, Shield, Tv, Smartphone, CreditCard } from 'lucide-react'
import { useRouter } from 'next/navigation'
import { toast } from 'sonner'
import { useAuthOptional } from '@/hooks/use-auth'

interface PricingCardProps {
  title?: string
  price?: number
  currency?: string
  interval?: string
  features?: string[]
  notIncluded?: string[]
  popular?: boolean
  description?: string
  ctaText?: string
  trialDays?: number
  onSubscribe?: () => Promise<void>
}

export function PricingCard({
  title = 'NewSpports Premium',
  price = 20,
  currency = 'EUR',
  interval = 'mes',
  features = [
    'Acceso ilimitado a todos los canales premium',
    'Calidad HD y 4K disponible',
    'Sin anuncios ni interrupciones',
    'Múltiples pantallas simultáneas',
    'Soporte 24/7 vía chat',
    'Grabación de programas en la nube',
    'Guía de programación EPG completa',
    'App para iOS y Android'
  ],
  notIncluded = [
    'Canales PPV especiales',
    'Eventos exclusivos premium'
  ],
  popular = true,
  description = 'Todo lo que necesitas para ver tus deportes favoritos',
  ctaText = 'Empezar Ahora',
  trialDays = 30,
  onSubscribe,
}: PricingCardProps) {
  const router = useRouter()
  const { user } = useAuthOptional()
  const [loading, setLoading] = useState(false)
  const [loadingTrial, setLoadingTrial] = useState(false)

  const handleSubscribe = async () => {
    if (onSubscribe) {
      setLoading(true)
      try {
        await onSubscribe()
      } catch (error) {
        toast.error('Error al procesar suscripción')
      } finally {
        setLoading(false)
      }
      return
    }

    // Default: crear sesión de checkout SIN trial
    setLoading(true)
    try {
      const body = {
        trialDays: 0, // Sin trial para pago directo
        email: user?.primaryEmail || '',
        name: user?.displayName || '',
      }
      
      // Si no hay usuario, pedir email
      if (!user && !body.email) {
        const email = prompt('Por favor, ingresa tu email para continuar:')
        if (!email) {
          setLoading(false)
          return
        }
        body.email = email
      }
      
      console.log('🔵 [PRICING-CARD] Enviando solicitud de checkout:', body)
      
      const response = await fetch('/api/stripe/checkout', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(body),
      })

      console.log('📦 [PRICING-CARD] Respuesta status:', response.status)
      const data = await response.json()
      console.log('📦 [PRICING-CARD] Respuesta data:', data)

      if (!response.ok) {
        throw new Error(data.error || `Error ${response.status}`)
      }

      if (data.url) {
        // Redirigir a Stripe Checkout
        window.location.href = data.url
      } else {
        throw new Error('URL de checkout no devuelta')
      }
    } catch (error) {
      console.error('Error al crear checkout:', error)
      toast.error('Error al crear sesión de pago')
    } finally {
      setLoading(false)
    }
  }

  const handleFreeTrial = async () => {
    setLoadingTrial(true)
    // Redirigir a la página de descarga de la app
    router.push('/mobile-app')
  }

  return (
    <Card className={`relative overflow-hidden ${popular ? 'border-red-500 shadow-2xl shadow-red-500/20' : 'border-gray-800'}`}>
      {popular && (
        <div className="absolute top-0 right-0 bg-gradient-to-br from-red-500 to-orange-500 text-white text-xs font-bold py-1 px-6 rounded-bl-lg">
          MÁS POPULAR
        </div>
      )}
      
      <div className="p-8">
        {/* Header */}
        <div className="text-center mb-6">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-red-500 to-orange-600 rounded-full mb-4">
            <Crown className="h-8 w-8 text-white" />
          </div>
          <h3 className="text-2xl font-bold text-white mb-2">{title}</h3>
          <p className="text-gray-400">{description}</p>
        </div>

        {/* Precio */}
        <div className="text-center mb-8">
          <div className="flex items-baseline justify-center gap-2">
            <span className="text-sm text-gray-400">{currency}</span>
            <span className="text-5xl font-bold text-white">€{price}</span>
            <span className="text-gray-400">/{interval}</span>
          </div>
          <div className="mt-3 text-sm text-gray-400">
            Sin compromisos • Cancela cuando quieras
          </div>
        </div>

        {/* Features */}
        <div className="space-y-4 mb-8">
          {features.map((feature, index) => (
            <div key={index} className="flex items-start gap-3">
              <div className="flex-shrink-0 w-5 h-5 bg-green-500/20 rounded-full flex items-center justify-center mt-0.5">
                <Check className="h-3 w-3 text-green-500" />
              </div>
              <span className="text-gray-300 text-sm">{feature}</span>
            </div>
          ))}
          
          {notIncluded.map((feature, index) => (
            <div key={index} className="flex items-start gap-3 opacity-60">
              <div className="flex-shrink-0 w-5 h-5 bg-gray-700 rounded-full flex items-center justify-center mt-0.5">
                <X className="h-3 w-3 text-gray-500" />
              </div>
              <span className="text-gray-500 text-sm line-through">{feature}</span>
            </div>
          ))}
        </div>

        {/* CTAs - 2 botones */}
        <div className="space-y-3">
          {/* Botón principal - Pagar ahora */}
          <Button
            onClick={handleSubscribe}
            disabled={loading || loadingTrial}
            className="w-full py-6 text-lg font-semibold bg-gradient-to-r from-red-600 to-orange-600 hover:from-red-700 hover:to-orange-700 transform hover:scale-[1.02] transition-all duration-200"
          >
            {loading ? (
              <>
                <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                Procesando...
              </>
            ) : (
              <>
                <CreditCard className="mr-2 h-5 w-5" />
                Suscribirse Ahora - €{price}/mes
              </>
            )}
          </Button>

          {/* Botón secundario - Trial con app */}
          <Button
            onClick={handleFreeTrial}
            disabled={loading || loadingTrial}
            variant="outline"
            className="w-full py-6 text-lg font-semibold border-green-500/50 text-green-500 hover:bg-green-500/10 hover:border-green-500 transform hover:scale-[1.02] transition-all duration-200"
          >
            {loadingTrial ? (
              <>
                <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                Redirigiendo...
              </>
            ) : (
              <>
                <Smartphone className="mr-2 h-5 w-5" />
                Probar 30 Días Gratis (Descarga la App)
              </>
            )}
          </Button>
        </div>

        {/* Garantías */}
        <div className="mt-6 pt-6 border-t border-gray-800">
          <div className="flex items-center justify-center gap-6 text-xs text-gray-500">
            <div className="flex items-center gap-1">
              <Shield className="h-3 w-3" />
              <span>Pago seguro</span>
            </div>
            <div className="flex items-center gap-1">
              <Tv className="h-3 w-3" />
              <span>Cancela cuando quieras</span>
            </div>
          </div>
        </div>
      </div>
    </Card>
  )
}