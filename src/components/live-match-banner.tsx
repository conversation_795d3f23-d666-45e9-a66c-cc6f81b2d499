'use client'

import { useEffect, useState } from 'react'
import { Activity, Calendar } from 'lucide-react'
import { format } from 'date-fns'
import { es } from 'date-fns/locale'
import Link from 'next/link'

export function LiveMatchBanner() {
  const [currentMatchIndex, setCurrentMatchIndex] = useState(0)
  const [loading, setLoading] = useState(true)
  const [liveMatches, setLiveMatches] = useState<any[]>([])
  const [upcomingMatch, setUpcomingMatch] = useState<any>(null)

  useEffect(() => {
    // Tentar buscar jogos reais primeiro
    const fetchRealMatches = async () => {
      try {
        const response = await fetch('/api/sports/live-fixtures')
        const data = await response.json()
        
        if (data.data && data.data.length > 0) {
          // Filtrar apenas jogos ao vivo
          const live = data.data.filter((fixture: any) => {
            const status = fixture.fixture?.status?.short
            return ['LIVE', '1H', '2H', 'HT', 'ET'].includes(status)
          })
          
          if (live.length > 0) {
            setLiveMatches(live.map((fixture: any) => ({
              id: fixture.fixture.id,
              homeTeam: fixture.teams.home.name,
              awayTeam: fixture.teams.away.name,
              homeScore: fixture.goals.home || 0,
              awayScore: fixture.goals.away || 0,
              status: mapStatus(fixture.fixture.status.short),
              minute: fixture.fixture.status.elapsed ? `${fixture.fixture.status.elapsed}'` : '',
              competition: fixture.league.name,
              isLive: true
            })))
          } else {
            // Se não há jogos ao vivo, buscar próximos jogos
            const upcomingResponse = await fetch('/api/sports/upcoming-events')
            const upcomingData = await upcomingResponse.json()
            
            if (upcomingData.data && upcomingData.data.length > 0) {
              const nextMatch = upcomingData.data[0]
              setUpcomingMatch({
                homeTeam: nextMatch.name.split(' vs ')[0] || nextMatch.name,
                awayTeam: nextMatch.name.split(' vs ')[1] || '',
                competition: nextMatch.competition || nextMatch.sport,
                startTime: new Date(nextMatch.dateFrom || nextMatch.date),
                sport: nextMatch.sport
              })
            }
          }
        }
      } catch (error) {
        console.error('Erro ao buscar jogos:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchRealMatches()
    // Atualizar a cada 30 segundos
    const interval = setInterval(fetchRealMatches, 30000)
    
    return () => clearInterval(interval)
  }, [])

  // Alternar entre jogos a cada 5 segundos se houver múltiplos
  useEffect(() => {
    if (liveMatches.length > 1) {
      const interval = setInterval(() => {
        setCurrentMatchIndex((prev) => (prev + 1) % liveMatches.length)
      }, 5000)
      return () => clearInterval(interval)
    }
  }, [liveMatches.length])

  if (loading) {
    return (
      <div className="inline-flex items-center gap-2 bg-gray-500/20 backdrop-blur-sm text-gray-400 px-4 py-2 rounded-full text-sm font-medium border border-gray-500/30">
        <Activity className="h-4 w-4 animate-pulse" />
        Buscando eventos en vivo...
      </div>
    )
  }

  // Se não há jogos ao vivo, mostrar próximo jogo se houver
  if (liveMatches.length === 0 && upcomingMatch) {
    return (
      <Link 
        href="/sports"
        className="inline-flex items-center gap-2 bg-blue-500/20 border-blue-500/30 backdrop-blur-sm text-blue-400 px-4 py-2 rounded-full text-sm font-medium border transition-all hover:scale-105 cursor-pointer"
      >
        <Calendar className="h-4 w-4" />
        <span className="font-bold">PRÓXIMO:</span>
        <span className="text-white">
          {upcomingMatch.homeTeam} vs {upcomingMatch.awayTeam}
        </span>
        <span className="text-xs opacity-80">
          {format(upcomingMatch.startTime, 'HH:mm', { locale: es })}
        </span>
        <span className="text-xs opacity-60 ml-1">• {upcomingMatch.competition}</span>
      </Link>
    )
  }
  
  // Se não há nada, não mostrar
  if (liveMatches.length === 0) {
    return null
  }

  const primaryMatch = liveMatches[currentMatchIndex]

  return (
    <Link 
      href="/sports"
      className="inline-flex items-center gap-2 bg-red-500/20 border-red-500/30 backdrop-blur-sm text-red-400 px-4 py-2 rounded-full text-sm font-medium border transition-all hover:scale-105 cursor-pointer"
    >
      <span className="animate-pulse">🔴</span>
      <span className="font-bold">EN VIVO:</span>
      <span className="text-white">
        {primaryMatch.homeTeam} {primaryMatch.homeScore} - {primaryMatch.awayScore} {primaryMatch.awayTeam}
      </span>
      {primaryMatch.minute && (
        <span className="text-xs opacity-80">{primaryMatch.minute}</span>
      )}
      {primaryMatch.status !== 'En Vivo' && (
        <span className="text-xs opacity-70">({primaryMatch.status})</span>
      )}
      <span className="text-xs opacity-60 ml-1">• {primaryMatch.competition}</span>
      {liveMatches.length > 1 && (
        <span className="text-xs opacity-50 ml-1">+{liveMatches.length - 1}</span>
      )}
    </Link>
  )
}

function mapStatus(status: string): string {
  const statusMap: Record<string, string> = {
    'LIVE': 'En Vivo',
    '1H': 'Primer Tiempo',
    '2H': 'Segundo Tiempo',
    'HT': 'Descanso',
    'ET': 'Tiempo Extra',
    'P': 'Penales',
    'FT': 'Finalizado',
    'NS': 'Por Comenzar'
  }
  return statusMap[status] || status
}