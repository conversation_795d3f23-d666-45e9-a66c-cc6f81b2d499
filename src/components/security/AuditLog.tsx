'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { RefreshCw, Download, AlertTriangle } from 'lucide-react';
import type { AuditLog, AuditAction } from '@/lib/security/audit';

// Mock data para demonstração
const mockLogs: AuditLog[] = [
  {
    id: '1',
    userId: 'user123',
    action: 'login',
    ipAddress: '***********',
    timestamp: new Date(Date.now() - 1000 * 60 * 5),
    severity: 'info',
  },
  {
    id: '2',
    userId: 'user456',
    action: 'login_failed',
    ipAddress: '********',
    timestamp: new Date(Date.now() - 1000 * 60 * 10),
    severity: 'warning',
    metadata: { reason: 'Invalid password' },
  },
  {
    id: '3',
    userId: 'admin',
    action: 'data_deleted',
    path: '/api/users/789',
    timestamp: new Date(Date.now() - 1000 * 60 * 30),
    severity: 'warning',
  },
  {
    id: '4',
    userId: 'hacker',
    action: 'rate_limit_exceeded',
    path: '/api/login',
    ipAddress: '*******',
    timestamp: new Date(Date.now() - 1000 * 60 * 60),
    severity: 'critical',
  },
];

export default function AuditLogViewer() {
  const [logs, setLogs] = useState<AuditLog[]>([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState<string>('all');
  const [severityFilter, setSeverityFilter] = useState<string>('all');

  useEffect(() => {
    fetchAuditLogs();
  }, [filter, severityFilter]);

  const fetchAuditLogs = async () => {
    try {
      setLoading(true);
      
      // TODO: Implementar fetch real quando tivermos API
      // const response = await fetch(`/api/audit/logs?filter=${filter}&severity=${severityFilter}`);
      // const data = await response.json();
      // setLogs(data.logs);
      
      // Por enquanto, usar dados mock
      setTimeout(() => {
        let filteredLogs = [...mockLogs];
        
        if (filter !== 'all') {
          filteredLogs = filteredLogs.filter(log => log.action === filter);
        }
        
        if (severityFilter !== 'all') {
          filteredLogs = filteredLogs.filter(log => log.severity === severityFilter);
        }
        
        setLogs(filteredLogs);
        setLoading(false);
      }, 500);
    } catch (error) {
      console.error('Erro ao buscar logs:', error);
      setLoading(false);
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'info': return 'bg-blue-100 text-blue-800';
      case 'warning': return 'bg-yellow-100 text-yellow-800';
      case 'error': return 'bg-red-100 text-red-800';
      case 'critical': return 'bg-red-600 text-white';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getActionColor = (action: string) => {
    if (action.includes('failed') || action.includes('denied')) {
      return 'bg-red-100 text-red-800';
    }
    if (action.includes('delete')) {
      return 'bg-orange-100 text-orange-800';
    }
    if (action.includes('create') || action.includes('login')) {
      return 'bg-green-100 text-green-800';
    }
    if (action.includes('update')) {
      return 'bg-yellow-100 text-yellow-800';
    }
    return 'bg-blue-100 text-blue-800';
  };

  const exportLogs = () => {
    const csvContent = [
      ['ID', 'User ID', 'Action', 'Path', 'IP Address', 'Timestamp', 'Severity'].join(','),
      ...logs.map(log => [
        log.id,
        log.userId,
        log.action,
        log.path || '',
        log.ipAddress || '',
        log.timestamp.toISOString(),
        log.severity,
      ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `audit-logs-${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
  };

  // Verificar se há atividades críticas
  const criticalCount = logs.filter(log => log.severity === 'critical').length;

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              Logs de Auditoria
              {criticalCount > 0 && (
                <Badge variant="destructive" className="animate-pulse">
                  <AlertTriangle className="w-3 h-3 mr-1" />
                  {criticalCount} Crítico{criticalCount > 1 ? 's' : ''}
                </Badge>
              )}
            </CardTitle>
            <CardDescription>
              Monitoramento de atividades e segurança do sistema
            </CardDescription>
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={fetchAuditLogs}
              disabled={loading}
            >
              <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
              Atualizar
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={exportLogs}
              disabled={logs.length === 0}
            >
              <Download className="w-4 h-4 mr-2" />
              Exportar
            </Button>
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        {/* Filtros */}
        <div className="flex gap-4 mb-6">
          <Select value={filter} onValueChange={setFilter}>
            <SelectTrigger className="w-[200px]">
              <SelectValue placeholder="Filtrar por ação" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Todas as ações</SelectItem>
              <SelectItem value="login">Login</SelectItem>
              <SelectItem value="logout">Logout</SelectItem>
              <SelectItem value="login_failed">Login falhou</SelectItem>
              <SelectItem value="data_accessed">Acesso a dados</SelectItem>
              <SelectItem value="data_created">Criação</SelectItem>
              <SelectItem value="data_updated">Atualização</SelectItem>
              <SelectItem value="data_deleted">Exclusão</SelectItem>
              <SelectItem value="suspicious_activity">Atividade suspeita</SelectItem>
              <SelectItem value="rate_limit_exceeded">Rate limit</SelectItem>
            </SelectContent>
          </Select>

          <Select value={severityFilter} onValueChange={setSeverityFilter}>
            <SelectTrigger className="w-[200px]">
              <SelectValue placeholder="Filtrar por severidade" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Todas</SelectItem>
              <SelectItem value="info">Info</SelectItem>
              <SelectItem value="warning">Aviso</SelectItem>
              <SelectItem value="error">Erro</SelectItem>
              <SelectItem value="critical">Crítico</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Tabela de logs */}
        <div className="overflow-x-auto">
          <table className="w-full text-sm">
            <thead>
              <tr className="border-b">
                <th className="text-left py-3 px-4">Data/Hora</th>
                <th className="text-left py-3 px-4">Usuário</th>
                <th className="text-left py-3 px-4">Ação</th>
                <th className="text-left py-3 px-4">Detalhes</th>
                <th className="text-left py-3 px-4">IP</th>
                <th className="text-left py-3 px-4">Severidade</th>
              </tr>
            </thead>
            <tbody>
              {loading ? (
                <tr>
                  <td colSpan={6} className="text-center py-8 text-muted-foreground">
                    Carregando logs...
                  </td>
                </tr>
              ) : logs.length === 0 ? (
                <tr>
                  <td colSpan={6} className="text-center py-8 text-muted-foreground">
                    Nenhum log encontrado
                  </td>
                </tr>
              ) : (
                logs.map((log) => (
                  <tr key={log.id} className="border-b hover:bg-muted/50">
                    <td className="py-3 px-4 whitespace-nowrap">
                      {log.timestamp.toLocaleString('pt-BR')}
                    </td>
                    <td className="py-3 px-4">
                      <code className="text-xs bg-muted px-1 py-0.5 rounded">
                        {log.userId}
                      </code>
                    </td>
                    <td className="py-3 px-4">
                      <Badge variant="outline" className={getActionColor(log.action)}>
                        {log.action.replace(/_/g, ' ')}
                      </Badge>
                    </td>
                    <td className="py-3 px-4 text-xs">
                      {log.path && <div>Path: {log.path}</div>}
                      {log.metadata && (
                        <div className="text-muted-foreground">
                          {JSON.stringify(log.metadata)}
                        </div>
                      )}
                    </td>
                    <td className="py-3 px-4">
                      {log.ipAddress && (
                        <code className="text-xs">{log.ipAddress}</code>
                      )}
                    </td>
                    <td className="py-3 px-4">
                      <Badge className={getSeverityColor(log.severity)}>
                        {log.severity}
                      </Badge>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </CardContent>
    </Card>
  );
}