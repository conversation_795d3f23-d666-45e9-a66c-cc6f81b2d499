'use client'

import { useEffect, useState } from 'react'
import { useAuth } from '@/hooks/use-auth'
import { useSubscription } from '@/hooks/use-subscription'
import { Card } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'

export function SubscriptionDebug() {
  const { user } = useAuth()
  const subscription = useSubscription()
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
    console.log('[SUBSCRIPTION-DEBUG] Component mounted')
    console.log('[SUBSCRIPTION-DEBUG] User:', user)
    console.log('[SUBSCRIPTION-DEBUG] Subscription:', subscription)
  }, [user, subscription])

  if (!mounted) return null

  // Só mostrar em desenvolvimento
  if (process.env.NODE_ENV !== 'development') return null

  return (
    <Card className="fixed bottom-4 right-4 p-4 bg-black/90 text-white text-xs max-w-sm z-50">
      <h3 className="font-bold mb-2 text-yellow-400">Debug Subscription</h3>
      
      <div className="space-y-1">
        <div>User: {user?.primaryEmail || 'Not logged in'}</div>
        <div>Loading: {subscription.loading ? 'Yes' : 'No'}</div>
        <div>Has Access: {subscription.hasAccess ? 'Yes' : 'No'}</div>
        <div>Is Subscribed: {subscription.isSubscribed ? 'Yes' : 'No'}</div>
        <div>Is In Trial: {subscription.isInTrial ? 'Yes' : 'No'}</div>
        <div>Subscription Tier: {subscription.subscriptionTier || 'None'}</div>
        <div>Ends At: {subscription.subscriptionEndsAt || 'N/A'}</div>
        
        <hr className="my-2 border-gray-600" />
        
        <div className="font-bold">
          Should Show: {
            subscription.isSubscribed ? 
              <Badge className="bg-yellow-600">PREMIUM</Badge> : 
            subscription.isInTrial ? 
              <Badge variant="secondary">TRIAL</Badge> : 
              <span className="text-gray-400">Nothing</span>
          }
        </div>
      </div>
    </Card>
  )
}