'use client'

import { useAccessStore } from '@/stores/access.store'
import { useTrialStore } from '@/stores/trial.store'
import { useUser } from '@stackframe/stack'
import { useEffect } from 'react'

export function AccessDebug() {
  const user = useUser()
  const { hasAccess, accessType, expiresAt, isLoading, checkAccess } = useAccessStore()
  const { startTime, timeRemaining, isExpired } = useTrialStore()
  
  useEffect(() => {
    if (user) {
      // Forçar rechecagem a cada 5 segundos para debug
      const interval = setInterval(() => {
        checkAccess(user.id, true)
      }, 5000)
      
      return () => clearInterval(interval)
    }
  }, [user, checkAccess])
  
  if (process.env.NODE_ENV !== 'development') {
    return null
  }
  
  return (
    <div className="fixed bottom-4 right-4 z-50 bg-black/90 text-white p-4 rounded-lg max-w-sm text-xs font-mono">
      <h3 className="font-bold mb-2 text-yellow-400">DEBUG INFO</h3>
      
      <div className="space-y-1">
        <div className="text-green-400">USER:</div>
        <div>ID: {user?.id || 'null'}</div>
        <div>Email: {user?.primaryEmail || 'null'}</div>
        
        <div className="text-green-400 mt-2">ACCESS:</div>
        <div>hasAccess: {String(hasAccess)}</div>
        <div>accessType: {accessType || 'null'}</div>
        <div>expiresAt: {expiresAt ? (typeof expiresAt === 'string' ? expiresAt : expiresAt.toISOString()) : 'null'}</div>
        <div>isLoading: {String(isLoading)}</div>
        
        <div className="text-green-400 mt-2">TRIAL:</div>
        <div>startTime: {startTime || 'null'}</div>
        <div>timeRemaining: {timeRemaining}s</div>
        <div>isExpired: {String(isExpired)}</div>
        
        <div className="text-green-400 mt-2">COMPUTED:</div>
        <div>shouldShowTimer: {String(!hasAccess && !isExpired)}</div>
        <div>shouldBlock: {String(isExpired && !hasAccess)}</div>
      </div>
      
      <button
        onClick={() => user && checkAccess(user.id, true)}
        className="mt-2 px-2 py-1 bg-blue-600 rounded text-white hover:bg-blue-700"
      >
        Force Refresh
      </button>
    </div>
  )
}