// DESABILITADO - Causando erros de inicialização
// Este componente foi temporariamente desabilitado devido a conflitos com TypeErrorInterceptor

/*
'use client'

import { useEffect } from 'react'
import { ExtensionMonitor } from '@/lib/debug/extension-monitor'
import { TypeErrorInterceptor } from '@/lib/debug/type-error-interceptor'

export function ExtensionMonitorInit() {
  useEffect(() => {
    // Inicializa o monitor apenas no cliente
    if (typeof window !== 'undefined') {
      console.log('🚀 Inicializando ExtensionMonitor...')
      
      // Pequeno delay para garantir que as extensões já carregaram
      setTimeout(() => {
        ExtensionMonitor.init()
        TypeErrorInterceptor.init()
        
        // Adiciona comandos globais para debug
        (window as any).getExtensionErrors = () => {
          const summary = ExtensionMonitor.getErrorSummary()
          console.log('📊 Resumo de erros de extensões:', summary)
          return summary
        }
        
        (window as any).diagnoseTypeError = () => {
          return TypeErrorInterceptor.diagnose()
        }
        
        console.log('💡 Dicas de debug:')
        console.log('  - window.getExtensionErrors() para ver erros capturados')
        console.log('  - window.diagnoseTypeError() para diagnosticar erros de type null')
      }, 1000)
    }
  }, [])
  
  return null
}
*/

// Componente vazio para evitar erros de importação
export function ExtensionMonitorInit() {
  return null
}