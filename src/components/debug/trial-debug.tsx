'use client'

import { useTrialStore } from '@/stores/trial.store'
import { useAccessStore } from '@/stores/access.store'
import { useTrialTimer } from '@/hooks/use-trial-timer'

export function TrialDebug() {
  const trialStore = useTrialStore()
  const accessStore = useAccessStore()
  const { timeRemaining, timeRemainingFormatted } = useTrialTimer()
  
  // Só mostrar em desenvolvimento
  if (process.env.NODE_ENV !== 'development') return null
  
  return (
    <div className="fixed bottom-4 right-4 bg-black/90 text-white p-4 rounded-lg text-xs font-mono max-w-sm z-50">
      <h3 className="font-bold mb-2 text-yellow-400">🔍 TRIAL DEBUG</h3>
      
      <div className="space-y-1">
        <div className="border-b border-gray-700 pb-1 mb-1">
          <strong>Trial State:</strong>
        </div>
        <div>startTime: {trialStore.startTime || 'null'}</div>
        <div>timeRemaining: {timeRemaining}s ({timeRemainingFormatted})</div>
        <div>isExpired: <span className={trialStore.isExpired ? 'text-red-500' : 'text-green-500'}>{String(trialStore.isExpired)}</span></div>
        <div>sessionId: {trialStore.sessionId || 'null'}</div>
        <div>trialId: {trialStore.trialId || 'null'}</div>
        
        <div className="border-b border-gray-700 pb-1 mb-1 mt-2">
          <strong>Access State:</strong>
        </div>
        <div>hasAccess: <span className={accessStore.hasAccess ? 'text-green-500' : 'text-red-500'}>{String(accessStore.hasAccess)}</span></div>
        <div>accessType: {accessStore.accessType || 'null'}</div>
        
        <div className="border-b border-gray-700 pb-1 mb-1 mt-2">
          <strong>Calculated:</strong>
        </div>
        {trialStore.startTime && (
          <div>elapsed: {Math.floor((Date.now() - trialStore.startTime) / 1000)}s</div>
        )}
        <div>shouldBlock: {String(trialStore.isExpired && !accessStore.hasAccess)}</div>
      </div>
      
      <div className="mt-3 space-y-1">
        <button
          onClick={() => {
            localStorage.removeItem('trial-storage')
            localStorage.removeItem('access-storage')
            window.location.reload()
          }}
          className="bg-red-600 px-2 py-1 rounded text-xs w-full"
        >
          Clear Storage & Reload
        </button>
        
        <button
          onClick={() => trialStore.resetTrial()}
          className="bg-blue-600 px-2 py-1 rounded text-xs w-full"
        >
          Reset Trial
        </button>
      </div>
    </div>
  )
}