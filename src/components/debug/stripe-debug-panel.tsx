'use client'

import { useEffect, useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Loader2, RefreshCw, CheckCircle2, XCircle, AlertCircle } from 'lucide-react'

interface StripeDebugData {
  user: any
  stripe_subscription: any
  profile: any
  stack_profile: any
  recent_webhook_events: any[]
  payment_history: any[]
  summary: {
    has_stripe_subscription: boolean
    has_active_subscription: boolean
    profile_has_premium: boolean
    stack_profile_has_premium: boolean
    profile_is_active: boolean
    stack_profile_is_active: boolean
  }
}

export function StripeDebugPanel() {
  const [data, setData] = useState<StripeDebugData | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchDebugData = async () => {
    setLoading(true)
    setError(null)
    
    try {
      const response = await fetch('/api/debug/stripe-status')
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      const data = await response.json()
      setData(data)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch debug data')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchDebugData()
  }, [])

  const getStatusBadge = (isActive: boolean) => {
    return isActive ? (
      <Badge className="bg-green-500">
        <CheckCircle2 className="w-3 h-3 mr-1" />
        Ativo
      </Badge>
    ) : (
      <Badge variant="destructive">
        <XCircle className="w-3 h-3 mr-1" />
        Inativo
      </Badge>
    )
  }

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'N/A'
    return new Date(dateString).toLocaleString('pt-BR')
  }

  if (!process.env.NEXT_PUBLIC_DEBUG) {
    return null
  }

  return (
    <Card className="fixed bottom-4 right-4 w-96 max-h-[600px] overflow-auto shadow-lg z-50">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">Stripe Debug</CardTitle>
        <Button
          size="icon"
          variant="ghost"
          onClick={fetchDebugData}
          disabled={loading}
        >
          {loading ? (
            <Loader2 className="h-4 w-4 animate-spin" />
          ) : (
            <RefreshCw className="h-4 w-4" />
          )}
        </Button>
      </CardHeader>
      <CardContent className="space-y-4">
        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded text-sm">
            {error}
          </div>
        )}

        {data && (
          <>
            {/* Summary */}
            <div className="space-y-2">
              <h3 className="font-semibold text-sm">Resumo</h3>
              <div className="grid grid-cols-2 gap-2 text-xs">
                <div className="flex justify-between">
                  <span>Assinatura Stripe:</span>
                  {data.summary.has_stripe_subscription ? (
                    <CheckCircle2 className="w-4 h-4 text-green-500" />
                  ) : (
                    <XCircle className="w-4 h-4 text-red-500" />
                  )}
                </div>
                <div className="flex justify-between">
                  <span>Assinatura Ativa:</span>
                  {data.summary.has_active_subscription ? (
                    <CheckCircle2 className="w-4 h-4 text-green-500" />
                  ) : (
                    <XCircle className="w-4 h-4 text-red-500" />
                  )}
                </div>
                <div className="flex justify-between">
                  <span>Profile Premium:</span>
                  {data.summary.profile_has_premium ? (
                    <CheckCircle2 className="w-4 h-4 text-green-500" />
                  ) : (
                    <XCircle className="w-4 h-4 text-red-500" />
                  )}
                </div>
                <div className="flex justify-between">
                  <span>Stack Premium:</span>
                  {data.summary.stack_profile_has_premium ? (
                    <CheckCircle2 className="w-4 h-4 text-green-500" />
                  ) : (
                    <XCircle className="w-4 h-4 text-red-500" />
                  )}
                </div>
              </div>
            </div>

            {/* User Info */}
            <div className="space-y-2">
              <h3 className="font-semibold text-sm">Usuário</h3>
              <div className="text-xs space-y-1">
                <p><strong>ID:</strong> {data.user.id}</p>
                <p><strong>Email:</strong> {data.user.email}</p>
              </div>
            </div>

            {/* Profile Info */}
            {data.profile && (
              <div className="space-y-2">
                <h3 className="font-semibold text-sm">Profile</h3>
                <div className="text-xs space-y-1">
                  <p><strong>Tier:</strong> {data.profile.subscription_tier}</p>
                  <p><strong>Status:</strong> {data.profile.subscription_status}</p>
                  <p><strong>Expira em:</strong> {formatDate(data.profile.subscription_current_period_end)}</p>
                  <p><strong>Customer ID:</strong> {data.profile.stripe_customer_id || 'N/A'}</p>
                  <div className="mt-1">{getStatusBadge(data.profile.is_active)}</div>
                </div>
              </div>
            )}

            {/* Stack Profile Info */}
            {data.stack_profile && (
              <div className="space-y-2">
                <h3 className="font-semibold text-sm">Stack Profile</h3>
                <div className="text-xs space-y-1">
                  <p><strong>Tier:</strong> {data.stack_profile.subscription_tier}</p>
                  <p><strong>Status:</strong> {data.stack_profile.subscription_status}</p>
                  <p><strong>Expira em:</strong> {formatDate(data.stack_profile.subscription_current_period_end)}</p>
                  <p><strong>Customer ID:</strong> {data.stack_profile.stripe_customer_id || 'N/A'}</p>
                  <div className="mt-1">{getStatusBadge(data.stack_profile.is_active)}</div>
                </div>
              </div>
            )}

            {/* Recent Webhooks */}
            {data.recent_webhook_events.length > 0 && (
              <div className="space-y-2">
                <h3 className="font-semibold text-sm">Webhooks Recentes</h3>
                <div className="space-y-1">
                  {data.recent_webhook_events.slice(0, 3).map((event, idx) => (
                    <div key={idx} className="text-xs bg-gray-50 p-2 rounded">
                      <p><strong>{event.event_type}</strong></p>
                      <p className="text-gray-600">{formatDate(event.created_at)}</p>
                      <Badge variant={event.status === 'success' ? 'default' : 'destructive'} className="mt-1">
                        {event.status}
                      </Badge>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </>
        )}
      </CardContent>
    </Card>
  )
}