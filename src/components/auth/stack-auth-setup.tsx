"use client";

import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { ExternalLink, Copy, CheckCircle } from "lucide-react";
import { useState } from "react";

export function StackAuthSetup() {
  const [copied, setCopied] = useState(false);

  const handleCopy = (text: string) => {
    navigator.clipboard.writeText(text);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  const envExample = `# Stack Auth - Obtenha em https://app.stack-auth.com
NEXT_PUBLIC_STACK_PROJECT_ID=your-project-id
NEXT_PUBLIC_STACK_PUBLISHABLE_CLIENT_KEY=your-publishable-key
STACK_SECRET_SERVER_KEY=your-secret-key`;

  return (
    <div className="min-h-screen flex items-center justify-center bg-background p-4">
      <Card className="max-w-2xl w-full">
        <CardHeader>
          <CardTitle>Configuração do Stack Auth</CardTitle>
          <CardDescription>
            As variáveis de ambiente do Stack Auth não estão configuradas. Siga os passos abaixo:
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <Alert>
            <AlertDescription>
              Para usar o sistema de autenticação, você precisa configurar as credenciais do Stack Auth.
            </AlertDescription>
          </Alert>

          <div className="space-y-4">
            <h3 className="font-semibold">1. Crie uma conta no Stack Auth</h3>
            <Button variant="outline" asChild>
              <a href="https://app.stack-auth.com" target="_blank" rel="noopener noreferrer">
                Acessar Stack Auth
                <ExternalLink className="ml-2 h-4 w-4" />
              </a>
            </Button>

            <h3 className="font-semibold">2. Crie um novo projeto</h3>
            <p className="text-sm text-muted-foreground">
              Após fazer login, crie um novo projeto e copie as credenciais.
            </p>

            <h3 className="font-semibold">3. Configure o arquivo .env.local</h3>
            <div className="relative">
              <pre className="bg-muted p-4 rounded-lg text-sm overflow-x-auto">
                {envExample}
              </pre>
              <Button
                size="sm"
                variant="outline"
                className="absolute top-2 right-2"
                onClick={() => handleCopy(envExample)}
              >
                {copied ? (
                  <>
                    <CheckCircle className="mr-2 h-4 w-4" />
                    Copiado!
                  </>
                ) : (
                  <>
                    <Copy className="mr-2 h-4 w-4" />
                    Copiar
                  </>
                )}
              </Button>
            </div>

            <h3 className="font-semibold">4. Reinicie o servidor</h3>
            <p className="text-sm text-muted-foreground">
              Após adicionar as variáveis ao .env.local, reinicie o servidor Next.js:
            </p>
            <pre className="bg-muted p-2 rounded text-sm">
              pnpm dev
            </pre>
          </div>

          <Alert variant="destructive">
            <AlertDescription>
              <strong>Importante:</strong> Nunca commite o arquivo .env.local no git. 
              Ele deve estar no .gitignore.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    </div>
  );
}