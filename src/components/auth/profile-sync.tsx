'use client'

import { useEffect, useState } from 'react'
import { useAuthOptional } from '@/hooks/use-auth'
import { createBrowserClient } from '@/lib/supabase/browser-client'

export function ProfileSync() {
  const { user: stackUser } = useAuthOptional()
  const [synced, setSynced] = useState(false)
  
  useEffect(() => {
    async function checkAndSyncProfile() {
      if (synced) return
      
      try {
        // Verificar se há usuário no Supabase
        const supabase = createBrowserClient()
        const { data: { user: supabaseUser } } = await supabase.auth.getUser()
        
        // Só sincronizar se há usuário autenticado no Supabase
        if (supabaseUser) {
          console.log('Usuário Supabase detectado, sincronizando perfil...')
          
          const response = await fetch('/api/auth/sync-profile', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            }
          });
          
          if (!response.ok) {
            const errorText = await response.text();
            console.warn('Aviso: Não foi possível sincronizar perfil:', errorText);
          } else {
            const data = await response.json();
            console.log('Perfil sincronizado:', data);
          }
          
          setSynced(true)
        }
      } catch (error) {
        console.warn('Aviso ao verificar perfil:', error)
      }
    }
    
    checkAndSyncProfile()
  }, [stackUser, synced])
  
  return null
}