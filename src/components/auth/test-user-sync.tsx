"use client";

import { useEffect } from 'react';

export function TestUserSync() {
  useEffect(() => {
    // Sync test user from localStorage to cookies on mount
    const syncTestUser = async () => {
      const storedUser = localStorage.getItem('streamplus-test-user');
      if (storedUser) {
        try {
          const userData = JSON.parse(storedUser);
          await fetch('/api/auth/sync-test-user', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ testUser: userData })
          });
        } catch (error) {
          console.error('Error syncing test user:', error);
        }
      }
    };

    syncTestUser();
  }, []);

  return null;
}