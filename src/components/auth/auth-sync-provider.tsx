'use client'

import { useEffect, useState } from 'react'
import { useUser } from '@stackframe/stack'
import { useAccessStore } from '@/stores/access.store'

export function AuthSyncProvider({ children }: { children: React.ReactNode }) {
  const stackUser = useUser()
  const [synced, setSynced] = useState(false)
  const { checkAccess } = useAccessStore()
  
  useEffect(() => {
    async function syncUser() {
      if (stackUser && !synced) {
        console.log('[AUTH-SYNC] Sincronizando usuário Stack Auth com Supabase...')
        
        try {
          // Chamar API para sincronizar (evita problema de importar código server-side)
          const response = await fetch('/api/sync-stack-user', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ stackUser })
          })
          
          const result = await response.json()
          
          if (result.success) {
            console.log('[AUTH-SYNC] ✅ Sincronização concluída')
            setSynced(true)
            
            // Verificar acesso premium após sincronização
            await checkAccess(stackUser.id)
          } else {
            console.error('[AUTH-SYNC] ❌ Erro na sincronização:', result.error)
          }
        } catch (error) {
          console.error('[AUTH-SYNC] ❌ Erro ao sincronizar:', error)
        }
      }
    }
    
    syncUser()
  }, [stackUser, synced, checkAccess])
  
  return <>{children}</>
}