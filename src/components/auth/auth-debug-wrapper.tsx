'use client'

import { useStackApp } from "@stackframe/stack"
import { AuthLogger } from "@/lib/debug/auth-logger"
import { useEffect } from "react"

interface AuthDebugWrapperProps {
  children: React.ReactNode
}

export function AuthDebugWrapper({ children }: AuthDebugWrapperProps) {
  const app = useStackApp()
  
  useEffect(() => {
    // Intercepta métodos de autenticação para adicionar logs
    if (app && typeof window !== 'undefined') {
      // Intercepta signInWithOAuth
      const originalSignInWithOAuth = app.signInWithOAuth.bind(app)
      app.signInWithOAuth = async (provider: string) => {
        AuthLogger.group(`🔐 Iniciando autenticação OAuth com ${provider}`)
        AuthLogger.log('📦 Provider:', provider)
        AuthLogger.log('🌐 URL de callback configurada:', window.location.origin + '/handler/oauth-callback')
        
        try {
          const result = await originalSignInWithOAuth(provider)
          AuthLogger.log('✅ Redirecionamento OAuth iniciado')
          return result
        } catch (error) {
          AuthLogger.error('❌ Erro ao iniciar OAuth:', error)
          throw error
        } finally {
          AuthLogger.groupEnd()
        }
      }
      
      // Intercepta signInWithCredential
      const originalSignInWithCredential = app.signInWithCredential.bind(app)
      app.signInWithCredential = async (options: any) => {
        AuthLogger.group('🔐 Iniciando autenticação com credenciais')
        AuthLogger.log('📧 Email:', AuthLogger.maskSensitive(options.email))
        AuthLogger.log('🔑 Senha fornecida:', options.password ? 'Sim' : 'Não')
        
        try {
          const result = await originalSignInWithCredential(options)
          
          if (result.status === 'success') {
            AuthLogger.log('✅ Login com credenciais bem-sucedido')
          } else {
            AuthLogger.error('❌ Falha no login:', result.error)
          }
          
          return result
        } catch (error) {
          AuthLogger.error('❌ Erro ao fazer login com credenciais:', error)
          throw error
        } finally {
          AuthLogger.groupEnd()
        }
      }
      
      // Intercepta signUpWithCredential
      const originalSignUpWithCredential = app.signUpWithCredential.bind(app)
      app.signUpWithCredential = async (options: any) => {
        AuthLogger.group('🔐 Iniciando cadastro com credenciais')
        AuthLogger.log('📧 Email:', AuthLogger.maskSensitive(options.email))
        AuthLogger.log('🔑 Senha fornecida:', options.password ? 'Sim' : 'Não')
        
        try {
          const result = await originalSignUpWithCredential(options)
          
          if (result.status === 'success') {
            AuthLogger.log('✅ Cadastro bem-sucedido')
          } else {
            AuthLogger.error('❌ Falha no cadastro:', result.error)
          }
          
          return result
        } catch (error) {
          AuthLogger.error('❌ Erro ao fazer cadastro:', error)
          throw error
        } finally {
          AuthLogger.groupEnd()
        }
      }
      
      // Monitora mudanças no estado de autenticação
      const checkAuthState = async () => {
        try {
          const user = await app.getUser()
          if (user) {
            AuthLogger.debug('👤 Estado de autenticação atual:', {
              id: user.id,
              email: AuthLogger.maskSensitive(user.primaryEmail || ''),
              displayName: user.displayName,
              emailVerified: user.primaryEmailVerified
            })
          } else {
            AuthLogger.debug('👤 Nenhum usuário autenticado')
          }
        } catch (error) {
          AuthLogger.error('❌ Erro ao verificar estado de autenticação:', error)
        }
      }
      
      // Verifica estado inicial
      checkAuthState()
      
      // Monitora mudanças na URL para callbacks OAuth
      const handleUrlChange = () => {
        const url = new URL(window.location.href)
        
        if (url.pathname.includes('oauth-callback')) {
          AuthLogger.group('🔄 OAuth Callback detectado')
          AuthLogger.log('📍 URL:', url.href)
          AuthLogger.log('🔍 Parâmetros:', {
            code: url.searchParams.get('code') ? 'Presente' : 'Ausente',
            state: url.searchParams.get('state') ? 'Presente' : 'Ausente',
            error: url.searchParams.get('error')
          })
          AuthLogger.groupEnd()
        }
      }
      
      // Monitora mudanças na URL
      window.addEventListener('popstate', handleUrlChange)
      
      // Cleanup
      return () => {
        window.removeEventListener('popstate', handleUrlChange)
      }
    }
  }, [app])
  
  return <>{children}</>
}