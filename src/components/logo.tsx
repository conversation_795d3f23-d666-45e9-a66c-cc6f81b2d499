import { cn } from '@/lib/utils'

interface LogoProps {
  className?: string
  size?: 'sm' | 'md' | 'lg' | 'xl'
}

export function Logo({ className, size = 'md' }: LogoProps) {
  const sizeClasses = {
    sm: 'text-lg',
    md: 'text-xl',
    lg: 'text-3xl',
    xl: 'text-5xl'
  }

  const ballSizes = {
    sm: 'h-4 w-4',
    md: 'h-5 w-5',
    lg: 'h-8 w-8',
    xl: 'h-12 w-12'
  }

  return (
    <div className={cn("flex items-center font-bold", sizeClasses[size], className)}>
      <span>NewSpp</span>
      <svg 
        viewBox="0 0 24 24" 
        className={cn("inline-block mx-0.5", ballSizes[size])}
        fill="currentColor"
      >
        {/* Círculo principal da bola */}
        <circle cx="12" cy="12" r="11" stroke="currentColor" strokeWidth="1" fill="none"/>
        
        {/* Padrão de hexágonos da bola de futebol */}
        <path d="M12 2 L9 6 L12 10 L15 6 Z" fill="currentColor" opacity="0.9"/>
        <path d="M9 6 L5 8 L5 12 L9 14 L12 10 Z" fill="currentColor" opacity="0.8"/>
        <path d="M15 6 L12 10 L15 14 L19 12 L19 8 Z" fill="currentColor" opacity="0.8"/>
        <path d="M9 14 L5 12 L5 16 L9 18 L12 14 Z" fill="currentColor" opacity="0.7"/>
        <path d="M15 14 L12 14 L12 18 L15 18 L19 16 L19 12 Z" fill="currentColor" opacity="0.7"/>
        <path d="M12 14 L9 18 L12 22 L15 18 Z" fill="currentColor" opacity="0.9"/>
      </svg>
      <span>rts</span>
    </div>
  )
}

// Versão simplificada para espaços menores
export function LogoSimple({ className, size = 'md' }: LogoProps) {
  const sizeClasses = {
    sm: 'text-lg',
    md: 'text-xl',
    lg: 'text-3xl',
    xl: 'text-5xl'
  }

  const ballSizes = {
    sm: 'h-4 w-4',
    md: 'h-5 w-5',
    lg: 'h-8 w-8',
    xl: 'h-12 w-12'
  }

  return (
    <div className={cn("flex items-center font-bold", sizeClasses[size], className)}>
      <span>NewSpp</span>
      <div className={cn("inline-block mx-0.5 rounded-full border-2 border-current relative", ballSizes[size])}>
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="w-full h-0.5 bg-current transform rotate-45"></div>
          <div className="w-full h-0.5 bg-current transform -rotate-45 absolute"></div>
        </div>
      </div>
      <span>rts</span>
    </div>
  )
}