'use client'

import { Crown } from 'lucide-react'
import { Badge } from '@/components/ui/badge'

interface PremiumBadgeProps {
  size?: 'sm' | 'md' | 'lg'
  showText?: boolean
  expiresAt?: string
}

export function PremiumBadge({ size = 'md', showText = true, expiresAt }: PremiumBadgeProps) {
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-5 w-5',
    lg: 'h-6 w-6'
  }

  const badgeClasses = {
    sm: 'text-xs px-2 py-0.5',
    md: 'text-sm px-2.5 py-0.5',
    lg: 'text-base px-3 py-1'
  }

  const formatExpiryDate = (date: string) => {
    const expiryDate = new Date(date)
    const now = new Date()
    const daysLeft = Math.ceil((expiryDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))
    
    if (daysLeft <= 7) {
      return `${daysLeft} días`
    }
    
    return expiryDate.toLocaleDateString('es-ES', { 
      day: 'numeric', 
      month: 'short', 
      year: 'numeric' 
    })
  }

  return (
    <Badge 
      className={`bg-gradient-to-r from-yellow-600 to-orange-600 text-white border-0 ${badgeClasses[size]} flex items-center gap-1`}
    >
      <Crown className={sizeClasses[size]} />
      {showText && (
        <span className="font-semibold">
          PREMIUM
          {expiresAt && (
            <span className="opacity-80 ml-1 font-normal">
              hasta {formatExpiryDate(expiresAt)}
            </span>
          )}
        </span>
      )}
    </Badge>
  )
}