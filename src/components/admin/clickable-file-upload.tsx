'use client'

import { useRef, ReactNode } from 'react'

interface ClickableFileUploadProps {
  accept?: string
  onChange: (file: File | null) => void
  disabled?: boolean
  children: ReactNode
  className?: string
}

export function ClickableFileUpload({ 
  accept = "*/*", 
  onChange, 
  disabled = false, 
  children,
  className = ''
}: ClickableFileUploadProps) {
  const inputRef = useRef<HTMLInputElement>(null)
  const wrapperRef = useRef<HTMLDivElement>(null)

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] || null
    console.log('[ClickableFileUpload] File selected:', file?.name)
    onChange(file)
    // Reset input to allow selecting the same file again
    if (inputRef.current) {
      inputRef.current.value = ''
    }
  }

  const triggerFileSelect = () => {
    if (!disabled && inputRef.current) {
      console.log('[ClickableFileUpload] Triggering file select')
      inputRef.current.click()
    }
  }

  return (
    <div 
      ref={wrapperRef}
      className={`relative ${className}`}
      onClick={triggerFileSelect}
      onKeyDown={(e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault()
          triggerFileSelect()
        }
      }}
      role="button"
      tabIndex={disabled ? -1 : 0}
      aria-disabled={disabled}
      style={{ cursor: disabled ? 'not-allowed' : 'pointer' }}
    >
      <input
        ref={inputRef}
        type="file"
        accept={accept}
        onChange={handleFileChange}
        className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
        style={{ fontSize: '0' }}
        disabled={disabled}
        aria-hidden="true"
        tabIndex={-1}
      />
      {children}
    </div>
  )
}