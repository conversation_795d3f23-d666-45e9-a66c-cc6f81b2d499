'use client'

import { useState, useRef } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Upload, Loader2, Video, X } from 'lucide-react'
import { toast } from 'sonner'

interface VideoUploadProps {
  currentVideo: string | null
  onVideoUploaded: (url: string) => void
  onVideoRemoved: () => void
}

export function VideoUpload({ currentVideo, onVideoUploaded, onVideoRemoved }: VideoUploadProps) {
  const [uploading, setUploading] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleFileSelect = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return

    if (file.size > 104857600) {
      toast.error('El archivo es muy grande. Máximo 100MB.')
      return
    }

    const validTypes = ['video/mp4', 'video/webm', 'video/ogg', 'video/quicktime']
    if (!validTypes.includes(file.type)) {
      toast.error('Formato de archivo no válido. Use MP4, WebM, OGG o MOV.')
      return
    }

    setUploading(true)
    const formData = new FormData()
    formData.append('file', file)

    try {
      const response = await fetch('/api/admin/upload-video', {
        method: 'POST',
        body: formData,
      })

      if (response.ok) {
        const data = await response.json()
        onVideoUploaded(data.url)
        toast.success('Video subido correctamente')
      } else {
        const error = await response.json()
        throw new Error(error.error || 'Error al subir')
      }
    } catch (error: any) {
      console.error('Error uploading video:', error)
      toast.error(error.message || 'Error al subir el video')
    } finally {
      setUploading(false)
      if (fileInputRef.current) {
        fileInputRef.current.value = ''
      }
    }
  }

  const handleButtonClick = () => {
    fileInputRef.current?.click()
  }

  return (
    <div>
      <Label>Subir Video Tutorial</Label>
      
      {/* Mostrar video actual si existe */}
      {currentVideo && (
        <div className="bg-slate-800 p-4 rounded-lg space-y-2 mb-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Video className="h-5 w-5 text-green-500" />
              <span className="text-sm">Video subido</span>
            </div>
            <Button
              size="sm"
              variant="ghost"
              onClick={() => {
                if (confirm('¿Estás seguro de eliminar el video?')) {
                  onVideoRemoved()
                  toast.success('Video eliminado')
                }
              }}
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
          <a 
            href={currentVideo} 
            target="_blank" 
            rel="noopener noreferrer"
            className="text-sm text-blue-400 hover:underline"
          >
            Ver video actual
          </a>
        </div>
      )}

      <div className="mt-2">
        <input
          ref={fileInputRef}
          type="file"
          accept="video/mp4,video/webm,video/ogg,video/quicktime"
          onChange={handleFileSelect}
          style={{ display: 'none' }}
        />
        
        <Button
          onClick={handleButtonClick}
          disabled={uploading}
          variant="outline"
          className="w-full"
        >
          {uploading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Subiendo...
            </>
          ) : (
            <>
              <Upload className="mr-2 h-4 w-4" />
              Seleccionar Video
            </>
          )}
        </Button>
      </div>
      
      <p className="text-sm text-muted-foreground mt-2">
        Formatos: MP4, WebM, OGG, MOV • Máximo: 100MB
      </p>
    </div>
  )
}