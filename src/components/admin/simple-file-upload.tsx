'use client'

import { useRef } from 'react'
import { Button } from '@/components/ui/button'
import { Upload, Loader2 } from 'lucide-react'

interface SimpleFileUploadProps {
  onFileSelect: (file: File) => Promise<void>
  uploading: boolean
}

export function SimpleFileUpload({ onFileSelect, uploading }: SimpleFileUploadProps) {
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleClick = () => {
    fileInputRef.current?.click()
  }

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      await onFileSelect(file)
      // Reset input
      if (fileInputRef.current) {
        fileInputRef.current.value = ''
      }
    }
  }

  return (
    <>
      <input
        ref={fileInputRef}
        type="file"
        accept="video/mp4,video/webm,video/ogg,video/quicktime"
        onChange={handleFileChange}
        style={{ display: 'none' }}
      />
      <Button
        onClick={handleClick}
        disabled={uploading}
        variant="outline"
        className="w-full"
        type="button"
      >
        {uploading ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            Subiendo...
          </>
        ) : (
          <>
            <Upload className="mr-2 h-4 w-4" />
            Seleccionar Video
          </>
        )}
      </Button>
    </>
  )
}