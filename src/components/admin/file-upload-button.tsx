'use client'

import { useRef, ReactNode } from 'react'
import { Button } from '@/components/ui/button'

interface FileUploadButtonProps {
  accept?: string
  onChange: (file: File | null) => void
  disabled?: boolean
  children: ReactNode
  className?: string
}

export function FileUploadButton({ 
  accept = "*/*", 
  onChange, 
  disabled = false, 
  children,
  className 
}: FileUploadButtonProps) {
  const inputRef = useRef<HTMLInputElement>(null)

  const handleClick = () => {
    console.log('[FileUploadButton] Click triggered')
    if (inputRef.current && !disabled) {
      // Reset the input value to allow selecting the same file again
      inputRef.current.value = ''
      // Trigger click
      inputRef.current.click()
    }
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    console.log('[FileUploadButton] File change triggered')
    const file = e.target.files?.[0] || null
    onChange(file)
  }

  return (
    <>
      <input
        ref={inputRef}
        type="file"
        accept={accept}
        onChange={handleChange}
        style={{
          position: 'fixed',
          top: '-100px',
          left: '-100px',
          width: '1px',
          height: '1px',
          opacity: 0,
          pointerEvents: 'none'
        }}
        tabIndex={-1}
        aria-hidden="true"
      />
      <Button
        type="button"
        onClick={handleClick}
        disabled={disabled}
        variant="outline"
        className={className}
      >
        {children}
      </Button>
    </>
  )
}