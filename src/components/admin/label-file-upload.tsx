'use client'

import { useId } from 'react'
import { Upload, Loader2 } from 'lucide-react'

interface LabelFileUploadProps {
  onFileSelect: (file: File) => Promise<void>
  uploading: boolean
}

export function LabelFileUpload({ onFileSelect, uploading }: LabelFileUploadProps) {
  const inputId = useId()

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      await onFileSelect(file)
      // Reset input
      e.target.value = ''
    }
  }

  return (
    <>
      <label 
        htmlFor={inputId}
        className={`
          inline-flex items-center justify-center w-full
          px-4 py-2 border border-gray-300 rounded-md
          bg-white hover:bg-gray-50 cursor-pointer
          ${uploading ? 'opacity-50 cursor-not-allowed' : ''}
        `}
      >
        {uploading ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            Subiendo...
          </>
        ) : (
          <>
            <Upload className="mr-2 h-4 w-4" />
            Seleccionar Video
          </>
        )}
      </label>
      <input
        id={inputId}
        type="file"
        accept="video/mp4,video/webm,video/ogg,video/quicktime"
        onChange={handleFileChange}
        disabled={uploading}
        className="sr-only"
      />
    </>
  )
}