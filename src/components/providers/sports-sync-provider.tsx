'use client'

import { useEffect } from 'react'
import { getSimpleSyncService } from '@/services/background/simple-sync-service'

export function SportsSyncProvider({ children }: { children: React.ReactNode }) {
  useEffect(() => {
    // Apenas no cliente
    if (typeof window !== 'undefined') {
      console.log('🚀 SportsSyncProvider: Preparando serviço...')
      
      // Iniciar após um pequeno delay
      const timer = setTimeout(() => {
        try {
          const service = getSimpleSyncService()
          if (service) {
            service.start()
            console.log('✅ Serviço iniciado com sucesso')
          }
        } catch (error) {
          console.error('❌ Erro ao iniciar serviço:', error)
        }
      }, 2000) // 2 segundos de delay
      
      // Cleanup
      return () => {
        clearTimeout(timer)
        try {
          const service = getSimpleSyncService()
          if (service) {
            service.stop()
          }
        } catch (error) {
          console.error('❌ Erro ao parar serviço:', error)
        }
      }
    }
  }, [])

  return <>{children}</>
}