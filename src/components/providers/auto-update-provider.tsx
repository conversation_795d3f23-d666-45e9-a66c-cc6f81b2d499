'use client'

import { useEffect } from 'react'
import { autoUpdateService } from '@/services/background/auto-update.service'

export function AutoUpdateProvider({ children }: { children: React.ReactNode }) {
  useEffect(() => {
    console.log('🚀 AutoUpdateProvider: Iniciando serviço de atualização automática')
    
    // Iniciar o serviço
    autoUpdateService.startAll()
    
    // Cleanup ao desmontar
    return () => {
      console.log('🛑 AutoUpdateProvider: Parando serviço de atualização automática')
      autoUpdateService.stopAll()
    }
  }, [])
  
  return <>{children}</>
}