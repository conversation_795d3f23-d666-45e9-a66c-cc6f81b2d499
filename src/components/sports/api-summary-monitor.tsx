'use client'

import { useEffect, useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { RefreshCw, Play, CheckCircle, XCircle } from 'lucide-react'

interface APISummary {
  total: number
  apisSuccess: number
  apisFailed: number
  details?: Array<{
    api: string
    success: boolean
    count: number
  }>
}

export function APISummaryMonitor() {
  const [summary, setSummary] = useState<APISummary | null>(null)
  const [loading, setLoading] = useState(false)
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null)
  
  const fetchData = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/sports/live-fixtures')
      const data = await response.json()
      if (data.summary) {
        setSummary(data.summary)
        setLastUpdate(new Date())
      }
    } catch (error) {
      console.error('Erro ao buscar dados:', error)
    }
    setLoading(false)
  }
  
  useEffect(() => {
    fetchData()
  }, [])
  
  const handleCallAll = async () => {
    setLoading(true)
    await fetchData()
  }
  
  if (!summary) return null
  
  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>Monitor de APIs Sports</span>
          <div className="flex items-center gap-2">
            {lastUpdate && (
              <span className="text-sm text-muted-foreground">
                Última atualização: {lastUpdate.toLocaleTimeString()}
              </span>
            )}
            <Button
              size="sm"
              variant="outline"
              onClick={handleCallAll}
              disabled={loading}
            >
              <RefreshCw className={`h-4 w-4 mr-1 ${loading ? 'animate-spin' : ''}`} />
              Chamar Todas
            </Button>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-3 gap-4 mb-4">
          <div className="text-center">
            <p className="text-2xl font-bold">{summary.apisSuccess}/12</p>
            <p className="text-sm text-muted-foreground">APIs com Sucesso</p>
          </div>
          <div className="text-center">
            <p className="text-2xl font-bold">{summary.apisFailed}</p>
            <p className="text-sm text-muted-foreground">APIs com Falha</p>
          </div>
          <div className="text-center">
            <p className="text-2xl font-bold">{summary.total}</p>
            <p className="text-sm text-muted-foreground">Fixtures ao Vivo</p>
          </div>
        </div>
        
        {summary.details && (
          <div>
            <p className="text-sm font-semibold mb-2">Status por API:</p>
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2">
              {summary.details.map((detail) => (
                <div key={detail.api} className="flex items-center justify-between p-2 bg-muted/50 rounded">
                  <span className="text-sm font-medium">{detail.api}</span>
                  <div className="flex items-center gap-1">
                    {detail.success ? (
                      <CheckCircle className="h-4 w-4 text-green-500" />
                    ) : (
                      <XCircle className="h-4 w-4 text-red-500" />
                    )}
                    <span className="text-sm text-muted-foreground">{detail.count}</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
        
        <div className="mt-4 p-3 bg-muted/30 rounded-lg">
          <p className="text-sm text-muted-foreground">
            As APIs são chamadas automaticamente a cada 15 minutos. 
            Todas as 12 APIs Sports são chamadas simultaneamente para obter os fixtures ao vivo.
          </p>
        </div>
      </CardContent>
    </Card>
  )
}