'use client'

import { Card } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Clock, MapPin, Tv } from 'lucide-react'
import { AllSportEvent, Fixture } from '@/types/sports'
import Link from 'next/link'

interface UpcomingEventCardProps {
  event?: AllSportEvent
  fixture?: Fixture
  compact?: boolean
}

export function UpcomingEventCard({ event, fixture, compact = false }: UpcomingEventCardProps) {
  // Gradientes e estilos visuais para cada esporte
  const sportsStyles = {
    football: {
      gradient: 'from-green-600 to-emerald-700',
      icon: '⚽',
      pattern: 'bg-gradient-to-br'
    },
    basketball: {
      gradient: 'from-orange-500 to-orange-700',
      icon: '🏀',
      pattern: 'bg-gradient-to-tr'
    },
    'formula-1': {
      gradient: 'from-red-600 to-gray-800',
      icon: '🏎️',
      pattern: 'bg-gradient-to-r'
    },
    tennis: {
      gradient: 'from-green-500 to-yellow-600',
      icon: '🎾',
      pattern: 'bg-gradient-to-bl'
    },
    mma: {
      gradient: 'from-gray-700 to-red-900',
      icon: '🥊',
      pattern: 'bg-gradient-to-tl'
    },
    boxing: {
      gradient: 'from-red-700 to-gray-900',
      icon: '🥊',
      pattern: 'bg-gradient-to-br'
    },
    volleyball: {
      gradient: 'from-blue-500 to-cyan-600',
      icon: '🏐',
      pattern: 'bg-gradient-to-tr'
    },
    baseball: {
      gradient: 'from-indigo-600 to-blue-700',
      icon: '⚾',
      pattern: 'bg-gradient-to-r'
    },
    hockey: {
      gradient: 'from-cyan-600 to-blue-800',
      icon: '🏒',
      pattern: 'bg-gradient-to-bl'
    },
    rugby: {
      gradient: 'from-green-700 to-emerald-900',
      icon: '🏉',
      pattern: 'bg-gradient-to-tl'
    },
    nfl: {
      gradient: 'from-purple-600 to-indigo-800',
      icon: '🏈',
      pattern: 'bg-gradient-to-br'
    },
    handball: {
      gradient: 'from-pink-600 to-purple-700',
      icon: '🤾',
      pattern: 'bg-gradient-to-tr'
    },
    motogp: {
      gradient: 'from-yellow-500 to-red-600',
      icon: '🏍️',
      pattern: 'bg-gradient-to-r'
    },
    nba: {
      gradient: 'from-red-600 to-blue-700',
      icon: '🏀',
      pattern: 'bg-gradient-to-bl'
    },
    default: {
      gradient: 'from-gray-600 to-gray-800',
      icon: '🏆',
      pattern: 'bg-gradient-to-br'
    }
  }
  
  const getSportStyle = (sport: string) => {
    // Mapear nomes de esportes em português/inglês para as chaves
    const sportMappings: Record<string, string> = {
      'futebol': 'football',
      'soccer': 'football',
      'basquete': 'basketball',
      'basquetebol': 'basketball',
      'fórmula 1': 'formula-1',
      'f1': 'formula-1',
      'tênis': 'tennis',
      'ténis': 'tennis',
      'boxe': 'boxing',
      'voleibol': 'volleyball',
      'vôlei': 'volleyball',
      'beisebol': 'baseball',
      'hóquei': 'hockey',
      'hoquei': 'hockey',
      'râguebi': 'rugby',
      'futebol americano': 'nfl',
      'handebol': 'handball',
      'andebol': 'handball'
    }
    
    const sportLower = sport.toLowerCase()
    const sportKey = sportMappings[sportLower] || sportLower.replace(/\s+/g, '-')
    return sportsStyles[sportKey as keyof typeof sportsStyles] || sportsStyles.default
  }
  
  if (fixture) {
    // Card para fixtures da API-SPORTS
    const matchDate = new Date(fixture.fixture.date)
    const isToday = new Date().toDateString() === matchDate.toDateString()
    const isTomorrow = new Date(Date.now() + 24 * 60 * 60 * 1000).toDateString() === matchDate.toDateString()
    const style = getSportStyle('football')
    
    return (
      <Link href="/sports">
        <Card className="group cursor-pointer overflow-hidden relative border-slate-700 hover:border-red-500/50 transition-all hover:scale-[1.02]">
          {/* Gradient Background */}
          <div className={`absolute inset-0 ${style.pattern} ${style.gradient} opacity-90 group-hover:opacity-100 transition-opacity`} />
          
          {/* Pattern Overlay */}
          <div className="absolute inset-0 bg-black/20" 
               style={{
                 backgroundImage: `repeating-linear-gradient(45deg, transparent, transparent 35px, rgba(255,255,255,.05) 35px, rgba(255,255,255,.05) 70px)`
               }} 
          />
          
          {/* Sport Icon Watermark */}
          <div className="absolute top-4 right-4 text-6xl opacity-10 group-hover:opacity-20 transition-opacity">
            {style.icon}
          </div>
          
          <div className={`relative ${compact ? 'p-3' : 'p-4'} space-y-2`}>
            {/* Liga e Horário */}
            <div className="flex items-center justify-between text-xs">
              <div className="flex items-center gap-2">
                {fixture.league.logo && (
                  <img 
                    src={fixture.league.logo} 
                    alt={fixture.league.name}
                    className="h-4 w-4 object-contain"
                  />
                )}
                <span className="text-gray-300">{fixture.league.name}</span>
              </div>
              <Badge className={`${isToday ? 'bg-green-600' : 'bg-blue-600'} border-0`}>
                {isToday ? 'Hoje' : isTomorrow ? 'Amanhã' : matchDate.toLocaleDateString('pt-BR', { day: 'numeric', month: 'short' })}
                {' • '}
                {matchDate.toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' })}
              </Badge>
            </div>
            
            {/* Times */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <img 
                    src={fixture.teams.home.logo} 
                    alt={fixture.teams.home.name}
                    className={`${compact ? 'h-5 w-5' : 'h-6 w-6'} object-contain`}
                  />
                  <span className={`${compact ? 'text-sm' : ''} text-white font-medium`}>
                    {fixture.teams.home.name}
                  </span>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <img 
                    src={fixture.teams.away.logo} 
                    alt={fixture.teams.away.name}
                    className={`${compact ? 'h-5 w-5' : 'h-6 w-6'} object-contain`}
                  />
                  <span className={`${compact ? 'text-sm' : ''} text-white font-medium`}>
                    {fixture.teams.away.name}
                  </span>
                </div>
              </div>
            </div>
            
            {/* Local */}
            {fixture.fixture.venue.name && !compact && (
              <div className="flex items-center gap-1 text-xs text-gray-500">
                <MapPin className="h-3 w-3" />
                <span className="truncate">{fixture.fixture.venue.name}</span>
              </div>
            )}
          </div>
        </Card>
      </Link>
    )
  }
  
  if (event) {
    // Card para eventos da AllSportDB
    const eventDate = new Date(event.dateFrom)
    const isToday = new Date().toDateString() === eventDate.toDateString()
    const isTomorrow = new Date(Date.now() + 24 * 60 * 60 * 1000).toDateString() === eventDate.toDateString()
    const style = getSportStyle(event.sport)
    
    return (
      <Link href="/sports">
        <Card className="group cursor-pointer overflow-hidden relative border-slate-700 hover:border-red-500/50 transition-all hover:scale-[1.02]">
          {/* Gradient Background */}
          <div className={`absolute inset-0 ${style.pattern} ${style.gradient} opacity-90 group-hover:opacity-100 transition-opacity`} />
          
          {/* Pattern Overlay */}
          <div className="absolute inset-0 bg-black/20" 
               style={{
                 backgroundImage: `repeating-linear-gradient(45deg, transparent, transparent 35px, rgba(255,255,255,.05) 35px, rgba(255,255,255,.05) 70px)`
               }} 
          />
          
          {/* Sport Icon Watermark */}
          <div className="absolute top-4 right-4 text-6xl opacity-10 group-hover:opacity-20 transition-opacity">
            {style.icon}
          </div>
          
          <div className={`relative ${compact ? 'p-3' : 'p-4'} space-y-2`}>
            {/* Esporte e Data */}
            <div className="flex items-center justify-between text-xs">
              <Badge variant="secondary" className="bg-black/50 backdrop-blur-sm border-0">
                <span className="mr-1">{event.emoji}</span>
                {event.sport}
              </Badge>
              <Badge className={`${isToday ? 'bg-green-600' : 'bg-blue-600'} border-0`}>
                {isToday ? 'Hoje' : isTomorrow ? 'Amanhã' : eventDate.toLocaleDateString('pt-BR', { day: 'numeric', month: 'short' })}
              </Badge>
            </div>
            
            {/* Nome do Evento */}
            <div>
              <h4 className={`${compact ? 'text-sm' : 'text-base'} font-semibold text-white line-clamp-2 group-hover:text-red-400 transition-colors`}>
                {event.name}
              </h4>
              {!compact && (
                <p className="text-xs text-gray-400 mt-1">{event.competition}</p>
              )}
            </div>
            
            {/* Local e Stream */}
            <div className="flex items-center justify-between">
              {event.location && event.location.length > 0 && (
                <div className="flex items-center gap-1 text-xs text-gray-500">
                  <MapPin className="h-3 w-3" />
                  <span className="truncate">
                    {event.location[0].name} {event.location[0].emoji}
                  </span>
                </div>
              )}
              {event.liveUrl && (
                <Badge variant="outline" className="h-5 bg-red-500/10 border-red-500/50 text-red-400">
                  <Tv className="h-3 w-3 mr-1" />
                  TV
                </Badge>
              )}
            </div>
          </div>
        </Card>
      </Link>
    )
  }
  
  return null
}