'use client'

import { Card } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Play, Clock, Users, Trophy } from 'lucide-react'
import { Fixture } from '@/types/sports'
import Link from 'next/link'
import { useRouter } from 'next/navigation'

interface LiveFixtureCardProps {
  fixture: Fixture
  onWatch?: () => void
}

export function LiveFixtureCard({ fixture, onWatch }: LiveFixtureCardProps) {
  const router = useRouter()
  
  const isLive = fixture.fixture.status.short === 'LIVE' || 
                 fixture.fixture.status.short === '1H' || 
                 fixture.fixture.status.short === '2H' ||
                 fixture.fixture.status.short === 'HT'
  
  const getStatusBadge = () => {
    const status = fixture.fixture.status
    let variant: 'default' | 'secondary' | 'destructive' = 'default'
    let className = ''
    
    if (isLive) {
      variant = 'destructive'
      className = 'animate-pulse'
    } else if (status.short === 'FT') {
      variant = 'secondary'
    }
    
    return (
      <Badge variant={variant} className={className}>
        {status.elapsed && isLive ? `${status.elapsed}'` : status.long}
      </Badge>
    )
  }
  
  const handleWatch = () => {
    if (onWatch) {
      onWatch()
    } else {
      // Por ahora, navegar a la página demo
      router.push('/watch/demo')
    }
  }
  
  return (
    <Card className="group overflow-hidden bg-slate-800/50 border-slate-700 hover:border-red-500/50 transition-all">
      <div className="p-4 space-y-4">
        {/* League Info */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {fixture.league.logo && (
              <img 
                src={fixture.league.logo} 
                alt={fixture.league.name}
                className="h-6 w-6 object-contain"
              />
            )}
            <span className="text-sm text-gray-400">{fixture.league.name}</span>
          </div>
          {getStatusBadge()}
        </div>
        
        {/* Match Info */}
        <div className="space-y-3">
          {/* Home Team */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <img 
                src={fixture.teams.home.logo} 
                alt={fixture.teams.home.name}
                className="h-10 w-10 object-contain"
              />
              <div>
                <p className="font-medium text-white">{fixture.teams.home.name}</p>
                {fixture.fixture.venue.name && (
                  <p className="text-xs text-gray-400">{fixture.fixture.venue.name}</p>
                )}
              </div>
            </div>
            <span className="text-2xl font-bold text-white">
              {fixture.goals.home ?? '-'}
            </span>
          </div>
          
          {/* Away Team */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <img 
                src={fixture.teams.away.logo} 
                alt={fixture.teams.away.name}
                className="h-10 w-10 object-contain"
              />
              <p className="font-medium text-white">{fixture.teams.away.name}</p>
            </div>
            <span className="text-2xl font-bold text-white">
              {fixture.goals.away ?? '-'}
            </span>
          </div>
        </div>
        
        {/* Match Details */}
        <div className="flex items-center gap-4 text-xs text-gray-400">
          <div className="flex items-center gap-1">
            <Clock className="h-3 w-3" />
            <span>{new Date(fixture.fixture.date).toLocaleTimeString('es-ES', { hour: '2-digit', minute: '2-digit' })}</span>
          </div>
          {fixture.fixture.referee && (
            <div className="flex items-center gap-1">
              <Users className="h-3 w-3" />
              <span>{fixture.fixture.referee}</span>
            </div>
          )}
        </div>
        
        {/* Action Button */}
        {isLive && (
          <Button 
            size="sm" 
            className="w-full bg-red-600 hover:bg-red-700 text-white"
            onClick={handleWatch}
          >
            <Play className="mr-2 h-4 w-4" />
            Ver en vivo
          </Button>
        )}
      </div>
    </Card>
  )
}