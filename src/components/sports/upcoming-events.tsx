'use client'

import { useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Skeleton } from '@/components/ui/skeleton'
import { Calendar, Clock, MapPin, ExternalLink, RefreshCw } from 'lucide-react'
import { useSportsStore } from '@/stores/sports.store'
import Link from 'next/link'

interface UpcomingEventsProps {
  sport?: string
  limit?: number
  showRefresh?: boolean
}

export function UpcomingEvents({ 
  sport, 
  limit = 10, 
  showRefresh = true 
}: UpcomingEventsProps) {
  const { 
    upcomingEvents, 
    loadingUpcomingEvents, 
    fetchUpcomingEvents 
  } = useSportsStore()

  // Carregar eventos ao montar o componente
  useEffect(() => {
    fetchUpcomingEvents({ sport })
    
    // Atualizar a cada 30 segundos para mostrar dados frescos
    const interval = setInterval(() => {
      fetchUpcomingEvents({ sport })
    }, 30 * 1000)
    
    return () => clearInterval(interval)
  }, [sport, fetchUpcomingEvents])

  // Função para forçar atualização
  const handleRefresh = async () => {
    console.log('🔄 Forçando atualização dos eventos...')
    await fetchUpcomingEvents({ sport })
  }

  // Filtrar eventos por esporte se especificado
  const filteredEvents = sport 
    ? upcomingEvents.filter(e => e.sport.toLowerCase() === sport.toLowerCase())
    : upcomingEvents

  // Limitar quantidade de eventos
  const displayEvents = filteredEvents.slice(0, limit)

  // Formatar data e hora
  const formatDateTime = (dateString: string) => {
    const date = new Date(dateString)
    const today = new Date()
    const tomorrow = new Date(today)
    tomorrow.setDate(tomorrow.getDate() + 1)
    
    let dateLabel = date.toLocaleDateString('pt-BR', { 
      weekday: 'short',
      day: 'numeric',
      month: 'short'
    })
    
    // Adicionar "Hoje" ou "Amanhã"
    if (date.toDateString() === today.toDateString()) {
      dateLabel = 'Hoje'
    } else if (date.toDateString() === tomorrow.toDateString()) {
      dateLabel = 'Amanhã'
    }
    
    const time = date.toLocaleTimeString('pt-BR', { 
      hour: '2-digit', 
      minute: '2-digit' 
    })
    
    return { dateLabel, time }
  }

  // Obter cor do badge por esporte
  const getSportColor = (sportName: string) => {
    const colors: Record<string, string> = {
      football: 'bg-green-600',
      basketball: 'bg-orange-600',
      baseball: 'bg-blue-600',
      hockey: 'bg-cyan-600',
      volleyball: 'bg-purple-600',
      handball: 'bg-pink-600',
      rugby: 'bg-red-600',
      motorsport: 'bg-yellow-600',
      tennis: 'bg-lime-600',
      mma: 'bg-red-800',
      boxing: 'bg-amber-600',
      esports: 'bg-violet-600'
    }
    
    return colors[sportName.toLowerCase()] || 'bg-gray-600'
  }

  if (loadingUpcomingEvents && displayEvents.length === 0) {
    return (
      <div className="space-y-4">
        {[...Array(3)].map((_, i) => (
          <Card key={i} className="bg-slate-800/50 border-slate-700">
            <CardContent className="p-6">
              <Skeleton className="h-6 w-3/4 mb-2" />
              <Skeleton className="h-4 w-1/2 mb-4" />
              <div className="flex gap-4">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-4 w-24" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  if (displayEvents.length === 0) {
    return (
      <Card className="bg-slate-800/50 border-slate-700">
        <CardContent className="p-12 text-center">
          <Calendar className="h-16 w-16 text-gray-500 mx-auto mb-4" />
          <p className="text-gray-400">
            Nenhum evento encontrado para os próximos dias
          </p>
          {showRefresh && (
            <Button 
              onClick={handleRefresh}
              variant="outline"
              className="mt-4 border-white/20"
            >
              <RefreshCw className="mr-2 h-4 w-4" />
              Tentar Novamente
            </Button>
          )}
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {showRefresh && (
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold text-white">
            Próximos Eventos
          </h3>
          <Button
            onClick={handleRefresh}
            variant="ghost"
            size="sm"
            className="text-gray-400 hover:text-white"
            disabled={loadingUpcomingEvents}
          >
            <RefreshCw className={`mr-2 h-4 w-4 ${loadingUpcomingEvents ? 'animate-spin' : ''}`} />
            Atualizar
          </Button>
        </div>
      )}
      
      <div className="grid gap-4">
        {displayEvents.map((event, index) => {
          const { dateLabel, time } = formatDateTime(event.dateFrom)
          // Usar uma chave única combinando ID e índice
          const uniqueKey = `${event.id}-${index}`
          
          return (
            <Card 
              key={uniqueKey}
              className="bg-slate-800/50 border-slate-700 hover:bg-slate-800/70 transition-all"
            >
              <CardContent className="p-6">
                <div className="flex items-start justify-between gap-4">
                  <div className="flex-1">
                    {/* Título do evento */}
                    <h4 className="text-lg font-semibold text-white mb-1 flex items-center gap-2">
                      <span className="text-2xl">{event.emoji}</span>
                      {event.name}
                    </h4>
                    
                    {/* Competição e esporte */}
                    <div className="flex items-center gap-3 mb-3">
                      <Badge 
                        variant="secondary" 
                        className={`${getSportColor(event.sport)} text-white border-0`}
                      >
                        {event.sport}
                      </Badge>
                      <span className="text-sm text-gray-400">
                        {event.competition}
                      </span>
                      {event.country && (
                        <>
                          <span className="text-gray-600">•</span>
                          <span className="text-sm text-gray-400">
                            {event.country}
                          </span>
                        </>
                      )}
                    </div>
                    
                    {/* Informações de data, hora e local */}
                    <div className="flex flex-wrap items-center gap-4 text-sm text-gray-300">
                      <div className="flex items-center gap-1">
                        <Calendar className="h-4 w-4 text-gray-500" />
                        <span>{dateLabel}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Clock className="h-4 w-4 text-gray-500" />
                        <span>{time}</span>
                      </div>
                      {event.location && (
                        <div className="flex items-center gap-1">
                          <MapPin className="h-4 w-4 text-gray-500" />
                          <span className="truncate max-w-[200px]">{event.location}</span>
                        </div>
                      )}
                    </div>
                  </div>
                  
                  {/* Botão de assistir */}
                  <div className="flex-shrink-0">
                    <Button
                      size="sm"
                      className="bg-red-600 hover:bg-red-700"
                      asChild
                    >
                      <Link href={event.liveUrl || '/watch/demo'}>
                        <ExternalLink className="mr-2 h-4 w-4" />
                        Assistir
                      </Link>
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>
      
      {filteredEvents.length > limit && (
        <div className="text-center">
          <Button
            variant="outline"
            className="border-white/20"
            asChild
          >
            <Link href="/sports/calendar">
              Ver todos os {filteredEvents.length} eventos
            </Link>
          </Button>
        </div>
      )}
    </div>
  )
}