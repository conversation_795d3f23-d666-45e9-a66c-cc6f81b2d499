'use client'

import { Card } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Play, Calendar, MapPin, ExternalLink, Ticket } from 'lucide-react'
import { AllSportEvent } from '@/types/sports'
import { useRouter } from 'next/navigation'

interface EventCardProps {
  event: AllSportEvent
  onWatch?: () => void
}

export function EventCard({ event, onWatch }: EventCardProps) {
  const router = useRouter()
  
  const startDate = new Date(event.dateFrom)
  const endDate = new Date(event.dateTo)
  const isMultiDay = startDate.toDateString() !== endDate.toDateString()
  const isLive = new Date() >= startDate && new Date() <= endDate
  const isFuture = new Date() < startDate
  
  const formatDate = () => {
    if (isMultiDay) {
      return `${startDate.toLocaleDateString('es-ES', { day: 'numeric', month: 'short' })} - ${endDate.toLocaleDateString('es-ES', { day: 'numeric', month: 'short', year: 'numeric' })}`
    }
    return startDate.toLocaleDateString('es-ES', { weekday: 'long', day: 'numeric', month: 'long', year: 'numeric' })
  }
  
  const handleWatch = () => {
    if (event.liveUrl && !event.liveUrlPaid) {
      // Si hay URL de transmisión gratuita, abrir en el player
      router.push(`/watch/demo?url=${encodeURIComponent(event.liveUrl)}`)
    } else if (onWatch) {
      onWatch()
    } else {
      router.push('/watch/demo')
    }
  }
  
  return (
    <Card className="group overflow-hidden bg-slate-800/50 border-slate-700 hover:border-red-500/50 transition-all">
      {/* Event Image */}
      {event.logoUrl && (
        <div className="relative aspect-video overflow-hidden">
          <img 
            src={event.logoUrl} 
            alt={event.name}
            className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700"
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black via-transparent to-transparent" />
          
          {/* Sport Badge */}
          <div className="absolute top-4 left-4">
            <Badge className="bg-black/50 backdrop-blur-sm border-0">
              <span className="mr-1">{event.emoji}</span>
              {event.sport}
            </Badge>
          </div>
          
          {/* Live Badge */}
          {isLive && (
            <div className="absolute top-4 right-4">
              <Badge variant="destructive" className="animate-pulse">
                EN VIVO
              </Badge>
            </div>
          )}
        </div>
      )}
      
      <div className="p-4 space-y-3">
        {/* Event Name */}
        <div>
          <h3 className="text-lg font-bold text-white line-clamp-2 group-hover:text-red-400 transition-colors">
            {event.name}
          </h3>
          <p className="text-sm text-gray-400 mt-1">{event.competition}</p>
        </div>
        
        {/* Event Details */}
        <div className="space-y-2 text-sm">
          <div className="flex items-center gap-2 text-gray-400">
            <Calendar className="h-4 w-4" />
            <span>{formatDate()}</span>
          </div>
          
          {event.location && event.location.length > 0 && (
            <div className="flex items-center gap-2 text-gray-400">
              <MapPin className="h-4 w-4" />
              <span className="line-clamp-1">
                {event.location[0].locations?.[0]?.name || event.location[0].name}
                {event.location[0].emoji && ` ${event.location[0].emoji}`}
              </span>
            </div>
          )}
        </div>
        
        {/* Actions */}
        <div className="flex gap-2 pt-2">
          {event.liveUrl && (
            <Button 
              size="sm" 
              className={`flex-1 ${isLive ? 'bg-red-600 hover:bg-red-700' : 'bg-slate-700 hover:bg-slate-600'}`}
              onClick={handleWatch}
              disabled={!isLive && isFuture}
            >
              <Play className="mr-2 h-4 w-4" />
              {isLive ? 'Ver ahora' : isFuture ? 'Próximamente' : 'Ver repetición'}
            </Button>
          )}
          
          {event.ticketsUrl && (
            <Button 
              size="sm" 
              variant="outline"
              className="bg-white/10 border-white/20 text-white hover:bg-white/20"
              onClick={() => window.open(event.ticketsUrl, '_blank')}
            >
              <Ticket className="h-4 w-4" />
            </Button>
          )}
          
          {event.webUrl && (
            <Button 
              size="sm" 
              variant="outline"
              className="bg-white/10 border-white/20 text-white hover:bg-white/20"
              onClick={() => window.open(event.webUrl, '_blank')}
            >
              <ExternalLink className="h-4 w-4" />
            </Button>
          )}
        </div>
      </div>
    </Card>
  )
}