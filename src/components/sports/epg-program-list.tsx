'use client'

import { EPGProgram } from '@/types/sports'
import { Card } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Clock, Play } from 'lucide-react'
import { Button } from '@/components/ui/button'

interface EPGProgramListProps {
  channelId: string
  channelName: string
  programs: EPGProgram[]
  onWatchProgram?: (program: EPGProgram) => void
}

export function EPGProgramList({ channelId, channelName, programs, onWatchProgram }: EPGProgramListProps) {
  const now = new Date()
  
  const isCurrentProgram = (program: EPGProgram, index: number) => {
    const start = new Date(program.start)
    
    // Si es el último programa, verificar solo que haya comenzado
    if (index === programs.length - 1) {
      return start <= now
    }
    
    // Para otros programas, verificar que esté entre inicio y el siguiente
    const nextProgram = programs[index + 1]
    if (nextProgram) {
      const nextStart = new Date(nextProgram.start)
      return start <= now && now < nextStart
    }
    
    return false
  }
  
  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleTimeString('es-ES', { 
      hour: '2-digit', 
      minute: '2-digit' 
    })
  }
  
  const getRelativeTime = (dateString: string) => {
    const programTime = new Date(dateString)
    const diffMs = programTime.getTime() - now.getTime()
    const diffMins = Math.floor(diffMs / 60000)
    
    if (diffMins < 0) {
      return 'En curso'
    } else if (diffMins === 0) {
      return 'Ahora'
    } else if (diffMins < 60) {
      return `En ${diffMins} min`
    } else {
      const hours = Math.floor(diffMins / 60)
      return `En ${hours}h ${diffMins % 60}min`
    }
  }
  
  return (
    <Card className="bg-slate-800/50 border-slate-700">
      <div className="p-4">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-white">{channelName}</h3>
          <Badge variant="outline" className="bg-white/10 border-white/20">
            {programs.length} programas
          </Badge>
        </div>
        
        <div className="space-y-2">
          {programs.map((program, index) => {
            const isCurrent = isCurrentProgram(program, index)
            const isPast = new Date(program.start) < now && !isCurrent
            
            return (
              <div
                key={`${channelId}-${index}`}
                className={`
                  p-3 rounded-lg transition-all
                  ${isCurrent ? 'bg-red-900/30 border border-red-500/50' : 'bg-slate-700/30 hover:bg-slate-700/50'}
                  ${isPast ? 'opacity-50' : ''}
                `}
              >
                <div className="flex items-start justify-between gap-3">
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <Clock className="h-4 w-4 text-gray-400" />
                      <span className="text-sm font-medium text-gray-300">
                        {formatTime(program.start)}
                      </span>
                      {isCurrent && (
                        <Badge variant="destructive" className="h-5 animate-pulse">
                          EN VIVO
                        </Badge>
                      )}
                      {!isPast && !isCurrent && (
                        <span className="text-xs text-gray-500">
                          {getRelativeTime(program.start)}
                        </span>
                      )}
                    </div>
                    
                    <h4 className="font-medium text-white line-clamp-1">
                      {program.title}
                    </h4>
                    
                    {program.description && (
                      <p className="text-sm text-gray-400 line-clamp-2 mt-1">
                        {program.description}
                      </p>
                    )}
                    
                    {program.category && (
                      <Badge 
                        variant="secondary" 
                        className="mt-2 bg-white/10 text-gray-300 border-0"
                      >
                        {program.category}
                      </Badge>
                    )}
                  </div>
                  
                  {isCurrent && onWatchProgram && (
                    <Button
                      size="sm"
                      className="bg-red-600 hover:bg-red-700 text-white shrink-0"
                      onClick={() => onWatchProgram(program)}
                    >
                      <Play className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              </div>
            )
          })}
        </div>
      </div>
    </Card>
  )
}