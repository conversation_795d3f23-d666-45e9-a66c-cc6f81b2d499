'use client'

import { useEffect, useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { RefreshCw, Play, Zap } from 'lucide-react'

interface RotationStats {
  currentAPI: string
  nextAPI: string
  apisAvailable: number
  nextAvailableIn: string
  callHistory: Record<string, string>
}

export function APIRotationMonitor() {
  const [stats, setStats] = useState<RotationStats | null>(null)
  const [loading, setLoading] = useState(false)
  
  const fetchStats = async () => {
    try {
      const response = await fetch('/api/sports/live-fixtures')
      const data = await response.json()
      if (data.rotation) {
        setStats(data.rotation)
      }
    } catch (error) {
      console.error('Erro ao buscar estatísticas:', error)
    }
  }
  
  useEffect(() => {
    fetchStats()
    const interval = setInterval(fetchStats, 10000) // Atualizar a cada 10 segundos
    return () => clearInterval(interval)
  }, [])
  
  const handleReset = async () => {
    setLoading(true)
    try {
      await (window as any).resetRotation()
      await fetchStats()
    } catch (error) {
      console.error('Erro ao resetar:', error)
    }
    setLoading(false)
  }
  
  const handleCallAll = async () => {
    setLoading(true)
    try {
      await (window as any).callAllAPIs()
      await fetchStats()
    } catch (error) {
      console.error('Erro ao chamar APIs:', error)
    }
    setLoading(false)
  }
  
  if (!stats) return null
  
  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>Monitor de Rotação de APIs</span>
          <div className="flex gap-2">
            <Button
              size="sm"
              variant="outline"
              onClick={handleReset}
              disabled={loading}
            >
              <RefreshCw className="h-4 w-4 mr-1" />
              Resetar
            </Button>
            <Button
              size="sm"
              variant="outline"
              onClick={handleCallAll}
              disabled={loading}
            >
              <Zap className="h-4 w-4 mr-1" />
              Chamar Todas
            </Button>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
          <div>
            <p className="text-sm text-muted-foreground">API Atual</p>
            <p className="font-semibold">{stats.currentAPI}</p>
          </div>
          <div>
            <p className="text-sm text-muted-foreground">Próxima API</p>
            <p className="font-semibold">{stats.nextAPI}</p>
          </div>
          <div>
            <p className="text-sm text-muted-foreground">APIs Disponíveis</p>
            <p className="font-semibold">{stats.apisAvailable}/12</p>
          </div>
          <div>
            <p className="text-sm text-muted-foreground">Próxima em</p>
            <p className="font-semibold">{stats.nextAvailableIn}</p>
          </div>
        </div>
        
        {stats.callHistory && Object.keys(stats.callHistory).length > 0 && (
          <div>
            <p className="text-sm text-muted-foreground mb-2">Histórico de Chamadas</p>
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2 text-sm">
              {Object.entries(stats.callHistory).map(([api, time]) => (
                <div key={api} className="flex justify-between">
                  <span className="font-medium">{api}:</span>
                  <span className="text-muted-foreground">{time}</span>
                </div>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}