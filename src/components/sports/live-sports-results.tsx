'use client'

import { useEffect, useState } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Activity, Trophy, Clock, MapPin, Users, TrendingUp, Calendar, ChevronRight, Play } from 'lucide-react'
import { Fixture, AllSportEvent } from '@/types/sports'
import Link from 'next/link'
import { SportsPlayerModal } from './sports-player-modal'
import { liveStreamsService } from '@/services/api/sports/live-streams.service'
import { useSimpleAuth } from '@/hooks/use-simple-auth'
import { useRouter } from 'next/navigation'
import { format } from 'date-fns'
import { es } from 'date-fns/locale'

export function LiveSportsResults() {
  const [liveEvents, setLiveEvents] = useState<AllSportEvent[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [selectedEvent, setSelectedEvent] = useState<any>(null)
  const [isPlayerOpen, setIsPlayerOpen] = useState(false)
  const { user } = useSimpleAuth()
  const router = useRouter()
  const [isLive, setIsLive] = useState(true)

  useEffect(() => {
    const fetchLiveEvents = async () => {
      try {
        setLoading(true)
        setError(null)
        
        // Buscar eventos ao vivo da AllSportDB
        const response = await fetch('/api/sports/live-events')
        const data = await response.json()
        
        if (data.success && data.data) {
          console.log('📊 Eventos AllSportDB:', data.data.length)
          console.log('🎯 Ao vivo?', data.isLive)
          
          setLiveEvents(data.data.slice(0, 6))
          setIsLive(data.isLive)
        } else {
          setError(data.error || 'Erro ao carregar eventos')
        }
      } catch (err) {
        console.error('Erro ao buscar eventos:', err)
        setError('Erro ao carregar eventos')
      } finally {
        setLoading(false)
      }
    }

    fetchLiveEvents()
    
    // Atualizar a cada 30 segundos
    const interval = setInterval(fetchLiveEvents, 30000)
    
    return () => clearInterval(interval)
  }, [])

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'LIVE':
      case '1H':
      case '2H':
        return (
          <Badge className="bg-red-600 border-0 animate-pulse">
            <span className="mr-1">●</span>
            AO VIVO
          </Badge>
        )
      case 'HT':
        return (
          <Badge className="bg-orange-600 border-0">
            INTERVALO
          </Badge>
        )
      case 'ET':
        return (
          <Badge className="bg-yellow-600 border-0">
            PRORROGAÇÃO
          </Badge>
        )
      case 'P':
        return (
          <Badge className="bg-purple-600 border-0">
            PÊNALTIS
          </Badge>
        )
      default:
        return null
    }
  }

  const getSportEmoji = (sport: string): string => {
    const sportEmojis: Record<string, string> = {
      'Football': '⚽',
      'Soccer': '⚽',
      'Basketball': '🏀',
      'Tennis': '🎾',
      'Baseball': '⚾',
      'Hockey': '🏒',
      'Ice Hockey': '🏒',
      'Boxing': '🥊',
      'MMA': '🥋',
      'Golf': '⛳',
      'Rugby': '🏉',
      'Cricket': '🏏',
      'Chess': '♟',
      'Formula 1': '🏎️',
      'Volleyball': '🏐',
      'Handball': '🤾',
      'Swimming': '🏊',
      'Athletics': '🏃',
      'Cycling': '🚴',
      'Motorsport': '🏍️',
      'Esports': '🎮'
    }
    return sportEmojis[sport] || '🏆'
  }

  const handleWatchClick = async (event: AllSportEvent) => {
    // SEMPRE redirecionar para login ao clicar
    if (!user) {
      router.push('/login')
      return
    }
    
    // Buscar stream para o evento
    const eventData = {
      idEvent: event.id.toString(),
      strEvent: event.name,
      strSport: event.sport
    }
    const stream = await liveStreamsService.getSportsEventStream(eventData as any)
    
    setSelectedEvent({
      id: event.id.toString(),
      name: event.name,
      league: event.competition,
      streamUrl: stream?.url || process.env.NEXT_PUBLIC_STREAM_URL || '',
      isIPTV: false
    })
    
    setIsPlayerOpen(true)
  }

  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {[...Array(6)].map((_, i) => (
          <Card key={i} className="bg-slate-800/50 animate-pulse">
            <CardContent className="p-6">
              <div className="h-20 bg-slate-700 rounded" />
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  if (error) {
    return (
      <Card className="bg-red-900/20 border-red-500/50">
        <CardContent className="p-8 text-center">
          <Activity className="h-12 w-12 text-red-500 mx-auto mb-3" />
          <p className="text-red-400">{error}</p>
        </CardContent>
      </Card>
    )
  }

  if (liveEvents.length === 0 && !loading) {
    return (
      <Card className="bg-slate-800/30 border-slate-700">
        <CardContent className="p-12 text-center">
          <div className="inline-flex items-center justify-center w-20 h-20 rounded-full bg-slate-700/50 mb-4">
            <Activity className="h-10 w-10 text-slate-400" />
          </div>
          <h3 className="text-xl font-semibold text-white mb-2">Nenhum evento ao vivo</h3>
          <p className="text-slate-400 mb-6">
            Não há eventos esportivos acontecendo neste momento
          </p>
          <p className="text-xs text-slate-500">
            Verificado em: {new Date().toLocaleTimeString('pt-BR')}
          </p>
          <Link href="/sports">
            <Badge className="bg-slate-700 hover:bg-slate-600 text-white px-4 py-2 cursor-pointer">
              Ver próximos eventos
              <ChevronRight className="ml-1 h-4 w-4" />
            </Badge>
          </Link>
        </CardContent>
      </Card>
    )
  }

  return (
    <>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {liveEvents.map((event, index) => {
          const eventDate = new Date(event.dateFrom)
          const sportEmoji = getSportEmoji(event.sport)
          
          return (
            <Card 
              key={`${event.id}-${index}`} 
              className="group relative overflow-hidden bg-slate-800/50 border-slate-700 hover:border-red-500/50 transition-all duration-300 hover:shadow-lg hover:shadow-red-500/20"
            >
              <CardContent className="relative p-6">
                {/* Header com esporte e status */}
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-2">
                    <span className="text-2xl">{sportEmoji}</span>
                    <span className="text-xs text-slate-400 truncate max-w-[150px]">
                      {typeof event.competition === 'string' ? event.competition : event.competition?.name || event.sport}
                    </span>
                  </div>
                  {isLive ? (
                    <Badge className="bg-red-600 border-0 animate-pulse">
                      <span className="mr-1">●</span>
                      AO VIVO
                    </Badge>
                  ) : (
                    <Badge className="bg-blue-600 border-0">
                      <Calendar className="h-3 w-3 mr-1" />
                      {format(eventDate, 'HH:mm', { locale: es })}
                    </Badge>
                  )}
                </div>
                
                {/* Nome do evento */}
                <div className="space-y-3">
                  <div className="text-center">
                    <h3 className="text-lg font-semibold text-white leading-tight">
                      {event.name}
                    </h3>
                  </div>
                </div>
                
                {/* Informações adicionais */}
                <div className="mt-4 pt-4 border-t border-slate-700">
                  <div className="space-y-2">
                    {/* Local */}
                    {event.location && (
                      <div className="flex items-center gap-2 text-xs text-slate-400">
                        <MapPin className="h-3 w-3" />
                        <span className="truncate">
                          {typeof event.location === 'string' ? event.location : event.location?.name || ''}
                          {event.country && `, ${typeof event.country === 'string' ? event.country : event.country.name || ''}`}
                        </span>
                      </div>
                    )}
                    
                    {/* Data e hora */}
                    <div className="flex items-center gap-2 text-xs text-slate-400">
                      <Clock className="h-3 w-3" />
                      <span>
                        {format(eventDate, "d 'de' MMMM 'às' HH:mm", { locale: es })}
                      </span>
                    </div>
                  </div>
                  
                  {/* Botão de assistir */}
                  <div className="mt-4 flex justify-center">
                    <button
                      onClick={() => handleWatchClick(event)}
                      className="flex items-center"
                    >
                      <Badge 
                        variant="default" 
                        className="bg-red-600 hover:bg-red-700 cursor-pointer flex items-center gap-1 px-4 py-2"
                      >
                        <Play className="h-3 w-3" />
                        {isLive ? 'Ver ao vivo' : 'Detalhes'}
                      </Badge>
                    </button>
                  </div>
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>
      
      {/* Player Modal - Só renderizar se usuário estiver logado */}
      {selectedEvent && user && (
        <SportsPlayerModal
          isOpen={isPlayerOpen}
          onClose={() => {
            setIsPlayerOpen(false)
            setSelectedEvent(null)
          }}
          event={selectedEvent}
        />
      )}
    </>
  )
}