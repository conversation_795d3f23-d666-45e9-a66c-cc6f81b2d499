'use client'

import { useEffect, useState } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Activity, Trophy, Calendar, MapPin, Globe, Play, ExternalLink, Lock } from 'lucide-react'
import { Event } from '@/types/sports'
import Link from 'next/link'
import { SportsPlayerModal } from './sports-player-modal'
import { format } from 'date-fns'
import { es } from 'date-fns/locale'
import { useSimpleAuth } from '@/hooks/use-simple-auth'
import { toast } from 'sonner'
import { useRouter } from 'next/navigation'

export function AllSportsEvents() {
  const [events, setEvents] = useState<Event[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [selectedEvent, setSelectedEvent] = useState<any>(null)
  const [isPlayerOpen, setIsPlayerOpen] = useState(false)
  const { user, requireAuth } = useSimpleAuth()
  const router = useRouter()

  useEffect(() => {
    const fetchEvents = async () => {
      try {
        setLoading(true)
        setError(null)
        
        // Usar o endpoint force-update que já busca da AllSportsDB
        const response = await fetch('/api/sports/force-update')
        const data = await response.json()
        
        if (data.success && data.data?.allSports) {
          setEvents(data.data.allSports)
        } else {
          setError('No se pudieron cargar los eventos')
        }
      } catch (err) {
        console.error('Error al buscar eventos:', err)
        setError('Error al cargar eventos')
      } finally {
        setLoading(false)
      }
    }

    fetchEvents()
  }, [])

  const getSportEmoji = (sport: string) => {
    const sportEmojis: Record<string, string> = {
      'Football': '⚽',
      'Basketball': '🏀',
      'Tennis': '🎾',
      'Baseball': '⚾',
      'Hockey': '🏒',
      'Volleyball': '🏐',
      'Rugby': '🏉',
      'Boxing': '🥊',
      'MMA': '🥋',
      'Golf': '⛳',
      'Cricket': '🏏',
      'Cycling': '🚴',
      'Swimming': '🏊',
      'Athletics': '🏃',
      'Chess': '♟️',
      'Water Polo': '🤽',
      'Multi-Sport Events': '🔥'
    }
    return sportEmojis[sport] || '🏆'
  }

  const handleWatchClick = (event: Event) => {
    // SEMPRE redirecionar para login ao clicar em qualquer evento
    if (!user) {
      // Não mostrar toast, apenas redirecionar
      router.push('/login')
      return
    }
    
    // Se estiver logado, abre o player
    setSelectedEvent({
      id: event.id.toString(),
      name: event.name,
      league: event.competition,
      streamUrl: '', // Será preenchido pelo serviço de streams
      isIPTV: false
    })
    setIsPlayerOpen(true)
  }

  const isEventLive = (event: Event) => {
    const now = new Date()
    const start = new Date(event.dateFrom)
    const end = event.dateTo ? new Date(event.dateTo) : new Date(start.getTime() + 3 * 60 * 60 * 1000)
    return now >= start && now <= end
  }

  const formatEventDate = (event: Event) => {
    const start = new Date(event.dateFrom)
    const end = event.dateTo ? new Date(event.dateTo) : null
    
    if (end && start.toDateString() !== end.toDateString()) {
      return `${format(start, 'd MMM', { locale: es })} - ${format(end, 'd MMM yyyy', { locale: es })}`
    }
    return format(start, "d 'de' MMMM yyyy", { locale: es })
  }

  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {[...Array(6)].map((_, i) => (
          <Card key={i} className="bg-slate-800/50 animate-pulse">
            <CardContent className="p-6">
              <div className="h-32 bg-slate-700 rounded" />
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  if (error) {
    return (
      <Card className="bg-red-900/20 border-red-500/50">
        <CardContent className="p-8 text-center">
          <Activity className="h-12 w-12 text-red-500 mx-auto mb-3" />
          <p className="text-red-400">{error}</p>
        </CardContent>
      </Card>
    )
  }

  if (events.length === 0) {
    return (
      <Card className="bg-slate-800/30 border-slate-700">
        <CardContent className="p-12 text-center">
          <Trophy className="h-16 w-16 text-slate-400 mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-white mb-2">No hay eventos disponibles</h3>
          <p className="text-slate-400">
            Vuelve más tarde para ver los próximos eventos deportivos
          </p>
        </CardContent>
      </Card>
    )
  }

  return (
    <>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {events.map((event, index) => {
          const isLive = isEventLive(event)
          // Usar uma chave única combinando ID e índice
          const uniqueKey = `${event.id}-${index}`
          
          return (
            <Card 
              key={uniqueKey} 
              className="group relative overflow-hidden bg-slate-800/50 border-slate-700 hover:border-red-500/50 transition-all duration-300 hover:shadow-lg hover:shadow-red-500/20"
            >
              <CardContent className="relative p-6">
                {/* Header con deporte y estado */}
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-2">
                    <span className="text-2xl">{getSportEmoji(event.sport)}</span>
                    <span className="text-sm text-slate-400">{event.sport}</span>
                  </div>
                  {isLive ? (
                    <Badge className="bg-red-600 border-0 animate-pulse">
                      <span className="mr-1">●</span>
                      EN VIVO
                    </Badge>
                  ) : (
                    <Badge className="bg-slate-600 border-0">
                      PRÓXIMO
                    </Badge>
                  )}
                </div>
                
                {/* Nombre del evento */}
                <h3 className="font-semibold text-white text-lg mb-2 line-clamp-2">
                  {event.name}
                </h3>
                
                {/* Competición */}
                <p className="text-sm text-gray-400 mb-3">
                  {event.competition}
                </p>
                
                {/* Fecha y ubicación */}
                <div className="space-y-2 text-sm">
                  <div className="flex items-center gap-2 text-slate-400">
                    <Calendar className="h-4 w-4" />
                    <span>{formatEventDate(event)}</span>
                  </div>
                  
                  {event.location && event.location.length > 0 && (
                    <div className="flex items-center gap-2 text-slate-400">
                      <MapPin className="h-4 w-4" />
                      <span className="truncate">
                        {event.location[0].name} {event.location[0].emoji}
                      </span>
                    </div>
                  )}
                </div>
                
                {/* Acciones */}
                <div className="mt-4 pt-4 border-t border-slate-700 flex items-center justify-between">
                  {isLive ? (
                    <button
                      onClick={() => handleWatchClick(event)}
                      className="flex items-center"
                    >
                      <Badge 
                        variant="default" 
                        className="bg-red-600 hover:bg-red-700 cursor-pointer flex items-center gap-1"
                      >
                        <Play className="h-3 w-3" />
                        Ver ahora
                      </Badge>
                    </button>
                  ) : (
                    <Badge variant="secondary" className="bg-slate-700">
                      <Calendar className="h-3 w-3 mr-1" />
                      Programado
                    </Badge>
                  )}
                  
                  {event.webUrl && (
                    <a 
                      href={event.webUrl} 
                      target="_blank" 
                      rel="noopener noreferrer"
                      className="text-slate-400 hover:text-white transition-colors"
                    >
                      <ExternalLink className="h-4 w-4" />
                    </a>
                  )}
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>
      
      {/* Player Modal */}
      {/* Player Modal - Só renderizar se usuário estiver logado */}
      {selectedEvent && user && (
        <SportsPlayerModal
          isOpen={isPlayerOpen}
          onClose={() => {
            setIsPlayerOpen(false)
            setSelectedEvent(null)
          }}
          event={selectedEvent}
        />
      )}
    </>
  )
}