'use client'

import { useState, useEffect } from 'react'
import { <PERSON><PERSON>, <PERSON>alogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { UniversalPlayer } from '@/components/player/universal-player'
import { IPTVStreamPlayer } from '@/components/player/iptv-stream-player'
import { X, Download, CreditCard, Clock, Smartphone, Trophy } from 'lucide-react'
import Link from 'next/link'
import { useTrialStore } from '@/stores/trial.store'
import { useSimpleAuth } from '@/hooks/use-simple-auth'
import { useRouter } from 'next/navigation'

interface SportsPlayerModalProps {
  isOpen: boolean
  onClose: () => void
  event: {
    id: string
    name: string
    league?: string
    streamUrl?: string
    isIPTV?: boolean
    logo?: string
    headers?: {
      'Referer'?: string
      'User-Agent'?: string
    }
  }
}

export function SportsPlayerModal({ isOpen, onClose, event }: SportsPlayerModalProps) {
  const [timeRemaining, setTimeRemaining] = useState(5 * 60) // 5 minutos em segundos
  const [showTrialOverlay, setShowTrialOverlay] = useState(false)
  const { resetTrial } = useTrialStore()
  const { user } = useSimpleAuth()
  const router = useRouter()
  
  // Proteção dupla - verificar usuário E se está aberto
  useEffect(() => {
    if (isOpen && !user) {
      console.log('⚠️ Player modal tentou abrir sem usuário - redirecionando')
      onClose()
      router.push('/login')
    }
  }, [isOpen, user, onClose, router])
  
  // Não renderizar se não estiver aberto ou sem usuário
  if (!isOpen || !user) return null

  useEffect(() => {
    if (!isOpen) {
      // Reset quando modal fecha
      setTimeRemaining(5 * 60)
      setShowTrialOverlay(false)
      return
    }

    // Reset trial quando abre
    resetTrial()

    // Configurar timer de 5 minutos
    const interval = setInterval(() => {
      setTimeRemaining((prev) => {
        if (prev <= 1) {
          setShowTrialOverlay(true)
          clearInterval(interval)
          return 0
        }
        return prev - 1
      })
    }, 1000)

    return () => clearInterval(interval)
  }, [isOpen, resetTrial])

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  const handleClose = () => {
    onClose()
    // Resetar estado
    setTimeRemaining(5 * 60)
    setShowTrialOverlay(false)
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-6xl p-0 bg-black overflow-hidden">
        <div className="relative">
          {/* Header */}
          <div className="absolute top-0 left-0 right-0 z-20 bg-gradient-to-b from-black/80 to-transparent p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <DialogTitle className="text-white text-xl font-semibold">
                  {event.name}
                </DialogTitle>
                {event.league && (
                  <Badge className="bg-red-600 border-0">
                    {event.league}
                  </Badge>
                )}
              </div>
              
              <div className="flex items-center gap-4">
                {!showTrialOverlay && (
                  <div className="flex items-center gap-2 bg-black/50 px-3 py-1.5 rounded-full">
                    <Clock className="h-4 w-4 text-yellow-500" />
                    <span className="text-white text-sm font-medium">
                      Prueba gratis: {formatTime(timeRemaining)}
                    </span>
                  </div>
                )}
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={handleClose}
                  className="text-white hover:bg-white/20"
                >
                  <X className="h-5 w-5" />
                </Button>
              </div>
            </div>
          </div>

          {/* Player */}
          <div className="aspect-video relative bg-black">
            {!showTrialOverlay && (
              event.isIPTV ? (
                <IPTVStreamPlayer
                  channelName={event.name}
                  streamUrl={event.streamUrl}
                  headers={event.headers}
                  logo={event.logo}
                />
              ) : (
                <UniversalPlayer 
                  channelName={event.name}
                  streamUrl={event.streamUrl}
                  isLive={true}
                />
              )
            )}

            {/* Trial Overlay */}
            {showTrialOverlay && (
              <div className="absolute inset-0 bg-black/95 backdrop-blur-md flex items-center justify-center z-30">
                <div className="max-w-md w-full mx-4 text-center space-y-6">
                  <div className="bg-gradient-to-br from-red-600 to-orange-600 w-20 h-20 rounded-full flex items-center justify-center mx-auto">
                    <Trophy className="h-10 w-10 text-white" />
                  </div>
                  
                  <div>
                    <h3 className="text-3xl font-bold text-white mb-2">
                      ¡Tu prueba gratuita ha terminado!
                    </h3>
                    <p className="text-gray-300 text-lg">
                      Obtén acceso ilimitado a todos los deportes en vivo
                    </p>
                  </div>

                  <div className="space-y-3">
                    <Link href="/mobile-app" className="block">
                      <Button size="lg" className="w-full bg-gradient-to-r from-red-600 to-orange-600 hover:from-red-700 hover:to-orange-700">
                        <Smartphone className="mr-2 h-5 w-5" />
                        Descargar App (1 mes gratis)
                      </Button>
                    </Link>
                    
                    <Link href="/pricing" className="block">
                      <Button size="lg" variant="outline" className="w-full bg-white/10 border-white/20 text-white hover:bg-white/20">
                        <CreditCard className="mr-2 h-5 w-5" />
                        Ver planes de suscripción
                      </Button>
                    </Link>
                  </div>

                  <div className="pt-4 border-t border-white/10">
                    <p className="text-sm text-gray-400">
                      Acceso completo desde €9.99/mes • Sin compromisos • Cancela cuando quieras
                    </p>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}