import { Badge } from '@/components/ui/badge'
import { cn } from '@/lib/utils'
import { CheckCircle, Clock, Lock, Zap, Crown } from 'lucide-react'

interface ChannelBadgeProps {
  status?: 'working' | 'partial' | 'blocked'
  category?: 'official' | 'premium' | 'sports' | 'news' | 'entertainment'
  quality?: 'HD' | 'SD' | '4K'
  isLive?: boolean
  className?: string
}

export function ChannelBadge({ status, category, quality, isLive, className }: ChannelBadgeProps) {
  if (!status && !category && !quality && !isLive) return null

  return (
    <div className={cn("flex items-center gap-1.5", className)}>
      {/* Status Badge */}
      {status === 'working' && (
        <Badge variant="default" className="bg-green-600/90 text-white border-green-700">
          <CheckCircle className="h-3 w-3 mr-1" />
          AO VIVO
        </Badge>
      )}
      
      {status === 'partial' && (
        <Badge variant="secondary" className="bg-yellow-600/90 text-white border-yellow-700">
          <Clock className="h-3 w-3 mr-1" />
          Limitado
        </Badge>
      )}
      
      {status === 'blocked' && (
        <Badge variant="secondary" className="bg-red-600/90 text-white border-red-700">
          <Lock className="h-3 w-3 mr-1" />
          Premium
        </Badge>
      )}
      
      {/* Category Badge */}
      {category === 'official' && (
        <Badge variant="outline" className="border-blue-500 text-blue-500">
          <Crown className="h-3 w-3 mr-1" />
          Oficial
        </Badge>
      )}
      
      {/* Quality Badge */}
      {quality && (
        <Badge variant="outline" className="border-gray-500 text-gray-400">
          {quality === '4K' && <Zap className="h-3 w-3 mr-1" />}
          {quality}
        </Badge>
      )}
      
      {/* Live Badge */}
      {isLive && (
        <Badge className="bg-red-600 text-white border-red-700 animate-pulse">
          <div className="h-2 w-2 bg-white rounded-full mr-1" />
          EN VIVO
        </Badge>
      )}
    </div>
  )
}