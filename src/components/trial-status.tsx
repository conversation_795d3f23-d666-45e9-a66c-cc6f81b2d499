'use client'

import { useEffect, useState } from 'react'
import { useTrialStore } from '@/stores/trial.store'
import { useTrialTimer } from '@/hooks/use-trial-timer'
import { useAccessStore } from '@/stores/access.store'
import { Clock } from 'lucide-react'
import { Badge } from '@/components/ui/badge'

export function TrialStatus() {
  const { isExpired, startTime } = useTrialStore()
  const { timeRemaining } = useTrialTimer()
  const { hasAccess } = useAccessStore()
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  if (!mounted) {
    return null
  }

  // Se tem acesso premium, não mostra o trial
  if (hasAccess) {
    return null
  }

  // Se não tem trial iniciado ou já expirou, não mostra nada
  if (!startTime || isExpired) {
    return null
  }

  // Formatar tempo restante
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  return (
    <Badge variant="secondary" className="flex items-center gap-1">
      <Clock className="h-4 w-4" />
      <span>Trial {formatTime(timeRemaining)}</span>
    </Badge>
  )
}