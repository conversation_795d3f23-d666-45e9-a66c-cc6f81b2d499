'use client'

import { useState, useEffect } from 'react'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { X, AlertTriangle } from 'lucide-react'
import { Button } from '@/components/ui/button'

export function BrowserExtensionWarning() {
  const [show, setShow] = useState(false)
  const [extensions, setExtensions] = useState<string[]>([])

  useEffect(() => {
    // Verifica extensões problemáticas conhecidas
    const detected: string[] = []
    
    const checks = [
      { name: 'MetaMask', test: () => !!(window as any).ethereum },
      { name: 'Trust Wallet', test: () => !!(window as any).trustwallet },
      { name: 'Phantom', test: () => !!(window as any).phantom },
      { name: 'Coinbase Wallet', test: () => !!(window as any).coinbaseWalletExtension },
      { name: 'Binance Wallet', test: () => !!(window as any).Bin<PERSON><PERSON><PERSON><PERSON> }
    ]

    checks.forEach(({ name, test }) => {
      try {
        if (test()) {
          detected.push(name)
        }
      } catch (e) {
        // Ignora erros
      }
    })

    if (detected.length > 0) {
      setExtensions(detected)
      
      // Verifica se já foi dispensado antes
      const dismissed = localStorage.getItem('extension-warning-dismissed')
      if (!dismissed) {
        setShow(true)
      }
    }
  }, [])

  const handleDismiss = () => {
    setShow(false)
    localStorage.setItem('extension-warning-dismissed', 'true')
  }

  if (!show) return null

  return (
    <div className="fixed bottom-4 right-4 max-w-md z-50">
      <Alert className="border-yellow-600 bg-yellow-950/90 backdrop-blur">
        <AlertTriangle className="h-4 w-4 text-yellow-600" />
        <AlertDescription className="text-yellow-200 pr-8">
          <div className="font-semibold mb-1 text-yellow-100">
            Extensiones de navegador detectadas
          </div>
          <p className="text-sm mb-2">
            Las extensiones {extensions.join(', ')} pueden interferir con el funcionamiento de la aplicación.
          </p>
          <p className="text-xs text-yellow-300">
            Si experimentas problemas, intenta desactivarlas temporalmente.
          </p>
        </AlertDescription>
        <Button
          size="sm"
          variant="ghost"
          className="absolute top-2 right-2 h-6 w-6 p-0 hover:bg-yellow-900"
          onClick={handleDismiss}
        >
          <X className="h-4 w-4 text-yellow-400" />
        </Button>
      </Alert>
    </div>
  )
}