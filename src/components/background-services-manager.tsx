'use client'

import { useEffect } from 'react'
import { getSimpleSyncService } from '@/services/background/simple-sync-service'
import { getAllSportsDBSyncService } from '@/services/background/allsportsdb-sync-service'

export function BackgroundServicesManager() {
  useEffect(() => {
    // Iniciar API Sports sync (15 minutos)
    const apiSportsService = getSimpleSyncService()
    if (apiSportsService) {
      console.log('🚀 Iniciando API Sports sync (15 min)...')
      apiSportsService.start()
    }
    
    // Iniciar AllSportsDB sync (30 segundos)
    const allSportsService = getAllSportsDBSyncService()
    if (allSportsService) {
      console.log('🏃 Iniciando AllSportsDB sync (30 seg)...')
      allSportsService.start()
    }
    
    // Cleanup ao desmontar
    return () => {
      if (apiSportsService) {
        apiSportsService.stop()
      }
      if (allSportsService) {
        allSportsService.stop()
      }
    }
  }, [])
  
  return null // Não renderiza nada
}