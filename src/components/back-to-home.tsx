import Link from 'next/link'
import { But<PERSON> } from '@/components/ui/button'
import { ArrowLeft } from 'lucide-react'

interface BackToHomeProps {
  align?: 'left' | 'center'
}

export function BackToHome({ align = 'center' }: BackToHomeProps) {
  return (
    <div className={`mb-8 ${align === 'left' ? 'text-left' : 'text-center'}`}>
      <Button variant="ghost" asChild className="text-white hover:text-gray-200">
        <Link href="/">
          <ArrowLeft className="mr-2 h-4 w-4" />
          Volver al inicio
        </Link>
      </Button>
    </div>
  )
}