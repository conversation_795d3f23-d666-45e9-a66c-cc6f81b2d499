'use client'

import { useEffect } from 'react'
import { usePathname } from 'next/navigation'
import { useAnonymousAccess } from '@/hooks/use-anonymous-access'

export function AnonymousAccessChecker() {
  const pathname = usePathname()
  const { isChecking } = useAnonymousAccess()
  
  useEffect(() => {
    // Log para debug
    console.log('[ANONYMOUS-ACCESS-CHECKER] Checking anonymous access on:', pathname)
  }, [pathname])
  
  // Este componente não renderiza nada, apenas executa a verificação
  return null
}