'use client'

import { useEffect, useRef } from 'react'
import { usePathname } from 'next/navigation'
import { createClient } from '@/lib/supabase/client'
import { useAuth } from '@/hooks/use-auth'

export function ActivityTracker() {
  const pathname = usePathname()
  const { user } = useAuth()
  const intervalRef = useRef<NodeJS.Timeout | null>(null)
  const sessionIdRef = useRef<string>()
  
  useEffect(() => {
    // Gerar ou obter session ID
    if (!sessionIdRef.current) {
      // Tentar obter do localStorage ou criar novo
      const stored = localStorage.getItem('activity_session_id')
      if (stored) {
        sessionIdRef.current = stored
      } else {
        sessionIdRef.current = `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
        localStorage.setItem('activity_session_id', sessionIdRef.current)
      }
    }
    
    const supabase = createClient()
    
    // Função para enviar heartbeat
    const sendHeartbeat = async () => {
      try {
        // Extrair channel ID se estiver assistindo
        let channelId = null
        if (pathname.startsWith('/watch/')) {
          const parts = pathname.split('/')
          if (parts.length >= 3) {
            channelId = parts[2]
          }
        }
        
        await supabase.rpc('update_user_heartbeat', {
          p_session_id: sessionIdRef.current,
          p_user_id: user?.id || null,
          p_channel_id: channelId
        })
      } catch (error) {
        console.error('Error sending heartbeat:', error)
      }
    }
    
    // Enviar heartbeat inicial
    sendHeartbeat()
    
    // Configurar intervalo de heartbeat (a cada 30 segundos)
    intervalRef.current = setInterval(sendHeartbeat, 30000)
    
    // Enviar heartbeat quando mudar de página
    const handleVisibilityChange = () => {
      if (!document.hidden) {
        sendHeartbeat()
      }
    }
    
    document.addEventListener('visibilitychange', handleVisibilityChange)
    
    // Cleanup
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
      }
      document.removeEventListener('visibilitychange', handleVisibilityChange)
    }
  }, [pathname, user?.id])
  
  return null
}