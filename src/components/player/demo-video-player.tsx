'use client'

import { useRef, useEffect, useState } from 'react'
import Hls from 'hls.js'
import { useTrialStore } from '@/stores/trial.store'
import { TrialOverlay } from './trial-overlay'
import { PlayerControls } from './player-controls'
import { Loader2 } from 'lucide-react'

interface DemoVideoPlayerProps {
  channelName?: string
}

// URLs de streams de demonstração que funcionam
const DEMO_STREAMS = [
  'https://test-streams.mux.dev/x36xhzz/x36xhzz.m3u8',
  'https://demo.unified-streaming.com/k8s/features/stable/video/tears-of-steel/tears-of-steel.ism/.m3u8',
  'https://bitdash-a.akamaihd.net/content/MI201109210084_1/m3u8s/f08e80da-bf1d-4e3d-8899-f0f6155f6efa.m3u8'
]

export function DemoVideoPlayer({ channelName = 'Canal Demo' }: DemoVideoPlayerProps) {
  const videoRef = useRef<HTMLVideoElement>(null)
  const [isClient, setIsClient] = useState(false)
  const [isReady, setIsReady] = useState(false)
  const [hasError, setHasError] = useState(false)
  const [currentStreamIndex, setCurrentStreamIndex] = useState(0)
  
  const { isExpired, timeRemaining, startTrial } = useTrialStore()
  
  useEffect(() => {
    setIsClient(true)
  }, [])
  
  useEffect(() => {
    if (isClient) {
      startTrial()
    }
  }, [isClient, startTrial])
  
  useEffect(() => {
    if (!videoRef.current || !isClient) return
    
    const video = videoRef.current
    console.log('🎬 Iniciando DemoVideoPlayer com stream:', DEMO_STREAMS[currentStreamIndex])
    
    // Usar HLS.js para melhor compatibilidade
    if (Hls.isSupported()) {
      const hls = new Hls({
        maxLoadingDelay: 4,
        maxBufferLength: 30,
        maxMaxBufferLength: 600
      })
      
      hls.loadSource(DEMO_STREAMS[currentStreamIndex])
      hls.attachMedia(video)
      
      hls.on(Hls.Events.MANIFEST_PARSED, () => {
        console.log('✅ Stream carregado com sucesso!')
        setIsReady(true)
        setHasError(false)
        if (!isExpired) {
          video.muted = true // Garantir que está mutado para autoplay
          video.play().catch(err => {
            console.error('❌ Erro ao iniciar reprodução:', err)
          })
        }
      })
      
      hls.on(Hls.Events.ERROR, (event, data) => {
        if (data.fatal) {
          console.log('Erro fatal HLS, tentando próximo stream...')
          if (currentStreamIndex < DEMO_STREAMS.length - 1) {
            hls.destroy()
            setCurrentStreamIndex(prev => prev + 1)
          } else {
            setHasError(true)
          }
        }
      })
      
      return () => {
        hls.destroy()
      }
    } else {
      // Fallback para navegadores que suportam HLS nativamente
      video.src = DEMO_STREAMS[currentStreamIndex]
      
      const handleCanPlay = () => {
        console.log('✅ Vídeo pronto para reprodução')
        setIsReady(true)
        setHasError(false)
        if (!isExpired) {
          video.muted = true // Garantir que está mutado para autoplay
          video.play().catch(err => {
            console.error('❌ Erro ao iniciar reprodução:', err)
          })
        }
      }
      
      const handleError = () => {
        console.log('Erro ao carregar stream, tentando próximo...')
        if (currentStreamIndex < DEMO_STREAMS.length - 1) {
          setCurrentStreamIndex(prev => prev + 1)
        } else {
          setHasError(true)
        }
      }
      
      video.addEventListener('canplay', handleCanPlay)
      video.addEventListener('error', handleError)
      
      return () => {
        video.removeEventListener('canplay', handleCanPlay)
        video.removeEventListener('error', handleError)
      }
    }
  }, [currentStreamIndex, isClient, isExpired])
  
  if (!isClient) {
    return (
      <div className="relative aspect-video bg-black rounded-lg overflow-hidden">
        <div className="absolute inset-0 flex items-center justify-center">
          <Loader2 className="h-8 w-8 animate-spin text-white" />
        </div>
      </div>
    )
  }
  
  return (
    <div className="relative aspect-video bg-black rounded-lg overflow-hidden group">
      {!isExpired && (
        <>
          <video
            ref={videoRef}
            className="w-full h-full"
            controls={false}
            playsInline
            muted
            autoPlay
          />
          
          {!isReady && !hasError && (
            <div className="absolute inset-0 flex items-center justify-center bg-black/50">
              <Loader2 className="h-8 w-8 animate-spin text-white" />
            </div>
          )}
          
          {hasError && (
            <div className="absolute inset-0 flex items-center justify-center bg-black">
              <div className="text-center space-y-4">
                <p className="text-white text-lg">
                  Error al cargar el stream
                </p>
                <button
                  onClick={() => window.location.reload()}
                  className="px-4 py-2 bg-white text-black rounded hover:bg-gray-200"
                >
                  Reintentar
                </button>
              </div>
            </div>
          )}
          
          <PlayerControls 
            playerRef={{ current: { 
              seekTo: (time: number) => {
                if (videoRef.current) videoRef.current.currentTime = time
              }
            } as any }}
            channelName={channelName}
            timeRemaining={timeRemaining}
          />
        </>
      )}
      
      {isExpired && <TrialOverlay />}
    </div>
  )
}