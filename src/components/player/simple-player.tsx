'use client'

import { useRef, useEffect, useState } from 'react'
import { useTrialStore } from '@/stores/trial.store'
import { TrialOverlay } from './trial-overlay'
import { PlayerControls } from './player-controls'
import { Loader2, Play } from 'lucide-react'

interface SimplePlayerProps {
  channelName?: string
}

// Usando um vídeo MP4 público para evitar problemas de CORS
const DEMO_VIDEO = 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4'

export function SimplePlayer({ channelName = 'Canal Demo' }: SimplePlayerProps) {
  const videoRef = useRef<HTMLVideoElement>(null)
  const [isReady, setIsReady] = useState(false)
  const [isPlaying, setIsPlaying] = useState(false)
  const [showPlayButton, setShowPlayButton] = useState(true)
  
  const { isExpired, timeRemaining, startTrial } = useTrialStore()
  
  useEffect(() => {
    startTrial()
  }, [startTrial])
  
  const handlePlay = async () => {
    if (videoRef.current && !isExpired) {
      try {
        setShowPlayButton(false)
        await videoRef.current.play()
        setIsPlaying(true)
      } catch (err) {
        console.error('Erro ao reproduzir:', err)
        setShowPlayButton(true)
      }
    }
  }
  
  const handleCanPlay = () => {
    console.log('✅ Vídeo carregado e pronto!')
    setIsReady(true)
  }
  
  useEffect(() => {
    if (videoRef.current) {
      videoRef.current.addEventListener('canplay', handleCanPlay)
      return () => {
        videoRef.current?.removeEventListener('canplay', handleCanPlay)
      }
    }
  }, [])
  
  // Pausar quando expirar
  useEffect(() => {
    if (isExpired && videoRef.current) {
      videoRef.current.pause()
      setIsPlaying(false)
    }
  }, [isExpired])
  
  return (
    <div className="relative aspect-video bg-black rounded-lg overflow-hidden group">
      {!isExpired && (
        <>
          <video
            ref={videoRef}
            className="w-full h-full"
            src={DEMO_VIDEO}
            controls={false}
            playsInline
            muted
            onPlay={() => setIsPlaying(true)}
            onPause={() => setIsPlaying(false)}
          />
          
          {/* Botão de play grande */}
          {showPlayButton && isReady && (
            <div 
              className="absolute inset-0 flex items-center justify-center bg-black/20 cursor-pointer"
              onClick={handlePlay}
            >
              <button className="bg-white/10 backdrop-blur-sm hover:bg-white/20 rounded-full p-6 transition-all">
                <Play className="h-12 w-12 text-white fill-white" />
              </button>
            </div>
          )}
          
          {/* Loading */}
          {!isReady && (
            <div className="absolute inset-0 flex items-center justify-center bg-black/50">
              <Loader2 className="h-8 w-8 animate-spin text-white" />
            </div>
          )}
          
          {/* Controles */}
          {isPlaying && (
            <PlayerControls 
              playerRef={{ current: { 
                seekTo: (time: number) => {
                  if (videoRef.current) videoRef.current.currentTime = time
                }
              } as any }}
              channelName={channelName}
              timeRemaining={timeRemaining}
            />
          )}
        </>
      )}
      
      {isExpired && <TrialOverlay />}
    </div>
  )
}