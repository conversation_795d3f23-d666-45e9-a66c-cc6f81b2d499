'use client'

import { useRef, useEffect, useState } from 'react'
import dynamic from 'next/dynamic'
import Hls from 'hls.js'

const ReactPlayer = dynamic(() => import('react-player'), { ssr: false })
import { useTrialStore } from '@/stores/trial.store'
import { TrialOverlay } from './trial-overlay'
import { PlayerControls } from './player-controls'
import { Loader2, Play, AlertCircle } from 'lucide-react'
import { logger } from '@/lib/logger'
import { LOG_TAGS } from '@/config/debug'

interface UniversalPlayerProps {
  channelName?: string
  streamUrl?: string
  isLive?: boolean
  headers?: Record<string, string>
  userAgent?: string
}

// Video MP4 público para fallback emergencial
const FALLBACK_VIDEO = 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4'

export function UniversalPlayer({ 
  channelName = 'Deportes en Vivo',
  streamUrl,
  isLive = true,
  headers,
  userAgent
}: UniversalPlayerProps) {
  const playerRef = useRef<ReactPlayer>(null)
  const hlsRef = useRef<Hls | null>(null)
  const [isReady, setIsReady] = useState(false)
  const [isPlaying, setIsPlaying] = useState(false)
  const [showPlayButton, setShowPlayButton] = useState(true)
  const [hasError, setHasError] = useState(false)
  const [buffering, setBuffering] = useState(false)
  const [useFallback, setUseFallback] = useState(false)
  
  const { isExpired, timeRemaining, startTrial } = useTrialStore()
  
  // URL a ser usada - detectar e contornar CORS se necessário
  const playbackUrl = useFallback ? FALLBACK_VIDEO : (streamUrl || FALLBACK_VIDEO)
  
  // Se a URL é externa e pode ter problemas de CORS, usar proxy
  const finalUrl = playbackUrl && playbackUrl.startsWith('http') && !playbackUrl.includes(window.location.host)
    ? `/api/proxy?url=${encodeURIComponent(playbackUrl)}`
    : playbackUrl
  
  useEffect(() => {
    startTrial()
    logger.info(LOG_TAGS.PLAYER, 'UniversalPlayer mounted', {
      channelName,
      streamUrl,
      isLive,
      hasHeaders: !!headers,
      hasUserAgent: !!userAgent
    })
  }, [startTrial, channelName, streamUrl, isLive, headers, userAgent])
  
  // Configurar HLS quando o player estiver pronto
  const handleReady = () => {
    logger.info(LOG_TAGS.PLAYER, 'Player ready', {
      streamUrl,
      playbackUrl,
      isM3u8: streamUrl?.includes('.m3u8')
    })
    setIsReady(true)
    
    const player = playerRef.current
    if (!player || !streamUrl || useFallback) {
      logger.debug(LOG_TAGS.PLAYER, 'Skipping HLS setup', {
        hasPlayer: !!player,
        hasStreamUrl: !!streamUrl,
        useFallback
      })
      return
    }
    
    const internalPlayer = player.getInternalPlayer() as HTMLVideoElement
    
    if (internalPlayer && Hls.isSupported() && playbackUrl.includes('.m3u8')) {
      logger.info(LOG_TAGS.PLAYER, 'Setting up HLS for stream', {
        url: streamUrl,
        finalUrl,
        headers,
        userAgent,
        usingProxy: finalUrl !== playbackUrl
      })
      
      // Limpar HLS anterior se existir
      if (hlsRef.current) {
        hlsRef.current.destroy()
      }
      
      const hls = new Hls({
        autoStartLoad: true,
        startLevel: -1, // Auto quality
        capLevelToPlayerSize: true,
        capLevelOnFPSDrop: true,
        maxLoadingDelay: 4,
        minAutoBitrate: 0,
        lowLatencyMode: true,
        xhrSetup: (xhr: XMLHttpRequest) => {
          // Configurar headers se fornecidos
          if (headers) {
            Object.entries(headers).forEach(([key, value]) => {
              xhr.setRequestHeader(key, value)
              logger.debug(LOG_TAGS.PLAYER, 'Setting header', { key, value })
            })
          }
          // Configurar user agent se fornecido
          if (userAgent) {
            logger.debug(LOG_TAGS.PLAYER, 'Setting user agent', { userAgent })
          }
        }
      })
      
      hlsRef.current = hls
      
      hls.on(Hls.Events.MANIFEST_PARSED, (event, data) => {
        const qualities = data.levels.map(l => `${l.height}p`)
        logger.info(LOG_TAGS.PLAYER, 'HLS manifest parsed', {
          qualities,
          levelCount: data.levels.length,
          startLevel: data.startLevel,
          url: streamUrl
        })
      })
      
      hls.on(Hls.Events.ERROR, (event, data) => {
        logger.error(LOG_TAGS.PLAYER, 'HLS error', {
          type: data.type,
          details: data.details,
          fatal: data.fatal,
          url: data.url
        })
        
        if (data.fatal) {
          switch (data.type) {
            case Hls.ErrorTypes.NETWORK_ERROR:
              logger.error(LOG_TAGS.PLAYER, 'Fatal network error, trying to recover')
              hls.startLoad()
              break
            case Hls.ErrorTypes.MEDIA_ERROR:
              logger.error(LOG_TAGS.PLAYER, 'Fatal media error, trying to recover')
              hls.recoverMediaError()
              break
            default:
              logger.error(LOG_TAGS.PLAYER, 'Fatal error, using fallback')
              handleError(new Error(`HLS Fatal Error: ${data.details}`))
              break
          }
        }
      })
      
      logger.info(LOG_TAGS.PLAYER, 'Loading HLS source', { url: finalUrl })
      hls.loadSource(finalUrl)
      hls.attachMedia(internalPlayer)
    } else {
      logger.info(LOG_TAGS.PLAYER, 'Direct playback (non-HLS)', {
        url: streamUrl,
        isHlsSupported: Hls.isSupported(),
        isM3u8: streamUrl?.includes('.m3u8')
      })
    }
  }
  
  const handleError = (error: any) => {
    logger.error(LOG_TAGS.PLAYER, 'Player error occurred', {
      error: error?.message || 'Unknown error',
      streamUrl,
      channelName
    })
    
    // Se não estamos usando fallback ainda, tentar
    if (!useFallback && streamUrl) {
      logger.warn(LOG_TAGS.PLAYER, 'Stream failed, trying fallback video')
      setUseFallback(true)
      setHasError(false)
    } else {
      setHasError(true)
    }
  }
  
  const handlePlay = async () => {
    if (!isExpired) {
      try {
        logger.info(LOG_TAGS.PLAYER, 'Play button clicked', {
          streamUrl,
          playbackUrl,
          isExpired
        })
        setShowPlayButton(false)
        setIsPlaying(true)
      } catch (err) {
        logger.error(LOG_TAGS.PLAYER, 'Failed to start playback', { error: err })
        setShowPlayButton(true)
      }
    }
  }
  
  // Pausar quando expirar
  useEffect(() => {
    if (isExpired && playerRef.current) {
      const player = playerRef.current.getInternalPlayer() as HTMLVideoElement
      if (player) {
        player.pause()
        setIsPlaying(false)
      }
    }
  }, [isExpired])
  
  // Limpar HLS ao desmontar
  useEffect(() => {
    return () => {
      if (hlsRef.current) {
        hlsRef.current.destroy()
      }
    }
  }, [])
  
  // Log apenas uma vez quando a URL mudar
  useEffect(() => {
    logger.info(LOG_TAGS.PLAYER, 'Player state updated', {
      playbackUrl,
      finalUrl,
      streamUrl,
      isReady,
      hasError,
      isExpired,
      useFallback,
      showPlayButton,
      isM3U8: playbackUrl?.includes('.m3u8'),
      usingProxy: finalUrl !== playbackUrl
    })
  }, [playbackUrl, finalUrl, isReady, hasError, useFallback])
  
  return (
    <div className="relative aspect-video bg-black rounded-lg overflow-hidden group">
      {!isExpired && (
        <>
          <ReactPlayer
            ref={playerRef}
            url={finalUrl}
            playing={isPlaying && !showPlayButton}
            controls={false}
            volume={0.8}
            muted={true} // Necessário para autoplay
            width="100%"
            height="100%"
            onReady={handleReady}
            onPlay={() => {
              setIsPlaying(true)
              logger.info(LOG_TAGS.PLAYER, 'Playback started')
            }}
            onPause={() => {
              setIsPlaying(false)
              logger.info(LOG_TAGS.PLAYER, 'Playback paused')
            }}
            onProgress={(state) => {
              // ReactPlayer doesn't have onBuffer/onBufferEnd
              // We can detect buffering from progress events
              if (state.loaded < state.played && !buffering) {
                setBuffering(true)
                logger.debug(LOG_TAGS.PLAYER, 'Buffering...')
              } else if (state.loaded >= state.played && buffering) {
                setBuffering(false)
                logger.debug(LOG_TAGS.PLAYER, 'Buffer complete')
              }
            }}
            onError={handleError}
            config={{
              file: {
                forceHLS: true,
                hlsOptions: {
                  xhrSetup: function(xhr: XMLHttpRequest, url: string) {
                    logger.debug(LOG_TAGS.PLAYER, 'HLS XHR Request', { url })
                  }
                },
                attributes: {
                  crossOrigin: 'anonymous',
                  playsInline: true
                }
              }
            }}
          />
          
          {/* Overlay com informações do canal */}
          {isReady && isPlaying && (
            <div className="absolute top-4 left-4 right-4 flex items-center justify-between pointer-events-none">
              <div className="bg-black/70 px-3 py-1.5 rounded-full">
                <p className="text-white text-sm font-medium">{channelName}</p>
              </div>
              {isLive && (
                <div className="flex items-center gap-2 bg-red-600 px-3 py-1 rounded-full">
                  <div className="h-2 w-2 bg-white rounded-full animate-pulse" />
                  <span className="text-white text-sm font-medium">EN VIVO</span>
                </div>
              )}
            </div>
          )}
          
          {/* Botão de play grande */}
          {showPlayButton && isReady && !hasError && (
            <div 
              className="absolute inset-0 flex items-center justify-center bg-black/30 cursor-pointer"
              onClick={handlePlay}
            >
              <button className="bg-white/10 backdrop-blur-sm hover:bg-white/20 rounded-full p-6 transition-all">
                <Play className="h-12 w-12 text-white fill-white" />
              </button>
            </div>
          )}
          
          {/* Loading/Buffering */}
          {(!isReady || buffering) && !hasError && (
            <div className="absolute inset-0 flex items-center justify-center bg-black/50">
              <div className="text-center space-y-2">
                <Loader2 className="h-8 w-8 animate-spin text-white mx-auto" />
                <p className="text-white text-sm">
                  {!isReady ? `Cargando ${channelName}...` : 'Buffering...'}
                </p>
              </div>
            </div>
          )}
          
          {/* Error state */}
          {hasError && (
            <div className="absolute inset-0 flex items-center justify-center bg-black/90">
              <div className="text-center space-y-4 p-6">
                <AlertCircle className="h-12 w-12 text-red-500 mx-auto" />
                <div>
                  <p className="text-white text-lg font-medium">
                    Error al cargar el contenido
                  </p>
                  <p className="text-gray-400 text-sm mt-1">
                    {channelName}
                  </p>
                </div>
                <button
                  onClick={() => window.location.reload()}
                  className="px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                >
                  Reintentar
                </button>
              </div>
            </div>
          )}
          
          {/* Controles */}
          {isPlaying && (
            <PlayerControls 
              playerRef={playerRef}
              channelName={channelName}
              timeRemaining={timeRemaining}
            />
          )}
        </>
      )}
      
      {isExpired && <TrialOverlay />}
    </div>
  )
}