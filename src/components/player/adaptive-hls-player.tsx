'use client'

import { useRef, useEffect, useState } from 'react'
import Hls from 'hls.js'
import { useTrialStore } from '@/stores/trial.store'
import { TrialOverlay } from './trial-overlay'
import { Loader2, Play, AlertCircle } from 'lucide-react'
import { logger } from '@/lib/logger'
import { LOG_TAGS } from '@/config/debug'

interface AdaptiveHlsPlayerProps {
  channelName?: string
  streamUrl?: string
  isLive?: boolean
  headers?: Record<string, string>
  userAgent?: string
}

export function AdaptiveHlsPlayer({ 
  channelName = 'Deportes en Vivo',
  streamUrl,
  isLive = true,
  headers,
  userAgent
}: AdaptiveHlsPlayerProps) {
  const videoRef = useRef<HTMLVideoElement>(null)
  const hlsRef = useRef<Hls | null>(null)
  const [isReady, setIsReady] = useState(false)
  const [isPlaying, setIsPlaying] = useState(false)
  const [showPlayButton, setShowPlayButton] = useState(true)
  const [hasError, setHasError] = useState(false)
  const [buffering, setBuffering] = useState(false)
  const [playbackStrategy, setPlaybackStrategy] = useState<'direct' | 'proxy' | 'iframe'>('direct')
  const [errorMessage, setErrorMessage] = useState<string>('')
  
  const { isExpired, timeRemaining, startTrial } = useTrialStore()
  
  useEffect(() => {
    startTrial()
    logger.info(LOG_TAGS.PLAYER, 'AdaptiveHlsPlayer mounted', {
      channelName,
      streamUrl,
      isLive,
      hasHeaders: !!headers,
      hasUserAgent: !!userAgent
    })
  }, [startTrial, channelName, streamUrl, isLive, headers, userAgent])
  
  // Tentar diferentes estratégias de reprodução
  const tryPlaybackStrategies = async () => {
    if (!videoRef.current || !streamUrl) return
    
    const video = videoRef.current
    let success = false
    
    // Estratégia 1: Tentar reprodução direta
    if (playbackStrategy === 'direct') {
      logger.info(LOG_TAGS.PLAYER, 'Trying direct playback')
      
      if (Hls.isSupported() && streamUrl.includes('.m3u8')) {
        const hls = new Hls({
          autoStartLoad: true,
          startLevel: -1,
          capLevelToPlayerSize: true,
          capLevelOnFPSDrop: true,
          maxLoadingDelay: 4,
          minAutoBitrate: 0,
          lowLatencyMode: true,
          xhrSetup: (xhr: XMLHttpRequest) => {
            if (headers) {
              Object.entries(headers).forEach(([key, value]) => {
                xhr.setRequestHeader(key, value)
              })
            }
          }
        })
        
        hlsRef.current = hls
        
        hls.on(Hls.Events.MANIFEST_PARSED, () => {
          logger.info(LOG_TAGS.PLAYER, 'Direct playback: HLS manifest parsed')
          setIsReady(true)
          success = true
        })
        
        hls.on(Hls.Events.ERROR, (event, data) => {
          logger.error(LOG_TAGS.PLAYER, 'Direct playback: HLS error', {
            type: data.type,
            details: data.details,
            fatal: data.fatal
          })
          
          if (data.fatal && !success) {
            // Tentar próxima estratégia
            setPlaybackStrategy('proxy')
          }
        })
        
        hls.loadSource(streamUrl)
        hls.attachMedia(video)
      } else if (video.canPlayType('application/vnd.apple.mpegurl')) {
        // Safari native HLS
        video.src = streamUrl
        setIsReady(true)
      }
    }
    
    // Estratégia 2: Usar proxy
    else if (playbackStrategy === 'proxy') {
      logger.info(LOG_TAGS.PLAYER, 'Trying proxy playback')
      
      const proxyUrl = `/api/hls-proxy?url=${encodeURIComponent(streamUrl)}`
      if (headers) {
        const headersParam = `&headers=${encodeURIComponent(JSON.stringify(headers))}`
        proxyUrl + headersParam
      }
      
      if (hlsRef.current) {
        hlsRef.current.destroy()
      }
      
      if (Hls.isSupported() && streamUrl.includes('.m3u8')) {
        const hls = new Hls({
          autoStartLoad: true,
          startLevel: -1,
          capLevelToPlayerSize: true,
          capLevelOnFPSDrop: true,
          maxLoadingDelay: 4,
          minAutoBitrate: 0,
          lowLatencyMode: true,
        })
        
        hlsRef.current = hls
        
        hls.on(Hls.Events.MANIFEST_PARSED, () => {
          logger.info(LOG_TAGS.PLAYER, 'Proxy playback: HLS manifest parsed')
          setIsReady(true)
          success = true
        })
        
        hls.on(Hls.Events.ERROR, (event, data) => {
          logger.error(LOG_TAGS.PLAYER, 'Proxy playback: HLS error', {
            type: data.type,
            details: data.details,
            fatal: data.fatal
          })
          
          if (data.fatal && !success) {
            setHasError(true)
            setErrorMessage('No se pudo cargar el contenido. El canal puede no estar disponible en tu región.')
          }
        })
        
        hls.loadSource(proxyUrl)
        hls.attachMedia(video)
      }
    }
  }
  
  // Configurar HLS quando o componente montar ou estratégia mudar
  useEffect(() => {
    tryPlaybackStrategies()
    
    // Cleanup
    return () => {
      if (hlsRef.current) {
        hlsRef.current.destroy()
        hlsRef.current = null
      }
    }
  }, [streamUrl, headers, playbackStrategy])
  
  const handlePlay = async () => {
    if (!isExpired && videoRef.current) {
      try {
        setShowPlayButton(false)
        await videoRef.current.play()
        setIsPlaying(true)
        logger.info(LOG_TAGS.PLAYER, 'Playback started')
      } catch (err) {
        logger.error(LOG_TAGS.PLAYER, 'Failed to start playback', { error: err })
        setShowPlayButton(true)
      }
    }
  }
  
  // Pausar quando expirar
  useEffect(() => {
    if (isExpired && videoRef.current) {
      videoRef.current.pause()
      setIsPlaying(false)
    }
  }, [isExpired])
  
  // Retry com estratégia diferente
  const handleRetry = () => {
    setHasError(false)
    setErrorMessage('')
    if (playbackStrategy === 'direct') {
      setPlaybackStrategy('proxy')
    } else {
      window.location.reload()
    }
  }
  
  return (
    <div className="relative aspect-video bg-black rounded-lg overflow-hidden group">
      {!isExpired && (
        <>
          <video
            ref={videoRef}
            className="w-full h-full"
            playsInline
            muted
            onLoadedMetadata={() => {
              logger.info(LOG_TAGS.PLAYER, 'Video metadata loaded')
              setIsReady(true)
            }}
            onPlay={() => setIsPlaying(true)}
            onPause={() => setIsPlaying(false)}
            onWaiting={() => setBuffering(true)}
            onPlaying={() => setBuffering(false)}
            onError={(e) => {
              logger.error(LOG_TAGS.PLAYER, 'Video error', { error: e })
              if (playbackStrategy === 'direct') {
                setPlaybackStrategy('proxy')
              } else {
                setHasError(true)
              }
            }}
          />
          
          {/* Overlay com informações do canal */}
          {isReady && isPlaying && (
            <div className="absolute top-4 left-4 right-4 flex items-center justify-between pointer-events-none">
              <div className="bg-black/70 px-3 py-1.5 rounded-full">
                <p className="text-white text-sm font-medium">{channelName}</p>
              </div>
              <div className="flex items-center gap-4">
                {playbackStrategy === 'proxy' && (
                  <div className="bg-blue-600/70 px-3 py-1 rounded-full">
                    <span className="text-white text-xs font-medium">PROXY</span>
                  </div>
                )}
                {isLive && (
                  <div className="flex items-center gap-2 bg-red-600 px-3 py-1 rounded-full">
                    <div className="h-2 w-2 bg-white rounded-full animate-pulse" />
                    <span className="text-white text-sm font-medium">EN VIVO</span>
                  </div>
                )}
              </div>
            </div>
          )}
          
          {/* Botão de play grande */}
          {showPlayButton && isReady && !hasError && (
            <div 
              className="absolute inset-0 flex items-center justify-center bg-black/30 cursor-pointer"
              onClick={handlePlay}
            >
              <button className="bg-white/10 backdrop-blur-sm hover:bg-white/20 rounded-full p-6 transition-all">
                <Play className="h-12 w-12 text-white fill-white" />
              </button>
            </div>
          )}
          
          {/* Loading/Buffering */}
          {(!isReady || buffering) && !hasError && (
            <div className="absolute inset-0 flex items-center justify-center bg-black/50">
              <div className="text-center space-y-2">
                <Loader2 className="h-8 w-8 animate-spin text-white mx-auto" />
                <p className="text-white text-sm">
                  {!isReady ? `Cargando ${channelName}...` : 'Buffering...'}
                </p>
                {playbackStrategy === 'proxy' && !isReady && (
                  <p className="text-gray-400 text-xs">
                    Usando proxy para contornar restrições...
                  </p>
                )}
              </div>
            </div>
          )}
          
          {/* Error state */}
          {hasError && (
            <div className="absolute inset-0 flex items-center justify-center bg-black/90">
              <div className="text-center space-y-4 p-6 max-w-md">
                <AlertCircle className="h-12 w-12 text-red-500 mx-auto" />
                <div>
                  <p className="text-white text-lg font-medium">
                    Error al cargar el contenido
                  </p>
                  <p className="text-gray-400 text-sm mt-1">
                    {channelName}
                  </p>
                  {errorMessage && (
                    <p className="text-gray-500 text-xs mt-2">
                      {errorMessage}
                    </p>
                  )}
                </div>
                <button
                  onClick={handleRetry}
                  className="px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                >
                  Reintentar {playbackStrategy === 'direct' ? 'con proxy' : ''}
                </button>
              </div>
            </div>
          )}
        </>
      )}
      
      {isExpired && <TrialOverlay />}
    </div>
  )
}