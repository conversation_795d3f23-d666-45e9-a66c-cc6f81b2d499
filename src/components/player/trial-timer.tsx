'use client'

import { useTrialTimer } from '@/hooks/use-trial-timer'
import { Clock, AlertCircle } from 'lucide-react'
import { cn } from '@/lib/utils'
import { useAccessStore } from '@/stores/access.store'

interface TrialTimerProps {
  className?: string
  showWarning?: boolean
}

export function TrialTimer({ className, showWarning = true }: TrialTimerProps) {
  const { timeRemaining, timeRemainingFormatted, isExpired } = useTrialTimer()
  const { hasAccess, accessType } = useAccessStore()
  
  console.log('[TRIAL-TIMER-COMPONENT] Render:', {
    timeRemaining,
    timeRemainingFormatted,
    isExpired,
    hasAccess,
    accessType
  })
  
  // Não mostrar timer se usuário tem acesso (premium ou app verificado)
  if (hasAccess && (accessType === 'assinatura' || accessType === 'app_verified')) {
    console.log('[TRIAL-TIMER-COMPONENT] Not showing - user has access:', accessType)
    return null
  }
  
  // Não mostrar se já tem acesso completo ou expirou
  if (isExpired) {
    console.log('[TRIAL-TIMER-COMPONENT] Not showing - expired')
    return null
  }
  
  // Mostrar warning quando restam menos de 60 segundos
  const isWarning = timeRemaining < 60
  
  return (
    <div 
      data-trial-timer
      className={cn(
        "flex items-center gap-2 px-3 py-1.5 rounded-full text-sm font-medium transition-all",
        isWarning && showWarning ? "bg-red-600/90 text-white animate-pulse" : "bg-black/70 text-white",
        className
      )}
    >
      {isWarning && showWarning ? (
        <AlertCircle className="h-4 w-4" />
      ) : (
        <Clock className="h-4 w-4" />
      )}
      <span>
        {isWarning && showWarning ? 'Expira em: ' : 'Trial: '}
        {timeRemainingFormatted}
      </span>
    </div>
  )
}