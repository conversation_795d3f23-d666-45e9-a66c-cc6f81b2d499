'use client'

import { useRef, useEffect, useState } from 'react'
import Hls from 'hls.js'
import { useTrialStore } from '@/stores/trial.store'
import { TrialOverlay } from './trial-overlay'
import { PlayerControls } from './player-controls'
import { Loader2, Play, AlertCircle, Tv } from 'lucide-react'

interface IPTVStreamPlayerProps {
  channelName: string
  streamUrl: string
  headers?: {
    'Referer'?: string
    'User-Agent'?: string
  }
  logo?: string
}

export function IPTVStreamPlayer({ 
  channelName,
  streamUrl,
  headers,
  logo 
}: IPTVStreamPlayerProps) {
  const videoRef = useRef<HTMLVideoElement>(null)
  const hlsRef = useRef<Hls | null>(null)
  const [isReady, setIsReady] = useState(false)
  const [isPlaying, setIsPlaying] = useState(false)
  const [showPlayButton, setShowPlayButton] = useState(true)
  const [hasError, setHasError] = useState(false)
  const [errorMessage, setErrorMessage] = useState('')
  const [useProxy, setUseProxy] = useState(true) // Usar proxy por padrão
  
  const { isExpired, timeRemaining, startTrial } = useTrialStore()
  
  useEffect(() => {
    startTrial()
  }, [startTrial])
  
  useEffect(() => {
    if (!videoRef.current || !streamUrl) return
    
    const video = videoRef.current
    console.log('🎬 Configurando stream IPTV:', {
      channel: channelName,
      url: streamUrl,
      useProxy,
      headers
    })
    
    // Determinar URL a usar (proxy ou direto)
    const urlToUse = useProxy 
      ? `/api/proxy/stream?url=${encodeURIComponent(streamUrl)}${headers?.['Referer'] ? `&referer=${encodeURIComponent(headers['Referer'])}` : ''}${headers?.['User-Agent'] ? `&userAgent=${encodeURIComponent(headers['User-Agent'])}` : ''}`
      : streamUrl
    
    console.log('📡 URL a usar:', urlToUse)
    
    if (Hls.isSupported()) {
      const hls = new Hls({
        maxLoadingDelay: 4,
        maxBufferLength: 30,
        maxMaxBufferLength: 600,
        enableWorker: true,
        lowLatencyMode: true,
        xhrSetup: !useProxy ? (xhr: XMLHttpRequest) => {
          // Configurar headers se fornecidos e não estiver usando proxy
          if (headers?.['Referer']) {
            try {
              xhr.setRequestHeader('Referer', headers['Referer'])
            } catch (e) {
              console.warn('Não foi possível definir Referer header')
            }
          }
          if (headers?.['User-Agent']) {
            try {
              xhr.setRequestHeader('User-Agent', headers['User-Agent'])
            } catch (e) {
              console.warn('Não foi possível definir User-Agent header')
            }
          }
        } : undefined
      })
      
      hlsRef.current = hls
      
      hls.on(Hls.Events.MANIFEST_PARSED, () => {
        console.log('✅ Stream IPTV carregado:', channelName)
        setIsReady(true)
        setHasError(false)
      })
      
      hls.on(Hls.Events.ERROR, (event, data) => {
        // Verificar se data existe antes de acessar propriedades
        if (!data) {
          console.error('❌ Erro HLS sem dados')
          return
        }
        
        console.error('❌ Erro HLS detalhado:', {
          type: data.type || 'unknown',
          details: data.details || 'no details',
          fatal: data.fatal || false,
          url: data.url || streamUrl,
          response: data.response || null,
          context: data.context || null
        })
        
        if (data.fatal) {
          switch (data.type) {
            case Hls.ErrorTypes.NETWORK_ERROR:
              // Se falhar sem proxy, tentar com proxy
              if (!useProxy) {
                console.log('🔄 Tentando com proxy devido a erro de rede:', data.details)
                setUseProxy(true)
                setHasError(false)
                hls.destroy()
                return
              }
              
              // Mensagem específica baseada no erro
              const details = data.details || ''
              if (details.includes && details.includes('manifestLoadError')) {
                setErrorMessage('No se pudo cargar el stream - Verifica la conexión')
              } else if (details.includes && details.includes('levelLoadError')) {
                setErrorMessage('Error al cargar la calidad del video')
              } else {
                setErrorMessage('Error de red - No se pudo conectar al stream')
              }
              break
            case Hls.ErrorTypes.MEDIA_ERROR:
              setErrorMessage('Error de medios - Formato no compatible')
              break
            default:
              setErrorMessage('Error al cargar el stream')
          }
          setHasError(true)
        }
      })
      
      hls.loadSource(urlToUse)
      hls.attachMedia(video)
      
      return () => {
        hls.destroy()
      }
    } else if (video.canPlayType('application/vnd.apple.mpegurl')) {
      // Safari nativo
      video.src = urlToUse
      video.addEventListener('canplay', () => {
        setIsReady(true)
        setHasError(false)
      })
      video.addEventListener('error', () => {
        if (!useProxy) {
          console.log('🔄 Tentando com proxy no Safari')
          setUseProxy(true)
          setHasError(false)
          return
        }
        setErrorMessage('Error al cargar el stream')
        setHasError(true)
      })
    } else {
      setErrorMessage('Tu navegador no soporta streaming HLS')
      setHasError(true)
    }
  }, [streamUrl, channelName, headers, useProxy])
  
  const handlePlay = async () => {
    if (videoRef.current && !isExpired) {
      try {
        setShowPlayButton(false)
        videoRef.current.muted = true // Garantir autoplay
        await videoRef.current.play()
        setIsPlaying(true)
      } catch (err) {
        console.error('Erro ao reproduzir:', err)
        setShowPlayButton(true)
      }
    }
  }
  
  // Pausar quando expirar
  useEffect(() => {
    if (isExpired && videoRef.current) {
      videoRef.current.pause()
      setIsPlaying(false)
    }
  }, [isExpired])
  
  // Retry handler
  const handleRetry = () => {
    setHasError(false)
    setErrorMessage('')
    // Se já tentou com proxy, usar um stream de teste
    if (useProxy && streamUrl.includes('moveonjoy.com')) {
      // Usar Red Bull TV como fallback (geralmente funciona bem)
      const fallbackUrl = 'https://rbmn-live.akamaized.net/hls/live/590964/BoRB-AT/master.m3u8'
      console.log('🔄 Usando stream de fallback:', fallbackUrl)
      
      // Forçar re-render com novo URL
      window.location.href = window.location.href.replace(
        encodeURIComponent(streamUrl), 
        encodeURIComponent(fallbackUrl)
      )
    } else {
      setUseProxy(!useProxy) // Alternar entre proxy e direto
      if (hlsRef.current) {
        hlsRef.current.destroy()
        hlsRef.current = null
      }
    }
  }
  
  return (
    <div className="relative aspect-video bg-black rounded-lg overflow-hidden group">
      {!isExpired && (
        <>
          <video
            ref={videoRef}
            className="w-full h-full"
            controls={false}
            playsInline
            muted
            onPlay={() => setIsPlaying(true)}
            onPause={() => setIsPlaying(false)}
          />
          
          {/* Logo do canal */}
          {logo && isReady && !hasError && (
            <div className="absolute top-4 left-4 bg-black/70 p-2 rounded">
              <img 
                src={logo} 
                alt={channelName}
                className="h-8 w-auto object-contain"
                onError={(e) => {
                  (e.target as HTMLImageElement).style.display = 'none'
                }}
              />
            </div>
          )}
          
          {/* Indicador de EN VIVO */}
          {isPlaying && (
            <div className="absolute top-4 right-4 flex items-center gap-2 bg-red-600 px-3 py-1 rounded-full">
              <div className="h-2 w-2 bg-white rounded-full animate-pulse" />
              <span className="text-white text-sm font-medium">EN VIVO</span>
            </div>
          )}
          
          {/* Nome do canal */}
          {isReady && !hasError && (
            <div className="absolute bottom-20 left-4 bg-black/70 px-3 py-1.5 rounded">
              <div className="text-white text-sm font-medium flex items-center gap-2">
                <Tv className="h-4 w-4" />
                {channelName}
              </div>
            </div>
          )}
          
          {/* Botão de play grande */}
          {showPlayButton && isReady && !hasError && (
            <div 
              className="absolute inset-0 flex items-center justify-center bg-black/30 cursor-pointer"
              onClick={handlePlay}
            >
              <button className="bg-white/10 backdrop-blur-sm hover:bg-white/20 rounded-full p-6 transition-all">
                <Play className="h-12 w-12 text-white fill-white" />
              </button>
            </div>
          )}
          
          {/* Loading */}
          {!isReady && !hasError && (
            <div className="absolute inset-0 flex items-center justify-center bg-black/50">
              <div className="text-center space-y-2">
                <Loader2 className="h-8 w-8 animate-spin text-white mx-auto" />
                <p className="text-white text-sm">Conectando con {channelName}...</p>
                {useProxy && (
                  <p className="text-gray-400 text-xs">Usando conexión alternativa</p>
                )}
              </div>
            </div>
          )}
          
          {/* Error state */}
          {hasError && (
            <div className="absolute inset-0 flex items-center justify-center bg-black/90">
              <div className="text-center space-y-4 p-6">
                <AlertCircle className="h-12 w-12 text-red-500 mx-auto" />
                <div>
                  <p className="text-white text-lg font-medium">
                    No se pudo cargar el canal
                  </p>
                  <p className="text-gray-400 text-sm mt-1">
                    {errorMessage}
                  </p>
                  <p className="text-gray-500 text-xs mt-2">
                    {channelName}
                  </p>
                </div>
                <button
                  onClick={handleRetry}
                  className="px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                >
                  Reintentar
                </button>
              </div>
            </div>
          )}
          
          {/* Controles */}
          {isPlaying && (
            <PlayerControls 
              playerRef={{ current: { 
                seekTo: (time: number) => {
                  if (videoRef.current) videoRef.current.currentTime = time
                }
              } as any }}
              channelName={channelName}
              timeRemaining={timeRemaining}
            />
          )}
        </>
      )}
      
      {isExpired && <TrialOverlay />}
    </div>
  )
}