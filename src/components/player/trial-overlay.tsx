'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Loader2, Smartphone, Monitor, Gift, CreditCard, Ticket } from 'lucide-react'
import { useRouter } from 'next/navigation'
import { useTrialStore } from '@/stores/trial.store'
import { toast } from 'sonner'
import { useUser } from '@stackframe/stack'
import { useAccessStore } from '@/stores/access.store'

export function TrialOverlay() {
  const [loading, setLoading] = useState(false)
  const [platform, setPlatform] = useState<'ios' | 'android' | 'desktop'>('desktop')
  const [showCodeInput, setShowCodeInput] = useState(false)
  const [code, setCode] = useState('')
  const [applyingCode, setApplyingCode] = useState(false)
  const router = useRouter()
  const { markOfferSeen, hasSeenOffer } = useTrialStore()
  const user = useUser()
  const { applyCoupon } = useAccessStore()
  
  useEffect(() => {
    // Detectar plataforma
    const userAgent = navigator.userAgent.toLowerCase()
    if (/iphone|ipad|ipod/.test(userAgent)) {
      setPlatform('ios')
    } else if (/android/.test(userAgent)) {
      setPlatform('android')
    }
    
    // Marcar oferta como vista
    if (!hasSeenOffer) {
      markOfferSeen()
    }
  }, [hasSeenOffer, markOfferSeen])
  
  const handleAppRedirect = async () => {
    setLoading(true)
    
    try {
      // Verificar si el usuario está autenticado
      if (!user) {
        // Redirigir al login si no está autenticado
        toast.error('Debes iniciar sesión para continuar')
        router.push('/login?redirect=/app-redirect')
        return
      }
      
      // Redirigir a la página de verificación de la app
      router.push('/app-redirect?source=trial')
      
    } catch (error) {
      console.error('Error:', error)
      toast.error('Error al acceder. Por favor, inténtalo de nuevo.')
    } finally {
      setLoading(false)
    }
  }
  
  const handleViewPlans = () => {
    router.push('/subscription')
  }
  
  const handleCodeSubmit = async () => {
    if (!code.trim()) {
      toast.error('Por favor, ingresa un código')
      return
    }
    
    if (!user) {
      toast.error('Debes iniciar sesión para usar un código')
      router.push('/login?redirect=/watch/demo')
      return
    }
    
    setApplyingCode(true)
    try {
      const result = await applyCoupon(user.id, code.trim())
      
      if (result.success) {
        toast.success(result.message)
        // Recargar la página para aplicar el acceso
        window.location.reload()
      } else {
        toast.error(result.message)
      }
    } catch (error) {
      console.error('Error al aplicar cupón:', error)
      toast.error('Error al aplicar código. Intenta nuevamente.')
    } finally {
      setApplyingCode(false)
    }
  }
  
  return (
    <div 
      data-trial-overlay
      className="absolute inset-0 bg-gradient-to-br from-black/90 via-slate-900/90 to-black/90 backdrop-blur-md flex items-center justify-center p-4 animate-fade-in">
      {/* Background pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute inset-0" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
        }} />
      </div>
      
      <Card className="relative max-w-md w-full bg-gradient-to-b from-slate-800/95 to-slate-900/95 backdrop-blur-xl border-slate-700 shadow-2xl">
        <div className="absolute inset-0 bg-gradient-to-r from-red-600/20 to-orange-600/20 rounded-lg" />
        
        <CardHeader className="relative text-center space-y-2 pb-0">
          <div className="mx-auto mb-4 relative">
            <div className="absolute inset-0 bg-gradient-to-r from-red-500 to-orange-500 rounded-full blur-2xl opacity-50 animate-pulse" />
            <div className="relative bg-gradient-to-r from-red-500 to-orange-500 p-4 rounded-full">
              <Gift className="h-12 w-12 text-white" />
            </div>
          </div>
          <CardTitle className="text-3xl md:text-4xl font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
            ¡Acceso Premium!
          </CardTitle>
          <CardDescription className="text-lg text-gray-300">
            Descarga la app y disfruta 1 mes gratis
          </CardDescription>
        </CardHeader>
        
        <CardContent className="relative space-y-6 pt-6">
          {/* Oferta especial con gradiente */}
          <div className="relative overflow-hidden rounded-xl bg-gradient-to-r from-red-600/20 to-orange-600/20 p-6 text-center space-y-2 border border-red-500/30">
            <div className="absolute inset-0 bg-gradient-to-r from-red-600/10 to-orange-600/10 animate-pulse" />
            <Badge className="relative mb-2 bg-gradient-to-r from-red-600 to-orange-600 border-0 text-white">
              🔥 OFERTA EXCLUSIVA
            </Badge>
            <p className="relative text-xl font-bold text-white">
              1 MES COMPLETAMENTE GRATIS
            </p>
            <p className="relative text-sm text-gray-300">
              Sin compromisos • Cancela cuando quieras
            </p>
          </div>
          
          {/* Botones de acción */}
          <div className="space-y-3">
            <Button
              size="lg"
              className="w-full bg-gradient-to-r from-red-600 to-orange-600 hover:from-red-700 hover:to-orange-700 text-white border-0"
              onClick={handleViewPlans}
              disabled={loading}
            >
              <CreditCard className="mr-2 h-4 w-4" />
              Suscribirse Premium - €20/mes
            </Button>
            
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <span className="w-full border-t border-gray-700/50" />
              </div>
              <div className="relative flex justify-center text-xs uppercase">
                <span className="bg-slate-900 px-2 text-gray-500">ou</span>
              </div>
            </div>
            
            <Button
              size="lg"
              variant="outline"
              className="w-full bg-green-600/10 border-green-600/30 text-green-500 hover:bg-green-600/20 hover:border-green-600/50"
              onClick={handleAppRedirect}
              disabled={loading}
            >
              {loading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Generando acceso...
                </>
              ) : (
                <>
                  <Smartphone className="mr-2 h-4 w-4" />
                  Descargar App (1 mes gratis)
                </>
              )}
            </Button>
            
            {/* Botão ou campo de cupom */}
            {!showCodeInput ? (
              <Button
                variant="ghost"
                className="w-full text-white/80 hover:text-white hover:bg-white/10"
                onClick={() => setShowCodeInput(true)}
              >
                <Ticket className="mr-2 h-4 w-4" />
                ¿Ya tienes un código?
              </Button>
            ) : (
              <div className="space-y-2">
                <Input
                  placeholder="Ingresa tu código"
                  value={code}
                  onChange={(e) => setCode(e.target.value.toUpperCase())}
                  className="text-center font-mono text-lg bg-white/10 border-white/20 text-white placeholder:text-white/50"
                  onKeyDown={(e) => e.key === 'Enter' && handleCodeSubmit()}
                  disabled={applyingCode}
                />
                <div className="flex gap-2">
                  <Button
                    onClick={handleCodeSubmit}
                    className="flex-1"
                    disabled={applyingCode}
                  >
                    {applyingCode ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Aplicando...
                      </>
                    ) : (
                      'Usar Código'
                    )}
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => {
                      setShowCodeInput(false)
                      setCode('')
                    }}
                    className="bg-white/10 border-white/20 text-white hover:bg-white/20"
                    disabled={applyingCode}
                  >
                    Cancelar
                  </Button>
                </div>
              </div>
            )}
          </div>
          
          {/* Beneficios con iconos */}
          <div className="space-y-3">
            <p className="font-semibold text-white text-center">Lo que incluye tu mes gratis:</p>
            <div className="grid grid-cols-2 gap-3">
              <div className="bg-white/5 rounded-lg p-3 flex items-center gap-2">
                <div className="text-green-500">✓</div>
                <span className="text-sm text-gray-300">+100 canales</span>
              </div>
              <div className="bg-white/5 rounded-lg p-3 flex items-center gap-2">
                <div className="text-green-500">✓</div>
                <span className="text-sm text-gray-300">HD y 4K</span>
              </div>
              <div className="bg-white/5 rounded-lg p-3 flex items-center gap-2">
                <div className="text-green-500">✓</div>
                <span className="text-sm text-gray-300">Sin anuncios</span>
              </div>
              <div className="bg-white/5 rounded-lg p-3 flex items-center gap-2">
                <div className="text-green-500">✓</div>
                <span className="text-sm text-gray-300">Multi-dispositivo</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}