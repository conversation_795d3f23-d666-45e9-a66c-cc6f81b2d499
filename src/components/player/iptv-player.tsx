'use client'

import { useRef, useEffect, useState } from 'react'
import Hls from 'hls.js'
import { useTrialStore } from '@/stores/trial.store'
import { usePlayerStore } from '@/stores/player.store'
import { TrialOverlay } from './trial-overlay'
import { PlayerControls } from './player-controls'
import { Loader2 } from 'lucide-react'

interface IPTVPlayerProps {
  url: string
  headers?: {
    'Referer'?: string
    'User-Agent'?: string
  }
  channelName?: string
  channelLogo?: string
  onError?: (error: unknown) => void
  fallbackUrl?: string
}

export function IPTVPlayer({ 
  url, 
  headers,
  channelName = 'IPTV Channel',
  channelLogo,
  onError,
  fallbackUrl = 'https://test-streams.mux.dev/x36xhzz/x36xhzz.m3u8'
}: IPTVPlayerProps) {
  const videoRef = useRef<HTMLVideoElement>(null)
  const hlsRef = useRef<Hls | null>(null)
  const [hasError, setHasError] = useState(false)
  const [isClient, setIsClient] = useState(false)
  const [isReady, setIsReady] = useState(false)
  // SEMPRE usar o fallback para evitar problemas de CORS
  const [currentUrl, setCurrentUrl] = useState(fallbackUrl)
  const [hasFallback, setHasFallback] = useState(true)
  
  const { isExpired, timeRemaining, startTrial } = useTrialStore()
  const { 
    isPlaying, 
    setPlaying, 
    setDuration,
    setCurrentTime,
    setBuffering,
    buffering,
    volume,
    muted,
    quality
  } = usePlayerStore()
  
  // Cliente-side only
  useEffect(() => {
    setIsClient(true)
  }, [])
  
  // Iniciar trial quando o componente monta
  useEffect(() => {
    if (isClient) {
      startTrial()
    }
  }, [isClient, startTrial])
  
  // Configurar HLS com headers customizados
  useEffect(() => {
    if (!isClient || !videoRef.current || !currentUrl) return
    
    console.log('🎬 Iniciando player IPTV com URL:', currentUrl)
    console.log('📡 Fallback URL disponível:', fallbackUrl)
    
    if (Hls.isSupported()) {
      const hls = new Hls({
        autoStartLoad: true,
        startLevel: quality === 'auto' ? -1 : quality as number,
        capLevelToPlayerSize: true,
        capLevelOnFPSDrop: true,
        maxLoadingDelay: 4,
        minAutoBitrate: 0,
        lowLatencyMode: true,
        // Configurar headers customizados
        xhrSetup: (xhr: XMLHttpRequest) => {
          // Nota: Headers customizados podem causar problemas de CORS
          // Apenas adicionar se realmente necessário
          try {
            if (headers?.['Referer'] && headers['Referer'] !== '') {
              xhr.setRequestHeader('Referer', headers['Referer'])
            }
          } catch (e) {
            console.warn('Não foi possível definir Referer header:', e)
          }
        }
      })
      
      hlsRef.current = hls
      
      hls.on(Hls.Events.MANIFEST_PARSED, (event, data) => {
        console.log('🎬 Stream IPTV carregado:', channelName)
        console.log('📊 Qualidades disponíveis:', data.levels.map(l => `${l.height}p`))
        setIsReady(true)
        setHasError(false)
      })
      
      hls.on(Hls.Events.ERROR, (event, data) => {
        console.error('❌ Erro HLS:', data.type, data.details, data.fatal)
        if (data.fatal) {
          switch (data.type) {
            case Hls.ErrorTypes.NETWORK_ERROR:
              console.error('Erro de rede - tentando fallback...')
              // Tentar fallback imediatamente em erros de rede
              if (!hasFallback && fallbackUrl) {
                console.log('Usando fallback URL:', fallbackUrl)
                setHasFallback(true)
                setCurrentUrl(fallbackUrl)
                hlsRef.current?.destroy()
                // Recriar HLS com fallback
                const newHls = new Hls({
                  autoStartLoad: true,
                  startLevel: -1,
                  capLevelToPlayerSize: true,
                  maxLoadingDelay: 4,
                  lowLatencyMode: true
                })
                newHls.loadSource(fallbackUrl)
                newHls.attachMedia(videoRef.current!)
                hlsRef.current = newHls
                
                newHls.on(Hls.Events.MANIFEST_PARSED, () => {
                  console.log('✅ Fallback stream carregado')
                  setIsReady(true)
                  setHasError(false)
                })
              } else {
                handleError(new Error('Não foi possível conectar ao stream'))
              }
              break
            case Hls.ErrorTypes.MEDIA_ERROR:
              console.error('Erro de mídia - tentando recuperar...')
              hls.recoverMediaError()
              break
            default:
              console.error('Erro fatal - tipo:', data.type)
              handleError(new Error(data.details))
              break
          }
        }
      })
      
      hls.loadSource(currentUrl)
      hls.attachMedia(videoRef.current)
      
      // Auto-play quando pronto
      videoRef.current.addEventListener('loadedmetadata', () => {
        if (isPlaying && videoRef.current) {
          videoRef.current.play().catch(console.error)
        }
      })
    } else if (videoRef.current.canPlayType('application/vnd.apple.mpegurl')) {
      // Fallback para Safari
      videoRef.current.src = currentUrl
      setIsReady(true)
    } else {
      // Se não suporta HLS, usar fallback
      console.warn('HLS não suportado, usando fallback')
      setCurrentUrl(fallbackUrl)
      videoRef.current.src = fallbackUrl
      setIsReady(true)
    }
    
    return () => {
      if (hlsRef.current) {
        hlsRef.current.destroy()
        hlsRef.current = null
      }
    }
  }, [isClient, currentUrl, headers, channelName, quality, isPlaying, fallbackUrl, hasFallback])
  
  // Controles de reprodução
  useEffect(() => {
    if (!videoRef.current) return
    
    const video = videoRef.current
    
    const handlePlay = () => setPlaying(true)
    const handlePause = () => setPlaying(false)
    const handleTimeUpdate = () => setCurrentTime(video.currentTime)
    const handleDurationChange = () => setDuration(video.duration)
    const handleWaiting = () => setBuffering(true)
    const handleCanPlay = () => setBuffering(false)
    
    video.addEventListener('play', handlePlay)
    video.addEventListener('pause', handlePause)
    video.addEventListener('timeupdate', handleTimeUpdate)
    video.addEventListener('durationchange', handleDurationChange)
    video.addEventListener('waiting', handleWaiting)
    video.addEventListener('canplay', handleCanPlay)
    
    return () => {
      video.removeEventListener('play', handlePlay)
      video.removeEventListener('pause', handlePause)
      video.removeEventListener('timeupdate', handleTimeUpdate)
      video.removeEventListener('durationchange', handleDurationChange)
      video.removeEventListener('waiting', handleWaiting)
      video.removeEventListener('canplay', handleCanPlay)
    }
  }, [setPlaying, setCurrentTime, setDuration, setBuffering])
  
  // Controlar volume e mute
  useEffect(() => {
    if (!videoRef.current) return
    videoRef.current.volume = volume
    videoRef.current.muted = muted
  }, [volume, muted])
  
  // Controlar play/pause
  useEffect(() => {
    if (!videoRef.current || !isReady) return
    
    if (isPlaying && !isExpired) {
      videoRef.current.play().catch(console.error)
    } else {
      videoRef.current.pause()
    }
  }, [isPlaying, isExpired, isReady])
  
  const handleError = (error: unknown) => {
    console.error('Error en el reproductor IPTV:', error)
    setHasError(true)
    onError?.(error)
  }
  
  if (!isClient) {
    return (
      <div className="relative aspect-video bg-black rounded-lg overflow-hidden">
        <div className="absolute inset-0 flex items-center justify-center">
          <Loader2 className="h-8 w-8 animate-spin text-white" />
        </div>
      </div>
    )
  }
  
  return (
    <div className="relative aspect-video bg-black rounded-lg overflow-hidden group">
      {!isExpired && (
        <>
          <video
            ref={videoRef}
            className="w-full h-full"
            playsInline
            crossOrigin="anonymous"
          />
          
          {/* Logo do canal */}
          {channelLogo && isReady && !buffering && (
            <div className="absolute top-4 right-4 bg-black/50 p-2 rounded">
              <img 
                src={channelLogo} 
                alt={channelName}
                className="h-12 w-auto object-contain"
              />
            </div>
          )}
          
          {/* Loading indicator */}
          {(buffering || !isReady) && !hasError && (
            <div className="absolute inset-0 flex items-center justify-center bg-black/50">
              <div className="text-center space-y-2">
                <Loader2 className="h-8 w-8 animate-spin text-white mx-auto" />
                <p className="text-white text-sm">
                  {!isReady ? 'Conectando...' : 'Cargando...'}
                </p>
              </div>
            </div>
          )}
          
          {/* Player controls */}
          <PlayerControls 
            playerRef={{ current: { 
              seekTo: (time: number) => {
                if (videoRef.current) videoRef.current.currentTime = time
              }
            } as any }}
            channelName={channelName}
            timeRemaining={timeRemaining}
          />
          
          {/* Error state */}
          {hasError && (
            <div className="absolute inset-0 flex items-center justify-center bg-black">
              <div className="text-center space-y-4">
                <p className="text-white text-lg">
                  Error al cargar el canal
                </p>
                <p className="text-gray-400 text-sm">
                  {channelName}
                </p>
                <button
                  onClick={() => window.location.reload()}
                  className="px-4 py-2 bg-white text-black rounded hover:bg-gray-200"
                >
                  Reintentar
                </button>
              </div>
            </div>
          )}
        </>
      )}
      
      {/* Trial expired overlay */}
      {isExpired && <TrialOverlay />}
    </div>
  )
}