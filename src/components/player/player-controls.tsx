'use client'

import { useState, useRef, useEffect } from 'react'
import {
  Play,
  Pause,
  Volume2,
  VolumeX,
  Maximize,
  Minimize,
  Settings,
  Subtitles,
  Languages,
  Monitor,
  Loader2,
  SkipBack,
  SkipForward
} from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
} from '@/components/ui/dropdown-menu'
import { Slider } from '@/components/ui/slider'
import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'

interface PlayerControlsProps {
  isPlaying: boolean
  isMuted: boolean
  volume: number
  currentTime: number
  duration: number
  isFullscreen: boolean
  isLoading: boolean
  qualities: string[]
  currentQuality: string
  audioTracks: any[]
  currentAudioTrack: number
  subtitles: any[]
  currentSubtitle: number
  onPlayPause: () => void
  onMute: () => void
  onVolumeChange: (volume: number) => void
  onSeek: (time: number) => void
  onFullscreen: () => void
  onQualityChange: (quality: string) => void
  onAudioTrackChange: (track: number) => void
  onSubtitleChange: (track: number) => void
  isLive?: boolean
}

export function PlayerControls({
  isPlaying,
  isMuted,
  volume,
  currentTime,
  duration,
  isFullscreen,
  isLoading,
  qualities,
  currentQuality,
  audioTracks,
  currentAudioTrack,
  subtitles,
  currentSubtitle,
  onPlayPause,
  onMute,
  onVolumeChange,
  onSeek,
  onFullscreen,
  onQualityChange,
  onAudioTrackChange,
  onSubtitleChange,
  isLive = true
}: PlayerControlsProps) {
  const [showVolumeSlider, setShowVolumeSlider] = useState(false)
  const progressRef = useRef<HTMLDivElement>(null)

  const formatTime = (seconds: number) => {
    if (isNaN(seconds) || !isFinite(seconds)) return '0:00'
    const mins = Math.floor(seconds / 60)
    const secs = Math.floor(seconds % 60)
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  const handleProgressClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!progressRef.current || isLive) return
    const rect = progressRef.current.getBoundingClientRect()
    const pos = (e.clientX - rect.left) / rect.width
    onSeek(pos * duration)
  }

  const progress = duration > 0 ? (currentTime / duration) * 100 : 0

  return (
    <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/90 to-transparent p-4">
      {/* Progress bar */}
      {!isLive && (
        <div 
          ref={progressRef}
          className="relative h-1 bg-white/20 rounded-full mb-4 cursor-pointer group"
          onClick={handleProgressClick}
        >
          <div 
            className="absolute h-full bg-red-600 rounded-full transition-all"
            style={{ width: `${progress}%` }}
          />
          <div 
            className="absolute h-3 w-3 bg-white rounded-full -top-1 transition-all opacity-0 group-hover:opacity-100"
            style={{ left: `${progress}%`, transform: 'translateX(-50%)' }}
          />
        </div>
      )}

      <div className="flex items-center justify-between">
        {/* Left controls */}
        <div className="flex items-center gap-2">
          {/* Play/Pause */}
          <Button
            size="sm"
            variant="ghost"
            className="text-white hover:bg-white/20"
            onClick={onPlayPause}
            disabled={isLoading}
          >
            {isLoading ? (
              <Loader2 className="h-5 w-5 animate-spin" />
            ) : isPlaying ? (
              <Pause className="h-5 w-5" />
            ) : (
              <Play className="h-5 w-5" />
            )}
          </Button>

          {/* Skip buttons for non-live content */}
          {!isLive && (
            <>
              <Button
                size="sm"
                variant="ghost"
                className="text-white hover:bg-white/20"
                onClick={() => onSeek(Math.max(0, currentTime - 10))}
              >
                <SkipBack className="h-4 w-4" />
              </Button>
              <Button
                size="sm"
                variant="ghost"
                className="text-white hover:bg-white/20"
                onClick={() => onSeek(Math.min(duration, currentTime + 10))}
              >
                <SkipForward className="h-4 w-4" />
              </Button>
            </>
          )}

          {/* Volume */}
          <div 
            className="flex items-center gap-2"
            onMouseEnter={() => setShowVolumeSlider(true)}
            onMouseLeave={() => setShowVolumeSlider(false)}
          >
            <Button
              size="sm"
              variant="ghost"
              className="text-white hover:bg-white/20"
              onClick={onMute}
            >
              {isMuted || volume === 0 ? (
                <VolumeX className="h-5 w-5" />
              ) : (
                <Volume2 className="h-5 w-5" />
              )}
            </Button>
            <div className={cn(
              "w-24 transition-all",
              showVolumeSlider ? "opacity-100" : "opacity-0 w-0"
            )}>
              <Slider
                value={[isMuted ? 0 : volume * 100]}
                onValueChange={([value]) => onVolumeChange(value / 100)}
                max={100}
                step={1}
                className="cursor-pointer"
              />
            </div>
          </div>

          {/* Time */}
          {!isLive && (
            <div className="text-white text-sm">
              {formatTime(currentTime)} / {formatTime(duration)}
            </div>
          )}

          {/* Live indicator */}
          {isLive && (
            <div className="flex items-center gap-2 px-3 py-1 bg-red-600 rounded">
              <div className="h-2 w-2 bg-white rounded-full animate-pulse" />
              <span className="text-white text-sm font-medium">EN VIVO</span>
            </div>
          )}
        </div>

        {/* Right controls */}
        <div className="flex items-center gap-2">
          {/* Subtitles */}
          {subtitles.length > 0 && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  size="sm"
                  variant="ghost"
                  className="text-white hover:bg-white/20"
                >
                  <Subtitles className="h-5 w-5" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-48">
                <DropdownMenuLabel>Subtítulos</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuRadioGroup 
                  value={currentSubtitle.toString()} 
                  onValueChange={(value) => onSubtitleChange(parseInt(value))}
                >
                  <DropdownMenuRadioItem value="-1">
                    Desactivado
                  </DropdownMenuRadioItem>
                  {subtitles.map((track, index) => (
                    <DropdownMenuRadioItem key={index} value={index.toString()}>
                      {track.label || `Subtítulo ${index + 1}`}
                    </DropdownMenuRadioItem>
                  ))}
                </DropdownMenuRadioGroup>
              </DropdownMenuContent>
            </DropdownMenu>
          )}

          {/* Audio tracks */}
          {audioTracks.length > 1 && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  size="sm"
                  variant="ghost"
                  className="text-white hover:bg-white/20"
                >
                  <Languages className="h-5 w-5" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-48">
                <DropdownMenuLabel>Idioma de Audio</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuRadioGroup 
                  value={currentAudioTrack.toString()} 
                  onValueChange={(value) => onAudioTrackChange(parseInt(value))}
                >
                  {audioTracks.map((track, index) => (
                    <DropdownMenuRadioItem key={index} value={index.toString()}>
                      {track.label || `Audio ${index + 1}`}
                    </DropdownMenuRadioItem>
                  ))}
                </DropdownMenuRadioGroup>
              </DropdownMenuContent>
            </DropdownMenu>
          )}

          {/* Quality */}
          {qualities.length > 0 && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  size="sm"
                  variant="ghost"
                  className="text-white hover:bg-white/20"
                >
                  <Settings className="h-5 w-5" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-48">
                <DropdownMenuLabel>Calidad de Video</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuRadioGroup 
                  value={currentQuality} 
                  onValueChange={onQualityChange}
                >
                  <DropdownMenuRadioItem value="auto">
                    Auto
                  </DropdownMenuRadioItem>
                  {qualities.map((quality) => (
                    <DropdownMenuRadioItem key={quality} value={quality}>
                      {quality}
                    </DropdownMenuRadioItem>
                  ))}
                </DropdownMenuRadioGroup>
              </DropdownMenuContent>
            </DropdownMenu>
          )}

          {/* Picture in Picture */}
          <Button
            size="sm"
            variant="ghost"
            className="text-white hover:bg-white/20"
            onClick={() => {
              const video = document.querySelector('video')
              if (video && document.pictureInPictureEnabled) {
                if (document.pictureInPictureElement) {
                  document.exitPictureInPicture()
                } else {
                  video.requestPictureInPicture()
                }
              }
            }}
          >
            <Monitor className="h-5 w-5" />
          </Button>

          {/* Fullscreen */}
          <Button
            size="sm"
            variant="ghost"
            className="text-white hover:bg-white/20"
            onClick={onFullscreen}
          >
            {isFullscreen ? (
              <Minimize className="h-5 w-5" />
            ) : (
              <Maximize className="h-5 w-5" />
            )}
          </Button>
        </div>
      </div>
    </div>
  )
}