'use client'

import { useRef, useEffect, useState } from 'react'
import Hls from 'hls.js'
import { Button } from '@/components/ui/button'
import { Play, Pause, Loader2 } from 'lucide-react'

interface HLSPlayerProps {
  url: string
  autoPlay?: boolean
  controls?: boolean
  useProxy?: boolean
}

export function HLSPlayer({ url, autoPlay = false, controls = true, useProxy = false }: HLSPlayerProps) {
  const videoRef = useRef<HTMLVideoElement>(null)
  const hlsRef = useRef<Hls | null>(null)
  const [isPlaying, setIsPlaying] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  
  // Usar proxy se necessário para contornar CORS
  const finalUrl = useProxy && url.startsWith('http') && !url.includes(window.location.host)
    ? `/api/proxy?url=${encodeURIComponent(url)}`
    : url
  
  useEffect(() => {
    const video = videoRef.current
    if (!video || !url) return
    
    console.log('🎬 HLSPlayer: Iniciando com URL:', url)
    console.log('🔗 URL final (com proxy?):', finalUrl)
    
    // Se o navegador suporta HLS nativamente (Safari)
    if (video.canPlayType('application/vnd.apple.mpegurl')) {
      console.log('📱 Suporte nativo HLS detectado')
      video.src = finalUrl
      if (autoPlay) {
        video.play().catch(e => {
          console.error('❌ Erro ao tocar (nativo):', e)
          setError(e.message)
        })
      }
      return
    }
    
    // Usar HLS.js
    if (Hls.isSupported()) {
      console.log('✅ HLS.js suportado, configurando...')
      
      const hls = new Hls({
        debug: true,
        enableWorker: true,
        lowLatencyMode: true,
        backBufferLength: 90
      })
      
      hlsRef.current = hls
      
      // Eventos HLS
      hls.on(Hls.Events.MEDIA_ATTACHED, () => {
        console.log('📎 Mídia anexada')
      })
      
      hls.on(Hls.Events.MANIFEST_PARSED, (event, data) => {
        console.log('📋 Manifest parsed:', {
          levels: data.levels.length,
          duration: data.levels[0]?.details?.totalduration
        })
        setIsLoading(false)
        
        if (autoPlay) {
          video.play().catch(e => {
            console.error('❌ Erro ao tocar:', e)
            setError(e.message)
          })
        }
      })
      
      hls.on(Hls.Events.ERROR, (event, data) => {
        console.error('❌ HLS Error:', data)
        
        if (data.fatal) {
          switch (data.type) {
            case Hls.ErrorTypes.NETWORK_ERROR:
              console.error('Network error - tentando recuperar...')
              hls.startLoad()
              break
            case Hls.ErrorTypes.MEDIA_ERROR:
              console.error('Media error - tentando recuperar...')
              hls.recoverMediaError()
              break
            default:
              console.error('Fatal error:', data)
              setError(`Erro fatal: ${data.details}`)
              hls.destroy()
              break
          }
        }
      })
      
      // Carregar source
      hls.loadSource(finalUrl)
      hls.attachMedia(video)
      
      // Cleanup
      return () => {
        hls.destroy()
      }
    } else {
      setError('HLS não suportado neste navegador')
    }
  }, [url, finalUrl, autoPlay])
  
  const handlePlayPause = () => {
    const video = videoRef.current
    if (!video) return
    
    if (video.paused) {
      video.play().then(() => {
        setIsPlaying(true)
      }).catch(e => {
        console.error('❌ Erro ao tocar:', e)
        setError(e.message)
      })
    } else {
      video.pause()
      setIsPlaying(false)
    }
  }
  
  return (
    <div className="relative aspect-video bg-black rounded-lg overflow-hidden">
      <video
        ref={videoRef}
        className="w-full h-full"
        controls={controls}
        muted
        playsInline
        onPlay={() => setIsPlaying(true)}
        onPause={() => setIsPlaying(false)}
        onLoadStart={() => setIsLoading(true)}
        onLoadedData={() => setIsLoading(false)}
        onError={(e) => {
          console.error('❌ Video error:', e)
          const video = e.currentTarget
          if (video.error) {
            setError(`Video error: ${video.error.message}`)
          }
        }}
      />
      
      {/* Custom Play Button */}
      {!controls && !isPlaying && !isLoading && (
        <div className="absolute inset-0 flex items-center justify-center">
          <Button
            onClick={handlePlayPause}
            size="lg"
            className="rounded-full w-16 h-16"
          >
            <Play className="h-8 w-8" />
          </Button>
        </div>
      )}
      
      {/* Loading */}
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-black/50">
          <Loader2 className="h-8 w-8 animate-spin text-white" />
        </div>
      )}
      
      {/* Error */}
      {error && (
        <div className="absolute inset-0 flex items-center justify-center bg-black/90">
          <div className="text-center p-4">
            <p className="text-red-500 mb-2">Erro ao carregar vídeo</p>
            <p className="text-white text-sm">{error}</p>
          </div>
        </div>
      )}
    </div>
  )
}