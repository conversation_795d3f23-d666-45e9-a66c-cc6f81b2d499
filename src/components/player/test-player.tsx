'use client'

import { useEffect, useRef, useState } from 'react'
import ReactPlayer from 'react-player'
import { Button } from '@/components/ui/button'

export function TestPlayer({ url }: { url: string }) {
  const [isPlaying, setIsPlaying] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [ready, setReady] = useState(false)
  const playerRef = useRef<ReactPlayer>(null)
  
  useEffect(() => {
    console.log('🧪 TestPlayer montado com URL:', url)
  }, [url])
  
  return (
    <div className="space-y-4">
      <div className="p-4 bg-gray-100 dark:bg-gray-800 rounded-lg">
        <h3 className="font-bold mb-2">Debug Info:</h3>
        <div className="space-y-1 text-sm font-mono">
          <p>URL: {url}</p>
          <p>Playing: {isPlaying ? 'Sim' : 'Não'}</p>
          <p>Ready: {ready ? 'Sim' : 'Não'}</p>
          <p>Error: {error || 'Nenhum'}</p>
        </div>
      </div>
      
      <div className="relative aspect-video bg-black rounded-lg overflow-hidden">
        <ReactPlayer
          ref={playerRef}
          url={url}
          playing={isPlaying}
          controls={true}
          volume={0.5}
          muted={true}
          width="100%"
          height="100%"
          onReady={() => {
            console.log('✅ Player pronto!')
            setReady(true)
          }}
          onStart={() => {
            console.log('▶️ Tocando')
            setIsPlaying(true)
          }}
          onPause={() => {
            console.log('⏸️ Pausado')
            setIsPlaying(false)
          }}
          onError={(e: any) => {
            console.error('❌ Erro no player:', e)
            setError(e?.message || 'Erro desconhecido')
          }}
          config={{
            file: {
              forceHLS: true,
              attributes: {
                crossOrigin: 'anonymous'
              }
            }
          }}
        />
      </div>
      
      <div className="flex gap-2">
        <Button 
          onClick={() => setIsPlaying(!isPlaying)}
          variant={isPlaying ? "destructive" : "default"}
        >
          {isPlaying ? 'Pausar' : 'Tocar'}
        </Button>
        
        <Button 
          onClick={() => {
            const video = document.querySelector('video')
            if (video) {
              console.log('📺 Estado do vídeo:')
              console.log('- src:', video.src)
              console.log('- currentSrc:', video.currentSrc)
              console.log('- readyState:', video.readyState)
              console.log('- networkState:', video.networkState)
              console.log('- error:', video.error)
              console.log('- paused:', video.paused)
              console.log('- muted:', video.muted)
            }
          }}
          variant="outline"
        >
          Verificar Vídeo
        </Button>
        
        <Button
          onClick={() => {
            // Testar com vídeo público
            const testUrl = 'https://test-streams.mux.dev/x36xhzz/x36xhzz.m3u8'
            console.log('🧪 Testando com URL pública:', testUrl)
            window.location.href = `/test-player?url=${encodeURIComponent(testUrl)}`
          }}
          variant="outline"
        >
          Testar Stream Público
        </Button>
      </div>
    </div>
  )
}