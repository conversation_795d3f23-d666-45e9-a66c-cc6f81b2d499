'use client'

import { useRef, useEffect, useState, useCallback } from 'react'
import Hls from 'hls.js'
import { useTrialStore } from '@/stores/trial.store'
import { TrialOverlay } from './trial-overlay'
import { TrialTimer } from './trial-timer'
import { PlayerControls } from './player-controls'
import { ChannelUnavailable } from './channel-unavailable'
import { Loader2, Play, AlertCircle, WifiOff, ShieldAlert, ServerCrash } from 'lucide-react'
import { logger } from '@/lib/logger'
import { LOG_TAGS } from '@/config/debug'
import { useTrialTimer } from '@/hooks/use-trial-timer'
import { getAllFallbackUrls } from '@/lib/channel-fallbacks'
import { alternativeStreams } from '@/lib/alternative-streams'
import { getProxyUrl } from '@/lib/stream-proxy'
import { useAccessStore } from '@/stores/access.store'
import { useAuthOptional } from '@/hooks/use-auth'

// Função auxiliar para verificar se deve resetar trial
function shouldResetTrial(lastStartTime: number | null): boolean {
  if (!lastStartTime) return false
  const hoursSinceStart = (Date.now() - lastStartTime) / (1000 * 60 * 60)
  return hoursSinceStart > 24 // Permite reset após 24 horas
}

interface EnhancedHlsPlayerProps {
  channelName?: string
  channelId?: string
  streamUrl?: string
  streamUrls?: string[]  // Array de URLs para fallback
  isLive?: boolean
  headers?: Record<string, string>
  userAgent?: string
  epgData?: any
  requiresProxy?: boolean
}

type ErrorType = 'network' | 'cors' | 'geoblocked' | 'invalid' | 'unknown'
type PlaybackState = 'idle' | 'loading' | 'ready' | 'playing' | 'error'

export function EnhancedHlsPlayer({ 
  channelName = 'Deportes en Vivo',
  channelId,
  streamUrl,
  streamUrls,
  isLive = true,
  headers,
  userAgent,
  epgData,
  requiresProxy = false
}: EnhancedHlsPlayerProps) {
  const timestamp = new Date().toISOString()
  console.log(`[HLS-PLAYER] ${timestamp} - ===== COMPONENT INITIALIZED =====`)
  console.log(`[HLS-PLAYER] ${timestamp} - Props received:`)
  console.log(`[HLS-PLAYER] ${timestamp} -   Channel Name:`, channelName)
  console.log(`[HLS-PLAYER] ${timestamp} -   Channel ID:`, channelId)
  console.log(`[HLS-PLAYER] ${timestamp} -   Stream URL:`, streamUrl?.substring(0, 100) + '...')
  console.log(`[HLS-PLAYER] ${timestamp} -   Stream URLs count:`, streamUrls?.length || 0)
  console.log(`[HLS-PLAYER] ${timestamp} -   Is Live:`, isLive)
  console.log(`[HLS-PLAYER] ${timestamp} -   Has Headers:`, !!headers)
  console.log(`[HLS-PLAYER] ${timestamp} -   Headers:`, headers || {})
  console.log(`[HLS-PLAYER] ${timestamp} -   User Agent:`, userAgent || 'none')
  console.log(`[HLS-PLAYER] ${timestamp} -   Requires Proxy:`, requiresProxy)
  const videoRef = useRef<HTMLVideoElement>(null)
  const hlsRef = useRef<Hls | null>(null)
  const containerRef = useRef<HTMLDivElement>(null)
  const [playbackState, setPlaybackState] = useState<PlaybackState>('idle')
  const [showPlayButton, setShowPlayButton] = useState(true)
  const [buffering, setBuffering] = useState(false)
  const [errorType, setErrorType] = useState<ErrorType>('unknown')
  const [errorDetails, setErrorDetails] = useState<string>('')
  const [usingProxy, setUsingProxy] = useState(false)
  const [retryCount, setRetryCount] = useState(0)
  const [currentUrlIndex, setCurrentUrlIndex] = useState(0)
  const [allUrls, setAllUrls] = useState<string[]>([])
  
  // Player controls state
  const [isPlaying, setIsPlaying] = useState(false)
  const [isMuted, setIsMuted] = useState(false)
  const [volume, setVolume] = useState(1)
  const [currentTime, setCurrentTime] = useState(0)
  const [duration, setDuration] = useState(0)
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [qualities, setQualities] = useState<string[]>([])
  const [currentQuality, setCurrentQuality] = useState('auto')
  const [audioTracks, setAudioTracks] = useState<any[]>([])
  const [currentAudioTrack, setCurrentAudioTrack] = useState(0)
  const [subtitles, setSubtitles] = useState<any[]>([])
  const [currentSubtitle, setCurrentSubtitle] = useState(-1)
  const [showControls, setShowControls] = useState(true)
  const controlsTimeoutRef = useRef<NodeJS.Timeout>()
  
  const { isExpired, startTrial, checkTrialStatus } = useTrialStore()
  const { timeRemaining } = useTrialTimer()
  const { hasAccess, accessType, checkAccess } = useAccessStore()
  const { user } = useAuthOptional()
  const [hasPremiumAccess, setHasPremiumAccess] = useState(false)
  const [isCheckingAccess, setIsCheckingAccess] = useState(true)
  const [hasInitialized, setHasInitialized] = useState(false)
  
  // Verificar acesso e trial - igual ao VideoPlayer
  useEffect(() => {
    console.log('🔍 [ENHANCED-HLS-PLAYER] checkAccessAndTrial effect triggered', {
      hasInitialized,
      user: user?.primaryEmail,
      hasAccess,
      isExpired,
      isWindow: typeof window !== 'undefined'
    })
    
    // Verificar se está no cliente e não foi inicializado ainda
    if (typeof window !== 'undefined' && !hasInitialized) {
      const checkAccessAndTrial = async () => {
        console.log('🚀 [ENHANCED-HLS-PLAYER] ===== CHECK ACCESS AND TRIAL START =====', {
          hasUser: !!user,
          userId: user?.id,
          userEmail: user?.primaryEmail,
          hasAccess,
          isExpired,
          startTime: useTrialStore.getState().startTime,
          timeRemaining: useTrialStore.getState().timeRemaining,
          timestamp: new Date().toISOString()
        })
        
        setIsCheckingAccess(true)
        
        try {
          // Primeiro verificar se tem acesso (cupom ou assinatura)
          if (user) {
            console.log('[ENHANCED-HLS-PLAYER] Checking access for user:', user.id)
            
            // Adicionar try-catch para checkAccess
            try {
              // Forçar refresh na primeira verificação
              await checkAccess(user.id, true)
            } catch (error) {
              console.error('❌ [ENHANCED-HLS-PLAYER] Error checking access:', error)
              console.log('🚫 [ENHANCED-HLS-PLAYER] Denying access due to error (fail-safe)')
              // Em caso de erro, negar acesso por segurança
              useAccessStore.getState().clearAccess()
            }
            
            // Verificar novamente o estado após checkAccess
            const newAccessState = useAccessStore.getState()
            console.log('[ENHANCED-HLS-PLAYER] Access state after check:', {
              hasAccess: newAccessState.hasAccess,
              accessType: newAccessState.accessType,
              expiresAt: newAccessState.expiresAt
            })
            
            // Se tem acesso premium, limpar o trial
            if (newAccessState.hasAccess && newAccessState.accessType === 'assinatura') {
              console.log('✅ [ENHANCED-HLS-PLAYER] User has PREMIUM access!', {
                accessType: newAccessState.accessType,
                expiresAt: newAccessState.expiresAt
              })
              useTrialStore.getState().reset()
              setIsCheckingAccess(false)
              setHasPremiumAccess(true)
              return
            }
          }
          
          // Se não tem acesso, iniciar/verificar trial
          const currentAccessState = useAccessStore.getState()
          if (!currentAccessState.hasAccess) {
            console.log('⏰ [ENHANCED-HLS-PLAYER] No premium access, checking trial status...', {
              currentAccessState
            })
            
            // Primeiro verificar o status atual
            await checkTrialStatus(user?.id)
            
            // SEMPRE iniciar trial se não tem acesso premium
            const currentState = useTrialStore.getState()
            console.log('🔍 [ENHANCED-HLS-PLAYER] Current trial state:', currentState)
            
            if (!currentState.startTime || currentState.startTime === 0) {
              console.log('🚀 [ENHANCED-HLS-PLAYER] No premium access, starting trial NOW', {
                reason: 'No startTime - new user needs trial',
                userId: user?.id
              })
              await startTrial(user?.id)
              
              // Log estado após iniciar trial
              const newTrialState = useTrialStore.getState()
              console.log('📊 [ENHANCED-HLS-PLAYER] Trial state AFTER startTrial:', {
                startTime: newTrialState.startTime,
                timeRemaining: newTrialState.timeRemaining,
                isExpired: newTrialState.isExpired
              })
            } else if (currentState.isExpired && shouldResetTrial(currentState.startTime)) {
              console.log('🔄 [ENHANCED-HLS-PLAYER] Resetting expired trial', {
                lastStartTime: currentState.startTime,
                userId: user?.id
              })
              await startTrial(user?.id)
            } else {
              console.log('⏰ [ENHANCED-HLS-PLAYER] Trial already active', {
                startTime: currentState.startTime,
                timeRemaining: currentState.timeRemaining,
                isExpired: currentState.isExpired
              })
            }
          } else {
            console.log('[ENHANCED-HLS-PLAYER] User has access, skipping trial')
            setHasPremiumAccess(true)
          }
        } catch (error) {
          console.error('❌ [ENHANCED-HLS-PLAYER] Fatal error in checkAccessAndTrial:', error)
          // Em caso de erro fatal, bloquear acesso
          useAccessStore.getState().clearAccess()
          setHasPremiumAccess(false)
        } finally {
          console.log('✅ [ENHANCED-HLS-PLAYER] Access check COMPLETED')
          setIsCheckingAccess(false)
        }
      }
      
      checkAccessAndTrial()
      setHasInitialized(true)
    }
  }, [hasInitialized, user, checkAccess, checkTrialStatus, startTrial])
  
  // Log inicial separado
  useEffect(() => {
    console.log('🚨 [ENHANCED-HLS-PLAYER] ===== COMPONENT MOUNTED =====')
    console.log('👤 [ENHANCED-HLS-PLAYER] User:', user?.id, user?.primaryEmail)
    console.log('⏰ [ENHANCED-HLS-PLAYER] Trial expired:', isExpired)
    console.log('🔓 [ENHANCED-HLS-PLAYER] Has access from store:', hasAccess)
    console.log('🎫 [ENHANCED-HLS-PLAYER] Access type:', accessType)
    console.log('⏱️ [ENHANCED-HLS-PLAYER] Time remaining:', timeRemaining)
    console.log('📍 [ENHANCED-HLS-PLAYER] Trial start time:', useTrialStore.getState().startTime)
    
    // Configurar array de URLs incluindo fallbacks
    const urls: string[] = []
    
    // Primeiro adicionar URLs fornecidas
    if (streamUrls && streamUrls.length > 0) {
      urls.push(...streamUrls)
    } else if (streamUrl) {
      urls.push(streamUrl)
    }
    
    // Se temos channelId, buscar URLs de fallback adicionais
    if (channelId) {
      const fallbackData = getAllFallbackUrls(channelId)
      const fallbackUrls = fallbackData.map(data => data.url).filter(url => url && !urls.includes(url))
      if (fallbackUrls.length > 0) {
        console.log('🎬 [EnhancedHlsPlayer] Adicionando URLs de fallback:', fallbackUrls)
        urls.push(...fallbackUrls)
      }
      
      // Adicionar também URLs alternativas
      const alternativeUrls = alternativeStreams[channelId]
      if (alternativeUrls && alternativeUrls.length > 0) {
        console.log('🎬 [EnhancedHlsPlayer] Adicionando URLs alternativas:', alternativeUrls)
        alternativeUrls.forEach(url => {
          if (!urls.includes(url)) {
            urls.push(url)
          }
        })
      }
    }
    
    setAllUrls(urls)
    
    console.log('🎬 [EnhancedHlsPlayer] ===== PLAYER MONTADO =====')
    console.log('🎬 [EnhancedHlsPlayer] Canal:', channelName)
    console.log('🎬 [EnhancedHlsPlayer] ID:', channelId)
    console.log('🎬 [EnhancedHlsPlayer] URLs disponíveis:', urls)
    console.log('🎬 [EnhancedHlsPlayer] Total URLs:', urls.length)
    console.log('🎬 [EnhancedHlsPlayer] isLive:', isLive)
    console.log('🎬 [EnhancedHlsPlayer] Headers:', headers)
    console.log('🎬 [EnhancedHlsPlayer] User-Agent:', userAgent)
    console.log('🎬 [EnhancedHlsPlayer] Requires Proxy:', requiresProxy)
    
    logger.info(LOG_TAGS.PLAYER, 'EnhancedHlsPlayer mounted', {
      channelName,
      streamUrl,
      streamUrls: urls.length,
      isLive,
      hasHeaders: !!headers,
      hasUserAgent: !!userAgent
    })
  }, [channelName, streamUrl, streamUrls, isLive, headers, userAgent])
  
  // Atualizar hasPremiumAccess quando hasAccess mudar
  useEffect(() => {
    setHasPremiumAccess(hasAccess)
    console.log('🔄 [ENHANCED-HLS-PLAYER] Premium access updated:', {
      hasAccess,
      accessType,
      hasPremiumAccess: hasAccess
    })
  }, [hasAccess, accessType])
  
  // Show/hide controls
  const showControlsTemporarily = useCallback(() => {
    setShowControls(true)
    if (controlsTimeoutRef.current) {
      clearTimeout(controlsTimeoutRef.current)
    }
    controlsTimeoutRef.current = setTimeout(() => {
      if (isPlaying) {
        setShowControls(false)
      }
    }, 3000)
  }, [isPlaying])
  
  // Control functions
  const handlePlayPause = useCallback(() => {
    if (!videoRef.current) return
    
    if (isPlaying) {
      videoRef.current.pause()
    } else {
      videoRef.current.play()
    }
  }, [isPlaying])
  
  const handleMute = useCallback(() => {
    if (!videoRef.current) return
    setIsMuted(!isMuted)
    videoRef.current.muted = !isMuted
  }, [isMuted])
  
  const handleVolumeChange = useCallback((newVolume: number) => {
    if (!videoRef.current) return
    setVolume(newVolume)
    videoRef.current.volume = newVolume
    if (newVolume > 0 && isMuted) {
      setIsMuted(false)
      videoRef.current.muted = false
    }
  }, [isMuted])
  
  const handleSeek = useCallback((time: number) => {
    if (!videoRef.current || isLive) return
    videoRef.current.currentTime = time
  }, [isLive])
  
  const handleFullscreen = useCallback(() => {
    if (!containerRef.current) return
    
    if (!document.fullscreenElement) {
      containerRef.current.requestFullscreen()
    } else {
      document.exitFullscreen()
    }
  }, [])
  
  const handleQualityChange = useCallback((quality: string) => {
    if (!hlsRef.current) return
    
    if (quality === 'auto') {
      hlsRef.current.currentLevel = -1
    } else {
      const levelIndex = hlsRef.current.levels.findIndex(
        level => `${level.height}p` === quality
      )
      if (levelIndex >= 0) {
        hlsRef.current.currentLevel = levelIndex
      }
    }
    setCurrentQuality(quality)
  }, [])
  
  const handleAudioTrackChange = useCallback((trackIndex: number) => {
    if (!hlsRef.current) return
    hlsRef.current.audioTrack = trackIndex
    setCurrentAudioTrack(trackIndex)
  }, [])
  
  const handleSubtitleChange = useCallback((trackIndex: number) => {
    if (!hlsRef.current) return
    hlsRef.current.subtitleTrack = trackIndex
    setCurrentSubtitle(trackIndex)
  }, [])
  
  // Determinar tipo de erro baseado nos detalhes
  const analyzeError = (error: any): { type: ErrorType; message: string } => {
    const details = error?.details || ''
    const response = error?.response || {}
    
    // Erro de CORS/CSP
    if (details.includes('manifestLoadError') && !usingProxy) {
      return { 
        type: 'cors', 
        message: 'Problema de CORS detectado. Tentando com proxy...' 
      }
    }
    
    // Erro 403 - Geoblocking ou autenticação
    if (response.code === 403 || details.includes('403')) {
      return { 
        type: 'geoblocked', 
        message: 'Este canal pode estar bloqueado na sua região ou requer autenticação' 
      }
    }
    
    // Erro 404 - URL inválida
    if (response.code === 404 || details.includes('404')) {
      return { 
        type: 'invalid', 
        message: 'O canal não está mais disponível ou a URL expirou' 
      }
    }
    
    // Erro de rede genérico
    if (error.type === 'networkError') {
      return { 
        type: 'network', 
        message: 'Erro de conexão. Verifique sua internet' 
      }
    }
    
    return { 
      type: 'unknown', 
      message: 'Erro desconhecido ao carregar o canal' 
    }
  }
  
  // Video event handlers
  useEffect(() => {
    const video = videoRef.current
    if (!video) return
    
    const handlePlay = () => {
      setIsPlaying(true)
      setPlaybackState('playing')
    }
    
    const handlePause = () => {
      setIsPlaying(false)
      if (playbackState === 'playing') {
        setPlaybackState('ready')
      }
    }
    
    const handleTimeUpdate = () => {
      setCurrentTime(video.currentTime)
    }
    
    const handleDurationChange = () => {
      setDuration(video.duration)
    }
    
    const handleVolumeChange = () => {
      setVolume(video.volume)
      setIsMuted(video.muted)
    }
    
    const handleWaiting = () => setBuffering(true)
    const handlePlaying = () => setBuffering(false)
    
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement)
    }
    
    // Add event listeners
    video.addEventListener('play', handlePlay)
    video.addEventListener('pause', handlePause)
    video.addEventListener('timeupdate', handleTimeUpdate)
    video.addEventListener('durationchange', handleDurationChange)
    video.addEventListener('volumechange', handleVolumeChange)
    video.addEventListener('waiting', handleWaiting)
    video.addEventListener('playing', handlePlaying)
    document.addEventListener('fullscreenchange', handleFullscreenChange)
    
    return () => {
      video.removeEventListener('play', handlePlay)
      video.removeEventListener('pause', handlePause)
      video.removeEventListener('timeupdate', handleTimeUpdate)
      video.removeEventListener('durationchange', handleDurationChange)
      video.removeEventListener('volumechange', handleVolumeChange)
      video.removeEventListener('waiting', handleWaiting)
      video.removeEventListener('playing', handlePlaying)
      document.removeEventListener('fullscreenchange', handleFullscreenChange)
    }
  }, [playbackState])
  
  // Configurar HLS
  const setupHLS = async (useProxy: boolean = false, urlIndex: number = currentUrlIndex) => {
    console.log('🎬 [setupHLS] ===== INICIANDO SETUP HLS =====')
    console.log('🎬 [setupHLS] Use Proxy:', useProxy)
    console.log('🎬 [setupHLS] URL Index:', urlIndex)
    console.log('🎬 [setupHLS] All URLs:', allUrls)
    
    if (!videoRef.current || allUrls.length === 0 || urlIndex >= allUrls.length) {
      if (urlIndex >= allUrls.length) {
        console.error('🎬 [setupHLS] ❌ TODAS AS URLs FALHARAM!')
        logger.error(LOG_TAGS.PLAYER, 'Todas as URLs falharam', {
          totalUrls: allUrls.length,
          channelName
        })
        setErrorType('invalid')
        setErrorDetails('Nenhuma fonte disponível para este canal')
        setPlaybackState('error')
      }
      return
    }
    
    const video = videoRef.current
    const currentStreamUrl = allUrls[urlIndex]
    setPlaybackState('loading')
    setUsingProxy(useProxy)
    setCurrentUrlIndex(urlIndex)
    
    // Limpar instância anterior
    if (hlsRef.current) {
      hlsRef.current.destroy()
      hlsRef.current = null
    }
    
    // Determinar URL
    let playbackUrl = currentStreamUrl
    const hlsTimestamp = new Date().toISOString()
    console.log(`[HLS-PLAYER] ${hlsTimestamp} - setupHLS called:`)
    console.log(`[HLS-PLAYER] ${hlsTimestamp} -   URL Index:`, urlIndex)
    console.log(`[HLS-PLAYER] ${hlsTimestamp} -   Total URLs:`, allUrls.length)
    console.log(`[HLS-PLAYER] ${hlsTimestamp} -   Current URL:`, currentStreamUrl)
    console.log(`[HLS-PLAYER] ${hlsTimestamp} -   Use Proxy:`, useProxy)
    console.log(`[HLS-PLAYER] ${hlsTimestamp} -   Retry Count:`, retryCount)
    console.log(`[HLS-PLAYER] ${hlsTimestamp} -   Channel ID:`, channelId)
    console.log(`[HLS-PLAYER] ${hlsTimestamp} -   Headers provided:`, !!headers)
    
    if (useProxy) {
      // Usar a lógica correta de proxy baseada no domínio
      playbackUrl = getProxyUrl(currentStreamUrl, {
        channelId,
        channelName,
        headers,
      })
      console.log(`[HLS-PLAYER] ${hlsTimestamp} -   Proxy URL:`, playbackUrl)
    } else {
      console.log(`[HLS-PLAYER] ${hlsTimestamp} -   Direct URL (no proxy)`)
    }
    
    logger.info(LOG_TAGS.PLAYER, `Attempting ${useProxy ? 'proxy' : 'direct'} playback`, {
      url: playbackUrl,
      urlIndex,
      totalUrls: allUrls.length,
      retryCount
    })
    
    if (Hls.isSupported() && currentStreamUrl.includes('.m3u8')) {
      const hls = new Hls({
        autoStartLoad: true,
        startLevel: -1,
        capLevelToPlayerSize: true,
        capLevelOnFPSDrop: true,
        maxLoadingDelay: 4,
        minAutoBitrate: 0,
        lowLatencyMode: true,
        enableWorker: true,
        maxBufferLength: 30,
        maxMaxBufferLength: 600,
        backBufferLength: 30,
        // Configurações otimizadas para evitar timeouts
        testBandwidth: false,
        debug: false,
        enableSoftwareAES: true,
        fragLoadingTimeOut: 30000, // Aumentado para 30s
        fragLoadingMaxRetry: 6,
        fragLoadingMaxRetryTimeout: 64000,
        manifestLoadingTimeOut: 20000, // Aumentado para 20s
        manifestLoadingMaxRetry: 4,
        manifestLoadingMaxRetryTimeout: 64000,
        levelLoadingTimeOut: 20000, // Aumentado para 20s
        levelLoadingMaxRetry: 4,
        levelLoadingMaxRetryTimeout: 64000,
        // Novas configurações para melhor performance
        initialLiveManifestSize: 3,
        maxBufferHole: 0.5,
        maxFragLookUpTolerance: 0.25,
        nudgeOffset: 0.1,
        nudgeMaxRetry: 3,
        startFragPrefetch: false,
        xhrSetup: (xhr: XMLHttpRequest) => {
          if (!useProxy && headers) {
            Object.entries(headers).forEach(([key, value]) => {
              xhr.setRequestHeader(key, value)
            })
          }
        }
      })
      
      hlsRef.current = hls
      
      // Eventos de sucesso
      hls.on(Hls.Events.MANIFEST_PARSED, (event, data) => {
        console.log('🎬 [HLS] ✅ MANIFEST PARSED COM SUCESSO!')
        console.log('🎬 [HLS] Níveis:', data.levels)
        console.log('🎬 [HLS] Audio tracks:', data.audioTracks)
        
        logger.info(LOG_TAGS.PLAYER, 'HLS manifest parsed successfully')
        setPlaybackState('ready')
        setRetryCount(0)
        
        // Configurar qualidades disponíveis
        if (data.levels && data.levels.length > 0) {
          const qualityOptions = data.levels.map(level => `${level.height}p`)
          setQualities(qualityOptions)
          logger.info(LOG_TAGS.PLAYER, 'Available qualities', { qualities: qualityOptions })
        }
      })
      
      // Quando os tracks de áudio são carregados
      hls.on(Hls.Events.AUDIO_TRACKS_UPDATED, (event, data) => {
        if (data.audioTracks && data.audioTracks.length > 0) {
          setAudioTracks(data.audioTracks)
          logger.info(LOG_TAGS.PLAYER, 'Audio tracks available', { 
            tracks: data.audioTracks.map(t => ({ name: t.name, lang: t.lang })) 
          })
        }
      })
      
      // Quando as legendas são carregadas
      hls.on(Hls.Events.SUBTITLE_TRACKS_UPDATED, (event, data) => {
        if (data.subtitleTracks && data.subtitleTracks.length > 0) {
          setSubtitles(data.subtitleTracks)
          logger.info(LOG_TAGS.PLAYER, 'Subtitle tracks available', { 
            tracks: data.subtitleTracks.map(t => ({ name: t.name, lang: t.lang })) 
          })
        }
      })
      
      // Quando a qualidade é mudada
      hls.on(Hls.Events.LEVEL_SWITCHED, (event, data) => {
        const level = hls.levels[data.level]
        if (level) {
          setCurrentQuality(`${level.height}p`)
        }
      })
      
      // Eventos de erro
      hls.on(Hls.Events.ERROR, (event, data) => {
        const errorTimestamp = new Date().toISOString()
        // Logar todos os erros para debug
        console.error(`[HLS-PLAYER] ${errorTimestamp} - ❌ HLS ERROR DETECTED`)
        console.error(`[HLS-PLAYER] ${errorTimestamp} -   Type:`, data.type)
        console.error(`[HLS-PLAYER] ${errorTimestamp} -   Details:`, data.details)
        console.error(`[HLS-PLAYER] ${errorTimestamp} -   Fatal:`, data.fatal)
        console.error(`[HLS-PLAYER] ${errorTimestamp} -   URL:`, data.url)
        console.error(`[HLS-PLAYER] ${errorTimestamp} -   Response:`, data.response)
        console.error(`[HLS-PLAYER] ${errorTimestamp} -   Context:`, data.context)
        console.error(`[HLS-PLAYER] ${errorTimestamp} -   Network Details:`, data.networkDetails)
        console.error(`[HLS-PLAYER] ${errorTimestamp} -   Full error data:`, JSON.stringify(data, null, 2))
        
        const { type, message } = analyzeError(data)
        
        logger.error(LOG_TAGS.PLAYER, 'HLS error', {
          type: data.type,
          details: data.details,
          fatal: data.fatal,
          errorType: type,
          url: data.url || playbackUrl,
          channelName,
          usingProxy
        })
        
        // Tratar erros não-fatais também
        if (!data.fatal && data.type === Hls.ErrorTypes.NETWORK_ERROR && data.details === 'fragLoadError') {
          console.warn('🎬 [HLS] Erro ao carregar fragmento, tentando recuperar...')
          // Para erro de fragmento, não fazer nada - HLS.js tentará automaticamente
          return
        }
        
        if (data.fatal) {
          switch (data.type) {
            case Hls.ErrorTypes.NETWORK_ERROR:
              console.error('🎬 [HLS] NETWORK_ERROR - Tentando próxima estratégia...')
              
              // Se detectamos erro de CORS e não estamos usando proxy, tentar com proxy
              if ((data.details === 'manifestLoadError' || data.details === 'fragLoadError') && !useProxy) {
                console.log('🎬 [HLS] Erro de CORS detectado. Forçando uso de PROXY...')
                logger.info(LOG_TAGS.PLAYER, 'CORS error detected, forcing proxy', {
                  channelName,
                  url: data.url
                })
                hls.destroy()
                hlsRef.current = null
                setupHLS(true, urlIndex) // Tentar mesma URL com proxy
              }
              // Tentar próxima URL se disponível
              else if (urlIndex + 1 < allUrls.length) {
                console.log('🎬 [HLS] Tentando próxima URL:', urlIndex + 1)
                logger.info(LOG_TAGS.PLAYER, 'Tentando próxima URL', {
                  currentIndex: urlIndex,
                  nextIndex: urlIndex + 1,
                  totalUrls: allUrls.length
                })
                hls.destroy()
                hlsRef.current = null
                // Tentar próxima URL
                setupHLS(useProxy, urlIndex + 1)
              } else if (!useProxy) {
                // Se todas as URLs falharam e ainda não tentamos com proxy
                console.log('🎬 [HLS] Todas URLs diretas falharam. Tentando com PROXY...')
                logger.info(LOG_TAGS.PLAYER, 'Todas as URLs falharam, tentando com proxy')
                hls.destroy()
                hlsRef.current = null
                setupHLS(true, 0) // Recomeçar com proxy
              } else {
                // Mostrar erro final
                console.error('🎬 [HLS] ❌ TODAS AS TENTATIVAS FALHARAM!')
                setErrorType(type)
                setErrorDetails(message)
                setPlaybackState('error')
              }
              break
              
            case Hls.ErrorTypes.MEDIA_ERROR:
              if (data.details === 'manifestIncompatibleCodecsError') {
                // Tentar fallback para stream direto sem HLS.js
                logger.info(LOG_TAGS.PLAYER, 'Codec incompatível, tentando reprodução direta')
                hls.destroy()
                hlsRef.current = null
                
                // Tentar reprodução direta no elemento de vídeo
                if (video) {
                  video.src = playbackUrl
                  video.play().catch(err => {
                    logger.error(LOG_TAGS.PLAYER, 'Falha na reprodução direta', { error: err })
                    setErrorType('invalid')
                    setErrorDetails('Este canal usa um formato incompatível')
                    setPlaybackState('error')
                  })
                }
              } else if (data.details === 'bufferStalledError' || data.details === 'bufferAppendError') {
                console.log('🎬 [HLS] Buffer error, tentando recuperar...')
                // Para erros de buffer, tentar recuperar
                hls.recoverMediaError()
                // Se continuar falhando, recriar o player
                setTimeout(() => {
                  if (hlsRef.current && playbackState === 'playing') {
                    const currentTime = video.currentTime
                    console.log('🎬 [HLS] Recriando player após erro de buffer...')
                    hls.destroy()
                    setupHLS(useProxy, urlIndex)
                    if (video && currentTime > 0) {
                      video.currentTime = currentTime
                    }
                  }
                }, 2000)
              } else {
                // Tentar recuperar erro de mídia
                hls.recoverMediaError()
              }
              break
              
            default:
              setErrorType(type)
              setErrorDetails(message)
              setPlaybackState('error')
              break
          }
        }
      })
      
      // Eventos de buffering
      hls.on(Hls.Events.FRAG_BUFFERED, () => {
        setBuffering(false)
      })
      
      console.log('🎬 [HLS] Carregando source:', playbackUrl)
      hls.loadSource(playbackUrl)
      hls.attachMedia(video)
      console.log('🎬 [HLS] Mídia anexada ao player')
      
    } else if (video.canPlayType('application/vnd.apple.mpegurl')) {
      // Safari native HLS
      video.src = playbackUrl
      setPlaybackState('ready')
    } else {
      // Navegador não suporta HLS
      setErrorType('invalid')
      setErrorDetails('Seu navegador não suporta reprodução HLS')
      setPlaybackState('error')
    }
  }
  
  // Inicializar player
  useEffect(() => {
    console.log('🎬 [useEffect] Inicializando player...')
    console.log('🎬 [useEffect] URLs:', allUrls)
    console.log('🎬 [useEffect] Requires Proxy:', requiresProxy)
    
    if (allUrls.length > 0) {
      // Começar do início com a primeira URL
      setCurrentUrlIndex(0)
      setupHLS(requiresProxy, 0)
    } else {
      console.warn('🎬 [useEffect] Nenhuma URL disponível!')
    }
    
    return () => {
      if (hlsRef.current) {
        hlsRef.current.destroy()
        hlsRef.current = null
      }
    }
  }, [allUrls, requiresProxy])
  
  const handlePlay = async () => {
    console.log('[TRIAL-PLAYER] handlePlay called', {
      isExpired,
      hasPremiumAccess,
      playbackState,
      hasVideo: !!videoRef.current
    })
    
    // Permitir reprodução se tiver acesso premium OU se trial não expirou
    if ((hasPremiumAccess || !isExpired) && videoRef.current && playbackState === 'ready') {
      try {
        setShowPlayButton(false)
        await videoRef.current.play()
        setPlaybackState('playing')
        logger.info(LOG_TAGS.PLAYER, 'Playback started')
      } catch (err) {
        logger.error(LOG_TAGS.PLAYER, 'Failed to start playback', { error: err })
        setShowPlayButton(true)
      }
    } else if (isExpired && !hasPremiumAccess) {
      console.log('[TRIAL-PLAYER] Cannot play - trial expired and no premium access')
    }
  }
  
  const handleRetry = () => {
    setPlaybackState('idle')
    setErrorType('unknown')
    setErrorDetails('')
    setRetryCount(0)
    setCurrentUrlIndex(0)
    setupHLS(false, 0)
  }
  
  // Pausar quando expirar (apenas se não tiver premium)
  useEffect(() => {
    console.log('🕑 [ENHANCED-HLS-PLAYER] isExpired/hasPremiumAccess changed:', {
      isExpired,
      hasPremiumAccess,
      timeRemaining,
      shouldBlock: isExpired && !hasPremiumAccess
    })
    
    if (isExpired && !hasPremiumAccess) {
      console.log('🚫🚫🚫 [ENHANCED-HLS-PLAYER] TRIAL EXPIRADO - BLOQUEANDO VÍDEO!')
      if (videoRef.current) {
        videoRef.current.pause()
        setPlaybackState('idle')
      }
    } else if (!isExpired) {
      console.log('✅ [ENHANCED-HLS-PLAYER] Trial ainda ativo, permitindo reprodução')
    } else if (hasPremiumAccess) {
      console.log('👑 [ENHANCED-HLS-PLAYER] Usuário premium, permitindo reprodução')
    }
  }, [isExpired, hasPremiumAccess, timeRemaining])
  
  // Forçar verificação quando trial expirar
  useEffect(() => {
    if (isExpired && user && !isCheckingAccess) {
      console.log('[ENHANCED-HLS-PLAYER] Trial expired! Forcing access check...')
      // Limpar cache do access store
      useAccessStore.getState().clearAccess()
      // Forçar nova verificação
      checkAccess(user.id, true).then(() => {
        console.log('[ENHANCED-HLS-PLAYER] Access check completed after trial expiration')
      })
    }
  }, [isExpired, user, checkAccess, isCheckingAccess])
  
  // Renderizar ícone de erro baseado no tipo
  const renderErrorIcon = () => {
    switch (errorType) {
      case 'network':
        return <WifiOff className="h-12 w-12 text-red-500 mx-auto" />
      case 'geoblocked':
        return <ShieldAlert className="h-12 w-12 text-orange-500 mx-auto" />
      case 'invalid':
        return <ServerCrash className="h-12 w-12 text-gray-500 mx-auto" />
      default:
        return <AlertCircle className="h-12 w-12 text-red-500 mx-auto" />
    }
  }
  
  return (
    <div 
      ref={containerRef}
      className="relative aspect-video bg-black rounded-lg overflow-hidden group"
      onMouseMove={showControlsTemporarily}
      onMouseLeave={() => {
        if (isPlaying && controlsTimeoutRef.current) {
          clearTimeout(controlsTimeoutRef.current)
          controlsTimeoutRef.current = setTimeout(() => {
            setShowControls(false)
          }, 3000)
        }
      }}
    >
      {(() => {
        // Não bloquear enquanto está verificando acesso
        if (isCheckingAccess) {
          return true // Mostrar vídeo enquanto verifica
        }
        
        // Mesma lógica de bloqueio
        const currentAccessState = useAccessStore.getState()
        const currentTrialState = useTrialStore.getState()
        
        const shouldBlockNoTrial = !currentTrialState.startTime && !currentAccessState.hasAccess && !isCheckingAccess
        const shouldBlockExpired = currentTrialState.isExpired && !currentAccessState.hasAccess
        const shouldBlock = shouldBlockNoTrial || shouldBlockExpired
        
        return !shouldBlock // Mostrar vídeo se NÃO deve bloquear
      })() && (
        <>
          <video
            ref={videoRef}
            className="w-full h-full"
            playsInline
            onLoadedMetadata={() => {
              logger.info(LOG_TAGS.PLAYER, 'Video metadata loaded')
            }}
          />
          
          {/* Overlay com informações do canal */}
          {playbackState === 'playing' && (
            <div className="absolute top-4 left-4 right-4 flex items-center justify-between pointer-events-none">
              <div className="flex items-center gap-2">
                <div className="bg-black/70 px-3 py-1.5 rounded-full">
                  <p className="text-white text-sm font-medium">{channelName}</p>
                </div>
                {/* Timer do trial - apenas se não tiver premium */}
                {!hasPremiumAccess && <TrialTimer />}
              </div>
              <div className="flex items-center gap-4">
                {usingProxy && (
                  <div className="bg-blue-600/70 px-3 py-1 rounded-full">
                    <span className="text-white text-xs font-medium">PROXY</span>
                  </div>
                )}
                {isLive && (
                  <div className="flex items-center gap-2 bg-red-600 px-3 py-1 rounded-full">
                    <div className="h-2 w-2 bg-white rounded-full animate-pulse" />
                    <span className="text-white text-sm font-medium">EN VIVO</span>
                  </div>
                )}
              </div>
            </div>
          )}
          
          {/* Botão de play */}
          {showPlayButton && playbackState === 'ready' && (
            <div 
              className="absolute inset-0 flex items-center justify-center bg-black/30 cursor-pointer"
              onClick={handlePlay}
            >
              <button className="bg-white/10 backdrop-blur-sm hover:bg-white/20 rounded-full p-6 transition-all">
                <Play className="h-12 w-12 text-white fill-white" />
              </button>
            </div>
          )}
          
          {/* Loading/Buffering */}
          {(playbackState === 'loading' || buffering) && (
            <div className="absolute inset-0 flex items-center justify-center bg-black/50">
              <div className="text-center space-y-2">
                <Loader2 className="h-8 w-8 animate-spin text-white mx-auto" />
                <p className="text-white text-sm">
                  {playbackState === 'loading' ? `Cargando ${channelName}...` : 'Buffering...'}
                </p>
                {usingProxy && playbackState === 'loading' && (
                  <p className="text-gray-400 text-xs">
                    Usando proxy para contornar restrições...
                  </p>
                )}
              </div>
            </div>
          )}
          
          {/* Error state */}
          {playbackState === 'error' && (
            errorType === 'geoblocked' || (requiresProxy && !usingProxy) ? (
              <ChannelUnavailable 
                channelName={channelName} 
                isPremium={channelId?.includes('laliga') || channelId?.includes('premier') || channelId?.includes('movistar')}
                estimatedAvailability="Em breve"
              />
            ) : (
              <div className="absolute inset-0 flex items-center justify-center bg-black/90">
                <div className="text-center space-y-4 p-6 max-w-md">
                  {renderErrorIcon()}
                  <div>
                    <p className="text-white text-lg font-medium">
                      Error al cargar el contenido
                    </p>
                    <p className="text-gray-400 text-sm mt-1">
                      {channelName}
                    </p>
                    <p className="text-gray-500 text-xs mt-2">
                      {errorDetails}
                    </p>
                  </div>
                  <div className="flex gap-3 justify-center">
                    <button
                      onClick={handleRetry}
                      className="px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                    >
                      Reintentar
                    </button>
                  </div>
                </div>
              </div>
            )
          )}
          
          {/* Player Controls */}
          {(playbackState === 'ready' || playbackState === 'playing') && showControls && (
            <PlayerControls
              isPlaying={isPlaying}
              isMuted={isMuted}
              volume={volume}
              currentTime={currentTime}
              duration={duration}
              isFullscreen={isFullscreen}
              isLoading={buffering}
              qualities={qualities}
              currentQuality={currentQuality}
              audioTracks={audioTracks}
              currentAudioTrack={currentAudioTrack}
              subtitles={subtitles}
              currentSubtitle={currentSubtitle}
              onPlayPause={handlePlayPause}
              onMute={handleMute}
              onVolumeChange={handleVolumeChange}
              onSeek={handleSeek}
              onFullscreen={handleFullscreen}
              onQualityChange={handleQualityChange}
              onAudioTrackChange={handleAudioTrackChange}
              onSubtitleChange={handleSubtitleChange}
              isLive={isLive}
            />
          )}
        </>
      )}
      
      {/* Mostrar overlay de trial quando apropriado */}
      {(() => {
        // Não bloquear enquanto está verificando acesso
        if (isCheckingAccess) {
          console.log('⏳ [ENHANCED-HLS-PLAYER] Still checking access, NOT blocking yet')
          return null
        }
        
        // Obter estado atualizado
        const currentAccessState = useAccessStore.getState()
        const currentTrialState = useTrialStore.getState()
        
        // Verificação de segurança: bloquear se:
        // 1. Trial expirou E não tem acesso premium
        // 2. OU não tem trial iniciado E não tem acesso E já verificou (segurança)
        const shouldBlockNoTrial = !currentTrialState.startTime && !currentAccessState.hasAccess && !isCheckingAccess
        const shouldBlockExpired = currentTrialState.isExpired && !currentAccessState.hasAccess
        
        const shouldShowOverlay = shouldBlockNoTrial || shouldBlockExpired
        
        console.log('🚨 [ENHANCED-HLS-PLAYER] ===== OVERLAY DECISION =====', {
          trialState: {
            isExpired: currentTrialState.isExpired,
            startTime: currentTrialState.startTime,
            timeRemaining: currentTrialState.timeRemaining
          },
          accessState: {
            hasAccess: currentAccessState.hasAccess,
            accessType: currentAccessState.accessType
          },
          decision: {
            shouldBlockNoTrial,
            shouldBlockExpired,
            shouldShowOverlay,
            reason: shouldBlockNoTrial ? 'No trial started' : shouldBlockExpired ? 'Trial expired' : 'Not blocked'
          },
          timestamp: new Date().toISOString()
        })
        
        return shouldShowOverlay ? <TrialOverlay /> : null
      })()}
    </div>
  )
}