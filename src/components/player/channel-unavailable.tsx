'use client'

import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { AlertCircle, Clock, Crown, ArrowLeft } from 'lucide-react'
import { useRouter } from 'next/navigation'

interface ChannelUnavailableProps {
  channelName: string
  isPremium?: boolean
  estimatedAvailability?: string
}

export function ChannelUnavailable({ 
  channelName, 
  isPremium = false,
  estimatedAvailability = 'Em breve'
}: ChannelUnavailableProps) {
  const router = useRouter()

  return (
    <div className="relative w-full h-full min-h-[600px] bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 flex items-center justify-center p-4">
      {/* Background pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
        }} />
      </div>

      <Card className="relative max-w-lg w-full bg-slate-800/90 backdrop-blur-xl border-slate-700 shadow-2xl">
        <CardContent className="p-8 text-center space-y-6">
          {/* Icon */}
          <div className="relative mx-auto w-20 h-20">
            <div className="absolute inset-0 bg-gradient-to-r from-orange-500/20 to-red-500/20 rounded-full blur-xl animate-pulse" />
            <div className="relative bg-gradient-to-r from-orange-500 to-red-500 rounded-full p-5">
              {isPremium ? <Crown className="h-10 w-10 text-white" /> : <AlertCircle className="h-10 w-10 text-white" />}
            </div>
          </div>

          {/* Title */}
          <div className="space-y-2">
            <h2 className="text-2xl font-bold text-white">{channelName}</h2>
            <p className="text-lg text-gray-300">
              {isPremium ? 'Canal Premium' : 'Temporariamente Indisponível'}
            </p>
          </div>

          {/* Status Badge */}
          <Badge variant="secondary" className="bg-slate-700/50 text-gray-300 border-slate-600">
            <Clock className="mr-2 h-3 w-3" />
            {estimatedAvailability}
          </Badge>

          {/* Description */}
          <p className="text-gray-400 max-w-sm mx-auto">
            {isPremium 
              ? 'Este canal está disponível exclusivamente para assinantes premium. Faça upgrade da sua conta para ter acesso completo.'
              : 'Estamos trabalhando para trazer este canal para você. Enquanto isso, explore nossos outros canais disponíveis.'}
          </p>

          {/* Actions */}
          <div className="space-y-3 pt-4">
            {isPremium && (
              <Button 
                size="lg"
                className="w-full bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white border-0"
                onClick={() => router.push('/subscription')}
              >
                <Crown className="mr-2 h-4 w-4" />
                Ver Planos Premium
              </Button>
            )}
            
            <Button 
              size="lg"
              variant="outline" 
              className="w-full bg-slate-700/50 border-slate-600 text-white hover:bg-slate-700"
              onClick={() => router.push('/browse')}
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Explorar Outros Canais
            </Button>
          </div>

          {/* Alternative suggestion */}
          {!isPremium && (
            <div className="pt-4 border-t border-slate-700">
              <p className="text-sm text-gray-400 mb-2">Enquanto isso, que tal assistir:</p>
              <div className="flex flex-wrap gap-2 justify-center">
                <Badge variant="secondary" className="cursor-pointer hover:bg-slate-600">Real Madrid TV</Badge>
                <Badge variant="secondary" className="cursor-pointer hover:bg-slate-600">ESPN</Badge>
                <Badge variant="secondary" className="cursor-pointer hover:bg-slate-600">Fox Sports</Badge>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}