'use client'

import { useRef, useEffect, useState } from 'react'
import ReactPlayer from 'react-player'
import Hls from 'hls.js'
import { useTrialStore } from '@/stores/trial.store'
import { usePlayerStore } from '@/stores/player.store'
import { useAccessStore } from '@/stores/access.store'
import { TrialOverlay } from './trial-overlay'
import { TrialTimer } from './trial-timer'
import { PlayerControls } from './player-controls'
import { cn } from '@/lib/utils'
import { Loader2 } from 'lucide-react'
import { logger } from '@/lib/logger'
import { LOG_TAGS } from '@/config/debug'
import { useUser } from '@stackframe/stack'
import { getProxyUrl, processStreamUrls } from '@/lib/stream-proxy'
import { useTrialTimer } from '@/hooks/use-trial-timer'

interface VideoPlayerProps {
  url: string
  fallbackUrls?: string[]
  poster?: string
  channelName?: string
  onError?: (error: unknown) => void
}

// Função auxiliar para verificar se deve resetar trial
function shouldResetTrial(lastStartTime: number | null): boolean {
  if (!lastStartTime) return false
  const hoursSinceStart = (Date.now() - lastStartTime) / (1000 * 60 * 60)
  return hoursSinceStart > 24 // Permite reset após 24 horas
}

export function VideoPlayer({ 
  url, 
  fallbackUrls = [], 
  poster,
  channelName = 'StreamPlus',
  onError 
}: VideoPlayerProps) {
  const playerRef = useRef<ReactPlayer>(null)
  const hlsRef = useRef<Hls | null>(null)
  const [urlIndex, setUrlIndex] = useState(0)
  const [hasError, setHasError] = useState(false)
  const [isClient, setIsClient] = useState(false)
  const [hasInitialized, setHasInitialized] = useState(false)
  
  const user = useUser()
  const { startTrial, isExpired, checkTrialStatus } = useTrialStore()
  const { hasAccess, checkAccess } = useAccessStore()
  const { timeRemaining } = useTrialTimer()
  const { 
    isPlaying, 
    setPlaying, 
    setDuration,
    setCurrentTime,
    setBuffering,
    buffering,
    volume,
    muted,
    quality
  } = usePlayerStore()
  
  // Bloquear conteúdo se trial expirou e não tem acesso
  const [isBlocked, setIsBlocked] = useState(false)
  const [isCheckingAccess, setIsCheckingAccess] = useState(true)
  
  // Log inicial do componente
  console.log('🎬 [VIDEO-PLAYER] Component rendered', {
    user: user?.primaryEmail,
    isExpired,
    hasAccess,
    timeRemaining,
    isBlocked,
    isCheckingAccess,
    isClient,
    hasInitialized,
    timestamp: new Date().toISOString()
  })
  
  // Cliente-side only
  useEffect(() => {
    console.log('🚀 [VIDEO-PLAYER] CLIENT EFFECT - Setting isClient to true')
    setIsClient(true)
    console.log('[STREAM-PLAYER] ========== VIDEO PLAYER MOUNTED ==========')
    console.log('[STREAM-PLAYER] Channel:', channelName)
    console.log('[STREAM-PLAYER] Primary URL:', url)
    console.log('[STREAM-PLAYER] Fallback URLs:', fallbackUrls)
    console.log('[STREAM-PLAYER] Total URLs available:', 1 + fallbackUrls.length)
    console.log('[STREAM-PLAYER] ==========================================')
    
    logger.info(LOG_TAGS.PLAYER, 'VideoPlayer mounted', {
      url,
      fallbackUrls,
      channelName,
      hasFallbacks: fallbackUrls.length > 0
    })
  }, [])
  
  // Verificar acesso e trial - sem depender de isClient
  useEffect(() => {
    console.log('🔍 [VIDEO-PLAYER] useEffect - checkAccessAndTrial triggered', {
      isClient,
      hasInitialized,
      user: user?.primaryEmail,
      hasAccess,
      isExpired,
      isWindow: typeof window !== 'undefined'
    })
    
    console.log('🔄 [VIDEO-PLAYER] checkAccessAndTrial effect deps:', {
      hasInitialized,
      hasUser: !!user,
      checkAccess: !!checkAccess,
      checkTrialStatus: !!checkTrialStatus,
      startTrial: !!startTrial
    })
    
    // Verificar se está no cliente e não foi inicializado ainda
    if (typeof window !== 'undefined' && !hasInitialized) {
      const checkAccessAndTrial = async () => {
        console.log('🚀 [TRIAL-PLAYER] ===== CHECK ACCESS AND TRIAL START =====', {
          hasUser: !!user,
          userId: user?.id,
          userEmail: user?.primaryEmail,
          hasAccess,
          isExpired,
          startTime: useTrialStore.getState().startTime,
          timeRemaining: useTrialStore.getState().timeRemaining,
          timestamp: new Date().toISOString()
        })
        
        setIsCheckingAccess(true)
        
        try {
          // Primeiro verificar se tem acesso (cupom ou assinatura)
          if (user) {
            console.log('[TRIAL-PLAYER] Checking access for user:', user.id)
            // Forçar refresh na primeira verificação
            await checkAccess(user.id, true)
            
            // Verificar novamente o estado após checkAccess
            const newAccessState = useAccessStore.getState()
            console.log('[TRIAL-PLAYER] Access state after check:', {
              hasAccess: newAccessState.hasAccess,
              accessType: newAccessState.accessType,
              expiresAt: newAccessState.expiresAt
            })
            
            // Se tem acesso premium, limpar o trial
            if (newAccessState.hasAccess && newAccessState.accessType === 'assinatura') {
              console.log('✅ [TRIAL-PLAYER] User has PREMIUM access!', {
                accessType: newAccessState.accessType,
                expiresAt: newAccessState.expiresAt
              })
              useTrialStore.getState().reset()
              setIsCheckingAccess(false)
              return
            }
          }
          
          // Se não tem acesso, iniciar/verificar trial
          const currentAccessState = useAccessStore.getState()
          if (!currentAccessState.hasAccess) {
            console.log('⏰ [TRIAL-PLAYER] No premium access, checking trial status...', {
              currentAccessState
            })
            
            // Primeiro verificar o status atual
            await checkTrialStatus(user?.id)
            
            // SEMPRE iniciar trial se não tem acesso premium
            const currentState = useTrialStore.getState()
            console.log('🔍 [TRIAL-PLAYER] Current trial state:', currentState)
            
            if (!currentState.startTime || currentState.startTime === 0) {
              console.log('🚀 [TRIAL-PLAYER] No premium access, starting trial NOW', {
                reason: 'No startTime - new user needs trial',
                userId: user?.id
              })
              await startTrial(user?.id)
              
              // Log estado após iniciar trial
              const newTrialState = useTrialStore.getState()
              console.log('📊 [TRIAL-PLAYER] Trial state AFTER startTrial:', {
                startTime: newTrialState.startTime,
                timeRemaining: newTrialState.timeRemaining,
                isExpired: newTrialState.isExpired
              })
            } else if (currentState.isExpired && shouldResetTrial(currentState.startTime)) {
              console.log('🔄 [TRIAL-PLAYER] Resetting expired trial', {
                lastStartTime: currentState.startTime,
                userId: user?.id
              })
              await startTrial(user?.id)
            } else {
              console.log('⏰ [TRIAL-PLAYER] Trial already active', {
                startTime: currentState.startTime,
                timeRemaining: currentState.timeRemaining,
                isExpired: currentState.isExpired
              })
            }
          } else {
            console.log('[TRIAL-PLAYER] User has access, skipping trial')
          }
        } finally {
          console.log('✅ [TRIAL-PLAYER] Access check COMPLETED')
          setIsCheckingAccess(false)
        }
      }
      
      checkAccessAndTrial()
      setHasInitialized(true)
    }
  }, [hasInitialized, user, checkAccess, checkTrialStatus, startTrial])
  
  // Atualizar estado de bloqueio
  useEffect(() => {
    console.log('🚦 [VIDEO-PLAYER] useEffect - block status update triggered', {
      isExpired,
      hasAccess,
      isCheckingAccess
    })
    
    // Não bloquear enquanto está verificando acesso
    if (isCheckingAccess) {
      console.log('⏳ [TRIAL-PLAYER] Still checking access, NOT blocking yet')
      return
    }
    
    // Obter estado atualizado
    const currentAccessState = useAccessStore.getState()
    const currentTrialState = useTrialStore.getState()
    
    // Verificação de segurança: bloquear se:
    // 1. Trial expirou E não tem acesso premium
    // 2. OU não tem trial iniciado E não tem acesso E já verificou (segurança)
    const shouldBlockNoTrial = !currentTrialState.startTime && !currentAccessState.hasAccess && !isCheckingAccess
    const shouldBlockExpired = currentTrialState.isExpired && !currentAccessState.hasAccess
    
    const shouldBlock = shouldBlockNoTrial || shouldBlockExpired
    
    console.log('🚨 [TRIAL-PLAYER] ===== BLOCK DECISION =====', {
      trialState: {
        isExpired: currentTrialState.isExpired,
        startTime: currentTrialState.startTime,
        timeRemaining: currentTrialState.timeRemaining
      },
      accessState: {
        hasAccess: currentAccessState.hasAccess,
        accessType: currentAccessState.accessType
      },
      decision: {
        shouldBlockNoTrial,
        shouldBlockExpired,
        shouldBlock,
        previousBlocked: isBlocked,
        changing: shouldBlock !== isBlocked,
        reason: shouldBlockNoTrial ? 'No trial started' : shouldBlockExpired ? 'Trial expired' : 'Not blocked'
      },
      timestamp: new Date().toISOString()
    })
    
    if (shouldBlock !== isBlocked) {
      console.log(shouldBlock ? '🚫 BLOCKING USER!' : '✅ UNBLOCKING USER!')
    }
    
    setIsBlocked(shouldBlock)
  }, [isExpired, hasAccess, isCheckingAccess])
  
  // Forçar verificação quando trial expirar
  useEffect(() => {
    if (isExpired && user && !isCheckingAccess) {
      console.log('[TRIAL-PLAYER] Trial expired! Forcing access check...')
      // Limpar cache do access store
      useAccessStore.getState().clearAccess()
      // Forçar nova verificação
      checkAccess(user.id, true).then(() => {
        console.log('[TRIAL-PLAYER] Access check completed after trial expiration')
      }).catch((error) => {
        console.error('[TRIAL-PLAYER] Error checking access after trial expiration:', error)
        // Em caso de erro, assumir que não tem acesso para garantir segurança
        console.log('[TRIAL-PLAYER] Setting blocked state due to access check error')
        setIsBlocked(true)
      })
    }
  }, [isExpired, user, checkAccess, isCheckingAccess])
  
  const handleReady = () => {
    console.log('[STREAM-PLAYER] ===== PLAYER READY EVENT =====')
    logger.info(LOG_TAGS.PLAYER, 'Player ready')
    
    const player = playerRef.current
    if (!player) {
      console.error('[STREAM-PLAYER] ❌ Player ref not found!')
      logger.error(LOG_TAGS.PLAYER, 'Player ref not found')
      return
    }
    
    const internalPlayer = player.getInternalPlayer() as HTMLVideoElement
    const sourceUrl = getCurrentUrl()
    
    console.log('[STREAM-PLAYER] Internal player type:', internalPlayer?.constructor?.name)
    console.log('[STREAM-PLAYER] HLS.js supported:', Hls.isSupported())
    console.log('[STREAM-PLAYER] Source URL to load:', sourceUrl)
    console.log('[STREAM-PLAYER] Quality setting:', quality)
    
    logger.debug(LOG_TAGS.PLAYER, 'Setting up HLS', {
      hlsSupported: Hls.isSupported(),
      sourceUrl,
      quality
    })
    
    if (internalPlayer && Hls.isSupported()) {
      console.log('[STREAM-PLAYER] 🎬 Initializing HLS.js...')
      const hls = new Hls({
        autoStartLoad: true,
        startLevel: quality === 'auto' ? -1 : quality as number,
        capLevelToPlayerSize: true,
        capLevelOnFPSDrop: true,
        maxLoadingDelay: 4,
        minAutoBitrate: 0,
        lowLatencyMode: true,
        enableWorker: true,
        debug: true // Enable HLS.js debug logs
      })
      
      hlsRef.current = hls
      
      // Add more HLS event listeners for debugging
      hls.on(Hls.Events.MEDIA_ATTACHING, () => {
        console.log('[STREAM-PLAYER] HLS: Media attaching...')
      })
      
      hls.on(Hls.Events.MEDIA_ATTACHED, () => {
        console.log('[STREAM-PLAYER] HLS: Media attached successfully')
      })
      
      hls.on(Hls.Events.MANIFEST_LOADING, () => {
        console.log('[STREAM-PLAYER] HLS: Loading manifest...')
      })
      
      hls.on(Hls.Events.MANIFEST_LOADED, (event, data) => {
        console.log('[STREAM-PLAYER] HLS: Manifest loaded successfully')
        console.log('[STREAM-PLAYER] HLS: Total duration:', data.totalduration)
        console.log('[STREAM-PLAYER] HLS: Network details:', data.networkDetails)
      })
      
      hls.on(Hls.Events.MANIFEST_PARSED, (event, data) => {
        const qualities = data.levels.map(l => `${l.height}p @ ${l.bitrate}bps`)
        console.log('[STREAM-PLAYER] HLS: Manifest parsed successfully')
        console.log('[STREAM-PLAYER] HLS: Available qualities:', qualities)
        console.log('[STREAM-PLAYER] HLS: Start level:', data.startLevel)
        console.log('[STREAM-PLAYER] HLS: Audio tracks:', data.audioTracks?.length || 0)
        
        logger.info(LOG_TAGS.PLAYER, 'HLS manifest parsed', {
          qualities,
          levelCount: data.levels.length,
          startLevel: data.startLevel
        })
      })
      
      hls.on(Hls.Events.LEVEL_SWITCHING, (event, data) => {
        console.log('[STREAM-PLAYER] HLS: Switching to level', data.level)
      })
      
      hls.on(Hls.Events.LEVEL_SWITCHED, (event, data) => {
        console.log('[STREAM-PLAYER] HLS: Switched to level', data.level)
      })
      
      hls.on(Hls.Events.FRAG_LOADING, (event, data) => {
        console.log('[STREAM-PLAYER] HLS: Loading fragment', data.frag.sn, 'of level', data.frag.level)
      })
      
      hls.on(Hls.Events.FRAG_LOADED, (event, data) => {
        console.log('[STREAM-PLAYER] HLS: Fragment loaded', data.frag.sn, 'size:', data.stats.total, 'bytes')
      })
      
      hls.on(Hls.Events.ERROR, (event, data) => {
        console.error('[STREAM-PLAYER] ❌ HLS ERROR:', {
          type: data.type,
          details: data.details,
          fatal: data.fatal,
          url: data.url,
          response: data.response,
          networkDetails: data.networkDetails,
          reason: data.reason
        })
        
        // Log specific error types
        if (data.type === Hls.ErrorTypes.NETWORK_ERROR) {
          console.error('[STREAM-PLAYER] ❌ Network error - CORS or connectivity issue')
        } else if (data.type === Hls.ErrorTypes.MEDIA_ERROR) {
          console.error('[STREAM-PLAYER] ❌ Media error - codec or format issue')
        } else if (data.type === Hls.ErrorTypes.KEY_SYSTEM_ERROR) {
          console.error('[STREAM-PLAYER] ❌ Key system error - DRM issue')
        } else if (data.type === Hls.ErrorTypes.MUX_ERROR) {
          console.error('[STREAM-PLAYER] ❌ Mux error - remuxing issue')
        }
        
        logger.error(LOG_TAGS.PLAYER, 'HLS error', {
          type: data.type,
          details: data.details,
          fatal: data.fatal,
          url: data.url
        })
        
        if (data.fatal) {
          console.error('[STREAM-PLAYER] ❌ FATAL ERROR - trying to recover or switch URL')
          
          switch (data.type) {
            case Hls.ErrorTypes.NETWORK_ERROR:
              console.log('[STREAM-PLAYER] 🔄 Attempting to recover from network error...')
              hls.startLoad()
              break
            case Hls.ErrorTypes.MEDIA_ERROR:
              console.log('[STREAM-PLAYER] 🔄 Attempting to recover from media error...')
              hls.recoverMediaError()
              break
            default:
              console.error('[STREAM-PLAYER] ❌ Cannot recover, switching to fallback URL')
              handleError(new Error(`HLS Fatal Error: ${data.details}`))
              break
          }
        }
      })
      
      console.log('[STREAM-PLAYER] 📡 Loading source:', sourceUrl)
      logger.info(LOG_TAGS.PLAYER, 'Loading HLS source', { sourceUrl })
      hls.loadSource(sourceUrl)
      hls.attachMedia(internalPlayer)
      
      console.log('[STREAM-PLAYER] ✅ HLS.js setup complete')
    } else {
      console.warn('[STREAM-PLAYER] ⚠️ HLS not supported or player not ready')
      console.log('[STREAM-PLAYER] Browser info:', {
        userAgent: navigator.userAgent,
        hlsSupported: Hls.isSupported(),
        hasInternalPlayer: !!internalPlayer
      })
      logger.warn(LOG_TAGS.PLAYER, 'HLS not supported or player not ready')
    }
  }
  
  const getCurrentUrl = () => {
    const currentUrl = urlIndex === 0 ? url : fallbackUrls[urlIndex - 1]
    const proxiedUrl = getProxyUrl(currentUrl)
    
    console.log('[STREAM-PLAYER] ===== GET CURRENT URL =====')
    console.log('[STREAM-PLAYER] URL Index:', urlIndex)
    console.log('[STREAM-PLAYER] Original URL:', url)
    console.log('[STREAM-PLAYER] Current URL:', currentUrl)
    console.log('[STREAM-PLAYER] Proxied URL:', proxiedUrl)
    console.log('[STREAM-PLAYER] Is Fallback:', urlIndex > 0)
    console.log('[STREAM-PLAYER] Is Proxied:', proxiedUrl !== currentUrl)
    console.log('[STREAM-PLAYER] Total Fallbacks:', fallbackUrls.length)
    console.log('[STREAM-PLAYER] ==========================')
    
    logger.debug(LOG_TAGS.PLAYER, 'Getting current URL', {
      urlIndex,
      currentUrl,
      proxiedUrl,
      isFallback: urlIndex > 0,
      isProxied: proxiedUrl !== currentUrl
    })
    return proxiedUrl
  }
  
  const handleError = (error: unknown) => {
    console.error('[STREAM-PLAYER] ❌❌❌ PLAYER ERROR ❌❌❌')
    console.error('[STREAM-PLAYER] Error:', error instanceof Error ? error.message : String(error))
    if (error instanceof Error && error.stack) {
      console.error('[STREAM-PLAYER] Stack:', error.stack)
    }
    console.error('[STREAM-PLAYER] Current URL:', getCurrentUrl())
    console.error('[STREAM-PLAYER] URL Index:', urlIndex)
    console.error('[STREAM-PLAYER] Has Fallbacks:', fallbackUrls.length > 0)
    console.error('[STREAM-PLAYER] Total URLs:', 1 + fallbackUrls.length)
    console.error('[STREAM-PLAYER] ❌❌❌❌❌❌❌❌❌❌❌❌❌❌❌')
    
    logger.error(LOG_TAGS.PLAYER, 'Player error', {
      error: error instanceof Error ? error.message : String(error),
      currentUrl: getCurrentUrl(),
      urlIndex,
      hasFallbacks: fallbackUrls.length > 0
    })
    
    // Intentar siguiente URL si existe
    if (urlIndex < fallbackUrls.length) {
      console.log('[STREAM-PLAYER] 🔄 TRYING FALLBACK URL')
      console.log('[STREAM-PLAYER] Next Index:', urlIndex + 1)
      console.log('[STREAM-PLAYER] Next URL:', fallbackUrls[urlIndex])
      console.log('[STREAM-PLAYER] Remaining URLs:', fallbackUrls.length - urlIndex)
      
      logger.info(LOG_TAGS.PLAYER, 'Trying fallback URL', {
        nextIndex: urlIndex + 1,
        nextUrl: fallbackUrls[urlIndex]
      })
      setUrlIndex(urlIndex + 1)
      setHasError(false)
    } else {
      console.error('[STREAM-PLAYER] ❌ ALL URLS FAILED - NO MORE FALLBACKS')
      console.error('[STREAM-PLAYER] Total attempts:', urlIndex + 1)
      logger.error(LOG_TAGS.PLAYER, 'All URLs failed - no more fallbacks')
      setHasError(true)
      onError?.(error)
    }
  }
  
  const handleProgress = (state: { playedSeconds: number, loadedSeconds: number }) => {
    setCurrentTime(state.playedSeconds)
  }
  
  if (!isClient) {
    return (
      <div className="relative aspect-video bg-black rounded-lg overflow-hidden">
        <div className="absolute inset-0 flex items-center justify-center">
          <Loader2 className="h-8 w-8 animate-spin text-white" />
        </div>
      </div>
    )
  }
  
  console.log('🎭 [VIDEO-PLAYER] RENDER:', {
    isBlocked,
    isCheckingAccess,
    hasAccess,
    isExpired,
    showingVideo: !isBlocked,
    showingOverlay: isBlocked
  })
  
  return (
    <div className="relative aspect-video bg-black rounded-lg overflow-hidden group">
      {!isBlocked && (
        <>
          <ReactPlayer
            ref={playerRef}
            url={getCurrentUrl()}
            playing={isPlaying}
            controls={false}
            volume={volume}
            muted={muted}
            width="100%"
            height="100%"
            onReady={handleReady}
            onPlay={() => setPlaying(true)}
            onPause={() => setPlaying(false)}
            onDuration={setDuration}
            onProgress={handleProgress}
            onBuffer={() => setBuffering(true)}
            onBufferEnd={() => setBuffering(false)}
            onError={handleError}
            config={{
              file: {
                forceHLS: true,
                attributes: {
                  poster,
                  crossOrigin: 'anonymous'
                },
              },
            }}
          />
          
          {/* Loading indicator */}
          {buffering && (
            <div className="absolute inset-0 flex items-center justify-center bg-black/50">
              <Loader2 className="h-8 w-8 animate-spin text-white" />
            </div>
          )}
          
          {/* Timer do trial - mostrar apenas se não tem acesso premium */}
          {!hasAccess && (
            <div className="absolute top-4 left-4 z-20">
              <TrialTimer key={`trial-timer-${hasAccess}-${isExpired}`} />
            </div>
          )}
          
          {/* Player controls - Temporarily disabled due to error */}
          {/* <PlayerControls 
            playerRef={playerRef}
            channelName={channelName}
          /> */}
          
          {/* Error state */}
          {hasError && (
            <div className="absolute inset-0 flex items-center justify-center bg-black">
              <div className="text-center space-y-4">
                <p className="text-white text-lg">
                  Error al cargar el stream
                </p>
                <button
                  onClick={() => window.location.reload()}
                  className="px-4 py-2 bg-white text-black rounded hover:bg-gray-200"
                >
                  Recargar
                </button>
              </div>
            </div>
          )}
        </>
      )}
      
      {/* Bloqueio quando trial expira e não tem acesso */}
      {isBlocked && <TrialOverlay />}
    </div>
  )
}