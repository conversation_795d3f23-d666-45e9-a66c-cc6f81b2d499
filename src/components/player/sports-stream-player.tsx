'use client'

import { useRef, useEffect, useState } from 'react'
import Hls from 'hls.js'
import { useTrialStore } from '@/stores/trial.store'
import { TrialOverlay } from './trial-overlay'
import { PlayerControls } from './player-controls'
import { Loader2, Play, AlertCircle } from 'lucide-react'
import { DEMO_STREAMS, getStreamForChannel } from '@/config/demo-streams'

interface SportsStreamPlayerProps {
  channelName?: string
  streamUrl?: string
  isLive?: boolean
}

export function SportsStreamPlayer({ 
  channelName = 'Deportes en Vivo',
  streamUrl,
  isLive = true 
}: SportsStreamPlayerProps) {
  const videoRef = useRef<HTMLVideoElement>(null)
  const hlsRef = useRef<Hls | null>(null)
  const [isReady, setIsReady] = useState(false)
  const [isPlaying, setIsPlaying] = useState(false)
  const [showPlayButton, setShowPlayButton] = useState(true)
  const [hasError, setHasError] = useState(false)
  const [currentStreamIndex, setCurrentStreamIndex] = useState(0)
  const [useMP4Fallback, setUseMP4Fallback] = useState(false)
  
  const { isExpired, timeRemaining, startTrial } = useTrialStore()
  
  useEffect(() => {
    startTrial()
  }, [startTrial])
  
  // Obter stream apropriado
  const getStreamUrl = () => {
    if (streamUrl) return streamUrl
    if (useMP4Fallback) return DEMO_STREAMS.fallback[0].url
    return getStreamForChannel(channelName || '')
  }
  
  const setupPlayer = () => {
    if (!videoRef.current) return
    
    const video = videoRef.current
    const selectedStream = getStreamUrl()
    
    console.log('🎬 Configurando stream:', channelName, 'URL:', selectedStream)
    
    // Se for MP4, usar reprodução direta
    if (useMP4Fallback || selectedStream.endsWith('.mp4')) {
      video.src = selectedStream
      video.addEventListener('canplay', () => {
        setIsReady(true)
        setHasError(false)
      })
      video.addEventListener('error', () => {
        console.error('Erro ao carregar vídeo MP4')
        setHasError(true)
      })
      return
    }
    
    // Usar HLS para streams .m3u8
    if (Hls.isSupported()) {
      const hls = new Hls({
        maxLoadingDelay: 4,
        maxBufferLength: 30,
        maxMaxBufferLength: 600,
        enableWorker: true,
        lowLatencyMode: isLive
      })
      
      hlsRef.current = hls
      
      hls.on(Hls.Events.MANIFEST_PARSED, () => {
        console.log('✅ Stream HLS carregado')
        setIsReady(true)
        setHasError(false)
      })
      
      hls.on(Hls.Events.ERROR, (event, data) => {
        console.warn('⚠️ Erro HLS:', data.type, data.details)
        if (data.fatal) {
          switch (data.type) {
            case Hls.ErrorTypes.NETWORK_ERROR:
              console.log('Erro de rede, tentando fallback MP4...')
              hls.destroy()
              setUseMP4Fallback(true)
              break
            case Hls.ErrorTypes.MEDIA_ERROR:
              console.log('Erro de mídia, tentando recuperar...')
              hls.recoverMediaError()
              break
            default:
              console.log('Erro fatal, usando fallback MP4...')
              hls.destroy()
              setUseMP4Fallback(true)
              break
          }
        }
      })
      
      hls.loadSource(selectedStream)
      hls.attachMedia(video)
      
      return () => {
        hls.destroy()
      }
    } else if (video.canPlayType('application/vnd.apple.mpegurl')) {
      // Safari nativo
      video.src = selectedStream
      setIsReady(true)
    } else {
      // Navegador não suporta HLS, usar MP4
      setUseMP4Fallback(true)
    }
  }
  
  useEffect(() => {
    if (videoRef.current) {
      const cleanup = setupPlayer()
      return cleanup
    }
  }, [channelName, streamUrl, isLive, useMP4Fallback])
  
  const handlePlay = async () => {
    if (videoRef.current && !isExpired) {
      try {
        setShowPlayButton(false)
        videoRef.current.muted = true // Garantir autoplay
        await videoRef.current.play()
        setIsPlaying(true)
      } catch (err) {
        console.error('Erro ao reproduzir:', err)
        setShowPlayButton(true)
      }
    }
  }
  
  // Pausar quando expirar
  useEffect(() => {
    if (isExpired && videoRef.current) {
      videoRef.current.pause()
      setIsPlaying(false)
    }
  }, [isExpired])
  
  // Retry handler
  const handleRetry = () => {
    setHasError(false)
    setUseMP4Fallback(false)
    setCurrentStreamIndex(0)
    if (hlsRef.current) {
      hlsRef.current.destroy()
    }
    setupPlayer()
  }
  
  return (
    <div className="relative aspect-video bg-black rounded-lg overflow-hidden group">
      {!isExpired && (
        <>
          <video
            ref={videoRef}
            className="w-full h-full"
            controls={false}
            playsInline
            muted
            onPlay={() => setIsPlaying(true)}
            onPause={() => setIsPlaying(false)}
          />
          
          {/* Indicador de EN VIVO */}
          {isLive && isPlaying && (
            <div className="absolute top-4 left-4 flex items-center gap-2 bg-red-600 px-3 py-1 rounded-full">
              <div className="h-2 w-2 bg-white rounded-full animate-pulse" />
              <span className="text-white text-sm font-medium">EN VIVO</span>
            </div>
          )}
          
          {/* Botão de play grande */}
          {showPlayButton && isReady && !hasError && (
            <div 
              className="absolute inset-0 flex items-center justify-center bg-black/30 cursor-pointer"
              onClick={handlePlay}
            >
              <button className="bg-white/10 backdrop-blur-sm hover:bg-white/20 rounded-full p-6 transition-all">
                <Play className="h-12 w-12 text-white fill-white" />
              </button>
            </div>
          )}
          
          {/* Loading */}
          {!isReady && !hasError && (
            <div className="absolute inset-0 flex items-center justify-center bg-black/50">
              <div className="text-center space-y-2">
                <Loader2 className="h-8 w-8 animate-spin text-white mx-auto" />
                <p className="text-white text-sm">Conectando con {channelName}...</p>
              </div>
            </div>
          )}
          
          {/* Error state */}
          {hasError && (
            <div className="absolute inset-0 flex items-center justify-center bg-black/90">
              <div className="text-center space-y-4 p-6">
                <AlertCircle className="h-12 w-12 text-red-500 mx-auto" />
                <div>
                  <p className="text-white text-lg font-medium">
                    Stream temporalmente no disponible
                  </p>
                  <p className="text-gray-400 text-sm mt-1">
                    {channelName}
                  </p>
                </div>
                <button
                  onClick={handleRetry}
                  className="px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                >
                  Reintentar
                </button>
              </div>
            </div>
          )}
          
          {/* Controles */}
          {isPlaying && (
            <PlayerControls 
              playerRef={{ current: { 
                seekTo: (time: number) => {
                  if (videoRef.current) videoRef.current.currentTime = time
                }
              } as any }}
              channelName={channelName}
              timeRemaining={timeRemaining}
            />
          )}
        </>
      )}
      
      {isExpired && <TrialOverlay />}
    </div>
  )
}