'use client'

import { useRef, useEffect, useState } from 'react'
import Hls from 'hls.js'
import { useTrialStore } from '@/stores/trial.store'
import { TrialOverlay } from './trial-overlay'
import { Loader2, Play, AlertCircle } from 'lucide-react'
import { logger } from '@/lib/logger'
import { LOG_TAGS } from '@/config/debug'

interface SimpleHlsPlayerProps {
  channelName?: string
  streamUrl?: string
  isLive?: boolean
  headers?: Record<string, string>
  userAgent?: string
}

export function SimpleHlsPlayer({ 
  channelName = 'Deportes en Vivo',
  streamUrl,
  isLive = true,
  headers,
  userAgent
}: SimpleHlsPlayerProps) {
  const videoRef = useRef<HTMLVideoElement>(null)
  const hlsRef = useRef<Hls | null>(null)
  const [isReady, setIsReady] = useState(false)
  const [isPlaying, setIsPlaying] = useState(false)
  const [showPlayButton, setShowPlayButton] = useState(true)
  const [hasError, setHasError] = useState(false)
  const [buffering, setBuffering] = useState(false)
  
  const { isExpired, timeRemaining, startTrial } = useTrialStore()
  
  useEffect(() => {
    startTrial()
    logger.info(LOG_TAGS.PLAYER, 'SimpleHlsPlayer mounted', {
      channelName,
      streamUrl,
      isLive,
      hasHeaders: !!headers,
      hasUserAgent: !!userAgent
    })
  }, [startTrial, channelName, streamUrl, isLive, headers, userAgent])
  
  // Configurar HLS quando o componente montar
  useEffect(() => {
    if (!videoRef.current || !streamUrl) return
    
    const video = videoRef.current
    
    // URL a ser usada - detectar e contornar CORS se necessário
    const finalUrl = streamUrl.startsWith('http') && !streamUrl.includes(window.location.host)
      ? `/api/hls-proxy?url=${encodeURIComponent(streamUrl)}`
      : streamUrl
    
    logger.info(LOG_TAGS.PLAYER, 'Setting up video', {
      streamUrl,
      finalUrl,
      usingProxy: finalUrl !== streamUrl
    })
    
    if (Hls.isSupported() && streamUrl.includes('.m3u8')) {
      logger.info(LOG_TAGS.PLAYER, 'Using HLS.js for stream')
      
      const hls = new Hls({
        autoStartLoad: true,
        startLevel: -1,
        capLevelToPlayerSize: true,
        capLevelOnFPSDrop: true,
        maxLoadingDelay: 4,
        minAutoBitrate: 0,
        lowLatencyMode: true,
        xhrSetup: (xhr: XMLHttpRequest) => {
          if (headers) {
            Object.entries(headers).forEach(([key, value]) => {
              xhr.setRequestHeader(key, value)
            })
          }
        }
      })
      
      hlsRef.current = hls
      
      hls.on(Hls.Events.MANIFEST_PARSED, () => {
        logger.info(LOG_TAGS.PLAYER, 'HLS manifest parsed')
        setIsReady(true)
      })
      
      hls.on(Hls.Events.ERROR, (event, data) => {
        logger.error(LOG_TAGS.PLAYER, 'HLS error', {
          type: data.type,
          details: data.details,
          fatal: data.fatal
        })
        
        if (data.fatal) {
          setHasError(true)
        }
      })
      
      hls.loadSource(finalUrl)
      hls.attachMedia(video)
    } else if (video.canPlayType('application/vnd.apple.mpegurl')) {
      // Safari native HLS
      logger.info(LOG_TAGS.PLAYER, 'Using native HLS (Safari)')
      video.src = finalUrl
      setIsReady(true)
    } else {
      // Direct playback
      logger.info(LOG_TAGS.PLAYER, 'Direct video playback')
      video.src = finalUrl
      setIsReady(true)
    }
    
    // Cleanup
    return () => {
      if (hlsRef.current) {
        hlsRef.current.destroy()
        hlsRef.current = null
      }
    }
  }, [streamUrl, headers])
  
  const handlePlay = async () => {
    if (!isExpired && videoRef.current) {
      try {
        setShowPlayButton(false)
        await videoRef.current.play()
        setIsPlaying(true)
        logger.info(LOG_TAGS.PLAYER, 'Playback started')
      } catch (err) {
        logger.error(LOG_TAGS.PLAYER, 'Failed to start playback', { error: err })
        setShowPlayButton(true)
      }
    }
  }
  
  // Pausar quando expirar
  useEffect(() => {
    if (isExpired && videoRef.current) {
      videoRef.current.pause()
      setIsPlaying(false)
    }
  }, [isExpired])
  
  return (
    <div className="relative aspect-video bg-black rounded-lg overflow-hidden group">
      {!isExpired && (
        <>
          <video
            ref={videoRef}
            className="w-full h-full"
            playsInline
            muted
            onLoadedMetadata={() => {
              logger.info(LOG_TAGS.PLAYER, 'Video metadata loaded')
              setIsReady(true)
            }}
            onPlay={() => setIsPlaying(true)}
            onPause={() => setIsPlaying(false)}
            onWaiting={() => setBuffering(true)}
            onPlaying={() => setBuffering(false)}
            onError={(e) => {
              logger.error(LOG_TAGS.PLAYER, 'Video error', { error: e })
              setHasError(true)
            }}
          />
          
          {/* Overlay com informações do canal */}
          {isReady && isPlaying && (
            <div className="absolute top-4 left-4 right-4 flex items-center justify-between pointer-events-none">
              <div className="bg-black/70 px-3 py-1.5 rounded-full">
                <p className="text-white text-sm font-medium">{channelName}</p>
              </div>
              {isLive && (
                <div className="flex items-center gap-2 bg-red-600 px-3 py-1 rounded-full">
                  <div className="h-2 w-2 bg-white rounded-full animate-pulse" />
                  <span className="text-white text-sm font-medium">EN VIVO</span>
                </div>
              )}
            </div>
          )}
          
          {/* Botão de play grande */}
          {showPlayButton && isReady && !hasError && (
            <div 
              className="absolute inset-0 flex items-center justify-center bg-black/30 cursor-pointer"
              onClick={handlePlay}
            >
              <button className="bg-white/10 backdrop-blur-sm hover:bg-white/20 rounded-full p-6 transition-all">
                <Play className="h-12 w-12 text-white fill-white" />
              </button>
            </div>
          )}
          
          {/* Loading/Buffering */}
          {(!isReady || buffering) && !hasError && (
            <div className="absolute inset-0 flex items-center justify-center bg-black/50">
              <div className="text-center space-y-2">
                <Loader2 className="h-8 w-8 animate-spin text-white mx-auto" />
                <p className="text-white text-sm">
                  {!isReady ? `Cargando ${channelName}...` : 'Buffering...'}
                </p>
              </div>
            </div>
          )}
          
          {/* Error state */}
          {hasError && (
            <div className="absolute inset-0 flex items-center justify-center bg-black/90">
              <div className="text-center space-y-4 p-6">
                <AlertCircle className="h-12 w-12 text-red-500 mx-auto" />
                <div>
                  <p className="text-white text-lg font-medium">
                    Error al cargar el contenido
                  </p>
                  <p className="text-gray-400 text-sm mt-1">
                    {channelName}
                  </p>
                </div>
                <button
                  onClick={() => window.location.reload()}
                  className="px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                >
                  Reintentar
                </button>
              </div>
            </div>
          )}
        </>
      )}
      
      {isExpired && <TrialOverlay />}
    </div>
  )
}