'use client'

import { useEffect, useState } from 'react'
import { PremiumBadge } from './premium-badge'
import { useAuth } from '@/hooks/use-auth'
import { useAccessStore } from '@/stores/access.store'
import { Clock, AlertCircle } from 'lucide-react'
import { Badge } from '@/components/ui/badge'

export function SubscriptionStatus() {
  const { user } = useAuth()
  const { hasAccess, accessType, expiresAt, isLoading } = useAccessStore()
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
    console.log('[SUBSCRIPTION-STATUS] Component mounted')
    console.log('[SUBSCRIPTION-STATUS] User:', user?.id)
    console.log('[SUBSCRIPTION-STATUS] Loading:', isLoading)
    console.log('[SUBSCRIPTION-STATUS] Has Access:', hasAccess)
    console.log('[SUBSCRIPTION-STATUS] Access Type:', accessType)
    console.log('[SUBSCRIPTION-STATUS] Expires At:', expiresAt)
  }, [user, isLoading, hasAccess, accessType, expiresAt])

  if (!mounted || isLoading || !user) {
    return null
  }

  // Usuario Premium (assinatura, cupom ou app)
  if (hasAccess && (accessType === 'assinatura' || accessType === 'cupom' || accessType === 'app')) {
    return <PremiumBadge size="sm" expiresAt={expiresAt} />
  }

  // Se não tem premium, não mostra nada (o TrialStatus cuida do trial)
  return null
}