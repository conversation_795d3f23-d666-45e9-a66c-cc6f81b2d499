import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";

// Rotas protegidas que requerem autenticação
export const protectedRoutes = [
  "/dashboard",
  "/admin", 
  "/profile",
  "/browse",
  "/sports",
  "/favorites",
  "/api/protected",
];

// Rotas de autenticação (usuários logados não devem acessar)
export const authRoutes = [
  "/auth/sign-in",
  "/auth/sign-up",
  "/auth/password-reset",
  "/login",
  "/register",
];

// Função para aplicar headers de segurança
export function applySecurityHeaders(response: NextResponse): NextResponse {
  // Headers de segurança críticos
  response.headers.set("X-Content-Type-Options", "nosniff");
  response.headers.set("X-Frame-Options", "DENY");
  response.headers.set("X-XSS-Protection", "1; mode=block");
  response.headers.set("Referrer-Policy", "strict-origin-when-cross-origin");
  response.headers.set("Permissions-Policy", "camera=(), microphone=(), geolocation=()");
  
  // Content Security Policy
  const csp = [
    "default-src 'self'",
    "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net",
    "style-src 'self' 'unsafe-inline'",
    "img-src 'self' data: https: blob:",
    "font-src 'self' data:",
    "connect-src 'self' http://localhost:* https://api.stack-auth.com wss://*.supabase.co wss://*.stack-auth.com https://*.supabase.co https://iptv-org.github.io https://*.m3u8 https://*.ts https://*.mpd https://*.mp4 https://*.webm https://* http://*",
    "media-src 'self' https: http: blob: data:",
    "object-src 'none'",
    "frame-ancestors 'none'",
  ].join("; ");
  
  response.headers.set("Content-Security-Policy", csp);
  
  return response;
}

// Função para verificar se a rota é protegida
export function isProtectedRoute(pathname: string): boolean {
  return protectedRoutes.some(route => pathname.startsWith(route));
}

// Função para verificar se é rota de autenticação
export function isAuthRoute(pathname: string): boolean {
  return authRoutes.some(route => pathname.startsWith(route));
}

// Função para log de auditoria
export async function logAccess(
  userId: string, 
  path: string, 
  ip?: string,
  action: string = 'access'
): Promise<void> {
  try {
    // TODO: Implementar quando tivermos acesso ao banco de dados
    console.log(`[AUDIT] User: ${userId}, Action: ${action}, Path: ${path}, IP: ${ip || 'unknown'}`);
  } catch (error) {
    console.error("Erro ao registrar log de auditoria:", error);
  }
}