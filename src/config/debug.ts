/**
 * Configuração de Debug Global
 */

export const DEBUG = process.env.NEXT_PUBLIC_DEBUG?.toLowerCase() === 'true' || 
                     process.env.DEBUG?.toLowerCase() === 'true';

export const LOG_LEVELS = {
  ERROR: 0,
  WARN: 1,
  INFO: 2,
  DEBUG: 3,
  TRACE: 4
} as const;

export const CURRENT_LOG_LEVEL = DEBUG ? LOG_LEVELS.DEBUG : LOG_LEVELS.INFO;

// Tags para diferentes componentes
export const LOG_TAGS = {
  HTTP: 'http',
  PLAYER: 'player',
  IPTV: 'iptv',
  SPORTS: 'sports',
  AUTH: 'auth',
  STREAM: 'stream',
  ENV: 'env',
  IAM: 'iam'
} as const;

export type LogTag = typeof LOG_TAGS[keyof typeof LOG_TAGS];