// Streams de demonstração que funcionam sem CORS
export const DEMO_STREAMS = {
  // Streams HLS públicos e funcionais
  sports: [
    {
      name: 'Stream Deportivo 1',
      url: 'https://bitdash-a.akamaihd.net/content/MI201109210084_1/m3u8s/f08e80da-bf1d-4e3d-8899-f0f6155f6efa.m3u8',
      type: 'hls' as const
    },
    {
      name: 'Stream Deportivo 2', 
      url: 'https://bitdash-a.akamaihd.net/content/sintel/hls/playlist.m3u8',
      type: 'hls' as const
    },
    {
      name: 'Stream Deportivo 3',
      url: 'https://demo.unified-streaming.com/k8s/features/stable/video/tears-of-steel/tears-of-steel.ism/.m3u8',
      type: 'hls' as const
    }
  ],
  
  // Streams MP4 como fallback
  fallback: [
    {
      name: 'Demo Video 1',
      url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4',
      type: 'mp4' as const
    },
    {
      name: 'Demo Video 2',
      url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4',
      type: 'mp4' as const
    }
  ]
}

// Mapeamento de canais para streams demo
export const CHANNEL_STREAM_MAP: Record<string, string> = {
  'espn': DEMO_STREAMS.sports[0].url,
  'fox': DEMO_STREAMS.sports[1].url,
  'sky': DEMO_STREAMS.sports[2].url,
  'bein': DEMO_STREAMS.sports[0].url,
  'dazn': DEMO_STREAMS.sports[1].url,
  'default': DEMO_STREAMS.sports[0].url
}

// Função para obter stream baseado no nome do canal
export function getStreamForChannel(channelName: string): string {
  const lowerName = channelName.toLowerCase()
  
  for (const [key, url] of Object.entries(CHANNEL_STREAM_MAP)) {
    if (lowerName.includes(key)) {
      return url
    }
  }
  
  return CHANNEL_STREAM_MAP.default
}