'use client'

import { useEffect } from 'react'
import { useAuthOptional } from '@/hooks/use-auth'
import { useAccessStore } from '@/stores/access.store'
import { useTrialStore } from '@/stores/trial.store'

export function usePremiumCheck() {
  const { user } = useAuthOptional()
  const { hasAccess, accessType, checkAccess } = useAccessStore()
  const { resetTrial, isExpired } = useTrialStore()
  
  useEffect(() => {
    const checkPremiumStatus = async () => {
      if (user) {
        console.log('[PREMIUM-CHECK] Checking premium for user:', user.id)
        await checkAccess(user.id)
      }
    }
    
    checkPremiumStatus()
  }, [user, checkAccess])
  
  // Se tem premium e trial está expirado, resetar o trial
  useEffect(() => {
    if (hasAccess && accessType === 'assinatura' && isExpired) {
      console.log('[PREMIUM-CHECK] User has premium but trial is expired, resetting trial')
      resetTrial()
    }
  }, [hasAccess, accessType, isExpired, resetTrial])
  
  return {
    hasPremium: hasAccess && accessType === 'assinatura',
    isCheckingPremium: !user || !hasAccess
  }
}