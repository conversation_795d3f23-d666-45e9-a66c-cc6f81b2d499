'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'

interface SimpleUser {
  id: string
  email: string
  name: string
  loggedAt: string
}

export function useSimpleAuth() {
  const [user, setUser] = useState<SimpleUser | null>(null)
  const [loading, setLoading] = useState(true)
  const router = useRouter()

  useEffect(() => {
    console.log('🔐 [useSimpleAuth] Hook inicializado')
    checkAuth()
    
    // Escutar mudanças no localStorage
    const handleStorageChange = () => {
      console.log('🔄 [useSimpleAuth] LocalStorage mudou - verificando auth')
      checkAuth()
    }
    
    window.addEventListener('storage', handleStorageChange)
    return () => window.removeEventListener('storage', handleStorageChange)
  }, [])

  const checkAuth = () => {
    console.log('🔍 [useSimpleAuth] Verificando autenticação...')
    try {
      const userStr = localStorage.getItem('streamplus-user')
      if (userStr) {
        const userData = JSON.parse(userStr)
        console.log('✅ [useSimpleAuth] Usuário encontrado:', {
          email: userData.email,
          loggedAt: userData.loggedAt
        })
        setUser(userData)
      } else {
        console.log('🚫 [useSimpleAuth] Nenhum usuário no localStorage')
        setUser(null)
      }
    } catch (error) {
      console.error('❌ [useSimpleAuth] Erro ao verificar auth:', error)
      setUser(null)
    } finally {
      setLoading(false)
    }
  }

  const login = (email: string, password: string) => {
    // TODO: Implementar login real com banco de dados
    throw new Error('Login por email/senha não está configurado. Use o login com Google.')
  }

  const logout = () => {
    localStorage.removeItem('streamplus-user')
    setUser(null)
    router.push('/')
  }

  const requireAuth = () => {
    if (!loading && !user) {
      router.push('/auth/simple-login')
    }
  }

  return {
    user,
    loading,
    login,
    logout,
    requireAuth,
    isAuthenticated: !!user
  }
}