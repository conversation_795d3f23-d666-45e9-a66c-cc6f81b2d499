'use client'

import { useEffect, useState } from 'react'
import { Fixture, AllSportEvent } from '@/types/sports'
import { footballClient, allSportDBClient } from '@/services/api/sports'

interface UseSportsDataReturn {
  upcomingFootball: Fixture[]
  upcomingFights: AllSportEvent[]
  upcomingMotorsports: AllSportEvent[]
  upcomingBasketball: AllSportEvent[]
  loading: boolean
  error: string | null
}

export function useSportsData(): UseSportsDataReturn {
  const [upcomingFootball, setUpcomingFootball] = useState<Fixture[]>([])
  const [upcomingFights, setUpcomingFights] = useState<AllSportEvent[]>([])
  const [upcomingMotorsports, setUpcomingMotorsports] = useState<AllSportEvent[]>([])
  const [upcomingBasketball, setUpcomingBasketball] = useState<AllSportEvent[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  
  useEffect(() => {
    const loadSportsData = async () => {
      try {
        setLoading(true)
        setError(null)
        
        // Obter data de hoje e próximos 7 dias
        const today = new Date().toISOString().split('T')[0]
        const nextWeek = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
        
        // Carregar dados em paralelo
        const [footballData, fightsData, motorsportsData, basketballData] = await Promise.allSettled([
          // Futebol - próximos jogos
          footballClient.getFixturesByDate(today).then(fixtures => 
            fixtures
              .filter(f => new Date(f.fixture.date) >= new Date())
              .sort((a, b) => new Date(a.fixture.date).getTime() - new Date(b.fixture.date).getTime())
              .slice(0, 6)
          ),
          
          // Lutas (MMA, Boxe)
          allSportDBClient.getEvents({
            sport: 'MMA',
            dateFrom: today,
            dateTo: nextWeek,
            liveUrlExists: 1
          }).then(events => events.slice(0, 4)),
          
          // Motorsports (F1, MotoGP)
          allSportDBClient.getEvents({
            sport: 'Formula',
            dateFrom: today,
            dateTo: nextWeek,
            liveUrlExists: 1
          }).then(events => events.slice(0, 4)),
          
          // Basquete
          allSportDBClient.getEvents({
            sport: 'Basketball',
            dateFrom: today,
            dateTo: nextWeek,
            liveUrlExists: 1
          }).then(events => events.slice(0, 4))
        ])
        
        // Processar resultados
        if (footballData.status === 'fulfilled') {
          setUpcomingFootball(footballData.value)
        }
        
        if (fightsData.status === 'fulfilled') {
          setUpcomingFights(fightsData.value)
        }
        
        if (motorsportsData.status === 'fulfilled') {
          setUpcomingMotorsports(motorsportsData.value)
        }
        
        if (basketballData.status === 'fulfilled') {
          setUpcomingBasketball(basketballData.value)
        }
        
      } catch (err) {
        console.error('Erro ao carregar dados esportivos:', err)
        setError('Erro ao carregar dados esportivos')
      } finally {
        setLoading(false)
      }
    }
    
    loadSportsData()
  }, [])
  
  return {
    upcomingFootball,
    upcomingFights,
    upcomingMotorsports,
    upcomingBasketball,
    loading,
    error
  }
}