'use client'

import { useState, useEffect } from 'react'
import { toast } from 'sonner'

export interface IPTVChannel {
  id: string
  name: string
  url: string
  country?: string
  languages?: string[]
  logo?: string
  categories?: string[]
  headers?: {
    'Referer'?: string
    'User-Agent'?: string
  }
}

export function useIPTVChannels(popular = true, limit = 20) {
  const [channels, setChannels] = useState<IPTVChannel[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    async function fetchChannels() {
      try {
        setLoading(true)
        setError(null)
        
        const params = new URLSearchParams({
          popular: popular.toString(),
          limit: limit.toString()
        })
        
        // Usar o novo endpoint IPTV-ORG
        console.log('🎯 Buscando canais do IPTV-ORG')
        const response = await fetch(`/api/iptv-org/streams?${params}`)
        const data = await response.json()
        
        if (data.success && data.data && data.data.length > 0) {
          // Transformar dados do IPTV-ORG para o formato esperado
          const iptvChannels = data.data.map((stream: any) => ({
            id: stream.uniqueId || stream.channel, // Usar uniqueId para evitar duplicatas
            name: stream.name || stream.channel,
            url: stream.url,
            country: stream.country,
            logo: stream.logo,
            categories: stream.categories || [],
            headers: {
              'Referer': stream.referrer || stream.http_referrer,
              'User-Agent': stream.user_agent || stream.http_user_agent || 'Mozilla/5.0'
            }
          }))
          setChannels(iptvChannels)
        } else {
          throw new Error('No se pudieron cargar canales IPTV')
        }
      } catch (err) {
        console.error('Error fetching IPTV channels:', err)
        setError(err instanceof Error ? err.message : 'Error desconocido')
        toast.error('Error al cargar canales IPTV')
      } finally {
        setLoading(false)
      }
    }

    fetchChannels()
  }, [popular, limit])

  return { channels, loading, error }
}