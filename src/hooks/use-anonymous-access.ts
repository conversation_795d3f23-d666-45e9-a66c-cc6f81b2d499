'use client'

import { useEffect, useState } from 'react'
import { createClient } from '@/lib/supabase/client'
import { useAccessStore } from '@/stores/access.store'

export function useAnonymousAccess() {
  const [isChecking, setIsChecking] = useState(true)
  const { hasAccess, accessType } = useAccessStore()
  
  useEffect(() => {
    checkAnonymousAccess()
  }, [])
  
  const checkAnonymousAccess = async () => {
    try {
      // Se já tem acesso autenticado, não verificar anônimo
      if (hasAccess && accessType !== 'anonymous') {
        setIsChecking(false)
        return
      }
      
      // Verificar cookie
      const sessionId = getCookie('app_access_token')
      
      // Se não tem cookie, verificar localStorage
      const localSessionId = localStorage.getItem('app_session_id')
      const localExpiresAt = localStorage.getItem('app_expires_at')
      
      const finalSessionId = sessionId || localSessionId
      
      if (!finalSessionId) {
        setIsChecking(false)
        return
      }
      
      // Verificar no banco se ainda é válido
      const supabase = createClient()
      const { data, error } = await supabase
        .rpc('check_anonymous_access', { p_session_id: finalSessionId })
        .single()
      
      if (!error && data?.has_access) {
        // Atualizar store de acesso
        useAccessStore.setState({
          hasAccess: true,
          accessType: 'app_token' as any,
          expiresAt: data.expires_at ? new Date(data.expires_at) : null,
          lastCheck: new Date()
        })
        
        // Salvar no localStorage para persistir
        if (!sessionId && localSessionId) {
          localStorage.setItem('app_session_id', localSessionId)
          localStorage.setItem('app_expires_at', data.expires_at || '')
        }
      }
      
    } catch (error) {
      console.error('Error checking anonymous access:', error)
    } finally {
      setIsChecking(false)
    }
  }
  
  return { isChecking }
}

function getCookie(name: string): string | null {
  if (typeof document === 'undefined') return null
  
  const value = `; ${document.cookie}`
  const parts = value.split(`; ${name}=`)
  
  if (parts.length === 2) {
    return parts.pop()?.split(';').shift() || null
  }
  
  return null
}