import { useEffect, useState } from 'react'
import useS<PERSON> from 'swr'

interface LiveMatch {
  id: string
  homeTeam: string
  awayTeam: string
  homeScore: number
  awayScore: number
  status: string
  minute?: string
  competition: string
  competitionLogo?: string
  startTime: Date
  isLive: boolean
}

const fetcher = async (url: string) => {
  const res = await fetch(url)
  if (!res.ok) throw new Error('Failed to fetch')
  return res.json()
}

export function useLiveMatches() {
  const [allMatches, setAllMatches] = useState<LiveMatch[]>([])

  // Buscar fixtures de futebol
  const { data: fixturesResponse } = useSWR('/api/sports/live-fixtures', fetcher, {
    refreshInterval: 30000, // 30 segundos
    revalidateOnFocus: false,
  })

  // Buscar outros eventos esportivos
  const { data: eventsResponse } = useSWR('/api/sports/live-events', fetcher, {
    refreshInterval: 30000,
    revalidateOnFocus: false,
  })

  // Buscar próximos jogos se não houver ao vivo
  const { data: upcomingData } = useSWR('/api/sports/upcoming-events', fetcher, {
    refreshInterval: 60000, // 1 minuto
    revalidateOnFocus: false,
  })

  useEffect(() => {
    const matches: LiveMatch[] = []

    // Processar fixtures de futebol
    if (fixturesResponse?.data && Array.isArray(fixturesResponse.data)) {
      const liveFootball = fixturesResponse.data
        .filter((fixture: any) => {
          const status = fixture.fixture?.status?.short
          return ['LIVE', '1H', '2H', 'HT', 'ET', 'P'].includes(status)
        })
        .map((fixture: any) => ({
          id: `football-${fixture.fixture.id}`,
          homeTeam: fixture.teams.home.name,
          awayTeam: fixture.teams.away.name,
          homeScore: fixture.goals.home || 0,
          awayScore: fixture.goals.away || 0,
          status: mapFootballStatus(fixture.fixture.status.short),
          minute: fixture.fixture.status.elapsed ? `${fixture.fixture.status.elapsed}'` : '',
          competition: fixture.league.name,
          competitionLogo: fixture.league.logo,
          startTime: new Date(fixture.fixture.date),
          isLive: true
        }))
      matches.push(...liveFootball)
    }

    // Processar outros eventos esportivos ao vivo
    if (eventsResponse?.data && Array.isArray(eventsResponse.data)) {
      const liveEvents = eventsResponse.data
        .filter((event: any) => 
          event.strStatus === 'Live' || 
          event.strStatus === 'In Progress' ||
          event.strStatus === 'Playing'
        )
        .map((event: any) => ({
          id: `event-${event.idEvent}`,
          homeTeam: event.strHomeTeam || event.strEvent.split(' vs ')[0] || event.strPlayer1,
          awayTeam: event.strAwayTeam || event.strEvent.split(' vs ')[1] || event.strPlayer2 || '',
          homeScore: parseInt(event.intHomeScore) || parseInt(event.intScore1) || 0,
          awayScore: parseInt(event.intAwayScore) || parseInt(event.intScore2) || 0,
          status: 'En Vivo',
          minute: event.strProgress || '',
          competition: event.strLeague || event.strSport,
          competitionLogo: event.strLeagueBadge,
          startTime: new Date(`${event.dateEvent} ${event.strTime}`),
          isLive: true
        }))
      matches.push(...liveEvents)
    }

    // Se não houver jogos ao vivo, adicionar próximos jogos
    if (matches.length === 0 && upcomingData?.events) {
      const nextMatches = upcomingData.events
        .slice(0, 5)
        .map((event: any) => ({
          id: `upcoming-${event.id}`,
          homeTeam: event.homeTeam,
          awayTeam: event.awayTeam,
          homeScore: 0,
          awayScore: 0,
          status: 'Próximamente',
          competition: event.league,
          competitionLogo: event.leagueLogo,
          startTime: new Date(event.date),
          isLive: false
        }))
      matches.push(...nextMatches)
    }

    // Ordenar por importância e status
    const sortedMatches = matches.sort((a, b) => {
      // Priorizar jogos ao vivo
      if (a.isLive && !b.isLive) return -1
      if (!a.isLive && b.isLive) return 1
      
      // Priorizar grandes ligas
      const priorityLeagues = ['Champions League', 'La Liga', 'Premier League', 'Serie A', 'Bundesliga', 'Europa League']
      const aIndex = priorityLeagues.findIndex(league => a.competition.includes(league))
      const bIndex = priorityLeagues.findIndex(league => b.competition.includes(league))
      
      if (aIndex !== -1 && bIndex === -1) return -1
      if (aIndex === -1 && bIndex !== -1) return 1
      if (aIndex !== -1 && bIndex !== -1) return aIndex - bIndex
      
      return 0
    })

    setAllMatches(sortedMatches)
  }, [fixturesResponse, eventsResponse, upcomingData])

  return {
    matches: allMatches,
    primaryMatch: allMatches[0] || null,
    loading: !fixturesResponse && !eventsResponse && !upcomingData,
    error: null
  }
}

function mapFootballStatus(status: string): string {
  const statusMap: Record<string, string> = {
    'LIVE': 'En Vivo',
    '1H': 'Primer Tiempo',
    '2H': 'Segundo Tiempo',
    'HT': 'Descanso',
    'ET': 'Tiempo Extra',
    'P': 'Penales',
    'FT': 'Finalizado',
    'NS': 'Por Comenzar'
  }
  return statusMap[status] || status
}