'use client'

import { useEffect, useState } from 'react'
import { useSportsStore } from '@/stores/sports.store'
import { Fixture, AllSportEvent } from '@/types/sports'
import { autoUpdateService } from '@/services/background/auto-update.service'

// Mapeamento de todos os esportes disponíveis
export const ALL_SPORTS_CATEGORIES = [
  { id: 'football', name: 'Futebol', emoji: '⚽', api: 'api-football' },
  { id: 'basketball', name: 'Basque<PERSON>', emoji: '🏀', api: 'api-basketball' },
  { id: 'mma', name: 'MMA/UFC', emoji: '🥊', api: 'allsportdb' },
  { id: 'boxing', name: 'Boxe', emoji: '🥊', api: 'allsportdb' },
  { id: 'formula1', name: 'Fórmula 1', emoji: '🏎️', api: 'api-formula1' },
  { id: 'motogp', name: '<PERSON><PERSON><PERSON>', emoji: '🏍️', api: 'allsportdb' },
  { id: 'baseball', name: 'Baseball', emoji: '⚾', api: 'api-baseball' },
  { id: 'hockey', name: 'Hockey', emoji: '🏒', api: 'api-hockey' },
  { id: 'handball', name: 'Handball', emoji: '🤾', api: 'api-handball' },
  { id: 'rugby', name: 'Rugby', emoji: '🏉', api: 'api-rugby' },
  { id: 'volleyball', name: 'Vôlei', emoji: '🏐', api: 'api-volleyball' },
  { id: 'nfl', name: 'NFL', emoji: '🏈', api: 'api-nfl' },
  { id: 'nba', name: 'NBA', emoji: '🏀', api: 'api-nba' },
  { id: 'afl', name: 'AFL', emoji: '🏈', api: 'api-afl' },
  { id: 'tennis', name: 'Tênis', emoji: '🎾', api: 'allsportdb' },
  { id: 'esports', name: 'eSports', emoji: '🎮', api: 'allsportdb' }
]

interface SportCategoryData {
  category: typeof ALL_SPORTS_CATEGORIES[0]
  events: AllSportEvent[]
  loading: boolean
  error?: string
}

export function useAllSportsData() {
  const { upcomingEvents, fetchUpcomingEvents, loadingUpcomingEvents } = useSportsStore()
  const [categorizedData, setCategorizedData] = useState<SportCategoryData[]>([])
  const [initialLoad, setInitialLoad] = useState(true)

  useEffect(() => {
    // Desabilitar auto-update temporariamente para evitar erros CORS
    // if (typeof window !== 'undefined' && !autoUpdateService.getStatus().isActive) {
    //   console.log('🚀 Iniciando serviço de atualização automática')
    //   autoUpdateService.startAll()
    // }

    // Carregar dados iniciais
    const loadData = async () => {
      if (initialLoad) {
        await fetchUpcomingEvents()
        setInitialLoad(false)
      }
    }

    loadData()

    // Atualizar a cada 5 minutos em vez de 30 segundos
    const interval = setInterval(() => {
      fetchUpcomingEvents()
    }, 5 * 60 * 1000) // 5 minutos

    return () => clearInterval(interval)
  }, [fetchUpcomingEvents, initialLoad])

  // Categorizar eventos por esporte quando upcomingEvents mudar
  useEffect(() => {
    const categorized = ALL_SPORTS_CATEGORIES.map(category => {
      // Filtrar eventos por esporte
      const sportEvents = upcomingEvents.filter(event => {
        const eventSport = event.sport?.toLowerCase() || ''
        const categoryName = category.name.toLowerCase()
        
        // Correspondência flexível
        if (category.id === 'football' && (eventSport.includes('soccer') || eventSport.includes('football'))) return true
        if (category.id === 'mma' && (eventSport.includes('mma') || eventSport.includes('ufc'))) return true
        if (category.id === 'boxing' && eventSport.includes('box')) return true
        if (category.id === 'formula1' && (eventSport.includes('f1') || eventSport.includes('formula'))) return true
        if (category.id === 'motogp' && (eventSport.includes('moto') || eventSport.includes('gp'))) return true
        if (category.id === 'nba' && eventSport.includes('nba')) return true
        if (category.id === 'nfl' && eventSport.includes('nfl')) return true
        if (category.id === 'tennis' && eventSport.includes('tennis')) return true
        if (category.id === 'esports' && (
          eventSport.includes('esports') || 
          eventSport.includes('cs:go') || 
          eventSport.includes('league of legends') ||
          eventSport.includes('dota') ||
          eventSport.includes('valorant')
        )) return true
        
        // Correspondência direta
        return eventSport.includes(category.id) || eventSport.includes(categoryName.split('/')[0])
      })

      return {
        category,
        events: sportEvents.slice(0, 6), // Limitar a 6 eventos por categoria
        loading: loadingUpcomingEvents,
        error: undefined
      }
    })

    setCategorizedData(categorized)
  }, [upcomingEvents, loadingUpcomingEvents])

  // Retornar dados organizados
  return {
    allCategories: categorizedData,
    totalEvents: upcomingEvents.length,
    loading: loadingUpcomingEvents,
    // Compatibilidade com código existente
    upcomingFootball: categorizedData.find(c => c.category.id === 'football')?.events || [],
    upcomingFights: [
      ...(categorizedData.find(c => c.category.id === 'mma')?.events || []),
      ...(categorizedData.find(c => c.category.id === 'boxing')?.events || [])
    ].slice(0, 4),
    upcomingMotorsports: [
      ...(categorizedData.find(c => c.category.id === 'formula1')?.events || []),
      ...(categorizedData.find(c => c.category.id === 'motogp')?.events || [])
    ].slice(0, 4),
    upcomingBasketball: [
      ...(categorizedData.find(c => c.category.id === 'basketball')?.events || []),
      ...(categorizedData.find(c => c.category.id === 'nba')?.events || [])
    ].slice(0, 4),
    // Novas categorias
    upcomingTennis: categorizedData.find(c => c.category.id === 'tennis')?.events || [],
    upcomingRugby: categorizedData.find(c => c.category.id === 'rugby')?.events || [],
    upcomingVolleyball: categorizedData.find(c => c.category.id === 'volleyball')?.events || [],
    upcomingHandball: categorizedData.find(c => c.category.id === 'handball')?.events || [],
    upcomingHockey: categorizedData.find(c => c.category.id === 'hockey')?.events || [],
    upcomingBaseball: categorizedData.find(c => c.category.id === 'baseball')?.events || [],
    upcomingNFL: categorizedData.find(c => c.category.id === 'nfl')?.events || [],
    upcomingAFL: categorizedData.find(c => c.category.id === 'afl')?.events || [],
    upcomingEsports: categorizedData.find(c => c.category.id === 'esports')?.events || [],
  }
}