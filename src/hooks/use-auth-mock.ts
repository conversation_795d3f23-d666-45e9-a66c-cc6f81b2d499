'use client'

import { useSimpleAuth } from './use-simple-auth'
import { useEffect } from 'react'

// Mock usando o sistema de auth simples
export function useUser(options?: { or?: 'redirect' }) {
  const { user, loading, requireAuth } = useSimpleAuth()
  
  useEffect(() => {
    if (options?.or === 'redirect' && !loading && !user) {
      requireAuth()
    }
  }, [user, loading, options?.or])
  
  if (loading) return null
  
  if (user) {
    return {
      id: user.id,
      primaryEmail: user.email,
      signedUpWithEmail: user.email,
      createdAt: new Date(user.loggedAt),
      updatedAt: new Date(user.loggedAt),
    }
  }
  
  return null
}

export function useAuth() {
  const user = useUser();
  
  return {
    user: user ? {
      id: user.id,
      email: user.primaryEmail || user.signedUpWithEmail || '',
      created_at: user.createdAt?.toISOString(),
      updated_at: user.updatedAt?.toISOString(),
    } : null,
    session: user ? { user } : null,
    loading: false
  };
}