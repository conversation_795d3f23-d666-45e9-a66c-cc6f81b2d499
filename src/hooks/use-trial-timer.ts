'use client'

import { useEffect, useRef } from 'react'
import { useTrialStore } from '@/stores/trial.store'
import { useAccessStore } from '@/stores/access.store'

export function useTrialTimer() {
  const { 
    startTime, 
    timeRemaining, 
    isExpired, 
    updateTimeRemaining,
    expireTrial 
  } = useTrialStore()
  
  const { hasAccess, accessType } = useAccessStore()
  const intervalRef = useRef<NodeJS.Timeout | null>(null)
  
  useEffect(() => {
    console.log('[TRIAL-TIMER] useEffect triggered', {
      startTime,
      isExpired,
      timeRemaining,
      hasAccess,
      accessType
    })
    
    // Se usuário tem acesso (premium ou app verificado), não executar timer
    if (hasAccess && (accessType === 'assinatura' || accessType === 'app_verified')) {
      console.log('[TRIAL-TIMER] User has access, skipping timer:', accessType)
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
        intervalRef.current = null
      }
      return
    }
    
    // Se já expirou, não fazer nada
    if (isExpired) {
      console.log('[TRIAL-TIMER] Already expired, skipping timer')
      return
    }
    
    // Função para atualizar o tempo
    const updateTimer = () => {
      if (!startTime) {
        console.log('[TRIAL-TIMER] No startTime, skipping update')
        return
      }
      
      const now = Date.now()
      const elapsed = Math.floor((now - startTime) / 1000)
      const remaining = Math.max(0, 5 * 60 - elapsed) // 5 minutos = 300 segundos
      
      console.log('[TRIAL-TIMER] Timer update:', {
        now,
        startTime,
        elapsed,
        remaining,
        shouldExpire: remaining <= 0
      })
      
      updateTimeRemaining(remaining)
      
      if (remaining <= 0) {
        console.log('[TRIAL-TIMER] Timer reached 0, calling expireTrial()')
        expireTrial()
        if (intervalRef.current) {
          clearInterval(intervalRef.current)
          intervalRef.current = null
        }
      }
    }
    
    // Atualizar imediatamente
    updateTimer()
    
    // Configurar intervalo para atualizar a cada segundo
    intervalRef.current = setInterval(updateTimer, 1000)
    
    // Limpar intervalo ao desmontar
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
        intervalRef.current = null
      }
    }
  }, [startTime, isExpired, updateTimeRemaining, expireTrial, hasAccess, accessType])
  
  // Formatar tempo para exibição
  const formatTime = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${minutes}:${secs.toString().padStart(2, '0')}`
  }
  
  return {
    timeRemaining,
    timeRemainingFormatted: formatTime(timeRemaining),
    isExpired
  }
}