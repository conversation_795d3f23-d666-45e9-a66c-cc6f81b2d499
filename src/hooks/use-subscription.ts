'use client'

import { useState, useEffect } from 'react'
import { useAuth } from './use-auth'

interface SubscriptionStatus {
  hasAccess: boolean
  isSubscribed: boolean
  isInTrial: boolean
  trialEndsAt?: string
  subscriptionEndsAt?: string
  subscriptionTier?: string
  loading: boolean
}

export function useSubscription() {
  const { user } = useAuth()
  const [status, setStatus] = useState<SubscriptionStatus>({
    hasAccess: false,
    isSubscribed: false,
    isInTrial: false,
    loading: true,
  })

  useEffect(() => {
    if (!user) {
      setStatus({
        hasAccess: false,
        isSubscribed: false,
        isInTrial: false,
        loading: false,
      })
      return
    }

    checkSubscriptionStatus()
  }, [user])

  const checkSubscriptionStatus = async () => {
    try {
      console.log('[USE-SUBSCRIPTION] Checking subscription status for user:', user?.id)
      const response = await fetch('/api/stripe/subscription-status')
      
      if (response.ok) {
        const data = await response.json()
        console.log('[USE-SUBSCRIPTION] Response data:', data)
        setStatus({
          ...data,
          loading: false,
        })
      } else {
        console.log('[USE-SUBSCRIPTION] Response not OK:', response.status)
        setStatus({
          hasAccess: false,
          isSubscribed: false,
          isInTrial: false,
          loading: false,
        })
      }
    } catch (error) {
      console.error('[USE-SUBSCRIPTION] Error:', error)
      setStatus({
        hasAccess: false,
        isSubscribed: false,
        isInTrial: false,
        loading: false,
      })
    }
  }

  const refresh = () => {
    checkSubscriptionStatus()
  }

  return {
    ...status,
    refresh,
  }
}