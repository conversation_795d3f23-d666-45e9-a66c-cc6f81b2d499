"use client";

import { useStackApp, useUser as useStackUser } from "@stackframe/stack";
import { AuthLogger } from "@/lib/debug/auth-logger";
import { useEffect, useState } from "react";
import { useRouter, usePathname } from "next/navigation";

interface MockUser {
  id: string;
  primaryEmail: string | null;
  displayName: string | null;
  emailVerified: boolean;
  hasPassword: boolean;
  signedUpAt: Date;
  clientMetadata: any;
  update: (data: any) => Promise<void>;
}

export function useAuth() {
  AuthLogger.log('🔐 useAuth hook llamado - modo: redirect')
  
  const app = useStackApp();
  const router = useRouter();
  const pathname = usePathname();
  const [isLoading, setIsLoading] = useState(true);
  
  // Try to get Stack Auth user without redirect first
  const stackUser = useStackUser();
  
  useEffect(() => {
    setIsLoading(false);
  }, [stackUser]);
  
  // Redirect logic - only redirect if no user after loading
  useEffect(() => {
    if (!isLoading && !stackUser) {
      const protectedRoutes = ['/browse', '/sports', '/dashboard', '/profile', '/favorites', '/subscription'];
      const isProtectedRoute = protectedRoutes.some(route => pathname.startsWith(route));
      
      if (isProtectedRoute) {
        AuthLogger.log('❌ Usuario no autenticado en ruta protegida - redirigiendo...')
        // Adicionar pequeno delay para evitar redirect prematuro
        setTimeout(() => {
          router.push('/login');
        }, 100);
      }
    }
  }, [isLoading, stackUser, pathname, router]);

  return {
    user: stackUser,
    isAuthenticated: !!stackUser,
    isLoading,
    signOut: async () => {
      AuthLogger.log('🚪 Iniciando cierre de sesión...')
      try {
        // Try to sign out from Stack Auth
        if (stackUser) {
          await app.signOut();
        }
        
        AuthLogger.log('✅ Cierre de sesión realizado correctamente')
        router.push('/');
      } catch (error) {
        AuthLogger.error('❌ Error al cerrar sesión:', error)
        throw error
      }
    },
    updateProfile: async (data: { displayName?: string }) => {
      if (!stackUser) {
        AuthLogger.warn('⚠️ Intento de actualizar perfil sin usuario autenticado')
        return;
      }
      
      AuthLogger.log('📝 Actualizando perfil:', data)
      try {
        await stackUser.update(data);
        AuthLogger.log('✅ Perfil actualizado correctamente')
      } catch (error) {
        AuthLogger.error('❌ Error al actualizar perfil:', error)
        throw error
      }
    }
  };
}

export function useAuthOptional() {
  AuthLogger.log('🔐 useAuthOptional hook llamado - modo: opcional')
  
  const app = useStackApp();
  const user = useStackUser();
  
  useEffect(() => {
    if (user) {
      AuthLogger.log('✅ Usuario autenticado (opcional):', {
        id: user.id,
        email: AuthLogger.maskSensitive(user.primaryEmail || 'sin email'),
        displayName: user.displayName
      })
    } else {
      AuthLogger.log('ℹ️ Usuario no autenticado (opcional)')
    }
  }, [user])

  return {
    user,
    isAuthenticated: !!user,
    signOut: async () => {
      AuthLogger.log('🚪 Iniciando cierre de sesión...')
      try {
        await app.signOut();
        AuthLogger.log('✅ Cierre de sesión realizado correctamente')
      } catch (error) {
        AuthLogger.error('❌ Error al cerrar sesión:', error)
        throw error
      }
    }
  };
}