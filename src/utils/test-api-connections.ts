/**
 * Script para testar a conectividade com todas as APIs
 * Execute no console: testAPIConnections()
 */

export async function testAPIConnections() {
  console.log('🧪 Testando conectividade com todas as APIs...\n')
  
  const API_KEY = 'eaf42062f90877281cf06a2ad2e1d11e'
  
  const apis = [
    { name: 'Football', url: 'https://v3.football.api-sports.io/status' },
    { name: 'Basketball', url: 'https://v1.basketball.api-sports.io/status' },
    { name: 'Baseball', url: 'https://v1.baseball.api-sports.io/status' },
    { name: 'Hockey', url: 'https://v1.hockey.api-sports.io/status' },
    { name: 'Volleyball', url: 'https://v1.volleyball.api-sports.io/status' },
    { name: 'Handball', url: 'https://v1.handball.api-sports.io/status' },
    { name: 'Rugby', url: 'https://v1.rugby.api-sports.io/status' },
    { name: 'Formula 1', url: 'https://v1.formula-1.api-sports.io/status' },
    { name: 'MMA', url: 'https://v1.mma.api-sports.io/status' },
    { name: 'NFL', url: 'https://v1.nfl.api-sports.io/status' },
    { name: 'AFL', url: 'https://v1.afl.api-sports.io/status' }
  ]
  
  const results = []
  
  for (const api of apis) {
    console.log(`Testando ${api.name}...`)
    
    try {
      const response = await fetch(api.url, {
        method: 'GET',
        headers: {
          'x-apisports-key': API_KEY
        }
      })
      
      const data = await response.json()
      
      results.push({
        api: api.name,
        status: response.ok ? '✅ OK' : '❌ ERRO',
        statusCode: response.status,
        account: data.response?.account,
        subscription: data.response?.subscription,
        requests: data.response?.requests
      })
      
    } catch (error) {
      results.push({
        api: api.name,
        status: '❌ ERRO',
        error: error.message
      })
    }
  }
  
  console.log('\n📊 RESULTADO DOS TESTES:')
  console.table(results)
  
  // Verificar AllSportDB também
  console.log('\n🧪 Testando AllSportDB...')
  try {
    const response = await fetch('https://api.allsportdb.com/v3/sports?apiKey=20d8be22-0948-4131-ae61-37dd981028bd')
    const data = await response.json()
    console.log('✅ AllSportDB:', response.ok ? 'Funcionando' : 'Com problemas')
    console.log('   Esportes disponíveis:', data.data?.length || 0)
  } catch (error) {
    console.log('❌ AllSportDB:', error.message)
  }
  
  return results
}

// Expor globalmente
if (typeof window !== 'undefined') {
  (window as any).testAPIConnections = testAPIConnections
}