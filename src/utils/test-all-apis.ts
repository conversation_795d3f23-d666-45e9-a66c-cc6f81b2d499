/**
 * <PERSON>ript para testar todas as 13 APIs da API Sports
 * Execute no console do navegador para verificar se todas as APIs estão funcionando
 */

import { 
  footballClient,
  basketballClient,
  baseballClient,
  hockeyClient,
  volleyballClient,
  handballClient,
  rugbyClient,
  formula1Client,
  mmaClient,
  nflClient,
  nbaClient,
  aflClient
} from '@/services/api/sports/api-sports.client'

export async function testAllAPIs() {
  console.log('🧪 Iniciando teste de todas as 13 APIs da API Sports...\n')
  
  const clients = [
    { name: 'Football', client: footballClient },
    { name: 'Basketball', client: basketballClient },
    { name: 'Baseball', client: baseballClient },
    { name: 'Hockey', client: hockeyClient },
    { name: 'Volleyball', client: volleyballClient },
    { name: 'Handball', client: handballClient },
    { name: 'Rugby', client: rugbyClient },
    { name: 'Formula 1', client: formula1Client },
    { name: 'MMA', client: mmaClient },
    { name: 'NFL', client: nflClient },
    { name: 'NBA', client: nbaClient },
    { name: 'AFL', client: aflClient }
  ]
  
  const results = []
  
  for (const { name, client } of clients) {
    console.log(`📡 Testando API ${name}...`)
    
    try {
      const startTime = Date.now()
      const events = await client.getLiveEvents()
      const responseTime = Date.now() - startTime
      
      results.push({
        api: name,
        status: '✅ OK',
        eventsCount: events.length,
        responseTime: `${responseTime}ms`
      })
      
      console.log(`✅ ${name}: ${events.length} eventos encontrados (${responseTime}ms)`)
    } catch (error) {
      results.push({
        api: name,
        status: '❌ ERRO',
        error: error.message
      })
      
      console.error(`❌ ${name}: ${error.message}`)
    }
  }
  
  // Resumo
  console.log('\n📊 RESUMO DO TESTE:')
  console.table(results)
  
  const successCount = results.filter(r => r.status === '✅ OK').length
  console.log(`\n✨ ${successCount}/13 APIs funcionando corretamente`)
  
  if (successCount < 13) {
    console.log('\n⚠️ ATENÇÃO: Nem todas as APIs estão funcionando!')
    console.log('Verifique se todas as APIs estão ativadas no seu dashboard da API Sports')
  }
  
  return results
}

// Expor globalmente para testes
if (typeof window !== 'undefined') {
  (window as any).testAllAPIs = testAllAPIs
}