/**
 * Utilitário para limpar o cache de quotas das APIs de esportes
 * Útil quando novas APIs são adicionadas ao sistema
 */

export function clearSportsAPICache() {
  if (typeof window !== 'undefined') {
    // Limpar cache de quotas
    localStorage.removeItem('sports-api-quotas')
    
    // Limpar qualquer outro cache relacionado
    const keysToRemove = []
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (key && (key.includes('sports') || key.includes('api-'))) {
        keysToRemove.push(key)
      }
    }
    
    keysToRemove.forEach(key => localStorage.removeItem(key))
    
    console.log('✅ Cache de APIs de esportes limpo com sucesso!')
    console.log('🔄 Recarregue a página para inicializar as novas APIs')
    
    return true
  }
  
  return false
}

// Expor globalmente para debug
if (typeof window !== 'undefined') {
  (window as any).clearSportsAPICache = clearSportsAPICache
}