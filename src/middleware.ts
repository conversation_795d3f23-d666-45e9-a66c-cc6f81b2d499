import { NextRequest, NextResponse } from 'next/server'
import { 
  applySecurityHeaders, 
  isProtectedRoute, 
  isAuthRoute
} from './middleware/security'

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl
  
  console.log(`[Middleware] ${request.method} ${pathname}`)
  
  // Check admin routes first
  if (pathname.startsWith('/admin')) {
    // Allow admin-login page
    if (pathname === '/admin-login') {
      return NextResponse.next()
    }

    // Check for admin cookie
    const adminAuth = request.cookies.get('admin-auth')
    
    if (!adminAuth || adminAuth.value !== 'true') {
      // Redirect to admin login
      console.log(`[Middleware] Admin não autenticado - redirecionando para /admin-login`)
      return NextResponse.redirect(new URL('/admin-login', request.url))
    }
    
    console.log(`[Middleware] Admin autenticado - permitindo acesso`)
  }
  
  // Aplicar headers de segurança em todas as respostas
  let response = NextResponse.next()
  response = applySecurityHeaders(response)
  
  // Rotas públicas que não requerem autenticação
  const publicRoutes = [
    '/',
    '/mobile-app',
    '/watch',
    '/api/public',
    '/api/sports',
    '/api/iptv-org',
    '/api/iptv-simple',
    '/api/admin-auth',
    '/auth/callback',
    '/auth/verify',
    '/handler',
    '/test-route',
    '/admin-login',
    '/subscription',
    '/api/admin-config',
    '/api/track-download',
    '/premio',
    '/api/cupons',
  ]
  
  // Verificar se é rota pública
  const isPublicRoute = publicRoutes.some(route => 
    pathname === route || pathname.startsWith(route + '/')
  )
  
  if (isPublicRoute) {
    console.log(`[Middleware] Permitindo acesso à rota pública: ${pathname}`)
    return response
  }
  
  // Para rotas protegidas - verificar autenticação
  if (isProtectedRoute(pathname)) {
    console.log(`[Middleware] Rota protegida detectada: ${pathname}`)
    
    // Verificar cookies de autenticação Stack Auth
    const allCookies = request.cookies.getAll();
    const stackAuthCookie = allCookies.find(cookie => 
      cookie.name.includes('stack-') || 
      cookie.name.includes('auth-session') ||
      cookie.name.includes('access-token')
    );
    
    if (!stackAuthCookie) {
      console.log(`[Middleware] Nenhuma autenticação encontrada - redirecionando para login`)
      const redirectUrl = new URL('/login', request.url);
      redirectUrl.searchParams.set('redirect', pathname);
      return NextResponse.redirect(redirectUrl);
    }
    
    console.log(`[Middleware] Autenticação encontrada - permitindo acesso`)
  }
  
  // Redirecionar usuários autenticados de páginas de auth
  if (isAuthRoute(pathname)) {
    console.log(`[Middleware] Rota de autenticação: ${pathname}`)
    // TODO: Implementar verificação de autenticação
  }
  
  return response
}

export const config = {
  matcher: [
    '/((?!_next/static|_next/image|favicon.ico|public).*)',
  ],
}