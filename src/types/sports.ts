// API Sports Types
export interface APIFootballResponse<T> {
  get: string
  parameters: Record<string, unknown>
  errors: unknown[]
  results: number
  paging?: {
    current: number
    total: number
  }
  response: T
}

export interface League {
  id: number
  name: string
  type: string
  logo: string
  country?: {
    name: string
    code: string
    flag: string
  }
}

export interface Team {
  id: number
  name: string
  code: string
  logo: string
  founded?: number
  venue?: {
    id: number
    name: string
    city: string
    capacity: number
  }
}

export interface Fixture {
  fixture: {
    id: number
    referee: string | null
    timezone: string
    date: string
    timestamp: number
    venue: {
      id: number | null
      name: string | null
      city: string | null
    }
    status: {
      long: string
      short: string
      elapsed: number | null
    }
  }
  league: League
  teams: {
    home: Team & { winner: boolean | null }
    away: Team & { winner: boolean | null }
  }
  goals: {
    home: number | null
    away: number | null
  }
  score: {
    halftime: {
      home: number | null
      away: number | null
    }
    fulltime: {
      home: number | null
      away: number | null
    }
    extratime: {
      home: number | null
      away: number | null
    }
    penalty: {
      home: number | null
      away: number | null
    }
  }
}

export interface Standing {
  rank: number
  team: Team
  points: number
  goalsDiff: number
  group: string
  form: string
  status: string
  description: string | null
  all: StandingStats
  home: StandingStats
  away: StandingStats
  update: string
}

export interface StandingStats {
  played: number
  win: number
  draw: number
  lose: number
  goals: {
    for: number
    against: number
  }
}

// All Sport DB Types
export interface AllSportEvent {
  id: number | string
  name: string
  date?: string
  dateFrom: string
  dateTo: string
  sportId?: number
  sport: string
  emoji?: string
  competitionId?: number
  competition?: string
  continentId?: number
  continent?: string
  country?: string
  url?: string
  webUrl?: string
  wikiUrl?: string
  ticketsUrl?: string
  facebookUrl?: string
  twitterUrl?: string
  liveUrl?: string
  liveUrlPaid?: boolean
  logoUrl?: string
  logoThumbnailUrl?: string
  logoSmallUrl?: string
  dateModified?: string
  location?: AllSportLocation[] | string
  score?: string
  status?: string
  homeLogo?: string
  awayLogo?: string
  leagueLogo?: string
}

export interface AllSportLocation {
  id: number
  regionId: number | null
  name: string
  continent: string
  continentId: number
  code: string
  emoji: string
  flagUrl: string
  locations?: {
    id: number
    name: string
    lat: number
    lng: number
  }[]
}

export interface AllSportSport {
  id: number
  name: string
  season: 'Summer' | 'Winter' | 'Other'
  emoji: string
  url: string
}

export interface AllSportCompetition {
  id: number
  name: string
  ageGroup: string
  gender: string
  emoji: string
  continentId: number
  continent: string
  sportId: number
  sport: string
  url: string
  logoUrl?: string
  logoSmallUrl?: string
  logoThumbnailUrl?: string
  dateModified: string
}

// EPG Types
export interface EPGProgram {
  start: string
  title: string
  description?: string
  category?: string
  icon?: string
}

export interface EPGChannel {
  id: string
  name: string
  logo?: string
  url?: string
  programs: EPGProgram[]
}

// Sports Store Types
export interface SportsState {
  // Live fixtures
  liveFixtures: Fixture[]
  loadingLiveFixtures: boolean
  
  // Upcoming events
  upcomingEvents: AllSportEvent[]
  loadingUpcomingEvents: boolean
  
  // EPG data
  epgChannels: Map<string, EPGProgram[]>
  loadingEPG: boolean
  
  // User preferences
  favoriteTeams: number[]
  favoriteLeagues: number[]
  favoriteSports: string[]
  
  // Actions
  fetchLiveFixtures: (sport?: string) => Promise<void>
  fetchUpcomingEvents: (params?: {
    sport?: string
    dateFrom?: string
    dateTo?: string
  }) => Promise<void>
  fetchEPGData: (channelId: string) => Promise<void>
  toggleFavoriteTeam: (teamId: number) => void
  toggleFavoriteLeague: (leagueId: number) => void
  toggleFavoriteSport: (sport: string) => void
}

// Sports categories for filtering
export const SPORTS_CATEGORIES = [
  { id: 'football', name: 'Fútbol', icon: '⚽', apiName: 'football' },
  { id: 'basketball', name: 'Baloncesto', icon: '🏀', apiName: 'basketball' },
  { id: 'formula1', name: 'Fórmula 1', icon: '🏎️', apiName: 'formula-1' },
  { id: 'motogp', name: 'MotoGP', icon: '🏍️', apiName: 'motogp' },
  { id: 'tennis', name: 'Tenis', icon: '🎾', apiName: 'tennis' },
  { id: 'nfl', name: 'NFL', icon: '🏈', apiName: 'nfl' },
  { id: 'nba', name: 'NBA', icon: '🏀', apiName: 'nba' },
  { id: 'mma', name: 'MMA', icon: '🥊', apiName: 'mma' },
  { id: 'rugby', name: 'Rugby', icon: '🏉', apiName: 'rugby' },
  { id: 'baseball', name: 'Béisbol', icon: '⚾', apiName: 'baseball' },
  { id: 'hockey', name: 'Hockey', icon: '🏒', apiName: 'hockey' },
  { id: 'volleyball', name: 'Voleibol', icon: '🏐', apiName: 'volleyball' }
] as const

export type SportCategory = typeof SPORTS_CATEGORIES[number]['id']