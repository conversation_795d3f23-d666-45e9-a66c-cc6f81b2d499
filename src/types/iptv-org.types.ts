// Tipos para a API IPTV-ORG

export interface IPTVStream {
  channel: string
  url: string
  // Campo http_referrer pode ter diferentes formatos
  http_referrer?: string | null
  referrer?: string | null
  user_agent?: string | null
  // Propriedades que começam com _
  _id?: string
}

export interface IPTVChannel {
  id: string
  name: string
  alt_names?: string[]
  network?: string | null
  owners?: string[]
  country: string
  subdivision?: string | null
  city?: string | null
  broadcast_area?: string[]
  languages?: string[]
  categories?: string[]
  is_nsfw?: boolean
  launched?: string | null
  closed?: string | null
  replaced_by?: string | null
  website?: string | null
  logo?: string
}

export interface IPTVGuide {
  channel: string
  site: string
  lang: string
  days: number
  url: string
}

// Tipo combinado para stream com metadados do canal
export interface EnrichedIPTVStream extends IPTVStream {
  channelData?: IPTVChannel
  quality?: string
  isLive?: boolean
}

// Filtros para buscar canais
export interface IPTVFilters {
  category?: string
  country?: string
  language?: string
  search?: string
  quality?: string
}