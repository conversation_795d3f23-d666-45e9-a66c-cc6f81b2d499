'use client'

import { useState } from 'react'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2, CheckCircle2, AlertCircle, Search } from 'lucide-react'
import { AdminGuard } from '@/components/auth/admin-guard'
import { toast } from 'sonner'

export default function StripeFixPage() {
  const [email, setEmail] = useState('')
  const [sessionId, setSessionId] = useState('')
  const [loading, setLoading] = useState(false)
  const [result, setResult] = useState<any>(null)
  const [error, setError] = useState<string | null>(null)

  async function handleFixByEmail() {
    if (!email) {
      toast.error('Por favor, insira um email')
      return
    }

    setLoading(true)
    setError(null)
    setResult(null)

    try {
      const response = await fetch('/api/stripe/fix-subscription-access', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email })
      })

      const data = await response.json()
      
      if (response.ok && data.success) {
        setResult(data)
        toast.success('Acesso premium restaurado com sucesso!')
      } else {
        setError(data.error || 'Erro ao processar')
        toast.error(data.error || 'Erro ao processar')
      }
    } catch (err) {
      const message = err instanceof Error ? err.message : 'Erro desconhecido'
      setError(message)
      toast.error(message)
    } finally {
      setLoading(false)
    }
  }

  async function handleProcessSession() {
    if (!sessionId) {
      toast.error('Por favor, insira um Session ID')
      return
    }

    setLoading(true)
    setError(null)
    setResult(null)

    try {
      const response = await fetch('/api/stripe/process-checkout-session', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ sessionId })
      })

      const data = await response.json()
      
      if (response.ok && data.success) {
        setResult(data)
        toast.success('Sessão processada com sucesso!')
      } else {
        setError(data.error || 'Erro ao processar')
        toast.error(data.error || 'Erro ao processar')
      }
    } catch (err) {
      const message = err instanceof Error ? err.message : 'Erro desconhecido'
      setError(message)
      toast.error(message)
    } finally {
      setLoading(false)
    }
  }

  return (
    <AdminGuard>
      <div className="min-h-screen bg-gradient-to-b from-gray-950 to-black py-12">
        <div className="max-w-4xl mx-auto px-4">
          <h1 className="text-3xl font-bold text-white mb-8">
            Correção Manual de Assinaturas Stripe
          </h1>

          <div className="grid gap-6">
            {/* Correção por Email */}
            <Card className="bg-gray-900 border-gray-800">
              <div className="p-6">
                <h2 className="text-xl font-semibold text-white mb-4">
                  1. Buscar e Corrigir por Email
                </h2>
                <p className="text-gray-400 text-sm mb-4">
                  Busca o cliente no Stripe e atualiza o banco de dados com a assinatura ativa
                </p>
                
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="email">Email do Cliente</Label>
                    <Input
                      id="email"
                      type="email"
                      placeholder="<EMAIL>"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      className="bg-gray-800 border-gray-700 text-white"
                    />
                  </div>
                  
                  <Button
                    onClick={handleFixByEmail}
                    disabled={loading || !email}
                    className="bg-blue-600 hover:bg-blue-700"
                  >
                    {loading ? (
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    ) : (
                      <Search className="mr-2 h-4 w-4" />
                    )}
                    Buscar e Corrigir
                  </Button>
                </div>
              </div>
            </Card>

            {/* Processar Session ID */}
            <Card className="bg-gray-900 border-gray-800">
              <div className="p-6">
                <h2 className="text-xl font-semibold text-white mb-4">
                  2. Processar Session ID do Stripe
                </h2>
                <p className="text-gray-400 text-sm mb-4">
                  Processa manualmente uma sessão de checkout do Stripe
                </p>
                
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="sessionId">Session ID do Stripe</Label>
                    <Input
                      id="sessionId"
                      type="text"
                      placeholder="cs_live_..."
                      value={sessionId}
                      onChange={(e) => setSessionId(e.target.value)}
                      className="bg-gray-800 border-gray-700 text-white font-mono text-sm"
                    />
                  </div>
                  
                  <Button
                    onClick={handleProcessSession}
                    disabled={loading || !sessionId}
                    className="bg-green-600 hover:bg-green-700"
                  >
                    {loading ? (
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    ) : (
                      <CheckCircle2 className="mr-2 h-4 w-4" />
                    )}
                    Processar Sessão
                  </Button>
                </div>
              </div>
            </Card>

            {/* Resultado */}
            {result && (
              <Alert className="bg-green-900/20 border-green-900">
                <CheckCircle2 className="h-4 w-4 text-green-500" />
                <AlertDescription>
                  <div className="space-y-2">
                    <p className="font-semibold text-green-400">Sucesso!</p>
                    <div className="text-sm text-gray-300 space-y-1">
                      <p>Usuário: {result.user?.email}</p>
                      <p>Tipo: {result.user?.type || result.user?.tableName}</p>
                      <p>Tier: {result.subscription?.tier || result.after?.subscription_tier}</p>
                      <p>Status: {result.subscription?.status || result.after?.subscription_status}</p>
                      <p>Expira em: {new Date(result.subscription?.expiresAt || result.after?.subscription_expires_at).toLocaleDateString('pt-BR')}</p>
                    </div>
                  </div>
                </AlertDescription>
              </Alert>
            )}

            {/* Erro */}
            {error && (
              <Alert className="bg-red-900/20 border-red-900">
                <AlertCircle className="h-4 w-4 text-red-500" />
                <AlertDescription>
                  <div className="space-y-2">
                    <p className="font-semibold text-red-400">Erro</p>
                    <p className="text-sm text-gray-300">{error}</p>
                  </div>
                </AlertDescription>
              </Alert>
            )}

            {/* Instruções */}
            <Card className="bg-gray-900/50 border-gray-800">
              <div className="p-6">
                <h3 className="text-lg font-semibold text-white mb-3">Instruções</h3>
                <div className="space-y-3 text-sm text-gray-400">
                  <div>
                    <p className="font-medium text-gray-300 mb-1">Para obter o Session ID:</p>
                    <ol className="list-decimal list-inside space-y-1 ml-2">
                      <li>Acesse o dashboard do Stripe</li>
                      <li>Vá em Pagamentos → Todos os pagamentos</li>
                      <li>Clique no pagamento do cliente</li>
                      <li>Copie o "Checkout Session ID"</li>
                    </ol>
                  </div>
                  
                  <div>
                    <p className="font-medium text-gray-300 mb-1">Problemas comuns:</p>
                    <ul className="list-disc list-inside space-y-1 ml-2">
                      <li>Cliente não encontrado: O email precisa ser exatamente o mesmo usado no Stripe</li>
                      <li>Usuário não encontrado: O cliente precisa ter feito login pelo menos uma vez</li>
                      <li>Webhook não disparou: Use o Session ID para processar manualmente</li>
                    </ul>
                  </div>
                </div>
              </div>
            </Card>
          </div>
        </div>
      </div>
    </AdminGuard>
  )
}