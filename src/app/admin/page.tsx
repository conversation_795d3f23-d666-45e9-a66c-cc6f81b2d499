'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Loader2, Users, Activity, LogOut, Smartphone, Save, Upload, Video, X, Shield, QrCode, Eye, CreditCard } from 'lucide-react'
import { createClient } from '@/lib/supabase/client'
import { toast } from 'sonner'
import QRCode from 'qrcode'
import { ButtonFileUpload } from '@/components/admin/button-file-upload'

interface MobileAppConfig {
  android_url: string
  ios_url: string
  qr_code_url: string
  tutorial_video_url: string
  tutorial_video_file: string | null
  android_badge_url: string
  ios_badge_url: string
  ios_available: boolean
  android_available: boolean
}

export default function AdminPage() {
  const router = useRouter()
  const [loading, setLoading] = useState(true)
  const [mobileConfig, setMobileConfig] = useState<MobileAppConfig | null>(null)
  const [savingConfig, setSavingConfig] = useState(false)
  const [uploadingVideo, setUploadingVideo] = useState(false)
  
  useEffect(() => {
    checkAuth()
    loadMobileConfig()
  }, [])
  
  const checkAuth = async () => {
    try {
      // Verificar cookie de admin
      const response = await fetch('/api/admin-auth')
      const data = await response.json()
      
      if (!data.isAuthenticated) {
        router.push('/admin-login')
        return
      }
    } catch (error) {
      console.error('Erro ao verificar auth:', error)
      router.push('/admin-login')
    }
    setLoading(false)
  }
  
  const loadMobileConfig = async () => {
    try {
      const response = await fetch('/api/admin/mobile-config')
      if (response.ok) {
        const data = await response.json()
        setMobileConfig(data)
      }
    } catch (error) {
      console.error('Error loading mobile config:', error)
    }
  }
  
  const handleSaveMobileConfig = async () => {
    if (!mobileConfig) return
    
    setSavingConfig(true)
    try {
      const response = await fetch('/api/admin/mobile-config', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(mobileConfig),
      })
      
      if (response.ok) {
        toast.success('Configuración guardada')
      } else {
        throw new Error('Error al guardar')
      }
    } catch (error) {
      console.error('Error saving mobile config:', error)
      toast.error('Error al guardar configuración')
    } finally {
      setSavingConfig(false)
    }
  }
  
  const handleLogout = async () => {
    try {
      await fetch('/api/admin-auth', { method: 'DELETE' })
      router.push('/admin-login')
    } catch (error) {
      console.error('Logout error:', error)
    }
  }
  
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    )
  }
  
  return (
    <div className="min-h-screen bg-background">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center gap-4">
            <div className="p-2 bg-primary/10 rounded-lg">
              <Shield className="h-6 w-6 text-primary" />
            </div>
            <div>
              <h1 className="text-3xl font-bold">Panel Administrativo</h1>
              <p className="text-muted-foreground">
                Gestión de la plataforma
              </p>
            </div>
          </div>
          <Button
            onClick={handleLogout}
            variant="outline"
            size="sm"
          >
            <LogOut className="h-4 w-4 mr-2" />
            Cerrar Sesión
          </Button>
        </div>
        
        {/* Quick Actions Grid */}
        <div className="grid gap-6 mb-8 md:grid-cols-3">
          {/* Stripe Fix Card */}
          <Card className="border-gray-800 hover:border-gray-700 transition-colors">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg">
                <CreditCard className="h-5 w-5 text-green-500" />
                Correção de Pagamentos
              </CardTitle>
              <CardDescription>
                Corrigir assinaturas do Stripe
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button
                className="w-full"
                variant="outline"
                onClick={() => router.push('/admin/stripe-fix')}
              >
                Abrir Ferramenta
              </Button>
            </CardContent>
          </Card>
        </div>
        
        {/* Mobile App Configuration */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Smartphone className="h-5 w-5" />
              Configuración de App Móvil
            </CardTitle>
            <CardDescription>
              Configura los enlaces y contenido de la aplicación móvil
            </CardDescription>
          </CardHeader>
          <CardContent>
            {mobileConfig && (
              <div className="space-y-6">
                {/* Android Configuration */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">Android</h3>
                  
                  <div className="grid gap-4">
                    <div>
                      <Label htmlFor="android-url">URL de Play Store</Label>
                      <Input
                        id="android-url"
                        value={mobileConfig.android_url}
                        onChange={(e) => setMobileConfig({
                          ...mobileConfig,
                          android_url: e.target.value
                        })}
                        placeholder="https://play.google.com/store/apps/details?id=..."
                      />
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <Switch
                        id="android-available"
                        checked={mobileConfig.android_available}
                        onCheckedChange={(checked) => setMobileConfig({
                          ...mobileConfig,
                          android_available: checked
                        })}
                      />
                      <Label htmlFor="android-available">
                        App Android disponible
                      </Label>
                    </div>
                  </div>
                </div>
                
                {/* iOS Configuration */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">iOS</h3>
                  
                  <div className="grid gap-4">
                    <div>
                      <Label htmlFor="ios-url">URL de App Store</Label>
                      <Input
                        id="ios-url"
                        value={mobileConfig.ios_url}
                        onChange={(e) => setMobileConfig({
                          ...mobileConfig,
                          ios_url: e.target.value
                        })}
                        placeholder="https://apps.apple.com/app/..."
                      />
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <Switch
                        id="ios-available"
                        checked={mobileConfig.ios_available}
                        onCheckedChange={(checked) => setMobileConfig({
                          ...mobileConfig,
                          ios_available: checked
                        })}
                      />
                      <Label htmlFor="ios-available">
                        App iOS disponible
                      </Label>
                    </div>
                  </div>
                </div>
                
                {/* QR Code Configuration */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">QR Code</h3>
                  
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="qr-url">URL del QR Code</Label>
                      <Input
                        id="qr-url"
                        value={mobileConfig.qr_code_url}
                        onChange={(e) => setMobileConfig({
                          ...mobileConfig,
                          qr_code_url: e.target.value
                        })}
                        placeholder="URL a la que apuntará el código QR"
                      />
                      <p className="text-sm text-muted-foreground mt-1">
                        Esta URL se mostrará cuando los usuarios escaneen el código QR
                      </p>
                    </div>
                    
                    {/* Mostrar QR Code generado */}
                    {mobileConfig.qr_code_url && (
                      <div className="bg-slate-800 p-6 rounded-lg flex flex-col items-center space-y-4">
                        <div className="bg-white p-4 rounded-lg">
                          <canvas
                            id="qr-canvas"
                            ref={(canvas) => {
                              if (canvas && mobileConfig.qr_code_url) {
                                QRCode.toCanvas(canvas, mobileConfig.qr_code_url, {
                                  width: 200,
                                  margin: 2,
                                  color: {
                                    dark: '#000000',
                                    light: '#FFFFFF'
                                  }
                                }, (error) => {
                                  if (error) console.error('Error generating QR:', error)
                                })
                              }
                            }}
                          />
                        </div>
                        <div className="flex items-center gap-2 text-sm text-muted-foreground">
                          <QrCode className="h-4 w-4" />
                          <span>QR Code generado automáticamente</span>
                        </div>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => {
                            const canvas = document.getElementById('qr-canvas') as HTMLCanvasElement
                            if (canvas) {
                              const url = canvas.toDataURL('image/png')
                              const a = document.createElement('a')
                              a.href = url
                              a.download = 'qrcode-streamplus.png'
                              a.click()
                            }
                          }}
                        >
                          <Save className="h-4 w-4 mr-2" />
                          Descargar QR Code
                        </Button>
                      </div>
                    )}
                  </div>
                </div>
                
                {/* Tutorial Video Configuration */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">Video Tutorial</h3>
                  
                  {/* Mostrar video actual si existe */}
                  {mobileConfig.tutorial_video_file && (
                    <div className="bg-slate-800 p-4 rounded-lg space-y-2">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <Video className="h-5 w-5 text-green-500" />
                          <span className="text-sm">Video subido</span>
                        </div>
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={async () => {
                            if (confirm('¿Estás seguro de eliminar el video?')) {
                              try {
                                const response = await fetch('/api/admin/upload-video', {
                                  method: 'DELETE',
                                })
                                if (response.ok) {
                                  setMobileConfig({
                                    ...mobileConfig,
                                    tutorial_video_file: null
                                  })
                                  toast.success('Video eliminado')
                                } else {
                                  throw new Error('Error al eliminar')
                                }
                              } catch (error) {
                                toast.error('Error al eliminar el video')
                              }
                            }
                          }}
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </div>
                      <a 
                        href={mobileConfig.tutorial_video_file} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="text-sm text-blue-400 hover:underline"
                      >
                        Ver video actual
                      </a>
                    </div>
                  )}
                  
                  {/* Upload de nuevo video */}
                  <div>
                    <Label>Subir Video Tutorial</Label>
                    <div className="mt-2">
                      <ButtonFileUpload
                        uploading={uploadingVideo}
                        onFileSelect={async (file) => {
                          if (file.size > 104857600) {
                            toast.error('El archivo es muy grande. Máximo 100MB.')
                            return
                          }
                          
                          setUploadingVideo(true)
                          const formData = new FormData()
                          formData.append('file', file)
                          
                          try {
                            const response = await fetch('/api/admin/upload-video', {
                              method: 'POST',
                              body: formData,
                            })
                            
                            if (response.ok) {
                              const data = await response.json()
                              setMobileConfig({
                                ...mobileConfig,
                                tutorial_video_file: data.url,
                                tutorial_video_url: null // Limpiar URL externa
                              })
                              toast.success('Video subido correctamente')
                              // Recargar configuración para mostrar el vídeo
                              await loadMobileConfig()
                            } else {
                              const error = await response.json()
                              throw new Error(error.error || 'Error al subir')
                            }
                          } catch (error: any) {
                            console.error('Error uploading video:', error)
                            toast.error(error.message || 'Error al subir el video')
                          } finally {
                            setUploadingVideo(false)
                          }
                        }}
                      />
                    </div>
                    <p className="text-sm text-muted-foreground mt-2">
                      Formatos: MP4, WebM, OGG, MOV • Máximo: 100MB
                    </p>
                  </div>
                  
                  {/* URL externa (alternativa) */}
                  <div className="relative">
                    <div className="absolute inset-0 flex items-center">
                      <span className="w-full border-t" />
                    </div>
                    <div className="relative flex justify-center text-xs uppercase">
                      <span className="bg-background px-2 text-muted-foreground">O usar URL externa</span>
                    </div>
                  </div>
                  
                  <div>
                    <Label htmlFor="tutorial-url">URL del Video (YouTube, etc)</Label>
                    <Input
                      id="tutorial-url"
                      value={mobileConfig.tutorial_video_url || ''}
                      onChange={(e) => setMobileConfig({
                        ...mobileConfig,
                        tutorial_video_url: e.target.value,
                        tutorial_video_file: null // Limpiar archivo si usa URL
                      })}
                      placeholder="https://youtube.com/watch?v=..."
                      disabled={!!mobileConfig.tutorial_video_file}
                    />
                    {mobileConfig.tutorial_video_file && (
                      <p className="text-sm text-yellow-500 mt-1">
                        Elimina el video subido para usar una URL externa
                      </p>
                    )}
                  </div>
                </div>
                
                {/* Save Button */}
                <Button
                  onClick={handleSaveMobileConfig}
                  disabled={savingConfig}
                  className="w-full"
                >
                  {savingConfig ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Guardando...
                    </>
                  ) : (
                    <>
                      <Save className="mr-2 h-4 w-4" />
                      Guardar Configuración
                    </>
                  )}
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}