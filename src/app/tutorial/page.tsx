'use client'

import { useEffect, useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { ArrowLeft, Download, Smartphone } from 'lucide-react'
import Link from 'next/link'

interface MobileAppConfig {
  tutorial_video_url: string | null
  tutorial_video_file: string | null
  android_url: string
}

export default function TutorialPage() {
  const [config, setConfig] = useState<MobileAppConfig | null>(null)
  const [loading, setLoading] = useState(true)
  
  useEffect(() => {
    loadMobileConfig()
  }, [])
  
  const loadMobileConfig = async () => {
    try {
      const response = await fetch('/api/admin/mobile-config')
      if (response.ok) {
        const data = await response.json()
        setConfig(data)
      }
    } catch (error) {
      console.error('Error loading mobile config:', error)
    } finally {
      setLoading(false)
    }
  }
  
  const videoUrl = config?.tutorial_video_file || config?.tutorial_video_url
  
  return (
    <div className="min-h-screen bg-gradient-to-b from-slate-950 to-slate-900">
      {/* Header */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Link href="/mobile-app">
          <Button variant="outline" className="gap-2">
            <ArrowLeft className="h-4 w-4" />
            Volver a Mobile App
          </Button>
        </Link>
      </div>
      
      {/* Content */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 pb-20">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-white mb-4">
            Tutorial de Instalación
          </h1>
          <p className="text-xl text-gray-300">
            Sigue estos pasos para instalar y configurar la app
          </p>
        </div>
        
        {loading ? (
          <div className="flex justify-center py-20">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-500"></div>
          </div>
        ) : videoUrl ? (
          <>
            {/* Video Player */}
            <Card className="bg-slate-800/50 border-slate-700 mb-8">
              <CardContent className="p-0">
                <div className="aspect-video">
                  {videoUrl.includes('youtube.com') || videoUrl.includes('youtu.be') ? (
                    <iframe
                      src={videoUrl.replace('watch?v=', 'embed/').replace('youtu.be/', 'youtube.com/embed/')}
                      className="w-full h-full rounded-lg"
                      allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                      allowFullScreen
                    />
                  ) : (
                    <video 
                      controls 
                      className="w-full h-full rounded-lg"
                      src={videoUrl}
                    >
                      Tu navegador no soporta la reproducción de video.
                    </video>
                  )}
                </div>
              </CardContent>
            </Card>
            
            {/* Steps */}
            <div className="space-y-6">
              <Card className="bg-slate-800/50 border-slate-700">
                <CardContent className="p-6">
                  <h2 className="text-2xl font-bold text-white mb-4">
                    Pasos para instalar:
                  </h2>
                  <ol className="space-y-4 text-gray-300">
                    <li className="flex gap-4">
                      <span className="flex-shrink-0 w-8 h-8 bg-purple-600 text-white rounded-full flex items-center justify-center font-bold">
                        1
                      </span>
                      <div>
                        <p className="font-semibold text-white">Descarga la app</p>
                        <p>Haz clic en el botón de descarga para tu dispositivo</p>
                      </div>
                    </li>
                    <li className="flex gap-4">
                      <span className="flex-shrink-0 w-8 h-8 bg-purple-600 text-white rounded-full flex items-center justify-center font-bold">
                        2
                      </span>
                      <div>
                        <p className="font-semibold text-white">Instala la aplicación</p>
                        <p>Sigue las instrucciones de tu dispositivo para instalar</p>
                      </div>
                    </li>
                    <li className="flex gap-4">
                      <span className="flex-shrink-0 w-8 h-8 bg-purple-600 text-white rounded-full flex items-center justify-center font-bold">
                        3
                      </span>
                      <div>
                        <p className="font-semibold text-white">Abre la app y verifica</p>
                        <p>Al abrir por primera vez, la app se verificará automáticamente</p>
                      </div>
                    </li>
                    <li className="flex gap-4">
                      <span className="flex-shrink-0 w-8 h-8 bg-purple-600 text-white rounded-full flex items-center justify-center font-bold">
                        4
                      </span>
                      <div>
                        <p className="font-semibold text-white">¡Disfruta 30 días gratis!</p>
                        <p>Tu suscripción gratuita se activará automáticamente</p>
                      </div>
                    </li>
                  </ol>
                </CardContent>
              </Card>
              
              {/* Download CTA */}
              <Card className="bg-gradient-to-r from-purple-900/50 to-pink-900/50 border-purple-500/50">
                <CardContent className="p-6 text-center">
                  <Smartphone className="h-12 w-12 text-white mx-auto mb-4" />
                  <h3 className="text-xl font-bold text-white mb-2">
                    ¿Listo para comenzar?
                  </h3>
                  <p className="text-gray-300 mb-6">
                    Descarga la app ahora y obtén tu mes gratis
                  </p>
                  <a 
                    href={config?.android_url || '#'}
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    <Button className="bg-green-600 hover:bg-green-700 gap-2">
                      <Download className="h-4 w-4" />
                      Descargar para Android
                    </Button>
                  </a>
                </CardContent>
              </Card>
            </div>
          </>
        ) : (
          <Card className="bg-slate-800/50 border-slate-700">
            <CardContent className="p-20 text-center">
              <p className="text-gray-400 text-lg">
                No hay video tutorial configurado.
              </p>
              <Link href="/mobile-app" className="mt-4 inline-block">
                <Button variant="outline">
                  Volver
                </Button>
              </Link>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}