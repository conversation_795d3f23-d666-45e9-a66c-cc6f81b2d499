'use client'

import { VideoPlayer } from '@/components/player/video-player'

export default function TestPlayerPage() {
  return (
    <div className="min-h-screen bg-gray-900 p-8">
      <h1 className="text-3xl font-bold text-white mb-8">Test Player - Trial System</h1>
      
      <div className="max-w-4xl mx-auto">
        <VideoPlayer
          url="https://test-streams.mux.dev/x36xhzz/x36xhzz.m3u8"
          channelName="Test Channel"
          fallbackUrls={[
            "https://moctobpltc-i.akamaihd.net/hls/live/571329/eight/playlist.m3u8",
            "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4"
          ]}
        />
        
        <div className="mt-8 p-4 bg-gray-800 rounded-lg text-white">
          <h2 className="text-xl font-semibold mb-4">Instruções de Teste</h2>
          <ol className="list-decimal list-inside space-y-2">
            <li>Abra o console do navegador (F12)</li>
            <li>O trial deve iniciar automaticamente ao carregar o player</li>
            <li>Timer deve aparecer contando de 5:00</li>
            <li>Após 5 minutos, overlay de bloqueio deve aparecer</li>
          </ol>
        </div>
      </div>
    </div>
  )
}