'use client'

import { useState, useEffect } from 'react'
import { HLSPlayer } from '@/components/player/hls-player'
import { Loader2, AlertCircle, RefreshCw } from 'lucide-react'
import { Button } from '@/components/ui/button'

interface Stream {
  name: string
  url: string
  country?: string
  logo?: string
}

export default function HLSTestPage() {
  const [streams, setStreams] = useState<Stream[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [useReliableApi, setUseReliableApi] = useState(false)
  
  // Streams de teste mock como fallback
  const mockStreams: Stream[] = [
    {
      name: 'Mux Test Stream',
      url: 'https://test-streams.mux.dev/x36xhzz/x36xhzz.m3u8'
    },
    {
      name: 'Tears of Steel',
      url: 'https://demo.unified-streaming.com/k8s/features/stable/video/tears-of-steel/tears-of-steel.ism/.m3u8'
    },
    {
      name: 'Sintel',
      url: 'https://bitdash-a.akamaihd.net/content/sintel/hls/playlist.m3u8'
    }
  ]
  
  // Carregar streams reais da IPTV
  const loadRealStreams = async () => {
    console.log('🔄 [HLS Test] Carregando streams reais...')
    setLoading(true)
    setError(null)
    
    try {
      const apiUrl = useReliableApi ? '/api/iptv-working' : '/api/iptv-simple'
      console.log('📡 [HLS Test] Usando API:', apiUrl)
      
      const response = await fetch(apiUrl)
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      
      const data = await response.json()
      console.log('📺 [HLS Test] Resposta da API:', data)
      
      if (data.success && data.data && data.data.length > 0) {
        // Pegar os primeiros 5 canais com URLs válidas
        const realStreams = data.data
          .filter((stream: any) => stream.url && stream.url.includes('.m3u8'))
          .slice(0, 5)
          .map((stream: any) => ({
            name: stream.name || stream.channel,
            url: stream.url,
            country: stream.country,
            logo: stream.logo
          }))
        
        console.log('✅ [HLS Test] Streams filtrados:', realStreams.length)
        
        if (realStreams.length > 0) {
          setStreams(realStreams)
        } else {
          console.warn('⚠️ [HLS Test] Nenhum stream .m3u8 encontrado, usando mock')
          setStreams(mockStreams)
        }
      } else {
        console.warn('⚠️ [HLS Test] Resposta vazia, usando mock')
        setStreams(mockStreams)
      }
    } catch (err) {
      console.error('❌ [HLS Test] Erro ao carregar streams:', err)
      setError(err instanceof Error ? err.message : 'Erro desconhecido')
      // Usar mock streams em caso de erro
      setStreams(mockStreams)
    } finally {
      setLoading(false)
    }
  }
  
  useEffect(() => {
    loadRealStreams()
  }, [useReliableApi])
  
  return (
    <div className="container mx-auto py-8 max-w-6xl">
      <div className="flex items-center justify-between mb-8">
        <h1 className="text-3xl font-bold">HLS Player Test - Streams Reais</h1>
        <div className="flex items-center gap-4">
          <label className="flex items-center gap-2 text-sm">
            <input
              type="checkbox"
              checked={useReliableApi}
              onChange={(e) => setUseReliableApi(e.target.checked)}
              className="rounded"
            />
            <span>Usar streams mais confiáveis</span>
          </label>
          <Button 
            onClick={loadRealStreams}
            disabled={loading}
            size="sm"
            variant="outline"
          >
            <RefreshCw className={`mr-2 h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
            Recarregar
          </Button>
        </div>
      </div>
      
      {error && (
        <div className="mb-6 p-4 bg-red-50 dark:bg-red-950 rounded-lg flex items-center gap-2">
          <AlertCircle className="h-5 w-5 text-red-600" />
          <p className="text-sm text-red-600">Erro: {error} - Usando streams de teste</p>
        </div>
      )}
      
      {loading ? (
        <div className="flex items-center justify-center py-20">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
            <p className="text-gray-600">Carregando streams IPTV reais...</p>
          </div>
        </div>
      ) : (
        <div className="grid gap-8">
          {streams.map((stream, index) => (
            <div key={`${stream.url}-${index}`} className="space-y-4 p-6 bg-gray-50 dark:bg-gray-900 rounded-lg">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <h2 className="text-xl font-semibold flex items-center gap-2">
                    {stream.logo && (
                      <img src={stream.logo} alt="" className="w-8 h-8 object-contain" />
                    )}
                    {stream.name}
                    {stream.country && (
                      <span className="text-sm text-gray-500">({stream.country})</span>
                    )}
                  </h2>
                  <p className="text-sm text-gray-600 dark:text-gray-400 font-mono mt-1 break-all">
                    {stream.url}
                  </p>
                </div>
                {index < 3 && streams.length > 3 && (
                  <span className="px-2 py-1 bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 text-xs rounded">
                    Stream Real IPTV
                  </span>
                )}
              </div>
              
              <div className="bg-black rounded-lg overflow-hidden">
                <HLSPlayer 
                  url={stream.url} 
                  autoPlay={false} 
                  controls={true}
                  // Usar proxy para contornar CORS
                  useProxy={true}
                />
              </div>
            </div>
          ))}
        </div>
      )}
      
      <div className="mt-8 p-4 bg-blue-50 dark:bg-blue-950 rounded-lg space-y-2">
        <h3 className="font-semibold mb-2">Debug Info:</h3>
        <p className="text-sm">• Abra o console (F12) para ver logs detalhados do HLS.js</p>
        <p className="text-sm">• Este player usa HLS.js diretamente, sem ReactPlayer</p>
        <p className="text-sm">• Os streams são carregados da API IPTV-ORG em tempo real</p>
        <p className="text-sm">• Se um stream não funcionar, pode estar offline ou com geoblocking</p>
        <p className="text-sm font-semibold text-green-600 dark:text-green-400">
          • Total de canais disponíveis: {loading ? '...' : `${streams.length} streams`}
        </p>
      </div>
    </div>
  )
}