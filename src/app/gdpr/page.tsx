'use client'

import { Footer } from '@/components/footer'
import { BackToHome } from '@/components/back-to-home'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { ScrollArea } from '@/components/ui/scroll-area'
import { 
  Shield, Lock, Eye, Database, Download, 
  UserX, FileText, Users, Mail, Globe, Settings
} from 'lucide-react'

export default function GDPRPage() {
  const rights = [
    {
      icon: Eye,
      title: 'Derecho de Acceso',
      description: 'Tienes derecho a solicitar una copia de tus datos personales que almacenamos.',
      action: 'Solicitar Datos'
    },
    {
      icon: FileText,
      title: 'Derecho de Rectificación',
      description: 'Puedes corregir cualquier dato personal inexacto o incompleto.',
      action: 'Corregi<PERSON>'
    },
    {
      icon: UserX,
      title: 'Derecho al Olvido',
      description: 'Puedes solicitar la eliminación completa de tus datos personales.',
      action: 'Eliminar Datos'
    },
    {
      icon: Download,
      title: 'Derecho a la Portabilidad',
      description: 'Puedes recibir tus datos en un formato estructurado y legible por máquina.',
      action: 'Exportar Datos'
    },
    {
      icon: Lock,
      title: 'Derecho de Oposición',
      description: 'Puedes oponerte al procesamiento de tus datos para ciertos propósitos.',
      action: 'Gestionar Opciones'
    },
    {
      icon: Database,
      title: 'Derecho de Limitación',
      description: 'Puedes solicitar la restricción del procesamiento de tus datos.',
      action: 'Limitar Uso'
    }
  ]

  const dataCategories = [
    {
      category: 'Datos de Identificación',
      types: ['Nombre completo', 'Dirección de email', 'Número de teléfono (opcional)'],
      purpose: 'Crear y mantener tu cuenta',
      retention: 'Mientras tu cuenta esté activa + 1 año'
    },
    {
      category: 'Datos de Uso',
      types: ['Historial de visualización', 'Preferencias', 'Interacciones con el servicio'],
      purpose: 'Personalizar tu experiencia y mejorar el servicio',
      retention: '2 años desde la última interacción'
    },
    {
      category: 'Datos de Pago',
      types: ['Método de pago', 'Historial de transacciones'],
      purpose: 'Procesar pagos y cumplir obligaciones fiscales',
      retention: '5 años según normativa fiscal'
    },
    {
      category: 'Datos Técnicos',
      types: ['Dirección IP', 'Tipo de dispositivo', 'Sistema operativo', 'Navegador'],
      purpose: 'Seguridad y optimización del servicio',
      retention: '90 días'
    }
  ]

  return (
    <div className="min-h-screen bg-gradient-to-b from-slate-950 to-slate-900">
      {/* Hero Section */}
      <section className="py-16 border-b border-slate-800">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <BackToHome />
          <Badge className="mb-4 bg-green-600 text-white">RGPD / GDPR</Badge>
          <h1 className="text-4xl font-bold text-white mb-4">
            Tus Derechos bajo el RGPD
          </h1>
          <p className="text-xl text-gray-300">
            Reglamento General de Protección de Datos (UE) 2016/679
          </p>
          <p className="text-gray-400 mt-4">
            En StreamPlus España nos tomamos muy en serio la protección de tus datos personales. 
            Aquí puedes conocer y ejercer todos tus derechos según el RGPD.
          </p>
        </div>
      </section>

      {/* Quick Info */}
      <section className="py-8 border-b border-slate-800">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid md:grid-cols-3 gap-4">
            <Card className="bg-green-900/20 border-green-500/20">
              <CardContent className="p-4 text-center">
                <Shield className="h-8 w-8 text-green-400 mx-auto mb-2" />
                <p className="text-sm font-semibold text-white">100% Cumplimiento RGPD</p>
              </CardContent>
            </Card>
            <Card className="bg-blue-900/20 border-blue-500/20">
              <CardContent className="p-4 text-center">
                <Users className="h-8 w-8 text-blue-400 mx-auto mb-2" />
                <p className="text-sm font-semibold text-white">DPO Certificado</p>
              </CardContent>
            </Card>
            <Card className="bg-purple-900/20 border-purple-500/20">
              <CardContent className="p-4 text-center">
                <Globe className="h-8 w-8 text-purple-400 mx-auto mb-2" />
                <p className="text-sm font-semibold text-white">Sede en la UE</p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Your Rights */}
      <section className="py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold text-white mb-8 text-center">Tus Derechos RGPD</h2>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {rights.map((right, index) => (
              <Card key={index} className="bg-slate-800/50 border-slate-700">
                <CardHeader>
                  <right.icon className="h-10 w-10 text-green-400 mb-4" />
                  <CardTitle className="text-white">{right.title}</CardTitle>
                  <CardDescription className="text-gray-300">
                    {right.description}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Button variant="outline" className="w-full">
                    {right.action}
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Data We Collect */}
      <section className="py-12 border-t border-slate-800">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold text-white mb-8">Datos que Recopilamos</h2>
          
          <div className="space-y-6">
            {dataCategories.map((category, index) => (
              <Card key={index} className="bg-slate-800/50 border-slate-700">
                <CardHeader>
                  <CardTitle className="text-white">{category.category}</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid md:grid-cols-3 gap-4">
                    <div>
                      <p className="text-sm font-semibold text-gray-400 mb-2">Tipos de Datos</p>
                      <ul className="text-sm text-gray-300 space-y-1">
                        {category.types.map((type, i) => (
                          <li key={i}>• {type}</li>
                        ))}
                      </ul>
                    </div>
                    <div>
                      <p className="text-sm font-semibold text-gray-400 mb-2">Propósito</p>
                      <p className="text-sm text-gray-300">{category.purpose}</p>
                    </div>
                    <div>
                      <p className="text-sm font-semibold text-gray-400 mb-2">Retención</p>
                      <p className="text-sm text-gray-300">{category.retention}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Legal Basis */}
      <section className="py-12 border-t border-slate-800">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <Card className="bg-slate-800/50 border-slate-700">
            <CardHeader>
              <CardTitle className="text-white">Base Legal para el Procesamiento</CardTitle>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-[300px] pr-4">
                <div className="space-y-4">
                  <div>
                    <h3 className="font-semibold text-white mb-2">Consentimiento (Art. 6.1.a RGPD)</h3>
                    <p className="text-gray-300">
                      Para actividades de marketing, análisis de comportamiento y personalización 
                      de contenido. Puedes retirar tu consentimiento en cualquier momento.
                    </p>
                  </div>
                  
                  <div>
                    <h3 className="font-semibold text-white mb-2">Ejecución de Contrato (Art. 6.1.b RGPD)</h3>
                    <p className="text-gray-300">
                      Para proporcionar el servicio de streaming que has contratado, procesar pagos 
                      y gestionar tu cuenta.
                    </p>
                  </div>
                  
                  <div>
                    <h3 className="font-semibold text-white mb-2">Obligación Legal (Art. 6.1.c RGPD)</h3>
                    <p className="text-gray-300">
                      Para cumplir con obligaciones fiscales, prevención de fraude y requerimientos 
                      legales.
                    </p>
                  </div>
                  
                  <div>
                    <h3 className="font-semibold text-white mb-2">Interés Legítimo (Art. 6.1.f RGPD)</h3>
                    <p className="text-gray-300">
                      Para mejorar nuestros servicios, prevenir abusos y garantizar la seguridad 
                      de nuestra plataforma.
                    </p>
                  </div>
                </div>
              </ScrollArea>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* How to Exercise Rights */}
      <section className="py-12 border-t border-slate-800">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold text-white mb-8">Cómo Ejercer tus Derechos</h2>
          
          <div className="grid md:grid-cols-2 gap-6">
            <Card className="bg-green-900/20 border-green-500/20">
              <CardHeader>
                <Mail className="h-10 w-10 text-green-400 mb-4" />
                <CardTitle className="text-white">Por Email</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-300 mb-4">
                  Envía tu solicitud a nuestro Delegado de Protección de Datos:
                </p>
                <a href="mailto:<EMAIL>" className="text-green-400 hover:text-green-300 font-medium">
                  <EMAIL>
                </a>
                <p className="text-sm text-gray-400 mt-2">
                  Respuesta en máximo 30 días
                </p>
              </CardContent>
            </Card>
            
            <Card className="bg-blue-900/20 border-blue-500/20">
              <CardHeader>
                <Settings className="h-10 w-10 text-blue-400 mb-4" />
                <CardTitle className="text-white">Desde tu Cuenta</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-300 mb-4">
                  Accede a tu panel de privacidad:
                </p>
                <Button className="bg-blue-600 hover:bg-blue-700">
                  Ir a Configuración de Privacidad
                </Button>
                <p className="text-sm text-gray-400 mt-2">
                  Gestión instantánea de tus datos
                </p>
              </CardContent>
            </Card>
          </div>
          
          <Card className="mt-6 bg-slate-800/50 border-slate-700">
            <CardContent className="p-6">
              <h3 className="text-lg font-semibold text-white mb-3">Información Necesaria</h3>
              <p className="text-gray-300 mb-3">
                Para procesar tu solicitud necesitaremos:
              </p>
              <ul className="text-gray-300 space-y-1">
                <li>• Prueba de tu identidad (DNI o documento equivalente)</li>
                <li>• Descripción clara del derecho que deseas ejercer</li>
                <li>• Dirección de email asociada a tu cuenta</li>
              </ul>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* Contact DPO */}
      <section className="py-16 border-t border-slate-800">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <Card className="bg-gradient-to-br from-green-900/20 to-emerald-900/20 border-green-500/20">
            <CardContent className="p-8 text-center">
              <Shield className="h-16 w-16 text-green-400 mx-auto mb-4" />
              <h2 className="text-2xl font-bold text-white mb-4">
                Delegado de Protección de Datos
              </h2>
              <p className="text-gray-300 mb-6">
                Nuestro DPO está disponible para cualquier consulta sobre privacidad y protección de datos
              </p>
              <div className="space-y-2">
                <p className="text-white">
                  <strong>Email:</strong> <EMAIL>
                </p>
                <p className="text-white">
                  <strong>Teléfono:</strong> +34 900 123 456
                </p>
                <p className="text-white">
                  <strong>Dirección:</strong> Calle Privacidad 1, 28001 Madrid, España
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </section>

      <Footer />
    </div>
  )
}