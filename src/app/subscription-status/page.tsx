'use client'

import { useEffect, useState } from 'react'
import { Card } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { CheckCircle, XCircle, AlertCircle, RefreshCw, CreditCard, User, Calendar, Shield } from 'lucide-react'
import { useRouter } from 'next/navigation'

export default function SubscriptionStatusPage() {
  const [status, setStatus] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const router = useRouter()

  const fetchStatus = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/subscription/verify')
      const data = await response.json()
      setStatus(data)
    } catch (error) {
      console.error('Error fetching status:', error)
      setStatus({ error: 'Error al cargar estado' })
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchStatus()
  }, [])

  if (loading) {
    return (
      <div className="container mx-auto p-8">
        <Card className="p-6">
          <div className="flex items-center gap-3">
            <RefreshCw className="h-5 w-5 animate-spin" />
            <p>Verificando estado de la suscripción...</p>
          </div>
        </Card>
      </div>
    )
  }

  if (!status || !status.authenticated) {
    return (
      <div className="container mx-auto p-8">
        <Card className="p-6">
          <Alert variant="destructive">
            <AlertCircle className="h-5 w-5" />
            <AlertDescription>
              No estás autenticado. Por favor, inicia sesión primero.
            </AlertDescription>
          </Alert>
          <Button 
            onClick={() => router.push('/login')} 
            className="mt-4"
          >
            Iniciar Sesión
          </Button>
        </Card>
      </div>
    )
  }

  const hasAccess = status.access?.hasAccess
  const isSubscriptionActive = status.access?.hasActiveSubscription
  const profile = status.profile

  return (
    <div className="container mx-auto p-8 space-y-6 max-w-4xl">
      <h1 className="text-3xl font-bold mb-6">Estado de Tu Suscripción</h1>

      {/* Estado Principal */}
      <Card className="p-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold">Estado General</h2>
          <Badge variant={hasAccess ? "default" : "destructive"} className="text-lg px-4 py-2">
            {hasAccess ? "ACCESO ACTIVO" : "SIN ACCESO"}
          </Badge>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="flex items-center gap-3">
            {hasAccess ? (
              <CheckCircle className="h-6 w-6 text-green-500" />
            ) : (
              <XCircle className="h-6 w-6 text-red-500" />
            )}
            <div>
              <p className="font-medium">Acceso a Canales</p>
              <p className="text-sm text-gray-600">
                {status.access?.reason}
              </p>
            </div>
          </div>

          <div className="flex items-center gap-3">
            <CreditCard className="h-6 w-6 text-blue-500" />
            <div>
              <p className="font-medium">Tipo de Suscripción</p>
              <p className="text-sm text-gray-600">
                {profile?.subscriptionTier || 'Ninguna'}
              </p>
            </div>
          </div>
        </div>
      </Card>

      {/* Información del Usuario */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
          <User className="h-5 w-5" />
          Información del Usuario
        </h3>
        <div className="space-y-3">
          <div>
            <p className="text-sm text-gray-600">Email</p>
            <p className="font-medium">{status.user?.email}</p>
          </div>
          <div>
            <p className="text-sm text-gray-600">Nombre</p>
            <p className="font-medium">{profile?.displayName || 'No especificado'}</p>
          </div>
          <div>
            <p className="text-sm text-gray-600">ID de Usuario</p>
            <p className="font-mono text-sm">{status.user?.id}</p>
          </div>
        </div>
      </Card>

      {/* Detalles de Suscripción */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
          <Shield className="h-5 w-5" />
          Detalles de Suscripción
        </h3>
        
        <div className="space-y-4">
          {/* Estado Stripe */}
          <div className="p-4 bg-gray-50 rounded-lg">
            <p className="text-sm font-medium text-gray-700 mb-2">Estado en Stripe</p>
            {status.stripe ? (
              <div className="space-y-2">
                <p className="text-sm">
                  <span className="text-gray-600">ID Cliente:</span> 
                  <code className="ml-2 bg-white px-2 py-1 rounded text-xs">
                    {status.stripe.customerId}
                  </code>
                </p>
                {status.stripe.subscriptionId && (
                  <>
                    <p className="text-sm">
                      <span className="text-gray-600">ID Suscripción:</span> 
                      <code className="ml-2 bg-white px-2 py-1 rounded text-xs">
                        {status.stripe.subscriptionId}
                      </code>
                    </p>
                    <p className="text-sm">
                      <span className="text-gray-600">Estado:</span> 
                      <Badge variant="default" className="ml-2">
                        {status.stripe.status}
                      </Badge>
                    </p>
                  </>
                )}
              </div>
            ) : (
              <p className="text-sm text-gray-500">No hay información de Stripe</p>
            )}
          </div>

          {/* Fechas importantes */}
          <div className="p-4 bg-gray-50 rounded-lg">
            <p className="text-sm font-medium text-gray-700 mb-2">Fechas Importantes</p>
            <div className="space-y-2">
              {profile?.currentPeriodEnd && (
                <p className="text-sm">
                  <span className="text-gray-600">Fin del Período:</span> 
                  <span className="ml-2 font-medium">
                    {new Date(profile.currentPeriodEnd).toLocaleDateString('es-ES')}
                  </span>
                </p>
              )}
              {profile?.trialEndDate && (
                <p className="text-sm">
                  <span className="text-gray-600">Fin del Trial:</span> 
                  <span className="ml-2 font-medium">
                    {new Date(profile.trialEndDate).toLocaleDateString('es-ES')}
                  </span>
                </p>
              )}
              <p className="text-sm">
                <span className="text-gray-600">Cuenta Creada:</span> 
                <span className="ml-2 font-medium">
                  {new Date(profile?.createdAt).toLocaleDateString('es-ES')}
                </span>
              </p>
            </div>
          </div>

          {/* Estado en Base de Datos */}
          <div className="p-4 bg-gray-50 rounded-lg">
            <p className="text-sm font-medium text-gray-700 mb-2">Estado en Base de Datos</p>
            <div className="space-y-2">
              <p className="text-sm">
                <span className="text-gray-600">subscription_tier:</span> 
                <code className="ml-2 bg-white px-2 py-1 rounded text-xs">
                  {profile?.subscriptionTier || 'null'}
                </code>
              </p>
              <p className="text-sm">
                <span className="text-gray-600">subscription_status:</span> 
                <code className="ml-2 bg-white px-2 py-1 rounded text-xs">
                  {profile?.subscriptionStatus || 'null'}
                </code>
              </p>
              <p className="text-sm">
                <span className="text-gray-600">stripe_customer_id:</span> 
                <code className="ml-2 bg-white px-2 py-1 rounded text-xs">
                  {profile?.stripeCustomerId || 'null'}
                </code>
              </p>
            </div>
          </div>
        </div>
      </Card>

      {/* Acciones */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold mb-4">Acciones</h3>
        <div className="flex flex-wrap gap-3">
          <Button onClick={fetchStatus} variant="outline">
            <RefreshCw className="h-4 w-4 mr-2" />
            Actualizar Estado
          </Button>
          
          {hasAccess ? (
            <Button onClick={() => router.push('/browse')} variant="default">
              Ver Canales
            </Button>
          ) : (
            <Button onClick={() => router.push('/subscription')} variant="default">
              Suscribirse Ahora
            </Button>
          )}

          {profile?.stripeCustomerId && (
            <Button 
              onClick={() => window.location.href = '/api/stripe/customer-portal'}
              variant="outline"
            >
              <CreditCard className="h-4 w-4 mr-2" />
              Gestionar Suscripción
            </Button>
          )}
        </div>
      </Card>

      {/* Debug Info */}
      <details className="mt-8">
        <summary className="cursor-pointer text-sm text-gray-600 hover:text-gray-800">
          Información de Debug (click para expandir)
        </summary>
        <Card className="mt-4 p-4">
          <pre className="text-xs overflow-auto">
            {JSON.stringify(status, null, 2)}
          </pre>
        </Card>
      </details>
    </div>
  )
}