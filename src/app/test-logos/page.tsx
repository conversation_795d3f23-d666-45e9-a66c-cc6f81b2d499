'use client'

import { channelLogoMapping } from '@/lib/channel-logo-mapping'
import { Card } from '@/components/ui/card'

export default function TestLogosPage() {
  return (
    <div className="container mx-auto p-6">
      <h1 className="text-3xl font-bold mb-6">Teste de Logos dos Canais</h1>
      
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
        {Object.entries(channelLogoMapping).map(([channelName, logoFile], index) => (
          <Card key={index} className="p-4">
            <div className="aspect-video bg-gray-100 rounded-lg overflow-hidden mb-2">
              <img 
                src={`/logos/${logoFile}`}
                alt={channelName}
                className="w-full h-full object-contain"
                onError={(e) => {
                  const target = e.target as HTMLImageElement
                  target.style.display = 'none'
                  target.parentElement!.innerHTML = '<div class="flex items-center justify-center h-full text-red-500">Erro ao carregar</div>'
                }}
              />
            </div>
            <p className="text-sm font-medium text-center truncate">{channelName}</p>
            <p className="text-xs text-gray-500 text-center truncate">{logoFile}</p>
          </Card>
        ))}
      </div>
      
      <div className="mt-8">
        <h2 className="text-xl font-bold mb-4">Total de logos mapeadas: {Object.keys(channelLogoMapping).length}</h2>
      </div>
    </div>
  )
}