'use client'

import { Foot<PERSON> } from '@/components/footer'
import { BackToHome } from '@/components/back-to-home'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { But<PERSON> } from '@/components/ui/button'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Cookie, Shield, BarChart, Settings, Info } from 'lucide-react'
import { useState } from 'react'

export default function CookiesPage() {
  const [preferences, setPreferences] = useState({
    necessary: true,
    analytics: true,
    marketing: false,
    functional: true
  })

  const cookieTypes = [
    {
      id: 'necessary',
      icon: Shield,
      title: 'Cookies Necesarias',
      description: 'Esenciales para el funcionamiento del sitio web. No se pueden desactivar.',
      examples: ['Autenticación', 'Seguridad', 'Preferencias de idioma'],
      canDisable: false,
      color: 'text-green-400'
    },
    {
      id: 'functional',
      icon: Settings,
      title: 'Cookies Funcionales',
      description: 'Mejoran la funcionalidad y personalización del sitio.',
      examples: ['Preferencias de visualización', 'Configuración del reproductor', 'Última actividad'],
      canDisable: true,
      color: 'text-blue-400'
    },
    {
      id: 'analytics',
      icon: BarChart,
      title: 'Cookies Analíticas',
      description: 'Nos ayudan a entender cómo los usuarios interactúan con nuestro servicio.',
      examples: ['Google Analytics', 'Métricas de rendimiento', 'Análisis de uso'],
      canDisable: true,
      color: 'text-purple-400'
    },
    {
      id: 'marketing',
      icon: Info,
      title: 'Cookies de Marketing',
      description: 'Se utilizan para mostrar anuncios relevantes (actualmente no utilizadas).',
      examples: ['Publicidad personalizada', 'Seguimiento de conversiones', 'Remarketing'],
      canDisable: true,
      color: 'text-yellow-400'
    }
  ]

  const handleSavePreferences = () => {
    // Aquí se guardarían las preferencias del usuario
    console.log('Preferencias guardadas:', preferences)
    // Mostrar notificación de éxito
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-slate-950 to-slate-900">
      {/* Hero Section */}
      <section className="py-16 border-b border-slate-800">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <Badge className="mb-4 bg-orange-600 text-white">Legal</Badge>
          <BackToHome />
          <div className="flex items-center gap-3 mb-4">
            <Cookie className="h-10 w-10 text-orange-400" />
            <h1 className="text-4xl font-bold text-white">Política de Cookies</h1>
          </div>
          <p className="text-gray-400">
            Última actualización: {new Date().toLocaleDateString('es-ES', { 
              year: 'numeric', 
              month: 'long', 
              day: 'numeric' 
            })}
          </p>
          <p className="text-gray-300 mt-4">
            Utilizamos cookies para mejorar tu experiencia en StreamPlus España. 
            Aquí puedes aprender más sobre ellas y gestionar tus preferencias.
          </p>
        </div>
      </section>

      {/* Cookie Settings */}
      <section className="py-12">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <Card className="bg-slate-800/50 border-slate-700 mb-8">
          <BackToHome />
            <CardHeader>
              <CardTitle className="text-white">Gestionar Preferencias de Cookies</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {cookieTypes.map((type) => (
                  <div key={type.id} className="flex items-start gap-4 p-4 rounded-lg bg-slate-900/50">
                    <div className="flex-shrink-0">
                      <type.icon className={`h-6 w-6 ${type.color}`} />
                    </div>
                    <div className="flex-grow">
                      <h3 className="text-lg font-semibold text-white mb-1">{type.title}</h3>
                      <p className="text-gray-300 text-sm mb-2">{type.description}</p>
                      <div className="flex flex-wrap gap-2 mb-3">
                        {type.examples.map((example, index) => (
                          <Badge key={index} variant="secondary" className="text-xs">
                            {example}
                          </Badge>
                        ))}
                      </div>
                    </div>
                    <div className="flex-shrink-0">
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          checked={preferences[type.id as keyof typeof preferences]}
                          onChange={(e) => !type.canDisable ? null : setPreferences({
                            ...preferences,
                            [type.id]: e.target.checked
                          })}
                          disabled={!type.canDisable}
                          className="sr-only peer"
                        />
                        <div className={`w-11 h-6 bg-gray-700 peer-focus:outline-none rounded-full peer 
                          ${type.canDisable ? 'peer-checked:after:translate-x-full peer-checked:bg-blue-600' : 'bg-green-600 cursor-not-allowed'} 
                          after:content-[''] after:absolute after:top-[2px] after:left-[2px] 
                          after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all
                          ${!type.canDisable ? 'after:translate-x-full' : ''}`}>
                        </div>
                      </label>
                    </div>
                  </div>
                ))}
              </div>
              <div className="mt-6 flex justify-end">
                <Button onClick={handleSavePreferences} className="bg-blue-600 hover:bg-blue-700">
                  Guardar Preferencias
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Detailed Information */}
          <Card className="bg-slate-800/50 border-slate-700">
            <CardHeader>
              <CardTitle className="text-white">Información Detallada sobre Cookies</CardTitle>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-[400px] pr-4">
                <div className="space-y-6">
                  <div>
                    <h3 className="text-lg font-semibold text-white mb-2">¿Qué son las cookies?</h3>
                    <p className="text-gray-300">
                      Las cookies son pequeños archivos de texto que los sitios web colocan en tu dispositivo 
                      para almacenar información sobre tus preferencias y mejorar tu experiencia de navegación. 
                      Son esenciales para el funcionamiento de muchas características del sitio web.
                    </p>
                  </div>

                  <div>
                    <h3 className="text-lg font-semibold text-white mb-2">¿Por qué usamos cookies?</h3>
                    <p className="text-gray-300 mb-3">Utilizamos cookies para:</p>
                    <ul className="list-disc list-inside text-gray-300 space-y-1">
                      <li>Mantener tu sesión iniciada de forma segura</li>
                      <li>Recordar tus preferencias de visualización y configuración</li>
                      <li>Mejorar el rendimiento del sitio web</li>
                      <li>Analizar cómo los usuarios interactúan con nuestro servicio</li>
                      <li>Prevenir actividades fraudulentas</li>
                    </ul>
                  </div>

                  <div>
                    <h3 className="text-lg font-semibold text-white mb-2">Cookies de Terceros</h3>
                    <p className="text-gray-300">
                      Además de nuestras propias cookies, podemos utilizar cookies de terceros confiables 
                      como Google Analytics para comprender mejor cómo utilizas nuestro servicio. Estas 
                      empresas tienen sus propias políticas de privacidad que rigen el uso de esta información.
                    </p>
                  </div>

                  <div>
                    <h3 className="text-lg font-semibold text-white mb-2">Control de Cookies</h3>
                    <p className="text-gray-300">
                      Puedes controlar y/o eliminar las cookies como desees. Puedes eliminar todas las cookies 
                      que ya están en tu ordenador y puedes configurar la mayoría de los navegadores para que 
                      no se instalen. Sin embargo, si haces esto, es posible que tengas que ajustar manualmente 
                      algunas preferencias cada vez que visites un sitio y que algunos servicios y funcionalidades 
                      no funcionen.
                    </p>
                  </div>

                  <div>
                    <h3 className="text-lg font-semibold text-white mb-2">Actualizaciones de esta Política</h3>
                    <p className="text-gray-300">
                      Podemos actualizar esta Política de Cookies de vez en cuando para reflejar cambios en 
                      nuestras prácticas o por otras razones operativas, legales o regulatorias. Te 
                      recomendamos que revises esta página periódicamente para estar informado sobre nuestro 
                      uso de cookies.
                    </p>
                  </div>
                </div>
              </ScrollArea>
            </CardContent>
          </Card>

          {/* Contact Info */}
          <div className="mt-8 p-6 bg-orange-900/20 rounded-lg border border-orange-500/20">
            <h3 className="text-lg font-semibold text-white mb-2">¿Preguntas sobre Cookies?</h3>
            <p className="text-gray-300">
              Si tienes preguntas sobre nuestra Política de Cookies, contáctanos en{' '}
              <a href="mailto:<EMAIL>" className="text-orange-400 hover:text-orange-300">
                <EMAIL>
              </a>
            </p>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  )
}