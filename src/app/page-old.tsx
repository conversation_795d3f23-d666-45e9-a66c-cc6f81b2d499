import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { PlayCircle, Tv, Trophy, Users } from "lucide-react";

export default function Home() {
  return (
    <div className="min-h-screen bg-gradient-to-b from-background to-background/95">
      {/* Hero Section */}
      <section className="relative px-6 lg:px-8 py-24 sm:py-32">
        <div className="mx-auto max-w-7xl">
          <div className="text-center">
            <h1 className="text-4xl font-bold tracking-tight sm:text-6xl bg-gradient-to-r from-red-500 to-yellow-500 bg-clip-text text-transparent">
              StreamPlus España
            </h1>
            <p className="mt-6 text-lg leading-8 text-muted-foreground">
              La plataforma premium de streaming deportivo para España.
              Disfruta de LaLiga, Champions League, F1 y más en HD.
            </p>
            <div className="mt-10 flex items-center justify-center gap-x-6">
              <Button size="lg" asChild>
                <Link href="/watch/demo">
                  <PlayCircle className="mr-2 h-4 w-4" />
                  Ver Demo
                </Link>
              </Button>
              <Button variant="outline" size="lg" asChild>
                <Link href="/pricing">
                  Ver Planes
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-24 sm:py-32">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="mx-auto max-w-2xl text-center">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">
              Todo el deporte en un solo lugar
            </h2>
            <p className="mt-6 text-lg leading-8 text-muted-foreground">
              Accede a los mejores eventos deportivos con la mejor calidad
            </p>
          </div>
          
          <div className="mx-auto mt-16 max-w-7xl">
            <div className="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3">
              {features.map((feature) => (
                <Card key={feature.name} className="bg-card/50 backdrop-blur">
                  <CardContent className="pt-6">
                    <div className="flex items-center gap-x-3">
                      <feature.icon className="h-8 w-8 text-primary" />
                      <h3 className="text-lg font-semibold">{feature.name}</h3>
                    </div>
                    <p className="mt-4 text-muted-foreground">
                      {feature.description}
                    </p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-24 sm:py-32 bg-muted/50">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="mx-auto max-w-2xl text-center">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">
              ¿Listo para empezar?
            </h2>
            <p className="mt-6 text-lg leading-8 text-muted-foreground">
              Únete a miles de aficionados que ya disfrutan del mejor deporte
            </p>
            <div className="mt-10">
              <Button size="lg" className="px-8" asChild>
                <Link href="/register">
                  Crear Cuenta Gratis
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}

const features = [
  {
    name: 'Streaming en HD',
    description: 'Disfruta de todos los partidos en alta definición con la mejor calidad de imagen.',
    icon: Tv,
  },
  {
    name: 'Todos los Deportes',
    description: 'Fútbol, F1, MotoGP, NBA, tenis y mucho más. Todo incluido en tu suscripción.',
    icon: Trophy,
  },
  {
    name: 'Multidispositivo',
    description: 'Ve tus eventos favoritos en TV, móvil, tablet o PC. Donde quieras, cuando quieras.',
    icon: Users,
  },
];