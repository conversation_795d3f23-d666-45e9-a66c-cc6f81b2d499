'use client'

import ReactPlayer from 'react-player'

export default function SimpleTestPage() {
  const testUrl = 'https://test-streams.mux.dev/x36xhzz/x36xhzz.m3u8'
  
  return (
    <div className="container mx-auto py-8 max-w-4xl">
      <h1 className="text-2xl font-bold mb-6">Simple Player Test</h1>
      
      <div className="space-y-4">
        <div className="p-4 bg-gray-100 dark:bg-gray-800 rounded">
          <p>URL: {testUrl}</p>
        </div>
        
        <div className="relative aspect-video bg-black rounded-lg overflow-hidden">
          <ReactPlayer
            url={testUrl}
            playing={true}
            controls={true}
            width="100%"
            height="100%"
            muted={true}
            volume={0.5}
            onReady={() => console.log('✅ Ready')}
            onStart={() => console.log('▶️ Started')}
            onPlay={() => console.log('▶️ Play')}
            onPause={() => console.log('⏸️ Pause')}
            onError={(e) => console.error('❌ Error:', e)}
            onProgress={(state) => console.log('Progress:', state)}
            config={{
              file: {
                forceHLS: true
              }
            }}
          />
        </div>
        
        <div className="p-4 bg-blue-50 dark:bg-blue-950 rounded">
          <p className="text-sm">Abra o console (F12) para ver os logs.</p>
          <p className="text-sm mt-2">Se o vídeo não carregar, verifique:</p>
          <ul className="list-disc list-inside text-sm mt-1">
            <li>Erros no console</li>
            <li>Aba Network para ver requisições</li>
            <li>Se há bloqueio de CORS</li>
          </ul>
        </div>
      </div>
    </div>
  )
}