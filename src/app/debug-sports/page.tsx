'use client'

import { useEffect } from 'react'
import { useSportsStore } from '@/stores/sports.store'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'

export default function DebugSportsPage() {
  const store = useSportsStore()
  
  useEffect(() => {
    // Log inicial
    console.log('🔍 [DEBUG] Estado inicial do store:', {
      upcomingEvents: store.upcomingEvents.length,
      loading: store.loadingUpcomingEvents
    })
  }, [])
  
  const testFetch = async () => {
    console.log('🚀 [DEBUG] Iniciando fetchUpcomingEvents...')
    await store.fetchUpcomingEvents()
    console.log('✅ [DEBUG] fetchUpcomingEvents completo:', {
      totalEvents: store.upcomingEvents.length,
      primeirosEventos: store.upcomingEvents.slice(0, 2)
    })
  }
  
  const checkMockData = () => {
    const events = store.upcomingEvents
    if (events.length > 0) {
      const firstEvent = events[0]
      console.log('🔍 [DEBUG] Analisando primeiro evento:', firstEvent)
      
      // Verificar se é mock data
      const isMock = firstEvent.name?.includes('Real Madrid') || 
                     firstEvent.name?.includes('Barcelona') ||
                     firstEvent.name?.includes('UFC 295')
      
      console.log(`📊 [DEBUG] Dados são ${isMock ? 'MOCK' : 'REAIS'}`)
      
      // Verificar estrutura
      console.log('🏗️ [DEBUG] Estrutura do evento:', {
        hasId: !!firstEvent.id,
        hasName: !!firstEvent.name,
        hasSport: !!firstEvent.sport,
        hasDateFrom: !!firstEvent.dateFrom,
        hasCompetition: !!firstEvent.competition
      })
    }
  }
  
  return (
    <div className="min-h-screen bg-black p-8">
      <h1 className="text-3xl font-bold text-white mb-8">Debug Sports Store</h1>
      
      <div className="space-y-4">
        <Card className="p-6 bg-gray-900 border-gray-800">
          <h2 className="text-xl font-bold text-white mb-4">Ações</h2>
          <div className="flex gap-4">
            <Button onClick={testFetch}>
              Buscar Eventos
            </Button>
            <Button onClick={checkMockData} variant="outline">
              Verificar se é Mock
            </Button>
            <Button onClick={() => window.location.reload()} variant="outline">
              Recarregar Página
            </Button>
          </div>
        </Card>
        
        <Card className="p-6 bg-gray-900 border-gray-800">
          <h2 className="text-xl font-bold text-white mb-4">
            Estado Atual ({store.upcomingEvents.length} eventos)
          </h2>
          <pre className="text-sm text-gray-300 overflow-auto max-h-[400px]">
            {JSON.stringify({
              loading: store.loadingUpcomingEvents,
              totalEvents: store.upcomingEvents.length,
              primeiros3Eventos: store.upcomingEvents.slice(0, 3).map(e => ({
                id: e.id,
                name: e.name,
                sport: e.sport,
                date: e.dateFrom
              }))
            }, null, 2)}
          </pre>
        </Card>
        
        <Card className="p-6 bg-gray-900 border-gray-800">
          <h2 className="text-xl font-bold text-white mb-4">Console</h2>
          <p className="text-sm text-gray-400">
            Abra o console do navegador (F12) para ver os logs detalhados
          </p>
        </Card>
      </div>
    </div>
  )
}