'use client'

import { useEffect, useState } from 'react'
import { Card } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { CheckCircle, XCircle, AlertCircle } from 'lucide-react'

export default function DebugStripePage() {
  const [debugInfo, setDebugInfo] = useState<any>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetch('/api/debug/env')
      .then(res => res.json())
      .then(data => {
        setDebugInfo(data)
        setLoading(false)
      })
      .catch(err => {
        console.error('Error fetching debug info:', err)
        setLoading(false)
      })
  }, [])

  if (loading) {
    return (
      <div className="container mx-auto p-8">
        <Card className="p-6">
          <p>Carregando informações de debug...</p>
        </Card>
      </div>
    )
  }

  if (!debugInfo) {
    return (
      <div className="container mx-auto p-8">
        <Card className="p-6">
          <p className="text-red-500">Erro ao carregar informações de debug</p>
        </Card>
      </div>
    )
  }

  const hasInvertedKeys = debugInfo.stripe?.keysAreInverted === '❌ SIM - AS CHAVES ESTÃO INVERTIDAS!'

  return (
    <div className="container mx-auto p-8 space-y-6">
      <h1 className="text-3xl font-bold mb-6">Debug Stripe Configuration</h1>

      {/* Status Principal */}
      <Alert variant={hasInvertedKeys ? "destructive" : "default"}>
        <div className="flex items-center gap-2">
          {hasInvertedKeys ? (
            <XCircle className="h-5 w-5" />
          ) : (
            <CheckCircle className="h-5 w-5" />
          )}
          <AlertDescription className="text-lg font-semibold">
            {debugInfo.status}
          </AlertDescription>
        </div>
      </Alert>

      {/* Detalhes das Chaves */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Secret Key */}
        <Card className="p-6">
          <h3 className="font-semibold mb-4">STRIPE_SECRET_KEY</h3>
          <div className="space-y-2">
            <p className="flex items-center gap-2">
              {debugInfo.stripe.STRIPE_SECRET_KEY.defined === '✅ Definida' ? (
                <CheckCircle className="h-4 w-4 text-green-500" />
              ) : (
                <XCircle className="h-4 w-4 text-red-500" />
              )}
              {debugInfo.stripe.STRIPE_SECRET_KEY.defined}
            </p>
            <p className="text-sm text-gray-600">
              Prefixo: <code className="bg-gray-100 px-2 py-1 rounded">
                {debugInfo.stripe.STRIPE_SECRET_KEY.prefix}
              </code>
            </p>
            <p className="flex items-center gap-2">
              {debugInfo.stripe.STRIPE_SECRET_KEY.isCorrect.startsWith('✅') ? (
                <CheckCircle className="h-4 w-4 text-green-500" />
              ) : (
                <XCircle className="h-4 w-4 text-red-500" />
              )}
              {debugInfo.stripe.STRIPE_SECRET_KEY.isCorrect}
            </p>
            {debugInfo.stripe.STRIPE_SECRET_KEY.problem && (
              <Alert variant="destructive" className="mt-2">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  {debugInfo.stripe.STRIPE_SECRET_KEY.problem}
                </AlertDescription>
              </Alert>
            )}
          </div>
        </Card>

        {/* Publishable Key */}
        <Card className="p-6">
          <h3 className="font-semibold mb-4">NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY</h3>
          <div className="space-y-2">
            <p className="flex items-center gap-2">
              {debugInfo.stripe.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY.defined === '✅ Definida' ? (
                <CheckCircle className="h-4 w-4 text-green-500" />
              ) : (
                <XCircle className="h-4 w-4 text-red-500" />
              )}
              {debugInfo.stripe.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY.defined}
            </p>
            <p className="text-sm text-gray-600">
              Prefixo: <code className="bg-gray-100 px-2 py-1 rounded">
                {debugInfo.stripe.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY.prefix}
              </code>
            </p>
            <p className="flex items-center gap-2">
              {debugInfo.stripe.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY.isCorrect.startsWith('✅') ? (
                <CheckCircle className="h-4 w-4 text-green-500" />
              ) : (
                <XCircle className="h-4 w-4 text-red-500" />
              )}
              {debugInfo.stripe.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY.isCorrect}
            </p>
            {debugInfo.stripe.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY.problem && (
              <Alert variant="destructive" className="mt-2">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  {debugInfo.stripe.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY.problem}
                </AlertDescription>
              </Alert>
            )}
          </div>
        </Card>
      </div>

      {/* Solução */}
      {debugInfo.solution && (
        <Alert className="border-yellow-500 bg-yellow-50">
          <AlertCircle className="h-5 w-5 text-yellow-600" />
          <div>
            <AlertDescription className="font-semibold text-lg mb-2">
              {debugInfo.solution.message}
            </AlertDescription>
            <ol className="list-decimal list-inside space-y-1">
              {debugInfo.solution.steps.map((step: string, index: number) => (
                <li key={index} className="text-sm">{step}</li>
              ))}
            </ol>
          </div>
        </Alert>
      )}

      {/* Outras Configurações */}
      <Card className="p-6">
        <h3 className="font-semibold mb-4">Outras Configurações</h3>
        <div className="space-y-2">
          <p>NODE_ENV: <code className="bg-gray-100 px-2 py-1 rounded">{debugInfo.env.NODE_ENV}</code></p>
          <p>App URL: <code className="bg-gray-100 px-2 py-1 rounded">{debugInfo.env.NEXT_PUBLIC_APP_URL}</code></p>
          <p>Webhook Secret: {debugInfo.env.STRIPE_WEBHOOK_SECRET}</p>
        </div>
      </Card>
    </div>
  )
}