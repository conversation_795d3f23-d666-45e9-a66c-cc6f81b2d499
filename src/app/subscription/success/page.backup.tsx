import { Check<PERSON>ir<PERSON>, ArrowR<PERSON>, Trophy } from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import Link from 'next/link'

export default function SubscriptionSuccessPage() {
  return (
    <div className="min-h-screen bg-gradient-to-b from-slate-900 to-slate-950 flex items-center justify-center p-4">
      <Card className="max-w-lg w-full p-8 bg-slate-800/50 border-slate-700 text-center">
        <div className="inline-flex items-center justify-center w-20 h-20 bg-green-500/20 rounded-full mb-6">
          <CheckCircle className="h-10 w-10 text-green-500" />
        </div>

        <h1 className="text-3xl font-bold text-white mb-4">
          ¡Bienvenido a NewSpports Premium!
        </h1>

        <p className="text-gray-300 mb-8 text-lg">
          Tu suscripción ha sido activada con éxito. Ahora tienes acceso ilimitado a todos los canales deportivos premium durante 1 mes.
        </p>

        <div className="space-y-4 mb-8">
          <div className="flex items-center justify-center gap-3 text-gray-300">
            <Trophy className="h-5 w-5 text-yellow-500" />
            <span>Acceso a más de 100 canales HD y 4K</span>
          </div>
          <div className="flex items-center justify-center gap-3 text-gray-300">
            <Trophy className="h-5 w-5 text-yellow-500" />
            <span>Sin anuncios ni interrupciones</span>
          </div>
          <div className="flex items-center justify-center gap-3 text-gray-300">
            <Trophy className="h-5 w-5 text-yellow-500" />
            <span>Soporte prioritario 24/7</span>
          </div>
        </div>

        <div className="space-y-3">
          <Button
            className="w-full bg-gradient-to-r from-red-600 to-orange-600"
            asChild
          >
            <Link href="/browse">
              <ArrowRight className="mr-2 h-5 w-5" />
              Explorar Canales Premium
            </Link>
          </Button>

          <Button
            variant="outline"
            className="w-full"
            asChild
          >
            <Link href="/account">
              Gestionar Mi Suscripción
            </Link>
          </Button>
        </div>

        <p className="text-sm text-gray-500 mt-6">
          Se renovará automáticamente el {new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toLocaleDateString('es-ES', { 
            year: 'numeric', 
            month: 'long', 
            day: 'numeric' 
          })} por €20/mes. Puedes cancelar en cualquier momento.
        </p>
      </Card>
    </div>
  )
}