import { X<PERSON>ircle, ArrowLeft, HelpCircle } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import Link from 'next/link'

export default function SubscriptionCancelPage() {
  return (
    <div className="min-h-screen bg-gradient-to-b from-slate-900 to-slate-950 flex items-center justify-center p-4">
      <Card className="max-w-lg w-full p-8 bg-slate-800/50 border-slate-700 text-center">
        <div className="inline-flex items-center justify-center w-20 h-20 bg-yellow-500/20 rounded-full mb-6">
          <XCircle className="h-10 w-10 text-yellow-500" />
        </div>

        <h1 className="text-3xl font-bold text-white mb-4">
          Pago Cancelado
        </h1>

        <p className="text-gray-300 mb-8 text-lg">
          No se ha realizado ningún cargo. Tu pago ha sido cancelado y no se ha procesado ninguna transacción.
        </p>

        <div className="space-y-3">
          <Button
            className="w-full bg-gradient-to-r from-red-600 to-orange-600"
            asChild
          >
            <Link href="/pricing">
              <ArrowLeft className="mr-2 h-5 w-5" />
              Volver a los Planes
            </Link>
          </Button>

          <Button
            variant="outline"
            className="w-full"
            asChild
          >
            <Link href="/browse">
              Explorar Canales Gratuitos
            </Link>
          </Button>
        </div>

        <div className="mt-8 p-4 bg-slate-900/50 rounded-lg">
          <div className="flex items-center gap-2 text-blue-400 mb-2">
            <HelpCircle className="h-5 w-5" />
            <span className="font-semibold">¿Necesitas ayuda?</span>
          </div>
          <p className="text-sm text-gray-400">
            Si experimentaste algún problema durante el proceso de pago, contá<NAME_EMAIL>
          </p>
        </div>
      </Card>
    </div>
  )
}