'use client'

import { Footer } from '@/components/footer'
import { BackToHome } from '@/components/back-to-home'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'

export default function TermsPage() {
  const sections = [
    {
      title: "1. Aceptación de los Términos",
      content: `Al acceder y utilizar NewSpports, usted acepta estar sujeto a estos Términos de Servicio y todas las leyes y regulaciones aplicables. Si no está de acuerdo con alguno de estos términos, no está autorizado a utilizar o acceder a este servicio.`
    },
    {
      title: "2. Descripción del Servicio",
      content: `NewSpports es una plataforma de streaming que ofrece contenido deportivo y de noticias en vivo y bajo demanda. El servicio incluye, pero no se limita a, transmisiones en vivo de eventos deportivos, noticias, repeticiones, análisis, estadísticas y contenido relacionado.`
    },
    {
      title: "3. Registro y Cuenta",
      content: `Para utilizar ciertos servicios, debe registrarse y mantener una cuenta activa. Usted es responsable de mantener la confidencialidad de su cuenta y contraseña, y es totalmente responsable de todas las actividades que ocurran bajo su cuenta.`
    },
    {
      title: "4. Uso Aceptable",
      content: `Usted se compromete a utilizar el servicio solo para fines legales y de acuerdo con estos Términos. Está prohibido:
      
• Compartir credenciales de cuenta con terceros
• Utilizar el servicio para fines comerciales sin autorización
• Intentar descargar, copiar o redistribuir contenido
• Utilizar cualquier método para eludir las restricciones geográficas
• Interferir con el funcionamiento normal del servicio`
    },
    {
      title: "5. Suscripciones y Pagos",
      content: `Las suscripciones se facturan por adelantado de forma mensual o anual. Los pagos no son reembolsables, excepto según lo exija la ley. NewSpports se reserva el derecho de cambiar los precios de suscripción con previo aviso de 30 días.`
    },
    {
      title: "6. Contenido y Propiedad Intelectual",
      content: `Todo el contenido disponible en NewSpports, incluidos videos, gráficos, logos y marcas, está protegido por derechos de autor y otras leyes de propiedad intelectual. No puede copiar, modificar, distribuir, vender o alquilar ninguna parte del servicio o contenido incluido.`
    },
    {
      title: "7. Limitación de Responsabilidad",
      content: `NewSpports no será responsable de ningún daño indirecto, incidental, especial, consecuente o punitivo, incluidos, entre otros, pérdida de beneficios, datos, uso, buena voluntad u otras pérdidas intangibles.`
    },
    {
      title: "8. Modificaciones del Servicio",
      content: `Nos reservamos el derecho de modificar o discontinuar, temporal o permanentemente, el servicio (o cualquier parte del mismo) con o sin previo aviso. No seremos responsables ante usted o terceros por cualquier modificación, cambio de precio, suspensión o discontinuación del servicio.`
    },
    {
      title: "9. Cancelación",
      content: `Puede cancelar su suscripción en cualquier momento a través de su panel de cuenta. La cancelación será efectiva al final del período de facturación actual. No se proporcionarán reembolsos por períodos parciales.`
    },
    {
      title: "10. Ley Aplicable",
      content: `Estos Términos se regirán e interpretarán de acuerdo con las leyes de España, sin tener en cuenta sus disposiciones sobre conflictos de leyes. Nuestra falta de aplicación de cualquier derecho o disposición de estos Términos no se considerará una renuncia a esos derechos.`
    }
  ]

  return (
    <div className="min-h-screen bg-gradient-to-b from-slate-950 to-slate-900">
      {/* Hero Section */}
      <section className="py-16 border-b border-slate-800">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <Badge className="mb-4">Legal</Badge>
          <BackToHome />
          <h1 className="text-4xl font-bold text-white mb-4">Términos de Servicio</h1>
          <p className="text-gray-400">
            Última actualización: {new Date().toLocaleDateString('es-ES', { 
              year: 'numeric', 
              month: 'long', 
              day: 'numeric' 
            })}
          </p>
        </div>
      </section>

      {/* Content */}
      <section className="py-12">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <Card className="bg-slate-800/50 border-slate-700">
          <BackToHome />
            <CardHeader>
              <CardTitle className="text-white">
                Por favor, lea estos términos cuidadosamente antes de usar nuestro servicio
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-[600px] pr-4">
                <div className="space-y-8">
                  {sections.map((section, index) => (
                    <div key={index}>
                      <h2 className="text-xl font-semibold text-white mb-3">
                        {section.title}
                      </h2>
                      <p className="text-gray-300 whitespace-pre-line">
                        {section.content}
                      </p>
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </CardContent>
          </Card>

          <div className="mt-8 p-6 bg-blue-900/20 rounded-lg border border-blue-500/20">
            <h3 className="text-lg font-semibold text-white mb-2">¿Tienes preguntas?</h3>
            <p className="text-gray-300">
              Si tienes alguna pregunta sobre estos Términos de Servicio, puedes contactarnos en{' '}
              <a href="mailto:<EMAIL>" className="text-blue-400 hover:text-blue-300">
                <EMAIL>
              </a>
            </p>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  )
}