'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import Link from 'next/link'
import { 
  Search, 
  User as UserIcon, 
  LogOut,
  Menu,
  X,
  Trophy
} from 'lucide-react'
import { Logo } from '@/components/logo'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { toast } from 'sonner'
import { useAuth } from '@/hooks/use-auth'
import { BackgroundServicesManager } from '@/components/background-services-manager'
import { usePremiumCheck } from '@/hooks/use-premium-check'
import { AccessDebug } from '@/components/debug/access-debug'
import { ActivityTracker } from '@/components/activity-tracker'
import { SubscriptionStatus } from '@/components/subscription-status'
import { TrialStatus } from '@/components/trial-status'
import { SubscriptionDebug } from '@/components/debug/subscription-debug'

export default function PlatformLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const router = useRouter()
  const { user, signOut } = useAuth()
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)
  const { hasPremium } = usePremiumCheck() // Verificar premium automaticamente
  
  async function handleLogout() {
    try {
      await signOut()
      toast.success('Sesión cerrada correctamente')
      router.push('/')
    } catch (error) {
      toast.error('Error al cerrar sesión')
    }
  }
  
  if (!user) {
    return null
  }
  
  const navItems = [
    { href: '/browse', label: 'Explorar', icon: Search },
    { href: '/sports', label: 'Deportes', icon: Trophy }
  ]
  
  return (
    <div className="min-h-screen bg-background">
      {/* Background Services */}
      <BackgroundServicesManager />
      <ActivityTracker />
      
      {/* Header */}
      <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 flex h-16 items-center justify-between">
          {/* Logo */}
          <Link href="/browse" className="flex items-center">
            <Logo size="md" />
          </Link>
          
          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-6">
            {navItems.map((item) => {
              const Icon = item.icon
              return (
                <Link
                  key={item.href}
                  href={item.href}
                  className="flex items-center space-x-2 text-sm font-medium transition-colors hover:text-primary"
                >
                  <Icon className="h-4 w-4" />
                  <span>{item.label}</span>
                </Link>
              )
            })}
          </nav>
          
          {/* User Menu */}
          <div className="flex items-center space-x-4">
            {/* Status Badges - Mostra Premium OU Trial */}
            <SubscriptionStatus />
            <TrialStatus />
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon" className="relative">
                  <UserIcon className="h-5 w-5" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-56">
                <DropdownMenuLabel>
                  <div className="flex flex-col space-y-1">
                    <p className="text-sm font-medium leading-none">Mi cuenta</p>
                    <p className="text-xs leading-none text-muted-foreground">
                      {user?.primaryEmailAddress || user?.clientMetadata?.email || 'Usuario'}
                    </p>
                  </div>
                </DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem asChild>
                  <Link href="/profile">
                    <UserIcon className="mr-2 h-4 w-4" />
                    Mi Perfil
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link href="/subscription">
                    <span className="mr-2">💳</span>
                    Suscripción
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={handleLogout}>
                  <LogOut className="mr-2 h-4 w-4" />
                  Cerrar sesión
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
            
            {/* Mobile Menu Button */}
            <Button
              variant="ghost"
              size="icon"
              className="md:hidden"
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
            >
              {mobileMenuOpen ? (
                <X className="h-5 w-5" />
              ) : (
                <Menu className="h-5 w-5" />
              )}
            </Button>
          </div>
        </div>
        
        {/* Mobile Navigation */}
        {mobileMenuOpen && (
          <div className="md:hidden border-t">
            <nav className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 space-y-3">
              {navItems.map((item) => {
                const Icon = item.icon
                return (
                  <Link
                    key={item.href}
                    href={item.href}
                    onClick={() => setMobileMenuOpen(false)}
                    className="flex items-center space-x-2 text-sm font-medium transition-colors hover:text-primary"
                  >
                    <Icon className="h-4 w-4" />
                    <span>{item.label}</span>
                  </Link>
                )
              })}
              <div className="pt-3 border-t">
                <button
                  onClick={handleLogout}
                  className="flex items-center space-x-2 text-sm font-medium text-destructive hover:text-destructive/90"
                >
                  <LogOut className="h-4 w-4" />
                  <span>Cerrar sesión</span>
                </button>
              </div>
            </nav>
          </div>
        )}
      </header>
      
      {/* Main Content */}
      <main>
        {children}
      </main>
      
      {/* Debug Components */}
      <AccessDebug />
      {process.env.NODE_ENV === 'development' && (
        <SubscriptionDebug />
      )}
    </div>
  )
}