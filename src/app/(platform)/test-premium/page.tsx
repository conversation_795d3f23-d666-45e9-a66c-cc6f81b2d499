'use client'

import { useState, useEffect } from 'react'
import { createClient } from '@/lib/supabase/client'
import { useAuthOptional } from '@/hooks/use-auth'
import { useAccessStore } from '@/stores/access.store'
import { useTrialStore } from '@/stores/trial.store'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Loader2, CheckCircle, XCircle, RefreshCw, Play, AlertCircle } from 'lucide-react'

export default function TestPremiumPage() {
  const { user } = useAuthOptional()
  const accessStore = useAccessStore()
  const trialStore = useTrialStore()
  const [loading, setLoading] = useState(false)
  const [sqlResults, setSqlResults] = useState<any>(null)
  const [apiResults, setApiResults] = useState<any>(null)
  const [fixResults, setFixResults] = useState<any>(null)

  // Auto refresh a cada 5 segundos
  useEffect(() => {
    const interval = setInterval(() => {
      checkEverything()
    }, 5000)
    
    // Check inicial
    checkEverything()
    
    return () => clearInterval(interval)
  }, [user?.id])

  const checkEverything = async () => {
    if (!user?.id) return
    
    try {
      // 1. Verificar via API
      const apiResponse = await fetch(`/api/debug-premium?stackId=${user.id}`)
      const apiData = await apiResponse.json()
      setApiResults(apiData)
      
      // 2. Verificar direto no banco
      const supabase = createClient()
      const { data: sqlData } = await supabase
        .rpc('check_user_access_unified', { p_user_id: user.id })
        .single()
      setSqlResults(sqlData)
      
    } catch (error) {
      console.error('Erro ao verificar:', error)
    }
  }

  const fixPremium = async () => {
    setLoading(true)
    setFixResults(null)
    
    try {
      const response = await fetch('/api/force-premium-fix', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ userId: user?.id })
      })
      
      const data = await response.json()
      setFixResults(data)
      
      // Recarregar verificações
      setTimeout(() => {
        checkEverything()
        accessStore.checkAccess(true)
      }, 1000)
      
    } catch (error) {
      setFixResults({ error: error instanceof Error ? error.message : 'Erro desconhecido' })
    } finally {
      setLoading(false)
    }
  }

  const StatusBadge = ({ condition, label }: { condition: boolean; label: string }) => (
    <Badge variant={condition ? "default" : "destructive"} className="gap-1">
      {condition ? <CheckCircle className="h-3 w-3" /> : <XCircle className="h-3 w-3" />}
      {label}
    </Badge>
  )

  if (!user) {
    return (
      <div className="container mx-auto p-8">
        <Card>
          <CardHeader>
            <CardTitle>Teste de Premium</CardTitle>
            <CardDescription>Faça login para testar o sistema premium</CardDescription>
          </CardHeader>
        </Card>
      </div>
    )
  }

  const isPremiumActive = apiResults?.stack_profile?.subscription_tier === 'premium' && 
                         apiResults?.stack_profile?.subscription_status === 'active' &&
                         !apiResults?.profile_analysis?.is_expired

  return (
    <div className="container mx-auto p-8 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>🧪 Teste Completo do Sistema Premium</span>
            <Button onClick={checkEverything} variant="outline" size="sm">
              <RefreshCw className="h-4 w-4 mr-2" />
              Atualizar
            </Button>
          </CardTitle>
          <CardDescription>
            Verificação em tempo real do status premium para {user.email}
          </CardDescription>
        </CardHeader>
      </Card>

      {/* Status Geral */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Status Geral</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <StatusBadge 
              condition={isPremiumActive} 
              label="Premium Ativo" 
            />
            <StatusBadge 
              condition={accessStore.hasAccess} 
              label="Tem Acesso" 
            />
            <StatusBadge 
              condition={!trialStore.isExpired} 
              label="Trial Válido" 
            />
            <StatusBadge 
              condition={sqlResults?.has_access} 
              label="SQL OK" 
            />
          </div>
        </CardContent>
      </Card>

      {/* Informações do Usuário */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Informações do Usuário</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <span className="font-medium">ID:</span> {user.id}
            </div>
            <div>
              <span className="font-medium">Email:</span> {user.email}
            </div>
            <div>
              <span className="font-medium">Tipo de Auth:</span> {apiResults?.auth_type || 'Verificando...'}
            </div>
            <div>
              <span className="font-medium">Provider:</span> {user.provider || 'N/A'}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Estado do Access Store */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Access Store (Frontend)</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span>hasAccess:</span>
              <Badge variant={accessStore.hasAccess ? "default" : "secondary"}>
                {String(accessStore.hasAccess)}
              </Badge>
            </div>
            <div className="flex justify-between">
              <span>accessType:</span>
              <Badge variant="outline">{accessStore.accessType || 'null'}</Badge>
            </div>
            <div className="flex justify-between">
              <span>expiresAt:</span>
              <span className="text-muted-foreground">
                {accessStore.expiresAt ? new Date(accessStore.expiresAt).toLocaleString('pt-BR') : 'null'}
              </span>
            </div>
            <div className="flex justify-between">
              <span>isChecking:</span>
              <Badge variant={accessStore.isChecking ? "secondary" : "outline"}>
                {String(accessStore.isChecking)}
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Estado do Trial Store */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Trial Store (Frontend)</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span>timeRemaining:</span>
              <Badge variant="outline">{trialStore.timeRemaining}s</Badge>
            </div>
            <div className="flex justify-between">
              <span>isExpired:</span>
              <Badge variant={trialStore.isExpired ? "destructive" : "default"}>
                {String(trialStore.isExpired)}
              </Badge>
            </div>
            <div className="flex justify-between">
              <span>startTime:</span>
              <span className="text-muted-foreground">
                {trialStore.startTime ? new Date(trialStore.startTime).toLocaleTimeString('pt-BR') : 'null'}
              </span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Resultado SQL */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Verificação SQL (Database)</CardTitle>
        </CardHeader>
        <CardContent>
          {sqlResults ? (
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span>has_access:</span>
                <Badge variant={sqlResults.has_access ? "default" : "destructive"}>
                  {String(sqlResults.has_access)}
                </Badge>
              </div>
              <div className="flex justify-between">
                <span>access_type:</span>
                <Badge variant="outline">{sqlResults.access_type || 'null'}</Badge>
              </div>
              <div className="flex justify-between">
                <span>subscription_tier:</span>
                <Badge variant="outline">{sqlResults.subscription_tier || 'null'}</Badge>
              </div>
              <div className="flex justify-between">
                <span>expires_at:</span>
                <span className="text-muted-foreground">
                  {sqlResults.expires_at ? new Date(sqlResults.expires_at).toLocaleString('pt-BR') : 'null'}
                </span>
              </div>
            </div>
          ) : (
            <p className="text-muted-foreground">Carregando...</p>
          )}
        </CardContent>
      </Card>

      {/* Perfil Stack Auth */}
      {apiResults?.stack_profile && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Perfil Stack Auth (Database)</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span>subscription_tier:</span>
                <Badge variant={apiResults.stack_profile.subscription_tier === 'premium' ? "default" : "secondary"}>
                  {apiResults.stack_profile.subscription_tier}
                </Badge>
              </div>
              <div className="flex justify-between">
                <span>subscription_status:</span>
                <Badge variant={apiResults.stack_profile.subscription_status === 'active' ? "default" : "destructive"}>
                  {apiResults.stack_profile.subscription_status}
                </Badge>
              </div>
              <div className="flex justify-between">
                <span>expires:</span>
                <span className="text-muted-foreground">
                  {apiResults.stack_profile.subscription_current_period_end ? 
                    new Date(apiResults.stack_profile.subscription_current_period_end).toLocaleString('pt-BR') : 
                    'null'}
                </span>
              </div>
              <div className="flex justify-between">
                <span>days_remaining:</span>
                <Badge variant={apiResults.profile_analysis?.days_remaining > 0 ? "default" : "destructive"}>
                  {apiResults.profile_analysis?.days_remaining || 0} dias
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Recomendações */}
      {apiResults?.recommendations && apiResults.recommendations.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg flex items-center gap-2">
              <AlertCircle className="h-5 w-5" />
              Recomendações
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2">
              {apiResults.recommendations.map((rec: string, i: number) => (
                <li key={i} className="text-sm flex items-start gap-2">
                  <span className="text-muted-foreground">•</span>
                  <span>{rec}</span>
                </li>
              ))}
            </ul>
          </CardContent>
        </Card>
      )}

      {/* Ações */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Ações de Teste</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex flex-wrap gap-4">
            <Button onClick={fixPremium} disabled={loading || isPremiumActive}>
              {loading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Aplicando Fix...
                </>
              ) : (
                <>
                  <CheckCircle className="mr-2 h-4 w-4" />
                  Forçar Ativação Premium
                </>
              )}
            </Button>
            
            <Button 
              variant="outline" 
              onClick={() => window.open('/watch/1', '_blank')}
            >
              <Play className="mr-2 h-4 w-4" />
              Testar Player
            </Button>
          </div>
          
          {fixResults && (
            <div className={`p-4 rounded-lg ${fixResults.success ? 'bg-green-50' : 'bg-red-50'}`}>
              <h4 className="font-medium mb-2">Resultado do Fix:</h4>
              <pre className="text-xs overflow-auto">
                {JSON.stringify(fixResults, null, 2)}
              </pre>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Debug Raw Data */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Debug Raw Data</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <h4 className="font-medium mb-2">API Results:</h4>
              <pre className="text-xs bg-muted p-4 rounded-lg overflow-auto max-h-64">
                {JSON.stringify(apiResults, null, 2)}
              </pre>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}