'use client'

import { useState, useEffect } from 'react'
import { Card } from '@/components/ui/card'
import { Play } from 'lucide-react'
import Link from 'next/link'

interface Channel {
  id: string
  name: string
  description: string
  logo_url: string
  stream_url: string
  category: string
  rating: number
  is_premium: boolean
  viewer_count: number
}

export default function BrowseSimplePage() {
  const [channels, setChannels] = useState<Channel[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    loadChannels()
  }, [])

  async function loadChannels() {
    try {
      console.log('🔄 [BrowseSimple] Iniciando carregamento...')
      const response = await fetch('/api/iptv-simple')
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      
      const data = await response.json()
      console.log('📊 [BrowseSimple] Dados recebidos:', data)
      
      if (data.success && Array.isArray(data.data)) {
        // Mapear apenas os primeiros 20 canais para teste
        const mappedChannels = data.data.slice(0, 20).map((stream: any, index: number) => ({
          id: `iptv-${stream.uniqueId || stream.channel || index}`,
          name: stream.name || stream.channel || `Canal ${index + 1}`,
          description: stream.country ? `Canal deportivo de ${stream.country}` : 'Canal deportivo',
          logo_url: stream.logo || 'https://images.unsplash.com/photo-1574629810360-7efbbe195018?w=300&h=200&fit=crop',
          stream_url: stream.url || '',
          category: 'sports',
          rating: 4.5,
          is_premium: true,
          viewer_count: Math.floor(10000 + Math.random() * 50000)
        }))
        
        console.log('✅ [BrowseSimple] Canais mapeados:', mappedChannels.length)
        setChannels(mappedChannels)
      } else {
        throw new Error('Formato de dados inválido')
      }
    } catch (err) {
      console.error('❌ [BrowseSimple] Erro:', err)
      setError(err instanceof Error ? err.message : 'Erro desconhecido')
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-white text-xl">Carregando canais...</div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-red-500 text-xl">Erro: {error}</div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-black p-8">
      <h1 className="text-4xl font-bold text-white mb-8">
        Canales Deportivos en Vivo ({channels.length})
      </h1>
      
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
        {channels.map((channel) => (
          <Link key={channel.id} href={`/watch/${channel.id}`}>
            <Card className="group cursor-pointer overflow-hidden bg-gray-900 border-gray-800 hover:border-red-500 transition-all">
              <div className="relative aspect-video bg-gray-800">
                <img 
                  src={channel.logo_url} 
                  alt={channel.name}
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    (e.target as HTMLImageElement).src = 'https://images.unsplash.com/photo-1574629810360-7efbbe195018?w=600&h=400&fit=crop'
                  }}
                />
                <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                  <div className="bg-white rounded-full p-3">
                    <Play className="h-6 w-6 text-black fill-black" />
                  </div>
                </div>
              </div>
              
              <div className="p-4">
                <h3 className="text-white font-semibold text-sm line-clamp-1">
                  {channel.name}
                </h3>
                <p className="text-gray-400 text-xs mt-1">
                  {channel.description}
                </p>
                <div className="flex items-center justify-between mt-2">
                  <span className="text-xs text-gray-500">
                    {channel.viewer_count.toLocaleString()} espectadores
                  </span>
                  <span className="text-xs text-yellow-500">
                    ★ {channel.rating}
                  </span>
                </div>
              </div>
            </Card>
          </Link>
        ))}
      </div>
    </div>
  )
}