'use client'

import { useState, useEffect } from 'react'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { Input } from '@/components/ui/input'
import { Play, Search, Heart, Star, Users, Tv, Zap, Trophy, X, RefreshCw, Smartphone } from 'lucide-react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { createDatabaseClient } from '@/lib/supabase/database-only'
import { toast } from 'sonner'
import { useAuth } from '@/hooks/use-auth'
import { ChannelBadge } from '@/components/ui/channel-badge'
import { getChannelStatus, getChannelCategory } from '@/lib/channel-status'
import { StripeDebugPanel } from '@/components/debug/stripe-debug-panel'
import { getChannel<PERSON>ogo } from '@/lib/channel-logos'
import { fetch<PERSON><PERSON><PERSON><PERSON><PERSON>, preload<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, getChan<PERSON><PERSON><PERSON>WithCache } from '@/services/logo-service'
import { getChannelLogoPath } from '@/lib/channel-logo-mapping'

interface Channel {
  id: string
  name: string
  description: string
  logo_url: string
  stream_url: string
  category: string
  rating: number
  is_premium: boolean
  viewer_count: number
  thumbnail?: string
  headers?: {
    'Referer'?: string
    'User-Agent'?: string
  }
  isIPTV?: boolean
  isLiveGame?: boolean
  isConfirmedWorking?: boolean
  requiresProxy?: boolean
}

// Remover dados mock - usar dados reais das APIs

const categories = [
  { id: 'all', name: 'Todos', icon: '🎬', gradient: 'from-purple-500 to-pink-500' }
]

export default function BrowsePage() {
  const router = useRouter()
  const [channels, setChannels] = useState<Channel[]>([])
  
  // Debug: monitorar mudanças nos canais
  useEffect(() => {
    console.log('📊 [Browse] Estado channels atualizado:', channels.length, 'canais')
  }, [channels])
  const [favorites, setFavorites] = useState<string[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [loading, setLoading] = useState(false)
  
  const supabase = createDatabaseClient()
  const { user, isLoading: authLoading, isAuthenticated } = useAuth()
  
  // Debug auth
  useEffect(() => {
    console.log('🔐 [Browse] Estado de autenticação:', {
      user,
      isAuthenticated,
      authLoading,
      localStorage: localStorage.getItem('streamplus-user')
    });
  }, [user, isAuthenticated, authLoading]);
  
  // Cargar canales reales y favoritos del usuario
  useEffect(() => {
    console.log('🔄 [Browse] useEffect inicial executado')
    let mounted = true
    let loadingStarted = false
    
    const load = async () => {
      if (mounted && !loadingStarted) {
        loadingStarted = true
        console.log('📡 [Browse] Iniciando carregamento de dados...')
        await loadFavorites()
        await loadRealChannels()
      }
    }
    
    // Pequeno delay para garantir que o componente está montado
    const timer = setTimeout(() => {
      load()
    }, 100)
    
    // Cleanup function
    return () => {
      console.log('🧹 [Browse] Cleanup do useEffect')
      mounted = false
      clearTimeout(timer)
    }
  }, [])
  
  // Cargar canales reales y favoritos del usuario
  async function loadRealChannels() {
    console.log('🔄 [Browse] Iniciando carregamento de canais IPTV...')
    
    if (loading) {
      console.log('⚠️ [Browse] Já está carregando, ignorando chamada duplicada')
      return
    }
    
    setLoading(true)
    try {
      // Carregar canais da API IPTV (68 canais selecionados)
      const response = await fetch('/api/iptv-simple')
      console.log('📡 [Browse] API Response status:', response.status)
      console.log('📡 [Browse] API Response headers:', response.headers)
      
      const result = await response.json()
      console.log('📡 [Browse] API Result:', result)
      
      if (result.success && result.data) {
        console.log('📺 [Browse] Canais IPTV carregados:', result.total)
        console.log('📺 [Browse] Raw channels data:', result.data)
        console.log('📺 [Browse] First channel raw:', result.data[0])
        
        const formattedChannels: Channel[] = result.data.map((stream: any, index: number) => {
          // Log detalhado para debug
          if (index < 5 || stream.channel === '30AGolfKingdom.us' || stream.uniqueId === '30AGolfKingdom.us') {
            console.log(`📺 [Browse] Raw stream ${index}:`, {
              channel: stream.channel,
              uniqueId: stream.uniqueId,
              name: stream.name,
              url: stream.url
            })
          }
          
          const channelId = stream.channel || stream.uniqueId
          const channelLogo = getChannelLogoWithCache(channelId)
          const formatted = {
            id: channelId,
            name: stream.name || stream.channel,
            description: stream.categories?.join(', ') || 'Deportes',
            logo_url: channelLogo,
            thumbnail: channelLogo,
            stream_url: stream.url,
            category: 'sports',
            rating: 4.5 + Math.random() * 0.5,
            is_premium: false,
            viewer_count: Math.floor(10000 + Math.random() * 40000),
            isIPTV: true,
            requiresProxy: stream.url?.includes('moveonjoy.com') || false
          }
          
          if (index < 5 || channelId === '30AGolfKingdom.us') {
            console.log(`📺 [Browse] Channel ${index} formatted:`, {
              id: formatted.id,
              name: formatted.name,
              idType: typeof formatted.id
            })
          }
          
          return formatted
        })
        
        console.log('📺 [Browse] Total de canais IPTV formatados:', formattedChannels.length)
        console.log('📺 [Browse] Channel IDs:', formattedChannels.map(ch => ch.id))
        console.log('✅ [Browse] Canais carregados com sucesso')
        
        setChannels(formattedChannels)
        
        // Pré-carregar logos em background
        const channelIds = formattedChannels.map(ch => ch.id)
        preloadChannelLogos(channelIds).then(() => {
          console.log('📺 [Browse] Logos pré-carregados com sucesso')
          // Forçar re-render após carregar logos
          setChannels([...formattedChannels])
        })
      } else {
        console.error('❌ [Browse] Failed to load channels:', result)
      }
      
    } catch (error) {
      console.error('Error al cargar canales:', error)
      toast.error('Error al cargar canales en vivo')
    } finally {
      setLoading(false)
    }
  }
  
  async function loadFavorites() {
    if (!user) return
    
    const { data } = await supabase
      .from('favorites')
      .select('channel_id')
      .eq('user_id', user.id)
    
    if (data) {
      setFavorites(data.map(f => f.channel_id))
    }
  }
  
  async function toggleFavorite(channelId: string, e: React.MouseEvent) {
    e.stopPropagation()
    
    if (!user) {
      toast.error('Debes iniciar sesión para añadir favoritos')
      router.push('/login?redirect=/browse')
      return
    }
    
    if (favorites.includes(channelId)) {
      // Quitar de favoritos
      await supabase
        .from('favorites')
        .delete()
        .eq('user_id', user.id)
        .eq('channel_id', channelId)
      
      setFavorites(favorites.filter(id => id !== channelId))
      toast.success('Eliminado de favoritos')
    } else {
      // Añadir a favoritos
      await supabase
        .from('favorites')
        .insert({
          user_id: user.id,
          channel_id: channelId
        })
      
      setFavorites([...favorites, channelId])
      toast.success('Añadido a favoritos')
    }
  }
  
  const filteredChannels = channels
    .filter(channel => {
      const matchesSearch = channel.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           channel.description.toLowerCase().includes(searchTerm.toLowerCase())
      const matchesCategory = selectedCategory === 'all' || channel.category === selectedCategory
      
      return matchesSearch && matchesCategory
    })
    // Canais já vêm ordenados por prioridade do getMainChannels()
  
  console.log('🔍 [Browse] Filtragem:', {
    totalChannels: channels.length,
    filteredChannels: filteredChannels.length,
    searchTerm,
    selectedCategory
  })
  
  function handleWatchChannel(channel: Channel) {
    console.log('🎯 [Browse] Navegando para canal:', channel.id, channel.name)
    console.log('🎯 [Browse] Channel object:', channel)
    console.log('🎯 [Browse] Channel ID type:', typeof channel.id)
    console.log('🎯 [Browse] Channel is premium:', channel.is_premium)
    console.log('🎯 [Browse] URL being navigated to:', `/watch/${channel.id}`)
    
    // Todos os canais vão para o watch - o trial/assinatura é verificado lá
    router.push(`/watch/${channel.id}`)
  }
  
  async function checkSubscriptionAndWatch(channel: Channel) {
    if (!user) {
      toast.error('Inicia sesión para ver canales premium')
      router.push('/login?redirect=/browse')
      return
    }
    
    // Aquí verificaríamos la suscripción del usuario
    // Por ahora, asumimos que tiene acceso
    console.log('🔐 Usuario autenticado, navegando para:', `/watch/${channel.id}`)
    router.push(`/watch/${channel.id}`)
  }
  
  return (
    <div className="min-h-screen bg-black overflow-x-hidden">
      {/* Hero Section com design moderno */}
      <section className="relative w-full overflow-hidden">
        {/* Background animado em toda largura */}
        <div className="pointer-events-none absolute inset-0 -z-10">
          <div className="absolute inset-0 bg-gradient-to-br from-red-900/20 via-black to-orange-900/20" />
          <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top_right,_var(--tw-gradient-stops))] from-red-600/30 via-transparent to-transparent" />
          <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_bottom_left,_var(--tw-gradient-stops))] from-orange-600/30 via-transparent to-transparent" />
          <div className="absolute inset-0 bg-[radial-gradient(circle_at_center,_rgba(255,60,0,0.15)_0%,transparent_70%)]" />
        </div>
        
        {/* Conteúdo centralizado com largura máxima */}
        <div className="relative z-10 mx-auto max-w-7xl py-16 px-4 sm:px-6 lg:px-8">
            {/* Título com melhor tipografia */}
            <div className="text-center space-y-4 mb-12">
              <h1 className="text-5xl sm:text-6xl font-black text-white tracking-tight">
                <span className="bg-gradient-to-r from-red-500 to-orange-500 bg-clip-text text-transparent">
                  StreamPlus
                </span>
                <span className="block text-3xl sm:text-4xl mt-2 font-bold text-gray-100">
                  Deportes en Vivo
                </span>
              </h1>
              <div className="text-lg sm:text-xl text-gray-400 max-w-2xl mx-auto">
                {loading ? (
                  <span className="inline-flex items-center gap-2">
                    <div className="w-4 h-4 border-2 border-red-500 border-t-transparent rounded-full animate-spin" />
                    Cargando canales...
                  </span>
                ) : (
                  <span>
                    <span className="text-red-500 font-semibold">{filteredChannels.length}</span> canales disponibles
                  </span>
                )}
              </div>
            </div>
            
            {/* Búsqueda mejorada */}
            <div className="max-w-3xl mx-auto">
              <div className="relative group">
                <div className="absolute inset-0 bg-gradient-to-r from-red-600 to-orange-600 rounded-2xl blur-xl opacity-20 group-hover:opacity-30 transition-opacity" />
                <div className="relative flex items-center">
                  <Search className="absolute left-6 text-gray-500 h-5 w-5 pointer-events-none" />
                  <Input
                    placeholder="Buscar canales, deportes, equipos..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-14 pr-6 py-4 text-base sm:text-lg bg-gray-900/90 backdrop-blur-xl border-gray-800 rounded-2xl text-white placeholder:text-gray-500 focus:border-red-500/50 focus:ring-2 focus:ring-red-500/20 transition-all"
                  />
                  {searchTerm && (
                    <button
                      onClick={() => setSearchTerm('')}
                      className="absolute right-6 text-gray-500 hover:text-gray-300 transition-colors"
                    >
                      <X className="h-5 w-5" />
                    </button>
                  )}
                </div>
              </div>
            </div>
        </div>
      </section>
      
      {/* Categorías mejoradas con sticky */}
      <section className="sticky top-0 z-20 w-full bg-black/90 backdrop-blur-xl border-b border-gray-900">
        <div className="px-4 sm:px-6 lg:px-8">
          <div className="flex items-center gap-6 overflow-x-auto scrollbar-hide py-6">
            {categories.map(category => (
              <button
                key={category.id}
                onClick={() => setSelectedCategory(category.id)}
                className={`
                  relative px-5 py-2.5 rounded-full font-medium transition-all duration-300 whitespace-nowrap
                  ${selectedCategory === category.id 
                    ? 'bg-gradient-to-r text-white shadow-lg shadow-red-500/25 scale-105' 
                    : 'bg-gray-900/50 text-gray-400 hover:bg-gray-800/50 hover:text-gray-200'
                  }
                `}
                style={{
                  backgroundImage: selectedCategory === category.id 
                    ? `linear-gradient(to right, var(--tw-gradient-stops))` 
                    : undefined,
                  ...(selectedCategory === category.id && {
                    backgroundImage: `linear-gradient(to right, ${category.gradient.replace('from-', '').replace('to-', ', ')})`
                  })
                }}
              >
                <span className="flex items-center gap-2 text-sm">
                  <span className="text-lg">{category.icon}</span>
                  <span className="font-semibold">{category.name}</span>
                </span>
              </button>
            ))}
          </div>
        </div>
      </section>
      
      {/* Grid de canales con mejor diseño */}
      <section className="py-8 bg-gradient-to-b from-gray-950 to-slate-950">
        <div className="px-4 sm:px-6 lg:px-8">
          {/* Loading state */}
          {loading && (
            <div className="grid gap-3 sm:gap-4 lg:gap-5" style={{
              gridTemplateColumns: 'repeat(auto-fill, minmax(240px, 1fr))'
            }}>
              {[...Array(12)].map((_, i) => (
                <div key={i} className="animate-pulse">
                  <div className="bg-gray-900 rounded-xl overflow-hidden">
                    <div className="aspect-video bg-gray-800" />
                    <div className="p-3 space-y-2">
                      <div className="h-4 bg-gray-800 rounded w-3/4" />
                      <div className="h-3 bg-gray-800 rounded w-1/2" />
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
          
          {/* Canales */}
          {!loading && (
            <>
              {filteredChannels.length === 0 ? (
                <div className="text-center py-20">
                  <p className="text-gray-400 text-lg">No se encontraron canales deportivos</p>
                </div>
              ) : (
                <div className="grid gap-3 sm:gap-4 lg:gap-5" style={{
                  gridTemplateColumns: 'repeat(auto-fill, minmax(240px, 1fr))'
                }}>
                {filteredChannels.map((channel) => (
                  <Card 
                key={channel.id} 
                className="group relative cursor-pointer overflow-hidden bg-gray-900/50 border-gray-800 hover:border-red-500/50 transition-all duration-500 hover:transform hover:scale-[1.02] hover:shadow-2xl hover:shadow-red-500/10"
                onClick={() => handleWatchChannel(channel)}
              >
                {/* Channel badges */}
                <div className="absolute top-3 left-3 right-3 z-10">
                  <ChannelBadge 
                    status={getChannelStatus(channel.id)?.status || (channel.isConfirmedWorking ? 'working' : undefined)}
                    category={getChannelStatus(channel.id)?.category || getChannelCategory(channel.id) as any}
                    quality={getChannelStatus(channel.id)?.quality}
                    isLive={channel.isLiveGame}
                  />
                </div>
                
                {/* Thumbnail mejorado */}
                <div className="relative aspect-video overflow-hidden bg-gray-800">
                  <img 
                    src={getChannelLogoPath(channel.name) || channel.thumbnail || channel.logo_url} 
                    alt={channel.name}
                    className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700 ease-out"
                    loading="lazy"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement
                      // Se a logo local falhou, tentar logos online
                      const channelName = channel.name.toLowerCase()
                      if (channelName.includes('espn') && !target.src.includes('unsplash')) {
                        target.src = 'https://upload.wikimedia.org/wikipedia/commons/thumb/2/2f/ESPN_wordmark.svg/2560px-ESPN_wordmark.svg.png'
                      } else if (channelName.includes('fox') && !target.src.includes('unsplash')) {
                        target.src = 'https://upload.wikimedia.org/wikipedia/commons/thumb/3/3a/Fox_Sports_1_logo.svg/2560px-Fox_Sports_1_logo.svg.png'
                      } else if (channelName.includes('nbc') && !target.src.includes('unsplash')) {
                        target.src = 'https://upload.wikimedia.org/wikipedia/commons/thumb/f/f2/NBC_Sports_2012.svg/2560px-NBC_Sports_2012.svg.png'
                      } else {
                        // Fallback final - imagem de esportes variada
                        const sportImages = [
                          'https://images.unsplash.com/photo-1461896836934-ffe607ba8211?w=600&h=400&fit=crop&q=80', // Atletismo
                          'https://images.unsplash.com/photo-1552674605-db6ffd4facb5?w=600&h=400&fit=crop&q=80', // Corrida
                          'https://images.unsplash.com/photo-1518611012118-696072aa579a?w=600&h=400&fit=crop&q=80', // Baseball
                          'https://images.unsplash.com/photo-1546519638-68e109498ffc?w=600&h=400&fit=crop&q=80', // Basketball
                          'https://images.unsplash.com/photo-1459865264687-595d652de67e?w=600&h=400&fit=crop&q=80', // Esportes
                          'https://images.unsplash.com/photo-1577223625816-7546f13df25d?w=600&h=400&fit=crop&q=80'  // Tênis
                        ]
                        const randomIndex = Math.floor(Math.random() * sportImages.length)
                        target.src = sportImages[randomIndex]
                      }
                    }}
                  />
                  
                  {/* Gradiente overlay mejorado */}
                  <div className="absolute inset-0 bg-gradient-to-t from-black via-black/60 to-transparent opacity-80" />
                  
                  {/* Premium Badge mejorado */}
                  {channel.is_premium && !channel.isLiveGame && (
                    <div className="absolute top-3 left-3 z-10">
                      <Badge className="bg-gradient-to-r from-yellow-500 to-amber-600 border-0 text-xs font-bold shadow-lg">
                        <Trophy className="h-3 w-3 mr-1" />
                        PREMIUM
                      </Badge>
                    </div>
                  )}
                  
                  {/* Viewers count mejorado */}
                  <div className="absolute top-3 right-3 bg-black/70 backdrop-blur-md px-3 py-1 rounded-full flex items-center gap-2">
                    <Users className="h-3 w-3 text-red-500" />
                    <span className="text-white text-xs font-medium">{channel.viewer_count.toLocaleString()}</span>
                  </div>
                  
                  {/* Play button overlay */}
                  <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <div className="bg-white/90 backdrop-blur-sm rounded-full p-3 transform scale-90 group-hover:scale-100 transition-transform duration-300">
                      <Play className="h-5 w-5 text-black fill-black" />
                    </div>
                  </div>
                </div>
                
                {/* Channel info redesenhado */}
                <div className="p-3 space-y-2">
                  <div>
                    <h3 className="text-white font-semibold text-sm line-clamp-1 group-hover:text-red-400 transition-colors">
                      {channel.name}
                    </h3>
                    <p className="text-gray-500 text-xs line-clamp-1 mt-1">
                      {channel.description}
                    </p>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="flex items-center gap-1">
                        <Star className="h-3 w-3 text-yellow-500 fill-yellow-500" />
                        <span className="text-gray-300 text-xs font-medium">{channel.rating.toFixed(1)}</span>
                      </div>
                      <span className="text-gray-600 text-[10px]">
                        {categories.find(c => c.id === channel.category)?.name || channel.category}
                      </span>
                    </div>
                    
                    <button
                      onClick={(e) => toggleFavorite(channel.id, e)}
                      className="p-1.5 rounded-full hover:bg-gray-800 transition-colors group/btn"
                    >
                      <Heart
                        className={`h-3.5 w-3.5 transition-all duration-300 ${
                          favorites.includes(channel.id) 
                            ? 'fill-red-500 text-red-500 scale-110' 
                            : 'text-gray-400 group-hover/btn:text-red-400'
                        }`}
                      />
                    </button>
                  </div>
                </div>
              </Card>
                ))}
                </div>
              )}
            </>
          )}
        </div>
      </section>
      
      {/* Estado vacío redesenhado - Agora só aparece quando o searchTerm tem valor */}
      {!loading && filteredChannels.length === 0 && searchTerm && (
        <section className="py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-md mx-auto text-center">
            <div className="bg-gray-900/50 backdrop-blur-xl rounded-3xl p-12 border border-gray-800">
              <div className="w-20 h-20 bg-gray-800 rounded-full flex items-center justify-center mx-auto mb-6">
                <Tv className="h-10 w-10 text-gray-600" />
              </div>
              <h3 className="text-2xl font-bold text-white mb-3">
                No encontramos canales
              </h3>
              <p className="text-gray-400 mb-8 text-base">
                Intenta buscar con otros términos o explora nuestras categorías
              </p>
              <div className="space-y-3">
                <Button
                  onClick={() => {
                    setSearchTerm('')
                    setSelectedCategory('all')
                  }}
                  className="w-full bg-red-600 hover:bg-red-700 text-white font-medium py-3"
                >
                  <RefreshCw className="mr-2 h-4 w-4" />
                  Limpiar filtros
                </Button>
                <Button
                  variant="outline"
                  onClick={loadRealChannels}
                  className="w-full border-gray-700 text-gray-300 hover:bg-gray-800 hover:text-white py-3"
                >
                  Recargar canales
                </Button>
              </div>
            </div>
          </div>
          </div>
        </section>
      )}
      
      {/* CTA Section redesenhado */}
      {!loading && filteredChannels.length > 0 && (
        <section className="relative w-full overflow-hidden py-16">
          {/* Background gradient para CTA */}
          <div className="pointer-events-none absolute inset-0 -z-10">
            <div className="absolute inset-0 bg-gradient-to-br from-red-600/10 via-transparent to-orange-600/10" />
          </div>
          
          <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="relative overflow-hidden rounded-3xl bg-gray-900/50 border border-gray-800">
              {/* Background gradient interno */}
              <div className="absolute inset-0 bg-gradient-to-br from-red-600/20 via-orange-600/10 to-red-600/20" />
              
              <div className="relative p-8 sm:p-12">
                <div className="grid lg:grid-cols-2 gap-8 items-center">
                  <div className="space-y-4">
                    <div className="inline-flex items-center gap-2 bg-red-500/20 text-red-400 px-4 py-2 rounded-full text-sm font-medium">
                      <Zap className="h-4 w-4" />
                      Oferta especial
                    </div>
                    <h3 className="text-3xl sm:text-4xl font-bold text-white">
                      Lleva la experiencia al siguiente nivel
                    </h3>
                    <p className="text-lg text-gray-300">
                      Descarga nuestra app móvil y disfruta de más de 100 canales premium con 1 mes completamente gratis
                    </p>
                    <div className="flex flex-col sm:flex-row gap-4 pt-4">
                      <Button size="lg" className="bg-gradient-to-r from-red-600 to-orange-600 hover:from-red-700 hover:to-orange-700 text-white font-semibold shadow-lg shadow-red-500/25" asChild>
                        <Link href="/mobile-app">
                          <Smartphone className="mr-2 h-5 w-5" />
                          Descargar App
                        </Link>
                      </Button>
                      <Button size="lg" variant="outline" className="border-gray-700 text-gray-300 hover:bg-gray-800 hover:text-white" asChild>
                        <Link href="/subscription">
                          Ver planes
                        </Link>
                      </Button>
                    </div>
                  </div>
                  <div className="hidden lg:flex justify-center">
                    <div className="relative">
                      <div className="absolute inset-0 bg-gradient-to-r from-red-600 to-orange-600 rounded-full blur-3xl opacity-20" />
                      <div className="relative bg-gray-900 rounded-3xl p-8 border border-gray-800">
                        <div className="grid grid-cols-2 gap-4">
                          <div className="bg-gray-800 rounded-xl p-4 text-center">
                            <Tv className="h-8 w-8 text-red-500 mx-auto mb-2" />
                            <div className="text-2xl font-bold text-white">100+</div>
                            <div className="text-sm text-gray-400">Canales HD</div>
                          </div>
                          <div className="bg-gray-800 rounded-xl p-4 text-center">
                            <Trophy className="h-8 w-8 text-yellow-500 mx-auto mb-2" />
                            <div className="text-2xl font-bold text-white">Premium</div>
                            <div className="text-sm text-gray-400">Contenido</div>
                          </div>
                          <div className="bg-gray-800 rounded-xl p-4 text-center">
                            <Smartphone className="h-8 w-8 text-blue-500 mx-auto mb-2" />
                            <div className="text-2xl font-bold text-white">Apps</div>
                            <div className="text-sm text-gray-400">iOS y Android</div>
                          </div>
                          <div className="bg-gray-800 rounded-xl p-4 text-center">
                            <Users className="h-8 w-8 text-green-500 mx-auto mb-2" />
                            <div className="text-2xl font-bold text-white">24/7</div>
                            <div className="text-sm text-gray-400">Soporte</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
      )}
      
      {/* Debug Panel - só aparece em desenvolvimento */}
      <StripeDebugPanel />
    </div>
  )
}