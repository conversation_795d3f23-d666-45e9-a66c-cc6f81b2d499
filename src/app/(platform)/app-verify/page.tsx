'use client'

import { useEffect, useState } from 'react'
import { useSearchParams, useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2, CheckCircle, AlertCircle, Tv, Calendar, Smartphone, Shield } from 'lucide-react'
import { toast } from 'sonner'
import confetti from 'canvas-confetti'

export default function AppVerifyPage() {
  const searchParams = useSearchParams()
  const router = useRouter()
  const token = searchParams.get('token')
  
  const [verifying, setVerifying] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState(false)
  const [countdown, setCountdown] = useState(10)

  useEffect(() => {
    if (!token) {
      setError('Token no encontrado')
      setVerifying(false)
      return
    }

    verifyToken()
  }, [token])

  useEffect(() => {
    if (success && countdown > 0) {
      const timer = setTimeout(() => {
        setCountdown(countdown - 1)
      }, 1000)
      return () => clearTimeout(timer)
    }
    
    if (success && countdown === 0) {
      router.push('/browse')
    }
  }, [success, countdown, router])

  const verifyToken = async () => {
    try {
      const response = await fetch('/api/app/verify-token', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ token })
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Error al verificar el token')
      }

      // Salvar no localStorage
      if (data.session_id) {
        localStorage.setItem('app_session_id', data.session_id)
        localStorage.setItem('app_expires_at', data.expires_at)
      }

      // Mostrar confetti
      confetti({
        particleCount: 100,
        spread: 70,
        origin: { y: 0.6 }
      })

      setSuccess(true)
      toast.success('¡Activación exitosa!')
      
    } catch (err) {
      console.error('Error:', err)
      setError(err instanceof Error ? err.message : 'Error al verificar el token')
    } finally {
      setVerifying(false)
    }
  }

  if (verifying) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4 bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900">
        <Card className="max-w-md w-full bg-slate-800/50 border-slate-700">
          <CardContent className="py-12">
            <div className="text-center">
              <Loader2 className="w-12 h-12 animate-spin mx-auto mb-4 text-primary" />
              <h3 className="text-lg font-semibold text-white">Verificando tu acceso...</h3>
              <p className="text-slate-300 mt-2">
                Por favor espera mientras activamos tu mes gratis
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4 bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900">
        <Card className="max-w-md w-full bg-slate-800/50 border-slate-700">
          <CardHeader>
            <CardTitle className="text-red-400 flex items-center gap-2">
              <AlertCircle className="h-6 w-6" />
              Error de Verificación
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <Alert variant="destructive" className="bg-red-900/20 border-red-800">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
            
            <div className="text-slate-300 text-sm">
              <p>Posibles causas:</p>
              <ul className="list-disc list-inside mt-2 space-y-1">
                <li>El enlace ya fue utilizado</li>
                <li>El enlace expiró (válido por 1 hora)</li>
                <li>El enlace es incorrecto</li>
              </ul>
            </div>
            
            <Button 
              onClick={() => router.push('/')}
              className="w-full"
              variant="outline"
            >
              Volver al inicio
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (success) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4 bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900">
        <Card className="max-w-2xl w-full bg-gradient-to-br from-slate-800/90 to-slate-900/90 border-green-600/20 shadow-2xl">
          <CardContent className="py-12">
            <div className="text-center space-y-6">
              {/* Icono de éxito */}
              <div className="relative mx-auto w-24 h-24">
                <div className="absolute inset-0 bg-green-500/20 rounded-full animate-ping" />
                <div className="relative bg-gradient-to-br from-green-500 to-green-600 rounded-full p-6">
                  <CheckCircle className="w-12 h-12 text-white" />
                </div>
              </div>

              {/* Título principal */}
              <div>
                <h1 className="text-4xl font-bold text-white mb-2">
                  ¡Felicidades!
                </h1>
                <h2 className="text-2xl text-green-400">
                  Tu mes gratis ha sido activado
                </h2>
              </div>

              {/* Descripción */}
              <p className="text-lg text-slate-300 max-w-md mx-auto">
                Disfruta de acceso completo a todos nuestros canales premium, 
                eventos deportivos en vivo y contenido exclusivo durante 30 días.
              </p>

              {/* Beneficios */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 max-w-xl mx-auto">
                <div className="bg-slate-800/50 rounded-lg p-4 border border-slate-700">
                  <Tv className="w-8 h-8 text-green-400 mx-auto mb-2" />
                  <p className="text-sm text-slate-300">+100 Canales HD</p>
                </div>
                <div className="bg-slate-800/50 rounded-lg p-4 border border-slate-700">
                  <Calendar className="w-8 h-8 text-green-400 mx-auto mb-2" />
                  <p className="text-sm text-slate-300">30 Días Gratis</p>
                </div>
                <div className="bg-slate-800/50 rounded-lg p-4 border border-slate-700">
                  <Smartphone className="w-8 h-8 text-green-400 mx-auto mb-2" />
                  <p className="text-sm text-slate-300">Multi-dispositivo</p>
                </div>
                <div className="bg-slate-800/50 rounded-lg p-4 border border-slate-700">
                  <Shield className="w-8 h-8 text-green-400 mx-auto mb-2" />
                  <p className="text-sm text-slate-300">Sin Anuncios</p>
                </div>
              </div>

              {/* Countdown */}
              <div className="space-y-4">
                <p className="text-slate-400">
                  Redirigiendo al catálogo en {countdown} segundos...
                </p>
                
                <Button 
                  onClick={() => router.push('/browse')}
                  size="lg"
                  className="bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white"
                >
                  Ir al Catálogo Ahora
                </Button>
              </div>

              {/* Nota informativa */}
              <div className="mt-8 p-4 bg-blue-900/20 rounded-lg border border-blue-800/30">
                <p className="text-sm text-blue-300">
                  💡 <strong>Consejo:</strong> Guarda este sitio en tus favoritos para acceder fácilmente. 
                  Tu acceso premium estará activo hasta el{' '}
                  <strong>{new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toLocaleDateString('es-ES')}</strong>
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return null
}