'use client'

import { useState, useEffect } from 'react'
import { useSearchParams, useRouter } from 'next/navigation'
import { useAuthOptional } from '@/hooks/use-auth'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2, Copy, CheckCircle, Smartphone, AlertCircle } from 'lucide-react'
import { toast } from 'sonner'

export default function AppRedirectPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const { user } = useAuthOptional()
  const [verificationCode, setVerificationCode] = useState<string>('')
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState(false)
  const [copied, setCopied] = useState(false)

  // Verificar se veio do app
  const fromApp = searchParams.get('source') === 'app'
  const appPlatform = searchParams.get('platform') || 'unknown'
  const [generatingToken, setGeneratingToken] = useState(false)

  useEffect(() => {
    // Se veio do app e tem usuário, gerar token de sessão único
    if (fromApp && user) {
      generateSessionToken()
    } else if (!fromApp && user) {
      generateVerificationCode()
    }
  }, [user, fromApp])

  const generateVerificationCode = async () => {
    if (!user) {
      setError('Por favor, inicia sesión primero')
      setLoading(false)
      return
    }

    try {
      setLoading(true)
      setError(null)

      // Gerar código de verificação
      const response = await fetch('/api/app-verification/generate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          userId: user.id,
          platform: appPlatform
        })
      })

      const data = await response.json()

      if (!response.ok) {
        // Se já ativou antes, mostrar mensagem e redirecionar
        if (data.error && data.error.includes('anteriormente')) {
          setError(data.error)
          setLoading(false)
          setTimeout(() => {
            router.push('/browse')
          }, 3000)
          return
        }
        throw new Error(data.error || 'Error al generar código')
      }

      setVerificationCode(data.code)
      
      // Si viene de la app, activar automáticamente después de 2 segundos
      if (fromApp) {
        setTimeout(() => {
          activateAppAccess(data.code)
        }, 2000)
      }

    } catch (err) {
      console.error('Erro ao gerar código:', err)
      setError(err instanceof Error ? err.message : 'Erro ao gerar código de verificação')
    } finally {
      setLoading(false)
    }
  }

  const activateAppAccess = async (code?: string) => {
    const codeToUse = code || verificationCode
    
    if (!codeToUse) {
      setError('Código de verificación no encontrado')
      return
    }

    try {
      setLoading(true)
      setError(null)

      const response = await fetch('/api/app-verification/activate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          userId: user?.id,
          code: codeToUse
        })
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Erro ao ativar acesso')
      }

      setSuccess(true)
      toast.success('¡Acceso activado con éxito! Has ganado 30 días gratis!')

      // Redirecionar para o browse após 3 segundos
      setTimeout(() => {
        router.push('/browse')
      }, 3000)

    } catch (err) {
      console.error('Erro ao ativar:', err)
      setError(err instanceof Error ? err.message : 'Erro ao ativar acesso')
    } finally {
      setLoading(false)
    }
  }

  const generateSessionToken = async () => {
    try {
      setGeneratingToken(true)
      setError(null)

      // Gerar token de sessão único
      const response = await fetch('/api/app-verification/generate-session-token', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          userId: user.id,
          platform: appPlatform
        })
      })

      const data = await response.json()

      if (!response.ok) {
        // Se já ativou antes, mostrar mensagem
        if (data.error && data.error.includes('anteriormente')) {
          setError(data.error)
          setTimeout(() => {
            router.push('/browse')
          }, 3000)
          return
        }
        throw new Error(data.error || 'Error al generar token')
      }

      // Redirecionar para a página de verificação com o token único
      router.push(`/app-redirect/${data.token}`)

    } catch (err) {
      console.error('Error al generar token:', err)
      setError(err instanceof Error ? err.message : 'Error al generar token de sesión')
    } finally {
      setGeneratingToken(false)
    }
  }

  const copyCode = () => {
    if (verificationCode) {
      navigator.clipboard.writeText(verificationCode)
      setCopied(true)
      toast.success('¡Código copiado!')
      setTimeout(() => setCopied(false), 2000)
    }
  }

  if (!user) {
    return (
      <div className="container mx-auto p-8 max-w-2xl">
        <Card>
          <CardHeader>
            <CardTitle>Inicio de Sesión Necesario</CardTitle>
            <CardDescription>
              Por favor, inicia sesión para continuar con la verificación de la aplicación
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={() => router.push('/login')} className="w-full">
              Iniciar Sesión
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="container mx-auto p-8 max-w-2xl">
      <Card className="shadow-xl">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 w-20 h-20 bg-primary/10 rounded-full flex items-center justify-center">
            <Smartphone className="w-10 h-10 text-primary" />
          </div>
          <CardTitle className="text-3xl">
            {fromApp ? '¡Bienvenido desde la App!' : 'Verificación de la Aplicación'}
          </CardTitle>
          <CardDescription className="text-lg">
            {fromApp 
              ? 'Estamos activando tu acceso premium por 30 días...' 
              : 'Usa el código a continuación para activar tu cuenta en la aplicación'}
          </CardDescription>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Status do usuário */}
          <div className="flex items-center justify-between p-4 bg-muted rounded-lg">
            <div>
              <p className="text-sm text-muted-foreground">Conta conectada</p>
              <p className="font-medium">{user.email}</p>
            </div>
            <Badge variant="outline">
              {appPlatform === 'ios' ? 'iOS' : appPlatform === 'android' ? 'Android' : 'App'}
            </Badge>
          </div>

          {/* Código de verificação */}
          {loading && !success ? (
            <div className="text-center py-8">
              <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4" />
              <p className="text-muted-foreground">
                {fromApp ? 'Activando tu acceso...' : 'Generando código...'}
              </p>
            </div>
          ) : error ? (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          ) : success ? (
            <div className="text-center py-8">
              <CheckCircle className="w-16 h-16 text-green-500 mx-auto mb-4" />
              <h3 className="text-xl font-semibold mb-2">¡Acceso Activado!</h3>
              <p className="text-muted-foreground mb-4">
                ¡Has ganado 30 días de acceso premium gratis!
              </p>
              <p className="text-sm text-muted-foreground">
                Redirigiendo al catálogo...
              </p>
            </div>
          ) : (
            <>
              <div className="space-y-4">
                <div className="relative">
                  <Input
                    value={verificationCode}
                    readOnly
                    className="text-center text-2xl font-mono pr-12 py-6"
                  />
                  <Button
                    size="icon"
                    variant="ghost"
                    className="absolute right-2 top-1/2 -translate-y-1/2"
                    onClick={copyCode}
                  >
                    {copied ? (
                      <CheckCircle className="h-5 w-5 text-green-500" />
                    ) : (
                      <Copy className="h-5 w-5" />
                    )}
                  </Button>
                </div>

                {!fromApp && (
                  <Alert>
                    <AlertDescription>
                      <ol className="list-decimal list-inside space-y-2 text-sm">
                        <li>Copia el código arriba</li>
                        <li>Abre la aplicación NewSpports</li>
                        <li>Pega el código cuando se solicite</li>
                        <li>¡Disfruta 30 días gratis!</li>
                      </ol>
                    </AlertDescription>
                  </Alert>
                )}
              </div>

              {fromApp && (
                <Button 
                  onClick={() => activateAppAccess()} 
                  className="w-full"
                  size="lg"
                  disabled={loading}
                >
                  {loading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Activando...
                    </>
                  ) : (
                    'Activar Acceso Premium'
                  )}
                </Button>
              )}
            </>
          )}

          {/* Informações adicionais */}
          <div className="border-t pt-6">
            <h4 className="font-medium mb-3">Beneficios de la App</h4>
            <ul className="space-y-2 text-sm text-muted-foreground">
              <li className="flex items-start">
                <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                <span>30 días de acceso premium gratis</span>
              </li>
              <li className="flex items-start">
                <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                <span>Acceso en todos los dispositivos</span>
              </li>
              <li className="flex items-start">
                <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                <span>Calidad HD en todos los canales</span>
              </li>
              <li className="flex items-start">
                <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                <span>Sin anuncios ni interrupciones</span>
              </li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}