'use client'

import { useEffect, useState } from 'react'
import { use<PERSON>ara<PERSON>, useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2, CheckCircle, AlertCircle } from 'lucide-react'
import { toast } from 'sonner'

export default function AppTokenVerificationPage() {
  const params = useParams()
  const router = useRouter()
  const token = params.token as string
  const [verifying, setVerifying] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState(false)

  useEffect(() => {
    if (token) {
      verifyAppToken()
    }
  }, [token])

  const verifyAppToken = async () => {
    try {
      const response = await fetch('/api/app-verification/verify-token', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ token })
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Error al verificar')
      }

      setSuccess(true)
      toast.success('¡Acceso activado! Tienes 30 días gratis')

      // Redirigir al catálogo después de 2 segundos
      setTimeout(() => {
        router.push('/browse')
      }, 2000)

    } catch (err) {
      console.error('Error:', err)
      setError(err instanceof Error ? err.message : 'Error al verificar el token')
    } finally {
      setVerifying(false)
    }
  }

  if (verifying) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4">
        <Card className="max-w-md w-full">
          <CardContent className="py-12">
            <div className="text-center">
              <Loader2 className="w-12 h-12 animate-spin mx-auto mb-4 text-primary" />
              <h3 className="text-lg font-semibold">Verificando tu aplicación...</h3>
              <p className="text-muted-foreground mt-2">
                Por favor espera mientras activamos tu acceso premium
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4">
        <Card className="max-w-md w-full">
          <CardHeader>
            <CardTitle className="text-destructive">Error de Verificación</CardTitle>
          </CardHeader>
          <CardContent>
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
            <p className="text-sm text-muted-foreground mt-4">
              Por favor, contacta soporte si el problema persiste.
            </p>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (success) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4">
        <Card className="max-w-md w-full">
          <CardContent className="py-12">
            <div className="text-center">
              <CheckCircle className="w-16 h-16 text-green-500 mx-auto mb-4" />
              <h3 className="text-2xl font-bold mb-2">¡Verificación Exitosa!</h3>
              <p className="text-muted-foreground mb-4">
                Has ganado 30 días de acceso premium gratis
              </p>
              <p className="text-sm text-muted-foreground">
                Redirigiendo al catálogo...
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return null
}