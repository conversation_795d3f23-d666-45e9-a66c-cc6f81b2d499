'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2, CheckCircle, AlertCircle, Tv, Calendar, Smartphone, Shield } from 'lucide-react'
import { toast } from 'sonner'
import confetti from 'canvas-confetti'
import { useAuthOptional } from '@/hooks/use-auth'
import { useAccessStore } from '@/stores/access.store'
import { useTrialStore } from '@/stores/trial.store'

export default function ActivarPage() {
  const router = useRouter()
  const { user } = useAuthOptional()
  const { hasAccess, accessType, expiresAt, checkAccess, isLoading } = useAccessStore()
  const { startTime, timeRemaining, isExpired } = useTrialStore()
  
  const [activating, setActivating] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState(false)
  const [countdown, setCountdown] = useState(10)

  useEffect(() => {
    if (success && countdown > 0) {
      const timer = setTimeout(() => {
        setCountdown(countdown - 1)
      }, 1000)
      return () => clearTimeout(timer)
    }
    
    if (success && countdown === 0) {
      router.push('/browse')
    }
  }, [success, countdown, router])

  const activateAccess = async () => {
    console.log('[ACTIVAR-PAGE] Iniciando ativação...')
    setActivating(true)
    setError(null)

    try {
      // Gerar ID único simples baseado em timestamp + random
      const simpleId = `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
      console.log('[ACTIVAR-PAGE] Device ID gerado:', simpleId)
      
      console.log('[ACTIVAR-PAGE] Fazendo requisição para /api/app/activate-simple...')
      console.log('[ACTIVAR-PAGE] Enviando dados do usuário:', {
        userId: user?.id,
        userEmail: (user as any)?.primaryEmail || (user as any)?.email
      })
      
      const response = await fetch('/api/app/activate-simple', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          userId: user?.id,
          userEmail: (user as any)?.primaryEmail || (user as any)?.email
        })
      })

      console.log('[ACTIVAR-PAGE] Status da resposta:', response.status)
      const data = await response.json()
      console.log('[ACTIVAR-PAGE] Dados da resposta:', data)

      if (!response.ok) {
        console.error('[ACTIVAR-PAGE] Erro na resposta:', data)
        throw new Error(data.error || 'Error al activar el acceso')
      }

      // Salvar no localStorage
      if (data.session_id) {
        console.log('[ACTIVAR-PAGE] Salvando session_id no localStorage:', data.session_id)
        localStorage.setItem('app_session_id', data.session_id)
        localStorage.setItem('app_expires_at', data.expires_at)
        localStorage.setItem('app_access_type', data.type || 'anonymous')
      }

      // Se foi autenticado, forçar refresh do access store
      if (data.type === 'authenticated') {
        console.log('[ACTIVAR-PAGE] Usuário autenticado, forçando refresh...')
        // Aguardar um pouco para garantir que o banco foi atualizado
        await new Promise(resolve => setTimeout(resolve, 1000))
      }

      // Mostrar confetti
      confetti({
        particleCount: 100,
        spread: 70,
        origin: { y: 0.6 }
      })

      setSuccess(true)
      toast.success('¡Activación exitosa!')
      console.log('[ACTIVAR-PAGE] Ativação concluída com sucesso!')
      
    } catch (err) {
      console.error('[ACTIVAR-PAGE] Erro durante ativação:', err)
      setError(err instanceof Error ? err.message : 'Error al activar el acceso')
    } finally {
      setActivating(false)
    }
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4 bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900">
        <Card className="max-w-md w-full bg-slate-800/50 border-slate-700">
          <CardHeader>
            <CardTitle className="text-red-400 flex items-center gap-2">
              <AlertCircle className="h-6 w-6" />
              Error de Activación
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <Alert variant="destructive" className="bg-red-900/20 border-red-800">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
            
            <div className="flex gap-2">
              <Button 
                onClick={() => setError(null)}
                className="flex-1"
                variant="outline"
              >
                Reintentar
              </Button>
              <Button 
                onClick={() => router.push('/')}
                className="flex-1"
                variant="ghost"
              >
                Volver al inicio
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (success) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4 bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900">
        <Card className="max-w-2xl w-full bg-gradient-to-br from-slate-800/90 to-slate-900/90 border-green-600/20 shadow-2xl">
          <CardContent className="py-12">
            <div className="text-center space-y-6">
              {/* Icono de éxito */}
              <div className="relative mx-auto w-24 h-24">
                <div className="absolute inset-0 bg-green-500/20 rounded-full animate-ping" />
                <div className="relative bg-gradient-to-br from-green-500 to-green-600 rounded-full p-6">
                  <CheckCircle className="w-12 h-12 text-white" />
                </div>
              </div>

              {/* Título principal */}
              <div>
                <h1 className="text-4xl font-bold text-white mb-2">
                  ¡Felicidades!
                </h1>
                <h2 className="text-2xl text-green-400">
                  Tu mes gratis ha sido activado
                </h2>
              </div>

              {/* Descripción */}
              <p className="text-lg text-slate-300 max-w-md mx-auto">
                Disfruta de acceso completo a todos nuestros canales premium, 
                eventos deportivos en vivo y contenido exclusivo durante 30 días.
              </p>

              {/* Beneficios */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 max-w-xl mx-auto">
                <div className="bg-slate-800/50 rounded-lg p-4 border border-slate-700">
                  <Tv className="w-8 h-8 text-green-400 mx-auto mb-2" />
                  <p className="text-sm text-slate-300">+100 Canales HD</p>
                </div>
                <div className="bg-slate-800/50 rounded-lg p-4 border border-slate-700">
                  <Calendar className="w-8 h-8 text-green-400 mx-auto mb-2" />
                  <p className="text-sm text-slate-300">30 Días Gratis</p>
                </div>
                <div className="bg-slate-800/50 rounded-lg p-4 border border-slate-700">
                  <Smartphone className="w-8 h-8 text-green-400 mx-auto mb-2" />
                  <p className="text-sm text-slate-300">Multi-dispositivo</p>
                </div>
                <div className="bg-slate-800/50 rounded-lg p-4 border border-slate-700">
                  <Shield className="w-8 h-8 text-green-400 mx-auto mb-2" />
                  <p className="text-sm text-slate-300">Sin Anuncios</p>
                </div>
              </div>

              {/* Countdown */}
              <div className="space-y-4">
                <p className="text-slate-400">
                  Redirigiendo al catálogo en {countdown} segundos...
                </p>
                
                <Button 
                  onClick={() => router.push('/browse')}
                  size="lg"
                  className="bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white"
                >
                  Ir al Catálogo Ahora
                </Button>
              </div>

              {/* Nota informativa */}
              <div className="mt-8 p-4 bg-blue-900/20 rounded-lg border border-blue-800/30">
                <p className="text-sm text-blue-300">
                  💡 <strong>Consejo:</strong> Guarda este sitio en tus favoritos para acceder fácilmente. 
                  Tu acceso premium estará activo hasta el{' '}
                  <strong>{new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toLocaleDateString('es-ES')}</strong>
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  // Tela inicial de ativação
  return (
    <div className="min-h-screen flex items-center justify-center p-4 bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900">
      <Card className="max-w-lg w-full bg-gradient-to-br from-slate-800/90 to-slate-900/90 border-slate-700 shadow-2xl">
        <CardHeader className="text-center">
          <div className="mx-auto w-20 h-20 bg-gradient-to-br from-primary to-primary/80 rounded-full p-5 mb-4">
            <Tv className="w-10 h-10 text-white" />
          </div>
          <CardTitle className="text-3xl text-white mb-2">
            Activa tu Mes Gratis
          </CardTitle>
          <CardDescription className="text-lg text-slate-300">
            Acceso completo a todo el contenido premium
          </CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-6">
          {/* Beneficios */}
          <div className="space-y-3">
            <div className="flex items-center gap-3 text-slate-300">
              <CheckCircle className="h-5 w-5 text-green-500 flex-shrink-0" />
              <span>Más de 100 canales en HD</span>
            </div>
            <div className="flex items-center gap-3 text-slate-300">
              <CheckCircle className="h-5 w-5 text-green-500 flex-shrink-0" />
              <span>Deportes en vivo y eventos exclusivos</span>
            </div>
            <div className="flex items-center gap-3 text-slate-300">
              <CheckCircle className="h-5 w-5 text-green-500 flex-shrink-0" />
              <span>Sin compromisos, cancela cuando quieras</span>
            </div>
            <div className="flex items-center gap-3 text-slate-300">
              <CheckCircle className="h-5 w-5 text-green-500 flex-shrink-0" />
              <span>Compatible con todos tus dispositivos</span>
            </div>
          </div>

          {/* Botón de activación */}
          <Button
            onClick={activateAccess}
            disabled={activating}
            size="lg"
            className="w-full bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70 text-white font-semibold"
          >
            {activating ? (
              <>
                <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                Activando...
              </>
            ) : (
              'Activar Ahora'
            )}
          </Button>

          {/* Nota de seguridad */}
          <p className="text-xs text-slate-500 text-center">
            Al activar, aceptas nuestros términos y condiciones. 
            Este enlace es exclusivo para usuarios de la aplicación.
          </p>
        </CardContent>
      </Card>
      
      {/* Debug Info - Only in development */}
      {process.env.NEXT_PUBLIC_DEBUG === 'true' && (
        <div className="mt-8 p-4 bg-black/50 rounded-lg text-xs font-mono text-white/80">
          <h3 className="text-sm font-bold mb-2 text-white">DEBUG INFO</h3>
          <div className="space-y-1">
            <div className="text-yellow-400">USER:</div>
            <div>ID: {user?.id || 'não autenticado'}</div>
            <div>Email: {(user as any)?.primaryEmail || (user as any)?.email || 'não autenticado'}</div>
            
            <div className="text-yellow-400 mt-2">ACCESS:</div>
            <div>hasAccess: {String(hasAccess)}</div>
            <div>accessType: {accessType || 'null'}</div>
            <div>expiresAt: {expiresAt ? new Date(expiresAt).toISOString() : 'null'}</div>
            <div>isLoading: {String(isLoading)}</div>
            
            <div className="text-yellow-400 mt-2">TRIAL:</div>
            <div>startTime: {startTime || 'null'}</div>
            <div>timeRemaining: {timeRemaining}s</div>
            <div>isExpired: {String(isExpired)}</div>
            
            <div className="text-yellow-400 mt-2">COMPUTED:</div>
            <div>shouldShowTimer: {String(!hasAccess && !isExpired && timeRemaining > 0)}</div>
            <div>shouldBlock: {String(!hasAccess && isExpired)}</div>
          </div>
          
          <Button
            onClick={() => {
              console.log('[DEBUG] Forçando refresh do access...')
              if (user?.id) {
                checkAccess(user.id, true)
              }
            }}
            size="sm"
            variant="outline"
            className="mt-2"
          >
            Force Refresh
          </Button>
        </div>
      )}
    </div>
  )
}