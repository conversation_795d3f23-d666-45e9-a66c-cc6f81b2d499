'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { User, Mail, Key, Shield, Loader2, Crown, Clock, CreditCard } from 'lucide-react'
import { toast } from 'sonner'
import { useAuth } from '@/hooks/use-auth'
import { useSubscription } from '@/hooks/use-subscription'
import { PremiumBadge } from '@/components/premium-badge'
import Link from 'next/link'

export default function ProfilePage() {
  const { user } = useAuth()
  const { hasAccess, isSubscribed, isInTrial, subscriptionEndsAt, trialEndsAt, loading: subscriptionLoading } = useSubscription()
  const [loading, setLoading] = useState(false)
  const [name, setName] = useState('')
  const [currentPassword, setCurrentPassword] = useState('')
  const [newPassword, setNewPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')
  
  // Detectar se o usuário fez login com OAuth
  const isOAuthUser = user?.oauthProviders && user.oauthProviders.length > 0
  const authProvider = isOAuthUser ? 'Google' : 'email'
  
  useEffect(() => {
    if (user) {
      setName(user.displayName || user.clientMetadata?.name || '')
    }
  }, [user])
  
  const handleUpdateProfile = async () => {
    try {
      setLoading(true)
      
      if (!user) {
        toast.error('Usuario no encontrado')
        return
      }
      
      // Atualizar nome no Stack Auth
      await user.update({ displayName: name })
      
      toast.success('Perfil actualizado correctamente')
    } catch (error) {
      console.error('Error updating profile:', error)
      toast.error('Error al actualizar el perfil')
    } finally {
      setLoading(false)
    }
  }
  
  const handleChangePassword = async () => {
    if (isOAuthUser) {
      toast.error('No puedes cambiar la contraseña cuando inicias sesión con ' + authProvider)
      return
    }
    
    if (newPassword !== confirmPassword) {
      toast.error('Las contraseñas no coinciden')
      return
    }
    
    if (newPassword.length < 8) {
      toast.error('La contraseña debe tener al menos 8 caracteres')
      return
    }
    
    try {
      setLoading(true)
      
      if (!user) {
        toast.error('Usuario no encontrado')
        return
      }
      
      // Cambiar contraseña en Stack Auth
      // Por ahora, mostrar mensaje informativo
      // TODO: Implementar cambio de contraseña con Stack Auth API
      toast.info('Función de cambio de contraseña en desarrollo')
      
      // Limpiar campos
      setCurrentPassword('')
      setNewPassword('')
      setConfirmPassword('')
    } catch (error: any) {
      console.error('Error changing password:', error)
      if (error.message?.includes('incorrect')) {
        toast.error('Contraseña actual incorrecta')
      } else {
        toast.error('Error al cambiar la contraseña')
      }
    } finally {
      setLoading(false)
    }
  }
  
  return (
    <div className="max-w-4xl mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold text-white mb-8">Mi Perfil</h1>
      
      {/* Status de Assinatura */}
      <Card className="bg-gradient-to-br from-slate-800/50 to-slate-900/50 border-slate-700 mb-6">
        <CardHeader>
          <CardTitle className="text-white flex items-center gap-2">
            <Crown className="h-5 w-5 text-yellow-500" />
            Estado de la Suscripción
          </CardTitle>
        </CardHeader>
        <CardContent>
          {subscriptionLoading ? (
            <div className="flex items-center gap-2">
              <Loader2 className="h-4 w-4 animate-spin" />
              <span>Cargando...</span>
            </div>
          ) : (
            <div className="space-y-4">
              {/* Status Premium */}
              {isSubscribed && (
                <div className="flex items-center justify-between p-4 bg-gradient-to-r from-yellow-600/20 to-orange-600/20 rounded-lg border border-yellow-600/30">
                  <div className="flex items-center gap-3">
                    <Crown className="h-6 w-6 text-yellow-500" />
                    <div>
                      <p className="font-semibold text-white">Cuenta Premium Activa</p>
                      <p className="text-sm text-gray-400">
                        Acceso completo a todos los canales
                      </p>
                    </div>
                  </div>
                  <PremiumBadge size="md" expiresAt={subscriptionEndsAt} />
                </div>
              )}
              
              {/* Status Trial */}
              {isInTrial && (
                <div className="flex items-center justify-between p-4 bg-blue-600/20 rounded-lg border border-blue-600/30">
                  <div className="flex items-center gap-3">
                    <Clock className="h-6 w-6 text-blue-500" />
                    <div>
                      <p className="font-semibold text-white">Período de Prueba</p>
                      <p className="text-sm text-gray-400">
                        Disfruta de acceso temporal
                      </p>
                    </div>
                  </div>
                  <Badge variant="secondary">
                    Trial hasta {new Date(trialEndsAt).toLocaleDateString('es-ES')}
                  </Badge>
                </div>
              )}
              
              {/* Sin acceso - Apenas botão para assinar */}
              {!hasAccess && !isInTrial && !isSubscribed && (
                <div className="text-center py-8">
                  <p className="text-gray-400 mb-4">
                    Aún no tienes una suscripción activa
                  </p>
                  <Link href="/subscription">
                    <Button className="bg-gradient-to-r from-red-600 to-orange-600 hover:from-red-700 hover:to-orange-700">
                      <CreditCard className="mr-2 h-4 w-4" />
                      Suscribirse Ahora
                    </Button>
                  </Link>
                </div>
              )}
              
              {/* Botón de gestión */}
              <div className="pt-2">
                <Link href="/subscription">
                  <Button variant="outline" className="w-full sm:w-auto">
                    <CreditCard className="mr-2 h-4 w-4" />
                    Gestionar Suscripción
                  </Button>
                </Link>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
      
      {/* Información de la cuenta */}
      <Card className="bg-slate-800/50 border-slate-700 mb-6">
        <CardHeader>
          <CardTitle className="text-white flex items-center gap-2">
            <User className="h-5 w-5" />
            Información de la Cuenta
          </CardTitle>
          <CardDescription>
            Gestiona tu información personal
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Email */}
          <div className="space-y-2">
            <Label className="flex items-center gap-2">
              <Mail className="h-4 w-4" />
              Correo Electrónico
            </Label>
            <div className="flex items-center gap-3">
              <Input 
                value={user?.primaryEmail || user?.clientMetadata?.email || ''} 
                disabled 
                className="bg-slate-700/50"
              />
              {isOAuthUser && (
                <Badge variant="secondary" className="flex items-center gap-1">
                  <Shield className="h-3 w-3" />
                  {authProvider}
                </Badge>
              )}
            </div>
          </div>
          
          {/* Nombre */}
          <div className="space-y-2">
            <Label htmlFor="name">Nombre</Label>
            <Input
              id="name"
              value={name}
              onChange={(e) => setName(e.target.value)}
              placeholder="Tu nombre"
              className="bg-slate-700/50"
            />
          </div>
          
          <Button 
            onClick={handleUpdateProfile} 
            disabled={loading}
            className="w-full sm:w-auto"
          >
            {loading ? (
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            ) : (
              <User className="mr-2 h-4 w-4" />
            )}
            Actualizar Perfil
          </Button>
        </CardContent>
      </Card>
      
      {/* Cambiar contraseña - Solo mostrar si NO es OAuth */}
      {!isOAuthUser && (
        <Card className="bg-slate-800/50 border-slate-700">
          <CardHeader>
            <CardTitle className="text-white flex items-center gap-2">
              <Key className="h-5 w-5" />
              Cambiar Contraseña
            </CardTitle>
            <CardDescription>
              Actualiza tu contraseña de acceso
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="current-password">Contraseña Actual</Label>
              <Input
                id="current-password"
                type="password"
                value={currentPassword}
                onChange={(e) => setCurrentPassword(e.target.value)}
                className="bg-slate-700/50"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="new-password">Nueva Contraseña</Label>
              <Input
                id="new-password"
                type="password"
                value={newPassword}
                onChange={(e) => setNewPassword(e.target.value)}
                className="bg-slate-700/50"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="confirm-password">Confirmar Contraseña</Label>
              <Input
                id="confirm-password"
                type="password"
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                className="bg-slate-700/50"
              />
            </div>
            
            <Button 
              onClick={handleChangePassword} 
              disabled={loading}
              className="w-full sm:w-auto"
            >
              {loading ? (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              ) : (
                <Key className="mr-2 h-4 w-4" />
              )}
              Cambiar Contraseña
            </Button>
          </CardContent>
        </Card>
      )}
      
      {/* Información adicional para usuarios OAuth */}
      {isOAuthUser && (
        <Card className="bg-blue-900/20 border-blue-500/20 mt-6">
          <CardContent className="p-6">
            <div className="flex items-start gap-3">
              <Shield className="h-5 w-5 text-blue-400 mt-0.5" />
              <div className="space-y-1">
                <p className="text-sm font-medium text-white">
                  Inicio de sesión seguro con {authProvider}
                </p>
                <p className="text-xs text-gray-400">
                  Tu contraseña es gestionada por {authProvider}. Para cambiarla, 
                  visita la configuración de tu cuenta de {authProvider}.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}