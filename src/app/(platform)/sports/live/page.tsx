'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent } from '@/components/ui/card'
import { Play, Tv } from 'lucide-react'
import { Skeleton } from '@/components/ui/skeleton'

interface WorkingSportsChannel {
  id: string
  name: string
  url: string
  category: string
  logo?: string
}

export default function SportsLivePage() {
  const router = useRouter()
  const [channels, setChannels] = useState<WorkingSportsChannel[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchWorkingChannels()
  }, [])

  const fetchWorkingChannels = async () => {
    try {
      const response = await fetch('/api/sports/working')
      const data = await response.json()
      
      if (data.success) {
        setChannels(data.data)
      }
    } catch (error) {
      console.error('Error fetching sports channels:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleChannelClick = (channel: WorkingSportsChannel) => {
    // Criar um objeto de canal compatível com o player
    const channelData = {
      id: channel.id,
      name: channel.name,
      url: channel.url,
      category: channel.category,
      logo: channel.logo || 'https://i.imgur.com/default-sports.png'
    }
    
    // Salvar no sessionStorage para passar para a página de watch
    sessionStorage.setItem('selectedChannel', JSON.stringify(channelData))
    
    // Navegar para a página de watch
    router.push(`/watch/sports/${channel.id}`)
  }

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-4xl font-bold mb-8">Canales Deportivos en Vivo</h1>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {[...Array(8)].map((_, i) => (
            <Card key={i} className="overflow-hidden">
              <Skeleton className="aspect-video" />
              <CardContent className="p-4">
                <Skeleton className="h-4 w-3/4 mb-2" />
                <Skeleton className="h-3 w-1/2" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-4xl font-bold mb-2">Canales Deportivos en Vivo</h1>
        <p className="text-muted-foreground">
          {channels.length} canales deportivos disponibles
        </p>
      </div>

      {channels.length === 0 ? (
        <div className="text-center py-12">
          <Tv className="h-16 w-16 mx-auto mb-4 text-muted-foreground" />
          <h2 className="text-2xl font-semibold mb-2">No hay canales disponibles</h2>
          <p className="text-muted-foreground">
            Los canales deportivos se están actualizando. Por favor, vuelve más tarde.
          </p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {channels.map((channel) => (
            <Card
              key={channel.id}
              className="overflow-hidden cursor-pointer transition-all hover:scale-105 hover:shadow-lg"
              onClick={() => handleChannelClick(channel)}
            >
              <div className="relative aspect-video bg-gradient-to-br from-red-600 to-red-800 flex items-center justify-center">
                {channel.logo ? (
                  <img
                    src={channel.logo}
                    alt={channel.name}
                    className="w-full h-full object-cover"
                    onError={(e) => {
                      e.currentTarget.style.display = 'none'
                      e.currentTarget.nextElementSibling?.classList.remove('hidden')
                    }}
                  />
                ) : null}
                <div className={channel.logo ? 'hidden' : ''}>
                  <Tv className="h-16 w-16 text-white/80" />
                </div>
                <div className="absolute inset-0 bg-black/40 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity">
                  <Play className="h-12 w-12 text-white" />
                </div>
              </div>
              <CardContent className="p-4">
                <h3 className="font-semibold text-lg mb-1 line-clamp-1">
                  {channel.name}
                </h3>
                <p className="text-sm text-muted-foreground">
                  {channel.category}
                </p>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  )
}