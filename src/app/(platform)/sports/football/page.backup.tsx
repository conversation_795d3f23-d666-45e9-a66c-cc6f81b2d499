'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs'
import { Trophy, Activity, Calendar, TrendingUp, Star, ArrowLeft, Users, Timer, MapPin, ArrowRight } from 'lucide-react'
import Link from 'next/link'
import { APISportsClient } from '@/services/api/sports/api-sports.client'
import { Fixture } from '@/types/sports'
import { format } from 'date-fns'
import { ptBR } from 'date-fns/locale'
import { Skeleton } from '@/components/ui/skeleton'

export default function FootballPage() {
  const [liveFixtures, setLiveFixtures] = useState<Fixture[]>([])
  const [upcomingFixtures, setUpcomingFixtures] = useState<Fixture[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedLeague, setSelectedLeague] = useState<string>('all')
  
  const apiClient = new APISportsClient()
  
  // Popular leagues with images
  const popularLeagues = [
    { id: '140', name: 'LaLiga', country: 'Spain', logo: 'https://media.api-sports.io/football/leagues/140.png' },
    { id: '39', name: 'Premier League', country: 'England', logo: 'https://media.api-sports.io/football/leagues/39.png' },
    { id: '135', name: 'Serie A', country: 'Italy', logo: 'https://media.api-sports.io/football/leagues/135.png' },
    { id: '78', name: 'Bundesliga', country: 'Germany', logo: 'https://media.api-sports.io/football/leagues/78.png' },
    { id: '61', name: 'Ligue 1', country: 'France', logo: 'https://media.api-sports.io/football/leagues/61.png' },
    { id: '2', name: 'Champions League', country: 'Europe', logo: 'https://media.api-sports.io/football/leagues/2.png' },
    { id: '3', name: 'Europa League', country: 'Europe', logo: 'https://media.api-sports.io/football/leagues/3.png' },
    { id: '94', name: 'Primeira Liga', country: 'Portugal', logo: 'https://media.api-sports.io/football/leagues/94.png' }
  ]
  
  useEffect(() => {
    loadFixtures()
  }, [])
  
  async function loadFixtures() {
    try {
      const [live, upcoming] = await Promise.all([
        apiClient.getLiveFixtures(),
        apiClient.getFixturesByDate(new Date().toISOString().split('T')[0])
      ])
      
      setLiveFixtures(live)
      setUpcomingFixtures(upcoming.filter(f => f.fixture.status.short === 'NS'))
    } catch (error) {
      console.error('Error loading fixtures:', error)
    } finally {
      setLoading(false)
    }
  }
  
  const filteredLiveFixtures = selectedLeague === 'all' 
    ? liveFixtures 
    : liveFixtures.filter(f => f.league.id.toString() === selectedLeague)
    
  const filteredUpcomingFixtures = selectedLeague === 'all'
    ? upcomingFixtures
    : upcomingFixtures.filter(f => f.league.id.toString() === selectedLeague)
  
  return (
    <div className="min-h-screen bg-gradient-to-b from-slate-950 to-slate-900">
      {/* Hero Section */}
      <section className="relative py-16 overflow-hidden">
        <div className="absolute inset-0">
          <img 
            src="https://images.unsplash.com/photo-1574629810360-7efbbe195018?w=1920&h=600&fit=crop"
            alt="Football"
            className="w-full h-full object-cover opacity-20"
          />
          <div className="absolute inset-0 bg-gradient-to-r from-green-900/50 to-emerald-900/50" />
        </div>
        
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <Button variant="ghost" asChild className="mb-6 text-white hover:text-gray-200">
            <Link href="/sports">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Voltar aos Esportes
            </Link>
          </Button>
          
          <div className="flex items-center gap-4 mb-8">
            <span className="text-6xl">⚽</span>
            <div>
              <h1 className="text-5xl font-bold text-white mb-2">Futebol</h1>
              <p className="text-xl text-gray-300">
                Todas as ligas, resultados ao vivo e estatísticas completas
              </p>
            </div>
          </div>
          
          {/* Quick Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-8">
            <Card className="bg-white/10 backdrop-blur-sm border-white/20">
              <CardContent className="p-4 text-center">
                <Activity className="h-8 w-8 text-green-400 mx-auto mb-2" />
                <p className="text-2xl font-bold text-white">{liveFixtures.length}</p>
                <p className="text-sm text-gray-300">Jogos ao vivo</p>
              </CardContent>
            </Card>
            <Card className="bg-white/10 backdrop-blur-sm border-white/20">
              <CardContent className="p-4 text-center">
                <Calendar className="h-8 w-8 text-blue-400 mx-auto mb-2" />
                <p className="text-2xl font-bold text-white">{upcomingFixtures.length}</p>
                <p className="text-sm text-gray-300">Jogos hoje</p>
              </CardContent>
            </Card>
            <Card className="bg-white/10 backdrop-blur-sm border-white/20">
              <CardContent className="p-4 text-center">
                <Trophy className="h-8 w-8 text-yellow-400 mx-auto mb-2" />
                <p className="text-2xl font-bold text-white">50+</p>
                <p className="text-sm text-gray-300">Ligas</p>
              </CardContent>
            </Card>
            <Card className="bg-white/10 backdrop-blur-sm border-white/20">
              <CardContent className="p-4 text-center">
                <TrendingUp className="h-8 w-8 text-purple-400 mx-auto mb-2" />
                <p className="text-2xl font-bold text-white">24/7</p>
                <p className="text-sm text-gray-300">Cobertura</p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Popular Leagues */}
      <section className="py-12 border-y border-slate-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-2xl font-bold text-white mb-6">Ligas Populares</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-4">
            {popularLeagues.map((league) => (
              <button
                key={league.id}
                onClick={() => setSelectedLeague(league.id)}
                className={`p-4 rounded-lg border transition-all ${
                  selectedLeague === league.id
                    ? 'bg-green-600/20 border-green-500'
                    : 'bg-slate-800/50 border-slate-700 hover:border-slate-600'
                }`}
              >
                <img 
                  src={league.logo} 
                  alt={league.name}
                  className="w-12 h-12 mx-auto mb-2 object-contain"
                />
                <p className="text-sm font-medium text-white truncate">{league.name}</p>
                <p className="text-xs text-gray-400">{league.country}</p>
              </button>
            ))}
          </div>
          <div className="mt-4 flex justify-end">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setSelectedLeague('all')}
              className="text-white border-white/20 hover:bg-white/10"
            >
              Mostrar todas as ligas
            </Button>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <section className="py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <Tabs defaultValue="live" className="space-y-8">
            <TabsList className="bg-slate-800/50 border-slate-700">
              <TabsTrigger value="live" className="data-[state=active]:bg-green-600">
                <Activity className="mr-2 h-4 w-4" />
                Ao Vivo ({filteredLiveFixtures.length})
              </TabsTrigger>
              <TabsTrigger value="upcoming">
                <Calendar className="mr-2 h-4 w-4" />
                Próximos Jogos ({filteredUpcomingFixtures.length})
              </TabsTrigger>
              <TabsTrigger value="leagues">
                <Trophy className="mr-2 h-4 w-4" />
                Ligas e Tabelas
              </TabsTrigger>
              <TabsTrigger value="stats">
                <TrendingUp className="mr-2 h-4 w-4" />
                Estatísticas
              </TabsTrigger>
            </TabsList>

            {/* Live Matches */}
            <TabsContent value="live" className="space-y-6">
              {loading ? (
                <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {[...Array(6)].map((_, i) => (
                    <Skeleton key={i} className="h-64 bg-slate-800/50 rounded-xl" />
                  ))}
                </div>
              ) : filteredLiveFixtures.length > 0 ? (
                <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {filteredLiveFixtures.map((fixture, index) => {
                    return (
                      <Link 
                        key={fixture.fixture.id} 
                        href={`/watch/sports/${fixture.fixture.id}`}
                        className="group cursor-pointer"
                      >
                        <div className="relative h-64 overflow-hidden rounded-xl">
                          {/* Gradiente de fundo */}
                          <div className="absolute inset-0 bg-gradient-to-br from-red-600 to-red-900 group-hover:from-red-500 group-hover:to-red-800 transition-all duration-500" />
                          
                          {/* Padrão decorativo */}
                          <div className="absolute inset-0" 
                               style={{
                                 backgroundImage: `repeating-linear-gradient(45deg, transparent, transparent 35px, rgba(255,255,255,.05) 35px, rgba(255,255,255,.05) 70px)`
                               }} 
                          />
                          
                          {/* Ícone de fundo */}
                          <div className="absolute inset-0 flex items-center justify-center opacity-5 group-hover:opacity-10 transition-opacity">
                            <span className="text-[200px]">⚽</span>
                          </div>
                          
                          {/* Live Badge */}
                          <div className="absolute top-4 left-4 flex items-center gap-2">
                            <Badge className="bg-red-600 border-0 animate-pulse">
                              <span className="animate-pulse mr-1">●</span>
                              AO VIVO
                            </Badge>
                            <Badge className="bg-green-600 border-0">
                              {fixture.fixture.status.elapsed}'
                            </Badge>
                          </div>
                          
                          {/* League Badge */}
                          <div className="absolute top-4 right-4">
                            <Badge variant="secondary" className="bg-black/50 backdrop-blur-sm border-0">
                              {fixture.league.name}
                            </Badge>
                          </div>
                          
                          {/* Content */}
                          <div className="absolute bottom-0 left-0 right-0 p-6">
                            {/* Score */}
                            <div className="text-center mb-4">
                              <p className="text-5xl font-bold text-white">
                                {fixture.goals.home ?? 0} - {fixture.goals.away ?? 0}
                              </p>
                            </div>
                            
                            {/* Teams */}
                            <div className="space-y-3">
                              {/* Home Team */}
                              <div className="flex items-center gap-3 bg-black/30 backdrop-blur-sm rounded-lg p-2">
                                {fixture.teams.home.logo && (
                                  <img 
                                    src={fixture.teams.home.logo} 
                                    alt={fixture.teams.home.name}
                                    className="w-8 h-8 object-contain"
                                  />
                                )}
                                <span className="text-white font-medium flex-1">{fixture.teams.home.name}</span>
                                {fixture.teams.home.winner && (
                                  <Trophy className="h-4 w-4 text-yellow-400" />
                                )}
                              </div>
                              
                              {/* Away Team */}
                              <div className="flex items-center gap-3 bg-black/30 backdrop-blur-sm rounded-lg p-2">
                                {fixture.teams.away.logo && (
                                  <img 
                                    src={fixture.teams.away.logo} 
                                    alt={fixture.teams.away.name}
                                    className="w-8 h-8 object-contain"
                                  />
                                )}
                                <span className="text-white font-medium flex-1">{fixture.teams.away.name}</span>
                                {fixture.teams.away.winner && (
                                  <Trophy className="h-4 w-4 text-yellow-400" />
                                )}
                              </div>
                            </div>
                            
                            {/* Stadium */}
                            {fixture.fixture.venue.name && (
                              <div className="mt-3 text-center">
                                <div className="text-xs text-gray-300 flex items-center justify-center gap-1">
                                  <MapPin className="h-3 w-3" />
                                  {fixture.fixture.venue.name}
                                </div>
                              </div>
                            )}
                            
                            {/* Hover Effect */}
                            <div className="absolute inset-0 bg-red-600/0 group-hover:bg-red-600/20 transition-colors duration-300" />
                          </div>
                        </div>
                      </Link>
                    )
                  })}
                </div>
              ) : (
                <Card className="bg-slate-800/50 border-slate-700 p-12 text-center">
                  <Activity className="h-16 w-16 text-gray-600 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold text-white mb-2">Nenhum jogo ao vivo</h3>
                  <p className="text-gray-400">
                    {selectedLeague !== 'all' 
                      ? 'Nenhum jogo ao vivo nesta liga no momento'
                      : 'Nenhum jogo de futebol ao vivo no momento'}
                  </p>
                </Card>
              )}
            </TabsContent>

            {/* Upcoming Matches */}
            <TabsContent value="upcoming" className="space-y-6">
              {loading ? (
                <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {[...Array(6)].map((_, i) => (
                    <Skeleton key={i} className="h-64 bg-slate-800/50 rounded-xl" />
                  ))}
                </div>
              ) : filteredUpcomingFixtures.length > 0 ? (
                <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {filteredUpcomingFixtures.map((fixture, index) => {
                    const isToday = new Date(fixture.fixture.date).toDateString() === new Date().toDateString()
                    
                    return (
                      <Link 
                        key={fixture.fixture.id} 
                        href={`/watch/sports/${fixture.fixture.id}`}
                        className="group cursor-pointer"
                      >
                        <div className="relative h-64 overflow-hidden rounded-xl">
                          {/* Gradiente de fundo */}
                          <div className="absolute inset-0 bg-gradient-to-br from-green-600 to-emerald-800 group-hover:from-green-500 group-hover:to-emerald-700 transition-all duration-500" />
                          
                          {/* Padrão decorativo */}
                          <div className="absolute inset-0" 
                               style={{
                                 backgroundImage: `repeating-linear-gradient(45deg, transparent, transparent 35px, rgba(255,255,255,.05) 35px, rgba(255,255,255,.05) 70px)`
                               }} 
                          />
                          
                          {/* Ícone de fundo */}
                          <div className="absolute inset-0 flex items-center justify-center opacity-5 group-hover:opacity-10 transition-opacity">
                            <span className="text-[200px]">⚽</span>
                          </div>
                          
                          {/* Date Badge */}
                          <div className="absolute top-4 left-4">
                            <Badge className={`${isToday ? 'bg-green-600' : 'bg-blue-600'} border-0`}>
                              {isToday ? 'Hoje' : format(new Date(fixture.fixture.date), 'dd MMM', { locale: ptBR })}
                            </Badge>
                          </div>
                          
                          {/* League Badge */}
                          <div className="absolute top-4 right-4">
                            <Badge variant="secondary" className="bg-black/50 backdrop-blur-sm border-0">
                              {fixture.league.name}
                            </Badge>
                          </div>
                          
                          {/* Content */}
                          <div className="absolute bottom-0 left-0 right-0 p-6">
                            {/* Match Time */}
                            <div className="text-center mb-4">
                              <p className="text-3xl font-bold text-white">
                                {format(new Date(fixture.fixture.date), 'HH:mm')}
                              </p>
                            </div>
                            
                            {/* Teams */}
                            <div className="space-y-3">
                              {/* Home Team */}
                              <div className="flex items-center gap-3 bg-black/30 backdrop-blur-sm rounded-lg p-2">
                                {fixture.teams.home.logo && (
                                  <img 
                                    src={fixture.teams.home.logo} 
                                    alt={fixture.teams.home.name}
                                    className="w-8 h-8 object-contain"
                                  />
                                )}
                                <span className="text-white font-medium flex-1">{fixture.teams.home.name}</span>
                              </div>
                              
                              {/* VS */}
                              <div className="text-center text-gray-300 text-sm font-medium">VS</div>
                              
                              {/* Away Team */}
                              <div className="flex items-center gap-3 bg-black/30 backdrop-blur-sm rounded-lg p-2">
                                {fixture.teams.away.logo && (
                                  <img 
                                    src={fixture.teams.away.logo} 
                                    alt={fixture.teams.away.name}
                                    className="w-8 h-8 object-contain"
                                  />
                                )}
                                <span className="text-white font-medium flex-1">{fixture.teams.away.name}</span>
                              </div>
                            </div>
                            
                            {/* Stadium */}
                            {fixture.fixture.venue.name && (
                              <div className="mt-3 text-center">
                                <div className="text-xs text-gray-300 flex items-center justify-center gap-1">
                                  <MapPin className="h-3 w-3" />
                                  {fixture.fixture.venue.name}
                                </div>
                              </div>
                            )}
                            
                            {/* Hover Effect */}
                            <div className="absolute inset-0 bg-green-600/0 group-hover:bg-green-600/20 transition-colors duration-300" />
                          </div>
                        </div>
                      </Link>
                    )
                  })}
                </div>
              ) : (
                <Card className="bg-slate-800/50 border-slate-700 p-12 text-center">
                  <Calendar className="h-16 w-16 text-gray-600 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold text-white mb-2">Nenhum jogo agendado</h3>
                  <p className="text-gray-400">
                    {selectedLeague !== 'all' 
                      ? 'Nenhum jogo agendado para esta liga hoje'
                      : 'Nenhum jogo de futebol agendado para hoje'}
                  </p>
                </Card>
              )}
            </TabsContent>

            {/* Leagues & Tables */}
            <TabsContent value="leagues" className="space-y-6">
              <div className="grid md:grid-cols-2 gap-6">
                {popularLeagues.slice(0, 6).map((league) => (
                  <Card key={league.id} className="bg-slate-800/50 border-slate-700 hover:border-slate-600 transition-all">
                    <CardHeader>
                      <div className="flex items-center gap-3">
                        <img src={league.logo} alt={league.name} className="w-12 h-12" />
                        <div>
                          <CardTitle className="text-white">{league.name}</CardTitle>
                          <CardDescription>{league.country}</CardDescription>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <Button className="w-full" variant="outline" asChild>
                        <Link href={`/sports/football/league/${league.id}`}>
                          Ver tabela completa
                          <ArrowRight className="ml-2 h-4 w-4" />
                        </Link>
                      </Button>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>

            {/* Statistics */}
            <TabsContent value="stats" className="space-y-6">
              <div className="grid md:grid-cols-3 gap-6">
                <Card className="bg-gradient-to-br from-yellow-900/20 to-orange-900/20 border-yellow-500/20">
                  <CardHeader>
                    <Star className="h-10 w-10 text-yellow-500 mb-4" />
                    <CardTitle className="text-white">Top Artilheiros</CardTitle>
                    <CardDescription>
                      Maiores goleadores de todas as ligas
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Button className="w-full bg-yellow-600 hover:bg-yellow-700">
                      Ver ranking
                    </Button>
                  </CardContent>
                </Card>
                
                <Card className="bg-gradient-to-br from-blue-900/20 to-purple-900/20 border-blue-500/20">
                  <CardHeader>
                    <Users className="h-10 w-10 text-blue-500 mb-4" />
                    <CardTitle className="text-white">Melhores Times</CardTitle>
                    <CardDescription>
                      Rankings e estatísticas de equipes
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Button className="w-full bg-blue-600 hover:bg-blue-700">
                      Ver estatísticas
                    </Button>
                  </CardContent>
                </Card>
                
                <Card className="bg-gradient-to-br from-green-900/20 to-teal-900/20 border-green-500/20">
                  <CardHeader>
                    <TrendingUp className="h-10 w-10 text-green-500 mb-4" />
                    <CardTitle className="text-white">Análises</CardTitle>
                    <CardDescription>
                      Tendências e estatísticas avançadas
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Button className="w-full bg-green-600 hover:bg-green-700">
                      Ver análises
                    </Button>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </section>
    </div>
  )
}