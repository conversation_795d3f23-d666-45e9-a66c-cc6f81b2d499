'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs'
import { Trophy, Activity, Calendar, TrendingUp, ArrowLeft, Users, Timer, MapPin, Zap, Swords } from 'lucide-react'
import Link from 'next/link'
import { AllSportDBClient } from '@/services/api/sports/allsport-db.client'
import { AllSportEvent } from '@/types/sports'
import { format } from 'date-fns'
import { ptBR } from 'date-fns/locale'
import { Skeleton } from '@/components/ui/skeleton'

export default function FightingPage() {
  const [upcomingFights, setUpcomingFights] = useState<AllSportEvent[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  
  const apiClient = new AllSportDBClient()
  
  // Fight categories with images
  const fightCategories = [
    {
      id: 'ufc',
      name: 'UFC',
      description: 'Ultimate Fighting Championship',
      image: 'https://images.unsplash.com/photo-1567013275689-c179a874478f?w=800&h=600&fit=crop',
      gradient: 'from-red-600 to-red-800',
      icon: '🥋'
    },
    {
      id: 'boxing',
      name: 'Boxe',
      description: 'Boxe profissional e amador',
      image: 'https://images.unsplash.com/photo-1549719386-74dfcbf7dbed?w=800&h=600&fit=crop',
      gradient: 'from-blue-600 to-blue-800',
      icon: '🥊'
    },
    {
      id: 'mma',
      name: 'MMA',
      description: 'Artes Marciais Mistas',
      image: 'https://images.unsplash.com/photo-1584464491033-06628f3a6b7b?w=800&h=600&fit=crop',
      gradient: 'from-purple-600 to-purple-800',
      icon: '🤼'
    },
    {
      id: 'wrestling',
      name: 'Wrestling',
      description: 'WWE, AEW e mais',
      image: 'https://images.unsplash.com/photo-1591035897819-f4bdf739f446?w=800&h=600&fit=crop',
      gradient: 'from-yellow-600 to-orange-600',
      icon: '💪'
    }
  ]
  
  // Fighter rankings mock data
  const rankings = {
    ufc: [
      { rank: 1, name: 'Islam Makhachev', country: '🇷🇺', record: '24-1', division: 'Lightweight' },
      { rank: 2, name: 'Alexander Volkanovski', country: '🇦🇺', record: '26-2', division: 'Featherweight' },
      { rank: 3, name: 'Jon Jones', country: '🇺🇸', record: '27-1', division: 'Heavyweight' },
      { rank: 4, name: 'Leon Edwards', country: '🇬🇧', record: '21-3', division: 'Welterweight' },
      { rank: 5, name: 'Israel Adesanya', country: '🇳🇬', record: '24-2', division: 'Middleweight' }
    ],
    boxing: [
      { rank: 1, name: 'Canelo Álvarez', country: '🇲🇽', record: '59-2-2', division: 'Super Middleweight' },
      { rank: 2, name: 'Terence Crawford', country: '🇺🇸', record: '40-0', division: 'Welterweight' },
      { rank: 3, name: 'Naoya Inoue', country: '🇯🇵', record: '25-0', division: 'Bantamweight' },
      { rank: 4, name: 'Oleksandr Usyk', country: '🇺🇦', record: '21-0', division: 'Heavyweight' },
      { rank: 5, name: 'Dmitry Bivol', country: '🇷🇺', record: '21-0', division: 'Light Heavyweight' }
    ]
  }
  
  useEffect(() => {
    loadFights()
  }, [])
  
  async function loadFights() {
    try {
      const today = new Date()
      const endDate = new Date()
      endDate.setMonth(endDate.getMonth() + 3)
      
      const fights = await apiClient.getEvents({
        sport: 'fighting',
        dateFrom: today.toISOString().split('T')[0],
        dateTo: endDate.toISOString().split('T')[0]
      })
      
      // Filter to show only fighting events
      const fightingEvents = fights.filter(event => 
        event.sport.toLowerCase().includes('fight') ||
        event.sport.toLowerCase().includes('boxing') ||
        event.sport.toLowerCase().includes('mma') ||
        event.sport.toLowerCase().includes('ufc') ||
        event.sport.toLowerCase().includes('wrestling') ||
        event.category?.toLowerCase().includes('combat')
      )
      
      setUpcomingFights(fightingEvents)
    } catch (error) {
      console.error('Error loading fights:', error)
    } finally {
      setLoading(false)
    }
  }
  
  const filteredFights = selectedCategory === 'all'
    ? upcomingFights
    : upcomingFights.filter(fight => {
        const sport = fight.sport.toLowerCase()
        const category = fight.category?.toLowerCase() || ''
        return sport.includes(selectedCategory) || category.includes(selectedCategory)
      })
  
  return (
    <div className="min-h-screen bg-gradient-to-b from-slate-950 to-slate-900">
      {/* Hero Section */}
      <section className="relative py-16 overflow-hidden">
        <div className="absolute inset-0">
          <img 
            src="https://images.unsplash.com/photo-1549719386-74dfcbf7dbed?w=1920&h=600&fit=crop"
            alt="Fighting"
            className="w-full h-full object-cover opacity-20"
          />
          <div className="absolute inset-0 bg-gradient-to-r from-red-900/50 to-orange-900/50" />
        </div>
        
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <Button variant="ghost" asChild className="mb-6 text-white hover:text-gray-200">
            <Link href="/sports">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Voltar aos Esportes
            </Link>
          </Button>
          
          <div className="flex items-center gap-4 mb-8">
            <span className="text-6xl">🥊</span>
            <div>
              <h1 className="text-5xl font-bold text-white mb-2">Lutas</h1>
              <p className="text-xl text-gray-300">
                UFC, Boxe, MMA e todos os eventos de combate
              </p>
            </div>
          </div>
          
          {/* Quick Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-8">
            <Card className="bg-white/10 backdrop-blur-sm border-white/20">
              <CardContent className="p-4 text-center">
                <Activity className="h-8 w-8 text-red-400 mx-auto mb-2" />
                <p className="text-2xl font-bold text-white">3</p>
                <p className="text-sm text-gray-300">Eventos hoje</p>
              </CardContent>
            </Card>
            <Card className="bg-white/10 backdrop-blur-sm border-white/20">
              <CardContent className="p-4 text-center">
                <Calendar className="h-8 w-8 text-blue-400 mx-auto mb-2" />
                <p className="text-2xl font-bold text-white">{upcomingFights.length}</p>
                <p className="text-sm text-gray-300">Próximos eventos</p>
              </CardContent>
            </Card>
            <Card className="bg-white/10 backdrop-blur-sm border-white/20">
              <CardContent className="p-4 text-center">
                <Trophy className="h-8 w-8 text-yellow-400 mx-auto mb-2" />
                <p className="text-2xl font-bold text-white">15+</p>
                <p className="text-sm text-gray-300">Organizações</p>
              </CardContent>
            </Card>
            <Card className="bg-white/10 backdrop-blur-sm border-white/20">
              <CardContent className="p-4 text-center">
                <TrendingUp className="h-8 w-8 text-purple-400 mx-auto mb-2" />
                <p className="text-2xl font-bold text-white">24/7</p>
                <p className="text-sm text-gray-300">Cobertura</p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Categories */}
      <section className="py-12 border-y border-slate-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-2xl font-bold text-white mb-6">Categorias</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {fightCategories.map((category) => (
              <button
                key={category.id}
                onClick={() => setSelectedCategory(category.id)}
                className={`relative overflow-hidden rounded-lg border transition-all ${
                  selectedCategory === category.id
                    ? 'ring-2 ring-red-500 border-red-500'
                    : 'border-slate-700 hover:border-slate-600'
                }`}
              >
                <div className="relative h-32">
                  <img 
                    src={category.image} 
                    alt={category.name}
                    className="w-full h-full object-cover"
                  />
                  <div className={`absolute inset-0 bg-gradient-to-t ${category.gradient} opacity-70`} />
                  <div className="absolute inset-0 flex flex-col items-center justify-center text-white">
                    <span className="text-3xl mb-2">{category.icon}</span>
                    <p className="font-bold text-lg">{category.name}</p>
                    <p className="text-xs opacity-90">{category.description}</p>
                  </div>
                </div>
              </button>
            ))}
          </div>
          <div className="mt-4 flex justify-end">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setSelectedCategory('all')}
              className="text-white border-white/20 hover:bg-white/10"
            >
              Mostrar todas as categorias
            </Button>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <section className="py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <Tabs defaultValue="upcoming" className="space-y-8">
            <TabsList className="bg-slate-800/50 border-slate-700">
              <TabsTrigger value="upcoming" className="data-[state=active]:bg-red-600">
                <Calendar className="mr-2 h-4 w-4" />
                Próximos Eventos ({filteredFights.length})
              </TabsTrigger>
              <TabsTrigger value="rankings">
                <Trophy className="mr-2 h-4 w-4" />
                Rankings
              </TabsTrigger>
              <TabsTrigger value="organizations">
                <Users className="mr-2 h-4 w-4" />
                Organizações
              </TabsTrigger>
              <TabsTrigger value="news">
                <Zap className="mr-2 h-4 w-4" />
                Notícias
              </TabsTrigger>
            </TabsList>

            {/* Upcoming Events */}
            <TabsContent value="upcoming" className="space-y-6">
              {loading ? (
                <div className="grid gap-4">
                  {[...Array(5)].map((_, i) => (
                    <Skeleton key={i} className="h-32 bg-slate-800/50" />
                  ))}
                </div>
              ) : filteredFights.length > 0 ? (
                <div className="grid gap-4">
                  {filteredFights.map((event) => (
                    <Card key={event.id} className="bg-slate-800/50 border-slate-700 hover:border-red-500/50 transition-all">
                      <CardContent className="p-6">
                        <div className="flex items-start justify-between mb-4">
                          <div>
                            <h3 className="text-xl font-bold text-white mb-1">{event.name}</h3>
                            <div className="flex items-center gap-4 text-sm text-gray-400">
                              <span className="flex items-center gap-1">
                                <Calendar className="h-3 w-3" />
                                {format(new Date(event.date), "dd 'de' MMMM", { locale: ptBR })}
                              </span>
                              <span className="flex items-center gap-1">
                                <Timer className="h-3 w-3" />
                                {format(new Date(event.date), 'HH:mm')}
                              </span>
                              {event.venue && (
                                <span className="flex items-center gap-1">
                                  <MapPin className="h-3 w-3" />
                                  {event.venue}
                                </span>
                              )}
                            </div>
                          </div>
                          <Badge className={`
                            ${event.sport.toLowerCase().includes('ufc') ? 'bg-red-600' : ''}
                            ${event.sport.toLowerCase().includes('boxing') ? 'bg-blue-600' : ''}
                            ${event.sport.toLowerCase().includes('mma') ? 'bg-purple-600' : ''}
                            ${event.sport.toLowerCase().includes('wrestling') ? 'bg-yellow-600' : ''}
                            border-0
                          `}>
                            {event.sport}
                          </Badge>
                        </div>
                        
                        {event.participants && event.participants.length > 0 && (
                          <div className="mb-4">
                            <p className="text-sm text-gray-400 mb-2">Card Principal:</p>
                            <div className="space-y-2">
                              {event.participants.slice(0, 3).map((fighter, i) => (
                                <div key={i} className="flex items-center gap-2 text-white">
                                  <Swords className="h-4 w-4 text-red-400" />
                                  <span>{fighter}</span>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                        
                        <div className="flex gap-2">
                          <Button size="sm" className="bg-red-600 hover:bg-red-700">
                            <Activity className="mr-2 h-4 w-4" />
                            Ver detalhes
                          </Button>
                          <Button size="sm" variant="outline" className="text-white border-white/20 hover:bg-white/10">
                            <Calendar className="mr-2 h-4 w-4" />
                            Adicionar ao calendário
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              ) : (
                <Card className="bg-slate-800/50 border-slate-700 p-12 text-center">
                  <Swords className="h-16 w-16 text-gray-600 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold text-white mb-2">Nenhum evento encontrado</h3>
                  <p className="text-gray-400">
                    {selectedCategory !== 'all' 
                      ? `Nenhum evento de ${fightCategories.find(c => c.id === selectedCategory)?.name} agendado`
                      : 'Nenhum evento de luta agendado no momento'}
                  </p>
                </Card>
              )}
            </TabsContent>

            {/* Rankings */}
            <TabsContent value="rankings" className="space-y-6">
              <div className="grid md:grid-cols-2 gap-6">
                {/* UFC Rankings */}
                <Card className="bg-slate-800/50 border-slate-700">
                  <CardHeader>
                    <CardTitle className="text-white flex items-center gap-2">
                      <span className="text-2xl">🥋</span>
                      UFC P4P Rankings
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {rankings.ufc.map((fighter) => (
                        <div key={fighter.rank} className="flex items-center justify-between p-3 rounded-lg bg-slate-900/50">
                          <div className="flex items-center gap-3">
                            <span className="text-2xl font-bold text-yellow-500">#{fighter.rank}</span>
                            <div>
                              <p className="font-semibold text-white">{fighter.name} {fighter.country}</p>
                              <p className="text-sm text-gray-400">{fighter.division} • {fighter.record}</p>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
                
                {/* Boxing Rankings */}
                <Card className="bg-slate-800/50 border-slate-700">
                  <CardHeader>
                    <CardTitle className="text-white flex items-center gap-2">
                      <span className="text-2xl">🥊</span>
                      Boxing P4P Rankings
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {rankings.boxing.map((fighter) => (
                        <div key={fighter.rank} className="flex items-center justify-between p-3 rounded-lg bg-slate-900/50">
                          <div className="flex items-center gap-3">
                            <span className="text-2xl font-bold text-yellow-500">#{fighter.rank}</span>
                            <div>
                              <p className="font-semibold text-white">{fighter.name} {fighter.country}</p>
                              <p className="text-sm text-gray-400">{fighter.division} • {fighter.record}</p>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            {/* Organizations */}
            <TabsContent value="organizations" className="space-y-6">
              <div className="grid md:grid-cols-3 gap-6">
                <Card className="bg-gradient-to-br from-red-900/20 to-red-800/20 border-red-500/20">
                  <CardHeader>
                    <div className="h-16 w-16 rounded-full bg-red-600/20 flex items-center justify-center mb-4">
                      <span className="text-3xl">🥋</span>
                    </div>
                    <CardTitle className="text-white">UFC</CardTitle>
                    <CardDescription>
                      Ultimate Fighting Championship - A maior organização de MMA do mundo
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Button className="w-full bg-red-600 hover:bg-red-700">
                      Ver próximos eventos UFC
                    </Button>
                  </CardContent>
                </Card>
                
                <Card className="bg-gradient-to-br from-blue-900/20 to-blue-800/20 border-blue-500/20">
                  <CardHeader>
                    <div className="h-16 w-16 rounded-full bg-blue-600/20 flex items-center justify-center mb-4">
                      <span className="text-3xl">🥊</span>
                    </div>
                    <CardTitle className="text-white">Boxing</CardTitle>
                    <CardDescription>
                      Todas as principais organizações: WBA, WBC, IBF, WBO
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Button className="w-full bg-blue-600 hover:bg-blue-700">
                      Ver próximas lutas
                    </Button>
                  </CardContent>
                </Card>
                
                <Card className="bg-gradient-to-br from-purple-900/20 to-purple-800/20 border-purple-500/20">
                  <CardHeader>
                    <div className="h-16 w-16 rounded-full bg-purple-600/20 flex items-center justify-center mb-4">
                      <span className="text-3xl">🤼</span>
                    </div>
                    <CardTitle className="text-white">Outras Organizações</CardTitle>
                    <CardDescription>
                      Bellator, ONE Championship, PFL, BKFC e mais
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Button className="w-full bg-purple-600 hover:bg-purple-700">
                      Explorar eventos
                    </Button>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            {/* News */}
            <TabsContent value="news" className="space-y-6">
              <Card className="bg-slate-800/50 border-slate-700 p-12 text-center">
                <Zap className="h-16 w-16 text-gray-600 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-white mb-2">Notícias em breve</h3>
                <p className="text-gray-400">
                  Estamos trabalhando para trazer as últimas notícias do mundo das lutas
                </p>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </section>
    </div>
  )
}