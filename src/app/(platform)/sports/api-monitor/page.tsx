'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  Activity, 
  Clock, 
  TrendingUp, 
  AlertTriangle,
  CheckCircle,
  XCircle,
  RefreshCw,
  BarChart,
  Zap
} from 'lucide-react'
import { requestManager } from '@/services/api/sports/request-manager'
import { sportsCacheService } from '@/services/cache/sports-cache.service'
import { autoUpdateService } from '@/services/background/auto-update.service'

export default function APIMonitorPage() {
  const [stats, setStats] = useState<any>(null)
  const [cacheStats, setCacheStats] = useState<any>(null)
  const [updateStatus, setUpdateStatus] = useState<any>(null)
  const [refreshing, setRefreshing] = useState(false)

  // Atualizar estatísticas
  const updateStats = () => {
    setStats(requestManager.getStats())
    setCacheStats(sportsCacheService.getStats())
    setUpdateStatus(autoUpdateService.getStatus())
  }

  // Forçar atualização
  const forceUpdate = async () => {
    setRefreshing(true)
    try {
      await autoUpdateService.forceUpdateAll()
      updateStats()
    } finally {
      setRefreshing(false)
    }
  }

  useEffect(() => {
    updateStats()
    const interval = setInterval(updateStats, 5000) // Atualizar a cada 5 segundos
    return () => clearInterval(interval)
  }, [])

  if (!stats) {
    return <div className="flex items-center justify-center min-h-screen">
      <RefreshCw className="h-8 w-8 animate-spin text-gray-400" />
    </div>
  }

  // Calcular uso de API Sports
  const apiSportsQuotas = stats.quotas.filter((q: any) => q.apiName.startsWith('api-'))
  const totalApiSportsLimit = 100 * apiSportsQuotas.length // 100 por API
  const apiSportsPercentage = (stats.apiSportsUsage / totalApiSportsLimit) * 100

  return (
    <div className="min-h-screen bg-gradient-to-b from-slate-900 to-slate-950 p-4 md:p-8">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-white mb-4 flex items-center gap-3">
            <Activity className="h-10 w-10 text-blue-500" />
            Monitor de APIs Esportivas
          </h1>
          <p className="text-gray-400">
            Acompanhe o uso das APIs e sistema de cache em tempo real
          </p>
        </div>

        {/* Alertas */}
        {apiSportsPercentage > 80 && (
          <Alert className="mb-6 border-yellow-500/50 bg-yellow-500/10">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              Atenção: Uso de API Sports acima de 80%. Considere reduzir as requisições.
            </AlertDescription>
          </Alert>
        )}

        {/* Cards de resumo */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card className="bg-slate-800/50 border-slate-700">
            <CardHeader className="pb-3">
              <CardTitle className="text-lg font-medium text-gray-300">
                API Sports Hoje
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-baseline gap-2">
                <span className="text-3xl font-bold text-white">
                  {stats.apiSportsUsage}
                </span>
                <span className="text-gray-400">/ {totalApiSportsLimit}</span>
              </div>
              <Progress 
                value={apiSportsPercentage} 
                className="mt-3 h-2"
              />
              <p className="text-sm text-gray-400 mt-2">
                {stats.apiSportsRemaining} requisições restantes
              </p>
            </CardContent>
          </Card>

          <Card className="bg-slate-800/50 border-slate-700">
            <CardHeader className="pb-3">
              <CardTitle className="text-lg font-medium text-gray-300">
                Total de Requisições
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-baseline gap-2">
                <span className="text-3xl font-bold text-white">
                  {stats.totalRequestsToday}
                </span>
                <span className="text-gray-400">hoje</span>
              </div>
              <p className="text-sm text-gray-400 mt-2">
                {stats.requestsLast24h} nas últimas 24h
              </p>
              <Badge 
                className="mt-2"
                variant={stats.successRate > 90 ? "default" : "secondary"}
              >
                {stats.successRate.toFixed(0)}% sucesso
              </Badge>
            </CardContent>
          </Card>

          <Card className="bg-slate-800/50 border-slate-700">
            <CardHeader className="pb-3">
              <CardTitle className="text-lg font-medium text-gray-300">
                Próximo Reset
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center gap-2">
                <Clock className="h-5 w-5 text-gray-400" />
                <span className="text-2xl font-bold text-white">
                  {stats.nextResetIn}
                </span>
              </div>
              <p className="text-sm text-gray-400 mt-2">
                Reset diário às 00:00
              </p>
            </CardContent>
          </Card>

          <Card className="bg-slate-800/50 border-slate-700">
            <CardHeader className="pb-3">
              <CardTitle className="text-lg font-medium text-gray-300">
                Cache Ativo
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-baseline gap-2">
                <span className="text-3xl font-bold text-white">
                  {cacheStats?.totalEntries || 0}
                </span>
                <span className="text-gray-400">entradas</span>
              </div>
              <p className="text-sm text-green-400 mt-2">
                95% economia de API
              </p>
              <Badge className="mt-2 bg-green-600">
                Atualização 15min
              </Badge>
            </CardContent>
          </Card>
        </div>

        {/* Detalhes por API */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
          <Card className="bg-slate-800/50 border-slate-700">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>Uso por API</CardTitle>
                <BarChart className="h-5 w-5 text-gray-400" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {stats.quotas.map((quota: any) => {
                  const percentage = (quota.usedToday / quota.dailyLimit) * 100
                  const isApiSports = quota.apiName.startsWith('api-')
                  
                  return (
                    <div key={quota.apiName} className="space-y-2">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <span className="text-sm font-medium text-white">
                            {quota.apiName}
                          </span>
                          {isApiSports && (
                            <Badge variant="outline" className="text-xs">
                              15min interval
                            </Badge>
                          )}
                        </div>
                        <span className="text-sm text-gray-400">
                          {quota.usedToday} / {quota.dailyLimit}
                        </span>
                      </div>
                      <Progress 
                        value={percentage} 
                        className="h-2"
                      />
                      {quota.lastRequest && (
                        <p className="text-xs text-gray-500">
                          Última: {new Date(quota.lastRequest).toLocaleTimeString()}
                        </p>
                      )}
                    </div>
                  )
                })}
              </div>
            </CardContent>
          </Card>

          <Card className="bg-slate-800/50 border-slate-700">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>Status de Atualização</CardTitle>
                <button
                  onClick={forceUpdate}
                  disabled={refreshing}
                  className="text-blue-400 hover:text-blue-300 transition-colors"
                >
                  <RefreshCw className={`h-5 w-5 ${refreshing ? 'animate-spin' : ''}`} />
                </button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {updateStatus?.schedules.map((schedule: any) => (
                  <div key={schedule.name} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-white">
                        {schedule.name}
                      </span>
                      <div className="flex items-center gap-2">
                        {schedule.isRunning ? (
                          <Badge className="bg-yellow-600">
                            <RefreshCw className="mr-1 h-3 w-3 animate-spin" />
                            Atualizando
                          </Badge>
                        ) : schedule.lastUpdate ? (
                          <Badge className="bg-green-600">
                            <CheckCircle className="mr-1 h-3 w-3" />
                            Atualizado
                          </Badge>
                        ) : (
                          <Badge variant="secondary">
                            <Clock className="mr-1 h-3 w-3" />
                            Aguardando
                          </Badge>
                        )}
                      </div>
                    </div>
                    {schedule.lastUpdate && (
                      <p className="text-xs text-gray-500">
                        Última: {new Date(schedule.lastUpdate).toLocaleTimeString()}
                        {schedule.nextUpdate && (
                          <> • Próxima: {new Date(schedule.nextUpdate).toLocaleTimeString()}</>
                        )}
                      </p>
                    )}
                  </div>
                ))}
              </div>

              <div className="mt-6 p-4 bg-slate-900/50 rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <Zap className="h-4 w-4 text-yellow-500" />
                  <span className="text-sm font-medium text-white">
                    Economia de Requisições
                  </span>
                </div>
                <p className="text-sm text-gray-400">
                  Com cache de 15 minutos: <span className="text-green-400 font-medium">96 req/dia</span>
                </p>
                <p className="text-sm text-gray-400">
                  Sem cache: <span className="text-red-400 font-medium">5.760 req/dia</span>
                </p>
                <p className="text-sm text-green-400 font-medium mt-1">
                  Economia de 98.3%
                </p>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Próximas requisições */}
        <Card className="bg-slate-800/50 border-slate-700">
          <CardHeader>
            <CardTitle>Próximas Requisições Planejadas</CardTitle>
            <CardDescription>
              Distribuição otimizada baseada em horários de pico
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {requestManager.getOptimalSchedule().map((item, idx) => (
                <div 
                  key={idx}
                  className="flex items-center justify-between p-3 bg-slate-900/50 rounded"
                >
                  <div className="flex items-center gap-3">
                    <Badge 
                      variant={item.priority === 1 ? "default" : "secondary"}
                      className={item.priority === 1 ? "bg-red-600" : ""}
                    >
                      P{item.priority}
                    </Badge>
                    <div>
                      <p className="text-sm font-medium text-white">
                        {item.api} → {item.endpoint}
                      </p>
                      <p className="text-xs text-gray-400">
                        {new Date(item.nextTime).toLocaleTimeString()}
                      </p>
                    </div>
                  </div>
                  <TrendingUp className="h-4 w-4 text-gray-400" />
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}