'use client'

import { useEffect, useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Calendar, Clock, MapPin, Trophy, TrendingUp, PlayCircle, ChevronLeft, ChevronRight } from 'lucide-react'
import { AllSportEvent } from '@/types/sports'
import { SportsDataService } from '@/services/api/sports/sports-data.service'
import Link from 'next/link'

export default function CalendarPage() {
  const [upcomingEvents, setUpcomingEvents] = useState<AllSportEvent[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedDays, setSelectedDays] = useState(7)
  const [selectedSport, setSelectedSport] = useState('all')

  const fetchUpcomingEvents = async () => {
    try {
      setLoading(true)
      const events = await SportsDataService.getAllUpcomingEvents(selectedDays)
      setUpcomingEvents(events)
    } catch (error) {
      console.error('Erro ao buscar próximos eventos:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchUpcomingEvents()
  }, [selectedDays])

  // Agrupar eventos por data
  const eventsByDate = upcomingEvents.reduce((acc, event) => {
    const date = new Date(event.dateFrom).toLocaleDateString('pt-BR')
    if (!acc[date]) acc[date] = []
    acc[date].push(event)
    return acc
  }, {} as Record<string, AllSportEvent[]>)

  // Filtrar por esporte
  const filteredEvents = selectedSport === 'all' 
    ? upcomingEvents 
    : upcomingEvents.filter(event => event.sport.toLowerCase().includes(selectedSport.toLowerCase()))

  // Agrupar eventos filtrados por data
  const filteredEventsByDate = filteredEvents.reduce((acc, event) => {
    const date = new Date(event.dateFrom).toLocaleDateString('pt-BR')
    if (!acc[date]) acc[date] = []
    acc[date].push(event)
    return acc
  }, {} as Record<string, AllSportEvent[]>)

  // Obter lista única de esportes
  const sports = Array.from(new Set(upcomingEvents.map(e => e.sport))).sort()

  const sportEmojis: Record<string, string> = {
    'Futebol': '⚽',
    'Basquete': '🏀',
    'Tênis': '🎾',
    'Vôlei': '🏐',
    'Fórmula 1': '🏎️',
    'MMA': '🥊',
    'Baseball': '⚾',
    'Hockey': '🏒',
    'Rugby': '🏉',
    'Handball': '🤾',
    'NFL': '🏈',
    'NBA': '🏀'
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-slate-950 to-slate-900">
      {/* Header */}
      <section className="relative py-12 overflow-hidden">
        <div className="absolute inset-0">
          <div className="absolute inset-0 bg-gradient-to-r from-blue-900/20 to-purple-900/20" />
        </div>
        
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="mb-8">
            <Badge className="bg-blue-600 border-0 px-4 py-1.5 text-sm mb-4">
              <Calendar className="mr-1 h-3 w-3" />
              CALENDÁRIO ESPORTIVO
            </Badge>
            
            <h1 className="text-4xl font-bold text-white mb-4">
              Próximos Eventos
            </h1>
            
            <p className="text-gray-400 max-w-3xl">
              Acompanhe todos os eventos esportivos dos próximos dias. 
              Nunca perca um jogo importante do seu esporte favorito.
            </p>
          </div>
          
          {/* Filters */}
          <div className="flex flex-wrap gap-4 items-center">
            <Select value={selectedDays.toString()} onValueChange={(v) => setSelectedDays(Number(v))}>
              <SelectTrigger className="w-48 bg-slate-800 border-slate-700">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="3">Próximos 3 dias</SelectItem>
                <SelectItem value="7">Próximos 7 dias</SelectItem>
                <SelectItem value="14">Próximos 14 dias</SelectItem>
                <SelectItem value="30">Próximos 30 dias</SelectItem>
              </SelectContent>
            </Select>
            
            <Select value={selectedSport} onValueChange={setSelectedSport}>
              <SelectTrigger className="w-48 bg-slate-800 border-slate-700">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Todos os esportes</SelectItem>
                {sports.map(sport => (
                  <SelectItem key={sport} value={sport}>
                    {sportEmojis[sport] || '🏆'} {sport}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            
            <div className="ml-auto">
              <Badge variant="secondary" className="bg-slate-700">
                {filteredEvents.length} eventos encontrados
              </Badge>
            </div>
          </div>
        </div>
      </section>

      {/* Content */}
      <section className="py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {loading ? (
            <div className="text-center py-20">
              <Calendar className="h-12 w-12 text-blue-500 animate-pulse mx-auto mb-4" />
              <p className="text-gray-400">Carregando calendário...</p>
            </div>
          ) : filteredEvents.length === 0 ? (
            <Card className="bg-slate-800/50 border-slate-700">
              <CardContent className="text-center py-20">
                <Calendar className="h-16 w-16 text-gray-600 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-white mb-2">
                  Nenhum evento encontrado
                </h3>
                <p className="text-gray-400">
                  Tente ajustar os filtros ou volte mais tarde
                </p>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-8">
              {Object.entries(filteredEventsByDate).map(([date, events]) => (
                <div key={date}>
                  <div className="flex items-center gap-4 mb-4">
                    <h2 className="text-xl font-semibold text-white">
                      {formatDate(date)}
                    </h2>
                    <Badge variant="secondary" className="bg-slate-700">
                      {events.length} eventos
                    </Badge>
                  </div>
                  
                  <div className="grid gap-4">
                    {events.map((event) => (
                      <EventCard key={event.id} event={event} />
                    ))}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </section>
    </div>
  )
}

function EventCard({ event }: { event: AllSportEvent }) {
  const eventDate = new Date(event.dateFrom)
  const isToday = new Date().toDateString() === eventDate.toDateString()
  const isTomorrow = new Date(Date.now() + 24 * 60 * 60 * 1000).toDateString() === eventDate.toDateString()
  
  const sportEmojis: Record<string, string> = {
    'Futebol': '⚽',
    'Basquete': '🏀',
    'Tênis': '🎾',
    'Vôlei': '🏐',
    'Fórmula 1': '🏎️',
    'MMA': '🥊',
    'Baseball': '⚾',
    'Hockey': '🏒',
    'Rugby': '🏉',
    'Handball': '🤾',
    'NFL': '🏈',
    'NBA': '🏀'
  }
  
  return (
    <Card className="bg-slate-800/50 border-slate-700 hover:border-slate-600 transition-all">
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <div className="flex items-center gap-3 mb-2">
              <span className="text-2xl">{sportEmojis[event.sport] || event.emoji || '🏆'}</span>
              <Badge variant="secondary" className="bg-slate-700">
                {event.sport}
              </Badge>
              {event.competition && (
                <Badge variant="outline" className="border-slate-600 text-gray-300">
                  {event.competition}
                </Badge>
              )}
              {isToday && (
                <Badge className="bg-green-600 border-0">
                  Hoje
                </Badge>
              )}
              {isTomorrow && (
                <Badge className="bg-blue-600 border-0">
                  Amanhã
                </Badge>
              )}
            </div>
            
            <h3 className="text-xl font-semibold text-white mb-3">
              {event.name}
            </h3>
            
            <div className="flex flex-wrap items-center gap-4 text-sm text-gray-400">
              <span className="flex items-center gap-1">
                <Clock className="h-4 w-4" />
                {eventDate.toLocaleTimeString('pt-BR', { 
                  hour: '2-digit', 
                  minute: '2-digit' 
                })}
              </span>
              {event.location && (
                <span className="flex items-center gap-1">
                  <MapPin className="h-4 w-4" />
                  {event.location}
                </span>
              )}
              {event.country && (
                <span className="flex items-center gap-1">
                  <Trophy className="h-4 w-4" />
                  {event.country}
                </span>
              )}
            </div>
          </div>
          
          <div className="flex flex-col items-end gap-2">
            <div className="text-right mb-2">
              <p className="text-sm text-gray-400">Começa em</p>
              <p className="text-lg font-semibold text-white">
                {getTimeUntil(eventDate)}
              </p>
            </div>
            
            <Button size="sm" asChild>
              <Link href={event.liveUrl}>
                <PlayCircle className="mr-2 h-4 w-4" />
                Detalhes
              </Link>
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

function formatDate(dateStr: string): string {
  const [day, month, year] = dateStr.split('/')
  const date = new Date(Number(year), Number(month) - 1, Number(day))
  
  const today = new Date()
  const tomorrow = new Date(today)
  tomorrow.setDate(tomorrow.getDate() + 1)
  
  if (date.toDateString() === today.toDateString()) {
    return 'Hoje'
  } else if (date.toDateString() === tomorrow.toDateString()) {
    return 'Amanhã'
  }
  
  const weekDays = ['Domingo', 'Segunda', 'Terça', 'Quarta', 'Quinta', 'Sexta', 'Sábado']
  const months = ['jan', 'fev', 'mar', 'abr', 'mai', 'jun', 'jul', 'ago', 'set', 'out', 'nov', 'dez']
  
  return `${weekDays[date.getDay()]}, ${day} de ${months[Number(month) - 1]}`
}

function getTimeUntil(date: Date): string {
  const now = new Date()
  const diff = date.getTime() - now.getTime()
  
  if (diff < 0) return 'Em andamento'
  
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(hours / 24)
  
  if (days > 0) {
    return `${days}d ${hours % 24}h`
  } else if (hours > 0) {
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))
    return `${hours}h ${minutes}m`
  } else {
    const minutes = Math.floor(diff / (1000 * 60))
    return `${minutes}m`
  }
}