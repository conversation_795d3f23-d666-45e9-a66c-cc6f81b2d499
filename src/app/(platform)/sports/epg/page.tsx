'use client'

import { useState, useEffect } from 'react'
import { Tv, Search, RefreshCw, Clock, Calendar } from 'lucide-react'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { EPGProgramList } from '@/components/sports/epg-program-list'
import { epgClient, SPORTS_CHANNELS } from '@/services/api/sports'
import { EPGProgram } from '@/types/sports'
import { Skeleton } from '@/components/ui/skeleton'
import { useRouter } from 'next/navigation'

export default function EPGPage() {
  const router = useRouter()
  const [searchTerm, setSearchTerm] = useState('')
  const [loading, setLoading] = useState(false)
  const [refreshing, setRefreshing] = useState(false)
  const [channelPrograms, setChannelPrograms] = useState<Map<string, EPGProgram[]>>(new Map())
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [currentTime, setCurrentTime] = useState(new Date())
  
  // Actualizar hora actual cada minuto
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentTime(new Date())
    }, 60000)
    
    return () => clearInterval(interval)
  }, [])
  
  // Cargar programación inicial
  useEffect(() => {
    loadAllChannels()
  }, [])
  
  const loadAllChannels = async () => {
    setLoading(true)
    
    try {
      // Buscar canais esportivos reais do IPTV-ORG
      const channelsResponse = await fetch('/api/iptv-org/channels?sports=true&popular=true')
      const channelsData = await channelsResponse.json()
      
      if (channelsData.success && channelsData.data) {
        // Por enquanto, simular programação para cada canal
        const programsMap = new Map<string, EPGProgram[]>()
        
        channelsData.data.slice(0, 15).forEach((channel: any) => {
          const programs = generateProgramsForChannel(channel.id, channel.name)
          programsMap.set(channel.id, programs)
        })
        
        setChannelPrograms(programsMap)
      }
    } catch (error) {
      console.error('Error loading EPG data:', error)
      // Fallback para dados mock
      const channelIds = SPORTS_CHANNELS.map(ch => ch.id)
      const programs = await epgClient.getMultiChannelPrograms(channelIds)
      setChannelPrograms(programs)
    } finally {
      setLoading(false)
    }
  }
  
  // Gerar programação simulada para um canal
  const generateProgramsForChannel = (channelId: string, channelName: string): EPGProgram[] => {
    const programs: EPGProgram[] = []
    const now = new Date()
    const startOfDay = new Date(now)
    startOfDay.setHours(0, 0, 0, 0)
    
    // Gerar programação para as próximas 24 horas
    for (let i = 0; i < 48; i++) {
      const startTime = new Date(startOfDay.getTime() + i * 30 * 60 * 1000)
      const endTime = new Date(startTime.getTime() + 30 * 60 * 1000)
      
      const programTypes = [
        { title: 'Partido de Fútbol', category: 'sports' },
        { title: 'Tenis en Vivo', category: 'sports' },
        { title: 'Baloncesto NBA', category: 'sports' },
        { title: 'Fórmula 1', category: 'sports' },
        { title: 'Resumen Deportivo', category: 'news' },
        { title: 'Documental Deportivo', category: 'documentary' }
      ]
      
      const program = programTypes[i % programTypes.length]
      
      programs.push({
        id: `${channelId}-prog-${i}`,
        title: program.title,
        description: `${program.title} en ${channelName}`,
        startTime: startTime.toISOString(),
        endTime: endTime.toISOString(),
        category: program.category,
        isLive: now >= startTime && now < endTime
      })
    }
    
    return programs
  }
  
  const handleRefresh = async () => {
    setRefreshing(true)
    epgClient.clearCache()
    await loadAllChannels()
    setRefreshing(false)
  }
  
  const handleSearch = async () => {
    if (!searchTerm.trim()) {
      loadAllChannels()
      return
    }
    
    setLoading(true)
    
    try {
      const results = await epgClient.searchPrograms(searchTerm)
      const newMap = new Map<string, EPGProgram[]>()
      
      // Agrupar resultados por canal
      results.forEach(({ channelId, program }) => {
        const existing = newMap.get(channelId) || []
        existing.push(program)
        newMap.set(channelId, existing)
      })
      
      setChannelPrograms(newMap)
    } catch (error) {
      console.error('Error searching programs:', error)
    } finally {
      setLoading(false)
    }
  }
  
  const handleWatchProgram = (channelId: string, program: EPGProgram) => {
    // Por ahora, navegar a la página demo
    router.push(`/watch/demo?channel=${channelId}`)
  }
  
  // Usar canais reais do channelPrograms
  const [channels, setChannels] = useState<any[]>([])
  
  useEffect(() => {
    // Buscar metadados dos canais quando os programas forem carregados
    const loadChannelMetadata = async () => {
      if (channelPrograms.size > 0) {
        const response = await fetch('/api/iptv-org/channels?sports=true')
        const data = await response.json()
        
        if (data.success && data.data) {
          // Filtrar apenas canais que temos programação
          const channelsWithPrograms = data.data.filter((ch: any) => 
            channelPrograms.has(ch.id)
          )
          setChannels(channelsWithPrograms)
        }
      }
    }
    
    loadChannelMetadata()
  }, [channelPrograms])
  
  // Filtrar canales por categoría
  const filteredChannels = channels.filter(channel => {
    if (selectedCategory === 'all') return true
    if (selectedCategory === 'football') {
      return channel.name.toLowerCase().includes('espn') || 
             channel.name.toLowerCase().includes('sports') ||
             channel.name.toLowerCase().includes('fox')
    }
    return true
  })
  
  // Obtener categorías únicas
  const categories = [
    { id: 'all', name: 'Todos', icon: '📺' },
    { id: 'sports', name: 'Deportes', icon: '⚽' },
    { id: 'football', name: 'Fútbol', icon: '⚽' }
  ]
  
  return (
    <div className="min-h-screen bg-gradient-to-b from-slate-900 to-slate-950">
      {/* Hero Section */}
      <section className="relative py-12 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-600/20 to-purple-600/20" />
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-8">
            <h1 className="text-4xl font-bold text-white mb-4 flex items-center justify-center gap-3">
              <Tv className="h-10 w-10 text-blue-500" />
              Guía de Programación
            </h1>
            <p className="text-xl text-gray-300">
              Toda la programación deportiva en tiempo real
            </p>
          </div>
          
          {/* Search and Actions */}
          <div className="max-w-2xl mx-auto space-y-4">
            <div className="flex gap-2">
              <div className="relative flex-1">
                <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                <Input
                  placeholder="Buscar programas..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                  className="pl-12 pr-4 py-6 text-lg bg-slate-800/50 border-slate-700 text-white placeholder:text-gray-400 focus:border-blue-500"
                />
              </div>
              <Button
                size="lg"
                onClick={handleSearch}
                className="bg-blue-600 hover:bg-blue-700"
              >
                Buscar
              </Button>
            </div>
            
            <div className="flex items-center justify-center gap-4 text-sm text-gray-400">
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4" />
                <span>
                  {currentTime.toLocaleTimeString('es-ES', { 
                    hour: '2-digit', 
                    minute: '2-digit' 
                  })}
                </span>
              </div>
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                <span>
                  {currentTime.toLocaleDateString('es-ES', { 
                    weekday: 'long', 
                    day: 'numeric', 
                    month: 'long' 
                  })}
                </span>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={handleRefresh}
                disabled={refreshing}
                className="bg-white/10 border-white/20 text-white hover:bg-white/20"
              >
                <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
              </Button>
            </div>
          </div>
        </div>
      </section>
      
      {/* Category Tabs */}
      <section className="px-4 py-8">
        <div className="max-w-7xl mx-auto">
          <Tabs value={selectedCategory} onValueChange={setSelectedCategory} className="space-y-6">
            <TabsList className="bg-slate-800/50 border border-slate-700">
              {categories.map(category => (
                <TabsTrigger 
                  key={category.id} 
                  value={category.id}
                  className="data-[state=active]:bg-blue-600"
                >
                  <span className="mr-2">{category.icon}</span>
                  {category.name}
                </TabsTrigger>
              ))}
            </TabsList>
            
            {/* Channel Programs */}
            <TabsContent value={selectedCategory} className="space-y-6">
              {loading ? (
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {[...Array(4)].map((_, i) => (
                    <Skeleton key={i} className="h-96 bg-slate-800/50" />
                  ))}
                </div>
              ) : (
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {filteredChannels.map(channel => {
                    const programs = channelPrograms.get(channel.id) || []
                    
                    if (programs.length === 0) return null
                    
                    return (
                      <EPGProgramList
                        key={channel.id}
                        channelId={channel.id}
                        channelName={channel.name}
                        programs={programs}
                        onWatchProgram={(program) => handleWatchProgram(channel.id, program)}
                      />
                    )
                  })}
                </div>
              )}
              
              {!loading && filteredChannels.every(ch => !channelPrograms.get(ch.id)?.length) && (
                <Card className="bg-slate-800/50 border-slate-700 p-12 text-center">
                  <Tv className="h-16 w-16 text-gray-500 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold text-white mb-2">
                    No hay programación disponible
                  </h3>
                  <p className="text-gray-400">
                    {searchTerm 
                      ? 'No se encontraron programas con ese término de búsqueda'
                      : 'La programación se actualizará en breve'
                    }
                  </p>
                </Card>
              )}
            </TabsContent>
          </Tabs>
        </div>
      </section>
      
      {/* Info Section */}
      <section className="px-4 pb-20">
        <div className="max-w-7xl mx-auto">
          <div className="bg-gradient-to-r from-blue-900/20 to-purple-900/20 rounded-2xl p-8 border border-blue-500/20">
            <div className="grid md:grid-cols-3 gap-6 text-center">
              <div>
                <div className="inline-flex items-center justify-center h-12 w-12 rounded-full bg-blue-500/20 mb-3">
                  <Tv className="h-6 w-6 text-blue-400" />
                </div>
                <h4 className="text-lg font-semibold text-white mb-1">
                  +15 Canales
                </h4>
                <p className="text-sm text-gray-400">
                  Los mejores canales deportivos
                </p>
              </div>
              <div>
                <div className="inline-flex items-center justify-center h-12 w-12 rounded-full bg-purple-500/20 mb-3">
                  <Clock className="h-6 w-6 text-purple-400" />
                </div>
                <h4 className="text-lg font-semibold text-white mb-1">
                  Tiempo Real
                </h4>
                <p className="text-sm text-gray-400">
                  Programación actualizada al minuto
                </p>
              </div>
              <div>
                <div className="inline-flex items-center justify-center h-12 w-12 rounded-full bg-green-500/20 mb-3">
                  <Calendar className="h-6 w-6 text-green-400" />
                </div>
                <h4 className="text-lg font-semibold text-white mb-1">
                  7 Días
                </h4>
                <p className="text-sm text-gray-400">
                  Vista de toda la semana
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}