'use client'

import { useEffect, useState, useRef } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { ScrollArea, ScrollBar } from '@/components/ui/scroll-area'
import { 
  Trophy, Activity, Calendar, Star, ArrowRight, Zap,
  Clock, MapPin, PlayCircle, RefreshCw, ChevronRight, ChevronLeft, Filter, Settings
} from 'lucide-react'
import Link from 'next/link'
import { AllSportEvent } from '@/types/sports'
import { APISummaryMonitor } from '@/components/sports/api-summary-monitor'

export default function SportsPage() {
  const [liveEvents, setLiveEvents] = useState<AllSportEvent[]>([])
  const [upcomingEvents, setUpcomingEvents] = useState<AllSportEvent[]>([])
  const [sportsStats, setSportsStats] = useState<Record<string, number>>({})
  const [loading, setLoading] = useState(true)
  const [refreshing, setRefreshing] = useState(false)
  const [selectedSport, setSelectedSport] = useState('all')
  const [currentSlide, setCurrentSlide] = useState(0)
  const scrollRef = useRef<HTMLDivElement>(null)
  const intervalRef = useRef<NodeJS.Timeout | null>(null)

  // Buscar dados
  const fetchData = async () => {
    console.log('🏟️ [SportsPage] Iniciando busca de dados...')
    try {
      setRefreshing(true)
      
      // Buscar tudo em paralelo usando endpoints da API
      console.log('📡 [SportsPage] Fazendo chamadas para APIs...')
      const [liveResponse, upcomingResponse] = await Promise.allSettled([
        fetch('/api/sports/live-events')
          .then(res => {
            console.log('✅ [SportsPage] Resposta live-events:', res.status, res.statusText)
            if (!res.ok) throw new Error(`HTTP error! status: ${res.status}`)
            return res.json()
          })
          .catch(err => {
            console.error('❌ [SportsPage] Erro em live-events:', err)
            throw err
          }),
        fetch('/api/sports/upcoming-events?days=7')
          .then(res => {
            console.log('✅ [SportsPage] Resposta upcoming-events:', res.status, res.statusText)
            if (!res.ok) throw new Error(`HTTP error! status: ${res.status}`)
            return res.json()
          })
          .catch(err => {
            console.error('❌ [SportsPage] Erro em upcoming-events:', err)
            throw err
          })
      ])
      
      // Processar resultados
      console.log('📊 [SportsPage] Processando resultados...')
      
      if (liveResponse.status === 'fulfilled') {
        console.log('✅ [SportsPage] Live events recebidos:', liveResponse.value)
        if (liveResponse.value.success) {
          // Remover duplicatas baseadas no ID
          const uniqueLiveEvents = Array.from(
            new Map(liveResponse.value.data?.map((event: AllSportEvent) => [event.id, event]) || []).values()
          )
          setLiveEvents(uniqueLiveEvents)
          console.log('📺 [SportsPage] Total de eventos ao vivo únicos:', uniqueLiveEvents.length)
        }
      } else {
        console.error('❌ [SportsPage] Falha ao buscar live events:', liveResponse.reason)
      }
      
      if (upcomingResponse.status === 'fulfilled') {
        console.log('✅ [SportsPage] Upcoming events recebidos:', upcomingResponse.value)
        if (upcomingResponse.value.success) {
          // Remover duplicatas baseadas no ID
          const uniqueUpcomingEvents = Array.from(
            new Map(upcomingResponse.value.data?.map((event: AllSportEvent) => [event.id, event]) || []).values()
          )
          setUpcomingEvents(uniqueUpcomingEvents)
          console.log('📅 [SportsPage] Total de eventos futuros únicos:', uniqueUpcomingEvents.length)
        }
      } else {
        console.error('❌ [SportsPage] Falha ao buscar upcoming events:', upcomingResponse.reason)
      }
      
      // Calcular estatísticas
      const stats: Record<string, number> = {}
      const allEvents = [...(liveResponse.status === 'fulfilled' && liveResponse.value.success ? liveResponse.value.data || [] : []), 
                        ...(upcomingResponse.status === 'fulfilled' && upcomingResponse.value.success ? upcomingResponse.value.data || [] : [])]
      
      console.log('📈 [SportsPage] Total de eventos para estatísticas:', allEvents.length)
      
      allEvents.forEach((event: AllSportEvent) => {
        const sport = event.sport || 'outros'
        stats[sport] = (stats[sport] || 0) + 1
      })
      
      console.log('📊 [SportsPage] Estatísticas calculadas:', stats)
      setSportsStats(stats)
      
    } catch (error) {
      console.error('💥 [SportsPage] Erro geral ao buscar dados:', error)
      
      // PRODUÇÃO: Nunca usar dados mock/falsos
      // Mostrar estado vazio se as APIs falharem
      console.log('❌ [SportsPage] APIs falharam - mostrando estado vazio')
      
      setLiveEvents([])
      setUpcomingEvents([])
      setSportsStats({})
      
    } finally {
      setLoading(false)
      setRefreshing(false)
      console.log('✅ [SportsPage] Busca de dados concluída')
    }
  }

  useEffect(() => {
    fetchData()
    
    // Atualizar a cada 5 minutos para respeitar os limites das APIs
    const interval = setInterval(fetchData, 5 * 60 * 1000) // 5 minutos
    return () => clearInterval(interval)
  }, [])

  // Filtrar eventos por esporte
  const filteredLiveEvents = selectedSport === 'all' 
    ? liveEvents 
    : liveEvents.filter(e => e.sport.toLowerCase().includes(selectedSport.toLowerCase()))
    
  const filteredUpcomingEvents = selectedSport === 'all'
    ? upcomingEvents
    : upcomingEvents.filter(e => e.sport.toLowerCase().includes(selectedSport.toLowerCase()))

  // Manejar el carrusel automático
  useEffect(() => {
    if (filteredLiveEvents.length > 0) {
      // Limpiar intervalo anterior
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
      }
      
      // Configurar nuevo intervalo
      intervalRef.current = setInterval(() => {
        setCurrentSlide((prev) => (prev + 1) % filteredLiveEvents.length)
      }, 4000) // Cambiar cada 4 segundos
      
      return () => {
        if (intervalRef.current) {
          clearInterval(intervalRef.current)
        }
      }
    }
  }, [filteredLiveEvents.length])
  
  // Scroll al slide actual
  useEffect(() => {
    if (scrollRef.current && filteredLiveEvents.length > 0) {
      const slideWidth = 366 // 350px + 16px gap
      scrollRef.current.scrollTo({
        left: currentSlide * slideWidth,
        behavior: 'smooth'
      })
    }
  }, [currentSlide, filteredLiveEvents.length])
  
  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % filteredLiveEvents.length)
  }
  
  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + filteredLiveEvents.length) % filteredLiveEvents.length)
  }

  // Agrupar próximos eventos por data
  const upcomingByDate = filteredUpcomingEvents.reduce((acc, event) => {
    const date = new Date(event.dateFrom).toLocaleDateString('pt-BR')
    if (!acc[date]) acc[date] = []
    acc[date].push(event)
    return acc
  }, {} as Record<string, AllSportEvent[]>)

  const sportsCategories = [
    {
      id: 'football',
      name: 'Futebol',
      icon: '⚽',
      description: 'LaLiga, Champions, Premier League',
      gradient: 'from-green-600 to-green-800',
      count: sportsStats['Futebol'] || 0,
      featuredTeams: ['Real Madrid', 'Barcelona', 'Liverpool'],
      featuredLogos: [
        'https://upload.wikimedia.org/wikipedia/en/5/56/Real_Madrid_CF.svg',
        'https://upload.wikimedia.org/wikipedia/en/4/47/FC_Barcelona_%28crest%29.svg',
        'https://upload.wikimedia.org/wikipedia/en/0/0c/Liverpool_FC.svg'
      ]
    },
    {
      id: 'basketball',
      name: 'Basquete',
      icon: '🏀',
      description: 'NBA, EuroLeague, ACB',
      gradient: 'from-orange-500 to-orange-700',
      count: sportsStats['Basquete'] || 0,
      featuredTeams: ['Lakers', 'Warriors', 'Bulls'],
      featuredLogos: [
        'https://upload.wikimedia.org/wikipedia/commons/3/3c/Los_Angeles_Lakers_logo.svg',
        'https://upload.wikimedia.org/wikipedia/en/0/01/Golden_State_Warriors_logo.svg',
        'https://upload.wikimedia.org/wikipedia/en/6/67/Chicago_Bulls_logo.svg'
      ]
    },
    {
      id: 'formula-1',
      name: 'Fórmula 1',
      icon: '🏎️',
      description: 'GP, Qualificações, Treinos',
      gradient: 'from-red-600 to-red-800',
      count: sportsStats['Fórmula 1'] || 0,
      featuredTeams: ['Red Bull', 'Ferrari', 'Mercedes'],
      featuredDrivers: ['Verstappen', 'Hamilton', 'Leclerc']
    },
    {
      id: 'mma',
      name: 'MMA/UFC',
      icon: '🥊',
      description: 'UFC, Bellator, ONE FC',
      gradient: 'from-gray-700 to-gray-900',
      count: sportsStats['MMA'] || 0,
      featuredFighters: ['Jon Jones', 'Israel Adesanya', 'Alex Volkanovski']
    },
    {
      id: 'tennis',
      name: 'Tênis',
      icon: '🎾',
      description: 'Grand Slams, ATP, WTA',
      gradient: 'from-yellow-500 to-green-600',
      count: sportsStats['Tênis'] || 0,
      featuredPlayers: ['Djokovic', 'Alcaraz', 'Swiatek']
    },
    {
      id: 'volleyball',
      name: 'Vôlei',
      icon: '🏐',
      description: 'Liga Nacional, Mundial',
      gradient: 'from-blue-500 to-blue-700',
      count: sportsStats['Vôlei'] || 0,
      featuredTeams: ['Brasil', 'Polônia', 'EUA']
    },
    {
      id: 'nfl',
      name: 'NFL',
      icon: '🏈',
      description: 'Liga Americana',
      gradient: 'from-purple-600 to-purple-800',
      count: sportsStats['NFL'] || 0,
      featuredTeams: ['Patriots', 'Cowboys', 'Chiefs'],
      featuredLogos: [
        'https://upload.wikimedia.org/wikipedia/en/b/b9/New_England_Patriots_logo.svg',
        'https://upload.wikimedia.org/wikipedia/commons/1/15/Dallas_Cowboys.svg',
        'https://upload.wikimedia.org/wikipedia/en/e/e1/Kansas_City_Chiefs_logo.svg'
      ]
    },
    {
      id: 'baseball',
      name: 'Baseball',
      icon: '⚾',
      description: 'MLB, Liga Japonesa',
      gradient: 'from-indigo-500 to-indigo-700',
      count: sportsStats['Baseball'] || 0,
      featuredTeams: ['Yankees', 'Dodgers', 'Red Sox']
    },
    {
      id: 'hockey',
      name: 'Hockey',
      icon: '🏒',
      description: 'NHL, KHL, SHL',
      gradient: 'from-cyan-600 to-blue-800',
      count: sportsStats['Hockey'] || 0,
      featuredTeams: ['Maple Leafs', 'Rangers', 'Bruins']
    },
    {
      id: 'rugby',
      name: 'Rugby',
      icon: '🏉',
      description: 'Six Nations, Rugby Championship',
      gradient: 'from-emerald-600 to-emerald-800',
      count: sportsStats['Rugby'] || 0,
      featuredTeams: ['All Blacks', 'Springboks', 'England']
    },
    {
      id: 'handball',
      name: 'Handball',
      icon: '🤾',
      description: 'Liga ASOBAL, Champions League',
      gradient: 'from-pink-600 to-pink-800',
      count: sportsStats['Handball'] || 0,
      featuredTeams: ['Barcelona', 'THW Kiel', 'PSG']
    },
    {
      id: 'nba',
      name: 'NBA',
      icon: '🏀',
      description: 'Liga Profissional de Basquete',
      gradient: 'from-red-600 to-blue-800',
      count: sportsStats['NBA'] || 0,
      featuredTeams: ['Lakers', 'Celtics', 'Warriors']
    }
  ]

  return (
    <div className="min-h-screen bg-gradient-to-b from-slate-950 to-slate-900">
      {/* Hero Section Compacto */}
      <section className="relative py-12 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-red-900/20 to-orange-900/20" />
        
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between mb-6">
            <div>
              <Badge className="bg-red-600 border-0 px-4 py-1.5 text-sm mb-2">
                <Zap className="mr-1 h-3 w-3" />
                CENTRO DE DEPORTES EN VIVO
              </Badge>
              
              <h1 className="text-3xl font-bold text-white">
                Todos los Deportes en Tiempo Real
              </h1>
            </div>
            
            <div className="flex gap-2">
              <Button
                onClick={fetchData}
                disabled={refreshing}
                variant="outline"
                className="border-white/20 text-white hover:bg-white/10"
              >
                <RefreshCw className={`mr-2 h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
                Actualizar
              </Button>
              <Button
                variant="outline"
                className="border-white/20 text-white hover:bg-white/10"
                asChild
              >
                <Link href="/sports/sync-monitor">
                  <Settings className="mr-2 h-4 w-4" />
                  Monitor
                </Link>
              </Button>
            </div>
          </div>
          
          {/* Stats Bar */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Card className="bg-red-900/20 border-red-500/30">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-400">En vivo ahora</p>
                    <p className="text-2xl font-bold text-white">{liveEvents.length}</p>
                  </div>
                  <Activity className="h-8 w-8 text-red-500" />
                </div>
              </CardContent>
            </Card>
            
            <Card className="bg-orange-900/20 border-orange-500/30">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-400">Próximos 7 días</p>
                    <p className="text-2xl font-bold text-white">{upcomingEvents.length}</p>
                  </div>
                  <Calendar className="h-8 w-8 text-orange-500" />
                </div>
              </CardContent>
            </Card>
            
            <Card className="bg-yellow-900/20 border-yellow-500/30">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-400">APIs activas</p>
                    <p className="text-2xl font-bold text-white">12</p>
                  </div>
                  <Trophy className="h-8 w-8 text-yellow-500" />
                </div>
              </CardContent>
            </Card>
            
            <Card className="bg-green-900/20 border-green-500/30">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-400">Deportes</p>
                    <p className="text-2xl font-bold text-white">{Object.keys(sportsStats).length}</p>
                  </div>
                  <Star className="h-8 w-8 text-green-500" />
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* API Summary Monitor - apenas em desenvolvimento */}
      {process.env.NODE_ENV === 'development' && (
        <section className="py-8 border-t border-slate-800">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <APISummaryMonitor />
          </div>
        </section>
      )}

      {/* Container 1: Resultados ao Vivo */}
      <section className="py-8 border-t border-slate-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold text-white flex items-center gap-2">
              <span className="animate-pulse h-3 w-3 rounded-full bg-red-500" />
              Resultados en Vivo
            </h2>
          </div>
          
          {loading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {[...Array(6)].map((_, i) => (
                <Card key={i} className="h-32 bg-slate-800/50 animate-pulse" />
              ))}
            </div>
          ) : filteredLiveEvents.length === 0 ? (
            <Card className="bg-slate-800/50 border-slate-700">
              <CardContent className="text-center py-10">
                <Activity className="h-12 w-12 text-gray-600 mx-auto mb-3" />
                <p className="text-gray-400">Ningún evento en vivo en este momento</p>
              </CardContent>
            </Card>
          ) : (
            <div className="relative">
              {/* Botones de navegación */}
              {filteredLiveEvents.length > 1 && (
                <>
                  <Button
                    size="icon"
                    variant="ghost"
                    className="absolute left-0 top-1/2 -translate-y-1/2 z-10 bg-black/50 hover:bg-black/70 text-white rounded-full"
                    onClick={prevSlide}
                  >
                    <ChevronLeft className="h-5 w-5" />
                  </Button>
                  <Button
                    size="icon"
                    variant="ghost"
                    className="absolute right-0 top-1/2 -translate-y-1/2 z-10 bg-black/50 hover:bg-black/70 text-white rounded-full"
                    onClick={nextSlide}
                  >
                    <ChevronRight className="h-5 w-5" />
                  </Button>
                </>
              )}
              
              <div className="overflow-hidden">
                <div
                  ref={scrollRef}
                  className="flex gap-4 pb-4 overflow-x-hidden scroll-smooth"
                  style={{ scrollBehavior: 'smooth' }}
                >
                  {filteredLiveEvents.map((event, index) => (
                  <Card key={`live-${event.id}-${index}`} className="min-w-[350px] bg-slate-800/50 border-slate-700 hover:border-red-500/50 transition-all">
                    <CardContent className="p-4">
                      <div className="flex items-start justify-between mb-2">
                        <Badge className="bg-red-600 border-0">
                          <span className="animate-pulse mr-1">●</span>
                          EN VIVO
                        </Badge>
                        <span className="text-2xl">{event.emoji}</span>
                      </div>
                      
                      {/* Logos dos times se disponíveis */}
                      {(event.homeLogo || event.awayLogo) && (
                        <div className="flex items-center justify-center gap-4 mb-3">
                          {event.homeLogo && (
                            <img 
                              src={event.homeLogo} 
                              alt="" 
                              className="w-12 h-12 object-contain"
                              onError={(e) => {
                                e.currentTarget.style.display = 'none'
                              }}
                            />
                          )}
                          <span className="text-gray-500 text-sm">VS</span>
                          {event.awayLogo && (
                            <img 
                              src={event.awayLogo} 
                              alt="" 
                              className="w-12 h-12 object-contain"
                              onError={(e) => {
                                e.currentTarget.style.display = 'none'
                              }}
                            />
                          )}
                        </div>
                      )}
                      
                      <h3 className="font-semibold text-white mb-1 line-clamp-1 text-center">
                        {event.name}
                      </h3>
                      
                      <p className="text-sm text-gray-400 mb-3 text-center">
                        {typeof event.competition === 'string' ? event.competition : event.competition?.name || ''} • {event.sport}
                      </p>
                      
                      {event.score && (
                        <div className="text-2xl font-bold text-white mb-3 text-center">
                          {event.score}
                        </div>
                      )}
                      
                      <div className="text-center text-sm text-gray-400">
                        {event.sport}
                      </div>
                    </CardContent>
                  </Card>
                  ))}
                </div>
              </div>
              
              {/* Indicadores de posición */}
              {filteredLiveEvents.length > 1 && (
                <div className="flex justify-center gap-1 mt-4">
                  {filteredLiveEvents.map((_, index) => (
                    <button
                      key={index}
                      onClick={() => setCurrentSlide(index)}
                      className={`h-2 transition-all ${
                        index === currentSlide
                          ? 'w-8 bg-red-500'
                          : 'w-2 bg-gray-500 hover:bg-gray-400'
                      } rounded-full`}
                    />
                  ))}
                </div>
              )}
            </div>
          )}
        </div>
      </section>

      {/* Container 2: Esportes Disponíveis */}
      <section className="py-8 border-t border-slate-800 bg-slate-950/50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="mb-6">
            <h2 className="text-2xl font-bold text-white mb-2">
              Todos los Deportes Disponibles
            </h2>
            <p className="text-gray-400">
              Cobertura completa de 12 APIs deportivas + AllSportDB
            </p>
          </div>
          
          {/* Fútbol - Destaque Especial */}
          <div className="mb-8">
            <div>
              <div className="relative h-64 md:h-80 overflow-hidden rounded-xl group cursor-pointer">
                {/* Gradiente de fondo */}
                <div className="absolute inset-0 bg-gradient-to-br from-green-600 to-emerald-800 group-hover:from-green-500 group-hover:to-emerald-700 transition-all duration-700" />
                
                {/* Patrón decorativo */}
                <div className="absolute inset-0" 
                     style={{
                       backgroundImage: `repeating-linear-gradient(45deg, transparent, transparent 50px, rgba(255,255,255,.03) 50px, rgba(255,255,255,.03) 100px)`
                     }} 
                />
                
                {/* Círculos decorativos */}
                <div className="absolute -top-20 -right-20 w-80 h-80 bg-white/10 rounded-full blur-3xl" />
                <div className="absolute -bottom-20 -left-20 w-80 h-80 bg-green-400/20 rounded-full blur-3xl" />
                
                {/* Contenido */}
                <div className="absolute inset-0 flex items-end p-8">
                  <div className="flex-1">
                    <Badge className="bg-red-600 border-0 mb-4">
                      {sportsStats['Fútbol'] || 0} eventos disponibles
                    </Badge>
                    
                    <h3 className="text-4xl md:text-5xl font-bold text-white mb-3 flex items-center gap-4">
                      <span>⚽</span> Fútbol
                    </h3>
                    
                    <p className="text-lg text-gray-200 mb-6">
                      LaLiga, Champions League, Premier League, Serie A y más
                    </p>
                    
                    {/* Equipos destacados */}
                    <div className="flex items-center gap-6">
                      <div className="flex -space-x-3">
                        <div className="w-12 h-12 rounded-full bg-white p-1.5 border-2 border-green-500">
                          <img 
                            src="https://upload.wikimedia.org/wikipedia/en/5/56/Real_Madrid_CF.svg" 
                            alt="Real Madrid" 
                            className="w-full h-full object-contain"
                          />
                        </div>
                        <div className="w-12 h-12 rounded-full bg-white p-1.5 border-2 border-green-500">
                          <img 
                            src="https://upload.wikimedia.org/wikipedia/en/4/47/FC_Barcelona_%28crest%29.svg" 
                            alt="Barcelona" 
                            className="w-full h-full object-contain"
                          />
                        </div>
                        <div className="w-12 h-12 rounded-full bg-white p-1.5 border-2 border-green-500">
                          <img 
                            src="https://upload.wikimedia.org/wikipedia/en/0/0c/Liverpool_FC.svg" 
                            alt="Liverpool" 
                            className="w-full h-full object-contain"
                          />
                        </div>
                        <div className="w-12 h-12 rounded-full bg-white p-1.5 border-2 border-green-500">
                          <img 
                            src="https://upload.wikimedia.org/wikipedia/en/b/be/Manchester_United_FC_crest.svg" 
                            alt="Man United" 
                            className="w-full h-full object-contain"
                          />
                        </div>
                      </div>
                      
                      <span className="text-white/80 text-sm">
                        Más de {sportsStats['Fútbol'] || 100} partidos disponibles
                      </span>
                    </div>
                  </div>
                  
                  {/* Icono decorativo */}
                  <div className="hidden md:block">
                    <div className="text-[200px] text-white/10 leading-none">
                      ⚽
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          {/* Otros Deportes en Grid */}
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
            {sportsCategories.filter(sport => sport.id !== 'football').map((sport) => (
              <button
                key={sport.id}
                onClick={() => setSelectedSport(sport.id)}
                className={`group relative overflow-hidden rounded-lg transition-all transform hover:scale-105 ${
                  selectedSport === sport.id ? 'ring-2 ring-red-500 scale-105' : ''
                }`}
              >
                {/* Background Gradient */}
                <div className="relative h-32 overflow-hidden rounded-lg">
                  <div className={`absolute inset-0 bg-gradient-to-br ${sport.gradient} group-hover:opacity-90 transition-opacity`} />
                  
                  {/* Pattern Overlay */}
                  <div className="absolute inset-0" 
                       style={{
                         backgroundImage: `repeating-linear-gradient(45deg, transparent, transparent 25px, rgba(255,255,255,.03) 25px, rgba(255,255,255,.03) 50px)`
                       }} 
                  />
                  
                  {/* Sport Icon */}
                  <div className="absolute top-2 right-2 text-2xl filter drop-shadow-lg">
                    {sport.icon}
                  </div>
                  
                  {/* Live Badge if has live events */}
                  {sport.count > 0 && (
                    <Badge className="absolute top-2 left-2 bg-red-600 border-0 text-xs px-1.5 py-0.5">
                      {sport.count}
                    </Badge>
                  )}
                  
                  {/* Content */}
                  <div className="absolute bottom-0 left-0 right-0 p-3">
                    <h3 className="text-sm font-bold text-white">{sport.name}</h3>
                    <p className="text-xs text-gray-200 line-clamp-1">{sport.description}</p>
                  </div>
                </div>
              </button>
            ))}
          </div>
          
        </div>
      </section>

      {/* Container 3: Próximos Eventos */}
      <section className="py-8 border-t border-slate-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold text-white flex items-center gap-2">
              <Calendar className="h-6 w-6 text-blue-500" />
              Próximos Eventos
            </h2>
          </div>
          
          {loading ? (
            <div className="space-y-4">
              {[...Array(3)].map((_, i) => (
                <Card key={i} className="h-24 bg-slate-800/50 animate-pulse" />
              ))}
            </div>
          ) : (
            <Tabs defaultValue="today" className="space-y-4">
              <TabsList className="bg-slate-800/50 border border-slate-700">
                <TabsTrigger value="today">Hoy</TabsTrigger>
                <TabsTrigger value="tomorrow">Mañana</TabsTrigger>
                <TabsTrigger value="week">Esta Semana</TabsTrigger>
              </TabsList>
              
              <TabsContent value="today" className="space-y-3">
                {Object.entries(upcomingByDate)
                  .filter(([date]) => {
                    const eventDate = new Date(date.split('/').reverse().join('-'))
                    return eventDate.toDateString() === new Date().toDateString()
                  })
                  .flatMap(([_, events]) => events)
                  .slice(0, 5)
                  .map((event, index) => (
                    <EventCard key={`event-${event.id}-${index}`} event={event} />
                  ))}
              </TabsContent>
              
              <TabsContent value="tomorrow" className="space-y-3">
                {Object.entries(upcomingByDate)
                  .filter(([date]) => {
                    const eventDate = new Date(date.split('/').reverse().join('-'))
                    const tomorrow = new Date()
                    tomorrow.setDate(tomorrow.getDate() + 1)
                    return eventDate.toDateString() === tomorrow.toDateString()
                  })
                  .flatMap(([_, events]) => events)
                  .slice(0, 5)
                  .map((event, index) => (
                    <EventCard key={`event-${event.id}-${index}`} event={event} />
                  ))}
              </TabsContent>
              
              <TabsContent value="week" className="space-y-3">
                {filteredUpcomingEvents.slice(0, 10).map((event, index) => (
                  <EventCard key={`week-${event.id}-${index}`} event={event} />
                ))}
              </TabsContent>
            </Tabs>
          )}
        </div>
      </section>

    </div>
  )
}

// Componente de tarjeta de evento
function EventCard({ event }: { event: AllSportEvent }) {
  const eventDate = new Date(event.dateFrom)
  const isToday = new Date().toDateString() === eventDate.toDateString()
  const isTomorrow = new Date(Date.now() + 24 * 60 * 60 * 1000).toDateString() === eventDate.toDateString()
  
  return (
    <Card className="bg-slate-800/50 border-slate-700 hover:border-slate-600 transition-all">
      <CardContent className="p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4 flex-1">
            <span className="text-2xl">{event.emoji}</span>
            
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-1">
                <Badge variant="secondary" className="text-xs">
                  {event.sport}
                </Badge>
                {event.competition && (
                  <span className="text-xs text-gray-500">{typeof event.competition === 'string' ? event.competition : event.competition?.name || ''}</span>
                )}
                {isToday && <Badge className="bg-green-600 border-0 text-xs">Hoy</Badge>}
                {isTomorrow && <Badge className="bg-blue-600 border-0 text-xs">Mañana</Badge>}
              </div>
              
              <h3 className="font-semibold text-white line-clamp-1">
                {event.name}
              </h3>
              
              <div className="flex items-center gap-4 mt-2 text-sm text-gray-400">
                <span className="flex items-center gap-1">
                  <Clock className="h-3 w-3" />
                  {eventDate.toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' })}
                </span>
                {event.location && (
                  <span className="flex items-center gap-1">
                    <MapPin className="h-3 w-3" />
                    {typeof event.location === 'string' ? event.location : event.location?.name || ''}
                  </span>
                )}
              </div>
            </div>
          </div>
          
          <div className="text-sm text-gray-500">
            {event.sport}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}