'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  Activity, 
  CheckCircle, 
  XCircle, 
  RefreshCw, 
  Clock, 
  Database,
  Zap,
  AlertTriangle
} from 'lucide-react'
import { testAllSportsAPIs } from '@/services/api/sports/api-tester'
import { useSportsCache } from '@/services/cache/sports-cache.service'
import { useSportsStore } from '@/stores/sports.store'

export default function APITestPage() {
  const [testResults, setTestResults] = useState<any[]>([])
  const [testing, setTesting] = useState(false)
  const [cacheStats, setCacheStats] = useState<any>(null)
  const { fetchUpcomingEvents, upcomingEvents } = useSportsStore()
  const cache = useSportsCache()
  
  // Testar todas as APIs
  const runAPITests = async () => {
    setTesting(true)
    try {
      const results = await testAllSportsAPIs()
      setTestResults(results)
    } catch (error) {
      console.error('Erro ao testar APIs:', error)
    } finally {
      setTesting(false)
    }
  }
  
  // Atualizar estatísticas do cache
  const updateCacheStats = () => {
    const stats = cache.getCacheStats()
    setCacheStats(stats)
  }
  
  // Testar sistema de cache
  const testCacheSystem = async () => {
    console.log('🔄 Testando sistema de cache...')
    
    // Primeira chamada (deve buscar da API)
    console.log('1ª chamada - Deve buscar da API')
    await fetchUpcomingEvents()
    
    // Segunda chamada imediata (deve vir do cache)
    console.log('2ª chamada - Deve vir do cache')
    await fetchUpcomingEvents()
    
    updateCacheStats()
  }
  
  // Limpar cache
  const clearCache = () => {
    cache.clearCache()
    updateCacheStats()
  }
  
  useEffect(() => {
    updateCacheStats()
    const interval = setInterval(updateCacheStats, 5000)
    return () => clearInterval(interval)
  }, [])
  
  // Agrupar resultados por API
  const groupedResults = testResults.reduce((acc, result) => {
    if (!acc[result.api]) {
      acc[result.api] = []
    }
    acc[result.api].push(result)
    return acc
  }, {} as Record<string, any[]>)
  
  return (
    <div className="min-h-screen bg-gradient-to-b from-slate-900 to-slate-950 p-8">
      <div className="max-w-7xl mx-auto">
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-white mb-4">
            🧪 Teste de APIs Esportivas
          </h1>
          <p className="text-gray-400">
            Verifique o status de todas as APIs e o sistema de cache
          </p>
        </div>
        
        <Tabs defaultValue="apis" className="space-y-6">
          <TabsList className="bg-slate-800/50">
            <TabsTrigger value="apis">APIs</TabsTrigger>
            <TabsTrigger value="cache">Cache</TabsTrigger>
            <TabsTrigger value="data">Dados</TabsTrigger>
          </TabsList>
          
          <TabsContent value="apis">
            <Card className="bg-slate-800/50 border-slate-700">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle>Status das APIs</CardTitle>
                    <CardDescription>
                      Teste todas as APIs esportivas disponíveis
                    </CardDescription>
                  </div>
                  <Button 
                    onClick={runAPITests}
                    disabled={testing}
                    className="bg-blue-600 hover:bg-blue-700"
                  >
                    {testing ? (
                      <>
                        <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                        Testando...
                      </>
                    ) : (
                      <>
                        <Activity className="mr-2 h-4 w-4" />
                        Testar APIs
                      </>
                    )}
                  </Button>
                </div>
              </CardHeader>
              
              <CardContent>
                {testResults.length === 0 ? (
                  <div className="text-center py-12 text-gray-400">
                    Clique em "Testar APIs" para verificar o status
                  </div>
                ) : (
                  <div className="space-y-6">
                    {Object.entries(groupedResults).map(([api, results]) => {
                      const successful = results.filter(r => r.success).length
                      const total = results.length
                      const successRate = (successful / total * 100).toFixed(0)
                      
                      return (
                        <div key={api} className="border border-slate-700 rounded-lg p-4">
                          <div className="flex items-center justify-between mb-4">
                            <h3 className="text-lg font-semibold text-white">{api}</h3>
                            <Badge 
                              variant={successful === total ? "default" : successful > 0 ? "secondary" : "destructive"}
                              className={
                                successful === total 
                                  ? "bg-green-600" 
                                  : successful > 0 
                                  ? "bg-yellow-600" 
                                  : "bg-red-600"
                              }
                            >
                              {successRate}% ({successful}/{total})
                            </Badge>
                          </div>
                          
                          <div className="space-y-2">
                            {results.map((result, idx) => (
                              <div 
                                key={idx}
                                className="flex items-center justify-between p-2 rounded bg-slate-900/50"
                              >
                                <div className="flex items-center gap-3">
                                  {result.success ? (
                                    <CheckCircle className="h-5 w-5 text-green-500" />
                                  ) : (
                                    <XCircle className="h-5 w-5 text-red-500" />
                                  )}
                                  <div>
                                    <p className="text-sm font-medium text-white">
                                      {result.endpoint}
                                    </p>
                                    <p className="text-xs text-gray-400">
                                      {result.sport}
                                    </p>
                                  </div>
                                </div>
                                
                                <div className="text-right">
                                  {result.success ? (
                                    <>
                                      <p className="text-sm text-white">
                                        {result.dataCount} resultados
                                      </p>
                                      <p className="text-xs text-gray-400">
                                        {result.responseTime}ms
                                      </p>
                                    </>
                                  ) : (
                                    <p className="text-xs text-red-400">
                                      {result.error}
                                    </p>
                                  )}
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      )
                    })}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="cache">
            <Card className="bg-slate-800/50 border-slate-700">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle>Sistema de Cache</CardTitle>
                    <CardDescription>
                      Cache com atualização automática a cada 15 minutos
                    </CardDescription>
                  </div>
                  <div className="flex gap-2">
                    <Button 
                      onClick={testCacheSystem}
                      variant="outline"
                      className="border-white/20"
                    >
                      <Zap className="mr-2 h-4 w-4" />
                      Testar Cache
                    </Button>
                    <Button 
                      onClick={clearCache}
                      variant="outline"
                      className="border-red-500/50 text-red-400 hover:bg-red-500/20"
                    >
                      <Database className="mr-2 h-4 w-4" />
                      Limpar Cache
                    </Button>
                  </div>
                </div>
              </CardHeader>
              
              <CardContent>
                <Alert className="mb-6 border-blue-500/50 bg-blue-500/10">
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    O cache reduz o consumo de API de 96 requisições/dia para apenas 4-6 requisições/dia,
                    mantendo os dados atualizados a cada 15 minutos.
                  </AlertDescription>
                </Alert>
                
                {cacheStats ? (
                  <div className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="bg-slate-900/50 p-4 rounded">
                        <p className="text-sm text-gray-400">Entradas no Cache</p>
                        <p className="text-2xl font-bold text-white">
                          {cacheStats.totalEntries}
                        </p>
                      </div>
                      <div className="bg-slate-900/50 p-4 rounded">
                        <p className="text-sm text-gray-400">Economia de API</p>
                        <p className="text-2xl font-bold text-green-400">
                          ~95%
                        </p>
                      </div>
                    </div>
                    
                    <div className="space-y-2">
                      <h4 className="text-sm font-medium text-gray-400">Entradas Ativas</h4>
                      {cacheStats.entries.map((entry: any, idx: number) => (
                        <div 
                          key={idx}
                          className="flex items-center justify-between p-3 bg-slate-900/50 rounded"
                        >
                          <div>
                            <p className="text-sm font-medium text-white">{entry.key}</p>
                            <p className="text-xs text-gray-400">
                              Idade: {entry.age} | Tamanho: {(entry.size / 1024).toFixed(1)}KB
                            </p>
                          </div>
                          <div className="flex items-center gap-2">
                            <Clock className="h-4 w-4 text-gray-400" />
                            <span className="text-sm text-gray-300">
                              Expira em: {entry.expiresIn}
                            </span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-12 text-gray-400">
                    Cache vazio
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="data">
            <Card className="bg-slate-800/50 border-slate-700">
              <CardHeader>
                <CardTitle>Dados em Cache</CardTitle>
                <CardDescription>
                  Eventos esportivos disponíveis
                </CardDescription>
              </CardHeader>
              
              <CardContent>
                <div className="space-y-4">
                  {upcomingEvents.slice(0, 10).map(event => (
                    <div 
                      key={event.id}
                      className="flex items-center justify-between p-4 bg-slate-900/50 rounded"
                    >
                      <div>
                        <p className="font-medium text-white">
                          {event.emoji} {event.name}
                        </p>
                        <p className="text-sm text-gray-400">
                          {event.competition} • {event.sport}
                        </p>
                      </div>
                      <div className="text-right">
                        <p className="text-sm text-white">
                          {new Date(event.dateFrom).toLocaleDateString()}
                        </p>
                        <p className="text-xs text-gray-400">
                          {new Date(event.dateFrom).toLocaleTimeString()}
                        </p>
                      </div>
                    </div>
                  ))}
                  
                  {upcomingEvents.length === 0 && (
                    <div className="text-center py-12 text-gray-400">
                      Nenhum evento disponível. Teste o cache primeiro.
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}