'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Play, Pause, RefreshCw, Activity, Clock, Database, Zap } from 'lucide-react'
import { toast } from 'sonner'

interface SyncStatus {
  isRunning: boolean
  schedules: Array<{
    api: string
    type: string
    lastUpdate: string | null
    nextUpdate: string | null
    isProcessing: boolean
    quotaUsed: number
    quotaLimit: number
    canUpdate: boolean
  }>
  totalRequests: number
  apiSportsUsage: number
  apiSportsRemaining: number
  nextReset: string
}

export default function SyncMonitorPage() {
  const [status, setStatus] = useState<SyncStatus | null>(null)
  const [loading, setLoading] = useState(false)
  
  // Buscar status
  const fetchStatus = async () => {
    try {
      const res = await fetch('/api/sports/sync-control')
      const data = await res.json()
      if (data.success) {
        setStatus(data.status)
      }
    } catch (error) {
      console.error('Erro ao buscar status:', error)
    }
  }
  
  // Controle do serviço
  const controlService = async (action: 'start' | 'stop' | 'force') => {
    setLoading(true)
    try {
      const res = await fetch('/api/sports/sync-control', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action })
      })
      
      const data = await res.json()
      if (data.success) {
        toast.success(data.message)
        fetchStatus()
      } else {
        toast.error(data.error)
      }
    } catch (error) {
      toast.error('Erro ao controlar serviço')
    } finally {
      setLoading(false)
    }
  }
  
  // Atualizar a cada 5 segundos
  useEffect(() => {
    fetchStatus()
    const interval = setInterval(fetchStatus, 5000)
    return () => clearInterval(interval)
  }, [])
  
  if (!status) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-slate-950 to-slate-900 p-8">
        <div className="max-w-7xl mx-auto">
          <Card className="bg-slate-800/50 border-slate-700">
            <CardContent className="p-8 text-center">
              <RefreshCw className="h-8 w-8 text-gray-400 animate-spin mx-auto mb-4" />
              <p className="text-gray-400">Carregando status...</p>
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }
  
  // Calcular progresso total
  const totalAPIs = status.schedules.length
  const processedAPIs = status.schedules.filter(s => s.lastUpdate).length
  const progressPercent = (processedAPIs / totalAPIs) * 100
  
  return (
    <div className="min-h-screen bg-gradient-to-b from-slate-950 to-slate-900 p-8">
      <div className="max-w-7xl mx-auto space-y-8">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">
              Monitor de Sincronização
            </h1>
            <p className="text-gray-400">
              Acompanhe as atualizações das APIs esportivas em tempo real
            </p>
          </div>
          
          <div className="flex gap-3">
            {status.isRunning ? (
              <Button 
                onClick={() => controlService('stop')}
                variant="destructive"
                disabled={loading}
              >
                <Pause className="mr-2 h-4 w-4" />
                Parar
              </Button>
            ) : (
              <Button 
                onClick={() => controlService('start')}
                disabled={loading}
              >
                <Play className="mr-2 h-4 w-4" />
                Iniciar
              </Button>
            )}
            
            <Button
              onClick={() => controlService('force')}
              variant="outline"
              disabled={loading}
            >
              <RefreshCw className="mr-2 h-4 w-4" />
              Forçar Atualização
            </Button>
          </div>
        </div>
        
        {/* Status Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card className="bg-slate-800/50 border-slate-700">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <Activity className="h-5 w-5 text-green-500" />
                Status
              </CardTitle>
            </CardHeader>
            <CardContent>
              <Badge className={status.isRunning ? 'bg-green-600' : 'bg-red-600'}>
                {status.isRunning ? 'ATIVO' : 'PARADO'}
              </Badge>
              <p className="text-sm text-gray-400 mt-2">
                {processedAPIs}/{totalAPIs} APIs processadas
              </p>
            </CardContent>
          </Card>
          
          <Card className="bg-slate-800/50 border-slate-700">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <Database className="h-5 w-5 text-blue-500" />
                Requisições Hoje
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-2xl font-bold text-white">
                {status.totalRequests}
              </p>
              <p className="text-sm text-gray-400">
                Reset em {status.nextReset}
              </p>
            </CardContent>
          </Card>
          
          <Card className="bg-slate-800/50 border-slate-700">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <Zap className="h-5 w-5 text-yellow-500" />
                API Sports
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-2xl font-bold text-white">
                {status.apiSportsUsage}/{status.apiSportsUsage + status.apiSportsRemaining}
              </p>
              <Progress 
                value={(status.apiSportsUsage / (status.apiSportsUsage + status.apiSportsRemaining)) * 100} 
                className="mt-2"
              />
            </CardContent>
          </Card>
          
          <Card className="bg-slate-800/50 border-slate-700">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <Clock className="h-5 w-5 text-purple-500" />
                Progresso
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-2xl font-bold text-white">
                {progressPercent.toFixed(0)}%
              </p>
              <Progress value={progressPercent} className="mt-2" />
            </CardContent>
          </Card>
        </div>
        
        {/* APIs Table */}
        <Card className="bg-slate-800/50 border-slate-700">
          <CardHeader>
            <CardTitle className="text-white">APIs Monitoradas</CardTitle>
            <CardDescription>
              Status detalhado de cada API
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="text-left text-gray-400 border-b border-slate-700">
                    <th className="pb-3">API</th>
                    <th className="pb-3">Status</th>
                    <th className="pb-3">Última Atualização</th>
                    <th className="pb-3">Próxima</th>
                    <th className="pb-3">Quota</th>
                    <th className="pb-3">Progresso</th>
                  </tr>
                </thead>
                <tbody className="text-white">
                  {status.schedules.map((schedule) => {
                    const quotaPercent = schedule.quotaLimit > 0 
                      ? (schedule.quotaUsed / schedule.quotaLimit) * 100 
                      : 0
                    
                    return (
                      <tr key={schedule.api} className="border-b border-slate-700/50">
                        <td className="py-3">
                          <div className="flex items-center gap-2">
                            <span className="font-medium">{schedule.api}</span>
                            <Badge variant="outline" className="text-xs">
                              {schedule.type}
                            </Badge>
                          </div>
                        </td>
                        <td className="py-3">
                          {schedule.isProcessing ? (
                            <Badge className="bg-yellow-600">
                              <RefreshCw className="mr-1 h-3 w-3 animate-spin" />
                              Processando
                            </Badge>
                          ) : schedule.canUpdate ? (
                            <Badge className="bg-green-600">Disponível</Badge>
                          ) : (
                            <Badge variant="secondary">Aguardando</Badge>
                          )}
                        </td>
                        <td className="py-3 text-gray-400">
                          {schedule.lastUpdate 
                            ? new Date(schedule.lastUpdate).toLocaleTimeString('pt-BR')
                            : 'Nunca'}
                        </td>
                        <td className="py-3 text-gray-400">
                          {schedule.nextUpdate 
                            ? new Date(schedule.nextUpdate).toLocaleTimeString('pt-BR')
                            : 'Agora'}
                        </td>
                        <td className="py-3">
                          <span className="text-sm">
                            {schedule.quotaUsed}/{schedule.quotaLimit}
                          </span>
                        </td>
                        <td className="py-3">
                          <div className="w-24">
                            <Progress 
                              value={quotaPercent} 
                              className="h-2"
                            />
                          </div>
                        </td>
                      </tr>
                    )
                  })}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}