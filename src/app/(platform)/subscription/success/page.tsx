'use client'

import { useEffect, useState } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { Check<PERSON>ircle2, Loader2, <PERSON>R<PERSON>, Tv } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import Link from 'next/link'

export default function SubscriptionSuccessPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const [loading, setLoading] = useState(true)
  const [sessionId, setSessionId] = useState<string | null>(null)

  useEffect(() => {
    // Pegar session_id da URL
    const session = searchParams.get('session_id')
    setSessionId(session)
    
    // Processar o pagamento se tiver session ID
    if (session) {
      processPayment(session)
    } else {
      setLoading(false)
    }
  }, [searchParams])

  useEffect(() => {
    // Log para debug
    console.log('[SUBSCRIPTION-SUCCESS] Page loaded')
    console.log('[SUBSCRIPTION-SUCCESS] Session ID:', sessionId)
  }, [sessionId])

  async function processPayment(sessionId: string) {
    try {
      console.log('[SUBSCRIPTION-SUCCESS] Processando pagamento com session:', sessionId)
      
      const response = await fetch('/api/stripe/process-checkout-session', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ sessionId })
      })

      const result = await response.json()
      
      if (result.success) {
        console.log('[SUBSCRIPTION-SUCCESS] ✅ Pagamento processado com sucesso:', result)
      } else {
        console.error('[SUBSCRIPTION-SUCCESS] ❌ Erro ao processar pagamento:', result)
      }
    } catch (error) {
      console.error('[SUBSCRIPTION-SUCCESS] ❌ Erro ao chamar API:', error)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-gray-950 to-black flex items-center justify-center">
        <div className="text-center space-y-4">
          <Loader2 className="h-8 w-8 animate-spin text-red-500 mx-auto" />
          <p className="text-gray-400">Procesando tu suscripción...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-950 to-black">
      {/* Background effects */}
      <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top,_var(--tw-gradient-stops))] from-red-900/20 via-transparent to-transparent pointer-events-none" />
      
      <div className="relative z-10 flex items-center justify-center min-h-screen p-4">
        <Card className="max-w-md w-full bg-gray-900/50 backdrop-blur-xl border-gray-800">
          <div className="p-8 text-center space-y-6">
            {/* Success Icon */}
            <div className="relative mx-auto w-20 h-20">
              <div className="absolute inset-0 bg-green-500/20 rounded-full blur-xl" />
              <div className="relative bg-green-500 rounded-full p-4 w-20 h-20 flex items-center justify-center">
                <CheckCircle2 className="h-10 w-10 text-white" />
              </div>
            </div>

            {/* Success Message */}
            <div className="space-y-2">
              <h1 className="text-3xl font-bold text-white">
                ¡Pago exitoso!
              </h1>
              <p className="text-gray-400">
                Tu suscripción premium está activa
              </p>
            </div>

            {/* Benefits */}
            <div className="bg-gray-800/50 rounded-lg p-4 space-y-3">
              <h3 className="font-semibold text-white text-sm">
                Ahora tienes acceso a:
              </h3>
              <ul className="space-y-2 text-sm text-gray-300">
                <li className="flex items-center gap-2">
                  <CheckCircle2 className="h-4 w-4 text-green-500" />
                  <span>Más de 100 canales premium en HD</span>
                </li>
                <li className="flex items-center gap-2">
                  <CheckCircle2 className="h-4 w-4 text-green-500" />
                  <span>Sin anuncios ni interrupciones</span>
                </li>
                <li className="flex items-center gap-2">
                  <CheckCircle2 className="h-4 w-4 text-green-500" />
                  <span>Acceso en todos tus dispositivos</span>
                </li>
                <li className="flex items-center gap-2">
                  <CheckCircle2 className="h-4 w-4 text-green-500" />
                  <span>Soporte prioritario 24/7</span>
                </li>
              </ul>
            </div>

            {/* Session ID for debug */}
            {process.env.NEXT_PUBLIC_DEBUG && sessionId && (
              <div className="text-xs text-gray-600 font-mono">
                Session: {sessionId.substring(0, 20)}...
              </div>
            )}

            {/* Actions */}
            <div className="space-y-3 pt-4">
              <Button
                size="lg"
                className="w-full bg-gradient-to-r from-red-600 to-orange-600 hover:from-red-700 hover:to-orange-700 text-white font-semibold"
                asChild
              >
                <Link href="/browse">
                  <Tv className="mr-2 h-5 w-5" />
                  Explorar canales premium
                </Link>
              </Button>
              
              <Button
                size="lg"
                variant="outline"
                className="w-full border-gray-700 text-gray-300 hover:bg-gray-800 hover:text-white"
                asChild
              >
                <Link href="/profile">
                  Ver mi suscripción
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
            </div>

            {/* Support */}
            <p className="text-xs text-gray-500">
              ¿Necesitas ayuda? Contacta a{' '}
              <a href="mailto:<EMAIL>" className="text-red-400 hover:underline">
                <EMAIL>
              </a>
            </p>
          </div>
        </Card>
      </div>
    </div>
  )
}