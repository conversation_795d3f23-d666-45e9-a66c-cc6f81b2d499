'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { PricingCard } from '@/components/subscription/pricing-card'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Loader2, ArrowLeft, CheckCircle, XCircle, CreditCard, Calendar, Mail, Crown } from 'lucide-react'
import { useAuth } from '@/hooks/use-auth'
import { createClient } from '@/lib/supabase/client'
import { toast } from 'sonner'
import Link from 'next/link'
import { PremiumBadge } from '@/components/premium-badge'
import { useSubscription } from '@/hooks/use-subscription'

export default function SubscriptionPage() {
  const router = useRouter()
  const { user, isLoading: authLoading } = useAuth()
  const { isSubscribed, subscriptionEndsAt, loading: subLoading } = useSubscription()
  const [loading, setLoading] = useState(true)
  const [subscription, setSubscription] = useState<any>(null)

  useEffect(() => {
    if (!authLoading) {
      if (user) {
        loadSubscription()
      } else {
        setLoading(false)
      }
    }
  }, [user, authLoading])

  const loadSubscription = async () => {
    try {
      const supabase = createClient()
      const { data: profile } = await supabase
        .from('profiles')
        .select('subscription_status, subscription_current_period_end, subscription_cancel_at_period_end, stripe_customer_id')
        .eq('id', user!.id)
        .single()

      setSubscription(profile)
    } catch (error) {
      console.error('Error al cargar suscripción:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleManageSubscription = async () => {
    try {
      const response = await fetch('/api/stripe/customer-portal', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
      })

      const data = await response.json()

      if (data.url) {
        window.location.href = data.url
      } else {
        throw new Error('URL del portal no devuelta')
      }
    } catch (error) {
      console.error('Error al abrir portal:', error)
      toast.error('Error al abrir portal del cliente')
    }
  }

  if (loading || authLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-slate-900 to-slate-950 flex items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin text-red-500" />
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-slate-900 to-slate-950">
      {/* Header */}
      <header className="sticky top-0 z-50 bg-slate-900/80 backdrop-blur-md border-b border-slate-800">
        <div className="max-w-7xl mx-auto px-4 py-4 flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button variant="ghost" size="sm" asChild>
              <Link href="/browse">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Volver
              </Link>
            </Button>
            <h1 className="text-xl font-semibold text-white">Suscripción Premium</h1>
          </div>
        </div>
      </header>

      {/* Content */}
      <main className="max-w-7xl mx-auto px-4 py-12">
        {/* Si ya tiene suscripción activa */}
        {subscription?.subscription_status === 'active' ? (
          <div className="max-w-2xl mx-auto">
            <Card className="p-8 bg-gradient-to-br from-slate-800/50 to-slate-900/50 border-slate-700">
              <div className="text-center mb-8">
                <div className="flex justify-center mb-6">
                  <PremiumBadge size="lg" expiresAt={subscriptionEndsAt} />
                </div>
                <div className="inline-flex items-center justify-center w-16 h-16 bg-green-500/20 rounded-full mb-4">
                  <CheckCircle className="h-8 w-8 text-green-500" />
                </div>
                <h2 className="text-2xl font-bold text-white mb-2">Suscripción Activa</h2>
                <p className="text-gray-400">Tienes acceso completo a todos los recursos premium</p>
              </div>

              <div className="space-y-4 mb-8">
                <div className="flex items-center justify-between p-4 bg-slate-900/50 rounded-lg">
                  <div className="flex items-center gap-3">
                    <CreditCard className="h-5 w-5 text-gray-400" />
                    <span className="text-gray-300">Estado</span>
                  </div>
                  <Badge className="bg-green-500/10 text-green-500 border-green-500/20">
                    Activa
                  </Badge>
                </div>

                {subscription.subscription_current_period_end && (
                  <div className="flex items-center justify-between p-4 bg-slate-900/50 rounded-lg">
                    <div className="flex items-center gap-3">
                      <Calendar className="h-5 w-5 text-gray-400" />
                      <span className="text-gray-300">Próximo cobro</span>
                    </div>
                    <span className="text-white">
                      {new Date(subscription.subscription_current_period_end).toLocaleDateString('es-ES')}
                    </span>
                  </div>
                )}

                {subscription.subscription_cancel_at_period_end && (
                  <div className="p-4 bg-orange-500/10 border border-orange-500/20 rounded-lg">
                    <p className="text-orange-400 text-sm">
                      Tu suscripción se cancelará al final del período actual
                    </p>
                  </div>
                )}
              </div>

              <Button
                onClick={handleManageSubscription}
                className="w-full bg-gradient-to-r from-blue-600 to-purple-600"
              >
                Administrar Suscripción
              </Button>
            </Card>
          </div>
        ) : (
          <>
            {/* Hero Section */}
            <div className="text-center mb-16">
              <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">
                Elige tu plan Premium
              </h1>
              <p className="text-xl text-gray-400 max-w-2xl mx-auto">
                Acceso ilimitado a todos los canales deportivos en alta definición.
                Cancela cuando quieras.
              </p>
            </div>

            {/* Pricing */}
            <div className="max-w-md mx-auto mb-16">
              <PricingCard />
            </div>

            {/* FAQ Section */}
            <div className="max-w-3xl mx-auto">
              <h2 className="text-2xl font-bold text-white text-center mb-8">
                Preguntas Frecuentes
              </h2>
              
              <div className="space-y-4">
                <Card className="p-6 bg-slate-800/50 border-slate-700">
                  <h3 className="text-lg font-semibold text-white mb-2">
                    ¿Cómo funciona el período de prueba?
                  </h3>
                  <p className="text-gray-400">
                    Tienes 30 días gratis para probar todos los recursos premium. 
                    Puedes cancelar en cualquier momento durante el período de prueba sin ser cobrado.
                  </p>
                </Card>

                <Card className="p-6 bg-slate-800/50 border-slate-700">
                  <h3 className="text-lg font-semibold text-white mb-2">
                    ¿Puedo cancelar mi suscripción?
                  </h3>
                  <p className="text-gray-400">
                    ¡Sí! Puedes cancelar tu suscripción en cualquier momento. 
                    Continuarás teniendo acceso hasta el final del período ya pagado.
                  </p>
                </Card>

                <Card className="p-6 bg-slate-800/50 border-slate-700">
                  <h3 className="text-lg font-semibold text-white mb-2">
                    ¿Qué formas de pago son aceptadas?
                  </h3>
                  <p className="text-gray-400">
                    Aceptamos todas las principales tarjetas de crédito y débito internacionales 
                    a través de Stripe, nuestra plataforma de pago segura.
                  </p>
                </Card>

                <Card className="p-6 bg-slate-800/50 border-slate-700">
                  <h3 className="text-lg font-semibold text-white mb-2">
                    ¿Cuántos dispositivos puedo usar?
                  </h3>
                  <p className="text-gray-400">
                    Con el plan Premium, puedes ver en hasta 4 dispositivos simultáneos 
                    y tener perfiles ilimitados en tu cuenta.
                  </p>
                </Card>
              </div>
            </div>

            {/* Contact */}
            <div className="text-center mt-16 pb-8">
              <p className="text-gray-400 mb-4">¿Todavía tienes dudas?</p>
              <Button variant="outline" asChild>
                <a href="mailto:<EMAIL>">
                  <Mail className="h-4 w-4 mr-2" />
                  Habla con soporte
                </a>
              </Button>
            </div>
          </>
        )}
      </main>
    </div>
  )
}