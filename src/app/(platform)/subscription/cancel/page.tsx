'use client'

import { useRouter } from 'next/navigation'
import { <PERSON><PERSON><PERSON><PERSON>, ArrowLeft, HelpCircle } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import Link from 'next/link'

export default function SubscriptionCancelPage() {
  const router = useRouter()

  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-950 to-black">
      {/* Background effects */}
      <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top,_var(--tw-gradient-stops))] from-gray-900/20 via-transparent to-transparent pointer-events-none" />
      
      <div className="relative z-10 flex items-center justify-center min-h-screen p-4">
        <Card className="max-w-md w-full bg-gray-900/50 backdrop-blur-xl border-gray-800">
          <div className="p-8 text-center space-y-6">
            {/* Cancel Icon */}
            <div className="relative mx-auto w-20 h-20">
              <div className="absolute inset-0 bg-yellow-500/20 rounded-full blur-xl" />
              <div className="relative bg-yellow-500 rounded-full p-4 w-20 h-20 flex items-center justify-center">
                <XCircle className="h-10 w-10 text-white" />
              </div>
            </div>

            {/* Cancel Message */}
            <div className="space-y-2">
              <h1 className="text-3xl font-bold text-white">
                Pago cancelado
              </h1>
              <p className="text-gray-400">
                No se realizó ningún cargo a tu tarjeta
              </p>
            </div>

            {/* Info */}
            <div className="bg-gray-800/50 rounded-lg p-4 space-y-2">
              <p className="text-sm text-gray-300">
                Tu sesión de pago fue cancelada. Si fue un error, puedes intentar nuevamente.
              </p>
              <p className="text-sm text-gray-400">
                Recuerda que con StreamPlus Premium obtienes:
              </p>
              <ul className="text-sm text-gray-400 space-y-1 mt-2">
                <li>• Acceso ilimitado a todos los canales</li>
                <li>• Sin anuncios ni interrupciones</li>
                <li>• Calidad HD en todos los streams</li>
                <li>• Soporte prioritario</li>
              </ul>
            </div>

            {/* Actions */}
            <div className="space-y-3 pt-4">
              <Button
                size="lg"
                className="w-full bg-gradient-to-r from-red-600 to-orange-600 hover:from-red-700 hover:to-orange-700 text-white font-semibold"
                onClick={() => router.push('/subscription')}
              >
                Intentar nuevamente
              </Button>
              
              <Button
                size="lg"
                variant="outline"
                className="w-full border-gray-700 text-gray-300 hover:bg-gray-800 hover:text-white"
                asChild
              >
                <Link href="/browse">
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  Volver a explorar
                </Link>
              </Button>
            </div>

            {/* Support */}
            <div className="flex items-center justify-center gap-2 text-xs text-gray-500">
              <HelpCircle className="h-3 w-3" />
              <span>
                ¿Problemas con el pago?{' '}
                <a href="mailto:<EMAIL>" className="text-red-400 hover:underline">
                  Contáctanos
                </a>
              </span>
            </div>
          </div>
        </Card>
      </div>
    </div>
  )
}