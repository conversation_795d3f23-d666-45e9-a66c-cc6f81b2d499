'use client'

import { Footer } from '@/components/footer'
import { BackToHome } from '@/components/back-to-home'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import Link from 'next/link'
import { 
  Home, Tv, Trophy, CreditCard, Smartphone, 
  Users, Shield, HelpCircle, FileText, Briefcase,
  Globe, ChevronRight
} from 'lucide-react'

export default function SitemapPage() {
  const sections = [
    {
      title: 'Principal',
      icon: Home,
      links: [
        { name: 'Inicio', href: '/' },
        { name: 'Características', href: '/features' },
        { name: '<PERSON><PERSON><PERSON>', href: '/subscription' },
        { name: 'App Móvil', href: '/mobile-app' }
      ]
    },
    {
      title: 'Plataforma',
      icon: Tv,
      links: [
        { name: 'Explorar', href: '/browse' },
        { name: '<PERSON> <PERSON><PERSON>', href: '/my-list' },
        { name: '<PERSON><PERSON><PERSON><PERSON>', href: '/continue-watching' },
        { name: 'Configurac<PERSON>', href: '/settings' }
      ]
    },
    {
      title: 'Deport<PERSON>',
      icon: Trophy,
      links: [
        { name: 'Todos los Deportes', href: '/sports' },
        { name: 'Fútbol', href: '/sports/football' },
        { name: 'Baloncesto', href: '/sports/basketball' },
        { name: 'Tenis', href: '/sports/tennis' },
        { name: 'Fórmula 1', href: '/sports/f1' },
        { name: 'eSports', href: '/sports/esports' },
        { name: 'EPG', href: '/sports/epg' },
        { name: 'Calendario', href: '/sports/calendar' }
      ]
    },
    {
      title: 'Cuenta',
      icon: Users,
      links: [
        { name: 'Iniciar Sesión', href: '/login' },
        { name: 'Registrarse', href: '/register' },
        { name: 'Mi Cuenta', href: '/account' },
        { name: 'Suscripción', href: '/subscription' },
        { name: 'Facturación', href: '/billing' },
        { name: 'Perfiles', href: '/profiles' }
      ]
    },
    {
      title: 'Soporte',
      icon: HelpCircle,
      links: [
        { name: 'Centro de Ayuda', href: '/help' },
        { name: 'FAQ', href: '/faq' },
        { name: 'Estado del Servicio', href: '/status' },
        { name: 'Guías', href: '/guides' }
      ]
    },
    {
      title: 'Compañía',
      icon: Briefcase,
      links: [
        { name: 'Sobre Nosotros', href: '/about' },
        { name: 'Carreras', href: '/careers' },
        { name: 'Prensa', href: '/press' },
        { name: 'Socios', href: '/partners' }
      ]
    },
    {
      title: 'Legal',
      icon: Shield,
      links: [
        { name: 'Términos de Servicio', href: '/terms' },
        { name: 'Política de Privacidad', href: '/privacy' },
        { name: 'Política de Cookies', href: '/cookies' },
        { name: 'GDPR', href: '/gdpr' }
      ]
    }
  ]

  return (
    <div className="min-h-screen bg-gradient-to-b from-slate-950 to-slate-900">
      {/* Hero Section */}
      <section className="py-16 border-b border-slate-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <BackToHome />
          <div className="text-center mb-8">
            <Badge className="mb-4 bg-gray-600 text-white">Navegación</Badge>
            <h1 className="text-4xl font-bold text-white mb-4">Mapa del Sitio</h1>
            <p className="text-gray-400">
              Encuentra rápidamente lo que buscas en StreamPlus España
            </p>
          </div>
        </div>
      </section>

      {/* Sitemap Content */}
      <section className="py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {sections.map((section, index) => (
              <Card key={index} className="bg-slate-800/50 border-slate-700">
                <CardHeader>
                  <CardTitle className="flex items-center gap-3 text-white">
                    <section.icon className="h-5 w-5 text-gray-400" />
                    {section.title}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {section.links.map((link, linkIndex) => (
                      <li key={linkIndex}>
                        <Link 
                          href={link.href}
                          className="flex items-center gap-2 text-gray-300 hover:text-white transition-colors group"
                        >
                          <ChevronRight className="h-4 w-4 text-gray-600 group-hover:text-gray-400" />
                          {link.name}
                        </Link>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Additional Links */}
          <Card className="mt-8 bg-slate-800/50 border-slate-700">
            <CardHeader>
              <CardTitle className="flex items-center gap-3 text-white">
                <Globe className="h-5 w-5 text-gray-400" />
                Enlaces Adicionales
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid md:grid-cols-3 gap-4">
                <Link 
                  href="/api/docs" 
                  className="text-gray-300 hover:text-white transition-colors flex items-center gap-2"
                >
                  <FileText className="h-4 w-4" />
                  Documentación API
                </Link>
                <Link 
                  href="/developers" 
                  className="text-gray-300 hover:text-white transition-colors flex items-center gap-2"
                >
                  <FileText className="h-4 w-4" />
                  Portal de Desarrolladores
                </Link>
                <Link 
                  href="/affiliates" 
                  className="text-gray-300 hover:text-white transition-colors flex items-center gap-2"
                >
                  <FileText className="h-4 w-4" />
                  Programa de Afiliados
                </Link>
              </div>
            </CardContent>
          </Card>

          {/* SEO Information */}
          <div className="mt-12 p-6 bg-blue-900/20 rounded-lg border border-blue-500/20">
            <h3 className="text-lg font-semibold text-white mb-2">Información SEO</h3>
            <p className="text-gray-300 mb-4">
              Este mapa del sitio ayuda a los motores de búsqueda a indexar correctamente nuestro contenido.
            </p>
            <div className="flex gap-4 text-sm">
              <span className="text-gray-400">Sitemap XML: /sitemap.xml</span>
              <span className="text-gray-400">Robots.txt: /robots.txt</span>
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  )
}