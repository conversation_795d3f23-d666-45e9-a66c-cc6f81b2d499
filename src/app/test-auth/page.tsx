"use client";

import { useStackApp, useUser } from "@stackframe/stack";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useState, useEffect } from "react";
import { toast } from "sonner";
import { useRouter } from "next/navigation";

export default function TestAuthPage() {
  const stackApp = useStackApp();
  const user = useUser();
  const router = useRouter();
  const [status, setStatus] = useState<any>({});
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    checkAuthStatus();
  }, []);

  const checkAuthStatus = async () => {
    setLoading(true);
    
    const newStatus = {
      clientEnvVars: {
        projectId: !!process.env.NEXT_PUBLIC_STACK_PROJECT_ID,
        publishableKey: !!process.env.NEXT_PUBLIC_STACK_PUBLISHABLE_CLIENT_KEY,
        appUrl: !!process.env.NEXT_PUBLIC_APP_URL,
      },
      stackApp: {
        exists: !!stackApp,
        methods: stackApp ? Object.keys(stackApp).slice(0, 5) : [],
      },
      currentUser: {
        exists: !!user,
        id: user?.id,
        email: user?.primaryEmail,
      },
      serverCheck: null
    };

    // Verificar servidor
    try {
      const response = await fetch('/api/auth/check', {
        credentials: 'include'
      });
      const data = await response.json();
      newStatus.serverCheck = data;
    } catch (error) {
      newStatus.serverCheck = { error: error.message };
    }

    setStatus(newStatus);
    setLoading(false);
  };

  const testLogin = async () => {
    try {
      const result = await stackApp.signInWithPassword({
        email: "<EMAIL>",
        password: "password123"
      });
      
      if (result?.userId) {
        toast.success("Login teste bem sucedido!");
        router.push('/browse');
      }
    } catch (error: any) {
      toast.error(`Erro no login teste: ${error.message}`);
    }
  };

  const testLogout = async () => {
    try {
      await stackApp.signOut();
      toast.success("Logout realizado!");
      router.push('/');
    } catch (error: any) {
      toast.error(`Erro no logout: ${error.message}`);
    }
  };

  return (
    <div className="min-h-screen p-8 bg-background">
      <Card className="max-w-4xl mx-auto p-6 space-y-6">
        <h1 className="text-2xl font-bold">Teste de Autenticação Stack Auth</h1>
        
        {loading ? (
          <div>Carregando...</div>
        ) : (
          <>
            <div className="space-y-4">
              <div>
                <h2 className="text-lg font-semibold mb-2">1. Variáveis de Ambiente (Cliente)</h2>
                <div className="space-y-1 text-sm">
                  <div>PROJECT_ID: {status.clientEnvVars?.projectId ? '✅ Configurada' : '❌ Faltando'}</div>
                  <div>PUBLISHABLE_KEY: {status.clientEnvVars?.publishableKey ? '✅ Configurada' : '❌ Faltando'}</div>
                  <div>APP_URL: {status.clientEnvVars?.appUrl ? '✅ Configurada' : '❌ Faltando'}</div>
                </div>
              </div>

              <div>
                <h2 className="text-lg font-semibold mb-2">2. Stack App</h2>
                <div className="space-y-1 text-sm">
                  <div>Instância: {status.stackApp?.exists ? '✅ Inicializada' : '❌ Não inicializada'}</div>
                  {status.stackApp?.methods?.length > 0 && (
                    <div>Métodos: {status.stackApp.methods.join(', ')}</div>
                  )}
                </div>
              </div>

              <div>
                <h2 className="text-lg font-semibold mb-2">3. Usuário Atual</h2>
                <div className="space-y-1 text-sm">
                  <div>Autenticado: {status.currentUser?.exists ? '✅ Sim' : '❌ Não'}</div>
                  {status.currentUser?.id && (
                    <>
                      <div>ID: {status.currentUser.id}</div>
                      <div>Email: {status.currentUser.email}</div>
                    </>
                  )}
                </div>
              </div>

              <div>
                <h2 className="text-lg font-semibold mb-2">4. Verificação do Servidor</h2>
                <pre className="text-xs bg-muted p-2 rounded">
                  {JSON.stringify(status.serverCheck, null, 2)}
                </pre>
              </div>
            </div>

            <div className="flex gap-4">
              <Button onClick={checkAuthStatus}>
                Recarregar Status
              </Button>
              
              {!user && (
                <Button onClick={() => router.push('/login')} variant="secondary">
                  Ir para Login
                </Button>
              )}
              
              {user && (
                <Button onClick={testLogout} variant="destructive">
                  Testar Logout
                </Button>
              )}
            </div>

            <div className="bg-yellow-100 dark:bg-yellow-900 p-4 rounded text-sm">
              <p className="font-semibold mb-2">Status:</p>
              {status.clientEnvVars?.projectId && status.clientEnvVars?.publishableKey ? (
                <p className="text-green-700 dark:text-green-300">
                  ✅ Variáveis configuradas! O login deve funcionar normalmente.
                </p>
              ) : (
                <div>
                  <p className="text-red-700 dark:text-red-300 mb-2">
                    ❌ Variáveis não carregadas. Execute:
                  </p>
                  <ol className="list-decimal list-inside space-y-1">
                    <li>Ctrl+C para parar o servidor</li>
                    <li>rm -rf .next</li>
                    <li>pnpm dev</li>
                  </ol>
                </div>
              )}
            </div>
          </>
        )}
      </Card>
    </div>
  );
}