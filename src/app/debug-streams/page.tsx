'use client'

import { useState } from 'react'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Loader2, CheckCircle, XCircle, AlertCircle, Play } from 'lucide-react'
import { EnhancedHlsPlayer } from '@/components/player/enhanced-hls-player'

interface DebugResult {
  url: string
  directAccessible: boolean
  requiresSpecialHeaders: boolean
  probableIssue: string
  recommendations: string[]
  results: Array<{
    url: string
    accessible: boolean
    statusCode?: number
    contentType?: string
    headers?: Record<string, string>
    error?: string
    method: string
    manifestContent?: string
    segments?: Array<{
      url: string
      accessible: boolean
      statusCode?: number
      error?: string
    }>
  }>
}

export default function DebugStreamsPage() {
  const [testUrl, setTestUrl] = useState('')
  const [testing, setTesting] = useState(false)
  const [debugResult, setDebugResult] = useState<DebugResult | null>(null)
  const [showPlayer, setShowPlayer] = useState(false)
  const [selectedHeaders, setSelectedHeaders] = useState<Record<string, string> | undefined>()
  
  // Canais problemáticos conhecidos
  const problematicChannels = [
    {
      name: 'Abu Dhabi Sports 1',
      url: 'https://vo-live-media.cdb.cdn.orange.com/Content/Channel/AbuDhabiSportsChannel1/HLS/index.m3u8'
    },
    {
      name: 'Abu Dhabi Sports 2',
      url: 'https://vo-live-media.cdb.cdn.orange.com/Content/Channel/AbuDhabiSportsChannel2/HLS/index.m3u8'
    },
    {
      name: 'ACC Digital Network',
      url: 'https://raycom-accdn-firetv.amagi.tv/playlist.m3u8'
    },
    {
      name: 'Alkass Five',
      url: 'https://liveakgr.alkassdigital.net/hls/live/2097037/Alkass5azq/master.m3u8'
    }
  ]
  
  const runDebug = async (url: string) => {
    setTesting(true)
    setDebugResult(null)
    setShowPlayer(false)
    
    try {
      const response = await fetch('/api/stream-debug', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ url })
      })
      
      const data = await response.json()
      setDebugResult(data)
      
      // Se encontrou headers que funcionam, usar no player
      const workingResult = data.results.find((r: any) => r.accessible && r.headers)
      if (workingResult) {
        setSelectedHeaders(workingResult.headers)
      }
    } catch (error) {
      console.error('Debug error:', error)
    } finally {
      setTesting(false)
    }
  }
  
  const getStatusColor = (statusCode?: number) => {
    if (!statusCode) return 'text-gray-500'
    if (statusCode >= 200 && statusCode < 300) return 'text-green-500'
    if (statusCode >= 400 && statusCode < 500) return 'text-red-500'
    if (statusCode >= 500) return 'text-orange-500'
    return 'text-yellow-500'
  }
  
  const getStatusBadge = (accessible: boolean, statusCode?: number) => {
    if (accessible) {
      return <Badge className="bg-green-600 border-0">✓ Accessible</Badge>
    }
    if (statusCode === 403) {
      return <Badge className="bg-red-600 border-0">403 Forbidden</Badge>
    }
    if (statusCode === 404) {
      return <Badge className="bg-orange-600 border-0">404 Not Found</Badge>
    }
    return <Badge className="bg-gray-600 border-0">Failed</Badge>
  }
  
  return (
    <div className="min-h-screen bg-black text-white p-8">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">Stream Debug Tool</h1>
        
        {/* URL Input */}
        <Card className="p-6 bg-gray-900 border-gray-800 mb-8">
          <div className="flex gap-4">
            <Input
              value={testUrl}
              onChange={(e) => setTestUrl(e.target.value)}
              placeholder="Enter stream URL to debug..."
              className="flex-1 bg-gray-800 border-gray-700"
            />
            <Button 
              onClick={() => runDebug(testUrl)} 
              disabled={!testUrl || testing}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {testing ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Testing...
                </>
              ) : (
                'Debug Stream'
              )}
            </Button>
          </div>
          
          {/* Quick test buttons */}
          <div className="mt-4">
            <p className="text-sm text-gray-400 mb-2">Quick test problematic channels:</p>
            <div className="flex flex-wrap gap-2">
              {problematicChannels.map(channel => (
                <Button
                  key={channel.url}
                  size="sm"
                  variant="outline"
                  onClick={() => {
                    setTestUrl(channel.url)
                    runDebug(channel.url)
                  }}
                  className="text-xs"
                >
                  {channel.name}
                </Button>
              ))}
            </div>
          </div>
        </Card>
        
        {/* Debug Results */}
        {debugResult && (
          <>
            {/* Summary */}
            <Card className="p-6 bg-gray-900 border-gray-800 mb-6">
              <h2 className="text-xl font-semibold mb-4">Debug Summary</h2>
              
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-gray-400">Direct Access:</span>
                  <div className="flex items-center gap-2">
                    {debugResult.directAccessible ? (
                      <CheckCircle className="h-5 w-5 text-green-500" />
                    ) : (
                      <XCircle className="h-5 w-5 text-red-500" />
                    )}
                    <span>{debugResult.directAccessible ? 'Yes' : 'No'}</span>
                  </div>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-gray-400">Probable Issue:</span>
                  <span className="text-yellow-500">{debugResult.probableIssue}</span>
                </div>
                
                {debugResult.requiresSpecialHeaders && (
                  <div className="flex items-center justify-between">
                    <span className="text-gray-400">Special Headers:</span>
                    <Badge className="bg-blue-600 border-0">Required</Badge>
                  </div>
                )}
              </div>
              
              {debugResult.recommendations.length > 0 && (
                <div className="mt-4">
                  <h3 className="font-medium mb-2">Recommendations:</h3>
                  <ul className="list-disc list-inside text-sm text-gray-400 space-y-1">
                    {debugResult.recommendations.map((rec, idx) => (
                      <li key={idx}>{rec}</li>
                    ))}
                  </ul>
                </div>
              )}
            </Card>
            
            {/* Detailed Results */}
            <Card className="p-6 bg-gray-900 border-gray-800 mb-6">
              <h2 className="text-xl font-semibold mb-4">Test Results</h2>
              
              <div className="space-y-4">
                {debugResult.results.map((result, idx) => (
                  <div key={idx} className="border border-gray-800 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center gap-3">
                        <h3 className="font-medium">Test #{idx + 1}</h3>
                        {getStatusBadge(result.accessible, result.statusCode)}
                      </div>
                      {result.statusCode && (
                        <span className={`font-mono ${getStatusColor(result.statusCode)}`}>
                          HTTP {result.statusCode}
                        </span>
                      )}
                    </div>
                    
                    {result.headers && (
                      <div className="mt-2">
                        <p className="text-sm text-gray-400 mb-1">Headers used:</p>
                        <pre className="text-xs bg-gray-800 p-2 rounded overflow-x-auto">
                          {JSON.stringify(result.headers, null, 2)}
                        </pre>
                      </div>
                    )}
                    
                    {result.error && (
                      <p className="text-sm text-red-400 mt-2">Error: {result.error}</p>
                    )}
                    
                    {result.manifestContent && (
                      <details className="mt-3">
                        <summary className="cursor-pointer text-sm text-blue-400">
                          View manifest content
                        </summary>
                        <pre className="mt-2 text-xs bg-gray-800 p-2 rounded overflow-x-auto">
                          {result.manifestContent}
                        </pre>
                      </details>
                    )}
                    
                    {result.segments && result.segments.length > 0 && (
                      <div className="mt-3">
                        <p className="text-sm text-gray-400 mb-2">Segment tests:</p>
                        <div className="space-y-1">
                          {result.segments.map((seg, segIdx) => (
                            <div key={segIdx} className="flex items-center gap-2 text-xs">
                              {seg.accessible ? (
                                <CheckCircle className="h-3 w-3 text-green-500" />
                              ) : (
                                <XCircle className="h-3 w-3 text-red-500" />
                              )}
                              <span className="text-gray-500">Segment {segIdx + 1}:</span>
                              <span className={seg.accessible ? 'text-green-400' : 'text-red-400'}>
                                {seg.accessible ? 'OK' : seg.error || 'Failed'}
                              </span>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </Card>
            
            {/* Test Player */}
            <Card className="p-6 bg-gray-900 border-gray-800">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-semibold">Test Player</h2>
                <Button
                  onClick={() => setShowPlayer(!showPlayer)}
                  className="bg-green-600 hover:bg-green-700"
                >
                  <Play className="h-4 w-4 mr-2" />
                  {showPlayer ? 'Hide' : 'Test'} Player
                </Button>
              </div>
              
              {showPlayer && (
                <div className="mt-4">
                  <EnhancedHlsPlayer
                    channelName="Debug Test"
                    streamUrl={testUrl}
                    headers={selectedHeaders}
                  />
                </div>
              )}
            </Card>
          </>
        )}
      </div>
    </div>
  )
}