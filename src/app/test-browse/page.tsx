'use client'

import { useState, useEffect } from 'react'
import { Card } from '@/components/ui/card'

export default function TestBrowsePage() {
  const [channels, setChannels] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const loadChannels = async () => {
      try {
        console.log('🔄 [TestBrowse] Iniciando carregamento...')
        
        const response = await fetch('/api/iptv-simple')
        console.log('📡 [TestBrowse] Response status:', response.status)
        
        const data = await response.json()
        console.log('📊 [TestBrowse] Data received:', data)
        
        if (data.success && Array.isArray(data.data)) {
          console.log('✅ [TestBrowse] Channels count:', data.data.length)
          setChannels(data.data.slice(0, 10)) // Apenas 10 primeiros para teste
        } else {
          throw new Error('Invalid data format')
        }
      } catch (err) {
        console.error('❌ [TestBrowse] Error:', err)
        setError(err instanceof Error ? err.message : 'Unknown error')
      } finally {
        setLoading(false)
      }
    }

    loadChannels()
  }, [])

  return (
    <div className="min-h-screen bg-black p-8">
      <h1 className="text-4xl font-bold text-white mb-8">Test Browse Page</h1>
      
      <div className="mb-4 text-white">
        <p>Loading: {loading ? 'Yes' : 'No'}</p>
        <p>Error: {error || 'None'}</p>
        <p>Channels: {channels.length}</p>
      </div>
      
      {loading && (
        <div className="text-white">Loading channels...</div>
      )}
      
      {error && (
        <div className="text-red-500">Error: {error}</div>
      )}
      
      {!loading && !error && channels.length === 0 && (
        <div className="text-yellow-500">No channels found</div>
      )}
      
      {!loading && !error && channels.length > 0 && (
        <div className="grid grid-cols-2 gap-4">
          {channels.map((channel, index) => (
            <Card key={`test-${index}`} className="p-4 bg-gray-900 text-white">
              <h3 className="font-bold">{channel.name || channel.channel}</h3>
              <p className="text-sm text-gray-400">{channel.country || 'N/A'}</p>
              <p className="text-xs text-gray-500 truncate">{channel.url}</p>
            </Card>
          ))}
        </div>
      )}
    </div>
  )
}