'use client'

import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Footer } from '@/components/footer'
import { BackToHome } from '@/components/back-to-home'
import { Button } from '@/components/ui/button'
import Link from 'next/link'
import { 
  Users, Target, Award, Globe, 
  Heart, Zap, Shield, Star
} from 'lucide-react'

export default function AboutPage() {
  const values = [
    {
      icon: Heart,
      title: "Pasión por el Deporte",
      description: "El deporte es nuestra pasión y queremos compartirla con millones de aficionados en toda España.",
      color: "text-red-500"
    },
    {
      icon: Shield,
      title: "Calidad Garantizada",
      description: "Nos comprometemos a ofrecer la mejor calidad de streaming y contenido exclusivo para nuestros usuarios.",
      color: "text-blue-500"
    },
    {
      icon: Users,
      title: "Comunidad Primero",
      description: "Nuestra comunidad es lo más importante. Escuchamos y mejoramos constantemente basándonos en vuestro feedback.",
      color: "text-green-500"
    },
    {
      icon: Zap,
      title: "Innovación Constante",
      description: "Utilizamos la última tecnología para ofrecer una experiencia de streaming incomparable.",
      color: "text-yellow-500"
    }
  ]

  const milestones = [
    {
      year: "2020",
      title: "El Comienzo",
      description: "NewSpports nace con la visión de revolucionar el streaming deportivo y de noticias."
    },
    {
      year: "2021",
      title: "Primera Liga",
      description: "Conseguimos los derechos de transmisión de LaLiga y principales competiciones europeas."
    },
    {
      year: "2022",
      title: "Expansión eSports",
      description: "Añadimos cobertura completa de eSports convirtiéndonos en la plataforma más completa."
    },
    {
      year: "2023",
      title: "1 Millón de Usuarios",
      description: "Alcanzamos nuestro primer millón de suscriptores activos en toda España."
    },
    {
      year: "2024",
      title: "Líderes del Mercado",
      description: "Nos consolidamos como la plataforma de streaming deportivo líder en España."
    }
  ]

  const team = [
    {
      name: "Carlos Rodríguez",
      role: "CEO & Co-fundador",
      description: "Ex-directivo de LaLiga con más de 15 años de experiencia en medios deportivos."
    },
    {
      name: "María García",
      role: "CTO & Co-fundadora",
      description: "Ingeniera de software especializada en tecnologías de streaming y sistemas distribuidos."
    },
    {
      name: "Javier López",
      role: "Director de Contenidos",
      description: "Periodista deportivo con amplia experiencia en televisión y medios digitales."
    },
    {
      name: "Ana Martín",
      role: "Directora de Marketing",
      description: "Experta en marketing digital y estrategias de crecimiento en el sector del entretenimiento."
    }
  ]

  return (
    <div className="min-h-screen bg-gradient-to-b from-slate-950 to-slate-900">
      {/* Hero Section */}
      <section className="relative py-20 overflow-hidden">
        <div className="absolute inset-0">
          <div className="absolute inset-0 bg-gradient-to-r from-blue-900/20 to-purple-900/20" />
        </div>
        
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <BackToHome />
          <Badge className="mb-4 bg-blue-600 text-white">Sobre Nosotros</Badge>
          <h1 className="text-5xl md:text-6xl font-bold text-white mb-6">
            La revolución del
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-purple-400"> streaming deportivo</span>
          </h1>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            NewSpports nació con una misión clara: democratizar el acceso al mejor contenido deportivo 
            mediante tecnología de vanguardia y una experiencia de usuario excepcional.
          </p>
        </div>
      </section>

      {/* Mission Section */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid md:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl font-bold text-white mb-6">Nuestra Misión</h2>
              <p className="text-gray-300 mb-4">
                En NewSpports, creemos que todos los aficionados al deporte merecen acceso a contenido 
                de calidad sin comprometer la experiencia. Nuestra misión es proporcionar la mejor plataforma 
                de streaming deportivo, combinando tecnología avanzada con contenido exclusivo.
              </p>
              <p className="text-gray-300 mb-6">
                Trabajamos incansablemente para ofrecer cobertura completa de todos los deportes, 
                desde el fútbol hasta los eSports, asegurando que nunca te pierdas los momentos más emocionantes.
              </p>
              <div className="flex gap-4">
                <div className="text-center">
                  <p className="text-4xl font-bold text-white">1M+</p>
                  <p className="text-gray-400">Usuarios Activos</p>
                </div>
                <div className="text-center">
                  <p className="text-4xl font-bold text-white">50+</p>
                  <p className="text-gray-400">Competiciones</p>
                </div>
                <div className="text-center">
                  <p className="text-4xl font-bold text-white">24/7</p>
                  <p className="text-gray-400">Contenido en Vivo</p>
                </div>
              </div>
            </div>
            <div className="relative">
              <div className="aspect-video rounded-xl overflow-hidden bg-gradient-to-br from-blue-600 to-purple-600 flex items-center justify-center">
                <Globe className="h-32 w-32 text-white opacity-20" />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Values Section */}
      <section className="py-16 border-t border-slate-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-white mb-4">Nuestros Valores</h2>
            <p className="text-gray-400 max-w-2xl mx-auto">
              Los principios que guían todo lo que hacemos en NewSpports
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {values.map((value, index) => (
              <Card 
                key={index} 
                className="bg-slate-800/50 border-slate-700 hover:border-slate-600 transition-all"
              >
                <CardHeader>
                  <value.icon className={`h-12 w-12 ${value.color} mb-4`} />
                  <CardTitle className="text-white">{value.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-gray-300">
                    {value.description}
                  </CardDescription>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Timeline Section */}
      <section className="py-16 border-t border-slate-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-white mb-4">Nuestra Historia</h2>
            <p className="text-gray-400 max-w-2xl mx-auto">
              Un viaje de innovación y crecimiento constante
            </p>
          </div>
          
          <div className="space-y-8">
            {milestones.map((milestone, index) => (
              <div key={index} className="flex gap-6 items-start">
                <div className="flex-shrink-0 w-24 text-right">
                  <Badge className="bg-blue-600 text-white">{milestone.year}</Badge>
                </div>
                <div className="flex-grow pb-8 border-l-2 border-slate-700 pl-6 relative">
                  <div className="absolute -left-2 top-0 w-4 h-4 bg-blue-600 rounded-full" />
                  <h3 className="text-xl font-semibold text-white mb-2">{milestone.title}</h3>
                  <p className="text-gray-300">{milestone.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Team Section */}
      <section className="py-16 border-t border-slate-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-white mb-4">Nuestro Equipo</h2>
            <p className="text-gray-400 max-w-2xl mx-auto">
              Las personas que hacen posible NewSpports
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {team.map((member, index) => (
              <Card 
                key={index} 
                className="bg-slate-800/50 border-slate-700 hover:border-slate-600 transition-all"
              >
                <CardHeader>
                  <div className="w-20 h-20 bg-gradient-to-br from-blue-600 to-purple-600 rounded-full mx-auto mb-4 flex items-center justify-center">
                    <Users className="h-10 w-10 text-white" />
                  </div>
                  <CardTitle className="text-white text-center">{member.name}</CardTitle>
                  <CardDescription className="text-center text-purple-400">
                    {member.role}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-300 text-center text-sm">
                    {member.description}
                  </p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Awards Section */}
      <section className="py-16 border-t border-slate-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-white mb-4">Reconocimientos</h2>
            <p className="text-gray-400 max-w-2xl mx-auto">
              Premios y reconocimientos que avalan nuestro trabajo
            </p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-6">
            <Card className="bg-gradient-to-br from-yellow-900/20 to-orange-900/20 border-yellow-500/20">
              <CardHeader className="text-center">
                <Award className="h-16 w-16 text-yellow-500 mx-auto mb-4" />
                <CardTitle className="text-white">Mejor Plataforma de Streaming 2024</CardTitle>
                <CardDescription>Premios Tecnología España</CardDescription>
              </CardHeader>
            </Card>
            
            <Card className="bg-gradient-to-br from-blue-900/20 to-purple-900/20 border-blue-500/20">
              <CardHeader className="text-center">
                <Target className="h-16 w-16 text-blue-500 mx-auto mb-4" />
                <CardTitle className="text-white">Innovación Digital 2023</CardTitle>
                <CardDescription>Digital Media Awards</CardDescription>
              </CardHeader>
            </Card>
            
            <Card className="bg-gradient-to-br from-green-900/20 to-emerald-900/20 border-green-500/20">
              <CardHeader className="text-center">
                <Star className="h-16 w-16 text-green-500 mx-auto mb-4" />
                <CardTitle className="text-white">Elección del Usuario 2024</CardTitle>
                <CardDescription>Sports Tech Awards</CardDescription>
              </CardHeader>
            </Card>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 border-t border-slate-800">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-4xl font-bold text-white mb-6">
            Únete a la revolución del streaming deportivo
          </h2>
          <p className="text-xl text-gray-300 mb-8">
            Forma parte de la comunidad que está cambiando la forma de ver deporte en España.
          </p>
          <div className="flex gap-4 justify-center">
            <Button size="lg" asChild>
              <Link href="/register">
                Comenzar Ahora
                <Zap className="ml-2 h-5 w-5" />
              </Link>
            </Button>
            <Button size="lg" variant="outline" asChild>
              <Link href="/careers">
                Trabaja con Nosotros
              </Link>
            </Button>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  )
}