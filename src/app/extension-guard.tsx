'use client'

import { useEffect } from 'react'

export function ExtensionGuard() {
  useEffect(() => {
    // Proteger contra erros de extensões
    const originalError = window.onerror
    
    window.onerror = function(msg, url, lineNo, columnNo, error) {
      // Ignorar erros de extensões
      if (url && url.includes('chrome-extension://')) {
        console.warn('Extension error ignored:', msg)
        return true // Previne propagação
      }
      
      // Chamar handler original para outros erros
      if (originalError) {
        return originalError(msg, url, lineNo, columnNo, error)
      }
      return false
    }
    
    // Proteger console.error também
    const originalConsoleError = console.error
    console.error = function(...args) {
      const errorStr = args.join(' ')
      if (errorStr.includes('chrome-extension://')) {
        console.warn('Extension error suppressed')
        return
      }
      originalConsoleError.apply(console, args)
    }
    
    // Cleanup
    return () => {
      window.onerror = originalError
      console.error = originalConsoleError
    }
  }, [])
  
  return null
}