'use client'

import { PremiumBadge } from '@/components/premium-badge'
import { TrialStatus } from '@/components/trial-status'
import { SubscriptionStatus } from '@/components/subscription-status'
import { useAuth } from '@/hooks/use-auth'
import { useSubscription } from '@/hooks/use-subscription'
import { Card } from '@/components/ui/card'

export default function TestPremiumBadgePage() {
  const { user } = useAuth()
  const subscription = useSubscription()

  return (
    <div className="p-8 space-y-8">
      <h1 className="text-2xl font-bold">Teste de Badges de Status</h1>
      
      <Card className="p-6 space-y-4">
        <h2 className="text-lg font-semibold">Informações do Usuário</h2>
        <div className="space-y-2 text-sm">
          <div>Email: {user?.primaryEmail || 'Não logado'}</div>
          <div>ID: {user?.id || 'N/A'}</div>
          <div>Loading: {subscription.loading ? 'Sim' : 'Não'}</div>
          <div>Is Subscribed: {subscription.isSubscribed ? 'Sim' : 'Não'}</div>
          <div>Is In Trial: {subscription.isInTrial ? 'Sim' : 'Não'}</div>
          <div>Has Access: {subscription.hasAccess ? 'Sim' : 'Não'}</div>
          <div>Subscription Tier: {subscription.subscriptionTier || 'N/A'}</div>
          <div>Ends At: {subscription.subscriptionEndsAt || 'N/A'}</div>
        </div>
      </Card>

      <Card className="p-6 space-y-4">
        <h2 className="text-lg font-semibold">Badges Individuais</h2>
        
        <div className="space-y-4">
          <div>
            <p className="text-sm text-gray-500 mb-2">Premium Badge (Small):</p>
            <PremiumBadge size="sm" expiresAt="2025-08-28T05:11:46.035+00:00" />
          </div>
          
          <div>
            <p className="text-sm text-gray-500 mb-2">Premium Badge (Medium):</p>
            <PremiumBadge size="md" expiresAt="2025-08-28T05:11:46.035+00:00" />
          </div>
          
          <div>
            <p className="text-sm text-gray-500 mb-2">Premium Badge (Large):</p>
            <PremiumBadge size="lg" expiresAt="2025-08-28T05:11:46.035+00:00" />
          </div>
          
          <div>
            <p className="text-sm text-gray-500 mb-2">Trial Status:</p>
            <TrialStatus />
          </div>
        </div>
      </Card>

      <Card className="p-6 space-y-4">
        <h2 className="text-lg font-semibold">Componente de Status (usado no header)</h2>
        <div className="flex items-center gap-4">
          <SubscriptionStatus />
          <span className="text-sm text-gray-500">← Este deve aparecer no header</span>
        </div>
      </Card>
    </div>
  )
}