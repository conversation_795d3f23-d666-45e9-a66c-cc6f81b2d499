'use client'

import { Footer } from '@/components/footer'
import { BackToHome } from '@/components/back-to-home'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { 
  Search, HelpCircle, MessageSquare, Book, 
  Smartphone, CreditCard, Tv, Settings,
  Shield, Zap, Globe, Users
} from 'lucide-react'
import Link from 'next/link'
import { useState } from 'react'

export default function HelpPage() {
  const [searchQuery, setSearchQuery] = useState('')

  const categories = [
    {
      icon: CreditCard,
      title: 'Facturación y Pagos',
      description: 'Gestión de suscripciones, métodos de pago y facturas',
      articles: 12,
      color: 'text-green-400',
      href: '/help/billing'
    },
    {
      icon: Tv,
      title: 'Reproducción y Calidad',
      description: 'Problemas de streaming, calidad de video y audio',
      articles: 18,
      color: 'text-blue-400',
      href: '/help/playback'
    },
    {
      icon: Smartphone,
      title: 'Dispositivos y Apps',
      description: 'Configuración en diferentes dispositivos y aplicaciones',
      articles: 15,
      color: 'text-purple-400',
      href: '/help/devices'
    },
    {
      icon: Settings,
      title: 'Cuenta y Configuración',
      description: 'Gestión de perfil, preferencias y configuración',
      articles: 10,
      color: 'text-orange-400',
      href: '/help/account'
    },
    {
      icon: Shield,
      title: 'Seguridad y Privacidad',
      description: 'Protección de cuenta, privacidad y seguridad',
      articles: 8,
      color: 'text-red-400',
      href: '/help/security'
    },
    {
      icon: Globe,
      title: 'Disponibilidad y Contenido',
      description: 'Acceso regional, catálogo y programación',
      articles: 14,
      color: 'text-teal-400',
      href: '/help/content'
    }
  ]

  const popularArticles = [
    {
      title: '¿Cómo cambio mi plan de suscripción?',
      category: 'Facturación',
      views: '12.5k'
    },
    {
      title: '¿Por qué el video se ve pixelado o con baja calidad?',
      category: 'Reproducción',
      views: '9.8k'
    },
    {
      title: '¿Cómo descargo la app en mi Smart TV?',
      category: 'Dispositivos',
      views: '8.2k'
    },
    {
      title: '¿Puedo compartir mi cuenta con familiares?',
      category: 'Cuenta',
      views: '7.1k'
    },
    {
      title: '¿Cómo activo los subtítulos?',
      category: 'Reproducción',
      views: '6.5k'
    }
  ]

  const quickActions = [
    {
      icon: MessageSquare,
      title: 'Chat en Vivo',
      description: 'Habla con un agente ahora',
      available: '24/7',
      action: 'Iniciar Chat'
    },
    {
      icon: Book,
      title: 'Guías Detalladas',
      description: 'Tutoriales paso a paso',
      available: '85 guías',
      action: 'Ver Guías'
    },
    {
      icon: Users,
      title: 'Comunidad',
      description: 'Pregunta a otros usuarios',
      available: 'Activa',
      action: 'Unirse'
    }
  ]

  return (
    <div className="min-h-screen bg-gradient-to-b from-slate-950 to-slate-900">
      {/* Hero Section */}
      <section className="py-20 border-b border-slate-800">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <BackToHome align="left" />
          <Badge className="mb-4 bg-blue-600 text-white">Centro de Ayuda</Badge>
          <h1 className="text-5xl font-bold text-white mb-6">
            ¿En qué podemos ayudarte?
          </h1>
          <p className="text-xl text-gray-300 mb-8">
            Encuentra respuestas rápidas o contacta con nuestro equipo de soporte
          </p>
          
          {/* Search Bar */}
          <div className="max-w-2xl mx-auto relative">
            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
            <Input
              type="text"
              placeholder="Buscar en el centro de ayuda..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-12 pr-4 py-6 text-lg bg-slate-800 border-slate-700 text-white placeholder-gray-400"
            />
          </div>
        </div>
      </section>

      {/* Quick Actions */}
      <section className="py-12 border-b border-slate-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid md:grid-cols-3 gap-6">
            {quickActions.map((action, index) => (
              <Card key={index} className="bg-slate-800/50 border-slate-700 hover:border-slate-600 transition-all">
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <action.icon className="h-10 w-10 text-blue-400" />
                    <Badge variant="secondary" className="bg-slate-700">
                      {action.available}
                    </Badge>
                  </div>
                  <CardTitle className="text-white mt-4">{action.title}</CardTitle>
                  <CardDescription>{action.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <Button className="w-full" variant="outline">
                    {action.action}
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Categories */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold text-white mb-8">Explorar por Categoría</h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {categories.map((category, index) => (
              <Link key={index} href={category.href}>
                <Card className="bg-slate-800/50 border-slate-700 hover:border-slate-600 transition-all cursor-pointer group">
                  <CardHeader>
                    <div className="flex items-center justify-between mb-4">
                      <category.icon className={`h-12 w-12 ${category.color}`} />
                      <span className="text-sm text-gray-400">{category.articles} artículos</span>
                    </div>
                    <CardTitle className="text-white group-hover:text-blue-400 transition-colors">
                      {category.title}
                    </CardTitle>
                    <CardDescription>{category.description}</CardDescription>
                  </CardHeader>
                </Card>
              </Link>
            ))}
          </div>
        </div>
      </section>

      {/* Popular Articles */}
      <section className="py-16 border-t border-slate-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between mb-8">
            <h2 className="text-3xl font-bold text-white">Artículos Populares</h2>
            <Button variant="outline" asChild>
              <Link href="/help/all">Ver todos</Link>
            </Button>
          </div>
          
          <div className="space-y-4">
            {popularArticles.map((article, index) => (
              <Card key={index} className="bg-slate-800/50 border-slate-700 hover:border-slate-600 transition-all cursor-pointer">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                      <HelpCircle className="h-5 w-5 text-gray-400" />
                      <div>
                        <h3 className="text-white font-medium hover:text-blue-400 transition-colors">
                          {article.title}
                        </h3>
                        <p className="text-sm text-gray-400 mt-1">
                          {article.category} · {article.views} vistas
                        </p>
                      </div>
                    </div>
                    <Zap className="h-5 w-5 text-yellow-400" />
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Still Need Help */}
      <section className="py-20 border-t border-slate-800">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold text-white mb-6">
            ¿No encuentras lo que buscas?
          </h2>
          <p className="text-xl text-gray-300 mb-8">
            Nuestro equipo de soporte está disponible 24/7 para ayudarte
          </p>
          <div className="flex gap-4 justify-center">
            <Button size="lg" className="bg-blue-600 hover:bg-blue-700">
              <MessageSquare className="mr-2 h-5 w-5" />
              Contactar Soporte
            </Button>
            <Button size="lg" variant="outline">
              <Book className="mr-2 h-5 w-5" />
              Ver FAQ Completo
            </Button>
          </div>
          
          <div className="mt-12 grid md:grid-cols-3 gap-6 text-left">
            <Card className="bg-green-900/20 border-green-500/20">
              <CardContent className="p-6">
                <div className="text-green-400 mb-2">Tiempo de respuesta</div>
                <div className="text-2xl font-bold text-white">&lt; 2 min</div>
                <div className="text-sm text-gray-400">Chat en vivo</div>
              </CardContent>
            </Card>
            <Card className="bg-blue-900/20 border-blue-500/20">
              <CardContent className="p-6">
                <div className="text-blue-400 mb-2">Satisfacción</div>
                <div className="text-2xl font-bold text-white">98%</div>
                <div className="text-sm text-gray-400">Usuarios satisfechos</div>
              </CardContent>
            </Card>
            <Card className="bg-purple-900/20 border-purple-500/20">
              <CardContent className="p-6">
                <div className="text-purple-400 mb-2">Disponibilidad</div>
                <div className="text-2xl font-bold text-white">24/7</div>
                <div className="text-sm text-gray-400">Siempre disponible</div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  )
}