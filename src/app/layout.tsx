import type { <PERSON>ada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { ThemeProvider } from "@/components/theme-provider";
import { Toaster } from "@/components/ui/sonner";
// import { AutoUpdateProvider } from "@/components/providers/auto-update-provider"; // DESABILITADO - usando sports-sync.service
import { SportsSyncProvider } from "@/components/providers/sports-sync-provider";
import { StackProviderWrapper } from "@/lib/auth/stack-provider-wrapper";
import { ProfileSync } from "@/components/auth/profile-sync";
import { TestUserSync } from "@/components/auth/test-user-sync";
import { TrialDebug } from "@/components/debug/trial-debug";
import { AuthSyncProvider } from "@/components/auth/auth-sync-provider";
import { ErrorBoundary } from "@/components/error-boundary";
import Script from "next/script";
import { AnonymousAccessChecker } from "@/components/anonymous-access-checker";
// import { BrowserExtensionWarning } from "@/components/browser-extension-warning"; // DESABILITADO - causando erros

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: {
    template: "%s | NewSpports",
    default: "NewSpports - Streaming Premium de Deportes",
  },
  description: "Plataforma premium de streaming de deportes y noticias",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="es" suppressHydrationWarning>
      <head>
        <link rel="icon" href="/favicon.svg" type="image/svg+xml" />
        <link rel="icon" href="/favicon.ico" sizes="any" />
        <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
        <link rel="manifest" href="/site.webmanifest" />
        <script 
          dangerouslySetInnerHTML={{
            __html: `
              // Proteção IMEDIATA contra erros de extensões
              (function() {
                if (typeof window === 'undefined') return;
                
                // Override imediato do error handler
                window.onerror = function(msg, src, line, col, err) {
                  if (src && src.includes('extension://')) return true;
                  if (msg && typeof msg === 'string') {
                    if (msg.includes("Cannot read properties of null (reading 'type')")) return true;
                    if (msg.includes('chrome-extension://')) return true;
                    if (msg.includes('egjidjbpglichdcondbcbdnbeeppgdph')) return true;
                  }
                  return false;
                };
                
                // Override de unhandledrejection
                window.addEventListener('unhandledrejection', function(e) {
                  if (e.reason && e.reason.message && e.reason.message.includes("Cannot read properties of null")) {
                    e.preventDefault();
                  }
                }, true);
              })();
            `
          }}
        />
      </head>
      <body className={`${inter.className} overflow-x-hidden`}>
        <ThemeProvider
          attribute="class"
          defaultTheme="dark"
          enableSystem
          disableTransitionOnChange
        >
          <ErrorBoundary>
            <StackProviderWrapper>
              <AuthSyncProvider>
                <ProfileSync />
                <TestUserSync />
                <AnonymousAccessChecker />
                <SportsSyncProvider>
                  {children}
                </SportsSyncProvider>
              </AuthSyncProvider>
            </StackProviderWrapper>
            <Toaster />
            <TrialDebug />
          </ErrorBoundary>
        </ThemeProvider>
      </body>
    </html>
  );
}