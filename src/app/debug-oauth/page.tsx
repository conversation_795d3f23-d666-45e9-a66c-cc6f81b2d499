"use client";

import { useStackApp, useUser } from "@stackframe/stack";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useState, useEffect } from "react";
import { toast } from "sonner";

export default function DebugOAuthPage() {
  const stackApp = useStackApp();
  const user = useUser();
  const [debugInfo, setDebugInfo] = useState<any>({});
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    collectDebugInfo();
  }, [stackApp, user]);

  const collectDebugInfo = () => {
    const info = {
      timestamp: new Date().toISOString(),
      stackApp: {
        exists: !!stackApp,
        type: typeof stackApp,
        methods: stackApp ? Object.keys(stackApp).filter(k => typeof stackApp[k] === 'function') : [],
        properties: stackApp ? Object.keys(stackApp).filter(k => typeof stackApp[k] !== 'function') : [],
        urls: stackApp?.urls || {},
      },
      user: {
        exists: !!user,
        id: user?.id,
        email: user?.primaryEmail,
        provider: user?.selectedTeam?.displayName || 'none',
      },
      browser: {
        userAgent: navigator.userAgent,
        language: navigator.language,
        cookies: document.cookie ? 'presente' : 'vazio',
      },
      environment: {
        projectId: !!process.env.NEXT_PUBLIC_STACK_PROJECT_ID,
        publishableKey: !!process.env.NEXT_PUBLIC_STACK_PUBLISHABLE_CLIENT_KEY,
        appUrl: process.env.NEXT_PUBLIC_APP_URL,
      }
    };

    setDebugInfo(info);
  };

  const testGoogleOAuth = async () => {
    try {
      setLoading(true);
      console.log("🔍 [DEBUG OAUTH] Iniciando teste de OAuth Google...");
      console.log("🔍 [DEBUG OAUTH] stackApp:", stackApp);
      console.log("🔍 [DEBUG OAUTH] signInWithOAuth exists?", !!stackApp?.signInWithOAuth);
      console.log("🔍 [DEBUG OAUTH] Type:", typeof stackApp?.signInWithOAuth);

      if (!stackApp) {
        throw new Error("Stack App não inicializado");
      }

      if (typeof stackApp.signInWithOAuth !== 'function') {
        throw new Error("signInWithOAuth não é uma função");
      }

      console.log("🔍 [DEBUG OAUTH] Chamando signInWithOAuth('google')...");
      
      // Adicionar um listener para verificar se o redirect vai acontecer
      let redirectHappened = false;
      const checkRedirect = setInterval(() => {
        if (window.location.href !== debugInfo.browser?.currentUrl) {
          redirectHappened = true;
          console.log("✅ [DEBUG OAUTH] Redirect detectado!");
          clearInterval(checkRedirect);
        }
      }, 100);

      // Tentar OAuth
      await stackApp.signInWithOAuth('google');

      // Se chegou aqui e não houve redirect em 3 segundos, algo deu errado
      setTimeout(() => {
        clearInterval(checkRedirect);
        if (!redirectHappened) {
          console.error("❌ [DEBUG OAUTH] Redirect não aconteceu após 3 segundos");
          toast.error("OAuth não redirecionou - verificar configuração");
        }
      }, 3000);

    } catch (error: any) {
      console.error("❌ [DEBUG OAUTH] Erro:", error);
      toast.error(`Erro: ${error.message}`);
    } finally {
      setTimeout(() => setLoading(false), 3000);
    }
  };

  const testSignOut = async () => {
    try {
      if (user) {
        await user.signOut();
        toast.success("Logout realizado!");
        window.location.reload();
      }
    } catch (error: any) {
      toast.error(`Erro no logout: ${error.message}`);
    }
  };

  return (
    <div className="min-h-screen p-8 bg-background">
      <Card className="max-w-4xl mx-auto p-6 space-y-6">
        <h1 className="text-2xl font-bold">Debug OAuth - Stack Auth</h1>
        
        <div className="space-y-4">
          <div className="bg-muted p-4 rounded">
            <h2 className="font-semibold mb-2">Stack App Status</h2>
            <pre className="text-xs overflow-auto">
              {JSON.stringify(debugInfo.stackApp, null, 2)}
            </pre>
          </div>

          <div className="bg-muted p-4 rounded">
            <h2 className="font-semibold mb-2">User Status</h2>
            <pre className="text-xs overflow-auto">
              {JSON.stringify(debugInfo.user, null, 2)}
            </pre>
          </div>

          <div className="bg-muted p-4 rounded">
            <h2 className="font-semibold mb-2">Environment</h2>
            <pre className="text-xs overflow-auto">
              {JSON.stringify(debugInfo.environment, null, 2)}
            </pre>
          </div>

          <div className="bg-muted p-4 rounded">
            <h2 className="font-semibold mb-2">Browser Info</h2>
            <pre className="text-xs overflow-auto">
              {JSON.stringify(debugInfo.browser, null, 2)}
            </pre>
          </div>
        </div>

        <div className="flex gap-4">
          <Button 
            onClick={testGoogleOAuth} 
            disabled={loading || !!user}
            variant="default"
          >
            {loading ? "Testando..." : "Testar Login Google"}
          </Button>
          
          {user && (
            <Button onClick={testSignOut} variant="destructive">
              Fazer Logout
            </Button>
          )}
          
          <Button onClick={collectDebugInfo} variant="outline">
            Atualizar Debug Info
          </Button>
        </div>

        <div className="text-sm text-muted-foreground">
          <p>Abra o console do navegador (F12) para ver logs detalhados.</p>
          <p>URL atual: {typeof window !== 'undefined' ? window.location.href : 'loading...'}</p>
        </div>
      </Card>
    </div>
  );
}