'use client'

import { Logo, LogoSimple } from '@/components/logo'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

export default function LogoDemoPage() {
  return (
    <div className="min-h-screen bg-slate-950 p-8">
      <div className="max-w-4xl mx-auto space-y-8">
        <h1 className="text-4xl font-bold text-white mb-8">Logo NewSpports - Demonstração</h1>
        
        <Card className="bg-slate-900 border-slate-800">
          <CardHeader>
            <CardTitle className="text-white">Logo Principal - Diferentes Tamanhos</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="flex items-center gap-4">
              <span className="text-gray-400 w-20">Small:</span>
              <Logo size="sm" className="text-white" />
            </div>
            <div className="flex items-center gap-4">
              <span className="text-gray-400 w-20">Medium:</span>
              <Logo size="md" className="text-white" />
            </div>
            <div className="flex items-center gap-4">
              <span className="text-gray-400 w-20">Large:</span>
              <Logo size="lg" className="text-white" />
            </div>
            <div className="flex items-center gap-4">
              <span className="text-gray-400 w-20">XLarge:</span>
              <Logo size="xl" className="text-white" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-slate-900 border-slate-800">
          <CardHeader>
            <CardTitle className="text-white">Logo Simplificado - Diferentes Tamanhos</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="flex items-center gap-4">
              <span className="text-gray-400 w-20">Small:</span>
              <LogoSimple size="sm" className="text-white" />
            </div>
            <div className="flex items-center gap-4">
              <span className="text-gray-400 w-20">Medium:</span>
              <LogoSimple size="md" className="text-white" />
            </div>
            <div className="flex items-center gap-4">
              <span className="text-gray-400 w-20">Large:</span>
              <LogoSimple size="lg" className="text-white" />
            </div>
            <div className="flex items-center gap-4">
              <span className="text-gray-400 w-20">XLarge:</span>
              <LogoSimple size="xl" className="text-white" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-slate-900 border-slate-800">
          <CardHeader>
            <CardTitle className="text-white">Logo com Cores Diferentes</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="flex items-center gap-4">
              <span className="text-gray-400 w-20">Vermelho:</span>
              <Logo size="lg" className="text-red-500" />
            </div>
            <div className="flex items-center gap-4">
              <span className="text-gray-400 w-20">Azul:</span>
              <Logo size="lg" className="text-blue-500" />
            </div>
            <div className="flex items-center gap-4">
              <span className="text-gray-400 w-20">Verde:</span>
              <Logo size="lg" className="text-green-500" />
            </div>
            <div className="flex items-center gap-4">
              <span className="text-gray-400 w-20">Gradiente:</span>
              <Logo size="lg" className="bg-gradient-to-r from-red-500 to-yellow-500 bg-clip-text text-transparent" />
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}