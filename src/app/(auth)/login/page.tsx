"use client";

import { useState } from "react";
import { useStackApp } from "@stackframe/stack";
import { useRouter } from "next/navigation";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { loginSchema, type LoginInput } from "@/lib/auth/validation";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Card } from "@/components/ui/card";
import { toast } from "sonner";
import Link from "next/link";
import { Loader2, Mail, Lock } from "lucide-react";
import { Logo } from "@/components/logo";

export default function LoginPage() {
  const stackApp = useStackApp();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);

  const form = useForm<LoginInput>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: "",
      password: "",
    },
  });

  async function onSubmit(data: LoginInput) {
    try {
      setIsLoading(true);
      console.log('🔐 [LOGIN] ===== INICIANDO PROCESSO DE LOGIN =====');
      console.log('🔐 [LOGIN] Dados:', { 
        email: data.email,
        hasPassword: !!data.password,
        timestamp: new Date().toISOString()
      });
      
      // Verificar se stackApp existe
      console.log('🔐 [LOGIN] Verificando stackApp:', {
        exists: !!stackApp,
        type: typeof stackApp,
        methods: stackApp ? Object.keys(stackApp) : []
      });
      
      if (!stackApp) {
        throw new Error('Stack App não inicializado');
      }
      
      console.log('🔐 [LOGIN] Chamando stackApp.signInWithCredential...');
      console.log('🔐 [LOGIN] Métodos disponíveis no stackApp:', {
        signInWithCredential: typeof stackApp.signInWithCredential,
        signInWithOAuth: typeof stackApp.signInWithOAuth,
        signUpWithCredential: typeof stackApp.signUpWithCredential,
      });
      
      // Usar Stack Auth para login
      const result = await stackApp.signInWithCredential({
        email: data.email,
        password: data.password,
      });
      
      console.log('🔐 [LOGIN] Resposta do Stack Auth:', {
        hasResult: !!result,
        status: result?.status,
        error: result?.error,
        keys: result ? Object.keys(result) : [],
        fullResult: result
      });
      
      if (result?.status === 'error') {
        console.error('❌ [LOGIN] Erro do Stack Auth:', result.error);
        throw new Error(result.error?.message || 'Login failed');
      }
      
      // Login bem sucedido se não houver erro
      console.log('✅ [LOGIN] Login bem sucedido!');
      toast.success("¡Inicio de sesión exitoso!");
      
      // Verificar cookies
      console.log('🍪 [LOGIN] Verificando cookies após login...');
      const checkResponse = await fetch('/api/auth/check', {
        credentials: 'include'
      });
      const checkData = await checkResponse.json();
      console.log('🍪 [LOGIN] Status de autenticação:', checkData);
      
      // Aguardar um momento para garantir que os cookies foram configurados
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Redirecionar para /browse
      console.log('🚀 [LOGIN] Redirecionando para /browse...');
      router.push('/browse');
      
    } catch (error: any) {
      console.error("❌ [LOGIN] ERRO DETALHADO:", {
        message: error.message,
        stack: error.stack,
        name: error.name,
        code: error.code,
        fullError: error
      });
      
      // Mensagens de erro específicas
      if (error.message?.includes("password") || error.message?.includes("credentials")) {
        toast.error("Email o contraseña incorrectos");
      } else if (error.message?.includes("not found") || error.message?.includes("User does not exist")) {
        toast.error("Usuario no encontrado. ¿Necesitas crear una cuenta?");
      } else if (error.message?.includes("Stack App") || error.message?.includes("environment")) {
        toast.error("Error de configuración. Por favor, recarga la página.");
      } else {
        toast.error("Error al iniciar sesión. Por favor, intenta de nuevo.");
      }
    } finally {
      setIsLoading(false);
      console.log('🔐 [LOGIN] ===== FIM DO PROCESSO DE LOGIN =====');
    }
  }

  const handleOAuthLogin = async () => {
    try {
      setIsLoading(true);
      console.log(`🔐 [OAUTH] ===== INICIANDO LOGIN COM GOOGLE =====`);
      console.log(`🔐 [OAUTH] stackApp existe?`, !!stackApp);
      console.log(`🔐 [OAUTH] stackApp.signInWithOAuth existe?`, typeof stackApp?.signInWithOAuth);
      console.log(`🔐 [OAUTH] Métodos disponíveis:`, stackApp ? Object.keys(stackApp).filter(k => typeof stackApp[k] === 'function') : []);
      
      if (!stackApp) {
        console.error('❌ [OAUTH] Stack App não inicializado!');
        throw new Error('Stack App não inicializado');
      }
      
      if (typeof stackApp.signInWithOAuth !== 'function') {
        console.error('❌ [OAUTH] signInWithOAuth não é uma função!');
        console.error('❌ [OAUTH] Tipo:', typeof stackApp.signInWithOAuth);
        throw new Error('Método signInWithOAuth não disponível');
      }
      
      // Verificar URLs configuradas
      console.log(`🔐 [OAUTH] URLs do stackApp:`, {
        signIn: stackApp.urls?.signIn,
        afterSignIn: stackApp.urls?.afterSignIn,
        oauthCallback: stackApp.urls?.oauthCallback
      });
      
      // Usar Stack Auth para OAuth - isso vai redirecionar
      console.log(`🔐 [OAUTH] Chamando stackApp.signInWithOAuth('google')...`);
      console.log(`🔐 [OAUTH] Nota: Este método faz redirect para o Google`);
      
      // signInWithOAuth não retorna uma promise com resultado, ele redireciona
      await stackApp.signInWithOAuth('google');
      
      // Este código só executa se houver erro no redirect
      console.log(`⚠️ [OAUTH] Se chegou aqui, o redirect não funcionou`);
      
    } catch (error: any) {
      console.error(`❌ [OAUTH] ERRO DETALHADO:`, {
        message: error.message,
        stack: error.stack,
        name: error.name,
        code: error.code,
        fullError: error
      });
      
      // Log adicional para debug
      console.error(`❌ [OAUTH] stackApp no momento do erro:`, stackApp);
      console.error(`❌ [OAUTH] window.location:`, window.location.href);
      
      if (error.message?.includes('Stack App') || error.message?.includes('environment')) {
        toast.error('Error de configuración. Por favor, recarga la página.');
      } else if (error.message?.includes('signInWithOAuth')) {
        toast.error('Método de login con Google no disponible. Recarga la página.');
      } else {
        toast.error('Error al iniciar sesión con Google. Por favor, intenta de nuevo.');
      }
    } finally {
      // Não desativar loading imediatamente pois pode haver redirect
      setTimeout(() => {
        setIsLoading(false);
      }, 3000);
      console.log(`🔐 [OAUTH] ===== FIM DO LOGIN COM GOOGLE =====`);
    }
  };


  return (
    <div className="min-h-screen flex items-center justify-center bg-background p-4">
      <Card className="w-full max-w-md p-8 space-y-6">
        <div className="text-center space-y-2">
          <Logo size="lg" className="justify-center mb-4" />
          <h1 className="text-2xl font-bold">Iniciar Sesión</h1>
          <p className="text-muted-foreground">
            Inicia sesión para acceder a tu cuenta
          </p>
        </div>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Correo electrónico</FormLabel>
                  <FormControl>
                    <div className="relative">
                      <Mail className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                      <Input
                        {...field}
                        type="email"
                        placeholder="<EMAIL>"
                        className="pl-10"
                        disabled={isLoading}
                      />
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="password"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Contraseña</FormLabel>
                  <FormControl>
                    <div className="relative">
                      <Lock className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                      <Input
                        {...field}
                        type="password"
                        placeholder="••••••••"
                        className="pl-10"
                        disabled={isLoading}
                      />
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />


            <Button 
              type="submit" 
              className="w-full" 
              disabled={isLoading}
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Iniciando sesión...
                </>
              ) : (
                "Iniciar Sesión"
              )}
            </Button>
          </form>
        </Form>

        <div className="space-y-4">
          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <span className="w-full border-t" />
            </div>
            <div className="relative flex justify-center text-xs uppercase">
              <span className="bg-background px-2 text-muted-foreground">
                O continúa con
              </span>
            </div>
          </div>

          <Button
            variant="outline"
            className="w-full"
            onClick={handleOAuthLogin}
            disabled={isLoading}
          >
            <svg className="mr-2 h-4 w-4" viewBox="0 0 24 24">
              <path
                fill="currentColor"
                d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
              />
              <path
                fill="currentColor"
                d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
              />
              <path
                fill="currentColor"
                d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
              />
              <path
                fill="currentColor"
                d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
              />
            </svg>
            Continuar con Google
          </Button>
        </div>

        <div className="text-center text-sm text-muted-foreground">
          ¿No tienes una cuenta?{" "}
          <Link
            href="/register"
            className="font-medium text-primary hover:underline"
          >
            Crear cuenta
          </Link>
        </div>

      </Card>
    </div>
  );
}