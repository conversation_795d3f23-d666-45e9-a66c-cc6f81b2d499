"use client";

import { useState } from "react";
import { useStackApp } from "@stackframe/stack";
import { useRouter } from "next/navigation";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { registerSchema, type RegisterInput } from "@/lib/auth/validation";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Card } from "@/components/ui/card";
import { toast } from "sonner";
import Link from "next/link";
import { Loader2, Mail, Lock, User } from "lucide-react";

export default function RegisterPage() {
  const stackApp = useStackApp();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);

  const form = useForm<RegisterInput>({
    resolver: zodResolver(registerSchema),
    defaultValues: {
      name: "",
      email: "",
      password: "",
      confirmPassword: "",
    },
  });

  async function onSubmit(data: RegisterInput) {
    try {
      setIsLoading(true);
      console.log('📝 [REGISTER] ===== INICIANDO PROCESSO DE REGISTRO =====');
      console.log('📝 [REGISTER] Dados:', { 
        email: data.email,
        name: data.name,
        hasPassword: !!data.password,
        passwordMatch: data.password === data.confirmPassword,
        timestamp: new Date().toISOString()
      });
      
      // Verificar se stackApp existe
      console.log('📝 [REGISTER] Verificando stackApp:', {
        exists: !!stackApp,
        type: typeof stackApp,
        methods: stackApp ? Object.keys(stackApp) : []
      });
      
      if (!stackApp) {
        throw new Error('Stack App não inicializado');
      }
      
      // Usar Stack Auth para registro
      console.log('🔐 [REGISTER] Chamando stackApp.signUpWithCredential...');
      console.log('🔐 [REGISTER] Parâmetros:', {
        email: data.email,
        passwordLength: data.password.length
      });
      console.log('🔐 [REGISTER] Métodos disponíveis no stackApp:', {
        signUpWithCredential: typeof stackApp.signUpWithCredential,
        signInWithCredential: typeof stackApp.signInWithCredential,
        signInWithOAuth: typeof stackApp.signInWithOAuth,
      });
      
      const result = await stackApp.signUpWithCredential({
        email: data.email,
        password: data.password,
      });
      
      console.log('🔐 [REGISTER] Resposta do Stack Auth:', {
        hasResult: !!result,
        status: result?.status,
        error: result?.error,
        keys: result ? Object.keys(result) : [],
        fullResult: result
      });

      if (result?.status === 'error') {
        console.error('❌ [REGISTER] Erro do Stack Auth:', result.error);
        throw new Error(result.error?.message || 'Registration failed');
      }
      
      // Registro bem sucedido se não houver erro
      console.log('✅ [REGISTER] Usuário criado com sucesso!');
      
      // Tentar fazer login automaticamente após o registro
      console.log('🔐 [REGISTER] Tentando fazer login automático...');
      try {
        const loginResult = await stackApp.signInWithCredential({
          email: data.email,
          password: data.password,
        });
        
        console.log('🔐 [REGISTER] Resultado do login automático:', {
          status: loginResult?.status,
          error: loginResult?.error
        });
        
        if (loginResult?.status === 'error') {
          console.warn('⚠️ [REGISTER] Login automático falhou:', loginResult.error);
          toast.warning("Cuenta creada. Por favor, inicia sesión.");
          router.push("/login");
          return;
        }
      } catch (loginError) {
        console.error('⚠️ [REGISTER] Erro no login automático:', loginError);
        toast.warning("Cuenta creada. Por favor, inicia sesión.");
        router.push("/login");
        return;
      }

      toast.success("¡Cuenta creada con éxito!");
      
      // Verificar cookies
      console.log('🍪 [REGISTER] Verificando cookies após registro...');
      const checkResponse = await fetch('/api/auth/check', {
        credentials: 'include'
      });
      const checkData = await checkResponse.json();
      console.log('🍪 [REGISTER] Status de autenticação:', checkData);
      
      // Aguardar um momento para garantir que tudo foi processado
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      console.log('🚀 [REGISTER] Redirecionando para /browse...');
      router.push("/browse");
    } catch (error: any) {
      console.error("❌ [REGISTER] ERRO DETALHADO:", {
        message: error.message,
        stack: error.stack,
        name: error.name,
        code: error.code,
        response: error.response,
        fullError: error
      });
      
      // Mensagens de erro específicas
      if (error.message?.includes("already") || error.message?.includes("exists")) {
        toast.error("Este email ya está registrado.");
      } else if (error.message?.includes("password")) {
        toast.error("La contraseña debe tener al menos 6 caracteres.");
      } else if (error.message?.includes("email")) {
        toast.error("Email inválido.");
      } else if (error.message?.includes("Stack App") || error.message?.includes("environment")) {
        toast.error("Error de configuración. Por favor, recarga la página.");
      } else {
        toast.error("Error al crear cuenta. Por favor, intenta de nuevo.");
      }
    } finally {
      setIsLoading(false);
      console.log('📝 [REGISTER] ===== FIM DO PROCESSO DE REGISTRO =====');
    }
  }

  const handleOAuthRegister = async (provider: 'google') => {
    try {
      setIsLoading(true);
      await stackApp.signInWithOAuth(provider);
    } catch (error) {
      console.error(`Erro ao registrar com ${provider}:`, error);
      toast.error(`Error al registrarse con ${provider}`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-background p-4">
      <Card className="w-full max-w-md p-8 space-y-6">
        <div className="text-center space-y-2">
          <h1 className="text-3xl font-bold">Crear Cuenta</h1>
          <p className="text-muted-foreground">
            Crea tu cuenta para comenzar
          </p>
        </div>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Nombre completo</FormLabel>
                  <FormControl>
                    <div className="relative">
                      <User className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                      <Input
                        {...field}
                        placeholder="Tu nombre"
                        className="pl-10"
                        disabled={isLoading}
                      />
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Correo electrónico</FormLabel>
                  <FormControl>
                    <div className="relative">
                      <Mail className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                      <Input
                        {...field}
                        type="email"
                        placeholder="<EMAIL>"
                        className="pl-10"
                        disabled={isLoading}
                      />
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="password"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Contraseña</FormLabel>
                  <FormControl>
                    <div className="relative">
                      <Lock className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                      <Input
                        {...field}
                        type="password"
                        placeholder="••••••••"
                        className="pl-10"
                        disabled={isLoading}
                      />
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="confirmPassword"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Confirmar contraseña</FormLabel>
                  <FormControl>
                    <div className="relative">
                      <Lock className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                      <Input
                        {...field}
                        type="password"
                        placeholder="••••••••"
                        className="pl-10"
                        disabled={isLoading}
                      />
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <Button 
              type="submit" 
              className="w-full" 
              disabled={isLoading}
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Creando cuenta...
                </>
              ) : (
                "Crear cuenta"
              )}
            </Button>
          </form>
        </Form>

        <div className="space-y-4">
          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <span className="w-full border-t" />
            </div>
            <div className="relative flex justify-center text-xs uppercase">
              <span className="bg-background px-2 text-muted-foreground">
                O continúa con
              </span>
            </div>
          </div>

          <Button
            variant="outline"
            className="w-full"
            onClick={() => handleOAuthRegister('google')}
            disabled={isLoading}
          >
            <svg className="mr-2 h-4 w-4" viewBox="0 0 24 24">
              <path
                fill="currentColor"
                d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
              />
              <path
                fill="currentColor"
                d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
              />
              <path
                fill="currentColor"
                d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
              />
              <path
                fill="currentColor"
                d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
              />
            </svg>
            Continuar con Google
          </Button>
        </div>

        <div className="text-center text-sm text-muted-foreground">
          ¿Ya tienes una cuenta?{" "}
          <Link
            href="/login"
            className="font-medium text-primary hover:underline"
          >
            Iniciar sesión
          </Link>
        </div>
      </Card>
    </div>
  );
}