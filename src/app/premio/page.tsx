'use client'

import { useEffect, useState } from 'react'
import { useSearchPara<PERSON>, useRouter } from 'next/navigation'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { <PERSON><PERSON>, PartyPopper, Loader2 } from 'lucide-react'
import { toast } from 'sonner'
import { useUser } from '@stackframe/stack'
import { createClient } from '@/lib/supabase/client'
import { useAccessStore } from '@/stores/access.store'

export default function PremioPage() {
  const searchParams = useSearchParams()
  const router = useRouter()
  const user = useUser()
  const [loading, setLoading] = useState(true)
  const [cupom, setCupom] = useState('')
  const [erro, setErro] = useState('')
  const [copiado, setCopiado] = useState(false)
  const { applyCoupon } = useAccessStore()

  useEffect(() => {
    verificarEGerar()
  }, [user])

  const verificarEGerar = async () => {
    try {
      // Verifica se veio do app
      const ref = searchParams.get('ref')
      if (ref !== 'app-install') {
        router.push('/')
        return
      }

      // Verifica se está logado
      if (!user) {
        // Salva URL para voltar após login
        if (typeof window !== 'undefined') {
          localStorage.setItem('redirect_after_login', window.location.href)
        }
        router.push('/login')
        return
      }

      const supabase = createClient()

      // Verifica se já tem cupom
      const { data: cupomExistente, error: errorCupom } = await supabase
        .from('cupons')
        .select('*')
        .eq('user_id', user.id)
        .single()

      if (errorCupom && errorCupom.code !== 'PGRST116') {
        throw errorCupom
      }

      if (cupomExistente) {
        if (cupomExistente.usado) {
          setErro('Ya has utilizado tu cupón de 1 mes gratis.')
        } else {
          setCupom(cupomExistente.codigo)
        }
      } else {
        // Gera novo cupom
        const novoCupom = await gerarCupom(user.id)
        setCupom(novoCupom)
      }

    } catch (error) {
      console.error('Erro:', error)
      setErro('Error al procesar tu solicitud.')
    } finally {
      setLoading(false)
    }
  }

  const gerarCupom = async (userId: string) => {
    // Gera código único
    const codigo = 'APP' + Math.random().toString(36).substr(2, 6).toUpperCase()
    
    const supabase = createClient()
    const { error } = await supabase
      .from('cupons')
      .insert({
        codigo,
        user_id: userId
      })

    if (error) throw error
    return codigo
  }

  const copiarCodigo = () => {
    navigator.clipboard.writeText(cupom)
    setCopiado(true)
    toast.success('¡Código copiado!')
    setTimeout(() => setCopiado(false), 2000)
  }

  const usarAgora = async () => {
    if (!user) return
    
    setLoading(true)
    try {
      const result = await applyCoupon(user.id, cupom)
      
      if (result.success) {
        toast.success(result.message)
        router.push('/browse')
      } else {
        toast.error(result.message)
      }
    } catch (error) {
      toast.error('Error al aplicar cupón')
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  if (erro) {
    return (
      <div className="container max-w-md mx-auto mt-10 p-4">
        <Alert variant="destructive">
          <AlertDescription>{erro}</AlertDescription>
        </Alert>
        <Button className="mt-4 w-full" onClick={() => router.push('/')}>
          Volver al Inicio
        </Button>
      </div>
    )
  }

  return (
    <div className="container max-w-lg mx-auto mt-10 p-4">
      <Card>
        <CardHeader className="text-center">
          <PartyPopper className="h-12 w-12 mx-auto mb-2 text-primary" />
          <CardTitle className="text-2xl">¡Felicitaciones! 1 Mes Gratis</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="bg-secondary p-4 rounded-lg text-center">
            <p className="text-sm mb-2">Tu código de acceso:</p>
            <div className="flex items-center justify-center gap-2">
              <span className="text-2xl font-mono font-bold">{cupom}</span>
              <Button size="icon" variant="ghost" onClick={copiarCodigo}>
                {copiado ? '✓' : <Copy className="h-4 w-4" />}
              </Button>
            </div>
          </div>

          <Alert>
            <AlertDescription>
              Este código libera 30 días de acceso ilimitado. 
              ¡Úsalo ahora o guárdalo para cuando quieras!
            </AlertDescription>
          </Alert>

          <div className="space-y-2">
            <Button 
              onClick={usarAgora} 
              className="w-full"
              disabled={loading}
            >
              {loading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Aplicando código...
                </>
              ) : (
                'Usar Código Ahora'
              )}
            </Button>
            <Button 
              variant="outline" 
              onClick={() => router.push('/')} 
              className="w-full"
            >
              Usar Después
            </Button>
          </div>

          <div className="text-center text-sm text-muted-foreground">
            <p>Tu código ha sido guardado y está vinculado a tu cuenta.</p>
            <p>Puedes usarlo en cualquier momento.</p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}