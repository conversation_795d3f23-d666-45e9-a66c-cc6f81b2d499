'use client'

import { Footer } from '@/components/footer'
import { BackToHome } from '@/components/back-to-home'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion'
import { 
  HelpCircle, CreditCard, Tv, Smartphone, 
  Settings, Shield, Globe, ChevronRight
} from 'lucide-react'
import Link from 'next/link'

export default function FAQPage() {
  const faqCategories = [
    {
      icon: CreditCard,
      title: 'Facturación y Suscripciones',
      color: 'text-green-400',
      questions: [
        {
          q: '¿Cuánto cuesta StreamPlus España?',
          a: 'Ofrecemos varios planes desde 9.99€/mes. El plan Básico incluye HD, el Premium 4K Ultra HD, y el Deportes+ acceso completo a todos los eventos deportivos en vivo. Todos los planes incluyen una prueba gratuita de 5 minutos.'
        },
        {
          q: '¿Cómo puedo cambiar mi plan de suscripción?',
          a: 'Puedes cambiar tu plan en cualquier momento desde tu cuenta. Ve a Configuración > Suscripción y selecciona el nuevo plan. Los cambios se aplicarán en tu próximo ciclo de facturación.'
        },
        {
          q: '¿Qué métodos de pago aceptan?',
          a: 'Aceptamos tarjetas de crédito/débito (Visa, Mastercard, American Express), PayPal, y transferencia bancaria SEPA. Todos los pagos son procesados de forma segura.'
        },
        {
          q: '¿Puedo cancelar en cualquier momento?',
          a: 'Sí, puedes cancelar tu suscripción en cualquier momento sin penalizaciones. Mantendrás acceso hasta el final de tu período de facturación actual.'
        },
        {
          q: '¿Ofrecen descuentos o promociones?',
          a: 'Sí, ofrecemos descuentos en planes anuales (ahorra 2 meses), descuentos para estudiantes (30% off), y promociones especiales durante eventos deportivos importantes.'
        }
      ]
    },
    {
      icon: Tv,
      title: 'Reproducción y Calidad',
      color: 'text-blue-400',
      questions: [
        {
          q: '¿Qué calidad de video ofrecen?',
          a: 'Ofrecemos streaming adaptativo desde SD hasta 4K Ultra HD con HDR10 y Dolby Vision en contenido compatible. La calidad se ajusta automáticamente según tu velocidad de internet.'
        },
        {
          q: '¿Por qué el video se ve pixelado o con buffer?',
          a: 'Esto suele deberse a una conexión lenta. Recomendamos: 5 Mbps para HD, 25 Mbps para 4K. Prueba reducir la calidad en configuración del reproductor o verifica tu conexión.'
        },
        {
          q: '¿Puedo descargar contenido para ver offline?',
          a: 'Sí, con los planes Premium y Deportes+ puedes descargar eventos para verlos sin conexión en dispositivos móviles. Las descargas expiran después de 7 días.'
        },
        {
          q: '¿Cómo activo los subtítulos o cambio el idioma?',
          a: 'Durante la reproducción, haz clic en el icono de configuración (engranaje) y selecciona "Subtítulos" o "Audio". Ofrecemos múltiples idiomas según el contenido.'
        },
        {
          q: '¿Qué navegadores son compatibles?',
          a: 'Chrome, Firefox, Safari, Edge (últimas versiones). Para 4K en ordenadores, necesitas Windows 10/11 o macOS 10.15+ con navegadores compatibles.'
        }
      ]
    },
    {
      icon: Smartphone,
      title: 'Dispositivos y Apps',
      color: 'text-purple-400',
      questions: [
        {
          q: '¿En qué dispositivos puedo ver StreamPlus?',
          a: 'Smart TVs (Samsung, LG, Sony, etc.), móviles iOS/Android, tablets, ordenadores, Chromecast, Apple TV, Fire TV, Roku, PlayStation, Xbox, y más.'
        },
        {
          q: '¿Cuántos dispositivos puedo usar simultáneamente?',
          a: 'Plan Básico: 2 dispositivos, Plan Premium: 4 dispositivos, Plan Deportes+: 4 dispositivos. Puedes registrar hasta 10 dispositivos en total.'
        },
        {
          q: '¿Cómo descargo la app en mi Smart TV?',
          a: 'Busca "StreamPlus España" en la tienda de apps de tu TV (Samsung Smart Hub, LG Content Store, etc.). La app es gratuita y requiere una suscripción activa.'
        },
        {
          q: '¿Puedo usar StreamPlus en el extranjero?',
          a: 'Sí, puedes acceder a tu cuenta desde cualquier país. Algunos contenidos pueden variar según restricciones de derechos, pero los deportes principales están disponibles globalmente.'
        },
        {
          q: '¿Cómo configuro Chromecast o AirPlay?',
          a: 'Asegúrate de que tu dispositivo y Chromecast/Apple TV estén en la misma red WiFi. Busca el icono de cast en el reproductor y selecciona tu dispositivo.'
        }
      ]
    },
    {
      icon: Settings,
      title: 'Cuenta y Configuración',
      color: 'text-orange-400',
      questions: [
        {
          q: '¿Cómo creo perfiles adicionales?',
          a: 'Ve a Configuración > Perfiles y haz clic en "Añadir perfil". Puedes crear hasta 5 perfiles con preferencias personalizadas y control parental.'
        },
        {
          q: '¿Puedo compartir mi cuenta con familiares?',
          a: 'Sí, puedes compartir tu cuenta con miembros de tu hogar. Cada uno puede tener su propio perfil con recomendaciones personalizadas.'
        },
        {
          q: '¿Cómo recupero mi contraseña?',
          a: 'En la página de inicio de sesión, haz clic en "¿Olvidaste tu contraseña?" e introduce tu email. Recibirás un enlace para restablecerla.'
        },
        {
          q: '¿Cómo configuro el control parental?',
          a: 'En cada perfil puedes establecer restricciones de edad. Ve a Configuración > Control Parental y selecciona el nivel de restricción deseado.'
        },
        {
          q: '¿Puedo cambiar mi dirección de email?',
          a: 'Sí, ve a Configuración > Cuenta > Email. Necesitarás verificar el nuevo email antes de que el cambio sea efectivo.'
        }
      ]
    },
    {
      icon: Shield,
      title: 'Seguridad y Privacidad',
      color: 'text-red-400',
      questions: [
        {
          q: '¿Cómo protegen mis datos personales?',
          a: 'Utilizamos encriptación SSL/TLS, cumplimos con RGPD, y nunca compartimos tus datos con terceros sin tu consentimiento. Consulta nuestra política de privacidad completa.'
        },
        {
          q: '¿Qué hago si creo que alguien usa mi cuenta?',
          a: 'Ve a Configuración > Seguridad > "Cerrar sesión en todos los dispositivos" y cambia tu contraseña inmediatamente. Activa la verificación en dos pasos para mayor seguridad.'
        },
        {
          q: '¿Guardan mi información de pago?',
          a: 'Tu información de pago es procesada y almacenada de forma segura por proveedores certificados PCI DSS. Solo guardamos los últimos 4 dígitos para tu referencia.'
        },
        {
          q: '¿Puedo eliminar mi cuenta y datos?',
          a: 'Sí, puedes solicitar la eliminación completa de tu cuenta y datos en Configuración > Privacidad > Eliminar cuenta. Procesamos las solicitudes en 30 días según RGPD.'
        },
        {
          q: '¿Usan cookies? ¿Puedo desactivarlas?',
          a: 'Usamos cookies esenciales para el funcionamiento y opcionales para mejorar tu experiencia. Puedes gestionar tus preferencias en Configuración > Privacidad > Cookies.'
        }
      ]
    },
    {
      icon: Globe,
      title: 'Contenido y Programación',
      color: 'text-teal-400',
      questions: [
        {
          q: '¿Qué deportes incluye StreamPlus?',
          a: 'Fútbol (LaLiga, Champions, Premier League), F1, MotoGP, NBA, NFL, tenis, golf, deportes de invierno, eSports (LoL, CS2, Valorant), y más de 50 competiciones.'
        },
        {
          q: '¿Puedo ver partidos pasados?',
          a: 'Sí, todos los eventos están disponibles en repetición hasta 7 días después. Los partidos importantes se guardan en nuestra biblioteca permanente.'
        },
        {
          q: '¿Tienen narración en español?',
          a: 'Sí, ofrecemos narración en español para todos los eventos principales. También disponible en inglés, catalán y euskera según el evento.'
        },
        {
          q: '¿Cómo sé qué eventos vienen?',
          a: 'Consulta nuestra guía EPG (Guía Electrónica de Programación) en la sección Deportes. También puedes activar notificaciones para tus equipos favoritos.'
        },
        {
          q: '¿Qué pasa si se cancela un evento?',
          a: 'Te notificaremos inmediatamente si un evento se cancela o pospone. Los eventos pospuestos se actualizan automáticamente en tu calendario.'
        }
      ]
    }
  ]

  return (
    <div className="min-h-screen bg-gradient-to-b from-slate-950 to-slate-900">
      {/* Hero Section */}
      <section className="py-16 border-b border-slate-800">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <Badge className="mb-4 bg-indigo-600 text-white">Soporte</Badge>
          <BackToHome />
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-6">
            Preguntas Frecuentes
          </h1>
          <p className="text-xl text-gray-300">
            Encuentra respuestas rápidas a las preguntas más comunes sobre StreamPlus España
          </p>
        </div>
      </section>

      {/* Quick Links */}
      <section className="py-8 border-b border-slate-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-wrap gap-2 justify-center">
          <BackToHome />
            {faqCategories.map((category, index) => (
              <Button key={index} variant="outline" size="sm" className="text-gray-300">
                <category.icon className={`mr-2 h-4 w-4 ${category.color}`} />
                {category.title}
              </Button>
            ))}
          </div>
        </div>
      </section>

      {/* FAQ Content */}
      <section className="py-12">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="space-y-12">
          <BackToHome />
            {faqCategories.map((category, categoryIndex) => (
              <div key={categoryIndex}>
                <div className="flex items-center gap-3 mb-6">
                  <category.icon className={`h-8 w-8 ${category.color}`} />
                  <h2 className="text-2xl font-bold text-white">{category.title}</h2>
                </div>
                
                <Accordion type="single" collapsible className="space-y-4">
                  {category.questions.map((item, index) => (
                    <AccordionItem 
                      key={`${categoryIndex}-${index}`} 
                      value={`${categoryIndex}-${index}`}
                      className="bg-slate-800/50 border-slate-700 rounded-lg px-6"
                    >
                      <AccordionTrigger className="text-white hover:text-gray-200 text-left">
                        <div className="flex items-start gap-3">
                          <HelpCircle className="h-5 w-5 text-gray-400 mt-0.5 flex-shrink-0" />
                          <span>{item.q}</span>
                        </div>
                      </AccordionTrigger>
                      <AccordionContent className="text-gray-300 ml-8">
                        {item.a}
                      </AccordionContent>
                    </AccordionItem>
                  ))}
                </Accordion>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Still Need Help */}
      <section className="py-16 border-t border-slate-800">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <Card className="bg-gradient-to-br from-indigo-900/20 to-purple-900/20 border-indigo-500/20">
          <BackToHome />
            <CardHeader className="text-center">
              <CardTitle className="text-2xl text-white mb-4">
                ¿No encuentras lo que buscas?
              </CardTitle>
              <p className="text-gray-300">
                Nuestro equipo de soporte está disponible 24/7 para ayudarte con cualquier pregunta
              </p>
            </CardHeader>
            <CardContent className="text-center">
              <div className="flex gap-4 justify-center">
                <Button asChild>
                  <Link href="/help">
                    Ir al Centro de Ayuda
                    <ChevronRight className="ml-2 h-4 w-4" />
                  </Link>
                </Button>
                <Button variant="outline">
                  Contactar Soporte
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </section>

      <Footer />
    </div>
  )
}