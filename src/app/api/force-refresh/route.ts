import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  const timestamp = new Date().toISOString()
  console.log(`[FORCE-REFRESH] ${timestamp} - ===== FORÇANDO REFRESH =====`)
  
  // Este endpoint apenas retorna sucesso
  // O cliente deve limpar o localStorage e recarregar os stores
  
  return NextResponse.json({
    success: true,
    message: 'Por favor, limpe o localStorage e recarregue a página',
    instructions: [
      '1. Abra o console do navegador (F12)',
      '2. Execute: localStorage.clear()',
      '3. Recarregue a página (F5 ou Ctrl+R)',
      '4. Faça login novamente se necessário'
    ],
    timestamp
  })
}