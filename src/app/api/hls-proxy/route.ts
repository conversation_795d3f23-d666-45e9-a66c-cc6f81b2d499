import { NextRequest, NextResponse } from 'next/server'
import { logger } from '@/lib/logger'
import { LOG_TAGS } from '@/config/debug'

function rewriteM3U8Content(content: string, baseUrl: string, proxyBaseUrl: string): string {
  const lines = content.split('\n')
  const rewritten = lines.map(line => {
    // Skip comments and empty lines
    if (line.startsWith('#') || line.trim() === '') {
      return line
    }
    
    // This is a URL line
    if (line.includes('.m3u8') || line.includes('.ts')) {
      let absoluteUrl: string
      
      if (line.startsWith('http://') || line.startsWith('https://')) {
        // Already absolute
        absoluteUrl = line
      } else if (line.startsWith('/')) {
        // Absolute path
        const urlObj = new URL(baseUrl)
        absoluteUrl = `${urlObj.protocol}//${urlObj.host}${line}`
      } else {
        // Relative path
        const baseUrlWithoutFile = baseUrl.substring(0, baseUrl.lastIndexOf('/'))
        absoluteUrl = `${baseUrlWithoutFile}/${line}`
      }
      
      // Rewrite to use proxy
      return `${proxyBaseUrl}?url=${encodeURIComponent(absoluteUrl.trim())}`
    }
    
    return line
  })
  
  return rewritten.join('\n')
}

export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams
  const url = searchParams.get('url')
  
  if (!url) {
    return NextResponse.json({ error: 'URL parameter is required' }, { status: 400 })
  }
  
  try {
    logger.info(LOG_TAGS.HTTP, 'HLS Proxy request', { url })
    
    // Parse the URL to get the host
    const parsedUrl = new URL(url)
    const originUrl = `${parsedUrl.protocol}//${parsedUrl.host}`
    
    // Build headers preserving important ones from the client
    const headers: Record<string, string> = {
      'User-Agent': request.headers.get('user-agent') || 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
      'Accept': '*/*',
      'Accept-Language': 'en-US,en;q=0.9,es;q=0.8',
      'Accept-Encoding': 'gzip, deflate, br',
      'Connection': 'keep-alive',
      'Origin': originUrl,
      'Referer': originUrl + '/',
    }
    
    // Add any custom headers from the request
    const customHeaders = searchParams.get('headers')
    if (customHeaders) {
      try {
        const parsed = JSON.parse(customHeaders)
        Object.assign(headers, parsed)
      } catch (e) {
        logger.warn(LOG_TAGS.HTTP, 'Failed to parse custom headers', { error: e })
      }
    }
    
    // Fazer a requisição para a URL original
    const response = await fetch(url, {
      headers,
      redirect: 'follow',
      signal: AbortSignal.timeout(30000), // 30 second timeout
    })
    
    if (!response.ok) {
      logger.error(LOG_TAGS.HTTP, 'HLS Proxy failed', {
        url,
        status: response.status,
        statusText: response.statusText
      })
      return NextResponse.json(
        { error: `Proxy failed: ${response.statusText}` },
        { status: response.status }
      )
    }
    
    const contentType = response.headers.get('content-type') || 'application/octet-stream'
    
    // Se for um arquivo M3U8, reescrever URLs
    if (url.includes('.m3u8') || contentType.includes('mpegurl') || contentType.includes('m3u8')) {
      const text = await response.text()
      const proxyBaseUrl = `${request.nextUrl.protocol}//${request.nextUrl.host}/api/hls-proxy`
      const rewrittenContent = rewriteM3U8Content(text, url, proxyBaseUrl)
      
      logger.info(LOG_TAGS.HTTP, 'M3U8 content rewritten', {
        url,
        originalLines: text.split('\n').length,
        rewrittenLines: rewrittenContent.split('\n').length
      })
      
      return new NextResponse(rewrittenContent, {
        status: 200,
        headers: {
          'Content-Type': 'application/vnd.apple.mpegurl',
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type',
          'Cache-Control': 'no-cache',
        }
      })
    }
    
    // Para outros arquivos (como .ts), apenas proxy direto
    const data = await response.arrayBuffer()
    
    logger.info(LOG_TAGS.HTTP, 'HLS Proxy success (binary)', {
      url,
      contentType,
      size: data.byteLength
    })
    
    return new NextResponse(data, {
      status: 200,
      headers: {
        'Content-Type': contentType,
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type',
        'Cache-Control': 'no-cache',
      }
    })
  } catch (error) {
    logger.error(LOG_TAGS.HTTP, 'HLS Proxy error', {
      url,
      error: error instanceof Error ? error.message : 'Unknown error'
    })
    
    return NextResponse.json(
      { error: 'Failed to proxy request', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
}

export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    }
  })
}