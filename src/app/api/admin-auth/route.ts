import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'

const ADMIN_EMAIL = '<EMAIL>'
const ADMIN_PASSWORD = '62845_Madhouse'

export async function POST(request: NextRequest) {
  try {
    const { email, password } = await request.json()

    // Verify credentials
    if (email === ADMIN_EMAIL && password === ADMIN_PASSWORD) {
      // Set admin cookie
      const cookieStore = await cookies()
      cookieStore.set('admin-auth', 'true', {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax',
        maxAge: 60 * 60 * 24 * 7, // 7 days
        path: '/'
      })

      return NextResponse.json({ success: true })
    }

    return NextResponse.json(
      { error: 'Invalid credentials' },
      { status: 401 }
    )
  } catch (error) {
    console.error('Admin auth error:', error)
    return NextResponse.json(
      { error: 'Authentication failed' },
      { status: 500 }
    )
  }
}

export async function GET() {
  try {
    const cookieStore = await cookies()
    const adminAuth = cookieStore.get('admin-auth')
    
    return NextResponse.json({
      isAuthenticated: adminAuth?.value === 'true'
    })
  } catch (error) {
    console.error('Admin auth check error:', error)
    return NextResponse.json({
      isAuthenticated: false
    })
  }
}

export async function DELETE() {
  // Logout - remove cookie
  const cookieStore = await cookies()
  cookieStore.delete('admin-auth')
  
  return NextResponse.json({ success: true })
}