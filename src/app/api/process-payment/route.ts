import { NextRequest, NextResponse } from 'next/server'
import { createServiceClient } from '@/lib/supabase/server'
import { stripe } from '@/lib/stripe'

export async function POST(request: NextRequest) {
  try {
    const { customerId, subscriptionId, userEmail } = await request.json()
    
    if (!customerId || !subscriptionId || !userEmail) {
      return NextResponse.json({
        error: 'Missing required fields: customerId, subscriptionId, userEmail'
      }, { status: 400 })
    }
    
    console.log('🔄 Processing manual payment:', { customerId, subscriptionId, userEmail })
    
    const supabase = createServiceClient()
    
    // Buscar usuário pelo email
    const { data: authUser, error: authError } = await supabase.auth.admin.getUserByEmail(userEmail)
    
    if (authError || !authUser) {
      console.error('❌ User not found:', userEmail)
      return NextResponse.json({
        error: `User not found: ${userEmail}`,
        details: authError
      }, { status: 404 })
    }
    
    console.log('✅ User found:', authUser.user.id)
    
    // Buscar assinatura no Stripe
    let subscription
    try {
      subscription = await stripe.subscriptions.retrieve(subscriptionId)
      console.log('✅ Stripe subscription found:', subscription.id)
    } catch (stripeError) {
      console.error('❌ Stripe subscription not found:', stripeError)
      // Criar dados mock para teste
      subscription = {
        id: subscriptionId,
        status: 'active',
        current_period_end: Math.floor(Date.now() / 1000) + (30 * 24 * 60 * 60) // 30 dias
      }
      console.log('⚠️  Using mock subscription data for testing')
    }
    
    // Atualizar perfil
    const updateData = {
      stripe_customer_id: customerId,
      stripe_subscription_id: subscriptionId,
      subscription_status: subscription.status,
      subscription_current_period_end: new Date(subscription.current_period_end * 1000).toISOString(),
      subscription_tier: 'premium',
      updated_at: new Date().toISOString(),
    }
    
    console.log('📝 Updating profile with:', updateData)
    
    const { data: profile, error: updateError } = await supabase
      .from('profiles')
      .update(updateData)
      .eq('id', authUser.user.id)
      .select()
      .single()
    
    if (updateError) {
      console.error('❌ Error updating profile:', updateError)
      return NextResponse.json({
        error: 'Failed to update profile',
        details: updateError,
        updateData
      }, { status: 500 })
    }
    
    console.log('✅ Profile updated successfully')
    
    return NextResponse.json({
      success: true,
      message: 'Payment processed successfully',
      profile,
      subscription: {
        id: subscription.id,
        status: subscription.status,
        current_period_end: new Date(subscription.current_period_end * 1000).toISOString()
      }
    })
    
  } catch (error) {
    console.error('❌ Process payment error:', error)
    return NextResponse.json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

// GET endpoint para verificar perfil
export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const email = searchParams.get('email')
  
  if (!email) {
    return NextResponse.json({
      error: 'Email parameter is required'
    }, { status: 400 })
  }
  
  try {
    const supabase = createServiceClient()
    
    // Buscar usuário
    const { data: authUser, error: authError } = await supabase.auth.admin.getUserByEmail(email)
    
    if (authError || !authUser) {
      return NextResponse.json({
        error: `User not found: ${email}`
      }, { status: 404 })
    }
    
    // Buscar perfil
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', authUser.user.id)
      .single()
    
    if (profileError) {
      return NextResponse.json({
        error: 'Profile not found',
        details: profileError
      }, { status: 404 })
    }
    
    return NextResponse.json({
      user: {
        id: authUser.user.id,
        email: authUser.user.email
      },
      profile
    })
    
  } catch (error) {
    console.error('❌ Get profile error:', error)
    return NextResponse.json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}