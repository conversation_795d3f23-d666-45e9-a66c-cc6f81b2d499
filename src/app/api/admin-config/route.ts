import { NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

export async function GET() {
  try {
    const supabase = await createClient()
    
    const { data, error } = await supabase
      .from('admin_config')
      .select('config_key, config_value')
      .in('config_key', ['apk_url', 'qrcode_url'])

    if (error) throw error

    const config: Record<string, string> = {}
    data?.forEach(item => {
      config[item.config_key] = item.config_value || ''
    })

    return NextResponse.json(config)
  } catch (error) {
    console.error('Error fetching config:', error)
    return NextResponse.json(
      { error: 'Failed to fetch config' },
      { status: 500 }
    )
  }
}