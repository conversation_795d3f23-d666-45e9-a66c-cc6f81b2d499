import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

export async function POST(request: NextRequest) {
  try {
    // Verificar header de segurança
    const secretKey = request.headers.get('x-secret-key')
    if (secretKey !== 'activate-premium-2025') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
    
    const { email, userId } = await request.json()
    
    if (!email && !userId) {
      return NextResponse.json({ error: 'Email or userId is required' }, { status: 400 })
    }
    
    // Criar cliente admin do Supabase
    const supabaseAdmin = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )
    
    console.log('🔍 Ativando premium para:', email || userId)
    
    // Criar ou atualizar perfil com premium
    const subscriptionData = {
      email: email || undefined,
      subscription_tier: 'premium',
      subscription_status: 'active',
      subscription_current_period_end: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
      stripe_customer_id: `cus_manual_${Date.now()}`,
      stripe_subscription_id: `sub_manual_${Date.now()}`,
      updated_at: new Date().toISOString()
    }
    
    // Se temos o userId, usar diretamente
    if (userId) {
      const { data: profile, error } = await supabaseAdmin
        .from('profiles')
        .upsert({
          id: userId,
          ...subscriptionData,
          created_at: new Date().toISOString()
        })
        .select()
        .single()
      
      if (error) {
        console.error('❌ Erro ao atualizar perfil:', error)
        return NextResponse.json({
          error: 'Erro ao atualizar perfil',
          details: error.message
        }, { status: 500 })
      }
      
      return NextResponse.json({
        success: true,
        message: 'Premium ativado com sucesso!',
        profile,
        subscription: {
          active: true,
          tier: 'premium',
          status: 'active',
          expiresAt: subscriptionData.subscription_current_period_end
        }
      })
    }
    
    // Se temos apenas o email, tentar encontrar e atualizar
    const { data: profiles, error: searchError } = await supabaseAdmin
      .from('profiles')
      .select('id')
      .eq('email', email)
      .single()
    
    if (searchError || !profiles) {
      console.log('❌ Perfil não encontrado para o email:', email)
      return NextResponse.json({
        error: 'Perfil não encontrado',
        email,
        hint: 'Use o userId diretamente se souber'
      }, { status: 404 })
    }
    
    const { data: updatedProfile, error: updateError } = await supabaseAdmin
      .from('profiles')
      .update(subscriptionData)
      .eq('id', profiles.id)
      .select()
      .single()
    
    if (updateError) {
      console.error('❌ Erro ao atualizar perfil:', updateError)
      return NextResponse.json({
        error: 'Erro ao atualizar perfil',
        details: updateError.message
      }, { status: 500 })
    }
    
    return NextResponse.json({
      success: true,
      message: 'Premium ativado com sucesso!',
      profile: updatedProfile,
      subscription: {
        active: true,
        tier: 'premium',
        status: 'active',
        expiresAt: subscriptionData.subscription_current_period_end
      }
    })
    
  } catch (error) {
    console.error('❌ Erro geral:', error)
    return NextResponse.json({
      error: 'Erro interno',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}