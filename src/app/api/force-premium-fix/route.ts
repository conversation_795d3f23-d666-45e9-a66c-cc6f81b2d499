import { NextRequest, NextResponse } from 'next/server'
import { createServiceClient } from '@/lib/supabase/server'

export async function POST(request: NextRequest) {
  const timestamp = new Date().toISOString()
  console.log(`[FORCE-PREMIUM-FIX] ${timestamp} - Starting...`)
  
  try {
    const { userId } = await request.json()
    
    if (!userId) {
      return NextResponse.json({ 
        success: false, 
        error: 'userId is required' 
      }, { status: 400 })
    }
    
    const supabase = createServiceClient()
    
    // 1. Buscar perfil Stack Auth
    const { data: stackProfile, error: stackError } = await supabase
      .from('stack_profiles')
      .select('*')
      .eq('id', userId)
      .single()
    
    if (stackError || !stackProfile) {
      console.log(`[FORCE-PREMIUM-FIX] ${timestamp} - Stack profile not found, creating...`)
      
      // Criar perfil se não existir
      const { data: newProfile, error: createError } = await supabase
        .from('stack_profiles')
        .insert({
          id: userId,
          email: '<EMAIL>',
          subscription_tier: 'premium',
          subscription_status: 'active',
          subscription_current_period_end: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
          subscription_start_date: new Date().toISOString()
        })
        .select()
        .single()
      
      if (createError) {
        console.error(`[FORCE-PREMIUM-FIX] ${timestamp} - Error creating profile:`, createError)
        return NextResponse.json({ 
          success: false, 
          error: 'Failed to create profile',
          details: createError.message 
        }, { status: 500 })
      }
      
      return NextResponse.json({
        success: true,
        action: 'created',
        profile: newProfile,
        message: 'Profile criado com premium ativo por 30 dias'
      })
    }
    
    // 2. Atualizar perfil existente
    console.log(`[FORCE-PREMIUM-FIX] ${timestamp} - Updating existing profile...`)
    
    const futureDate = new Date()
    futureDate.setDate(futureDate.getDate() + 30)
    
    const { data: updatedProfile, error: updateError } = await supabase
      .from('stack_profiles')
      .update({
        subscription_tier: 'premium',
        subscription_status: 'active',
        subscription_current_period_end: futureDate.toISOString(),
        subscription_start_date: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq('id', userId)
      .select()
      .single()
    
    if (updateError) {
      console.error(`[FORCE-PREMIUM-FIX] ${timestamp} - Error updating profile:`, updateError)
      return NextResponse.json({ 
        success: false, 
        error: 'Failed to update profile',
        details: updateError.message 
      }, { status: 500 })
    }
    
    // 3. Verificar se funcionou
    const { data: checkResult, error: checkError } = await supabase
      .rpc('check_user_access_unified', { p_user_id: userId })
      .single()
    
    console.log(`[FORCE-PREMIUM-FIX] ${timestamp} - Check result:`, checkResult)
    
    // 4. Limpar cache se existir
    try {
      // Forçar limpeza de cache no cliente
      await fetch(`${process.env.NEXT_PUBLIC_APP_URL}/api/clear-cache`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ userId })
      }).catch(() => {})
    } catch (e) {
      // Ignorar erro de cache
    }
    
    return NextResponse.json({
      success: true,
      action: 'updated',
      profile: updatedProfile,
      accessCheck: checkResult,
      message: 'Premium ativado com sucesso por 30 dias',
      details: {
        has_access: checkResult?.has_access,
        access_type: checkResult?.access_type,
        expires_at: checkResult?.expires_at,
        days_remaining: futureDate ? Math.floor((futureDate.getTime() - Date.now()) / (1000 * 60 * 60 * 24)) : 0
      }
    })
    
  } catch (error) {
    console.error(`[FORCE-PREMIUM-FIX] ${timestamp} - Unexpected error:`, error)
    return NextResponse.json({
      success: false,
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}