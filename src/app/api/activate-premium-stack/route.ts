import { NextRequest, NextResponse } from 'next/server'
import { createServiceClient } from '@/lib/supabase/server'
import { stackServerApp } from '@/lib/auth/stack-server'

export async function POST(request: NextRequest) {
  const timestamp = new Date().toISOString()
  console.log(`[ACTIVATE-PREMIUM-STACK] ${timestamp} - ===== REQUEST RECEIVED =====`)
  
  try {
    const body = await request.json()
    const { userId, email, tier = 'premium', months = 1 } = body
    
    console.log(`[ACTIVATE-PREMIUM-STACK] ${timestamp} - Parameters:`)
    console.log(`[ACTIVATE-PREMIUM-STACK] ${timestamp} -   UserId:`, userId)
    console.log(`[ACTIVATE-PREMIUM-STACK] ${timestamp} -   Email:`, email)
    console.log(`[ACTIVATE-PREMIUM-STACK] ${timestamp} -   Tier:`, tier)
    console.log(`[ACTIVATE-PREMIUM-STACK] ${timestamp} -   Months:`, months)
    
    if (!userId && !email) {
      return NextResponse.json({ 
        success: false, 
        error: 'userId ou email é obrigatório' 
      }, { status: 400 })
    }
    
    let stackUserId = userId
    let userEmail = email
    
    // Se não temos userId mas temos email, buscar no Stack Auth
    if (!userId && email) {
      console.log(`[ACTIVATE-PREMIUM-STACK] ${timestamp} - Buscando usuário no Stack Auth por email...`)
      
      try {
        // Listar todos os usuários do Stack e encontrar por email
        const users = await stackServerApp.listUsers()
        const stackUser = users.find(u => u.primaryEmail?.toLowerCase() === email.toLowerCase())
        
        if (stackUser) {
          stackUserId = stackUser.id
          userEmail = stackUser.primaryEmail
          console.log(`[ACTIVATE-PREMIUM-STACK] ${timestamp} - ✅ Usuário Stack Auth encontrado:`, stackUserId)
        } else {
          console.log(`[ACTIVATE-PREMIUM-STACK] ${timestamp} - ❌ Usuário não encontrado no Stack Auth`)
          return NextResponse.json({ 
            success: false, 
            error: 'Usuário não encontrado no Stack Auth' 
          }, { status: 404 })
        }
      } catch (stackError) {
        console.error(`[ACTIVATE-PREMIUM-STACK] ${timestamp} - ❌ Erro ao buscar no Stack Auth:`, stackError)
        return NextResponse.json({ 
          success: false, 
          error: 'Erro ao buscar usuário no Stack Auth' 
        }, { status: 500 })
      }
    }
    
    // Se temos userId mas não email, buscar no Stack Auth
    if (userId && !email) {
      console.log(`[ACTIVATE-PREMIUM-STACK] ${timestamp} - Buscando email no Stack Auth...`)
      try {
        const stackUser = await stackServerApp.getUser(userId)
        if (stackUser) {
          userEmail = stackUser.primaryEmail
          console.log(`[ACTIVATE-PREMIUM-STACK] ${timestamp} - ✅ Email encontrado:`, userEmail)
        }
      } catch (stackError) {
        console.error(`[ACTIVATE-PREMIUM-STACK] ${timestamp} - ⚠️ Erro ao buscar email:`, stackError)
      }
    }
    
    const supabase = createServiceClient()
    
    // Verificar se já tem perfil na tabela stack_profiles
    console.log(`[ACTIVATE-PREMIUM-STACK] ${timestamp} - Verificando perfil existente em stack_profiles...`)
    const { data: existingProfile, error: checkError } = await supabase
      .from('stack_profiles')
      .select('*')
      .eq('id', stackUserId)
      .single()
    
    if (checkError && checkError.code !== 'PGRST116') {
      console.error(`[ACTIVATE-PREMIUM-STACK] ${timestamp} - ❌ Erro ao verificar perfil:`, checkError)
    }
    
    const endDate = new Date()
    endDate.setMonth(endDate.getMonth() + months)
    
    const profileData = {
      id: stackUserId,
      email: userEmail,
      full_name: userEmail?.split('@')[0] || 'Premium User',
      subscription_tier: tier,
      subscription_status: 'active',
      subscription_current_period_end: endDate.toISOString(),
      subscription_start_date: new Date().toISOString(),
      stripe_customer_id: `stack_manual_${stackUserId}`,
      stripe_subscription_id: `stack_sub_${Date.now()}`,
      updated_at: new Date().toISOString()
    }
    
    let result
    
    if (existingProfile) {
      console.log(`[ACTIVATE-PREMIUM-STACK] ${timestamp} - Atualizando perfil existente...`)
      // Atualizar perfil existente
      const { data, error } = await supabase
        .from('stack_profiles')
        .update({
          email: profileData.email,
          subscription_tier: profileData.subscription_tier,
          subscription_status: profileData.subscription_status,
          subscription_current_period_end: profileData.subscription_current_period_end,
          subscription_start_date: profileData.subscription_start_date,
          stripe_customer_id: profileData.stripe_customer_id,
          stripe_subscription_id: profileData.stripe_subscription_id,
          updated_at: profileData.updated_at
        })
        .eq('id', stackUserId)
        .select()
        .single()
      
      if (error) {
        console.error(`[ACTIVATE-PREMIUM-STACK] ${timestamp} - ❌ Erro ao atualizar perfil:`, error)
        return NextResponse.json({ 
          success: false, 
          error: 'Erro ao atualizar perfil',
          details: error.message 
        }, { status: 500 })
      }
      
      result = data
    } else {
      console.log(`[ACTIVATE-PREMIUM-STACK] ${timestamp} - Criando novo perfil em stack_profiles...`)
      console.log(`[ACTIVATE-PREMIUM-STACK] ${timestamp} - Dados do perfil:`, JSON.stringify(profileData, null, 2))
      
      // Criar novo perfil
      const { data, error } = await supabase
        .from('stack_profiles')
        .insert([profileData])
        .select()
        .single()
      
      if (error) {
        console.error(`[ACTIVATE-PREMIUM-STACK] ${timestamp} - ❌ Erro ao criar perfil:`, error)
        return NextResponse.json({ 
          success: false, 
          error: 'Erro ao criar perfil',
          details: error.message 
        }, { status: 500 })
      }
      
      result = data
    }
    
    console.log(`[ACTIVATE-PREMIUM-STACK] ${timestamp} - ✅ Premium ativado com sucesso`)
    console.log(`[ACTIVATE-PREMIUM-STACK] ${timestamp} -   User ID:`, stackUserId)
    console.log(`[ACTIVATE-PREMIUM-STACK] ${timestamp} -   Email:`, userEmail)
    console.log(`[ACTIVATE-PREMIUM-STACK] ${timestamp} -   Tier:`, tier)
    console.log(`[ACTIVATE-PREMIUM-STACK] ${timestamp} -   Expira em:`, endDate.toISOString())
    
    // Registrar no histórico de pagamentos
    await supabase
      .from('payment_history')
      .insert({
        user_id: stackUserId,
        customer_id: profileData.stripe_customer_id,
        subscription_id: profileData.stripe_subscription_id,
        amount: 0, // Manual activation
        currency: 'brl',
        status: 'succeeded',
        payment_method: 'manual_activation_stack',
        description: `Ativação manual Stack Auth - ${tier} - ${months} mês(es)`,
        metadata: {
          tier,
          months,
          activated_by: 'manual_stack',
          activated_at: new Date().toISOString(),
          stack_user: true
        }
      })
    
    return NextResponse.json({ 
      success: true, 
      message: `Premium ${tier} ativado com sucesso para ${userEmail || stackUserId}`,
      profile: result,
      expiresAt: endDate.toISOString(),
      userId: stackUserId
    })
    
  } catch (error) {
    console.error(`[ACTIVATE-PREMIUM-STACK] ${timestamp} - ❌ Erro geral:`, error)
    return NextResponse.json({ 
      success: false, 
      error: 'Erro interno do servidor',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}