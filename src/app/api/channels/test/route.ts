import { NextResponse } from 'next/server'

// Canal de teste para debug
const TEST_CHANNEL = {
  id: '999',
  iptvId: 'test-channel',
  slug: 'test-channel',
  name: 'Canal de Teste',
  category: 'deportes',
  logo: 'https://via.placeholder.com/150',
  source: {
    web: 'https://test-streams.com/mystream.m3u8',
    app: 'https://test-streams.com/mystream.m3u8'
  },
  fallbackSources: [],
  epgId: null,
  isActive: true,
  isPremium: false
}

export async function GET() {
  return NextResponse.json(TEST_CHANNEL)
}