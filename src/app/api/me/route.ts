import { NextResponse } from 'next/server'
import { stackServerApp } from '@/lib/auth/stack-server'
import { cookies } from 'next/headers'

export async function GET() {
  try {
    // Buscar token nos cookies
    const cookieStore = await cookies()
    const authCookies = cookieStore.getAll().filter(c => 
      c.name.includes('stack-auth') || 
      c.name.includes('__session')
    )
    
    console.log('[API/ME] Cookies encontrados:', authCookies.map(c => c.name))
    
    // Tentar obter usuário atual via Stack
    let user = null
    
    // Buscar todos os usuários e encontrar pelo email se tivermos
    const users = await stackServerApp.listUsers()
    
    // Se temos cookies de sessão, tentar encontrar o usuário
    if (authCookies.length > 0) {
      // Por enquanto, vamos retornar o primeiro usuário (você)
      // Em produção, isso seria resolvido via token de sessão
      user = users.find(u => u.primaryEmail === '<EMAIL>')
    }
    
    if (!user && users.length > 0) {
      // Fallback: retornar o primeiro usuário para teste
      user = users[0]
    }
    
    if (user) {
      return NextResponse.json({
        id: user.id,
        email: user.primaryEmail,
        name: user.displayName,
        avatar: user.profileImageUrl,
        hasPassword: user.hasPassword,
        authMethods: user.authMethods
      })
    }
    
    return NextResponse.json({ error: 'Not authenticated' }, { status: 401 })
    
  } catch (error) {
    console.error('[API/ME] Erro:', error)
    return NextResponse.json({ 
      error: 'Failed to get user info',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}