import { NextRequest, NextResponse } from 'next/server'
import { createServerDatabaseClient } from '@/lib/supabase/database-only'

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams
    const email = searchParams.get('email')
    
    if (!email) {
      return NextResponse.json(
        { error: 'Email parameter required' },
        { status: 400 }
      )
    }
    
    const supabase = await createServerDatabaseClient()
    
    // 1. Buscar usuário por email em ambas as tabelas
    const { data: stackProfile } = await supabase
      .from('stack_profiles')
      .select('*')
      .eq('email', email)
      .single()
    
    const { data: supabaseProfile } = await supabase
      .from('profiles')
      .select('*')
      .eq('email', email)
      .single()
    
    const userId = stackProfile?.id || supabaseProfile?.id
    
    if (!userId) {
      return NextResponse.json({
        email,
        userFound: false,
        message: 'User not found in any profile table'
      })
    }
    
    // 2. Verificar acesso usando a função RPC
    const { data: accessData, error: accessError } = await supabase
      .rpc('check_user_access_unified', { p_user_id: userId })
      .single()
    
    // 3. Verificar sessões de trial
    const { data: trialSessions } = await supabase
      .from('trial_sessions')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(5)
    
    // 4. Verificar cupons
    const { data: coupons } = await supabase
      .from('user_coupons')
      .select('*, cupons(*)')
      .eq('user_id', userId)
      .order('used_at', { ascending: false })
    
    // 5. Verificar tokens de app
    const { data: appTokens } = await supabase
      .from('app_verification_tokens')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(5)
    
    // 6. Verificar assinaturas Stripe
    const { data: stripeCustomer } = await supabase
      .from('stripe_customers')
      .select('*')
      .eq('user_id', userId)
      .single()
    
    let stripeSubscriptions = null
    if (stripeCustomer) {
      const { data } = await supabase
        .from('stripe_subscriptions')
        .select('*')
        .eq('customer_id', stripeCustomer.stripe_customer_id)
        .order('created_at', { ascending: false })
      stripeSubscriptions = data
    }
    
    const response = {
      timestamp: new Date().toISOString(),
      email,
      userId,
      profileSource: stackProfile ? 'stack_auth' : 'supabase_auth',
      
      // Estado de acesso atual
      currentAccess: {
        hasAccess: accessData?.has_access || false,
        accessType: accessData?.access_type || null,
        expiresAt: accessData?.expires_at || null,
        rpcError: accessError?.message || null
      },
      
      // Detalhes do perfil
      profile: {
        stackProfile: stackProfile ? {
          id: stackProfile.id,
          email: stackProfile.email,
          name: stackProfile.name,
          created_at: stackProfile.created_at,
          has_premium: stackProfile.has_premium,
          premium_until: stackProfile.premium_until
        } : null,
        supabaseProfile: supabaseProfile ? {
          id: supabaseProfile.id,
          email: supabaseProfile.email,
          full_name: supabaseProfile.full_name,
          created_at: supabaseProfile.created_at
        } : null
      },
      
      // Histórico de trials
      trialHistory: trialSessions?.map(session => ({
        id: session.id,
        session_id: session.session_id,
        start_time: session.trial_start,
        end_time: session.trial_end,
        duration_minutes: session.trial_duration_minutes,
        created_at: session.created_at
      })) || [],
      
      // Cupons usados
      couponsUsed: coupons?.map(uc => ({
        coupon_code: uc.cupons?.codigo,
        used_at: uc.used_at,
        valid_until: uc.valid_until
      })) || [],
      
      // Tokens de app
      appTokenHistory: appTokens?.map(token => ({
        token_id: token.token,
        created_at: token.created_at,
        expires_at: token.expires_at,
        used: token.used,
        used_at: token.used_at,
        platform: token.platform
      })) || [],
      
      // Assinatura Stripe
      stripeInfo: {
        hasCustomer: !!stripeCustomer,
        customerId: stripeCustomer?.stripe_customer_id,
        subscriptions: stripeSubscriptions?.map(sub => ({
          id: sub.stripe_subscription_id,
          status: sub.status,
          current_period_start: sub.current_period_start,
          current_period_end: sub.current_period_end,
          created_at: sub.created_at,
          updated_at: sub.updated_at
        })) || []
      },
      
      // Resumo
      summary: {
        hasAnyAccess: accessData?.has_access || false,
        accessSources: [
          coupons && coupons.length > 0 && 'coupon',
          appTokens?.some(t => t.used) && 'app_activation',
          stripeSubscriptions?.some(s => s.status === 'active') && 'stripe_subscription',
          stackProfile?.has_premium && 'manual_premium'
        ].filter(Boolean),
        shouldHaveAccess: !!(
          (coupons && coupons.some(c => new Date(c.valid_until) > new Date())) ||
          (appTokens && appTokens.some(t => t.used)) ||
          (stripeSubscriptions && stripeSubscriptions.some(s => s.status === 'active')) ||
          (stackProfile && stackProfile.has_premium && new Date(stackProfile.premium_until) > new Date())
        )
      }
    }
    
    return NextResponse.json(response)
    
  } catch (error) {
    console.error('Error in access-state debug:', error)
    return NextResponse.json(
      { error: 'Internal server error', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
}