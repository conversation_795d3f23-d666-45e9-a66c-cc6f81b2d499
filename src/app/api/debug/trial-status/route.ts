import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient()
    
    // Get user from auth
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json({
        error: 'Not authenticated',
        details: authError?.message
      }, { status: 401 })
    }
    
    // Check trial sessions for this user
    const { data: userTrials, error: trialError } = await supabase
      .from('trial_sessions')
      .select('*')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false })
    
    // Check if user has active trial
    const { data: rpcResult, error: rpcError } = await supabase
      .rpc('get_or_create_trial_session', {
        p_user_id: user.id,
        p_session_id: null
      })
      .single()
    
    // Get all trials from this IP (for debugging)
    const { data: allTrials } = await supabase
      .from('trial_sessions')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(10)
    
    const now = new Date()
    
    return NextResponse.json({
      user: {
        id: user.id,
        email: user.email
      },
      current_trial: rpcResult,
      user_trials: userTrials || [],
      recent_trials: allTrials || [],
      server_time: now.toISOString(),
      debug: {
        has_active_trial: rpcResult?.is_expired === false,
        time_remaining: rpcResult?.time_remaining || 0,
        trial_count: userTrials?.length || 0,
        last_trial: userTrials?.[0] || null
      }
    })
  } catch (error) {
    console.error('[TRIAL-STATUS-DEBUG] Error:', error)
    return NextResponse.json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 })
  }
}