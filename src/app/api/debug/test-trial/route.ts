import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient()
    
    // Get user from auth
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json({
        error: 'Not authenticated',
        message: 'Please login to test trial system'
      }, { status: 401 })
    }
    
    // Check trial status for this user
    const { data: rpcResult, error: rpcError } = await supabase
      .rpc('get_or_create_trial_session', {
        p_user_id: user.id,
        p_session_id: null
      })
      .single()
    
    if (rpcError) {
      console.error('[TEST-TRIAL] RPC Error:', rpcError)
      return NextResponse.json({
        error: 'Failed to check trial',
        details: rpcError.message
      }, { status: 500 })
    }
    
    // Get all trial sessions for this user
    const { data: userTrials, error: trialsError } = await supabase
      .from('trial_sessions')
      .select('*')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false })
    
    return NextResponse.json({
      success: true,
      user: {
        id: user.id,
        email: user.email
      },
      current_trial: {
        trial_id: rpcResult.trial_id,
        time_remaining: rpcResult.time_remaining,
        is_expired: rpcResult.is_expired,
        formatted_time: `${Math.floor(rpcResult.time_remaining / 60)}:${(rpcResult.time_remaining % 60).toString().padStart(2, '0')}`
      },
      all_user_trials: userTrials || [],
      test_info: {
        message: 'Este endpoint verifica se o trial está funcionando corretamente por usuário',
        instructions: [
          '1. Acesse este endpoint em diferentes navegadores',
          '2. O time_remaining deve continuar de onde parou',
          '3. Não deve resetar para 5:00 ao trocar de navegador',
          '4. Após expirar em um navegador, deve estar expirado em todos'
        ]
      }
    })
  } catch (error) {
    console.error('[TEST-TRIAL] Error:', error)
    return NextResponse.json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 })
  }
}

// POST para forçar reset do trial (apenas para testes)
export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient()
    
    // Get user from auth
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json({
        error: 'Not authenticated'
      }, { status: 401 })
    }
    
    // Delete all trial sessions for this user
    const { error: deleteError } = await supabase
      .from('trial_sessions')
      .delete()
      .eq('user_id', user.id)
    
    if (deleteError) {
      return NextResponse.json({
        error: 'Failed to reset trials',
        details: deleteError.message
      }, { status: 500 })
    }
    
    return NextResponse.json({
      success: true,
      message: 'All trials reset for user',
      user_id: user.id
    })
  } catch (error) {
    console.error('[TEST-TRIAL-RESET] Error:', error)
    return NextResponse.json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 })
  }
}