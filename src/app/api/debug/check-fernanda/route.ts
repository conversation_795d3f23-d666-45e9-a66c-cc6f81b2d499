import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { stackServerApp } from '@/lib/auth/stack-server'

export async function GET(request: NextRequest) {
  const timestamp = new Date().toISOString()
  console.log(`[DEBUG-FERNANDA] ${timestamp} - ===== INICIANDO DEBUG FERNANDA =====`)
  
  try {
    const supabase = await createClient()
    const email = '<EMAIL>'
    
    // 1. Verificar profiles (Supabase Auth)
    console.log(`[DEBUG-FERNANDA] ${timestamp} - Buscando em profiles...`)
    const { data: profileData, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('email', email)
      .single()
    
    if (profileError) {
      console.log(`[DEBUG-FERNANDA] ${timestamp} - Erro em profiles:`, profileError.message)
    } else {
      console.log(`[DEBUG-FERNANDA] ${timestamp} - Dados em profiles:`)
      console.log(JSON.stringify(profileData, null, 2))
    }
    
    // 2. Verificar Stack Auth
    console.log(`[DEBUG-FERNANDA] ${timestamp} - Buscando usuário Stack Auth...`)
    let stackUserId = null
    try {
      const users = await stackServerApp.listUsers()
      const stackUser = users.find(u => u.primaryEmail === email)
      
      if (stackUser) {
        stackUserId = stackUser.id
        console.log(`[DEBUG-FERNANDA] ${timestamp} - Stack Auth ID encontrado:`, stackUserId)
        console.log(`[DEBUG-FERNANDA] ${timestamp} - Stack Auth User:`)
        console.log(JSON.stringify({
          id: stackUser.id,
          email: stackUser.primaryEmail,
          displayName: stackUser.displayName,
          signedUpAt: stackUser.signedUpAt
        }, null, 2))
      } else {
        console.log(`[DEBUG-FERNANDA] ${timestamp} - Usuário NÃO encontrado no Stack Auth`)
      }
    } catch (stackError) {
      console.error(`[DEBUG-FERNANDA] ${timestamp} - Erro ao buscar Stack Auth:`, stackError)
    }
    
    // 3. Verificar stack_profiles
    console.log(`[DEBUG-FERNANDA] ${timestamp} - Buscando em stack_profiles...`)
    const { data: stackProfileData, error: stackProfileError } = await supabase
      .from('stack_profiles')
      .select('*')
      .eq('email', email)
    
    if (stackProfileError) {
      console.log(`[DEBUG-FERNANDA] ${timestamp} - Erro em stack_profiles:`, stackProfileError.message)
    } else {
      console.log(`[DEBUG-FERNANDA] ${timestamp} - Dados em stack_profiles (${stackProfileData?.length || 0} registros):`)
      console.log(JSON.stringify(stackProfileData, null, 2))
    }
    
    // 4. Se temos Stack ID, buscar por ID também
    if (stackUserId) {
      console.log(`[DEBUG-FERNANDA] ${timestamp} - Buscando stack_profiles por ID...`)
      const { data: stackByIdData, error: stackByIdError } = await supabase
        .from('stack_profiles')
        .select('*')
        .eq('id', stackUserId)
        .single()
      
      if (stackByIdError) {
        console.log(`[DEBUG-FERNANDA] ${timestamp} - Erro ao buscar por ID:`, stackByIdError.message)
      } else {
        console.log(`[DEBUG-FERNANDA] ${timestamp} - Dados encontrados por ID:`)
        console.log(JSON.stringify(stackByIdData, null, 2))
      }
    }
    
    // 5. Verificar RPC function
    console.log(`[DEBUG-FERNANDA] ${timestamp} - Testando RPC check_user_access_unified...`)
    if (stackUserId) {
      const { data: rpcData, error: rpcError } = await supabase
        .rpc('check_user_access_unified', { p_user_id: stackUserId })
        .single()
      
      if (rpcError) {
        console.log(`[DEBUG-FERNANDA] ${timestamp} - Erro RPC:`, rpcError.message)
      } else {
        console.log(`[DEBUG-FERNANDA] ${timestamp} - Resultado RPC:`)
        console.log(JSON.stringify(rpcData, null, 2))
      }
    }
    
    // 6. Buscar usuário autenticado atual
    console.log(`[DEBUG-FERNANDA] ${timestamp} - Verificando usuário autenticado...`)
    const { data: { user } } = await supabase.auth.getUser()
    if (user) {
      console.log(`[DEBUG-FERNANDA] ${timestamp} - Usuário autenticado:`)
      console.log(JSON.stringify({
        id: user.id,
        email: user.email,
        provider: user.app_metadata?.provider
      }, null, 2))
    } else {
      console.log(`[DEBUG-FERNANDA] ${timestamp} - Nenhum usuário autenticado`)
    }
    
    // Resumo
    const summary = {
      email,
      foundInProfiles: !!profileData,
      foundInStackAuth: !!stackUserId,
      foundInStackProfiles: !!stackProfileData && stackProfileData.length > 0,
      stackUserId,
      profilesData: profileData || null,
      stackProfilesData: stackProfileData || null,
      authUser: user ? { id: user.id, email: user.email } : null
    }
    
    console.log(`[DEBUG-FERNANDA] ${timestamp} - ===== RESUMO =====`)
    console.log(JSON.stringify(summary, null, 2))
    
    return NextResponse.json(summary)
    
  } catch (error) {
    console.error(`[DEBUG-FERNANDA] ${timestamp} - Erro geral:`, error)
    return NextResponse.json(
      { error: 'Erro ao verificar dados', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
}