import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  const timestamp = new Date().toISOString()
  console.log(`[DEBUG-FERNANDA-STATUS] ${timestamp} - ===== VERIFICANDO STATUS DA FERNANDA =====`)
  
  try {
    // Fazer uma requisição para o endpoint subscription-status simulando a Fernanda
    const response = await fetch('http://localhost:3000/api/stripe/subscription-status', {
      headers: {
        'Cookie': request.headers.get('cookie') || ''
      }
    })
    
    const data = await response.json()
    
    console.log(`[DEBUG-FERNANDA-STATUS] ${timestamp} - Response status:`, response.status)
    console.log(`[DEBUG-FERNANDA-STATUS] ${timestamp} - Response data:`, JSON.stringify(data, null, 2))
    
    // Análise do resultado
    const analysis = {
      timestamp,
      statusCode: response.status,
      data,
      analysis: {
        hasAccess: data.hasAccess || false,
        isSubscribed: data.isSubscribed || false,
        isInTrial: data.isInTrial || false,
        shouldShowPremium: data.isSubscribed && data.subscriptionTier === 'premium',
        shouldShowTrial: data.isInTrial && !data.isSubscribed,
        shouldShowNothing: !data.hasAccess && !data.isInTrial && !data.isSubscribed
      }
    }
    
    console.log(`[DEBUG-FERNANDA-STATUS] ${timestamp} - Analysis:`, JSON.stringify(analysis, null, 2))
    
    return NextResponse.json(analysis)
    
  } catch (error) {
    console.error(`[DEBUG-FERNANDA-STATUS] ${timestamp} - Erro:`, error)
    return NextResponse.json(
      { error: 'Erro ao verificar status', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
}