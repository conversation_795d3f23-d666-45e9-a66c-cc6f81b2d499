import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { stackServerApp } from '@/lib/auth/stack-server'

export async function GET(request: NextRequest) {
  const timestamp = new Date().toISOString()
  console.log(`[DEBUG-SUBSCRIPTION] ${timestamp} - ===== DEBUG CHECK STARTED =====`)
  
  try {
    const supabase = await createClient()
    
    // 1. Verificar usuário autenticado
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      console.log(`[DEBUG-SUBSCRIPTION] ${timestamp} - ❌ No authenticated user`)
      return NextResponse.json({
        error: 'No authenticated user',
        timestamp
      }, { status: 401 })
    }
    
    console.log(`[DEBUG-SUBSCRIPTION] ${timestamp} - User found:`, {
      id: user.id,
      email: user.email
    })
    
    // 2. Buscar em profiles (Supabase Auth)
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', user.id)
      .single()
    
    console.log(`[DEBUG-SUBSCRIPTION] ${timestamp} - Profile data:`, profile || 'NOT FOUND')
    if (profileError) {
      console.log(`[DEBUG-SUBSCRIPTION] ${timestamp} - Profile error:`, profileError)
    }
    
    // 3. Buscar Stack Auth user
    let stackProfile = null
    let stackUserId = null
    try {
      const users = await stackServerApp.listUsers()
      const stackUser = users.find(u => u.primaryEmail === user.email)
      if (stackUser) {
        stackUserId = stackUser.id
        console.log(`[DEBUG-SUBSCRIPTION] ${timestamp} - Stack Auth user found:`, stackUserId)
        
        // Buscar em stack_profiles
        const { data: sp } = await supabase
          .from('stack_profiles')
          .select('*')
          .eq('id', stackUserId)
          .single()
        
        stackProfile = sp
        console.log(`[DEBUG-SUBSCRIPTION] ${timestamp} - Stack profile data:`, stackProfile || 'NOT FOUND')
      }
    } catch (error) {
      console.log(`[DEBUG-SUBSCRIPTION] ${timestamp} - Stack Auth error:`, error)
    }
    
    // 4. Chamar RPC check_user_access_unified
    const rpcUserId = stackUserId || user.id
    console.log(`[DEBUG-SUBSCRIPTION] ${timestamp} - Calling RPC with ID:`, rpcUserId)
    
    const { data: accessData, error: rpcError } = await supabase
      .rpc('check_user_access_unified', { p_user_id: rpcUserId })
      .single()
    
    console.log(`[DEBUG-SUBSCRIPTION] ${timestamp} - RPC result:`, accessData)
    if (rpcError) {
      console.log(`[DEBUG-SUBSCRIPTION] ${timestamp} - RPC error:`, rpcError)
    }
    
    // 5. Verificar API subscription-status
    const statusResponse = await fetch(`${process.env.NEXT_PUBLIC_APP_URL}/api/stripe/subscription-status`, {
      headers: {
        'Cookie': request.headers.get('cookie') || ''
      }
    })
    
    const statusData = await statusResponse.json()
    console.log(`[DEBUG-SUBSCRIPTION] ${timestamp} - Status API result:`, statusData)
    
    // 6. Compilar resultado final
    const result = {
      timestamp,
      auth: {
        supabaseUserId: user.id,
        stackUserId: stackUserId,
        email: user.email,
        authType: stackUserId ? 'Stack Auth' : 'Supabase Auth'
      },
      profiles: {
        supabaseProfile: profile ? {
          id: profile.id,
          email: profile.email,
          subscription_tier: profile.subscription_tier,
          subscription_status: profile.subscription_status,
          subscription_current_period_end: profile.subscription_current_period_end,
          stripe_customer_id: profile.stripe_customer_id,
          stripe_subscription_id: profile.stripe_subscription_id,
          app_verified: profile.app_verified
        } : null,
        stackProfile: stackProfile ? {
          id: stackProfile.id,
          email: stackProfile.email,
          subscription_tier: stackProfile.subscription_tier,
          subscription_status: stackProfile.subscription_status,
          subscription_current_period_end: stackProfile.subscription_current_period_end,
          stripe_customer_id: stackProfile.stripe_customer_id,
          stripe_subscription_id: stackProfile.stripe_subscription_id,
          app_verified: stackProfile.app_verified
        } : null
      },
      rpcCheck: accessData,
      statusAPI: statusData,
      finalStatus: {
        hasAccess: accessData?.has_access || statusData?.hasAccess || false,
        accessType: accessData?.access_type || statusData?.accessType || null,
        tier: accessData?.subscription_tier || statusData?.subscriptionTier || 'free',
        expiresAt: accessData?.expires_at || statusData?.subscriptionEndsAt || null
      }
    }
    
    console.log(`[DEBUG-SUBSCRIPTION] ${timestamp} - ===== FINAL RESULT =====`)
    console.log(JSON.stringify(result, null, 2))
    
    return NextResponse.json(result)
    
  } catch (error) {
    console.error(`[DEBUG-SUBSCRIPTION] ${timestamp} - ❌ Error:`, error)
    return NextResponse.json({
      error: 'Internal error',
      message: error instanceof Error ? error.message : 'Unknown error',
      timestamp
    }, { status: 500 })
  }
}