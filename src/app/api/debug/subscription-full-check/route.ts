import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { stackServerApp } from '@/lib/auth/stack-server'

export async function GET(request: NextRequest) {
  const timestamp = new Date().toISOString()
  console.log(`[DEBUG-SUBSCRIPTION-CHECK] ${timestamp} - ===== CHECKING SUBSCRIPTION =====`)
  
  try {
    const supabase = await createClient()
    
    // Obter usuário autenticado
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      return NextResponse.json({ error: 'Não autenticado' }, { status: 401 })
    }
    
    console.log(`[DEBUG-SUBSCRIPTION-CHECK] User:`, user.email)
    
    // Fazer a mesma chamada que o hook faz
    const response = await fetch('http://localhost:3000/api/stripe/subscription-status', {
      headers: {
        'Cookie': request.headers.get('cookie') || ''
      }
    })
    
    const data = await response.json()
    
    console.log(`[DEBUG-SUBSCRIPTION-CHECK] Response:`, JSON.stringify(data, null, 2))
    
    // Verificar diretamente no banco também
    let dbProfile = null
    let stackProfile = null
    
    // Verificar profiles
    const { data: profileData } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', user.id)
      .single()
    
    dbProfile = profileData
    
    // Verificar stack_profiles se for Stack Auth
    try {
      const users = await stackServerApp.listUsers()
      const stackUser = users.find(u => u.primaryEmail === user.email)
      
      if (stackUser) {
        const { data: stackData } = await supabase
          .from('stack_profiles')
          .select('*')
          .eq('id', stackUser.id)
          .single()
        
        stackProfile = stackData
      }
    } catch (error) {
      console.error('Erro ao buscar Stack profile:', error)
    }
    
    return NextResponse.json({
      timestamp,
      user: {
        id: user.id,
        email: user.email
      },
      apiResponse: data,
      databaseData: {
        profiles: dbProfile,
        stack_profiles: stackProfile
      },
      analysis: {
        shouldBePremium: (dbProfile?.subscription_tier === 'premium' && dbProfile?.subscription_status === 'active') || 
                        (stackProfile?.subscription_tier === 'premium' && stackProfile?.subscription_status === 'active'),
        apiSaysPremium: data.isSubscribed === true,
        apiSaysTrial: data.isInTrial === true,
        problem: data.isInTrial && !data.isSubscribed && 
                (dbProfile?.subscription_tier === 'premium' || stackProfile?.subscription_tier === 'premium')
      }
    })
    
  } catch (error) {
    console.error(`[DEBUG-SUBSCRIPTION-CHECK] Error:`, error)
    return NextResponse.json(
      { error: 'Erro ao verificar', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
}