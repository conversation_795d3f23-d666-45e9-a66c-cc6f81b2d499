import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { headers } from 'next/headers'

export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient()
    
    // Get user from auth
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json({
        error: 'Not authenticated',
        details: authError?.message
      }, { status: 401 })
    }
    
    // Check stripe_subscriptions table
    const { data: stripeSubscription, error: stripeError } = await supabase
      .from('stripe_subscriptions')
      .select('*')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false })
      .limit(1)
      .single()
    
    // Check profiles table
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', user.id)
      .single()
    
    // Check stack_profiles table
    const { data: stackProfile, error: stackProfileError } = await supabase
      .from('stack_profiles')
      .select('*')
      .eq('id', user.id)
      .single()
    
    // Check recent webhook events
    const { data: webhookEvents, error: webhookError } = await supabase
      .from('stripe_webhook_events')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(5)
    
    // Check payment history
    const { data: paymentHistory, error: paymentError } = await supabase
      .from('payment_history')
      .select('*')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false })
      .limit(5)
    
    const now = new Date()
    
    return NextResponse.json({
      user: {
        id: user.id,
        email: user.email,
        created_at: user.created_at
      },
      stripe_subscription: stripeSubscription || null,
      profile: profile ? {
        id: profile.id,
        email: profile.email,
        subscription_tier: profile.subscription_tier,
        subscription_status: profile.subscription_status,
        subscription_current_period_end: profile.subscription_current_period_end,
        stripe_customer_id: profile.stripe_customer_id,
        stripe_subscription_id: profile.stripe_subscription_id,
        is_active: profile.subscription_status === 'active' && 
                   profile.subscription_current_period_end && 
                   new Date(profile.subscription_current_period_end) > now
      } : null,
      stack_profile: stackProfile ? {
        id: stackProfile.id,
        email: stackProfile.email,
        subscription_tier: stackProfile.subscription_tier,
        subscription_status: stackProfile.subscription_status,
        subscription_current_period_end: stackProfile.subscription_current_period_end,
        stripe_customer_id: stackProfile.stripe_customer_id,
        stripe_subscription_id: stackProfile.stripe_subscription_id,
        is_active: stackProfile.subscription_status === 'active' && 
                   stackProfile.subscription_current_period_end && 
                   new Date(stackProfile.subscription_current_period_end) > now
      } : null,
      recent_webhook_events: webhookEvents || [],
      payment_history: paymentHistory || [],
      summary: {
        has_stripe_subscription: !!stripeSubscription,
        has_active_subscription: (stripeSubscription?.status === 'active' && 
                                 stripeSubscription?.current_period_end && 
                                 new Date(stripeSubscription.current_period_end) > now) || false,
        profile_has_premium: profile?.subscription_tier === 'premium',
        stack_profile_has_premium: stackProfile?.subscription_tier === 'premium',
        profile_is_active: profile?.subscription_status === 'active' && 
                          profile?.subscription_current_period_end && 
                          new Date(profile.subscription_current_period_end) > now,
        stack_profile_is_active: stackProfile?.subscription_status === 'active' && 
                                stackProfile?.subscription_current_period_end && 
                                new Date(stackProfile.subscription_current_period_end) > now
      }
    })
  } catch (error) {
    console.error('[STRIPE-STATUS-DEBUG] Error:', error)
    return NextResponse.json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 })
  }
}