import { NextResponse } from 'next/server'

export async function GET() {
  // Verificar se as chaves do Stripe estão corretas
  const stripeSecretKey = process.env.STRIPE_SECRET_KEY || ''
  const stripePublishableKey = process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY || ''
  
  // Detectar se as chaves estão invertidas
  const secretKeyLooksLikePublishable = stripeSecretKey.startsWith('pk_')
  const publishableKeyLooksLikeSecret = stripePublishableKey.startsWith('sk_')
  const keysAreInverted = secretKeyLooksLikePublishable || publishableKeyLooksLikeSecret
  
  return NextResponse.json({
    status: keysAreInverted ? '❌ ERRO: Chaves do Stripe estão INVERTIDAS!' : '✅ Chaves parecem estar corretas',
    stripe: {
      STRIPE_SECRET_KEY: {
        defined: stripeSecretKey ? '✅ Definida' : '❌ Não definida',
        prefix: stripeSecretKey ? stripeSecretKey.substring(0, 7) + '...' : 'N/A',
        isCorrect: stripeSecretKey.startsWith('sk_') ? '✅ Parece ser secret key' : '❌ NÃO parece ser secret key',
        problem: secretKeyLooksLikePublishable ? '⚠️ Esta é uma PUBLISHABLE KEY sendo usada como SECRET!' : null
      },
      NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY: {
        defined: stripePublishableKey ? '✅ Definida' : '❌ Não definida',
        prefix: stripePublishableKey ? stripePublishableKey.substring(0, 7) + '...' : 'N/A',
        isCorrect: stripePublishableKey.startsWith('pk_') ? '✅ Parece ser publishable key' : '❌ NÃO parece ser publishable key',
        problem: publishableKeyLooksLikeSecret ? '⚠️ Esta é uma SECRET KEY sendo usada como PUBLISHABLE!' : null
      },
      keysAreInverted: keysAreInverted ? '❌ SIM - AS CHAVES ESTÃO INVERTIDAS!' : '✅ NÃO - Chaves estão corretas'
    },
    env: {
      NODE_ENV: process.env.NODE_ENV,
      NEXT_PUBLIC_APP_URL: process.env.NEXT_PUBLIC_APP_URL,
      STRIPE_WEBHOOK_SECRET: process.env.STRIPE_WEBHOOK_SECRET ? '✅ Definida' : '❌ Não definida',
    },
    solution: keysAreInverted ? {
      message: '🔧 SOLUÇÃO: No Railway, você precisa INVERTER as chaves:',
      steps: [
        '1. STRIPE_SECRET_KEY deve receber o valor que começa com sk_live_...',
        '2. NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY deve receber o valor que começa com pk_live_...',
        '3. Salve e aguarde o redeploy'
      ]
    } : null
  })
}