import { NextRequest, NextResponse } from 'next/server'
import { logger } from '@/lib/logger'
import { LOG_TAGS } from '@/config/debug'

interface DetailedTestResult {
  channelId: string
  name: string
  status: 'working' | 'error_404' | 'error_403' | 'error_cors' | 'error_auth' | 'error_geo' | 'error_other'
  streamUrl?: string
  errorDetails?: string
  httpStatus?: number
  solution?: string
  canBeFixed?: boolean
  testTimestamp: string
}

async function testChannelWithAllMethods(channel: any, origin: string): Promise<DetailedTestResult> {
  const startTime = Date.now()
  
  try {
    // 1. Buscar dados do canal da API
    const channelResponse = await fetch(`${origin}/api/iptv/channel/${channel.id}`)
    
    if (!channelResponse.ok) {
      return {
        channelId: channel.id,
        name: channel.name,
        status: 'error_404',
        errorDetails: `Channel API returned ${channelResponse.status}`,
        httpStatus: channelResponse.status,
        canBeFixed: false,
        testTimestamp: new Date().toISOString()
      }
    }
    
    const channelData = await channelResponse.json()
    
    if (!channelData.success || !channelData.data?.url) {
      return {
        channelId: channel.id,
        name: channel.name,
        status: 'error_404',
        errorDetails: 'No stream URL in API response',
        canBeFixed: false,
        testTimestamp: new Date().toISOString()
      }
    }
    
    const streamUrl = channelData.data.url
    
    // 2. Testar acesso direto
    const directTest = await fetch(streamUrl, {
      method: 'HEAD',
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        ...(channelData.data.headers || {})
      },
      signal: AbortSignal.timeout(10000)
    }).catch(err => null)
    
    if (directTest && directTest.ok) {
      return {
        channelId: channel.id,
        name: channel.name,
        status: 'working',
        streamUrl,
        httpStatus: directTest.status,
        testTimestamp: new Date().toISOString()
      }
    }
    
    // 3. Analisar tipo de erro
    if (directTest) {
      if (directTest.status === 403) {
        // Tentar com proxy para geo-blocking
        const proxyTest = await fetch(`${origin}/api/advanced-proxy?url=${encodeURIComponent(streamUrl)}&channelId=${channel.id}`, {
          method: 'HEAD',
          signal: AbortSignal.timeout(10000)
        }).catch(err => null)
        
        if (proxyTest && proxyTest.ok) {
          return {
            channelId: channel.id,
            name: channel.name,
            status: 'working',
            streamUrl,
            httpStatus: 200,
            solution: 'Proxy required for geo-blocking',
            canBeFixed: true,
            testTimestamp: new Date().toISOString()
          }
        }
        
        return {
          channelId: channel.id,
          name: channel.name,
          status: 'error_geo',
          streamUrl,
          httpStatus: 403,
          errorDetails: 'Geoblocked - requires VPN or specific location',
          canBeFixed: true,
          solution: 'Use proxy with geo-bypass',
          testTimestamp: new Date().toISOString()
        }
      }
      
      if (directTest.status === 401) {
        return {
          channelId: channel.id,
          name: channel.name,
          status: 'error_auth',
          streamUrl,
          httpStatus: 401,
          errorDetails: 'Authentication required',
          canBeFixed: false,
          testTimestamp: new Date().toISOString()
        }
      }
      
      if (directTest.status === 404) {
        return {
          channelId: channel.id,
          name: channel.name,
          status: 'error_404',
          streamUrl,
          httpStatus: 404,
          errorDetails: 'Stream not found - URL may be expired',
          canBeFixed: false,
          testTimestamp: new Date().toISOString()
        }
      }
    }
    
    // 4. Testar com proxy para CORS
    const proxyUrl = `${origin}/api/advanced-proxy?url=${encodeURIComponent(streamUrl)}&channelId=${channel.id}`
    const proxyResponse = await fetch(proxyUrl, {
      method: 'HEAD',
      signal: AbortSignal.timeout(10000)
    }).catch(err => null)
    
    if (proxyResponse && proxyResponse.ok) {
      return {
        channelId: channel.id,
        name: channel.name,
        status: 'working',
        streamUrl,
        httpStatus: 200,
        solution: 'Proxy required for CORS',
        canBeFixed: true,
        testTimestamp: new Date().toISOString()
      }
    }
    
    // 5. Testar o conteúdo real do stream
    const contentTest = await fetch(proxyUrl, {
      signal: AbortSignal.timeout(15000)
    }).catch(err => null)
    
    if (contentTest && contentTest.ok) {
      const contentType = contentTest.headers.get('content-type')
      if (contentType?.includes('mpegurl') || contentType?.includes('m3u8')) {
        return {
          channelId: channel.id,
          name: channel.name,
          status: 'working',
          streamUrl,
          httpStatus: 200,
          solution: 'Content test passed',
          canBeFixed: true,
          testTimestamp: new Date().toISOString()
        }
      }
    }
    
    return {
      channelId: channel.id,
      name: channel.name,
      status: 'error_other',
      streamUrl,
      errorDetails: 'All test methods failed',
      canBeFixed: false,
      httpStatus: proxyResponse?.status || 0,
      testTimestamp: new Date().toISOString()
    }
    
  } catch (error) {
    return {
      channelId: channel.id,
      name: channel.name,
      status: 'error_other',
      errorDetails: error instanceof Error ? error.message : 'Unknown error',
      canBeFixed: false,
      testTimestamp: new Date().toISOString()
    }
  }
}

export async function GET(request: NextRequest) {
  try {
    logger.info(LOG_TAGS.IPTV, 'Starting comprehensive channel test')
    
    // Buscar todos os canais
    const iptvResponse = await fetch(`${request.nextUrl.origin}/api/iptv?limit=500`)
    const iptvData = await iptvResponse.json()
    
    if (!iptvData.success || !iptvData.data) {
      return NextResponse.json({ error: 'Failed to fetch channels' }, { status: 500 })
    }
    
    const allChannels = iptvData.data
    logger.info(LOG_TAGS.IPTV, `Testing ${allChannels.length} channels`)
    
    const results: DetailedTestResult[] = []
    const batchSize = 5 // Testar 5 canais por vez
    
    // Processar em lotes para não sobrecarregar
    for (let i = 0; i < allChannels.length; i += batchSize) {
      const batch = allChannels.slice(i, i + batchSize)
      const batchPromises = batch.map((channel: any) => 
        testChannelWithAllMethods(channel, request.nextUrl.origin)
      )
      
      const batchResults = await Promise.all(batchPromises)
      results.push(...batchResults)
      
      // Log progresso
      logger.info(LOG_TAGS.IPTV, `Progress: ${Math.min(i + batchSize, allChannels.length)}/${allChannels.length}`)
    }
    
    // Gerar relatório detalhado
    const summary = {
      total: results.length,
      working: results.filter(r => r.status === 'working').length,
      fixable: results.filter(r => r.canBeFixed).length,
      unfixable: results.filter(r => !r.canBeFixed && r.status !== 'working').length,
      byStatus: {
        working: results.filter(r => r.status === 'working').length,
        error_404: results.filter(r => r.status === 'error_404').length,
        error_403: results.filter(r => r.status === 'error_403').length,
        error_cors: results.filter(r => r.status === 'error_cors').length,
        error_auth: results.filter(r => r.status === 'error_auth').length,
        error_geo: results.filter(r => r.status === 'error_geo').length,
        error_other: results.filter(r => r.status === 'error_other').length
      }
    }
    
    // Salvar resultados em arquivo para análise
    const report = {
      timestamp: new Date().toISOString(),
      summary,
      workingChannels: results.filter(r => r.status === 'working'),
      fixableChannels: results.filter(r => r.canBeFixed && r.status !== 'working'),
      unfixableChannels: results.filter(r => !r.canBeFixed && r.status !== 'working')
    }
    
    logger.info(LOG_TAGS.IPTV, 'Channel test completed', summary)
    
    return NextResponse.json({
      success: true,
      report
    })
    
  } catch (error) {
    logger.error(LOG_TAGS.IPTV, 'Test all channels error', { error })
    return NextResponse.json({ 
      success: false, 
      error: error instanceof Error ? error.message : 'Test failed' 
    }, { status: 500 })
  }
}