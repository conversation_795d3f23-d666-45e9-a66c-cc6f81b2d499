import { NextRequest, NextResponse } from 'next/server'
import { getChannelHeaders } from '@/lib/channel-headers'

export async function GET(request: NextRequest) {
  const channelId = request.nextUrl.searchParams.get('channelId')
  
  console.log('🧪 Test Headers API called')
  console.log('Channel ID received:', channelId)
  
  if (!channelId) {
    return NextResponse.json({ 
      error: 'channelId is required',
      received: null 
    }, { status: 400 })
  }
  
  const headers = getChannelHeaders(channelId)
  console.log('Headers found:', headers)
  
  return NextResponse.json({
    channelId,
    headersFound: !!headers,
    headers: headers || 'none'
  })
}