import { NextResponse } from 'next/server'
import { fetchIPTVChannels, getPopularSportsChannels } from '@/lib/iptv-org-server'

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const sports = searchParams.get('sports') === 'true'
    const popular = searchParams.get('popular') === 'true'
    const country = searchParams.get('country')
    
    let channels = await fetchIPTVChannels()
    
    // Filtrar apenas canais esportivos
    if (sports) {
      channels = channels.filter(ch => ch.categories?.includes('sports'))
    }
    
    // Filtrar por país se especificado
    if (country) {
      channels = channels.filter(ch => 
        ch.country === country.toUpperCase()
      )
    }
    
    // Se quiser apenas canais populares
    if (popular && sports) {
      channels = await getPopularSportsChannels()
    }
    
    // Ordenar por nome
    channels.sort((a, b) => a.name.localeCompare(b.name))
    
    return NextResponse.json({
      success: true,
      data: channels,
      total: channels.length
    })
  } catch (error) {
    console.error('Error fetching IPTV channels:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch channels' },
      { status: 500 }
    )
  }
}