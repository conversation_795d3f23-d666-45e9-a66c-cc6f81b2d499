import { NextResponse } from 'next/server'
import { 
  fetchSportsStreams, 
  fetchIPTVChannels, 
  getPopularSportsChannels 
} from '@/lib/iptv-org-server'

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const country = searchParams.get('country')
    const quality = searchParams.get('quality')
    const popular = searchParams.get('popular') === 'true'
    
    let streams = await fetchSportsStreams()
    
    // Buscar metadados dos canais
    const channels = await fetchIPTVChannels()
    const channelsMap = new Map(channels.map(ch => [ch.id, ch]))
    
    // Filtrar por país se especificado
    if (country) {
      streams = streams.filter(stream => {
        const channel = channelsMap.get(stream.channel)
        return channel?.country === country.toUpperCase()
      })
    }
    
    // Filtrar por qualidade se especificado
    if (quality) {
      streams = streams.filter(s => s.quality === quality)
    }
    
    // Removendo filtro popular para mostrar todos os canais
    console.log(`🎯 [API] Total de streams esportivos disponíveis: ${streams.length}`)
    
    // Enriquecer streams com metadados dos canais e garantir IDs únicos
    console.log(`🔍 Processing ${streams.length} streams for enrichment`)
    
    const enrichedStreams = streams.map((stream, index) => {
      const channel = channelsMap.get(stream.channel)
      // Criar ID único baseado apenas no canal (sem timestamp)
      const uniqueId = stream.channel
      
      return {
        ...stream,
        uniqueId,
        name: channel?.name || stream.channel,
        logo: channel?.logo,
        country: channel?.country,
        categories: channel?.categories || []
      }
    })
    
    console.log(`✅ Enriched ${enrichedStreams.length} streams with unique IDs`)
    
    // Limitar resultados
    const limit = parseInt(searchParams.get('limit') || '200')
    const paginatedStreams = enrichedStreams.slice(0, limit)
    
    console.log(`📦 [API] Retornando ${paginatedStreams.length} de ${enrichedStreams.length} streams`)
    
    return NextResponse.json({
      success: true,
      data: paginatedStreams,
      total: enrichedStreams.length
    })
  } catch (error) {
    console.error('❌ Error fetching IPTV streams:', error)
    console.error('Stack trace:', error.stack)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch streams',
        details: error.message 
      },
      { status: 500 }
    )
  }
}