import { NextResponse } from 'next/server'
import { iptvOrgService } from '@/services/api/iptv-org.service'

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const channelId = searchParams.get('channel')
    
    if (channelId) {
      // Buscar EPG de um canal específico
      const epg = await iptvOrgService.getChannelEPG(channelId)
      return NextResponse.json({
        success: true,
        data: epg,
        channel: channelId
      })
    }
    
    // Retornar todos os guias disponíveis
    const guides = await iptvOrgService.getGuides()
    
    // Filtrar apenas guias de canais esportivos
    const sportsChannels = await iptvOrgService.getSportsChannels()
    const sportsChannelIds = new Set(sportsChannels.map(ch => ch.id))
    
    const sportsGuides = guides.filter(guide => 
      sportsChannelIds.has(guide.channel)
    )
    
    return NextResponse.json({
      success: true,
      data: sportsGuides,
      total: sportsGuides.length
    })
  } catch (error) {
    console.error('Error fetching EPG data:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch EPG data' },
      { status: 500 }
    )
  }
}