import { NextResponse } from 'next/server'
import { fetchSportsStreams } from '@/lib/iptv-org-server'

export async function GET() {
  try {
    // Buscar todos os streams esportivos
    const streams = await fetchSportsStreams()
    
    // Análise de duplicatas
    const channelCounts = new Map<string, number>()
    const duplicateChannels = new Set<string>()
    
    streams.forEach(stream => {
      const count = channelCounts.get(stream.channel) || 0
      channelCounts.set(stream.channel, count + 1)
      
      if (count > 0) {
        duplicateChannels.add(stream.channel)
      }
    })
    
    // Buscar detalhes dos canais duplicados
    const duplicateDetails = Array.from(duplicateChannels).map(channelId => ({
      channelId,
      count: channelCounts.get(channelId),
      streams: streams.filter(s => s.channel === channelId).map(s => ({
        url: s.url,
        quality: s.quality,
        referrer: s.referrer || s.http_referrer,
        user_agent: s.user_agent || s.http_user_agent
      }))
    }))
    
    return NextResponse.json({
      success: true,
      data: {
        totalStreams: streams.length,
        uniqueChannels: channelCounts.size,
        duplicateChannels: duplicateChannels.size,
        duplicateDetails: duplicateDetails.slice(0, 10) // Primeiros 10 para debug
      }
    })
  } catch (error) {
    console.error('Debug error:', error)
    return NextResponse.json(
      { success: false, error: 'Debug failed' },
      { status: 500 }
    )
  }
}