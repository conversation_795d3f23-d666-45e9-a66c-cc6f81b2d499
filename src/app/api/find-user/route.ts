import { NextRequest, NextResponse } from 'next/server'
import { stackServerApp } from '@/lib/auth/stack-server'

export async function GET(request: NextRequest) {
  try {
    const email = '<EMAIL>'
    
    // Buscar usuário no Stack Auth
    const users = await stackServerApp.listUsers({
      limit: 100
    })
    
    const user = users.items.find(u => u.primaryEmail === email)
    
    if (!user) {
      return NextResponse.json({
        success: false,
        error: 'Usuário não encontrado no Stack Auth',
        totalUsers: users.items.length,
        emails: users.items.map(u => u.primaryEmail).slice(0, 10) // Primeiros 10 emails
      })
    }
    
    return NextResponse.json({
      success: true,
      stackAuth: {
        id: user.id,
        email: user.primaryEmail,
        displayName: user.displayName,
        createdAt: user.signedUpAt
      },
      // Este ID deve existir na tabela profiles
      profileId: user.id
    })
    
  } catch (error) {
    console.error('Erro ao buscar usuário:', error)
    return NextResponse.json({
      success: false,
      error: 'Erro ao buscar usuário',
      details: error instanceof Error ? error.message : 'Erro desconhecido'
    }, { status: 500 })
  }
}