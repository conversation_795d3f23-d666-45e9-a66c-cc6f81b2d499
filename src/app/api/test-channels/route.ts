import { NextRequest, NextResponse } from 'next/server'
import { logger } from '@/lib/logger'
import { LOG_TAGS } from '@/config/debug'

interface ChannelTestResult {
  channelId: string
  name: string
  status: 'working' | 'error_404' | 'error_403' | 'error_cors' | 'error_other'
  errorDetails?: string
  streamUrl?: string
  httpStatus?: number
  headers?: Record<string, string>
}

export async function POST(request: NextRequest) {
  try {
    const { channels } = await request.json()
    
    if (!channels || !Array.isArray(channels)) {
      return NextResponse.json({ error: 'Invalid channels data' }, { status: 400 })
    }
    
    const results: ChannelTestResult[] = []
    
    // Testar cada canal
    for (const channel of channels) {
      logger.info(LOG_TAGS.IPTV, `Testing channel: ${channel.name}`, { channelId: channel.id })
      
      try {
        // Primeiro, buscar dados do canal
        const channelResponse = await fetch(`${request.nextUrl.origin}/api/iptv/channel/${channel.id}`)
        
        if (!channelResponse.ok) {
          results.push({
            channelId: channel.id,
            name: channel.name,
            status: 'error_404',
            errorDetails: `Channel API returned ${channelResponse.status}`,
            httpStatus: channelResponse.status
          })
          continue
        }
        
        const channelData = await channelResponse.json()
        
        if (!channelData.success || !channelData.data?.url) {
          results.push({
            channelId: channel.id,
            name: channel.name,
            status: 'error_404',
            errorDetails: 'No stream URL found',
            httpStatus: 404
          })
          continue
        }
        
        // Testar a URL do stream diretamente
        const streamUrl = channelData.data.url
        const testResponse = await fetch(streamUrl, {
          method: 'HEAD',
          headers: {
            'User-Agent': channelData.data.userAgent || 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            ...(channelData.data.headers || {})
          },
          signal: AbortSignal.timeout(5000)
        }).catch(err => {
          logger.error(LOG_TAGS.IPTV, 'Direct stream test failed', { 
            channelId: channel.id,
            error: err.message 
          })
          return null
        })
        
        if (!testResponse) {
          // Tentar com proxy
          const proxyUrl = `${request.nextUrl.origin}/api/advanced-proxy?url=${encodeURIComponent(streamUrl)}`
          const proxyResponse = await fetch(proxyUrl, {
            method: 'HEAD',
            signal: AbortSignal.timeout(5000)
          }).catch(err => {
            logger.error(LOG_TAGS.IPTV, 'Proxy test failed', { 
              channelId: channel.id,
              error: err.message 
            })
            return null
          })
          
          if (proxyResponse && proxyResponse.ok) {
            results.push({
              channelId: channel.id,
              name: channel.name,
              status: 'working',
              streamUrl,
              httpStatus: 200,
              headers: { 'Via': 'proxy' }
            })
          } else {
            results.push({
              channelId: channel.id,
              name: channel.name,
              status: 'error_cors',
              errorDetails: 'CORS/Network error, proxy also failed',
              streamUrl,
              httpStatus: proxyResponse?.status || 0
            })
          }
        } else if (testResponse.status === 403) {
          results.push({
            channelId: channel.id,
            name: channel.name,
            status: 'error_403',
            errorDetails: 'Forbidden - Geoblocked or requires auth',
            streamUrl,
            httpStatus: 403
          })
        } else if (testResponse.status === 404) {
          results.push({
            channelId: channel.id,
            name: channel.name,
            status: 'error_404',
            errorDetails: 'Stream not found',
            streamUrl,
            httpStatus: 404
          })
        } else if (testResponse.ok) {
          results.push({
            channelId: channel.id,
            name: channel.name,
            status: 'working',
            streamUrl,
            httpStatus: testResponse.status
          })
        } else {
          results.push({
            channelId: channel.id,
            name: channel.name,
            status: 'error_other',
            errorDetails: `HTTP ${testResponse.status}: ${testResponse.statusText}`,
            streamUrl,
            httpStatus: testResponse.status
          })
        }
        
      } catch (error) {
        logger.error(LOG_TAGS.IPTV, 'Channel test error', { 
          channelId: channel.id,
          error: error instanceof Error ? error.message : String(error)
        })
        
        results.push({
          channelId: channel.id,
          name: channel.name,
          status: 'error_other',
          errorDetails: error instanceof Error ? error.message : 'Unknown error'
        })
      }
    }
    
    // Resumo dos resultados
    const summary = {
      total: results.length,
      working: results.filter(r => r.status === 'working').length,
      error_404: results.filter(r => r.status === 'error_404').length,
      error_403: results.filter(r => r.status === 'error_403').length,
      error_cors: results.filter(r => r.status === 'error_cors').length,
      error_other: results.filter(r => r.status === 'error_other').length
    }
    
    logger.info(LOG_TAGS.IPTV, 'Channel test summary', summary)
    
    return NextResponse.json({
      success: true,
      summary,
      results
    })
    
  } catch (error) {
    logger.error(LOG_TAGS.IPTV, 'Test channels error', { error })
    return NextResponse.json({ 
      success: false, 
      error: error instanceof Error ? error.message : 'Test failed' 
    }, { status: 500 })
  }
}