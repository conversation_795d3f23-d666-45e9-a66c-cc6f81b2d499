import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { checkRateLimit, loginRateLimiter } from "@/lib/security/rate-limit";
import { validateCSRFMiddleware } from "@/lib/security/csrf";
import { audit } from "@/lib/security/audit";

// Schema de validação para atualização de perfil
const updateProfileSchema = z.object({
  action: z.enum(["update", "delete"]),
  data: z.object({
    name: z.string().min(1).max(100).trim().optional(),
    email: z.string().email().optional(),
    phone: z.string()
      .regex(/^\+?[1-9]\d{1,14}$/, "Número de telefone inválido")
      .optional(),
    bio: z.string().max(500).optional(),
    preferences: z.object({
      language: z.enum(["pt", "es", "en"]).optional(),
      notifications: z.boolean().optional(),
      theme: z.enum(["light", "dark", "auto"]).optional(),
    }).optional(),
  }),
});

export async function POST(request: NextRequest) {
  try {
    // 1. Rate limiting
    const rateLimitResult = await checkRateLimit(request);
    if (!rateLimitResult.success) {
      audit.rateLimitExceeded(
        request.headers.get('x-forwarded-for') || 'unknown',
        '/api/protected/profile',
        request.ip
      );
      
      return new NextResponse(
        JSON.stringify({ error: 'Too many requests. Please try again later.' }),
        { 
          status: 429,
          headers: {
            ...rateLimitResult.headers,
            'Content-Type': 'application/json',
          }
        }
      );
    }

    // 2. Verificar autenticação (mock por enquanto)
    const mockAuth = request.cookies.get('mock_auth');
    const userId = mockAuth?.value || null;
    
    if (!userId) {
      audit.accessDenied('anonymous', '/api/protected/profile', request.ip);
      
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // 3. Validar CSRF token
    const csrfValid = await validateCSRFMiddleware(request, userId);
    if (!csrfValid) {
      audit.suspiciousActivity(
        userId,
        'CSRF token validation failed',
        { path: '/api/protected/profile', ip: request.ip }
      );
      
      return NextResponse.json(
        { error: 'Invalid CSRF token' },
        { status: 403 }
      );
    }

    // 4. Validar entrada com Zod
    const body = await request.json();
    const validationResult = updateProfileSchema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          error: 'Validation failed',
          details: validationResult.error.flatten()
        },
        { status: 400 }
      );
    }

    const { action, data } = validationResult.data;

    // 5. Verificar permissões específicas
    if (action === "delete") {
      // Apenas admins podem deletar perfis (mock check)
      const isAdmin = false; // TODO: Implementar verificação real
      
      if (!isAdmin) {
        audit.accessDenied(userId, '/api/protected/profile (delete)', request.ip);
        
        return NextResponse.json(
          { error: 'Forbidden: Admin access required' },
          { status: 403 }
        );
      }
    }

    // 6. Log de auditoria da ação
    audit.dataModification(
      userId,
      action === 'delete' ? 'data_deleted' : 'data_updated',
      '/api/protected/profile',
      data
    );

    // 7. Processar requisição (mock)
    const result = await processProfileUpdate(userId, action, data);

    // 8. Resposta com headers de segurança
    return NextResponse.json(
      { 
        success: true,
        data: result,
        message: action === 'delete' 
          ? 'Profile deleted successfully' 
          : 'Profile updated successfully'
      },
      {
        headers: {
          'X-Content-Type-Options': 'nosniff',
          'Cache-Control': 'no-store, no-cache, must-revalidate',
        }
      }
    );

  } catch (error) {
    // Log de erro
    console.error('API Error:', error);
    
    // Não expor detalhes do erro em produção
    const errorMessage = process.env.NODE_ENV === 'development' 
      ? error instanceof Error ? error.message : 'Unknown error'
      : 'Internal server error';

    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    );
  }
}

// GET - Buscar perfil com validação
export async function GET(request: NextRequest) {
  try {
    // Rate limiting menos restritivo para GET
    const rateLimitResult = await checkRateLimit(request, undefined, 200);
    if (!rateLimitResult.success) {
      return new NextResponse(
        JSON.stringify({ error: 'Too many requests' }),
        { 
          status: 429,
          headers: rateLimitResult.headers
        }
      );
    }

    // Verificar autenticação
    const mockAuth = request.cookies.get('mock_auth');
    const userId = mockAuth?.value || null;
    
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Log de acesso
    audit.dataAccess(userId, '/api/protected/profile', 'GET');

    // Buscar dados do perfil (mock)
    const profile = {
      id: userId,
      name: 'Usuario Demo',
      email: '<EMAIL>',
      phone: '+5511999999999',
      bio: 'Amante de deportes y streaming',
      preferences: {
        language: 'es',
        notifications: true,
        theme: 'dark',
      },
      createdAt: new Date().toISOString(),
    };

    return NextResponse.json(
      { success: true, data: profile },
      {
        headers: {
          'Cache-Control': 'private, max-age=0',
        }
      }
    );

  } catch (error) {
    console.error('API Error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Função mock para processar atualização
async function processProfileUpdate(
  userId: string,
  action: string,
  data: any
): Promise<any> {
  // Simular processamento
  await new Promise(resolve => setTimeout(resolve, 100));
  
  if (action === 'delete') {
    return { deleted: true, userId };
  }
  
  return {
    id: userId,
    ...data,
    updatedAt: new Date().toISOString(),
  };
}