import { NextResponse } from 'next/server'
import { createServerDatabaseClient } from '@/lib/supabase/database-only'
import jwt from 'jsonwebtoken'

export async function POST(request: Request) {
  try {
    const { token } = await request.json()
    
    if (!token) {
      return NextResponse.json(
        { error: 'Token não fornecido' },
        { status: 400 }
      )
    }
    
    // Verificar e decodificar JWT
    const jwtSecret = process.env.JWT_SECRET || 'default-secret-change-in-production'
    let decoded: { tokenId: string; userId: string }
    
    try {
      decoded = jwt.verify(token, jwtSecret)
    } catch (jwtError) {
      return NextResponse.json(
        { error: 'Token inválido ou expirado' },
        { status: 401 }
      )
    }
    
    const supabase = await createServerDatabaseClient()
    
    // Verificar se o token existe e não foi usado
    const { data: tokenData, error: tokenError } = await supabase
      .from('app_verification_tokens')
      .select('*')
      .eq('token', decoded.tokenId)
      .eq('user_id', decoded.userId)
      .eq('used', false)
      .single()
    
    if (tokenError || !tokenData) {
      return NextResponse.json(
        { error: 'Token não encontrado ou já utilizado' },
        { status: 404 }
      )
    }
    
    // Verificar se o token não expirou
    if (new Date(tokenData.expires_at) < new Date()) {
      return NextResponse.json(
        { error: 'Token expirado' },
        { status: 401 }
      )
    }
    
    // Marcar token como usado
    const { error: updateTokenError } = await supabase
      .from('app_verification_tokens')
      .update({ 
        used: true, 
        used_at: new Date().toISOString() 
      })
      .eq('id', tokenData.id)
    
    if (updateTokenError) {
      console.error('Erro ao atualizar token:', updateTokenError)
      return NextResponse.json(
        { error: 'Erro ao processar token' },
        { status: 500 }
      )
    }
    
    // Atualizar perfil do usuário com 1 mês grátis
    const expirationDate = new Date()
    expirationDate.setMonth(expirationDate.getMonth() + 1)
    
    const { error: profileError } = await supabase
      .from('profiles')
      .update({
        subscription_tier: 'premium',
        subscription_expires_at: expirationDate.toISOString(),
        trial_used: true
      })
      .eq('id', decoded.userId)
    
    if (profileError) {
      console.error('Erro ao atualizar perfil:', profileError)
      return NextResponse.json(
        { error: 'Erro ao ativar assinatura' },
        { status: 500 }
      )
    }
    
    return NextResponse.json({
      success: true,
      message: 'Assinatura premium ativada com sucesso!',
      expiresAt: expirationDate.toISOString()
    })
    
  } catch (error) {
    console.error('Erro ao verificar token:', error)
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}