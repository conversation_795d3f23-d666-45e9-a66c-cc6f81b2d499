import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { cookies } from 'next/headers'

export async function GET(request: NextRequest) {
  try {
    const cookieStore = await cookies()
    const supabase = createClient()
    
    const { data: { user }, error } = await supabase.auth.getUser()
    
    if (error || !user) {
      return NextResponse.json({ 
        error: 'Usuário não autenticado',
        authenticated: false 
      })
    }
    
    return NextResponse.json({
      authenticated: true,
      id: user.id,
      email: user.email,
      metadata: user.user_metadata
    })
    
  } catch (error) {
    return NextResponse.json({ 
      error: 'Erro ao buscar dados do usuário'
    }, { status: 500 })
  }
}