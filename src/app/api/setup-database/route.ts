import { NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

export async function POST(request: Request) {
  try {
    // Verificar autorização básica
    const authHeader = request.headers.get('authorization')
    if (authHeader !== `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    const migrations = [
      // Enable extensions
      `CREATE EXTENSION IF NOT EXISTS "uuid-ossp"`,
      `CREATE EXTENSION IF NOT EXISTS "pgcrypto"`,
      
      // Create types
      `CREATE TYPE channel_category AS ENUM ('sports', 'esports', 'entertainment', 'news', 'kids')`,
      `CREATE TYPE subscription_tier AS ENUM ('free', 'basic', 'premium', 'vip')`,
      `CREATE TYPE content_rating AS ENUM ('G', 'PG', 'PG-13', 'R', 'NC-17')`,
      
      // Create profiles table
      `CREATE TABLE IF NOT EXISTS profiles (
        id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
        username TEXT UNIQUE,
        full_name TEXT,
        avatar_url TEXT,
        subscription_tier subscription_tier DEFAULT 'free',
        subscription_expires_at TIMESTAMPTZ,
        trial_used BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMPTZ DEFAULT NOW(),
        updated_at TIMESTAMPTZ DEFAULT NOW()
      )`,
      
      // Create channels table
      `CREATE TABLE IF NOT EXISTS channels (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        name TEXT NOT NULL,
        slug TEXT UNIQUE NOT NULL,
        description TEXT,
        logo_url TEXT,
        banner_url TEXT,
        stream_url TEXT NOT NULL,
        fallback_urls TEXT[] DEFAULT '{}',
        category channel_category NOT NULL,
        is_premium BOOLEAN DEFAULT FALSE,
        required_tier subscription_tier DEFAULT 'free',
        is_active BOOLEAN DEFAULT TRUE,
        viewer_count INTEGER DEFAULT 0,
        metadata JSONB DEFAULT '{}',
        created_at TIMESTAMPTZ DEFAULT NOW(),
        updated_at TIMESTAMPTZ DEFAULT NOW()
      )`,
      
      // Create viewing_sessions table
      `CREATE TABLE IF NOT EXISTS viewing_sessions (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
        channel_id UUID REFERENCES channels(id) ON DELETE CASCADE,
        started_at TIMESTAMPTZ DEFAULT NOW(),
        ended_at TIMESTAMPTZ,
        duration_seconds INTEGER,
        quality TEXT,
        ip_address INET,
        user_agent TEXT,
        created_at TIMESTAMPTZ DEFAULT NOW()
      )`,
      
      // Create app_verification_tokens table
      `CREATE TABLE IF NOT EXISTS app_verification_tokens (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
        token TEXT UNIQUE NOT NULL,
        used BOOLEAN DEFAULT FALSE,
        used_at TIMESTAMPTZ,
        expires_at TIMESTAMPTZ NOT NULL,
        platform TEXT CHECK (platform IN ('ios', 'android')),
        created_at TIMESTAMPTZ DEFAULT NOW()
      )`,
      
      // Create favorites table
      `CREATE TABLE IF NOT EXISTS favorites (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
        channel_id UUID REFERENCES channels(id) ON DELETE CASCADE,
        created_at TIMESTAMPTZ DEFAULT NOW(),
        UNIQUE(user_id, channel_id)
      )`,
      
      // Create channel_schedule table
      `CREATE TABLE IF NOT EXISTS channel_schedule (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        channel_id UUID REFERENCES channels(id) ON DELETE CASCADE,
        title TEXT NOT NULL,
        description TEXT,
        starts_at TIMESTAMPTZ NOT NULL,
        ends_at TIMESTAMPTZ NOT NULL,
        is_live BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMPTZ DEFAULT NOW(),
        updated_at TIMESTAMPTZ DEFAULT NOW()
      )`,
      
      // Create indexes
      `CREATE INDEX IF NOT EXISTS idx_channels_category ON channels(category)`,
      `CREATE INDEX IF NOT EXISTS idx_channels_is_active ON channels(is_active)`,
      `CREATE INDEX IF NOT EXISTS idx_channels_slug ON channels(slug)`,
      `CREATE INDEX IF NOT EXISTS idx_viewing_sessions_user_id ON viewing_sessions(user_id)`,
      `CREATE INDEX IF NOT EXISTS idx_viewing_sessions_channel_id ON viewing_sessions(channel_id)`,
      `CREATE INDEX IF NOT EXISTS idx_viewing_sessions_started_at ON viewing_sessions(started_at)`,
      `CREATE INDEX IF NOT EXISTS idx_app_verification_tokens_token ON app_verification_tokens(token)`,
      `CREATE INDEX IF NOT EXISTS idx_app_verification_tokens_expires_at ON app_verification_tokens(expires_at)`,
      `CREATE INDEX IF NOT EXISTS idx_favorites_user_id ON favorites(user_id)`,
      `CREATE INDEX IF NOT EXISTS idx_channel_schedule_channel_id ON channel_schedule(channel_id)`,
      `CREATE INDEX IF NOT EXISTS idx_channel_schedule_starts_at ON channel_schedule(starts_at)`,
      
      // Create update timestamp function
      `CREATE OR REPLACE FUNCTION update_updated_at_column()
      RETURNS TRIGGER AS $$
      BEGIN
          NEW.updated_at = NOW();
          RETURN NEW;
      END;
      $$ language 'plpgsql'`,
      
      // Create triggers
      `DROP TRIGGER IF EXISTS update_profiles_updated_at ON profiles`,
      `CREATE TRIGGER update_profiles_updated_at BEFORE UPDATE ON profiles
        FOR EACH ROW EXECUTE FUNCTION update_updated_at_column()`,
      
      `DROP TRIGGER IF EXISTS update_channels_updated_at ON channels`,
      `CREATE TRIGGER update_channels_updated_at BEFORE UPDATE ON channels
        FOR EACH ROW EXECUTE FUNCTION update_updated_at_column()`,
      
      `DROP TRIGGER IF EXISTS update_channel_schedule_updated_at ON channel_schedule`,
      `CREATE TRIGGER update_channel_schedule_updated_at BEFORE UPDATE ON channel_schedule
        FOR EACH ROW EXECUTE FUNCTION update_updated_at_column()`,
      
      // Enable RLS
      `ALTER TABLE profiles ENABLE ROW LEVEL SECURITY`,
      `ALTER TABLE channels ENABLE ROW LEVEL SECURITY`,
      `ALTER TABLE viewing_sessions ENABLE ROW LEVEL SECURITY`,
      `ALTER TABLE app_verification_tokens ENABLE ROW LEVEL SECURITY`,
      `ALTER TABLE favorites ENABLE ROW LEVEL SECURITY`,
      `ALTER TABLE channel_schedule ENABLE ROW LEVEL SECURITY`,
    ]

    const results = []
    for (let i = 0; i < migrations.length; i++) {
      try {
        const { data, error } = await supabase.rpc('exec_sql', {
          query: migrations[i]
        })
        
        if (error) throw error
        
        results.push({
          index: i,
          statement: migrations[i].substring(0, 50) + '...',
          success: true
        })
      } catch (error: unknown) {
        results.push({
          index: i,
          statement: migrations[i].substring(0, 50) + '...',
          success: false,
          error: error instanceof Error ? error.message : 'An unknown error occurred'
        })
      }
    }

    // Insert demo channels
    const demoChannels = [
      {
        name: 'LaLiga TV',
        slug: 'laliga-tv',
        description: 'Todos los partidos de LaLiga en directo',
        logo_url: '/logos/laliga.png',
        stream_url: 'https://test-streams.mux.dev/x36xhzz/x36xhzz.m3u8',
        category: 'sports',
        is_premium: true,
        required_tier: 'premium'
      },
      {
        name: 'Champions League',
        slug: 'champions-league',
        description: 'Los mejores partidos de Europa',
        logo_url: '/logos/champions.png',
        stream_url: 'https://test-streams.mux.dev/x36xhzz/x36xhzz.m3u8',
        category: 'sports',
        is_premium: true,
        required_tier: 'premium'
      },
      {
        name: 'Formula 1',
        slug: 'formula-1',
        description: 'Todas las carreras de F1 en directo',
        logo_url: '/logos/f1.png',
        stream_url: 'https://test-streams.mux.dev/x36xhzz/x36xhzz.m3u8',
        category: 'sports',
        is_premium: true,
        required_tier: 'vip'
      },
      {
        name: 'ESPN Deportes',
        slug: 'espn-deportes',
        description: 'Lo mejor del deporte internacional',
        logo_url: '/logos/espn.png',
        stream_url: 'https://test-streams.mux.dev/x36xhzz/x36xhzz.m3u8',
        category: 'sports',
        is_premium: false,
        required_tier: 'basic'
      },
      {
        name: 'eSports Arena',
        slug: 'esports-arena',
        description: 'Torneos de League of Legends, CS:GO y más',
        logo_url: '/logos/esports.png',
        stream_url: 'https://test-streams.mux.dev/x36xhzz/x36xhzz.m3u8',
        category: 'esports',
        is_premium: false,
        required_tier: 'free'
      }
    ]

    const { error: channelsError } = await supabase
      .from('channels')
      .upsert(demoChannels, { onConflict: 'slug' })

    if (channelsError) {
      results.push({
        statement: 'Insert demo channels',
        success: false,
        error: channelsError.message
      })
    } else {
      results.push({
        statement: 'Insert demo channels',
        success: true
      })
    }

    // Create RLS policies
    const policies = [
      // Profiles policies
      {
        table: 'profiles',
        name: 'Public profiles are viewable by everyone',
        definition: 'FOR SELECT USING (true)'
      },
      {
        table: 'profiles',
        name: 'Users can update own profile',
        definition: 'FOR UPDATE USING ((SELECT auth.uid()) = id)'
      },
      {
        table: 'profiles',
        name: 'Users can insert own profile',
        definition: 'FOR INSERT WITH CHECK ((SELECT auth.uid()) = id)'
      },
      // Channels policies
      {
        table: 'channels',
        name: 'Channels are viewable by everyone',
        definition: 'FOR SELECT USING (is_active = true)'
      },
      // Viewing sessions policies
      {
        table: 'viewing_sessions',
        name: 'Users can view own sessions',
        definition: 'FOR SELECT USING ((SELECT auth.uid()) = user_id)'
      },
      {
        table: 'viewing_sessions',
        name: 'Users can create own sessions',
        definition: 'FOR INSERT WITH CHECK ((SELECT auth.uid()) = user_id)'
      },
      {
        table: 'viewing_sessions',
        name: 'Users can update own sessions',
        definition: 'FOR UPDATE USING ((SELECT auth.uid()) = user_id)'
      },
      // Favorites policies
      {
        table: 'favorites',
        name: 'Users can view own favorites',
        definition: 'FOR SELECT USING ((SELECT auth.uid()) = user_id)'
      },
      {
        table: 'favorites',
        name: 'Users can create own favorites',
        definition: 'FOR INSERT WITH CHECK ((SELECT auth.uid()) = user_id)'
      },
      {
        table: 'favorites',
        name: 'Users can delete own favorites',
        definition: 'FOR DELETE USING ((SELECT auth.uid()) = user_id)'
      },
      // Channel schedule policies
      {
        table: 'channel_schedule',
        name: 'Schedule is viewable by everyone',
        definition: 'FOR SELECT USING (true)'
      }
    ]

    for (const policy of policies) {
      try {
        const policySQL = `CREATE POLICY "${policy.name}" ON ${policy.table} ${policy.definition}`
        await supabase.rpc('exec_sql', { query: policySQL })
        results.push({
          statement: `Create policy: ${policy.name}`,
          success: true
        })
      } catch (error: unknown) {
        results.push({
          statement: `Create policy: ${policy.name}`,
          success: false,
          error: error instanceof Error ? error.message : 'An unknown error occurred'
        })
      }
    }

    return NextResponse.json({
      message: 'Database setup completed',
      results
    })

  } catch (error: unknown) {
    return NextResponse.json(
      { error: 'Setup failed', details: error instanceof Error ? error.message : 'An unknown error occurred' },
      { status: 500 }
    )
  }
}