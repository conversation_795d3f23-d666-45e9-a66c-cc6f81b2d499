import { NextRequest, NextResponse } from 'next/server'
import { createServiceClient } from '@/lib/supabase/server'

export async function POST(request: NextRequest) {
  const timestamp = new Date().toISOString()
  console.log(`[CHECK-ACCESS API] ${timestamp} - ===== REQUEST RECEIVED =====`)
  
  try {
    const body = await request.json()
    const { userId } = body
    
    console.log(`[CHECK-ACCESS API] ${timestamp} - User ID:`, userId)
    
    if (!userId) {
      console.log(`[CHECK-ACCESS API] ${timestamp} - ❌ No userId provided`)
      return NextResponse.json({ 
        error: 'userId is required',
        has_access: false 
      }, { status: 400 })
    }
    
    const supabase = createServiceClient()
    
    // Chamar a função RPC check_user_access_unified (aceita string ID)
    console.log(`[CHECK-ACCESS API] ${timestamp} - Calling RPC check_user_access_unified...`)
    const { data, error } = await supabase
      .rpc('check_user_access_unified', { p_user_id: userId })
      .single()
    
    if (error) {
      console.error(`[CHECK-ACCESS API] ${timestamp} - ❌ RPC Error:`, error)
      return NextResponse.json({ 
        error: 'Failed to check access',
        details: error.message,
        has_access: false 
      }, { status: 500 })
    }
    
    console.log(`[CHECK-ACCESS API] ${timestamp} - ✅ RPC Success:`)
    console.log(`[CHECK-ACCESS API] ${timestamp} -   has_access:`, data?.has_access)
    console.log(`[CHECK-ACCESS API] ${timestamp} -   access_type:`, data?.access_type)
    console.log(`[CHECK-ACCESS API] ${timestamp} -   expires_at:`, data?.expires_at)
    
    // Retornar no formato esperado pelo frontend
    return NextResponse.json({
      has_access: data?.has_access || false,
      access_type: data?.access_type || null,
      expires_at: data?.expires_at || null,
      userId
    })
    
  } catch (error) {
    console.error(`[CHECK-ACCESS API] ${timestamp} - ❌ Unexpected error:`, error)
    return NextResponse.json({ 
      error: 'Internal server error',
      has_access: false 
    }, { status: 500 })
  }
}