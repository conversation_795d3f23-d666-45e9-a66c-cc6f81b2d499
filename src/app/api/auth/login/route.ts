import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { loginRateLimiter } from "@/lib/security/rate-limit";
import { generateCSRFToken } from "@/lib/security/csrf";
import { audit } from "@/lib/security/audit";
import { cookies } from "next/headers";

// Schema de validação para login
const loginSchema = z.object({
  email: z.string()
    .email("Email inválido")
    .toLowerCase()
    .trim(),
  password: z.string()
    .min(6, "Senha deve ter no mínimo 6 caracteres")
    .max(100, "Senha muito longa"),
  rememberMe: z.boolean().optional().default(false),
});

// Função para hash de senha (mock - em produção usar bcrypt)
function verifyPassword(password: string, hash: string): boolean {
  // TODO: Implementar verificação real com bcrypt
  // TODO: Implementar verificação real com bcrypt
  return false; // Sempre retorna falso para forçar uso de OAuth
}

export async function POST(request: NextRequest) {
  const ipAddress = request.headers.get('x-forwarded-for') || 
                   request.headers.get('x-real-ip') || 
                   request.ip || 
                   'unknown';

  try {
    // 1. Rate limiting específico para login (mais restritivo)
    const rateLimitResult = await loginRateLimiter.check(ipAddress);
    
    if (!rateLimitResult.success) {
      audit.rateLimitExceeded(ipAddress, '/api/auth/login', ipAddress);
      
      return NextResponse.json(
        { 
          error: 'Muitas tentativas de login. Tente novamente em 15 minutos.',
          retryAfter: rateLimitResult.resetAt.toISOString(),
        },
        { 
          status: 429,
          headers: {
            'Retry-After': String(Math.ceil((rateLimitResult.resetAt.getTime() - Date.now()) / 1000)),
          }
        }
      );
    }

    // 2. Validar entrada com Zod
    const body = await request.json();
    const validationResult = loginSchema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          error: 'Dados inválidos',
          details: validationResult.error.flatten()
        },
        { status: 400 }
      );
    }

    const { email, password, rememberMe } = validationResult.data;

    // 3. Simular busca de usuário no banco
    const mockUser = {
      id: 'user123',
      email: '<EMAIL>',
      passwordHash: 'hashed_demo123',
      isActive: true,
      emailVerified: true,
    };

    // 4. Verificar se usuário existe e está ativo
    if (!mockUser || mockUser.email !== email) {
      audit.loginFailed(email, ipAddress, 'User not found');
      
      // Resposta genérica para não revelar se o email existe
      return NextResponse.json(
        { error: 'Email ou senha incorretos' },
        { status: 401 }
      );
    }

    if (!mockUser.isActive) {
      audit.loginFailed(email, ipAddress, 'Account inactive');
      
      return NextResponse.json(
        { error: 'Conta desativada. Entre em contato com o suporte.' },
        { status: 403 }
      );
    }

    // 5. Verificar senha
    const passwordValid = verifyPassword(password, mockUser.passwordHash);
    
    if (!passwordValid) {
      audit.loginFailed(email, ipAddress, 'Invalid password');
      
      return NextResponse.json(
        { error: 'Email ou senha incorretos' },
        { status: 401 }
      );
    }

    // 6. Verificar se email foi verificado
    if (!mockUser.emailVerified) {
      return NextResponse.json(
        { 
          error: 'Email não verificado',
          code: 'EMAIL_NOT_VERIFIED'
        },
        { status: 403 }
      );
    }

    // 7. Login bem-sucedido - criar sessão
    const sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const sessionExpiry = rememberMe 
      ? 30 * 24 * 60 * 60 * 1000 // 30 dias
      : 24 * 60 * 60 * 1000;      // 24 horas

    // 8. Gerar novo token CSRF para a sessão
    const csrfToken = generateCSRFToken(mockUser.id);

    // 9. Configurar cookies seguros
    const cookieStore = await cookies();
    
    // Cookie de autenticação (temporário - usar Stack Auth em produção)
    cookieStore.set('mock_auth', mockUser.id, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: sessionExpiry / 1000,
      path: '/',
    });

    // Cookie de sessão
    cookieStore.set('session_id', sessionId, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: sessionExpiry / 1000,
      path: '/',
    });

    // 10. Log de auditoria de sucesso
    audit.login(mockUser.id, ipAddress, {
      userAgent: request.headers.get('user-agent'),
      rememberMe,
    });

    // 11. Resposta de sucesso
    return NextResponse.json(
      {
        success: true,
        user: {
          id: mockUser.id,
          email: mockUser.email,
        },
        csrfToken,
        expiresAt: new Date(Date.now() + sessionExpiry).toISOString(),
      },
      {
        headers: {
          'X-CSRF-Token': csrfToken,
        }
      }
    );

  } catch (error) {
    // Log de erro sem expor detalhes
    console.error('Login error:', error);
    
    return NextResponse.json(
      { error: 'Erro ao processar login' },
      { status: 500 }
    );
  }
}

// GET - Verificar status de autenticação
export async function GET(request: NextRequest) {
  try {
    const cookieStore = await cookies();
    const authCookie = cookieStore.get('mock_auth');
    const sessionCookie = cookieStore.get('session_id');
    
    if (!authCookie || !sessionCookie) {
      return NextResponse.json(
        { authenticated: false },
        { status: 200 }
      );
    }

    // Gerar novo token CSRF para requisições subsequentes
    const csrfToken = generateCSRFToken(authCookie.value);

    return NextResponse.json(
      {
        authenticated: true,
        user: {
          id: authCookie.value,
        },
        csrfToken,
      },
      {
        headers: {
          'X-CSRF-Token': csrfToken,
          'Cache-Control': 'no-store, no-cache, must-revalidate',
        }
      }
    );

  } catch (error) {
    console.error('Auth check error:', error);
    return NextResponse.json(
      { authenticated: false },
      { status: 200 }
    );
  }
}