import { NextRequest, NextResponse } from 'next/server';
import { createDatabaseClient } from '@/lib/supabase/database-only';
import { cookies } from 'next/headers';

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const code = searchParams.get('code');
    const state = searchParams.get('state');
    const error = searchParams.get('error');
    
    console.log('🔐 Auth Callback:', { code: !!code, state: !!state, error });
    
    // Se houver erro, redirecionar para login com mensagem
    if (error) {
      return NextResponse.redirect(new URL(`/login?error=${error}`, request.url));
    }
    
    // Se não houver código, erro
    if (!code) {
      return NextResponse.redirect(new URL('/login?error=no_code', request.url));
    }
    
    // Processar o código OAuth usando Supabase
    const supabase = createDatabaseClient();
    const { data, error: authError } = await supabase.auth.exchangeCodeForSession(code);
    
    if (authError || !data.session) {
      console.error('❌ Auth error:', authError);
      return NextResponse.redirect(new URL('/login?error=auth_failed', request.url));
    }
    
    // Criar sessão simples
    const cookieStore = await cookies();
    cookieStore.set('auth-session', JSON.stringify({
      id: data.session.user.id,
      email: data.session.user.email,
      loggedAt: new Date().toISOString()
    }), {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 60 * 60 * 24 * 30 // 30 dias
    });
    
    console.log('✅ Login successful:', data.session.user.email);
    
    // Redirecionar para a página inicial ou destino original
    const redirectTo = state || '/';
    return NextResponse.redirect(new URL(redirectTo, request.url));
    
  } catch (error) {
    console.error('❌ Callback error:', error);
    return NextResponse.redirect(new URL('/login?error=internal_error', request.url));
  }
}