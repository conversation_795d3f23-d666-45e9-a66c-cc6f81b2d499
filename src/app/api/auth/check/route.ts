import { NextResponse } from "next/server";
import { cookies } from "next/headers";
import { StackServerApp } from "@stackframe/stack";

export async function GET() {
  try {
    console.log('🔍 [AUTH CHECK] Verificando autenticação...');
    
    // Verificar variáveis de ambiente
    const envCheck = {
      projectId: !!process.env.NEXT_PUBLIC_STACK_PROJECT_ID,
      publishableKey: !!process.env.NEXT_PUBLIC_STACK_PUBLISHABLE_CLIENT_KEY,
      secretKey: !!process.env.STACK_SECRET_SERVER_KEY,
    };
    
    console.log('📋 [AUTH CHECK] Variáveis de ambiente:', envCheck);
    
    if (!envCheck.projectId || !envCheck.publishableKey || !envCheck.secretKey) {
      return NextResponse.json({
        authenticated: false,
        error: 'Stack Auth não configurado',
        envCheck
      }, { status: 503 });
    }

    // Verificar cookies
    const cookieStore = await cookies();
    const allCookies = cookieStore.getAll();
    
    const stackCookies = allCookies.filter(cookie => 
      cookie.name.includes('stack-') || 
      cookie.name.includes('auth') ||
      cookie.name.includes('access-token')
    );
    
    console.log('🍪 [AUTH CHECK] Cookies Stack Auth:', stackCookies.map(c => c.name));

    // Verificar usuário via Stack Auth
    try {
      const stackApp = new StackServerApp({
        tokenStore: "cookie",
      });
      
      const user = await stackApp.getUser();
      
      if (user) {
        console.log('✅ [AUTH CHECK] Usuário autenticado:', user.id);
        return NextResponse.json({
          authenticated: true,
          user: {
            id: user.id,
            email: user.primaryEmail,
            emailVerified: user.primaryEmailVerified,
          },
          cookies: stackCookies.map(c => c.name),
          envCheck
        });
      } else {
        console.log('❌ [AUTH CHECK] Nenhum usuário autenticado');
        return NextResponse.json({
          authenticated: false,
          message: 'Não autenticado',
          cookies: stackCookies.map(c => c.name),
          envCheck
        });
      }
    } catch (error: any) {
      console.error('❌ [AUTH CHECK] Erro ao verificar usuário:', error);
      return NextResponse.json({
        authenticated: false,
        error: error.message,
        cookies: stackCookies.map(c => c.name),
        envCheck
      });
    }
    
  } catch (error: any) {
    console.error('❌ [AUTH CHECK] Erro geral:', error);
    return NextResponse.json({
      authenticated: false,
      error: error.message
    }, { status: 500 });
  }
}