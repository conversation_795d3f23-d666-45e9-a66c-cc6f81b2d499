import { NextRequest, NextResponse } from "next/server";
import { cookies } from "next/headers";

export async function POST(request: NextRequest) {
  try {
    console.log('🚪 [logout] Removendo cookies...');
    
    const cookieStore = await cookies();
    
    // Remover cookie temporário
    cookieStore.delete('streamplus-temp-user');
    
    // Remover cookies do Stack Auth se existirem
    const allCookies = cookieStore.getAll();
    allCookies.forEach(cookie => {
      if (cookie.name.includes('stack-') || 
          cookie.name.includes('auth-session') ||
          cookie.name.includes('access-token')) {
        cookieStore.delete(cookie.name);
      }
    });
    
    console.log('✅ [logout] Cookies removidos');
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('❌ [logout] Erro:', error);
    return NextResponse.json(
      { error: 'Failed to logout' },
      { status: 500 }
    );
  }
}