import { NextRequest, NextResponse } from 'next/server';
import { stackServerApp } from '@/lib/auth/stack-server';

export async function GET(request: NextRequest) {
  console.log('\n🔍 [Check Session] Verificando sessão...');
  
  // Listar todos os cookies
  const cookies = request.cookies.getAll();
  console.log('🍪 [Check Session] Cookies encontrados:', 
    cookies.map(c => ({ name: c.name, hasValue: !!c.value }))
  );
  
  try {
    // Tentar obter usuário do Stack Auth
    const user = await stackServerApp.getUser();
    
    if (user) {
      console.log('✅ [Check Session] Usuário Stack Auth encontrado:', {
        id: user.id,
        email: user.primaryEmail,
        displayName: user.displayName
      });
      
      return NextResponse.json({
        success: true,
        provider: 'stack-auth',
        user: {
          id: user.id,
          email: user.primaryEmail,
          displayName: user.displayName,
          hasPassword: user.hasPassword,
          oauthProviders: user.oauthProviders
        }
      });
    }
  } catch (error) {
    console.log('⚠️ [Check Session] Erro ao verificar Stack Auth:', error);
  }
  
  // Verificar usuário de teste
  const testUserCookie = cookies.find(c => c.name === 'streamplus-test-user');
  if (testUserCookie) {
    try {
      const testUser = JSON.parse(testUserCookie.value);
      console.log('✅ [Check Session] Usuário de teste encontrado:', testUser.email);
      
      return NextResponse.json({
        success: true,
        provider: 'test-user',
        user: testUser
      });
    } catch (error) {
      console.log('⚠️ [Check Session] Erro ao decodificar usuário de teste:', error);
    }
  }
  
  console.log('❌ [Check Session] Nenhuma sessão encontrada');
  return NextResponse.json({
    success: false,
    message: 'No session found',
    availableCookies: cookies.map(c => c.name)
  });
}