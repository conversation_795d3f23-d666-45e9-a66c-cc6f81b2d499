import { NextResponse } from 'next/server';
import { createDatabaseClient } from '@/lib/supabase/database-only';

export async function POST(request: Request) {
  try {
    const supabase = createDatabaseClient();
    
    // Get the current user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      return NextResponse.json({ error: 'No authenticated user' }, { status: 401 });
    }
    
    // Check if profile exists
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', user.id)
      .single();
    
    if (profileError && profileError.code !== 'PGRST116') {
      // Error other than "not found"
      console.error('Error checking profile:', profileError);
      return NextResponse.json({ error: 'Error checking profile' }, { status: 500 });
    }
    
    // If profile doesn't exist, create it
    if (!profile) {
      const username = user.email?.split('@')[0] || 'user';
      const fullName = user.user_metadata?.full_name || user.email?.split('@')[0] || 'Usuario';
      
      const { data: newProfile, error: insertError } = await supabase
        .from('profiles')
        .insert({
          id: user.id,
          username: username,
          full_name: fullName,
          subscription_tier: 'free',
          trial_used: false
        })
        .select()
        .single();
      
      if (insertError) {
        console.error('Error creating profile:', insertError);
        return NextResponse.json({ error: 'Error creating profile' }, { status: 500 });
      }
      
      return NextResponse.json({ 
        message: 'Profile created successfully',
        profile: newProfile 
      });
    }
    
    return NextResponse.json({ 
      message: 'Profile already exists',
      profile 
    });
    
  } catch (error) {
    console.error('Sync profile error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}