import { NextRequest, NextResponse } from 'next/server'
import { logger } from '@/lib/logger'
import { LOG_TAGS } from '@/config/debug'
import { getChannelHeaders, isGeoRestricted } from '@/lib/channel-fixes'

// Cache para tokens e cookies de autenticação
const authCache = new Map<string, { cookies: string; expires: number }>()

// User agents otimizados para diferentes serviços
const USER_AGENTS = {
  default: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  mobile: 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1',
  vlc: 'VLC/3.0.16 LibVLC/3.0.16',
  ffmpeg: 'Lavf/58.76.100',
  android: 'Dalvik/2.1.0 (Linux; U; Android 13; SM-S908B Build/TP1A.220624.014)'
}

// Headers específicos por domínio
const DOMAIN_HEADERS: Record<string, Record<string, string>> = {
  'vo-live-media.cdb.cdn.orange.com': {
    'Origin': 'https://www.cdb.com',
    'Referer': 'https://www.cdb.com/',
    'Sec-Fetch-Dest': 'empty',
    'Sec-Fetch-Mode': 'cors',
    'Sec-Fetch-Site': 'same-site'
  },
  'liveakgr.alkassdigital.net': {
    'Origin': 'https://www.alkassdigital.net',
    'Referer': 'https://www.alkassdigital.net/',
  },
  'raycom-accdn-firetv.amagi.tv': {
    'X-Forwarded-For': generateRandomIP(),
    'X-Real-IP': generateRandomIP()
  }
}

function generateRandomIP(): string {
  // Gerar IP dos EUA para contornar geoblocking
  const usRanges = [
    { start: [3, 0, 0, 0], end: [3, 255, 255, 255] },    // General Electric
    { start: [4, 0, 0, 0], end: [4, 255, 255, 255] },    // Level 3
    { start: [8, 0, 0, 0], end: [8, 255, 255, 255] },    // IBM
    { start: [12, 0, 0, 0], end: [12, 255, 255, 255] },  // AT&T
    { start: [15, 0, 0, 0], end: [15, 255, 255, 255] },  // HP
    { start: [17, 0, 0, 0], end: [17, 255, 255, 255] },  // Apple
  ]
  
  const range = usRanges[Math.floor(Math.random() * usRanges.length)]
  return `${range.start[0]}.${Math.floor(Math.random() * 256)}.${Math.floor(Math.random() * 256)}.${Math.floor(Math.random() * 256)}`
}

function rewriteM3U8Content(content: string, baseUrl: string, proxyBaseUrl: string): string {
  const lines = content.split('\n')
  const rewritten = lines.map(line => {
    const trimmed = line.trim()
    
    // Skip comments and empty lines
    if (!trimmed || trimmed.startsWith('#')) {
      // Mas reescrever URLs em tags EXT-X-KEY
      if (trimmed.startsWith('#EXT-X-KEY') && trimmed.includes('URI=')) {
        return trimmed.replace(/URI="([^"]+)"/g, (match, url) => {
          const absoluteUrl = makeAbsoluteUrl(url, baseUrl)
          return `URI="${proxyBaseUrl}?url=${encodeURIComponent(absoluteUrl)}"`
        })
      }
      return line
    }
    
    // This is a URL line
    const absoluteUrl = makeAbsoluteUrl(trimmed, baseUrl)
    return `${proxyBaseUrl}?url=${encodeURIComponent(absoluteUrl)}`
  })
  
  return rewritten.join('\n')
}

function makeAbsoluteUrl(url: string, baseUrl: string): string {
  if (url.startsWith('http://') || url.startsWith('https://')) {
    return url
  } else if (url.startsWith('/')) {
    const urlObj = new URL(baseUrl)
    return `${urlObj.protocol}//${urlObj.host}${url}`
  } else {
    const baseWithoutFile = baseUrl.substring(0, baseUrl.lastIndexOf('/'))
    return `${baseWithoutFile}/${url}`
  }
}

async function handleAuthChallenge(url: string, response: Response): Promise<Response | null> {
  const wwwAuth = response.headers.get('www-authenticate')
  
  if (!wwwAuth) return null
  
  logger.info(LOG_TAGS.HTTP, 'Auth challenge detected', { url, wwwAuth })
  
  // Tentar obter cookies/tokens da página original
  try {
    const domain = new URL(url).hostname
    const cached = authCache.get(domain)
    
    if (cached && cached.expires > Date.now()) {
      // Usar cookies cached
      const authResponse = await fetch(url, {
        headers: {
          'Cookie': cached.cookies,
          'User-Agent': USER_AGENTS.default,
        }
      })
      
      if (authResponse.ok) {
        return authResponse
      }
    }
    
    // Tentar obter novos cookies
    const baseUrl = new URL(url).origin
    const authPageResponse = await fetch(baseUrl, {
      headers: {
        'User-Agent': USER_AGENTS.default,
      }
    })
    
    const cookies = authPageResponse.headers.get('set-cookie')
    if (cookies) {
      authCache.set(domain, {
        cookies,
        expires: Date.now() + 3600000 // 1 hora
      })
      
      // Tentar novamente com cookies
      return await fetch(url, {
        headers: {
          'Cookie': cookies,
          'User-Agent': USER_AGENTS.default,
        }
      })
    }
  } catch (error) {
    logger.error(LOG_TAGS.HTTP, 'Auth challenge handling failed', { error })
  }
  
  return null
}

export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams
  const url = searchParams.get('url')
  const forceUserAgent = searchParams.get('ua')
  const channelId = searchParams.get('channelId')
  
  if (!url) {
    return NextResponse.json({ error: 'URL parameter is required' }, { status: 400 })
  }
  
  try {
    const urlObj = new URL(url)
    const domain = urlObj.hostname
    
    logger.info(LOG_TAGS.HTTP, 'Advanced proxy request', { 
      url, 
      domain,
      forceUserAgent,
      channelId 
    })
    
    // Construir headers
    const headers: Record<string, string> = {
      'User-Agent': forceUserAgent || USER_AGENTS.default,
      'Accept': '*/*',
      'Accept-Language': 'en-US,en;q=0.9',
      'Accept-Encoding': 'gzip, deflate, br',
      'Connection': 'keep-alive',
      'Cache-Control': 'no-cache',
      'Pragma': 'no-cache',
    }
    
    // Adicionar headers específicos do domínio
    if (DOMAIN_HEADERS[domain]) {
      Object.assign(headers, DOMAIN_HEADERS[domain])
    }
    
    // Adicionar headers específicos do canal
    if (channelId) {
      const channelHeaders = getChannelHeaders(channelId)
      if (channelHeaders) {
        Object.assign(headers, channelHeaders)
        logger.info(LOG_TAGS.HTTP, 'Applied channel-specific headers', { channelId })
      }
      
      // Se é geo-restrito, adicionar IPs
      if (isGeoRestricted(channelId)) {
        headers['X-Forwarded-For'] = generateRandomIP()
        headers['X-Real-IP'] = generateRandomIP()
        logger.info(LOG_TAGS.HTTP, 'Applied geo-bypass headers', { channelId })
      }
    }
    
    // Headers customizados do cliente
    const customHeaders = searchParams.get('headers')
    if (customHeaders) {
      try {
        const parsed = JSON.parse(customHeaders)
        Object.assign(headers, parsed)
      } catch (e) {
        logger.warn(LOG_TAGS.HTTP, 'Failed to parse custom headers')
      }
    }
    
    // Primeira tentativa
    let response = await fetch(url, {
      headers,
      redirect: 'follow',
      signal: AbortSignal.timeout(30000),
    })
    
    // Se falhou com 401/403, tentar com autenticação
    if (response.status === 401 || response.status === 403) {
      const authResponse = await handleAuthChallenge(url, response)
      if (authResponse) {
        response = authResponse
      }
    }
    
    // Se ainda está falhando, tentar com diferentes user agents
    if (!response.ok && !forceUserAgent) {
      for (const [key, ua] of Object.entries(USER_AGENTS)) {
        if (key === 'default') continue
        
        logger.info(LOG_TAGS.HTTP, `Trying with ${key} user agent`)
        
        const altResponse = await fetch(url, {
          headers: { ...headers, 'User-Agent': ua },
          redirect: 'follow',
          signal: AbortSignal.timeout(10000),
        }).catch(() => null)
        
        if (altResponse && altResponse.ok) {
          response = altResponse
          break
        }
      }
    }
    
    if (!response.ok) {
      logger.error(LOG_TAGS.HTTP, 'Advanced proxy failed', {
        url,
        status: response.status,
        statusText: response.statusText
      })
      
      return NextResponse.json(
        { 
          error: `Proxy failed: ${response.statusText}`,
          status: response.status,
          suggestions: [
            'Stream may be geoblocked',
            'Try using VPN',
            'Check if stream requires subscription'
          ]
        },
        { status: response.status }
      )
    }
    
    const contentType = response.headers.get('content-type') || 'application/octet-stream'
    
    // Se for M3U8, reescrever URLs
    if (url.includes('.m3u8') || contentType.includes('mpegurl') || contentType.includes('m3u8')) {
      const text = await response.text()
      const proxyBaseUrl = `${request.nextUrl.protocol}//${request.nextUrl.host}/api/advanced-proxy`
      const rewrittenContent = rewriteM3U8Content(text, url, proxyBaseUrl)
      
      logger.info(LOG_TAGS.HTTP, 'M3U8 content rewritten', {
        url,
        originalLines: text.split('\n').length,
        rewrittenLines: rewrittenContent.split('\n').length
      })
      
      return new NextResponse(rewrittenContent, {
        status: 200,
        headers: {
          'Content-Type': 'application/vnd.apple.mpegurl',
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, OPTIONS',
          'Access-Control-Allow-Headers': '*',
          'Cache-Control': 'no-cache',
        }
      })
    }
    
    // Para outros arquivos, proxy direto
    const data = await response.arrayBuffer()
    
    logger.info(LOG_TAGS.HTTP, 'Advanced proxy success', {
      url,
      contentType,
      size: data.byteLength
    })
    
    return new NextResponse(data, {
      status: 200,
      headers: {
        'Content-Type': contentType,
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, OPTIONS',
        'Access-Control-Allow-Headers': '*',
        'Cache-Control': contentType.includes('mpegurl') ? 'no-cache' : 'public, max-age=3600',
      }
    })
  } catch (error) {
    logger.error(LOG_TAGS.HTTP, 'Advanced proxy error', {
      url,
      error: error instanceof Error ? error.message : 'Unknown error'
    })
    
    return NextResponse.json(
      { 
        error: 'Failed to proxy request', 
        details: error instanceof Error ? error.message : 'Unknown error',
        suggestions: [
          'Check if URL is valid',
          'Stream may be down',
          'Try again later'
        ]
      },
      { status: 500 }
    )
  }
}

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': '*',
    }
  })
}