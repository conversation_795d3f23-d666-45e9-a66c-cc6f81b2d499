import { NextRequest, NextResponse } from 'next/server'

// Este é um proxy especializado para streaming HLS com suporte robusto a CORS
export async function GET(request: NextRequest) {
  const url = request.nextUrl.searchParams.get('url')
  
  if (!url) {
    return NextResponse.json({ error: 'URL is required' }, { status: 400 })
  }

  try {
    console.log('🎬 [STREAM-PROXY] Proxying URL:', url)
    
    // Headers customizados para diferentes domínios
    const headers: HeadersInit = {
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
      'Accept': '*/*',
      'Accept-Language': 'en-US,en;q=0.9,es;q=0.8',
      'Cache-Control': 'no-cache',
      'Pragma': 'no-cache',
    }

    // Headers específicos por domínio
    if (url.includes('moveonjoy.com')) {
      headers['Referer'] = 'https://moveonjoy.com/'
      headers['Origin'] = 'https://moveonjoy.com'
    } else if (url.includes('akamaized.net')) {
      headers['Referer'] = 'https://www.rtve.es/'
    }

    // Fazer requisição com streaming support
    const response = await fetch(url, {
      headers,
      // @ts-ignore - duplex é suportado mas não está no tipo
      duplex: 'half',
    })

    if (!response.ok) {
      console.error('🎬 [STREAM-PROXY] Error response:', response.status, response.statusText)
      return NextResponse.json(
        { error: `Stream error: ${response.status} ${response.statusText}` },
        { status: response.status }
      )
    }

    // Para manifests M3U8, processar URLs relativas e adicionar proxy
    const contentType = response.headers.get('content-type') || ''
    if (contentType.includes('mpegurl') || url.endsWith('.m3u8')) {
      const text = await response.text()
      const baseUrl = new URL(url)
      const basePath = baseUrl.href.substring(0, baseUrl.href.lastIndexOf('/') + 1)
      
      // Processar o manifest para tornar URLs absolutas E passar pelo proxy
      const processed = text.split('\n').map(line => {
        line = line.trim()
        
        // Processar URI em tags EXT-X-MAP
        if (line.includes('URI=')) {
          return line.replace(/URI="([^"]+)"/g, (match, uri) => {
            if (!uri.startsWith('http')) {
              uri = new URL(uri, basePath).href
            }
            // Passar fragmento pelo proxy também
            const proxiedUri = `/api/stream-proxy?url=${encodeURIComponent(uri)}`
            return `URI="${proxiedUri}"`
          })
        }
        
        // Processar URLs de segmentos (.ts files)
        if (line && !line.startsWith('#')) {
          let segmentUrl = line
          if (!segmentUrl.startsWith('http')) {
            segmentUrl = new URL(line, basePath).href
          }
          // Passar segmento pelo proxy
          return `/api/stream-proxy?url=${encodeURIComponent(segmentUrl)}`
        }
        
        return line
      }).join('\n')

      return new NextResponse(processed, {
        headers: {
          'Content-Type': 'application/vnd.apple.mpegurl',
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, HEAD, OPTIONS',
          'Access-Control-Allow-Headers': 'Range, Accept, Content-Type',
          'Access-Control-Expose-Headers': 'Content-Length, Content-Range',
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0',
        }
      })
    }

    // Para outros tipos de conteúdo, fazer streaming direto
    const responseHeaders = new Headers({
      'Content-Type': response.headers.get('content-type') || 'application/octet-stream',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, HEAD, OPTIONS',
      'Access-Control-Allow-Headers': 'Range, Accept, Content-Type',
      'Access-Control-Expose-Headers': 'Content-Length, Content-Range',
      'Cache-Control': 'no-cache',
    })

    // Preservar headers importantes
    const preserveHeaders = ['content-length', 'content-range', 'accept-ranges']
    preserveHeaders.forEach(header => {
      const value = response.headers.get(header)
      if (value) {
        responseHeaders.set(header, value)
      }
    })

    // Streaming do body
    return new NextResponse(response.body, {
      status: response.status,
      headers: responseHeaders,
    })
    
  } catch (error) {
    console.error('🎬 [STREAM-PROXY] Error:', error)
    return NextResponse.json(
      { error: 'Failed to proxy stream', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
}

// OPTIONS para preflight CORS
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, HEAD, OPTIONS',
      'Access-Control-Allow-Headers': 'Range, Accept, Content-Type',
      'Access-Control-Max-Age': '86400',
    }
  })
}