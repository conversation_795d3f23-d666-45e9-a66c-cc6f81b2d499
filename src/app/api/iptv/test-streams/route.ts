import { NextResponse } from 'next/server'

// Streams de teste que geralmente funcionam bem
const testStreams = [
  {
    id: "espn-brasil",
    name: "ESPN Brasil",
    url: "https://cdn-5.nxplay.com.br/ESPN_BRASIL_HD/index.m3u8",
    country: "BR",
    logo: "https://upload.wikimedia.org/wikipedia/commons/thumb/2/2f/ESPN_wordmark.svg/512px-ESPN_wordmark.svg.png",
    categories: ["sports"],
    quality: "1080p",
    headers: {
      "Referer": "https://cnnbrasil.com.br/",
      "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
    }
  },
  {
    id: "sportv1-br",
    name: "SporTV 1 Brasil",
    url: "https://newedge.eu-central-1.edge.mycdn.live/live/sportv1/sportv1_2000/index.m3u8",
    country: "BR",
    logo: "https://upload.wikimedia.org/wikipedia/commons/thumb/5/5f/SporTV_logo_2022.png/512px-SporTV_logo_2022.png",
    categories: ["sports"],
    quality: "720p",
    headers: {}
  },
  {
    id: "redbull-tv",
    name: "Red Bull TV",
    url: "https://rbmn-live.akamaized.net/hls/live/590964/BoRB-AT/master.m3u8",
    country: "INT",
    logo: "https://img.redbull.com/images/c_fill,g_auto,w_1500,h_1000/q_auto,f_auto/redbullcom/tv/FO-1MR39KYYH2111/red-bull-tv",
    categories: ["sports"],
    quality: "1080p",
    headers: {}
  },
  {
    id: "olympic-channel",
    name: "Olympic Channel",
    url: "https://ott-linear-channels.stg.bolt.olympics.com/hls/channel01b/index.m3u8",
    country: "INT",
    logo: "https://upload.wikimedia.org/wikipedia/commons/thumb/a/a4/Olympic_Channel_logo.png/512px-Olympic_Channel_logo.png",
    categories: ["sports"],
    quality: "720p",
    headers: {}
  },
  {
    id: "stadium",
    name: "Stadium",
    url: "https://dai2.xumo.com/amagi_hls_data_xumo1234A-stadiumsports/CDN/master.m3u8",
    country: "US",
    logo: "https://upload.wikimedia.org/wikipedia/en/thumb/8/84/Stadium_%28sports_network%29_logo.svg/512px-Stadium_%28sports_network%29_logo.svg.png",
    categories: ["sports"],
    quality: "720p",
    headers: {}
  },
  {
    id: "racing-tv",
    name: "Racing TV",
    url: "https://lightning-now80s-samsungnz.amagi.tv/playlist.m3u8",
    country: "UK",
    logo: "https://upload.wikimedia.org/wikipedia/en/thumb/b/b4/Racing_TV_logo.svg/512px-Racing_TV_logo.svg.png",
    categories: ["sports"],
    quality: "720p",
    headers: {}
  },
  {
    id: "pluto-sports",
    name: "Pluto TV Sports",
    url: "https://service-stitcher.clusters.pluto.tv/stitch/hls/channel/5ca7f16c37b88b269307e102/master.m3u8?advertisingId=&appName=web&appVersion=5.14.0-0f5ca04c21649b8c8aad4e56266a23b96d73b83a&app_name=web&clientDeviceType=0&clientID=6fbead95-26b1-415d-998f-1bdef62d10be&clientModelNumber=na&deviceDNT=false&deviceId=6fbead95-26b1-415d-998f-1bdef62d10be&deviceLat=48.8582&deviceLon=2.3387&deviceMake=Chrome&deviceModel=web&deviceType=web&deviceVersion=88.0.4324.150&marketingRegion=FR&serverSideAds=false&sessionID=cc7dc550-6be3-11eb-9c02-0242ac110002&sid=cc7dc550-6be3-11eb-9c02-0242ac110002&userId=",
    country: "US",
    logo: "https://upload.wikimedia.org/wikipedia/commons/thumb/8/80/Pluto_TV_logo_2020.svg/512px-Pluto_TV_logo_2020.svg.png",
    categories: ["sports"],
    quality: "720p",
    headers: {}
  },
  {
    id: "sport-tv-plus",
    name: "Sport TV+",
    url: "https://sporttvp.lionflixapp.live/index.m3u8",
    country: "PT",
    logo: "https://upload.wikimedia.org/wikipedia/commons/thumb/2/2f/Sport_TV_logo.svg/512px-Sport_TV_logo.svg.png",
    categories: ["sports"],
    quality: "1080p",
    headers: {}
  },
  {
    id: "dubai-sports",
    name: "Dubai Sports",
    url: "https://dmisxthvll.cdn.mgmlcdn.com/dubaisports/smil:dubaisports.stream.smil/chunklist.m3u8",
    country: "AE",
    logo: "https://i.imgur.com/hAlGqLH.png",
    categories: ["sports"],
    quality: "720p",
    headers: {}
  }
]

export async function GET() {
  try {
    console.log('🎯 Retornando streams de teste funcionais')
    
    return NextResponse.json({
      success: true,
      data: testStreams
    })
    
  } catch (error) {
    console.error('❌ Erro ao buscar streams de teste:', error)
    return NextResponse.json({
      success: false,
      error: 'Erro ao buscar streams',
      data: []
    }, { status: 500 })
  }
}