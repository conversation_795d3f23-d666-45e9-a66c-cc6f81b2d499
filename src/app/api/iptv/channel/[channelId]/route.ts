import { NextRequest, NextResponse } from 'next/server'
import { fetchIPTVStreams, fetchIPTVChannels } from '@/lib/iptv-org-server'
import { logger } from '@/lib/logger'
import { LOG_TAGS } from '@/config/debug'
import { getFixedChannelId, getChannelHeaders, isGeoRestricted, requiresAuth } from '@/lib/channel-fixes'
import { needsFallback, getWorkingUrl } from '@/lib/channel-fallbacks'
import { getPremiumChannelById } from '@/lib/premium-football-channels'
import { getWorkingChannel, CHANNEL_ID_MAPPING } from '@/lib/real-working-channels'
import { getAllPossibleUrls, shouldUseProxy } from '@/lib/channel-url-fixes'

export async function GET(
  request: NextRequest,
  context: { params: Promise<{ channelId: string }> }
) {
  console.log('[IPTV-CHANNEL-API] ===== NEW REQUEST =====')
  console.log('[IPTV-CHANNEL-API] Time:', new Date().toISOString())
  
  try {
    console.log('[IPTV-CHANNEL-API] Awaiting params...')
    const params = await context.params
    const { channelId } = params

    console.log('[IPTV-CHANNEL-API] Channel ID requested:', channelId)
    console.log('[IPTV-CHANNEL-API] Full URL:', request.url)
    console.log('[IPTV-CHANNEL-API] Method:', request.method)
    logger.info(LOG_TAGS.IPTV, 'Fetching channel data', { channelId })

    // Remover verificação de assinatura aqui - o controle é feito no player após 5 minutos

    // Verificar se é um canal premium
    if (channelId.startsWith('premium-')) {
      console.log('[IPTV-CHANNEL-API] Premium channel detected')
      const premiumId = channelId.replace('premium-', '')
      const premiumChannel = getPremiumChannelById(premiumId)
      
      if (premiumChannel) {
        console.log('[IPTV-CHANNEL-API] Premium channel found:', premiumChannel.name)
        console.log('[IPTV-CHANNEL-API] Available streams:', premiumChannel.streams.length)
        
        const stream = premiumChannel.streams[0]
        const response = {
          success: true,
          data: {
            id: channelId,
            name: premiumChannel.name,
            url: stream.url,
            streams: premiumChannel.streams, // Enviar todas as URLs para fallback
            country: premiumChannel.country,
            languages: [premiumChannel.language],
            logo: premiumChannel.logo,
            categories: ['sports', 'premium', premiumChannel.category.toLowerCase()],
            headers: stream.headers || {},
            userAgent: stream.headers?.['User-Agent'] || 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            epg: null,
            requiresProxy: stream.requiresProxy || true
          }
        }
        
        console.log('[IPTV-CHANNEL-API] ✅ Returning premium channel')
        console.log('[IPTV-CHANNEL-API] Primary URL:', stream.url)
        console.log('[IPTV-CHANNEL-API] Requires proxy:', stream.requiresProxy)
        console.log('[IPTV-CHANNEL-API] ==============================')
        
        return NextResponse.json(response)
      } else {
        console.log('[IPTV-CHANNEL-API] ❌ Premium channel not found:', premiumId)
      }
    }
    
    // Canal de teste especial
    if (channelId === 'test-stream') {
      return NextResponse.json({
        success: true,
        data: {
          id: 'test-stream',
          name: 'Test Stream - Big Buck Bunny',
          url: 'https://test-streams.mux.dev/x36xhzz/x36xhzz.m3u8',
          country: 'Test',
          languages: ['en'],
          logo: 'https://images.unsplash.com/photo-1574629810360-7efbbe195018?w=300&h=200&fit=crop',
          categories: ['sports'],
          headers: {},
          userAgent: null,
          epg: null
        }
      })
    }

    // Não usar mais dados demo - sempre buscar dados reais

    // Apply channel ID fixes (handles prefix removal and mapping)
    console.log('[IPTV-CHANNEL-API] Applying channel ID fixes...')
    const actualChannelId = getFixedChannelId(channelId)
    console.log('[IPTV-CHANNEL-API] Actual channel ID:', actualChannelId)
    console.log('[IPTV-CHANNEL-API] Original channel ID:', channelId)
    
    // Check if channel requires special handling
    if (isGeoRestricted(actualChannelId)) {
      console.log('[IPTV-CHANNEL-API] ⚠️ Channel is geo-restricted')
      logger.warn(LOG_TAGS.IPTV, 'Channel is geo-restricted', { channelId, actualChannelId })
    }
    
    if (requiresAuth(actualChannelId)) {
      console.log('[IPTV-CHANNEL-API] ⚠️ Channel requires authentication')
      logger.warn(LOG_TAGS.IPTV, 'Channel requires authentication', { channelId, actualChannelId })
    }

    // Buscar streams e canais
    console.log('[IPTV-CHANNEL-API] Fetching streams and channels from IPTV-ORG...')
    let streams, channels
    
    try {
      const startTime = Date.now()
      const results = await Promise.all([
        fetchIPTVStreams(),
        fetchIPTVChannels()
      ])
      const fetchTime = Date.now() - startTime
      
      streams = results[0]
      channels = results[1]
      console.log('[IPTV-CHANNEL-API] Fetch completed in:', fetchTime, 'ms')
      console.log('[IPTV-CHANNEL-API] Total streams:', streams.length)
      console.log('[IPTV-CHANNEL-API] Total channels:', channels.length)
    } catch (fetchError) {
      console.error('[IPTV-CHANNEL-API] ❌ Error fetching IPTV data:', fetchError)
      throw fetchError
    }
    
    // Encontrar o stream do canal
    console.log('[IPTV-CHANNEL-API] Looking for stream:', actualChannelId)
    console.log('[IPTV-CHANNEL-API] First 10 channel IDs from IPTV-ORG:', streams.slice(0, 10).map(s => s.channel))
    console.log('[IPTV-CHANNEL-API] Searching for channels containing "30A":', streams.filter(s => s.channel?.toLowerCase().includes('30a')).map(s => s.channel))
    console.log('[IPTV-CHANNEL-API] Total streams with "golf" in name:', streams.filter(s => s.channel?.toLowerCase().includes('golf')).length)
    
    // Debug específico para 30AGolfKingdom
    if (actualChannelId.includes('30A') || actualChannelId.includes('Golf')) {
      console.log('[IPTV-CHANNEL-API] Debugging Golf channel lookup:')
      console.log('[IPTV-CHANNEL-API]   Looking for:', actualChannelId)
      console.log('[IPTV-CHANNEL-API]   Similar channels:', streams.filter(s => 
        s.channel?.includes('Golf') || 
        s.channel?.includes('30A') ||
        s.channel?.includes('Kingdom')
      ).map(s => s.channel))
    }
    
    let stream = streams.find(s => s.channel === actualChannelId)
    console.log('[IPTV-CHANNEL-API] Stream found in IPTV-ORG:', !!stream)
    
    if (stream) {
      console.log('[IPTV-CHANNEL-API] Original stream URL:', stream.url)
      console.log('[IPTV-CHANNEL-API] Stream quality:', stream.quality || 'unknown')
    }
    
    // Se não encontrou o stream, verificar se precisa de fallback
    if (!stream && needsFallback(actualChannelId)) {
      console.log('[IPTV-CHANNEL-API] No stream found, checking fallbacks...')
      logger.info(LOG_TAGS.IPTV, 'Channel needs fallback URL', { 
        channelId: actualChannelId 
      })
      
      const fallback = getWorkingUrl(actualChannelId)
      if (fallback.url) {
        console.log('[IPTV-CHANNEL-API] ✅ Fallback found!')
        console.log('[IPTV-CHANNEL-API] Fallback URL:', fallback.url)
        console.log('[IPTV-CHANNEL-API] Fallback requires proxy:', fallback.requiresProxy)
        
        // Criar um stream fake com a URL alternativa
        stream = {
          channel: actualChannelId,
          url: fallback.url,
          http_referrer: fallback.headers?.Referer,
          user_agent: fallback.headers?.['User-Agent'],
          requiresProxy: fallback.requiresProxy
        }
        logger.info(LOG_TAGS.IPTV, 'Using fallback URL', { 
          channelId: actualChannelId,
          fallbackUrl: fallback.url,
          requiresProxy: fallback.requiresProxy
        })
      } else {
        console.log('[IPTV-CHANNEL-API] ❌ No fallback URL available')
      }
    }
    
    // Se não encontrou, tentar com canais reais/funcionais antes de desistir
    if (!stream) {
      console.log('[IPTV-CHANNEL-API] Stream not found yet, checking real working channels...')
      
      // Primeiro tentar com o ID atual
      let workingChannel = getWorkingChannel(actualChannelId)
      
      // Se não encontrar, tentar com o ID original
      if (!workingChannel) {
        workingChannel = getWorkingChannel(channelId)
      }
      
      // Se ainda não encontrar, verificar mapeamento
      if (!workingChannel && CHANNEL_ID_MAPPING[actualChannelId]) {
        const mappedId = CHANNEL_ID_MAPPING[actualChannelId]
        console.log(`[IPTV-CHANNEL-API] Trying mapped ID: ${actualChannelId} -> ${mappedId}`)
        workingChannel = getWorkingChannel(mappedId)
      }
      
      if (workingChannel) {
        console.log('[IPTV-CHANNEL-API] ✅ Found in working channels!')
        console.log('[IPTV-CHANNEL-API] Channel:', workingChannel.name)
        console.log('[IPTV-CHANNEL-API] URL:', workingChannel.url)
        console.log('[IPTV-CHANNEL-API] Success rate:', workingChannel.successRate)
        
        // Obter todas as URLs possíveis incluindo correções
        const allUrls = getAllPossibleUrls(workingChannel.id, workingChannel.url)
        if (workingChannel.fallbackUrls) {
          workingChannel.fallbackUrls.forEach(url => {
            if (!allUrls.includes(url)) {
              allUrls.push(url)
            }
          })
        }
        
        console.log('[IPTV-CHANNEL-API] All possible URLs:', allUrls.length)
        console.log('[IPTV-CHANNEL-API] URLs:', allUrls)
        
        // Verificar se precisa de proxy baseado no canal
        const requiresProxy = workingChannel.requiresProxy || shouldUseProxy(workingChannel.id)
        
        // Retornar resposta formatada para canal funcional
        const response = {
          success: true,
          data: {
            id: channelId,
            name: workingChannel.name,
            url: allUrls[0], // Usar primeira URL (pode ser a corrigida)
            streams: allUrls.map((url, index) => ({ 
              url, 
              quality: index === 0 ? 'HD' : 'SD' 
            })),
            country: workingChannel.country,
            languages: [workingChannel.language],
            logo: workingChannel.logo || null,
            categories: ['sports', workingChannel.category],
            headers: workingChannel.headers || {},
            userAgent: workingChannel.headers?.['User-Agent'] || 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            epg: null,
            requiresProxy,
            successRate: workingChannel.successRate
          }
        }
        
        console.log('[IPTV-CHANNEL-API] ✅ Returning working channel')
        console.log('[IPTV-CHANNEL-API] ==============================')
        return NextResponse.json(response)
      }
    }
    
    if (!stream) {
      console.error('[IPTV-CHANNEL-API] ❌ Channel not found in working channels or fallbacks')
      console.error('[IPTV-CHANNEL-API] Requested:', channelId)
      console.error('[IPTV-CHANNEL-API] Fixed ID:', actualChannelId)
      console.error('[IPTV-CHANNEL-API] Available ESPN channels:', streams.filter(s => s.channel?.includes('ESPN')).map(s => s.channel))
      
      logger.warn(LOG_TAGS.IPTV, 'Channel stream not found', { 
        channelId, 
        actualChannelId,
        availableChannels: streams.slice(0, 5).map(s => s.channel) 
      })
      return NextResponse.json(
        { 
          success: false, 
          error: 'Canal não encontrado' 
        },
        { status: 404 }
      )
    }

    // Buscar metadados do canal
    const channel = channels.find(ch => ch.id === actualChannelId)
    console.log('[IPTV-CHANNEL-API] Channel metadata found:', !!channel)
    if (channel) {
      console.log('[IPTV-CHANNEL-API] Channel name:', channel.name)
      console.log('[IPTV-CHANNEL-API] Channel country:', channel.country)
      console.log('[IPTV-CHANNEL-API] Channel categories:', channel.categories)
    }

    // Formatar resposta
    console.log('[IPTV-CHANNEL-API] Building response...')
    let headers: Record<string, string> = {}
    if (stream.http_referrer || stream.referrer) {
      headers['Referer'] = stream.http_referrer || stream.referrer
      console.log('[IPTV-CHANNEL-API] Added Referer header:', headers['Referer'])
    }
    
    // Aplicar headers específicos do canal
    const channelHeaders = getChannelHeaders(actualChannelId)
    if (channelHeaders) {
      headers = { ...headers, ...channelHeaders }
      console.log('[IPTV-CHANNEL-API] Applied channel-specific headers')
      logger.info(LOG_TAGS.IPTV, 'Applied channel-specific headers', { 
        channelId: actualChannelId,
        headers: Object.keys(channelHeaders)
      })
    }
    
    const userAgent = stream.user_agent || stream.http_user_agent || 
                     (channelHeaders?.['User-Agent'])
    
    console.log('[IPTV-CHANNEL-API] Final headers:', headers)
    console.log('[IPTV-CHANNEL-API] User-Agent:', userAgent || 'none')
    
    const response = {
      id: stream.channel,
      name: channel?.name || stream.channel,
      url: stream.url,
      country: channel?.country,
      languages: channel?.languages || [],
      logo: channel?.logo,
      categories: channel?.categories || [],
      headers,
      userAgent,
      epg: channel?.guides?.[0] || null,
      requiresProxy: stream.requiresProxy || false
    }
    
    console.log('[IPTV-CHANNEL-API] ✅ RESPONSE READY')
    console.log('[IPTV-CHANNEL-API] Channel:', response.name)
    console.log('[IPTV-CHANNEL-API] URL:', response.url)
    console.log('[IPTV-CHANNEL-API] Requires proxy:', response.requiresProxy)
    console.log('[IPTV-CHANNEL-API] Has headers:', Object.keys(headers).length > 0)
    console.log('[IPTV-CHANNEL-API] ==============================')
    
    logger.info(LOG_TAGS.IPTV, 'Channel data found', {
      channelId,
      actualChannelId,
      name: response.name,
      url: response.url,
      hasHeaders: Object.keys(headers).length > 0,
      hasUserAgent: !!userAgent
    })

    return NextResponse.json({
      success: true,
      data: response
    })

  } catch (error) {
    console.error('[IPTV-CHANNEL-API] ❌❌❌ ERROR ❌❌❌')
    console.error('[IPTV-CHANNEL-API] Error:', error instanceof Error ? error.message : String(error))
    console.error('[IPTV-CHANNEL-API] Stack trace:', error instanceof Error ? error.stack : 'N/A')
    console.error('[IPTV-CHANNEL-API] Channel ID:', channelId)
    console.error('[IPTV-CHANNEL-API] ❌❌❌❌❌❌❌❌❌❌❌')
    
    logger.error(LOG_TAGS.IPTV, 'Failed to fetch channel data', { 
      error: error instanceof Error ? error.message : String(error),
      channelId 
    })
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Erro ao buscar canal'
      },
      { status: 500 }
    )
  }
}