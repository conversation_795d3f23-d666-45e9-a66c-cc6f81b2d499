import { NextResponse } from 'next/server'
import { iptvOrgService } from '@/services/api/iptv/iptv-org.service'

export async function GET() {
  try {
    console.log('🔴 Testando API IPTV-ORG...')
    
    // Forçar atualização dos dados
    await iptvOrgService.fetchData(true)
    
    // Buscar canais esportivos
    const sportsChannels = await iptvOrgService.getSportsChannels({})
    
    console.log(`✅ ${sportsChannels.length} canais esportivos encontrados`)
    
    // Pegar detalhes dos primeiros 10 canais
    const channelDetails = sportsChannels.slice(0, 10).map(channel => ({
      id: channel.channel,
      name: channel.channelData?.name,
      country: channel.channelData?.country,
      languages: channel.channelData?.languages,
      url: channel.url,
      quality: channel.quality,
      headers: {
        referrer: channel.http_referrer || channel.referrer,
        userAgent: channel.user_agent
      }
    }))
    
    // Buscar canais populares
    const popularChannels = await iptvOrgService.getPopularSportsChannels(10)
    
    return NextResponse.json({
      success: true,
      summary: {
        totalSportsChannels: sportsChannels.length,
        sampleChannels: channelDetails.length,
        popularChannels: popularChannels.length
      },
      data: {
        sampleChannels: channelDetails,
        popularChannels: popularChannels.map(ch => ({
          name: ch.channelData?.name,
          url: ch.url,
          country: ch.channelData?.country
        }))
      }
    })
    
  } catch (error) {
    console.error('❌ Erro ao testar IPTV:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Erro desconhecido'
    }, { status: 500 })
  }
}