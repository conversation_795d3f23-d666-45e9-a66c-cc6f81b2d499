import { NextRequest, NextResponse } from 'next/server'
import { iptvOrgService } from '@/services/api/iptv/iptv-org.service'

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams
    const popular = searchParams.get('popular') === 'true'
    const limit = parseInt(searchParams.get('limit') || '20')
    
    console.log('🎯 Buscando canais esportivos IPTV...')
    
    let channels
    if (popular) {
      channels = await iptvOrgService.getPopularSportsChannels(limit)
    } else {
      channels = await iptvOrgService.getSportsChannels({})
    }
    
    // Mapear para o formato esperado pelo frontend
    const mappedChannels = channels.map(stream => ({
      id: stream.channel,
      name: stream.channelData?.name || stream.channel,
      url: stream.url,
      country: stream.channelData?.country || 'INT',
      languages: stream.channelData?.languages || [],
      logo: stream.channelData?.logo || null,
      categories: stream.channelData?.categories || ['sports'],
      quality: stream.quality,
      headers: {
        'Referer': stream.http_referrer || stream.referrer || '',
        'User-Agent': stream.user_agent || 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      }
    }))
    
    console.log(`✅ ${mappedChannels.length} canais esportivos encontrados`)
    
    return NextResponse.json({
      success: true,
      data: mappedChannels
    })
    
  } catch (error) {
    console.error('❌ Erro ao buscar canais IPTV:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Erro ao buscar canais',
      data: []
    }, { status: 500 })
  }
}