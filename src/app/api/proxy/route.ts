import { NextRequest, NextResponse } from 'next/server'
import { logger } from '@/lib/logger'
import { LOG_TAGS } from '@/config/debug'

export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams
  const url = searchParams.get('url')
  
  if (!url) {
    return NextResponse.json({ error: 'URL parameter is required' }, { status: 400 })
  }
  
  try {
    logger.info(LOG_TAGS.HTTP, 'Proxy request', { url })
    
    // Fazer a requisição para a URL original
    const response = await fetch(url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Accept': '*/*',
        'Accept-Language': 'en-US,en;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
      }
    })
    
    if (!response.ok) {
      logger.error(LOG_TAGS.HTTP, 'Proxy failed', {
        url,
        status: response.status,
        statusText: response.statusText
      })
      return NextResponse.json(
        { error: `Proxy failed: ${response.statusText}` },
        { status: response.status }
      )
    }
    
    // Obter o conteúdo
    const contentType = response.headers.get('content-type') || 'application/octet-stream'
    const data = await response.arrayBuffer()
    
    logger.info(LOG_TAGS.HTTP, 'Proxy success', {
      url,
      contentType,
      size: data.byteLength
    })
    
    // Retornar com headers CORS apropriados
    return new NextResponse(data, {
      status: 200,
      headers: {
        'Content-Type': contentType,
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type',
        'Cache-Control': 'no-cache',
      }
    })
  } catch (error) {
    logger.error(LOG_TAGS.HTTP, 'Proxy error', {
      url,
      error: error instanceof Error ? error.message : 'Unknown error'
    })
    
    return NextResponse.json(
      { error: 'Failed to proxy request', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
}

export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    }
  })
}