import { NextRequest, NextResponse } from 'next/server'
import { logger } from '@/lib/logger'
import { LOG_TAGS } from '@/config/debug'

export async function GET(request: NextRequest) {
  const timestamp = new Date().toISOString()
  console.log(`[PROXY-API] ${timestamp} - ===== NEW PROXY REQUEST =====`)
  console.log(`[PROXY-API] ${timestamp} - Request URL:`, request.url)
  console.log(`[PROXY-API] ${timestamp} - Request method:`, request.method)
  console.log(`[PROXY-API] ${timestamp} - Headers:`, Object.fromEntries(request.headers.entries()))
  
  try {
    const url = request.nextUrl.searchParams.get('url')
    const referer = request.nextUrl.searchParams.get('referer')
    const userAgent = request.nextUrl.searchParams.get('userAgent')
    const channelId = request.nextUrl.searchParams.get('channelId')
    const channelName = request.nextUrl.searchParams.get('channel')
    
    console.log(`[PROXY-API] ${timestamp} - Parameters:`)
    console.log(`[PROXY-API] ${timestamp} -   URL:`, url)
    console.log(`[PROXY-API] ${timestamp} -   Referer:`, referer || 'none')
    console.log(`[PROXY-API] ${timestamp} -   User-Agent:`, userAgent || 'default')
    console.log(`[PROXY-API] ${timestamp} -   Channel ID:`, channelId || 'none')
    console.log(`[PROXY-API] ${timestamp} -   Channel Name:`, channelName || 'none')
    
    if (!url) {
      console.log('🔴 [PROXY] URL não fornecida')
      return NextResponse.json(
        { error: 'URL is required' },
        { status: 400 }
      )
    }
    
    logger.info(LOG_TAGS.STREAM, 'Proxy request', { 
      url,
      hasReferer: !!referer,
      hasUserAgent: !!userAgent,
      channelId,
      channelName
    })
    
    // Headers para a requisição
    const headers: Record<string, string> = {
      'User-Agent': userAgent || 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
      'Accept': '*/*',
      'Accept-Language': 'en-US,en;q=0.9',
      'Cache-Control': 'no-cache',
      'Pragma': 'no-cache',
      'Connection': 'keep-alive',
      'Accept-Encoding': 'gzip, deflate, br',
      'Sec-Fetch-Dest': 'empty',
      'Sec-Fetch-Mode': 'cors',
      'Sec-Fetch-Site': 'cross-site'
    }
    
    // Headers específicos para moveonjoy.com
    if (url.includes('moveonjoy.com')) {
      headers['Referer'] = 'https://moveonjoy.com/'
      headers['Origin'] = 'https://moveonjoy.com'
      headers['Sec-Ch-Ua'] = '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"'
      headers['Sec-Ch-Ua-Mobile'] = '?0'
      headers['Sec-Ch-Ua-Platform'] = '"Windows"'
      console.log('🔷 [PROXY] Aplicando headers específicos para moveonjoy.com')
    } else if (referer) {
      headers['Referer'] = referer
      headers['Origin'] = new URL(referer).origin
    }
    
    // Fazer a requisição com timeout e retry
    console.log(`[PROXY-API] ${timestamp} - Making request to:`, url)
    console.log(`[PROXY-API] ${timestamp} - Request headers:`, headers)
    
    // Implementar AbortController para timeout
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), 15000) // 15 segundos de timeout
    
    let response
    let attempts = 0
    const maxAttempts = 3
    
    while (attempts < maxAttempts) {
      try {
        response = await fetch(url, {
          headers,
          redirect: 'follow',
          signal: controller.signal
        })
        clearTimeout(timeoutId)
        break
      } catch (fetchError) {
        attempts++
        console.log(`🔴 [PROXY] Tentativa ${attempts} falhou:`, fetchError)
        
        if (attempts >= maxAttempts) {
          throw fetchError
        }
        
        // Esperar antes de tentar novamente (exponential backoff)
        await new Promise(resolve => setTimeout(resolve, 1000 * attempts))
      }
    }
    
    console.log(`[PROXY-API] ${timestamp} - Response status:`, response.status)
    console.log(`[PROXY-API] ${timestamp} - Response headers:`, Object.fromEntries(response.headers.entries()))
    
    if (!response.ok) {
      console.error(`[PROXY-API] ${timestamp} - ❌ Request failed`)
      console.error(`[PROXY-API] ${timestamp} -   Status:`, response.status)
      console.error(`[PROXY-API] ${timestamp} -   Status text:`, response.statusText)
      console.error(`[PROXY-API] ${timestamp} -   URL:`, url)
      
      logger.error(LOG_TAGS.STREAM, 'Proxy request failed', {
        status: response.status,
        statusText: response.statusText,
        url
      })
      
      return NextResponse.json(
        { error: `Proxy request failed: ${response.status}` },
        { status: response.status }
      )
    }
    
    // Obter o conteúdo
    const contentType = response.headers.get('content-type') || 'application/octet-stream'
    console.log('🔷 [PROXY] Content-Type:', contentType)
    
    // Para m3u8, precisamos processar URLs relativas
    if (contentType.includes('application/vnd.apple.mpegurl') || url.endsWith('.m3u8')) {
      console.log('🔷 [PROXY] Detectado arquivo m3u8, processando...')
      const text = await response.text()
      const urlObj = new URL(url)
      const baseUrl = urlObj.origin + urlObj.pathname.substring(0, urlObj.pathname.lastIndexOf('/') + 1)
      
      console.log('🔷 [PROXY] Base URL para resolução:', baseUrl)
      
      // Processar URLs relativas no m3u8
      const processedText = text.split('\n').map(line => {
        const trimmedLine = line.trim()
        
        // Se é uma linha vazia ou comentário (exceto EXT-X-MAP), manter como está
        if (!trimmedLine || (trimmedLine.startsWith('#') && !trimmedLine.startsWith('#EXT-X-MAP'))) {
          return line
        }
        
        // Processar EXT-X-MAP
        if (trimmedLine.startsWith('#EXT-X-MAP')) {
          const uriMatch = trimmedLine.match(/URI="([^"]+)"/)
          if (uriMatch && uriMatch[1]) {
            const mapUri = uriMatch[1]
            if (!mapUri.startsWith('http')) {
              const absoluteMapUri = new URL(mapUri, baseUrl).toString()
              console.log(`🔷 [PROXY] Convertendo MAP URI: ${mapUri} -> ${absoluteMapUri}`)
              return trimmedLine.replace(`URI="${mapUri}"`, `URI="${absoluteMapUri}"`)
            }
          }
          return line
        }
        
        // Para linhas de URL (não começam com #)
        if (!trimmedLine.startsWith('http')) {
          // É uma URL relativa
          let absoluteUrl
          if (trimmedLine.startsWith('/')) {
            // URL absoluta no servidor
            absoluteUrl = urlObj.origin + trimmedLine
          } else {
            // URL relativa
            absoluteUrl = new URL(trimmedLine, baseUrl).toString()
          }
          console.log(`🔷 [PROXY] Convertendo URL relativa: ${trimmedLine} -> ${absoluteUrl}`)
          return absoluteUrl
        }
        
        return line
      }).join('\n')
      
      const buffer = new TextEncoder().encode(processedText)
      console.log('🔷 [PROXY] M3U8 processado, tamanho:', buffer.byteLength)
      
      return new NextResponse(buffer, {
        status: 200,
        headers: {
          'Content-Type': contentType,
          'Content-Length': buffer.byteLength.toString(),
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Range',
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        }
      })
    }
    
    const buffer = await response.arrayBuffer()
    
    logger.info(LOG_TAGS.STREAM, 'Proxy request successful', {
      contentType,
      contentLength: buffer.byteLength
    })
    
    // Retornar o conteúdo com os headers apropriados
    return new NextResponse(buffer, {
      status: 200,
      headers: {
        'Content-Type': contentType,
        'Content-Length': buffer.byteLength.toString(),
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Range',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    })
    
  } catch (error) {
    console.error('🔴 [PROXY] Erro no proxy:', error)
    logger.error(LOG_TAGS.STREAM, 'Proxy error', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined
    })
    
    // Retornar erro mais específico
    if (error instanceof Error && error.name === 'AbortError') {
      return NextResponse.json(
        { error: 'Request timeout - stream took too long to respond' },
        { status: 504 }
      )
    }
    
    return NextResponse.json(
      { error: 'Proxy error occurred', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
}

// Lidar com requisições OPTIONS para CORS
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Range',
    },
  })
}