import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

export async function POST(request: NextRequest) {
  const timestamp = new Date().toISOString()
  console.log(`\n[ACTIVATE-ADMIN] ${timestamp} - ===== ATIVAÇÃO COM ADMIN =====`)
  
  try {
    const body = await request.json()
    const { userId, userEmail } = body
    
    console.log(`[ACTIVATE-ADMIN] ${timestamp} - User ID: ${userId}`)
    console.log(`[ACTIVATE-ADMIN] ${timestamp} - User Email: ${userEmail}`)
    
    if (!userId) {
      return NextResponse.json({ error: 'User ID required' }, { status: 400 })
    }
    
    // Criar cliente Supabase com service role key (bypass RLS)
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!
    
    if (!supabaseUrl || !supabaseServiceKey) {
      console.error(`[ACTIVATE-ADMIN] ${timestamp} - Missing Supabase environment variables`)
      return NextResponse.json({ error: 'Server configuration error' }, { status: 500 })
    }
    
    const supabase = createClient(supabaseUrl, supabaseServiceKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    })
    
    // Data de expiração (30 dias)
    const expiresAt = new Date()
    expiresAt.setDate(expiresAt.getDate() + 30)
    
    // Primeiro tentar stack_profiles
    console.log(`[ACTIVATE-ADMIN] ${timestamp} - Verificando stack_profiles...`)
    
    const { data: stackCheck, error: stackCheckError } = await supabase
      .from('stack_profiles')
      .select('id')
      .eq('id', userId)
      .single()
    
    if (stackCheck && !stackCheckError) {
      // Existe em stack_profiles
      console.log(`[ACTIVATE-ADMIN] ${timestamp} - Usuário encontrado em stack_profiles, atualizando...`)
      
      const { data: updated, error: updateError } = await supabase
        .from('stack_profiles')
        .update({
          subscription_tier: 'premium',
          subscription_status: 'active',
          subscription_current_period_end: expiresAt.toISOString(),
          app_verified: true,
          app_verified_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('id', userId)
        .select()
        .single()
      
      if (updateError) {
        console.error(`[ACTIVATE-ADMIN] ${timestamp} - Erro ao atualizar stack_profiles:`, updateError)
        return NextResponse.json({
          error: 'Failed to update stack profile',
          details: updateError.message
        }, { status: 500 })
      }
      
      console.log(`[ACTIVATE-ADMIN] ${timestamp} - ✅ Stack profile atualizado com sucesso`)
      
      // Também criar/atualizar anonymous_access para compatibilidade
      await supabase
        .from('anonymous_access')
        .upsert({
          user_id: userId,
          session_id: `app-${Date.now()}`,
          expires_at: expiresAt.toISOString(),
          is_active: true,
          device_info: { source: 'app_activation' }
        }, {
          onConflict: 'user_id'
        })
      
      return NextResponse.json({
        success: true,
        message: 'Premium activated successfully!',
        user_id: userId,
        expires_at: expiresAt.toISOString(),
        profile_type: 'stack_profiles'
      })
    }
    
    // Se não existe em stack_profiles, criar lá
    console.log(`[ACTIVATE-ADMIN] ${timestamp} - Usuário não encontrado, criando em stack_profiles...`)
    
    const { data: newProfile, error: createError } = await supabase
      .from('stack_profiles')
      .insert({
        id: userId,
        email: userEmail || null,
        subscription_tier: 'premium',
        subscription_status: 'active',
        subscription_current_period_end: expiresAt.toISOString(),
        app_verified: true,
        app_verified_at: new Date().toISOString(),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single()
    
    if (createError) {
      console.error(`[ACTIVATE-ADMIN] ${timestamp} - Erro ao criar stack_profile:`, createError)
      return NextResponse.json({
        error: 'Failed to create profile',
        details: createError.message,
        code: createError.code
      }, { status: 500 })
    }
    
    console.log(`[ACTIVATE-ADMIN] ${timestamp} - ✅ Stack profile criado com sucesso`)
    
    // Também criar anonymous_access para compatibilidade
    await supabase
      .from('anonymous_access')
      .upsert({
        user_id: userId,
        session_id: `app-${Date.now()}`,
        expires_at: expiresAt.toISOString(),
        is_active: true,
        device_info: { source: 'app_activation' }
      }, {
        onConflict: 'user_id'
      })
    
    return NextResponse.json({
      success: true,
      message: 'Premium activated successfully!',
      user_id: userId,
      expires_at: expiresAt.toISOString(),
      profile_type: 'stack_profiles_new'
    })
    
  } catch (error) {
    console.error(`[ACTIVATE-ADMIN] ${timestamp} - ❌ ERRO GERAL:`, error)
    return NextResponse.json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}