import { NextRequest, NextResponse } from 'next/server'
import { createServiceClient } from '@/lib/supabase/server'
import { v4 as uuidv4 } from 'uuid'

export async function POST(request: NextRequest) {
  const timestamp = new Date().toISOString()
  console.log(`[GENERATE-APP-TOKEN] ${timestamp} - Request received`)
  
  try {
    // Obter dados da requisição
    const body = await request.json()
    const { platform = 'unknown', app_version = '1.0.0' } = body
    
    // Obter IP do cliente
    const forwarded = request.headers.get('x-forwarded-for')
    const ip = forwarded ? forwarded.split(',')[0] : 'unknown'
    const userAgent = request.headers.get('user-agent') || 'unknown'
    
    console.log(`[GENERATE-APP-TOKEN] ${timestamp} - Platform: ${platform}, IP: ${ip}`)
    
    const supabase = createServiceClient()
    
    // Verificar rate limit
    const { data: rateLimitOk } = await supabase
      .rpc('check_app_token_rate_limit', { p_ip_address: ip })
      .single()
    
    if (!rateLimitOk) {
      console.log(`[GENERATE-APP-TOKEN] ${timestamp} - Rate limit exceeded for IP: ${ip}`)
      return NextResponse.json({ 
        error: 'Demasiadas solicitudes. Intenta más tarde.' 
      }, { status: 429 })
    }
    
    // Gerar token único
    const token = uuidv4()
    
    // Calcular expiração (1 hora)
    const expiresAt = new Date()
    expiresAt.setHours(expiresAt.getHours() + 1)
    
    // Salvar token no banco
    const { data, error } = await supabase
      .from('app_tokens')
      .insert({
        token,
        platform,
        app_version,
        ip_address: ip,
        user_agent: userAgent,
        expires_at: expiresAt.toISOString(),
        metadata: {
          timestamp: timestamp,
          source: 'app_download'
        }
      })
      .select()
      .single()
    
    if (error) {
      console.error(`[GENERATE-APP-TOKEN] ${timestamp} - Error saving token:`, error)
      return NextResponse.json({ 
        error: 'Error al generar enlace de acceso' 
      }, { status: 500 })
    }
    
    // Construir URL de redirecionamento
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'https://newspports.com'
    const redirectUrl = `${baseUrl}/app-verify?token=${token}`
    
    console.log(`[GENERATE-APP-TOKEN] ${timestamp} - Token generated successfully: ${token}`)
    
    return NextResponse.json({
      success: true,
      redirect_url: redirectUrl,
      expires_in: 3600, // segundos
      message: 'Enlace de acceso generado'
    })
    
  } catch (error) {
    console.error(`[GENERATE-APP-TOKEN] ${timestamp} - Unexpected error:`, error)
    return NextResponse.json({ 
      error: 'Error interno del servidor' 
    }, { status: 500 })
  }
}

// Endpoint público - não requer autenticação
export const runtime = 'nodejs'