import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

export async function POST(request: NextRequest) {
  const timestamp = new Date().toISOString()
  console.log(`\n[ACTIVATE-MINIMAL] ${timestamp} - ===== ATIVAÇÃO MÍNIMA INICIADA =====`)
  
  try {
    const body = await request.json()
    const { deviceId, userId, userEmail } = body
    
    console.log(`[ACTIVATE-MINIMAL] ${timestamp} - Dados recebidos:`)
    console.log(`  - Device ID: ${deviceId}`)
    console.log(`  - User ID: ${userId}`)
    console.log(`  - User Email: ${userEmail}`)
    
    if (!userId) {
      console.error(`[ACTIVATE-MINIMAL] ${timestamp} - ❌ User ID não fornecido`)
      return NextResponse.json(
        { error: 'Usuario no identificado' },
        { status: 400 }
      )
    }
    
    // Criar cliente Supabase
    const supabase = await createClient()
    
    // Definir data de expiração (30 dias)
    const expiresAt = new Date()
    expiresAt.setDate(expiresAt.getDate() + 30)
    
    console.log(`[ACTIVATE-MINIMAL] ${timestamp} - Ativando premium até: ${expiresAt.toISOString()}`)
    
    // Verificar se é um ID de Stack Auth (string) ou Supabase (UUID)
    const isStackAuth = !userId.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i)
    const tableName = isStackAuth ? 'stack_profiles' : 'profiles'
    
    console.log(`[ACTIVATE-MINIMAL] ${timestamp} - Tipo de usuário: ${isStackAuth ? 'Stack Auth' : 'Supabase'}`)
    console.log(`[ACTIVATE-MINIMAL] ${timestamp} - Usando tabela: ${tableName}`)
    
    // Primeiro, verificar se o profile existe usando count
    const { count, error: countError } = await supabase
      .from(tableName)
      .select('*', { count: 'exact', head: true })
      .eq('id', userId)
    
    if (countError) {
      console.error(`[ACTIVATE-MINIMAL] ${timestamp} - ❌ Erro ao contar profiles:`, countError)
      return NextResponse.json(
        { error: 'Error al verificar perfil', details: countError.message },
        { status: 500 }
      )
    }
    
    const profileExists = count && count > 0
    console.log(`[ACTIVATE-MINIMAL] ${timestamp} - Profile existe: ${profileExists}`)
    
    if (!profileExists) {
      // Profile não existe, criar
      console.log(`[ACTIVATE-MINIMAL] ${timestamp} - Criando novo profile...`)
      
      const insertData: any = {
        id: userId,
        email: userEmail,
        subscription_tier: 'premium',
        subscription_status: 'active',
        subscription_current_period_end: expiresAt.toISOString(),
        app_verified: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
      
      // Adicionar campos específicos para Stack Auth
      if (isStackAuth) {
        insertData.display_name = null
      }
      
      const { error: insertError } = await supabase
        .from(tableName)
        .insert(insertData)
      
      if (insertError) {
        console.error(`[ACTIVATE-MINIMAL] ${timestamp} - ❌ Erro ao criar profile:`, insertError)
        return NextResponse.json(
          { error: 'Error al crear perfil', details: insertError.message },
          { status: 500 }
        )
      }
      
      console.log(`[ACTIVATE-MINIMAL] ${timestamp} - ✅ Profile criado com sucesso`)
    } else {
      // Profile existe, atualizar
      console.log(`[ACTIVATE-MINIMAL] ${timestamp} - Atualizando profile existente...`)
      
      const { error: updateError } = await supabase
        .from(tableName)
        .update({
          subscription_tier: 'premium',
          subscription_status: 'active',
          subscription_current_period_end: expiresAt.toISOString(),
          app_verified: true,
          updated_at: new Date().toISOString()
        })
        .eq('id', userId)
      
      if (updateError) {
        console.error(`[ACTIVATE-MINIMAL] ${timestamp} - ❌ Erro ao atualizar profile:`, updateError)
        return NextResponse.json(
          { error: 'Error al actualizar perfil', details: updateError.message },
          { status: 500 }
        )
      }
      
      console.log(`[ACTIVATE-MINIMAL] ${timestamp} - ✅ Profile atualizado com sucesso`)
    }
    
    console.log(`[ACTIVATE-MINIMAL] ${timestamp} - ===== ATIVAÇÃO CONCLUÍDA COM SUCESSO =====`)
    
    return NextResponse.json({
      success: true,
      type: 'authenticated',
      auth_provider: isStackAuth ? 'stack' : 'supabase',
      user_id: userId,
      expires_at: expiresAt.toISOString(),
      message: 'Premium ativado com sucesso!'
    })
    
  } catch (error) {
    console.error(`[ACTIVATE-MINIMAL] ${timestamp} - ❌ Erro geral:`, error)
    return NextResponse.json(
      { error: 'Error interno del servidor', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'API de ativação mínima',
    endpoints: {
      POST: '/api/app/activate-minimal - Ativa premium sem logs'
    }
  })
}