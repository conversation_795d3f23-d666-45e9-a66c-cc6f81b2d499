import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

export async function POST(request: NextRequest) {
  const timestamp = new Date().toISOString()
  console.log(`\n[ACTIVATE-FINAL] ${timestamp} - ===== INICIANDO ATIVAÇÃO FINAL =====`)
  
  try {
    const body = await request.json()
    const { userId, userEmail } = body
    
    console.log(`[ACTIVATE-FINAL] ${timestamp} - User ID: ${userId}`)
    console.log(`[ACTIVATE-FINAL] ${timestamp} - User Email: ${userEmail}`)
    
    if (!userId) {
      return NextResponse.json({ error: 'User ID required' }, { status: 400 })
    }
    
    // Criar cliente Supabase
    const supabase = await createClient()
    
    // Data de expiração (30 dias)
    const expiresAt = new Date()
    expiresAt.setDate(expiresAt.getDate() + 30)
    
    // Para Stack Auth users, usar apenas profiles (não stack_profiles)
    console.log(`[ACTIVATE-FINAL] ${timestamp} - Atualizando tabela profiles...`)
    
    // Primeiro, verificar se o profile existe
    const { data: existingProfile, error: selectError } = await supabase
      .from('profiles')
      .select('id, email, created_at')
      .eq('id', userId)
      .maybeSingle()
    
    console.log(`[ACTIVATE-FINAL] ${timestamp} - Resultado da busca:`, { existingProfile, selectError })
    
    if (selectError && selectError.code !== 'PGRST116') {
      console.error(`[ACTIVATE-FINAL] ${timestamp} - Erro ao buscar profile:`, selectError)
      return NextResponse.json({
        error: 'Failed to fetch profile',
        details: selectError.message
      }, { status: 500 })
    }
    
    if (!existingProfile) {
      // Profile não existe, criar
      console.log(`[ACTIVATE-FINAL] ${timestamp} - Profile não existe, criando...`)
      
      const insertData = {
        id: userId,
        email: userEmail || null,
        subscription_tier: 'premium',
        subscription_status: 'active',
        subscription_current_period_end: expiresAt.toISOString(),
        app_verified: true
      }
      
      console.log(`[ACTIVATE-FINAL] ${timestamp} - Dados para inserir:`, insertData)
      
      const { data: newProfile, error: insertError } = await supabase
        .from('profiles')
        .insert(insertData)
        .select()
        .single()
      
      if (insertError) {
        console.error(`[ACTIVATE-FINAL] ${timestamp} - Erro ao criar profile:`, insertError)
        return NextResponse.json({
          error: 'Failed to create profile',
          details: insertError.message,
          code: insertError.code
        }, { status: 500 })
      }
      
      console.log(`[ACTIVATE-FINAL] ${timestamp} - ✅ Profile criado com sucesso:`, newProfile)
      
      return NextResponse.json({
        success: true,
        action: 'created',
        user_id: userId,
        expires_at: expiresAt.toISOString(),
        message: 'Premium activated successfully!'
      })
      
    } else {
      // Profile existe, atualizar
      console.log(`[ACTIVATE-FINAL] ${timestamp} - Profile existe, atualizando...`)
      
      const updateData = {
        subscription_tier: 'premium',
        subscription_status: 'active',
        subscription_current_period_end: expiresAt.toISOString(),
        app_verified: true,
        updated_at: new Date().toISOString()
      }
      
      console.log(`[ACTIVATE-FINAL] ${timestamp} - Dados para atualizar:`, updateData)
      
      const { data: updatedProfile, error: updateError } = await supabase
        .from('profiles')
        .update(updateData)
        .eq('id', userId)
        .select()
        .single()
      
      if (updateError) {
        console.error(`[ACTIVATE-FINAL] ${timestamp} - Erro ao atualizar profile:`, updateError)
        return NextResponse.json({
          error: 'Failed to update profile',
          details: updateError.message,
          code: updateError.code
        }, { status: 500 })
      }
      
      console.log(`[ACTIVATE-FINAL] ${timestamp} - ✅ Profile atualizado com sucesso:`, updatedProfile)
      
      return NextResponse.json({
        success: true,
        action: 'updated',
        user_id: userId,
        expires_at: expiresAt.toISOString(),
        message: 'Premium activated successfully!'
      })
    }
    
  } catch (error) {
    console.error(`[ACTIVATE-FINAL] ${timestamp} - ❌ ERRO GERAL:`, error)
    return NextResponse.json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}