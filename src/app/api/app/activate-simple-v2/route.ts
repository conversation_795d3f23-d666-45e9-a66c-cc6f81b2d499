import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { headers, cookies } from 'next/headers'

export async function POST(request: NextRequest) {
  const timestamp = new Date().toISOString()
  console.log(`\n[ACTIVATE-SIMPLE-V2] ${timestamp} - ===== ATIVAÇÃO SIMPLES V2 INICIADA =====`)
  
  try {
    const body = await request.json()
    const { deviceId, userId, userEmail } = body
    
    console.log(`[ACTIVATE-SIMPLE-V2] ${timestamp} - Dados recebidos:`)
    console.log(`  - Device ID: ${deviceId}`)
    console.log(`  - User ID: ${userId}`)
    console.log(`  - User Email: ${userEmail}`)
    
    if (!userId) {
      console.error(`[ACTIVATE-SIMPLE-V2] ${timestamp} - ❌ User ID não fornecido`)
      return NextResponse.json(
        { error: 'Usuario no identificado' },
        { status: 400 }
      )
    }
    
    // Criar cliente Supabase
    const supabase = await createClient()
    
    // Definir data de expiração (30 dias)
    const expiresAt = new Date()
    expiresAt.setDate(expiresAt.getDate() + 30)
    
    console.log(`[ACTIVATE-SIMPLE-V2] ${timestamp} - Ativando premium até: ${expiresAt.toISOString()}`)
    
    // Verificar se é um ID de Stack Auth (string) ou Supabase (UUID)
    const isStackAuth = !userId.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i)
    const tableName = isStackAuth ? 'stack_profiles' : 'profiles'
    
    console.log(`[ACTIVATE-SIMPLE-V2] ${timestamp} - Tipo de usuário: ${isStackAuth ? 'Stack Auth' : 'Supabase'}`)
    console.log(`[ACTIVATE-SIMPLE-V2] ${timestamp} - Usando tabela: ${tableName}`)
    
    // Primeiro, verificar se o profile existe
    const { data: existingProfile, error: selectError } = await supabase
      .from(tableName)
      .select('id')
      .eq('id', userId)
      .single()
    
    if (selectError && selectError.code !== 'PGRST116') {
      console.error(`[ACTIVATE-SIMPLE-V2] ${timestamp} - ❌ Erro ao buscar profile:`, selectError)
      return NextResponse.json(
        { error: 'Error al buscar perfil', details: selectError.message },
        { status: 500 }
      )
    }
    
    if (!existingProfile) {
      // Profile não existe, criar
      console.log(`[ACTIVATE-SIMPLE-V2] ${timestamp} - Profile não existe, criando...`)
      
      const insertData: any = {
        id: userId,
        email: userEmail,
        subscription_tier: 'premium',
        subscription_status: 'active',
        subscription_current_period_end: expiresAt.toISOString(),
        app_verified: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
      
      // Adicionar campos específicos para Stack Auth
      if (isStackAuth) {
        insertData.display_name = null
      }
      
      const { error: insertError } = await supabase
        .from(tableName)
        .insert(insertData)
      
      if (insertError) {
        console.error(`[ACTIVATE-SIMPLE-V2] ${timestamp} - ❌ Erro ao criar profile:`, insertError)
        return NextResponse.json(
          { error: 'Error al crear perfil', details: insertError.message },
          { status: 500 }
        )
      }
      
      console.log(`[ACTIVATE-SIMPLE-V2] ${timestamp} - ✅ Profile criado com sucesso`)
    } else {
      // Profile existe, atualizar
      console.log(`[ACTIVATE-SIMPLE-V2] ${timestamp} - Profile existe, atualizando...`)
      
      const { error: updateError } = await supabase
        .from(tableName)
        .update({
          subscription_tier: 'premium',
          subscription_status: 'active',
          subscription_current_period_end: expiresAt.toISOString(),
          app_verified: true,
          updated_at: new Date().toISOString()
        })
        .eq('id', userId)
      
      if (updateError) {
        console.error(`[ACTIVATE-SIMPLE-V2] ${timestamp} - ❌ Erro ao atualizar profile:`, updateError)
        return NextResponse.json(
          { error: 'Error al actualizar perfil', details: updateError.message },
          { status: 500 }
        )
      }
      
      console.log(`[ACTIVATE-SIMPLE-V2] ${timestamp} - ✅ Profile atualizado com sucesso`)
    }
    
    // Registrar ativação
    console.log(`[ACTIVATE-SIMPLE-V2] ${timestamp} - Registrando ativação...`)
    
    const { error: logError } = await supabase
      .from('activation_logs')
      .insert({
        user_id: userId,
        activation_type: 'app',
        device_id: deviceId,
        metadata: {
          user_email: userEmail,
          auth_type: isStackAuth ? 'stack' : 'supabase',
          activated_at: timestamp
        }
      })
    
    if (logError) {
      console.warn(`[ACTIVATE-SIMPLE-V2] ${timestamp} - ⚠️ Erro ao registrar log (não crítico):`, logError)
    }
    
    console.log(`[ACTIVATE-SIMPLE-V2] ${timestamp} - ===== ATIVAÇÃO CONCLUÍDA COM SUCESSO =====`)
    
    return NextResponse.json({
      success: true,
      type: 'authenticated',
      auth_provider: isStackAuth ? 'stack' : 'supabase',
      user_id: userId,
      expires_at: expiresAt.toISOString(),
      message: 'Premium ativado com sucesso!'
    })
    
  } catch (error) {
    console.error(`[ACTIVATE-SIMPLE-V2] ${timestamp} - ❌ Erro geral:`, error)
    return NextResponse.json(
      { error: 'Error interno del servidor', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'API de ativação simples v2',
    endpoints: {
      POST: '/api/app/activate-simple-v2 - Ativa premium passando userId e userEmail'
    }
  })
}