import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

export async function POST(request: NextRequest) {
  const timestamp = new Date().toISOString()
  console.log(`\n[ACTIVATE-DIAGNOSTIC] ${timestamp} - ===== DIAGNÓSTICO DE ATIVAÇÃO =====`)
  
  try {
    const body = await request.json()
    const { userId, userEmail } = body
    
    console.log(`[ACTIVATE-DIAGNOSTIC] ${timestamp} - User ID: ${userId}`)
    console.log(`[ACTIVATE-DIAGNOSTIC] ${timestamp} - User Email: ${userEmail}`)
    
    if (!userId) {
      return NextResponse.json({ error: 'User ID required' }, { status: 400 })
    }
    
    // Criar cliente Supabase
    const supabase = await createClient()
    
    const diagnostics = {
      userId,
      userEmail,
      timestamp,
      checks: [] as any[]
    }
    
    // 1. Verificar se conseguimos acessar o banco
    console.log(`[ACTIVATE-DIAGNOSTIC] ${timestamp} - 1. Verificando conexão com banco...`)
    try {
      const { data: testData, error: testError } = await supabase
        .from('profiles')
        .select('count')
        .limit(0)
      
      if (testError) {
        diagnostics.checks.push({
          test: 'database_connection',
          status: 'error',
          message: testError.message,
          code: testError.code
        })
        console.log(`[ACTIVATE-DIAGNOSTIC] ${timestamp} - ❌ Erro ao conectar com banco:`, testError)
      } else {
        diagnostics.checks.push({
          test: 'database_connection',
          status: 'success',
          message: 'Conexão com banco OK'
        })
        console.log(`[ACTIVATE-DIAGNOSTIC] ${timestamp} - ✅ Conexão com banco OK`)
      }
    } catch (e) {
      diagnostics.checks.push({
        test: 'database_connection',
        status: 'exception',
        message: e instanceof Error ? e.message : 'Unknown error'
      })
    }
    
    // 2. Verificar estrutura da tabela profiles
    console.log(`[ACTIVATE-DIAGNOSTIC] ${timestamp} - 2. Verificando estrutura da tabela profiles...`)
    try {
      // Tentar buscar informações sobre a tabela
      const { data: schemaData, error: schemaError } = await supabase
        .from('information_schema.columns')
        .select('column_name, data_type, is_nullable')
        .eq('table_name', 'profiles')
        .eq('table_schema', 'public')
      
      if (schemaError) {
        diagnostics.checks.push({
          test: 'table_structure',
          status: 'error',
          message: 'Não foi possível verificar estrutura da tabela',
          error: schemaError.message
        })
        console.log(`[ACTIVATE-DIAGNOSTIC] ${timestamp} - ❌ Erro ao verificar estrutura:`, schemaError)
      } else if (schemaData && schemaData.length > 0) {
        diagnostics.checks.push({
          test: 'table_structure',
          status: 'success',
          columns: schemaData,
          message: `Tabela profiles tem ${schemaData.length} colunas`
        })
        console.log(`[ACTIVATE-DIAGNOSTIC] ${timestamp} - ✅ Estrutura da tabela:`, schemaData)
      } else {
        diagnostics.checks.push({
          test: 'table_structure',
          status: 'warning',
          message: 'Tabela profiles pode não existir ou não há acesso'
        })
        console.log(`[ACTIVATE-DIAGNOSTIC] ${timestamp} - ⚠️ Tabela profiles não encontrada`)
      }
    } catch (e) {
      diagnostics.checks.push({
        test: 'table_structure',
        status: 'exception',
        message: e instanceof Error ? e.message : 'Unknown error'
      })
    }
    
    // 3. Verificar se o usuário já existe
    console.log(`[ACTIVATE-DIAGNOSTIC] ${timestamp} - 3. Verificando se usuário existe...`)
    try {
      const { data: userData, error: userError, count } = await supabase
        .from('profiles')
        .select('id, email, subscription_status, subscription_tier', { count: 'exact' })
        .eq('id', userId)
      
      if (userError) {
        diagnostics.checks.push({
          test: 'user_exists',
          status: 'error',
          message: userError.message,
          code: userError.code
        })
        console.log(`[ACTIVATE-DIAGNOSTIC] ${timestamp} - ❌ Erro ao buscar usuário:`, userError)
      } else {
        diagnostics.checks.push({
          test: 'user_exists',
          status: 'success',
          exists: count > 0,
          userData: userData?.[0] || null,
          message: count > 0 ? 'Usuário encontrado' : 'Usuário não existe'
        })
        console.log(`[ACTIVATE-DIAGNOSTIC] ${timestamp} - ✅ Resultado da busca:`, { count, userData })
      }
    } catch (e) {
      diagnostics.checks.push({
        test: 'user_exists',
        status: 'exception',
        message: e instanceof Error ? e.message : 'Unknown error'
      })
    }
    
    // 4. Tentar criar um profile de teste
    console.log(`[ACTIVATE-DIAGNOSTIC] ${timestamp} - 4. Tentando operação de teste...`)
    try {
      const testId = `test-${Date.now()}`
      const expiresAt = new Date()
      expiresAt.setDate(expiresAt.getDate() + 30)
      
      // Primeiro tentar inserir
      const { data: insertData, error: insertError } = await supabase
        .from('profiles')
        .insert({
          id: testId,
          email: '<EMAIL>',
          subscription_tier: 'free',
          subscription_status: 'active',
          created_at: new Date().toISOString()
        })
        .select()
      
      if (insertError) {
        diagnostics.checks.push({
          test: 'insert_test',
          status: 'error',
          message: insertError.message,
          code: insertError.code,
          hint: 'Pode indicar problema de permissões ou estrutura'
        })
        console.log(`[ACTIVATE-DIAGNOSTIC] ${timestamp} - ❌ Erro ao inserir teste:`, insertError)
      } else {
        // Se conseguiu inserir, deletar
        const { error: deleteError } = await supabase
          .from('profiles')
          .delete()
          .eq('id', testId)
        
        diagnostics.checks.push({
          test: 'insert_test',
          status: 'success',
          message: 'Insert e delete funcionando',
          inserted: insertData,
          deleted: !deleteError
        })
        console.log(`[ACTIVATE-DIAGNOSTIC] ${timestamp} - ✅ Teste de insert/delete OK`)
      }
    } catch (e) {
      diagnostics.checks.push({
        test: 'insert_test',
        status: 'exception',
        message: e instanceof Error ? e.message : 'Unknown error'
      })
    }
    
    // 5. Verificar RLS policies
    console.log(`[ACTIVATE-DIAGNOSTIC] ${timestamp} - 5. Verificando RLS policies...`)
    try {
      const { data: rlsData, error: rlsError } = await supabase
        .rpc('check_rls_status', { table_name: 'profiles' })
      
      if (rlsError) {
        // RLS pode estar ativado mas não temos função para verificar
        diagnostics.checks.push({
          test: 'rls_status',
          status: 'info',
          message: 'Não foi possível verificar status RLS (normal)',
          error: rlsError.message
        })
      } else {
        diagnostics.checks.push({
          test: 'rls_status',
          status: 'success',
          data: rlsData
        })
      }
    } catch (e) {
      // Ignorar erro de RLS check
    }
    
    // 6. Tentar a operação real de ativação
    console.log(`[ACTIVATE-DIAGNOSTIC] ${timestamp} - 6. Tentando ativação real...`)
    try {
      const expiresAt = new Date()
      expiresAt.setDate(expiresAt.getDate() + 30)
      
      // Usar upsert em vez de verificar + insert/update
      const { data: upsertData, error: upsertError } = await supabase
        .from('profiles')
        .upsert({
          id: userId,
          email: userEmail || null,
          subscription_tier: 'premium',
          subscription_status: 'active',
          subscription_current_period_end: expiresAt.toISOString(),
          app_verified: true,
          updated_at: new Date().toISOString()
        }, {
          onConflict: 'id'
        })
        .select()
        .single()
      
      if (upsertError) {
        diagnostics.checks.push({
          test: 'real_activation',
          status: 'error',
          message: upsertError.message,
          code: upsertError.code,
          details: upsertError
        })
        console.log(`[ACTIVATE-DIAGNOSTIC] ${timestamp} - ❌ Erro na ativação real:`, upsertError)
      } else {
        diagnostics.checks.push({
          test: 'real_activation',
          status: 'success',
          message: 'Ativação bem sucedida!',
          data: upsertData
        })
        console.log(`[ACTIVATE-DIAGNOSTIC] ${timestamp} - ✅ Ativação real OK:`, upsertData)
        
        // Se chegou aqui, a ativação funcionou!
        return NextResponse.json({
          success: true,
          message: 'Premium activated successfully!',
          user_id: userId,
          expires_at: expiresAt.toISOString(),
          diagnostics
        })
      }
    } catch (e) {
      diagnostics.checks.push({
        test: 'real_activation',
        status: 'exception',
        message: e instanceof Error ? e.message : 'Unknown error'
      })
    }
    
    // Retornar diagnóstico completo
    console.log(`[ACTIVATE-DIAGNOSTIC] ${timestamp} - Diagnóstico completo:`, diagnostics)
    
    return NextResponse.json({
      success: false,
      error: 'Activation failed - see diagnostics',
      diagnostics
    })
    
  } catch (error) {
    console.error(`[ACTIVATE-DIAGNOSTIC] ${timestamp} - ❌ ERRO GERAL:`, error)
    return NextResponse.json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}