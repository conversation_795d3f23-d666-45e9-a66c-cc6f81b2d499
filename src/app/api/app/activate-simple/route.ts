import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

export async function POST(request: NextRequest) {
  const timestamp = new Date().toISOString()
  console.log(`\n[ACTIVATE-SIMPLE] ${timestamp} - ===== ATIVAÇÃO SIMPLES =====`)
  
  try {
    const body = await request.json()
    const { userId, userEmail } = body
    
    console.log(`[ACTIVATE-SIMPLE] ${timestamp} - User ID: ${userId}`)
    console.log(`[ACTIVATE-SIMPLE] ${timestamp} - User Email: ${userEmail}`)
    
    if (!userId) {
      return NextResponse.json({ error: 'User ID required' }, { status: 400 })
    }
    
    // Criar cliente Supabase com service role key (bypass RLS)
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!
    
    if (!supabaseUrl || !supabaseServiceKey) {
      console.error(`[ACTIVATE-SIMPLE] ${timestamp} - Missing Supabase environment variables`)
      return NextResponse.json({ error: 'Server configuration error' }, { status: 500 })
    }
    
    const supabase = createClient(supabaseUrl, supabaseServiceKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    })
    
    // Data de expiração (30 dias)
    const expiresAt = new Date()
    expiresAt.setDate(expiresAt.getDate() + 30)
    
    // Atualizar ou criar em stack_profiles com campos básicos
    console.log(`[ACTIVATE-SIMPLE] ${timestamp} - Atualizando stack_profiles com campos básicos...`)
    
    // Campos básicos que sabemos que existem
    const basicData = {
      id: userId,
      email: userEmail || null,
      subscription_tier: 'premium',
      subscription_status: 'active',
      subscription_current_period_end: expiresAt.toISOString(),
      updated_at: new Date().toISOString()
    }
    
    console.log(`[ACTIVATE-SIMPLE] ${timestamp} - Dados básicos:`, basicData)
    
    // Usar upsert para criar ou atualizar
    const { data: profile, error: profileError } = await supabase
      .from('stack_profiles')
      .upsert(basicData, {
        onConflict: 'id',
        ignoreDuplicates: false
      })
      .select()
      .single()
    
    if (profileError) {
      console.error(`[ACTIVATE-SIMPLE] ${timestamp} - Erro ao upsert stack_profiles:`, profileError)
      
      // Se falhou em stack_profiles, criar registro de acesso anônimo
      console.log(`[ACTIVATE-SIMPLE] ${timestamp} - Criando acesso alternativo em anonymous_access...`)
      
      const { data: anonAccess, error: anonError } = await supabase
        .from('anonymous_access')
        .upsert({
          user_id: userId,
          session_id: `premium-${Date.now()}`,
          expires_at: expiresAt.toISOString(),
          is_active: true,
          device_info: { 
            source: 'app_activation',
            email: userEmail,
            activated_at: timestamp
          }
        }, {
          onConflict: 'user_id'
        })
        .select()
        .single()
      
      if (anonError) {
        console.error(`[ACTIVATE-SIMPLE] ${timestamp} - Erro ao criar anonymous_access:`, anonError)
        return NextResponse.json({
          error: 'Failed to activate premium',
          details: anonError.message
        }, { status: 500 })
      }
      
      console.log(`[ACTIVATE-SIMPLE] ${timestamp} - ✅ Acesso anônimo criado com sucesso`)
      
      return NextResponse.json({
        success: true,
        message: 'Premium activated successfully!',
        user_id: userId,
        expires_at: expiresAt.toISOString(),
        access_type: 'anonymous_access'
      })
    }
    
    console.log(`[ACTIVATE-SIMPLE] ${timestamp} - ✅ Stack profile atualizado:`, profile)
    
    // Também criar anonymous_access para garantir
    await supabase
      .from('anonymous_access')
      .upsert({
        user_id: userId,
        session_id: `premium-${Date.now()}`,
        expires_at: expiresAt.toISOString(),
        is_active: true,
        device_info: { 
          source: 'app_activation',
          email: userEmail,
          activated_at: timestamp
        }
      }, {
        onConflict: 'user_id'
      })
    
    return NextResponse.json({
      success: true,
      message: 'Premium activated successfully!',
      user_id: userId,
      expires_at: expiresAt.toISOString(),
      access_type: 'stack_profiles'
    })
    
  } catch (error) {
    console.error(`[ACTIVATE-SIMPLE] ${timestamp} - ❌ ERRO GERAL:`, error)
    return NextResponse.json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}