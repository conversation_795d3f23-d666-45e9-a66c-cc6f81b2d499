import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

export async function POST(request: NextRequest) {
  const timestamp = new Date().toISOString()
  console.log(`\n[ACTIVATE-FIXED] ${timestamp} - ===== ATIVAÇÃO CORRIGIDA =====`)
  
  try {
    const body = await request.json()
    const { userId, userEmail } = body
    
    console.log(`[ACTIVATE-FIXED] ${timestamp} - User ID: ${userId}`)
    console.log(`[ACTIVATE-FIXED] ${timestamp} - User Email: ${userEmail}`)
    
    if (!userId) {
      return NextResponse.json({ error: 'User ID required' }, { status: 400 })
    }
    
    // Criar cliente Supabase
    const supabase = await createClient()
    
    // Data de expiração (30 dias)
    const expiresAt = new Date()
    expiresAt.setDate(expiresAt.getDate() + 30)
    
    // Para Stack Auth users, vamos usar a tabela stack_profiles que já tem app_verified
    console.log(`[ACTIVATE-FIXED] ${timestamp} - Tentando atualizar stack_profiles...`)
    
    // Primeiro verificar se existe em stack_profiles
    const { data: stackProfile, error: stackError } = await supabase
      .from('stack_profiles')
      .select('id, email, subscription_tier, subscription_status')
      .eq('id', userId)
      .single()
    
    if (stackProfile) {
      // Existe em stack_profiles, atualizar
      console.log(`[ACTIVATE-FIXED] ${timestamp} - Encontrado em stack_profiles, atualizando...`)
      
      const { data: updated, error: updateError } = await supabase
        .from('stack_profiles')
        .update({
          subscription_tier: 'premium',
          subscription_status: 'active',
          subscription_current_period_end: expiresAt.toISOString(),
          app_verified: true,
          app_verified_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('id', userId)
        .select()
        .single()
      
      if (updateError) {
        console.error(`[ACTIVATE-FIXED] ${timestamp} - Erro ao atualizar stack_profiles:`, updateError)
        return NextResponse.json({
          error: 'Failed to update profile',
          details: updateError.message
        }, { status: 500 })
      }
      
      console.log(`[ACTIVATE-FIXED] ${timestamp} - ✅ Stack profile atualizado com sucesso`)
      
      return NextResponse.json({
        success: true,
        message: 'Premium activated successfully!',
        user_id: userId,
        expires_at: expiresAt.toISOString(),
        profile_type: 'stack_profiles'
      })
    }
    
    // Se não existe em stack_profiles, verificar profiles (Supabase auth)
    console.log(`[ACTIVATE-FIXED] ${timestamp} - Não encontrado em stack_profiles, verificando profiles...`)
    
    // Para profiles, vamos criar um registro básico sem app_verified se a coluna não existir
    const profileData: any = {
      id: userId,
      email: userEmail || null,
      subscription_tier: 'premium',
      subscription_status: 'active',
      subscription_current_period_end: expiresAt.toISOString(),
      updated_at: new Date().toISOString()
    }
    
    // Usar upsert para criar ou atualizar
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .upsert(profileData, {
        onConflict: 'id',
        ignoreDuplicates: false
      })
      .select()
      .single()
    
    if (profileError) {
      console.error(`[ACTIVATE-FIXED] ${timestamp} - Erro ao upsert profiles:`, profileError)
      
      // Se falhou, tentar sem campos que podem não existir
      console.log(`[ACTIVATE-FIXED] ${timestamp} - Tentando upsert simplificado...`)
      
      const simpleData = {
        id: userId,
        subscription_tier: 'premium',
        subscription_expires_at: expiresAt
      }
      
      const { data: simpleProfile, error: simpleError } = await supabase
        .from('profiles')
        .upsert(simpleData, {
          onConflict: 'id'
        })
        .select()
        .single()
      
      if (simpleError) {
        console.error(`[ACTIVATE-FIXED] ${timestamp} - Erro no upsert simplificado:`, simpleError)
        return NextResponse.json({
          error: 'Failed to activate premium',
          details: simpleError.message
        }, { status: 500 })
      }
      
      console.log(`[ACTIVATE-FIXED] ${timestamp} - ✅ Profile criado com campos básicos`)
      
      return NextResponse.json({
        success: true,
        message: 'Premium activated successfully!',
        user_id: userId,
        expires_at: expiresAt.toISOString(),
        profile_type: 'profiles_simple'
      })
    }
    
    console.log(`[ACTIVATE-FIXED] ${timestamp} - ✅ Profile atualizado com sucesso`)
    
    return NextResponse.json({
      success: true,
      message: 'Premium activated successfully!',
      user_id: userId,
      expires_at: expiresAt.toISOString(),
      profile_type: 'profiles'
    })
    
  } catch (error) {
    console.error(`[ACTIVATE-FIXED] ${timestamp} - ❌ ERRO GERAL:`, error)
    return NextResponse.json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}