import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { headers, cookies } from 'next/headers'
import { stackServerApp } from '@/lib/auth/stack-server'

export async function POST(request: NextRequest) {
  const timestamp = new Date().toISOString()
  console.log(`\n[ACTIVATE-TEST] ${timestamp} - ===== TESTE DE ATIVAÇÃO INICIADO =====`)
  
  try {
    const body = await request.json()
    const { deviceId } = body
    
    console.log(`[ACTIVATE-TEST] ${timestamp} - Device ID:`, deviceId)
    
    // Verificar cookies primeiro
    const cookieStore = await cookies()
    const allCookies = cookieStore.getAll()
    console.log(`[ACTIVATE-TEST] ${timestamp} - Todos os cookies:`)
    allCookies.forEach(cookie => {
      console.log(`  - ${cookie.name}: ${cookie.value.substring(0, 50)}...`)
    })
    
    // Verificar headers
    const headersList = await headers()
    const authHeader = headersList.get('authorization')
    console.log(`[ACTIVATE-TEST] ${timestamp} - Authorization header:`, authHeader ? 'presente' : 'ausente')
    
    // Criar cliente Supabase
    const supabase = await createClient()
    
    // Verificar se há usuário autenticado
    console.log(`[ACTIVATE-TEST] ${timestamp} - Verificando autenticação...`)
    
    // Tentar pegar o usuário do Stack Auth
    let stackUser = null
    try {
      console.log(`[ACTIVATE-TEST] ${timestamp} - Tentando obter usuário do Stack Auth...`)
      stackUser = await stackServerApp.getUser()
      console.log(`[ACTIVATE-TEST] ${timestamp} - Usuário Stack Auth encontrado:`, !!stackUser)
      if (stackUser) {
        console.log(`[ACTIVATE-TEST] ${timestamp} - Stack User ID:`, stackUser.id)
        console.log(`[ACTIVATE-TEST] ${timestamp} - Stack User Email:`, stackUser.primaryEmail)
      }
    } catch (stackError) {
      console.error(`[ACTIVATE-TEST] ${timestamp} - ❌ Erro ao obter usuário do Stack Auth:`, stackError)
    }
    
    // Também tentar Supabase Auth como fallback
    const { data: { user: supabaseUser }, error: authError } = await supabase.auth.getUser()
    
    if (authError) {
      console.error(`[ACTIVATE-TEST] ${timestamp} - ❌ Erro ao obter usuário do Supabase:`, authError)
    }
    
    console.log(`[ACTIVATE-TEST] ${timestamp} - Usuário Supabase Auth:`, !!supabaseUser)
    
    // Usar o usuário que foi encontrado
    const user = stackUser || supabaseUser
    
    if (user) {
      const userId = stackUser ? stackUser.id : user.id
      const userEmail = stackUser ? stackUser.primaryEmail : user.email
      
      console.log(`[ACTIVATE-TEST] ${timestamp} - Usuário ID:`, userId)
      console.log(`[ACTIVATE-TEST] ${timestamp} - Usuário Email:`, userEmail)
      console.log(`[ACTIVATE-TEST] ${timestamp} - Tipo de usuário:`, stackUser ? 'Stack Auth' : 'Supabase Auth')
      
      // Atualizar diretamente a tabela profiles para premium
      const expiresAt = new Date()
      expiresAt.setDate(expiresAt.getDate() + 30)
      
      console.log(`[ACTIVATE-TEST] ${timestamp} - Atualizando profile para premium até:`, expiresAt)
      
      // Primeiro, verificar se o profile existe (para Stack Auth, usar stack_profiles)
      const tableName = stackUser ? 'stack_profiles' : 'profiles'
      console.log(`[ACTIVATE-TEST] ${timestamp} - Verificando profile na tabela:`, tableName)
      
      const { data: existingProfile, error: selectError } = await supabase
        .from(tableName)
        .select('id')
        .eq('id', userId)
        .single()
      
      if (selectError) {
        console.error(`[ACTIVATE-TEST] ${timestamp} - ❌ Erro ao buscar profile:`, selectError)
        
        // Se não existe, criar
        if (selectError.code === 'PGRST116') {
          console.log(`[ACTIVATE-TEST] ${timestamp} - Profile não existe, criando...`)
          
          const insertData = stackUser ? {
            id: userId,
            email: userEmail,
            display_name: stackUser.displayName || null,
            subscription_tier: 'premium',
            subscription_status: 'active',
            subscription_current_period_end: expiresAt.toISOString(),
            app_verified: true,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          } : {
            id: userId,
            email: userEmail,
            subscription_tier: 'premium',
            subscription_status: 'active',
            subscription_current_period_end: expiresAt.toISOString(),
            app_verified: true,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          }
          
          const { error: insertError } = await supabase
            .from(tableName)
            .insert(insertData)
          
          if (insertError) {
            console.error(`[ACTIVATE-TEST] ${timestamp} - ❌ Erro ao criar profile:`, insertError)
            return NextResponse.json(
              { error: 'Error al crear perfil', details: insertError.message },
              { status: 500 }
            )
          }
          
          console.log(`[ACTIVATE-TEST] ${timestamp} - ✅ Profile criado com sucesso`)
        } else {
          return NextResponse.json(
            { error: 'Error al buscar perfil', details: selectError.message },
            { status: 500 }
          )
        }
      } else {
        // Profile existe, atualizar
        console.log(`[ACTIVATE-TEST] ${timestamp} - Profile existe, atualizando...`)
        
        const { error: updateError } = await supabase
          .from(tableName)
          .update({
            subscription_tier: 'premium',
            subscription_status: 'active',
            subscription_current_period_end: expiresAt.toISOString(),
            app_verified: true,
            updated_at: new Date().toISOString()
          })
          .eq('id', userId)
        
        if (updateError) {
          console.error(`[ACTIVATE-TEST] ${timestamp} - ❌ Erro ao atualizar profile:`, updateError)
          return NextResponse.json(
            { error: 'Error al actualizar perfil', details: updateError.message },
            { status: 500 }
          )
        }
        
        console.log(`[ACTIVATE-TEST] ${timestamp} - ✅ Profile atualizado com sucesso`)
      }
      
      console.log(`[ACTIVATE-TEST] ${timestamp} - ===== ATIVAÇÃO CONCLUÍDA COM SUCESSO =====`)
      
      return NextResponse.json({
        success: true,
        type: 'authenticated',
        auth_provider: stackUser ? 'stack' : 'supabase',
        user_id: userId,
        expires_at: expiresAt.toISOString(),
        message: 'Premium ativado com sucesso!'
      })
    }
    
    // Se não há usuário autenticado
    console.log(`[ACTIVATE-TEST] ${timestamp} - ⚠️ Usuário não autenticado`)
    console.log(`[ACTIVATE-TEST] ${timestamp} - Por enquanto, retornando erro para testes`)
    
    return NextResponse.json(
      { 
        error: 'Usuario no autenticado',
        message: 'Debes iniciar sesión para activar el premium'
      },
      { status: 401 }
    )
    
  } catch (error) {
    console.error(`[ACTIVATE-TEST] ${timestamp} - ❌ Erro geral:`, error)
    return NextResponse.json(
      { error: 'Error interno del servidor', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'API de teste de ativação',
    endpoints: {
      POST: '/api/app/activate-test - Ativa premium para usuário autenticado'
    }
  })
}