import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

export async function POST(request: NextRequest) {
  const timestamp = new Date().toISOString()
  console.log(`\n[ACTIVATE-ULTRA-SIMPLE] ${timestamp} - ===== INICIANDO =====`)
  
  try {
    const body = await request.json()
    const { userId, userEmail } = body
    
    console.log(`[ACTIVATE-ULTRA-SIMPLE] ${timestamp} - User ID: ${userId}`)
    console.log(`[ACTIVATE-ULTRA-SIMPLE] ${timestamp} - User Email: ${userEmail}`)
    
    if (!userId) {
      return NextResponse.json({ error: 'User ID required' }, { status: 400 })
    }
    
    // Criar cliente Supabase com service role para bypass RLS
    const supabase = await createClient()
    
    // Data de expiração (30 dias)
    const expiresAt = new Date()
    expiresAt.setDate(expiresAt.getDate() + 30)
    
    // Determinar tabela baseado no formato do ID
    const isUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(userId)
    const tableName = isUUID ? 'profiles' : 'stack_profiles'
    
    console.log(`[ACTIVATE-ULTRA-SIMPLE] ${timestamp} - Tabela: ${tableName}`)
    console.log(`[ACTIVATE-ULTRA-SIMPLE] ${timestamp} - Atualizando para premium...`)
    
    // Tentar atualizar primeiro
    const { data: updateData, error: updateError } = await supabase
      .from(tableName)
      .update({
        subscription_tier: 'premium',
        subscription_status: 'active',
        subscription_current_period_end: expiresAt.toISOString(),
        app_verified: true,
        updated_at: new Date().toISOString()
      })
      .eq('id', userId)
      .select()
    
    if (updateError) {
      console.error(`[ACTIVATE-ULTRA-SIMPLE] ${timestamp} - Erro ao atualizar:`, updateError)
      
      // Se falhou, tentar criar
      console.log(`[ACTIVATE-ULTRA-SIMPLE] ${timestamp} - Tentando criar novo profile...`)
      
      const insertData: any = {
        id: userId,
        email: userEmail || null,
        subscription_tier: 'premium',
        subscription_status: 'active', 
        subscription_current_period_end: expiresAt.toISOString(),
        app_verified: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
      
      // Adicionar campos específicos para stack_profiles
      if (!isUUID) {
        insertData.display_name = null
      }
      
      const { data: insertData2, error: insertError } = await supabase
        .from(tableName)
        .insert(insertData)
        .select()
      
      if (insertError) {
        console.error(`[ACTIVATE-ULTRA-SIMPLE] ${timestamp} - Erro ao inserir:`, insertError)
        return NextResponse.json({
          error: 'Failed to activate premium',
          details: insertError.message
        }, { status: 500 })
      }
      
      console.log(`[ACTIVATE-ULTRA-SIMPLE] ${timestamp} - ✅ Profile criado:`, insertData2)
    } else {
      console.log(`[ACTIVATE-ULTRA-SIMPLE] ${timestamp} - ✅ Profile atualizado:`, updateData)
    }
    
    console.log(`[ACTIVATE-ULTRA-SIMPLE] ${timestamp} - ===== SUCESSO =====`)
    
    return NextResponse.json({
      success: true,
      user_id: userId,
      expires_at: expiresAt.toISOString(),
      message: 'Premium activated successfully!'
    })
    
  } catch (error) {
    console.error(`[ACTIVATE-ULTRA-SIMPLE] ${timestamp} - ERRO GERAL:`, error)
    return NextResponse.json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}