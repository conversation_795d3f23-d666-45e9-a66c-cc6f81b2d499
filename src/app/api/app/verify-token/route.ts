import { NextRequest, NextResponse } from 'next/server'
import { createServiceClient } from '@/lib/supabase/server'
import { cookies } from 'next/headers'

export async function POST(request: NextRequest) {
  const timestamp = new Date().toISOString()
  console.log(`[VERIFY-APP-TOKEN] ${timestamp} - Request received`)
  
  try {
    const { token } = await request.json()
    
    if (!token) {
      return NextResponse.json({ 
        error: 'Token es requerido' 
      }, { status: 400 })
    }
    
    console.log(`[VERIFY-APP-TOKEN] ${timestamp} - Verifying token: ${token}`)
    
    const supabase = createServiceClient()
    
    // Buscar token no banco
    const { data: tokenData, error: tokenError } = await supabase
      .from('app_tokens')
      .select('*')
      .eq('token', token)
      .single()
    
    if (tokenError || !tokenData) {
      console.log(`[VERIFY-APP-TOKEN] ${timestamp} - Token not found: ${token}`)
      return NextResponse.json({ 
        error: 'Token inválido o no encontrado' 
      }, { status: 404 })
    }
    
    // Verificar se já foi usado
    if (tokenData.used) {
      console.log(`[VERIFY-APP-TOKEN] ${timestamp} - Token already used: ${token}`)
      return NextResponse.json({ 
        error: 'Este enlace ya fue utilizado' 
      }, { status: 400 })
    }
    
    // Verificar se expirou
    if (new Date(tokenData.expires_at) < new Date()) {
      console.log(`[VERIFY-APP-TOKEN] ${timestamp} - Token expired: ${token}`)
      return NextResponse.json({ 
        error: 'Este enlace ha expirado' 
      }, { status: 400 })
    }
    
    // Obter IP do cliente
    const forwarded = request.headers.get('x-forwarded-for')
    const ip = forwarded ? forwarded.split(',')[0] : 'unknown'
    
    // Criar sessão anônima para o usuário
    const sessionId = crypto.randomUUID()
    const futureDate = new Date()
    futureDate.setDate(futureDate.getDate() + 30) // 30 dias de acesso
    
    // Criar registro de acesso anônimo
    const { data: anonymousProfile, error: profileError } = await supabase
      .from('anonymous_access')
      .insert({
        session_id: sessionId,
        token_id: tokenData.id,
        activated_at: new Date().toISOString(),
        expires_at: futureDate.toISOString(),
        ip_address: ip,
        platform: tokenData.platform,
        metadata: {
          app_version: tokenData.app_version,
          activation_source: 'app_token'
        }
      })
      .select()
      .single()
    
    if (profileError) {
      console.error(`[VERIFY-APP-TOKEN] ${timestamp} - Error creating anonymous access:`, profileError)
      // Continuar mesmo se falhar, pois vamos usar cookie
    }
    
    // Marcar token como usado
    const { error: updateError } = await supabase
      .from('app_tokens')
      .update({
        used: true,
        used_at: new Date().toISOString(),
        used_by_user_id: sessionId
      })
      .eq('token', token)
    
    if (updateError) {
      console.error(`[VERIFY-APP-TOKEN] ${timestamp} - Error updating token:`, updateError)
    }
    
    // Criar cookie de sessão
    const cookieStore = await cookies()
    cookieStore.set({
      name: 'app_access_token',
      value: sessionId,
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      path: '/',
      maxAge: 30 * 24 * 60 * 60 // 30 dias em segundos
    })
    
    // Também salvar no localStorage via resposta
    const response = NextResponse.json({
      success: true,
      message: 'Acceso activado exitosamente',
      expires_at: futureDate.toISOString(),
      session_id: sessionId
    })
    
    // Adicionar header para cliente salvar no localStorage
    response.headers.set('X-Session-ID', sessionId)
    response.headers.set('X-Expires-At', futureDate.toISOString())
    
    console.log(`[VERIFY-APP-TOKEN] ${timestamp} - Token verified successfully: ${token}`)
    
    return response
    
  } catch (error) {
    console.error(`[VERIFY-APP-TOKEN] ${timestamp} - Unexpected error:`, error)
    return NextResponse.json({ 
      error: 'Error interno del servidor' 
    }, { status: 500 })
  }
}