import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

export async function POST(request: NextRequest) {
  const timestamp = new Date().toISOString()
  console.log(`\n[ACTIVATE-DEBUG] ${timestamp} - ===== DEBUG ACTIVATION =====`)
  
  try {
    const body = await request.json()
    const { userId, userEmail } = body
    
    console.log(`[ACTIVATE-DEBUG] ${timestamp} - User ID: ${userId}`)
    console.log(`[ACTIVATE-DEBUG] ${timestamp} - User Email: ${userEmail}`)
    
    if (!userId) {
      return NextResponse.json({ error: 'User ID required' }, { status: 400 })
    }
    
    // Criar cliente Supabase
    const supabase = await createClient()
    
    // Primeiro, verificar quais tabelas existem
    console.log(`[ACTIVATE-DEBUG] ${timestamp} - Verificando tabelas disponíveis...`)
    
    const { data: tables, error: tablesError } = await supabase
      .from('profiles')
      .select('id')
      .limit(0)
    
    if (tablesError) {
      console.log(`[ACTIVATE-DEBUG] ${timestamp} - Tabela 'profiles' não existe ou erro:`, tablesError.message)
    } else {
      console.log(`[ACTIVATE-DEBUG] ${timestamp} - Tabela 'profiles' existe`)
    }
    
    // Tentar buscar/criar profile direto na tabela profiles
    console.log(`[ACTIVATE-DEBUG] ${timestamp} - Verificando se profile existe...`)
    
    const { data: existingProfile, error: selectError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', userId)
      .single()
    
    const expiresAt = new Date()
    expiresAt.setDate(expiresAt.getDate() + 30)
    
    if (selectError && selectError.code === 'PGRST116') {
      // Profile não existe, criar
      console.log(`[ACTIVATE-DEBUG] ${timestamp} - Profile não existe, criando novo...`)
      
      const { data: newProfile, error: insertError } = await supabase
        .from('profiles')
        .insert({
          id: userId,
          email: userEmail || null,
          subscription_tier: 'premium',
          subscription_status: 'active',
          subscription_current_period_end: expiresAt.toISOString(),
          app_verified: true,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()
        .single()
      
      if (insertError) {
        console.error(`[ACTIVATE-DEBUG] ${timestamp} - Erro ao criar profile:`, insertError)
        return NextResponse.json({
          error: 'Failed to create profile',
          details: insertError.message,
          code: insertError.code
        }, { status: 500 })
      }
      
      console.log(`[ACTIVATE-DEBUG] ${timestamp} - Profile criado:`, newProfile)
      
      return NextResponse.json({
        success: true,
        action: 'created',
        profile: newProfile,
        expires_at: expiresAt.toISOString()
      })
      
    } else if (existingProfile) {
      // Profile existe, atualizar
      console.log(`[ACTIVATE-DEBUG] ${timestamp} - Profile existe, atualizando...`)
      console.log(`[ACTIVATE-DEBUG] ${timestamp} - Profile atual:`, existingProfile)
      
      const { data: updatedProfile, error: updateError } = await supabase
        .from('profiles')
        .update({
          subscription_tier: 'premium',
          subscription_status: 'active',
          subscription_current_period_end: expiresAt.toISOString(),
          app_verified: true,
          updated_at: new Date().toISOString()
        })
        .eq('id', userId)
        .select()
        .single()
      
      if (updateError) {
        console.error(`[ACTIVATE-DEBUG] ${timestamp} - Erro ao atualizar profile:`, updateError)
        return NextResponse.json({
          error: 'Failed to update profile',
          details: updateError.message,
          code: updateError.code
        }, { status: 500 })
      }
      
      console.log(`[ACTIVATE-DEBUG] ${timestamp} - Profile atualizado:`, updatedProfile)
      
      return NextResponse.json({
        success: true,
        action: 'updated',
        profile: updatedProfile,
        expires_at: expiresAt.toISOString()
      })
      
    } else {
      // Erro ao buscar
      console.error(`[ACTIVATE-DEBUG] ${timestamp} - Erro ao buscar profile:`, selectError)
      return NextResponse.json({
        error: 'Failed to fetch profile',
        details: selectError?.message,
        code: selectError?.code
      }, { status: 500 })
    }
    
  } catch (error) {
    console.error(`[ACTIVATE-DEBUG] ${timestamp} - ERRO GERAL:`, error)
    return NextResponse.json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}