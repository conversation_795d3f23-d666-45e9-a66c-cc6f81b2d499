import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

export async function GET(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  const userId = params.userId
  console.log(`[CHECK-ACCESS] Checking access for user: ${userId}`)
  
  try {
    // Criar cliente Supabase com service role key
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!
    
    const supabase = createClient(supabaseUrl, supabaseServiceKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    })
    
    // Verificar stack_profiles
    const { data: stackProfile, error: stackError } = await supabase
      .from('stack_profiles')
      .select('*')
      .eq('id', userId)
      .single()
    
    // Verificar anonymous_access
    const { data: anonAccess, error: anonError } = await supabase
      .from('anonymous_access')
      .select('*')
      .eq('user_id', userId)
      .gte('expires_at', new Date().toISOString())
      .eq('is_active', true)
      .single()
    
    const now = new Date()
    
    return NextResponse.json({
      success: true,
      user_id: userId,
      timestamp: now.toISOString(),
      stack_profile: stackProfile || null,
      anonymous_access: anonAccess || null,
      has_premium: stackProfile && 
        stackProfile.subscription_tier === 'premium' && 
        stackProfile.subscription_status === 'active' &&
        new Date(stackProfile.subscription_current_period_end) > now,
      has_anonymous_access: anonAccess && 
        new Date(anonAccess.expires_at) > now
    })
    
  } catch (error) {
    console.error('[CHECK-ACCESS] Error:', error)
    return NextResponse.json({
      error: 'Failed to check access',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}