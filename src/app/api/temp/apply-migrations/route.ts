import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

export async function GET(request: NextRequest) {
  console.log('[APPLY-MIGRATIONS] Iniciando aplicação de migrações...')
  
  try {
    const supabase = await createClient()
    const results = []
    
    // Migration 1: Alterar anonymous_access
    console.log('[APPLY-MIGRATIONS] Tentando alterar anonymous_access...')
    try {
      const { error } = await supabase.rpc('query_execute', {
        query: `ALTER TABLE anonymous_access ALTER COLUMN token_id DROP NOT NULL;`
      })
      
      if (error) {
        results.push({ migration: 'alter_anonymous_access', status: 'error', message: error.message })
      } else {
        results.push({ migration: 'alter_anonymous_access', status: 'success' })
      }
    } catch (e) {
      results.push({ migration: 'alter_anonymous_access', status: 'error', message: 'RPC not available' })
    }
    
    // Migration 2: Criar activation_logs
    console.log('[APPLY-MIGRATIONS] Criando tabela activation_logs...')
    
    // Tentar verificar se a tabela existe
    const { error: checkError } = await supabase
      .from('activation_logs')
      .select('id')
      .limit(0)
    
    if (checkError && checkError.code === '42P01') {
      // Tabela não existe, tentar criar via SQL direto
      results.push({ 
        migration: 'create_activation_logs', 
        status: 'manual_required',
        sql: `
CREATE TABLE IF NOT EXISTS activation_logs (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id TEXT,
  activation_type VARCHAR(50),
  device_id TEXT,
  metadata JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);`
      })
    } else if (checkError) {
      results.push({ migration: 'create_activation_logs', status: 'error', message: checkError.message })
    } else {
      results.push({ migration: 'create_activation_logs', status: 'already_exists' })
    }
    
    // Migration 3: Atualizar função check_user_access_unified
    console.log('[APPLY-MIGRATIONS] Atualizando função check_user_access_unified...')
    results.push({ 
      migration: 'update_check_user_access_unified', 
      status: 'manual_required',
      sql: `
CREATE OR REPLACE FUNCTION check_user_access_unified(p_user_id TEXT, p_now TIMESTAMP WITH TIME ZONE DEFAULT NOW())
RETURNS TABLE(
  has_access BOOLEAN,
  access_type TEXT,
  expires_at TIMESTAMP WITH TIME ZONE
) AS $$
DECLARE
  v_profile_exists BOOLEAN;
  v_has_premium BOOLEAN;
  v_premium_expires TIMESTAMP WITH TIME ZONE;
  v_has_anonymous BOOLEAN;
  v_anonymous_expires TIMESTAMP WITH TIME ZONE;
BEGIN
  -- Check for profile-based access first (both tables)
  SELECT 
    CASE 
      WHEN COUNT(*) > 0 THEN true 
      ELSE false 
    END,
    CASE 
      WHEN COUNT(*) > 0 AND MAX(subscription_status) = 'active' THEN true 
      ELSE false 
    END,
    MAX(subscription_current_period_end)
  INTO v_profile_exists, v_has_premium, v_premium_expires
  FROM (
    SELECT subscription_status, subscription_current_period_end
    FROM profiles 
    WHERE id::text = p_user_id
    UNION ALL
    SELECT subscription_status, subscription_current_period_end
    FROM stack_profiles
    WHERE id = p_user_id
  ) combined_profiles;

  -- Check for anonymous access
  SELECT 
    COUNT(*) > 0,
    MAX(expires_at)
  INTO v_has_anonymous, v_anonymous_expires
  FROM anonymous_access
  WHERE user_id = p_user_id
    AND expires_at > p_now
    AND is_active = true;

  -- Return results based on what was found
  IF v_has_premium AND v_premium_expires > p_now THEN
    RETURN QUERY SELECT true, 'authenticated'::TEXT, v_premium_expires;
  ELSIF v_has_anonymous THEN
    RETURN QUERY SELECT true, 'anonymous'::TEXT, v_anonymous_expires;
  ELSE
    RETURN QUERY SELECT false, NULL::TEXT, NULL::TIMESTAMP WITH TIME ZONE;
  END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;`
    })
    
    return NextResponse.json({
      success: true,
      message: 'Migrations checked',
      results,
      instructions: 'Please apply the SQL commands marked as "manual_required" in the Supabase SQL Editor'
    })
    
  } catch (error) {
    console.error('[APPLY-MIGRATIONS] Erro:', error)
    return NextResponse.json({
      error: 'Failed to apply migrations',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}