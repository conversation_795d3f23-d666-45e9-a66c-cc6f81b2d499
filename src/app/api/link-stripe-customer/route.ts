import { NextRequest, NextResponse } from 'next/server'
import { createServiceClient } from '@/lib/supabase/server'
import { stackServerApp } from '@/lib/auth/stack-server'

export async function POST(request: NextRequest) {
  const timestamp = new Date().toISOString()
  console.log(`[LINK-STRIPE] ${timestamp} - ===== REQUEST RECEIVED =====`)
  
  try {
    const body = await request.json()
    const { stackUserId, stripeCustomerId, email } = body
    
    console.log(`[LINK-STRIPE] ${timestamp} - Parameters:`)
    console.log(`[LINK-STRIPE] ${timestamp} -   Stack User ID:`, stackUserId)
    console.log(`[LINK-STRIPE] ${timestamp} -   Stripe Customer ID:`, stripeCustomerId)
    console.log(`[LINK-STRIPE] ${timestamp} -   Email:`, email)
    
    if (!stackUserId || !stripeCustomerId) {
      return NextResponse.json({ 
        success: false, 
        error: 'stackUserId e stripeCustomerId são obrigatórios' 
      }, { status: 400 })
    }
    
    const supabase = createServiceClient()
    
    // Atualizar perfil com stripe_customer_id
    const { data, error } = await supabase
      .from('profiles')
      .update({
        stripe_customer_id: stripeCustomerId,
        email: email,
        updated_at: new Date().toISOString()
      })
      .eq('id', stackUserId)
      .select()
      .single()
    
    if (error) {
      console.error(`[LINK-STRIPE] ${timestamp} - ❌ Erro ao atualizar perfil:`, error)
      
      // Se o perfil não existe, tentar criar
      if (error.code === 'PGRST116') {
        console.log(`[LINK-STRIPE] ${timestamp} - Perfil não existe, criando...`)
        
        // Buscar dados do usuário no Stack
        let stackUser
        try {
          stackUser = await stackServerApp.getUser(stackUserId)
        } catch (stackError) {
          console.error(`[LINK-STRIPE] ${timestamp} - ❌ Erro ao buscar usuário no Stack:`, stackError)
        }
        
        const { data: newProfile, error: createError } = await supabase
          .from('profiles')
          .insert({
            id: stackUserId,
            email: email || stackUser?.primaryEmail,
            full_name: stackUser?.displayName || email?.split('@')[0] || 'User',
            stripe_customer_id: stripeCustomerId,
            subscription_tier: 'free',
            subscription_status: 'inactive',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          })
          .select()
          .single()
        
        if (createError) {
          console.error(`[LINK-STRIPE] ${timestamp} - ❌ Erro ao criar perfil:`, createError)
          return NextResponse.json({ 
            success: false, 
            error: 'Erro ao criar perfil',
            details: createError.message 
          }, { status: 500 })
        }
        
        console.log(`[LINK-STRIPE] ${timestamp} - ✅ Perfil criado e vinculado`)
        return NextResponse.json({ 
          success: true, 
          message: 'Perfil criado e vinculado com Stripe',
          profile: newProfile
        })
      }
      
      return NextResponse.json({ 
        success: false, 
        error: 'Erro ao atualizar perfil',
        details: error.message 
      }, { status: 500 })
    }
    
    console.log(`[LINK-STRIPE] ${timestamp} - ✅ Perfil vinculado com sucesso`)
    
    return NextResponse.json({ 
      success: true, 
      message: 'Perfil vinculado com Stripe Customer',
      profile: data
    })
    
  } catch (error) {
    console.error(`[LINK-STRIPE] ${timestamp} - ❌ Erro geral:`, error)
    return NextResponse.json({ 
      success: false, 
      error: 'Erro interno do servidor',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

// GET endpoint para verificar vínculo
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const stackUserId = searchParams.get('stackUserId')
    
    if (!stackUserId) {
      return NextResponse.json({ 
        success: false, 
        error: 'stackUserId é obrigatório' 
      }, { status: 400 })
    }
    
    const supabase = createServiceClient()
    
    const { data: profile, error } = await supabase
      .from('profiles')
      .select('id, email, stripe_customer_id, subscription_tier, subscription_status')
      .eq('id', stackUserId)
      .single()
    
    if (error) {
      if (error.code === 'PGRST116') {
        return NextResponse.json({ 
          success: false, 
          error: 'Perfil não encontrado',
          exists: false
        })
      }
      throw error
    }
    
    return NextResponse.json({
      success: true,
      exists: true,
      hasStripeCustomer: !!profile.stripe_customer_id,
      profile
    })
    
  } catch (error) {
    return NextResponse.json({ 
      success: false, 
      error: 'Erro ao verificar vínculo'
    }, { status: 500 })
  }
}