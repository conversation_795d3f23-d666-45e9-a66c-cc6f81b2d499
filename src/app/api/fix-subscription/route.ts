import { NextRequest, NextResponse } from 'next/server'
import { createServiceClient } from '@/lib/supabase/server'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const email = searchParams.get('email') || '<EMAIL>'
    
    const supabase = createServiceClient()
    
    console.log('🔍 Buscando usuário:', email)
    
    // Buscar usuário no auth
    const { data: authUser, error: authError } = await supabase.auth.admin.getUserByEmail(email)
    
    if (authError || !authUser) {
      return NextResponse.json({
        error: 'Usuário não encontrado',
        email,
        authError
      }, { status: 404 })
    }
    
    const userId = authUser.user.id
    console.log('✅ Usuário encontrado:', userId)
    
    // Verificar se perfil existe
    const { data: existingProfile, error: checkError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', userId)
      .maybeSingle()
    
    console.log('📋 Perfil existente:', existingProfile)
    
    const subscriptionData = {
      subscription_tier: 'premium',
      subscription_status: 'active',
      stripe_customer_id: `cus_fix_${Date.now()}`,
      stripe_subscription_id: `sub_fix_${Date.now()}`,
      subscription_expires_at: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
      updated_at: new Date().toISOString()
    }
    
    let result
    
    if (!existingProfile) {
      // Criar perfil
      console.log('📝 Criando novo perfil...')
      const { data: newProfile, error: insertError } = await supabase
        .from('profiles')
        .insert({
          id: userId,
          ...subscriptionData
        })
        .select()
        .single()
      
      if (insertError) {
        return NextResponse.json({
          error: 'Erro ao criar perfil',
          details: insertError
        }, { status: 500 })
      }
      
      result = newProfile
    } else {
      // Atualizar perfil existente
      console.log('🔄 Atualizando perfil existente...')
      const { data: updatedProfile, error: updateError } = await supabase
        .from('profiles')
        .update(subscriptionData)
        .eq('id', userId)
        .select()
        .single()
      
      if (updateError) {
        // Tentar forçar update usando service role
        console.log('⚠️  Tentando update com service role...')
        
        // Criar novo cliente com service role
        const serviceSupabase = createServiceClient()
        
        const { data: forcedUpdate, error: forceError } = await serviceSupabase
          .from('profiles')
          .update(subscriptionData)
          .eq('id', userId)
          .select()
          .single()
        
        if (forceError) {
          return NextResponse.json({
            error: 'Erro ao atualizar perfil',
            details: forceError,
            userId,
            subscriptionData
          }, { status: 500 })
        }
        
        result = forcedUpdate
      } else {
        result = updatedProfile
      }
    }
    
    console.log('✅ Perfil atualizado:', result)
    
    // Verificar resultado final
    const { data: finalProfile } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', userId)
      .single()
    
    return NextResponse.json({
      success: true,
      message: 'Assinatura ativada com sucesso!',
      user: {
        id: userId,
        email: authUser.user.email
      },
      profile: finalProfile,
      subscriptionActive: finalProfile?.subscription_tier === 'premium',
      validUntil: finalProfile?.subscription_expires_at
    })
    
  } catch (error) {
    console.error('❌ Erro:', error)
    return NextResponse.json({
      error: 'Erro interno',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}