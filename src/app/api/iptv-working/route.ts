import { NextResponse } from 'next/server'
import { httpGet } from '@/lib/http'
import { logger } from '@/lib/logger'
import { LOG_TAGS } from '@/config/debug'

// Lista de países/regiões com melhor disponibilidade
const PREFERRED_COUNTRIES = ['US', 'UK', 'CA', 'ES', 'BR', 'MX', 'AR', 'INT']

// Palavras-chave que indicam canais mais estáveis
const RELIABLE_KEYWORDS = ['espn', 'fox', 'sky', 'bein', 'dazn', 'sport', 'golf', 'nba', 'nfl']

export async function GET() {
  try {
    logger.info(LOG_TAGS.IPTV, 'Fetching working IPTV streams')
    
    // Buscar dados
    const [streams, channels] = await Promise.all([
      httpGet('https://iptv-org.github.io/api/streams.json', { tag: LOG_TAGS.IPTV }),
      httpGet('https://iptv-org.github.io/api/channels.json', { tag: LOG_TAGS.IPTV })
    ])
    
    // Criar mapa de canais
    const channelsMap = new Map()
    channels.forEach(ch => channelsMap.set(ch.id, ch))
    
    // Filtrar e pontuar streams
    const scoredStreams = streams
      .filter(stream => {
        const channel = channelsMap.get(stream.channel)
        return channel?.categories?.includes('sports') && 
               stream.url && 
               stream.url.includes('.m3u8')
      })
      .map(stream => {
        const channel = channelsMap.get(stream.channel)
        let score = 0
        
        // Pontuação por país
        if (PREFERRED_COUNTRIES.includes(channel?.country)) {
          score += 10
        }
        
        // Pontuação por palavras-chave confiáveis
        const nameLC = (channel?.name || '').toLowerCase()
        if (RELIABLE_KEYWORDS.some(kw => nameLC.includes(kw))) {
          score += 5
        }
        
        // Pontuação por qualidade
        if (stream.url.includes('720p') || stream.url.includes('1080p')) {
          score += 3
        }
        
        // Penalizar streams com tokens (podem expirar)
        if (stream.url.includes('token=') || stream.url.includes('auth=')) {
          score -= 5
        }
        
        return {
          ...stream,
          uniqueId: stream.channel,
          name: channel?.name || stream.channel,
          logo: channel?.logo,
          country: channel?.country,
          categories: channel?.categories || [],
          score,
          // Adicionar informações de debug
          debug: {
            hasToken: stream.url.includes('token=') || stream.url.includes('auth='),
            quality: stream.url.match(/\d+p/) ? stream.url.match(/\d+p/)[0] : 'unknown',
            reliable: score > 5
          }
        }
      })
      // Ordenar por score
      .sort((a, b) => b.score - a.score)
      // Pegar os melhores
      .slice(0, 20)
    
    logger.info(LOG_TAGS.IPTV, 'Working streams selected', {
      total: scoredStreams.length,
      topScore: scoredStreams[0]?.score,
      countries: [...new Set(scoredStreams.map(s => s.country))].slice(0, 10)
    })
    
    return NextResponse.json({
      success: true,
      data: scoredStreams,
      total: scoredStreams.length,
      debug: {
        totalSportsStreams: streams.filter(s => {
          const ch = channelsMap.get(s.channel)
          return ch?.categories?.includes('sports')
        }).length,
        withM3u8: scoredStreams.filter(s => s.url.includes('.m3u8')).length,
        reliable: scoredStreams.filter(s => s.score > 5).length
      }
    })
    
  } catch (error: any) {
    logger.error(LOG_TAGS.IPTV, 'Failed to fetch working streams', {
      error: error.message
    })
    return NextResponse.json({ 
      success: false, 
      error: error.message 
    }, { status: 500 })
  }
}