import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { stackServerApp } from '@/lib/auth/stack-server'

export async function POST(request: NextRequest) {
  const timestamp = new Date().toISOString().replace('T', ' ').replace('Z', '')
  console.log(`[TEST-ACTIVATE-PREMIUM] ${timestamp} - ===== TESTE DE ATIVAÇÃO PREMIUM =====`)
  
  try {
    const supabase = await createClient()
    
    // Verificar usuário autenticado
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      console.log(`[TEST-ACTIVATE-PREMIUM] ${timestamp} - ❌ Nenhum usuário autenticado`)
      return NextResponse.json({
        error: 'No authenticated user',
        timestamp
      }, { status: 401 })
    }
    
    console.log(`[TEST-ACTIVATE-PREMIUM] ${timestamp} - Usuário encontrado:`, {
      id: user.id,
      email: user.email
    })
    
    // Calcular data de expiração (30 dias)
    const expiresAt = new Date()
    expiresAt.setDate(expiresAt.getDate() + 30)
    
    const updateData = {
      subscription_tier: 'premium',
      subscription_status: 'active',
      subscription_current_period_end: expiresAt.toISOString(),
      stripe_customer_id: `test_customer_${Date.now()}`,
      stripe_subscription_id: `test_subscription_${Date.now()}`,
      updated_at: new Date().toISOString()
    }
    
    console.log(`[TEST-ACTIVATE-PREMIUM] ${timestamp} - Dados de atualização:`, updateData)
    
    // 1. Atualizar na tabela profiles
    const { data: profileUpdate, error: profileError } = await supabase
      .from('profiles')
      .update(updateData)
      .eq('id', user.id)
      .select()
    
    if (profileError) {
      console.error(`[TEST-ACTIVATE-PREMIUM] ${timestamp} - Erro ao atualizar profile:`, profileError)
    } else {
      console.log(`[TEST-ACTIVATE-PREMIUM] ${timestamp} - ✅ Profile atualizado:`, profileUpdate)
    }
    
    // 2. Verificar se é usuário Stack Auth e atualizar stack_profiles
    let stackUserId = null
    let stackProfileUpdate = null
    
    try {
      const users = await stackServerApp.listUsers()
      const stackUser = users.find(u => u.primaryEmail === user.email)
      
      if (stackUser) {
        stackUserId = stackUser.id
        console.log(`[TEST-ACTIVATE-PREMIUM] ${timestamp} - Stack Auth user encontrado:`, stackUserId)
        
        // Atualizar stack_profiles
        const { data: stackUpdate, error: stackError } = await supabase
          .from('stack_profiles')
          .update(updateData)
          .eq('id', stackUserId)
          .select()
        
        if (stackError) {
          console.error(`[TEST-ACTIVATE-PREMIUM] ${timestamp} - Erro ao atualizar stack_profile:`, stackError)
          
          // Tentar inserir se não existir
          const { data: stackInsert, error: insertError } = await supabase
            .from('stack_profiles')
            .insert({
              id: stackUserId,
              email: user.email,
              full_name: stackUser.displayName || user.email?.split('@')[0],
              ...updateData
            })
            .select()
          
          if (insertError) {
            console.error(`[TEST-ACTIVATE-PREMIUM] ${timestamp} - Erro ao inserir stack_profile:`, insertError)
          } else {
            console.log(`[TEST-ACTIVATE-PREMIUM] ${timestamp} - ✅ Stack profile inserido:`, stackInsert)
            stackProfileUpdate = stackInsert
          }
        } else {
          console.log(`[TEST-ACTIVATE-PREMIUM] ${timestamp} - ✅ Stack profile atualizado:`, stackUpdate)
          stackProfileUpdate = stackUpdate
        }
      }
    } catch (error) {
      console.error(`[TEST-ACTIVATE-PREMIUM] ${timestamp} - Erro ao verificar Stack Auth:`, error)
    }
    
    // 3. Verificar o status após atualização
    const checkUserId = stackUserId || user.id
    console.log(`[TEST-ACTIVATE-PREMIUM] ${timestamp} - Verificando acesso com ID:`, checkUserId)
    
    const { data: accessCheck, error: accessError } = await supabase
      .rpc('check_user_access_unified', { p_user_id: checkUserId })
      .single()
    
    if (accessError) {
      console.error(`[TEST-ACTIVATE-PREMIUM] ${timestamp} - Erro ao verificar acesso:`, accessError)
    } else {
      console.log(`[TEST-ACTIVATE-PREMIUM] ${timestamp} - Status de acesso:`, accessCheck)
    }
    
    // 4. Compilar resultado
    const result = {
      success: true,
      timestamp,
      user: {
        id: user.id,
        stackId: stackUserId,
        email: user.email
      },
      updates: {
        profile: profileUpdate ? 'Updated' : 'Failed',
        stackProfile: stackProfileUpdate ? 'Updated' : (stackUserId ? 'Failed' : 'Not Stack Auth user')
      },
      accessCheck: accessCheck || { error: accessError?.message },
      premium: {
        tier: 'premium',
        status: 'active',
        expiresAt: expiresAt.toISOString()
      }
    }
    
    console.log(`[TEST-ACTIVATE-PREMIUM] ${timestamp} - ===== RESULTADO FINAL =====`)
    console.log(JSON.stringify(result, null, 2))
    
    return NextResponse.json(result)
    
  } catch (error) {
    console.error(`[TEST-ACTIVATE-PREMIUM] ${timestamp} - ❌ Erro geral:`, error)
    return NextResponse.json({
      error: 'Internal error',
      message: error instanceof Error ? error.message : 'Unknown error',
      timestamp
    }, { status: 500 })
  }
}