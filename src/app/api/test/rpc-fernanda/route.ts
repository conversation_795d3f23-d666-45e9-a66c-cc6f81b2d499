import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

export async function GET(request: NextRequest) {
  const timestamp = new Date().toISOString()
  console.log(`[TEST-RPC-FERNANDA] ${timestamp} - ===== TESTANDO RPC PARA FERNANDA =====`)
  
  try {
    const supabase = await createClient()
    const fernandaStackId = 'f8367e51-b191-42bc-9b35-1f3ba2648985'
    
    // Teste 1: Chamar RPC diretamente
    console.log(`[TEST-RPC-FERNANDA] ${timestamp} - Chamando RPC check_user_access_unified`)
    console.log(`[TEST-RPC-FERNANDA] ${timestamp} - Parâmetro: p_user_id =`, fernandaStackId)
    
    const { data: rpcData, error: rpcError } = await supabase
      .rpc('check_user_access_unified', { p_user_id: fernandaStackId })
      .single()
    
    console.log(`[TEST-RPC-FERNANDA] ${timestamp} - Resposta RPC:`)
    console.log(`[TEST-RPC-FERNANDA] ${timestamp} - Data:`, JSON.stringify(rpcData, null, 2))
    console.log(`[TEST-RPC-FERNANDA] ${timestamp} - Error:`, rpcError)
    
    // Teste 2: Verificar dados diretos na tabela
    console.log(`[TEST-RPC-FERNANDA] ${timestamp} - Buscando dados diretos em stack_profiles`)
    const { data: stackData, error: stackError } = await supabase
      .from('stack_profiles')
      .select('*')
      .eq('id', fernandaStackId)
      .single()
    
    console.log(`[TEST-RPC-FERNANDA] ${timestamp} - Dados em stack_profiles:`)
    console.log(JSON.stringify(stackData, null, 2))
    
    // Teste 3: Validar condições da RPC manualmente
    if (stackData) {
      const now = new Date()
      const periodEnd = stackData.subscription_current_period_end ? new Date(stackData.subscription_current_period_end) : null
      
      console.log(`[TEST-RPC-FERNANDA] ${timestamp} - Validando condições:`)
      console.log(`[TEST-RPC-FERNANDA] ${timestamp} - subscription_tier:`, stackData.subscription_tier, '(deve ser "premium")')
      console.log(`[TEST-RPC-FERNANDA] ${timestamp} - subscription_status:`, stackData.subscription_status, '(deve ser "active")')
      console.log(`[TEST-RPC-FERNANDA] ${timestamp} - subscription_current_period_end:`, stackData.subscription_current_period_end)
      console.log(`[TEST-RPC-FERNANDA] ${timestamp} - Agora:`, now.toISOString())
      console.log(`[TEST-RPC-FERNANDA] ${timestamp} - Period end > Agora?`, periodEnd && periodEnd > now)
      
      const shouldHaveAccess = 
        stackData.subscription_tier === 'premium' &&
        stackData.subscription_status === 'active' &&
        periodEnd && periodEnd > now
      
      console.log(`[TEST-RPC-FERNANDA] ${timestamp} - Deveria ter acesso?`, shouldHaveAccess)
    }
    
    // Teste 4: Executar query SQL diretamente
    console.log(`[TEST-RPC-FERNANDA] ${timestamp} - Executando query SQL diretamente`)
    const { data: sqlData, error: sqlError } = await supabase.rpc('execute_sql', {
      query: `
        SELECT 
          id,
          subscription_tier,
          subscription_status,
          subscription_current_period_end,
          subscription_current_period_end > NOW() as is_valid
        FROM stack_profiles 
        WHERE id = '${fernandaStackId}'
      `
    })
    
    if (!sqlError) {
      console.log(`[TEST-RPC-FERNANDA] ${timestamp} - Resultado SQL:`, sqlData)
    }
    
    return NextResponse.json({
      timestamp,
      fernandaStackId,
      rpcResult: {
        data: rpcData,
        error: rpcError
      },
      directData: {
        data: stackData,
        error: stackError
      },
      analysis: stackData ? {
        hasPremiumTier: stackData.subscription_tier === 'premium',
        hasActiveStatus: stackData.subscription_status === 'active',
        hasValidDate: stackData.subscription_current_period_end && new Date(stackData.subscription_current_period_end) > new Date(),
        shouldHaveAccess: stackData.subscription_tier === 'premium' && 
                         stackData.subscription_status === 'active' && 
                         stackData.subscription_current_period_end && 
                         new Date(stackData.subscription_current_period_end) > new Date()
      } : null
    })
    
  } catch (error) {
    console.error(`[TEST-RPC-FERNANDA] ${timestamp} - Erro:`, error)
    return NextResponse.json(
      { error: 'Erro ao testar RPC', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
}