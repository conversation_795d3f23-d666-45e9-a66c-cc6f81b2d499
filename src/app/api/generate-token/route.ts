import { NextResponse } from 'next/server'
import { createServerDatabaseClient } from '@/lib/supabase/database-only'
import jwt from 'jsonwebtoken'
import { randomBytes } from 'crypto'
import { stackServerApp } from '@/lib/auth/stack-server'

export async function POST(request: Request) {
  try {
    // Obter usuário autenticado
    const user = await stackServerApp.getUser()
    
    if (!user) {
      return NextResponse.json(
        { error: 'No autorizado' },
        { status: 401 }
      )
    }
    
    // Obter cliente Supabase para dados (sem auth)
    const supabase = await createServerDatabaseClient()
    
    // Gerar token único
    const tokenId = randomBytes(16).toString('hex')
    const expiresAt = new Date()
    expiresAt.setHours(expiresAt.getHours() + 24) // Expira em 24 horas
    
    // Criar payload do token
    const payload = {
      userId: user.id,
      email: user.primaryEmail || user.clientReadOnlyMetadata?.email || '',
      tokenId,
      type: 'app_verification',
      exp: Math.floor(expiresAt.getTime() / 1000)
    }
    
    // Assinar token com JWT
    const jwtSecret = process.env.JWT_SECRET || 'default-secret-change-in-production'
    const token = jwt.sign(payload, jwtSecret)
    
    // Detectar plataforma do user-agent
    const userAgent = request.headers.get('user-agent') || ''
    let platform: 'ios' | 'android' = 'android'
    
    if (/iPhone|iPad|iPod/i.test(userAgent)) {
      platform = 'ios'
    }
    
    // Salvar token no banco de dados
    const { error: insertError } = await supabase
      .from('app_verification_tokens')
      .insert({
        user_id: user.id,
        token: tokenId,
        expires_at: expiresAt.toISOString(),
        platform,
        used: false
      })
    
    if (insertError) {
      console.error('Erro ao salvar token:', insertError)
      return NextResponse.json(
        { error: 'Erro ao gerar token' },
        { status: 500 }
      )
    }
    
    return NextResponse.json({
      token,
      expiresAt: expiresAt.toISOString(),
      platform
    })
    
  } catch (error) {
    console.error('Erro ao gerar token:', error)
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}