import { NextRequest, NextResponse } from 'next/server'
import { getWorkingSportsChannels } from '@/lib/working-sports-channels'

export async function GET(request: NextRequest) {
  try {
    const channels = getWorkingSportsChannels()
    
    return NextResponse.json({
      success: true,
      data: channels,
      total: channels.length
    })
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to fetch sports channels'
    }, { status: 500 })
  }
}