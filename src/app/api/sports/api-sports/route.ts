import { NextRequest, NextResponse } from 'next/server'

const API_KEY = process.env.API_SPORTS_KEY || 'eaf42062f90877281cf06a2ad2e1d11e'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const sport = searchParams.get('sport') || 'football'
    const endpoint = searchParams.get('endpoint')
    
    if (!endpoint) {
      return NextResponse.json(
        { error: 'Endpoint is required' },
        { status: 400 }
      )
    }
    
    // Construir a URL base conforme o esporte
    let baseUrl = 'https://v3.football.api-sports.io'
    if (sport !== 'football') {
      baseUrl = `https://v1.${sport}.api-sports.io`
    }
    
    // Construir URL completa com parâmetros
    const url = new URL(`${baseUrl}${endpoint}`)
    
    // Copiar todos os parâmetros exceto 'sport' e 'endpoint'
    searchParams.forEach((value, key) => {
      if (key !== 'sport' && key !== 'endpoint') {
        url.searchParams.append(key, value)
      }
    })
    
    // Fazer a requisição do lado do servidor
    const response = await fetch(url.toString(), {
      headers: {
        'x-apisports-key': API_KEY,
        'Accept': 'application/json',
      },
      cache: 'no-store'
    })
    
    if (!response.ok) {
      throw new Error(`API error: ${response.status}`)
    }
    
    const data = await response.json()
    
    // Adicionar headers CORS
    return NextResponse.json(data, {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type',
        'Cache-Control': 'public, s-maxage=900, stale-while-revalidate=60' // 15 min cache
      }
    })
  } catch (error) {
    console.error('API Sports error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch sports data' },
      { status: 500 }
    )
  }
}