import { NextRequest, NextResponse } from 'next/server'
import { allSportDBClient } from '@/services/api/sports/allsport-db.client'
import { 
  footballClient,
  basketballClient,
  baseballClient,
  hockeyClient,
  volleyballClient,
  handballClient,
  rugbyClient,
  formula1Client,
  mmaClient,
  nflClient,
  nbaClient,
  aflClient
} from '@/services/api/sports/api-sports.client'
import { apiCache } from '@/services/cache/api-cache'

export async function GET(request: NextRequest) {
  try {
    const CACHE_KEY = 'live-fixtures:all-sports'
    
    // Verificar cache primeiro
    const cached = apiCache.get(CACHE_KEY)
    if (cached) {
      console.log('🟢 Retornando fixtures do cache')
      return NextResponse.json(cached)
    }
    
    console.log('🔴 Cache miss - Buscando fixtures ao vivo...')
    
    // Lista de todos os clientes
    const apiClients = [
      { name: 'football', client: footballClient },
      { name: 'basketball', client: basketballClient },
      { name: 'baseball', client: baseballClient },
      { name: 'hockey', client: hockey<PERSON>lient },
      { name: 'volleyball', client: volleyballClient },
      { name: 'handball', client: handballClient },
      { name: 'rugby', client: rugbyClient },
      { name: 'formula1', client: formula1Client },
      { name: 'mma', client: mmaClient },
      { name: 'nfl', client: nflClient },
      { name: 'nba', client: nbaClient },
      { name: 'afl', client: aflClient }
    ]
    
    // Chamar todas as APIs simultaneamente
    const promises = apiClients.map(async ({ name, client }) => {
      try {
        // Verificar cache individual por esporte
        const sportCacheKey = `live-fixtures:${name}`
        const sportCached = apiCache.get(sportCacheKey)
        
        if (sportCached) {
          console.log(`✅ ${name}: ${sportCached.length} fixtures (do cache)`)
          return { name, fixtures: sportCached, success: true }
        }
        
        console.log(`📡 Chamando ${name}...`)
        const fixtures = await client.getLiveFixtures()
        
        // Cachear resultado individual
        apiCache.set(sportCacheKey, fixtures, 15 * 60 * 1000) // 15 minutos
        
        console.log(`✅ ${name}: ${fixtures.length} fixtures encontrados`)
        return { name, fixtures, success: true }
      } catch (error) {
        console.log(`❌ ${name}: Erro - ${error instanceof Error ? error.message : 'Desconhecido'}`)
        return { name, fixtures: [], success: false }
      }
    })
    
    // Aguardar todas as chamadas
    const results = await Promise.all(promises)
    
    // Combinar todos os fixtures
    const allFixturesRaw = results
      .filter(r => r.success)
      .flatMap(r => r.fixtures)
    
    // Remover duplicatas baseado no ID
    const seenIds = new Set()
    const allFixtures = allFixturesRaw.filter(fixture => {
      const id = fixture.fixture?.id || fixture.id
      if (!id || seenIds.has(id)) {
        return false
      }
      seenIds.add(id)
      return true
    })
    
    // Contar sucessos
    const successCount = results.filter(r => r.success).length
    console.log(`\n📊 Resumo: ${successCount}/12 APIs responderam com sucesso`)
    console.log(`📋 Total de fixtures ao vivo: ${allFixtures.length}`)
    
    // Se nenhuma API Sports funcionou, tentar AllSportDB
    if (successCount === 0) {
      console.log('⚠️ Todas as APIs falharam, usando AllSportDB como fallback...')
      
      try {
        // Verificar cache do AllSportDB primeiro
        const allSportCacheKey = 'allsportdb:today-events'
        let events = apiCache.get(allSportCacheKey)
        
        if (!events) {
          console.log('📡 Buscando eventos do AllSportDB...')
          events = await allSportDBClient.getTodayEvents()
          // Cache por 30 segundos apenas
          apiCache.set(allSportCacheKey, events, 30 * 1000)
        } else {
          console.log('✅ Eventos do AllSportDB retornados do cache')
        }
        
        const now = new Date()
        
        // Filtrar apenas eventos ao vivo
        const liveEvents = events.filter(event => {
          const start = new Date(event.dateFrom)
          const end = new Date(event.dateTo || new Date(start.getTime() + 3 * 60 * 60 * 1000))
          return now >= start && now <= end
        })
        
        console.log(`✅ ${liveEvents.length} eventos ao vivo encontrados (AllSportDB)`)
        
        // Converter para formato compatível
        const fixtures = liveEvents.map(e => ({
          fixture: {
            id: e.id,
            date: e.dateFrom,
            timestamp: new Date(e.dateFrom).getTime() / 1000,
            venue: { name: e.location },
            status: { long: 'Em andamento', short: 'LIVE', elapsed: null }
          },
          teams: {
            home: { name: e.name.split(' vs ')[0] || e.name },
            away: { name: e.name.split(' vs ')[1] || '' }
          },
          goals: { home: null, away: null },
          league: {
            name: e.competition,
            country: e.country
          }
        }))
        
        const response = {
          success: true,
          data: fixtures,
          source: 'allsportdb',
          summary: {
            total: fixtures.length,
            apisSuccess: 0,
            apisFailed: 12,
            fallback: true
          }
        }
        
        // Não cachear quando é fallback
        return NextResponse.json(response)
      } catch (allSportError) {
        console.error('❌ AllSportDB também falhou:', allSportError)
        return NextResponse.json({
          success: true,
          data: [],
          source: 'none',
          summary: {
            total: 0,
            apisSuccess: 0,
            apisFailed: 12,
            error: 'Todas as APIs falharam'
          }
        })
      }
    }
    
    const response = {
      success: true,
      data: allFixtures,
      source: 'api-sports-all',
      summary: {
        total: allFixtures.length,
        apisSuccess: successCount,
        apisFailed: 12 - successCount,
        details: results.map(r => ({
          api: r.name,
          success: r.success,
          count: r.fixtures.length
        }))
      }
    }
    
    // Cachear resposta completa por 15 minutos
    apiCache.set(CACHE_KEY, response, 15 * 60 * 1000)
    
    return NextResponse.json(response)
  } catch (error) {
    console.error('❌ Erro na rota live-fixtures:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Erro desconhecido',
        data: []
      },
      { status: 500 }
    )
  }
}