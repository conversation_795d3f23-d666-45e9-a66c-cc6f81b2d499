import { NextResponse } from 'next/server'
import { apiRotation } from '@/services/api/sports/api-rotation.service'

export async function GET() {
  try {
    const stats = apiRotation.getStats()
    
    return NextResponse.json({ 
      success: true, 
      stats,
      message: 'Sistema de rotação: 1 API Sports a cada 15 minutos'
    })
  } catch (error) {
    console.error('Erro ao obter status de rotação:', error)
    return NextResponse.json(
      { success: false, error: 'Erro ao obter status' },
      { status: 500 }
    )
  }
}

export async function POST() {
  try {
    // Resetar rotação
    apiRotation.reset()
    
    return NextResponse.json({ 
      success: true, 
      message: 'Rotação resetada! Próxima API: football'
    })
  } catch (error) {
    console.error('Erro ao resetar rotação:', error)
    return NextResponse.json(
      { success: false, error: 'Erro ao resetar rotação' },
      { status: 500 }
    )
  }
}