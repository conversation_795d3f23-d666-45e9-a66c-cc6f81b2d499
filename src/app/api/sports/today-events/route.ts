import { NextRequest, NextResponse } from 'next/server'
import { allSportDBClient } from '@/services/api/sports/allsport-db.client'
import { SportsDataService } from '@/services/api/sports/sports-data.service'

export async function GET(request: NextRequest) {
  try {
    console.log('📅 API Route: Buscando eventos de hoje...')
    
    // Usar o serviço unificado que já tem a lógica de priorização
    const events = await SportsDataService.getAllLiveResults()
    
    console.log(`✅ ${events.length} eventos encontrados para hoje`)
    
    return NextResponse.json({
      success: true,
      data: events,
      source: 'mixed'
    })
  } catch (error) {
    console.error('❌ Erro na rota today-events:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Erro desconhecido',
        data: []
      },
      { status: 500 }
    )
  }
}