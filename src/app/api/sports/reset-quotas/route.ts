import { NextResponse } from 'next/server'
import { requestManager } from '@/services/api/sports/request-manager'

export async function POST() {
  try {
    // Resetar todas as quotas
    requestManager.resetAllQuotas()
    
    return NextResponse.json({ 
      success: true, 
      message: 'Todas as quotas foram resetadas com sucesso!' 
    })
  } catch (error) {
    console.error('Erro ao resetar quotas:', error)
    return NextResponse.json(
      { success: false, error: 'Erro ao resetar quotas' },
      { status: 500 }
    )
  }
}

export async function GET() {
  try {
    // Obter estatísticas atuais
    const stats = requestManager.getStats()
    
    return NextResponse.json({ 
      success: true, 
      stats 
    })
  } catch (error) {
    console.error('Erro ao obter estatísticas:', error)
    return NextResponse.json(
      { success: false, error: 'Erro ao obter estatísticas' },
      { status: 500 }
    )
  }
}