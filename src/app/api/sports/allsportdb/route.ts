import { NextRequest, NextResponse } from 'next/server'

const ALLSPORTDB_API_KEY = process.env.ALLSPORTDB_API_KEY || '3'
const BASE_URL = 'https://www.thesportsdb.com/api/v2/json'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const endpoint = searchParams.get('endpoint')
    
    if (!endpoint) {
      return NextResponse.json(
        { error: 'Endpoint is required' },
        { status: 400 }
      )
    }
    
    // Construir a URL completa
    const url = `${BASE_URL}/${ALLSPORTDB_API_KEY}/${endpoint}`
    
    // Fazer a requisição do lado do servidor
    const response = await fetch(url, {
      headers: {
        'Accept': 'application/json',
        'Cache-Control': 'no-cache',
      },
      cache: 'no-store'
    })
    
    if (!response.ok) {
      throw new Error(`API error: ${response.status}`)
    }
    
    const data = await response.json()
    
    // Adicionar headers CORS
    return NextResponse.json(data, {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type',
        'Cache-Control': 'public, s-maxage=60, stale-while-revalidate=30'
      }
    })
  } catch (error) {
    console.error('AllSportDB API error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch sports data' },
      { status: 500 }
    )
  }
}