import { NextResponse } from 'next/server'
import { requestManager } from '@/services/api/sports/request-manager'

export async function GET() {
  try {
    const stats = requestManager.getStats()
    const allQuotas = stats.quotas
    
    // Separar APIs Sports das outras
    const apiSportsQuotas = allQuotas.filter(q => q.apiName.startsWith('api-'))
    const otherQuotas = allQuotas.filter(q => !q.apiName.startsWith('api-'))
    
    // Calcular próximas chamadas
    const now = new Date()
    const apiSportsStatus = apiSportsQuotas.map(quota => {
      const nextAvailable = requestManager.getNextAvailableTime(quota.apiName)
      const canCallNow = !quota.lastRequest || 
        (now.getTime() - new Date(quota.lastRequest).getTime() >= 15 * 60 * 1000)
      
      return {
        api: quota.apiName,
        sport: quota.apiName.replace('api-', ''),
        usedToday: quota.usedToday,
        limit: quota.dailyLimit,
        percentUsed: Math.round((quota.usedToday / quota.dailyLimit) * 100),
        lastCall: quota.lastRequest ? new Date(quota.lastRequest).toLocaleTimeString() : 'Nunca',
        canCallNow,
        nextAvailable: nextAvailable ? nextAvailable.toLocaleTimeString() : 'Agora',
        minutesUntilNext: nextAvailable ? 
          Math.ceil((nextAvailable.getTime() - now.getTime()) / 1000 / 60) : 0
      }
    })
    
    // Resumo
    const summary = {
      totalAPISports: apiSportsQuotas.length,
      availableNow: apiSportsStatus.filter(s => s.canCallNow).length,
      totalUsedToday: apiSportsQuotas.reduce((sum, q) => sum + q.usedToday, 0),
      totalLimit: apiSportsQuotas.reduce((sum, q) => sum + q.dailyLimit, 0),
      averageUsage: Math.round(
        apiSportsQuotas.reduce((sum, q) => sum + (q.usedToday / q.dailyLimit) * 100, 0) / 
        apiSportsQuotas.length
      ),
      cacheEnabled: true,
      cacheMinutes: 15,
      intervalMinutes: 15
    }
    
    return NextResponse.json({ 
      success: true,
      timestamp: new Date().toISOString(),
      summary,
      apiSportsStatus: apiSportsStatus.sort((a, b) => b.percentUsed - a.percentUsed),
      otherAPIs: otherQuotas.map(q => ({
        api: q.apiName,
        usedToday: q.usedToday,
        limit: q.dailyLimit,
        percentUsed: Math.round((q.usedToday / q.dailyLimit) * 100)
      })),
      message: 'Sistema configurado: 12 APIs Sports chamadas a cada 15 minutos com cache'
    })
  } catch (error) {
    console.error('Erro ao obter status das APIs:', error)
    return NextResponse.json(
      { success: false, error: 'Erro ao obter status' },
      { status: 500 }
    )
  }
}