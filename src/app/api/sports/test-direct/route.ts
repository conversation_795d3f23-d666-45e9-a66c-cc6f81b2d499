import { NextResponse } from 'next/server'

export async function GET() {
  try {
    const API_KEY = 'ee85c2e1628d43579c7098cc53410413'
    
    // Teste 1: Jogos ao vivo
    console.log('🔴 Teste 1: Buscando jogos ao vivo...')
    const liveResponse = await fetch('https://v3.football.api-sports.io/fixtures?live=all', {
      headers: {
        'x-apisports-key': API_KEY
      }
    })
    
    const liveData = await liveResponse.json()
    console.log('Status:', liveResponse.status)
    console.log('Jogos ao vivo:', liveData.response?.length || 0)
    
    // Teste 2: Jogos de hoje
    const today = new Date().toISOString().split('T')[0]
    console.log(`\n🔴 Teste 2: Buscando jogos de ${today}...`)
    
    const todayResponse = await fetch(`https://v3.football.api-sports.io/fixtures?date=${today}`, {
      headers: {
        'x-apisports-key': API_KEY
      }
    })
    
    const todayData = await todayResponse.json()
    console.log('Status:', todayResponse.status)
    console.log('Jogos hoje:', todayData.response?.length || 0)
    
    // Teste 3: Verificar quota
    console.log('\n🔴 Teste 3: Verificando status da API...')
    const statusResponse = await fetch('https://v3.football.api-sports.io/status', {
      headers: {
        'x-apisports-key': API_KEY
      }
    })
    
    const statusData = await statusResponse.json()
    console.log('Status da conta:', statusData)
    
    return NextResponse.json({
      success: true,
      tests: {
        live: {
          status: liveResponse.status,
          count: liveData.response?.length || 0,
          errors: liveData.errors || null,
          sample: liveData.response?.slice(0, 3)
        },
        today: {
          status: todayResponse.status,
          count: todayData.response?.length || 0,
          errors: todayData.errors || null,
          sample: todayData.response?.slice(0, 3)
        },
        account: {
          status: statusResponse.status,
          data: statusData.response
        }
      }
    })
    
  } catch (error) {
    console.error('❌ Erro:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Erro desconhecido'
    }, { status: 500 })
  }
}