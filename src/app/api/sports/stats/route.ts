import { NextResponse } from 'next/server'
import { requestManager } from '@/services/api/sports/request-manager'

export async function GET() {
  try {
    const stats = requestManager.getStats()
    
    return NextResponse.json({
      success: true,
      data: stats
    })
  } catch (error) {
    console.error('Erro ao buscar estatísticas:', error)
    return NextResponse.json({
      success: false,
      error: 'Erro ao buscar estatísticas'
    }, { status: 500 })
  }
}