import { NextResponse } from 'next/server'

export async function GET() {
  try {
    // Por enquanto, retornar um status mock
    const mockStatus = {
      isRunning: true,
      schedules: [
        {
          api: 'api-football',
          type: 'api-sports',
          lastUpdate: new Date().toISOString(),
          nextUpdate: new Date(Date.now() + 15 * 60 * 1000).toISOString(),
          isProcessing: false,
          quotaUsed: 10,
          quotaLimit: 100,
          canUpdate: true
        },
        {
          api: 'allsportdb',
          type: 'allsportdb',
          lastUpdate: new Date().toISOString(),
          nextUpdate: new Date(Date.now() + 30 * 1000).toISOString(),
          isProcessing: false,
          quotaUsed: 1000,
          quotaLimit: 3333,
          canUpdate: true
        }
      ],
      totalRequests: 50,
      apiSportsUsage: 10,
      apiSportsRemaining: 1190,
      nextReset: '23h 30m'
    }
    
    return NextResponse.json({
      success: true,
      status: mockStatus
    })
  } catch (error: any) {
    return NextResponse.json(
      { 
        success: false, 
        error: error.message 
      },
      { status: 500 }
    )
  }
}

export async function POST(request: Request) {
  try {
    const { action } = await request.json()
    
    return NextResponse.json({ 
      success: true, 
      message: `Ação '${action}' recebida (modo simplificado)` 
    })
    
  } catch (error: any) {
    return NextResponse.json(
      { 
        success: false, 
        error: error.message 
      },
      { status: 500 }
    )
  }
}