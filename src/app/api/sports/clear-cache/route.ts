import { NextResponse } from 'next/server'
import { sportsCacheService } from '@/services/cache/sports-cache.service'

export async function POST() {
  try {
    // Limpar todo o cache
    sportsCacheService.clearAll()
    
    return NextResponse.json({
      success: true,
      message: 'Cache limpo com sucesso'
    })
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Erro ao limpar cache'
    }, { status: 500 })
  }
}