import { NextResponse } from 'next/server'
import { APISportsClient } from '@/services/api/sports/api-sports.client'
import { allSportDBClient } from '@/services/api/sports/allsport-db.client'

export async function GET() {
  console.log('🧪 TESTE DIRETO DAS APIs (SEM CONTROLE DE QUOTA)\n')
  
  const results = []
  const sports = [
    'football', 'basketball', 'baseball', 'hockey',
    'volleyball', 'handball', 'rugby', 'formula1',
    'mma', 'nfl', 'nba', 'afl'
  ]
  
  // Testar cada API Sports
  for (const sport of sports) {
    try {
      const client = new APISportsClient(sport)
      const events = await client.getLiveEvents()
      console.log(`✅ ${sport}: ${events.length} eventos ao vivo`)
      results.push({ 
        api: sport, 
        success: true, 
        count: events.length,
        sample: events.length > 0 ? events[0] : null
      })
    } catch (error: any) {
      console.log(`❌ ${sport}: ${error.message}`)
      results.push({ 
        api: sport, 
        success: false, 
        count: 0,
        error: error.message 
      })
    }
    
    // Aguardar entre requisições
    await new Promise(resolve => setTimeout(resolve, 500))
  }
  
  // Testar AllSportDB
  try {
    const today = new Date().toISOString().split('T')[0]
    const events = await allSportDBClient.getEvents({
      dateFrom: today,
      dateTo: today
    })
    console.log(`✅ AllSportDB: ${events.length} eventos hoje`)
    results.push({ 
      api: 'allsportdb', 
      success: true, 
      count: events.length,
      sample: events.length > 0 ? events[0] : null
    })
  } catch (error: any) {
    console.log(`❌ AllSportDB: ${error.message}`)
    results.push({ 
      api: 'allsportdb', 
      success: false, 
      count: 0,
      error: error.message 
    })
  }
  
  // Calcular totais
  const totalAPISports = results
    .filter(r => r.api !== 'allsportdb')
    .reduce((sum, r) => sum + r.count, 0)
  
  const totalEvents = results.reduce((sum, r) => sum + r.count, 0)
  
  return NextResponse.json({
    success: true,
    timestamp: new Date().toISOString(),
    summary: {
      totalAPISports,
      totalAllSportDB: results.find(r => r.api === 'allsportdb')?.count || 0,
      totalEvents,
      successfulAPIs: results.filter(r => r.success).length,
      failedAPIs: results.filter(r => !r.success).length
    },
    results
  })
}