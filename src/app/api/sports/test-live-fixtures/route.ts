import { NextResponse } from 'next/server'
import { SportsDataService } from '@/services/api/sports/sports-data.service'
import { UnifiedSportsService } from '@/services/api/sports/unified-sports.service'

export async function GET() {
  try {
    console.log('🚀 Buscando resultados ao vivo REAIS das APIs...')
    
    // Usar UnifiedSportsService para obter dados REAIS ao vivo
    const liveEvents = await UnifiedSportsService.getLiveResults()
    
    // Se não houver eventos ao vivo, tentar buscar diretamente de APIs específicas
    if (liveEvents.length === 0) {
      console.log('📡 Tentando buscar dados diretos das APIs específicas...')
      
      // Buscar dados ao vivo de futebol e basquete (mais populares)
      const [footballEvents, basketballEvents] = await Promise.all([
        SportsDataService.getLiveResultsForAPI('api-football').catch(() => []),
        SportsDataService.getLiveResultsForAPI('api-basketball').catch(() => [])
      ])
      
      // Combinar todos os eventos
      const allEvents = [...footballEvents, ...basketballEvents]
      
      // IMPORTANTE: Retornar array vazio se não houver jogos ao vivo reais
      console.log(`📊 Total de eventos ao vivo REAIS: ${allEvents.length}`)
      
      return NextResponse.json({
        success: true,
        data: allEvents, // Retorna array vazio se não houver jogos
        source: 'api-sports-direct',
        summary: {
          total: allEvents.length,
          apisSuccess: 2,
          apisFailed: 0,
          details: [
            {
              api: 'api-football',
              success: true,
              count: footballEvents.length
            },
            {
              api: 'api-basketball',
              success: true,
              count: basketballEvents.length
            }
          ]
        }
      })
    }
    
    return NextResponse.json({
      success: true,
      data: liveEvents,
      source: 'unified-api',
      summary: {
        total: liveEvents.length,
        apisSuccess: 1,
        apisFailed: 0,
        details: [
          {
            api: 'unified',
            success: true,
            count: liveEvents.length
          }
        ]
      }
    })
  } catch (error) {
    console.error('❌ Erro ao buscar jogos ao vivo:', error)
    
    // Em caso de erro, retornar array vazio (não usar dados mock)
    return NextResponse.json({
      success: true,
      data: [], // Array vazio em caso de erro
      error: 'Erro ao buscar jogos ao vivo',
      source: 'error',
      summary: {
        total: 0,
        apisSuccess: 0,
        apisFailed: 1,
        details: []
      }
    })
  }
}