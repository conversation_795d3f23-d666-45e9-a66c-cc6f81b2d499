import { NextResponse } from 'next/server'
import { apiCache } from '@/services/cache/api-cache'

export async function GET() {
  const stats = apiCache.getStats()
  
  const summary = {
    totalEntries: stats.length,
    apiSportsEntries: stats.filter(s => !s.key.includes('allsport')).length,
    allSportDBEntries: stats.filter(s => s.key.includes('allsport')).length,
    totalSize: stats.reduce((acc, s) => acc + s.size, 0),
    entries: stats.map(s => ({
      ...s,
      sizeKB: (s.size / 1024).toFixed(2),
      expiresInMin: (s.expiresIn / 60).toFixed(1)
    }))
  }
  
  return NextResponse.json({
    success: true,
    cache: summary,
    rules: {
      apiSports: '15 minutos de cache',
      allSportDB: '30 segundos de cache'
    }
  })
}

// Limpar cache
export async function DELETE() {
  apiCache.clear()
  return NextResponse.json({
    success: true,
    message: 'Cache limpo com sucesso'
  })
}