import { NextRequest, NextResponse } from 'next/server'
import { allSportDBClient } from '@/services/api/sports/allsport-db.client'

export async function GET(request: NextRequest) {
  console.log('\n🔵 [API /api/sports/live-events] Nova requisição recebida')
  console.log('🔑 [API] Headers:', Object.fromEntries(request.headers.entries()))
  
  try {
    console.log('🔴 [API] Buscando eventos ao vivo da AllSportDB...')
    console.log('🔑 [API] API_KEY disponível:', !!process.env.ALLSPORT_DB_KEY)
    
    const events = await allSportDBClient.getTodayEvents()
    console.log(`📋 Total de eventos hoje: ${events.length}`)
    
    const now = new Date()
    console.log(`⏰ Hora atual: ${now.toISOString()}`)
    
    // Filtrar apenas eventos ao vivo
    const liveEvents = events.filter(event => {
      const start = new Date(event.dateFrom)
      const end = new Date(event.dateTo || new Date(start.getTime() + 3 * 60 * 60 * 1000))
      return now >= start && now <= end
    })
    
    console.log(`✅ ${liveEvents.length} eventos ao vivo encontrados`)
    
    // Se não houver eventos ao vivo, mostrar alguns próximos
    if (liveEvents.length === 0 && events.length > 0) {
      console.log('📅 Próximos 3 eventos:')
      events.slice(0, 3).forEach(e => {
        console.log(`  - ${e.name} às ${new Date(e.dateFrom).toLocaleString('pt-BR')}`)
      })
    }
    
    // Filtrar apenas eventos futuros se não houver ao vivo
    const upcomingEvents = events.filter(event => {
      const start = new Date(event.dateFrom)
      return start > now
    }).slice(0, 6)
    
    // Se não houver eventos ao vivo, retornar os próximos eventos
    let dataToReturn = liveEvents.length > 0 ? liveEvents : upcomingEvents
    
    // Remover duplicatas baseadas no ID
    const uniqueEvents = new Map()
    dataToReturn.forEach(event => {
      if (!uniqueEvents.has(event.id)) {
        uniqueEvents.set(event.id, event)
      }
    })
    dataToReturn = Array.from(uniqueEvents.values())
    
    console.log(`🔍 Removidas ${dataToReturn.length} duplicatas de eventos`)
    
    return NextResponse.json({
      success: true,
      data: dataToReturn,
      total: dataToReturn.length,
      isLive: liveEvents.length > 0,
      debug: {
        totalToday: events.length,
        liveCount: liveEvents.length,
        currentTime: now.toISOString()
      }
    })
  } catch (error) {
    console.error('❌ Erro ao buscar eventos ao vivo:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Erro desconhecido',
        data: []
      },
      { status: 500 }
    )
  }
}