import { NextResponse } from 'next/server'
import { footballClient } from '@/services/api/sports/api-sports.client'
import { allSportDBClient } from '@/services/api/sports/allsport-db.client'

export async function GET() {
  try {
    console.log('🔴 Forçando busca de jogos ao vivo...')
    
    // Primeiro tentar API Sports (futebol)
    let footballFixtures = []
    try {
      console.log('📡 Tentando API Sports Football...')
      footballFixtures = await footballClient.getLiveFixtures()
      console.log(`✅ API Sports: ${footballFixtures.length} jogos ao vivo`)
    } catch (error) {
      console.error('❌ Erro API Sports:', error)
    }
    
    // Se não encontrar jogos ao vivo, buscar jogos de hoje
    if (footballFixtures.length === 0) {
      try {
        console.log('📡 Buscando jogos de hoje...')
        const today = new Date().toISOString().split('T')[0]
        const todayFixtures = await footballClient.getFixturesByDate(today)
        console.log(`✅ API Sports: ${todayFixtures.length} jogos hoje`)
        
        // Filtrar jogos que estão ocorrendo agora ou nas próximas horas
        const now = new Date()
        const upcomingFixtures = todayFixtures.filter(fixture => {
          const fixtureDate = new Date(fixture.fixture.date)
          const diff = fixtureDate.getTime() - now.getTime()
          return diff > -7200000 && diff < 7200000 // 2 horas antes até 2 horas depois
        })
        
        footballFixtures = upcomingFixtures
        console.log(`📊 ${upcomingFixtures.length} jogos próximos/em andamento`)
      } catch (error) {
        console.error('❌ Erro ao buscar jogos de hoje:', error)
      }
    }
    
    // Também tentar AllSportsDB
    let allSportsEvents = []
    try {
      console.log('📡 Tentando AllSportsDB...')
      allSportsEvents = await allSportDBClient.getTodayEvents()
      console.log(`✅ AllSportsDB: ${allSportsEvents.length} eventos hoje`)
    } catch (error) {
      console.error('❌ Erro AllSportsDB:', error)
    }
    
    return NextResponse.json({
      success: true,
      summary: {
        apiSports: footballFixtures.length,
        allSportsDB: allSportsEvents.length,
        total: footballFixtures.length + allSportsEvents.length
      },
      data: {
        football: footballFixtures.slice(0, 10),
        allSports: allSportsEvents.slice(0, 10)
      }
    })
    
  } catch (error) {
    console.error('❌ Erro geral:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Erro desconhecido'
    }, { status: 500 })
  }
}