import { NextRequest, NextResponse } from 'next/server'
import { allSportDBClient } from '@/services/api/sports/allsport-db.client'
import { eventsCache } from '@/lib/cache/events-cache'

export async function GET(request: NextRequest) {
  console.log('\n🔵 [API /api/sports/upcoming-events] Nova requisição recebida')
  
  try {
    const { searchParams } = new URL(request.url)
    const days = parseInt(searchParams.get('days') || '7')
    
    // Verificar cache primeiro
    const cacheKey = `upcoming-events-${days}`;
    const cachedData = eventsCache.get(cacheKey);
    
    if (cachedData) {
      console.log(`📦 [API] Retornando eventos do cache (${cachedData.length} eventos)`);
      return NextResponse.json({
        success: true,
        data: cachedData,
        source: 'cache'
      });
    }
    
    console.log(`📆 [API] Buscando próximos eventos (${days} dias)...`)
    
    // Buscar eventos dos próximos dias usando AllSportDB
    const today = new Date()
    const events: any[] = []
    let consecutiveErrors = 0;
    const maxConsecutiveErrors = 2; // Parar após 2 erros consecutivos (provavelmente rate limit)
    
    for (let i = 0; i < days; i++) {
      // Se tiver muitos erros consecutivos, parar
      if (consecutiveErrors >= maxConsecutiveErrors) {
        console.log('⚠️ [API] Muitos erros consecutivos, parando busca...');
        break;
      }
      
      const date = new Date(today)
      date.setDate(today.getDate() + i)
      const dateStr = date.toISOString().split('T')[0]
      
      console.log(`📅 [API] Buscando eventos para ${dateStr}...`)
      
      try {
        // Adicionar delay entre requisições para evitar rate limit
        if (i > 0) {
          await new Promise(resolve => setTimeout(resolve, 1000)); // 1 segundo entre requisições
        }
        
        const dayEvents = await allSportDBClient.getEventsByDate(dateStr)
        events.push(...dayEvents)
        consecutiveErrors = 0; // Reset contador de erros
      } catch (err: any) {
        console.error(`❌ [API] Erro ao buscar eventos de ${dateStr}:`, err.message)
        
        // Se for erro 429 (rate limit), aumentar contador
        if (err.message?.includes('429')) {
          consecutiveErrors++;
        }
      }
    }
    
    console.log(`✅ [API] ${events.length} próximos eventos encontrados`)
    
    // Salvar no cache
    if (events.length > 0) {
      eventsCache.set(cacheKey, events, 1800000); // Cache por 30 minutos
    }
    
    return NextResponse.json({
      success: true,
      data: events,
      source: 'allsportdb'
    })
  } catch (error) {
    console.error('❌ Erro na rota upcoming-events:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Erro desconhecido',
        data: []
      },
      { status: 500 }
    )
  }
}