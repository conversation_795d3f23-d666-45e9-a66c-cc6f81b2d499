import { NextResponse } from 'next/server'
import { SportsDataService } from '@/services/api/sports/sports-data.service'

export async function GET() {
  try {
    // Testar todas as funcionalidades
    const [liveEvents, upcomingEvents, stats] = await Promise.all([
      SportsDataService.getAllLiveResults(),
      SportsDataService.getAllUpcomingEvents(7),
      SportsDataService.getSportsStats()
    ])
    
    return NextResponse.json({
      success: true,
      data: {
        liveEvents: {
          count: liveEvents.length,
          samples: liveEvents.slice(0, 3)
        },
        upcomingEvents: {
          count: upcomingEvents.length,
          samples: upcomingEvents.slice(0, 3)
        },
        stats: stats
      },
      timestamp: new Date().toISOString()
    })
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}