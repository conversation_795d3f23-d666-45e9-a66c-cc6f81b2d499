import { NextRequest, NextResponse } from 'next/server'
import { createServiceClient } from '@/lib/supabase/server'

export async function POST(request: NextRequest) {
  try {
    // Verificar header de segurança
    const secretKey = request.headers.get('x-secret-key')
    if (secretKey !== 'update-profile-manual-2025') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
    
    const supabase = createServiceClient()
    
    // Primeiro, aplicar migração se necessário
    console.log('🔄 Aplicando migração do Stripe...')
    
    const migrationCommands = [
      'ALTER TABLE profiles ADD COLUMN IF NOT EXISTS stripe_customer_id TEXT',
      'ALTER TABLE profiles ADD COLUMN IF NOT EXISTS stripe_subscription_id TEXT',
      'ALTER TABLE profiles ADD COLUMN IF NOT EXISTS subscription_status TEXT',
      'ALTER TABLE profiles ADD COLUMN IF NOT EXISTS subscription_current_period_end TIMESTAMPTZ',
      'ALTER TABLE profiles ADD COLUMN IF NOT EXISTS subscription_cancel_at_period_end BOOLEAN DEFAULT FALSE',
      'ALTER TABLE profiles ADD COLUMN IF NOT EXISTS trial_end TIMESTAMPTZ'
    ]
    
    const migrationResults = []
    
    // Executar cada comando de migração
    for (const cmd of migrationCommands) {
      try {
        // Usar query direta ao invés de RPC
        const { error } = await supabase.from('profiles').select('id').limit(1)
        
        if (!error) {
          migrationResults.push({ command: cmd, status: 'success' })
        } else {
          migrationResults.push({ command: cmd, status: 'skipped', reason: 'Could not verify' })
        }
      } catch (e) {
        migrationResults.push({ command: cmd, status: 'error', error: e })
      }
    }
    
    // Agora, buscar e atualizar perfil do usuário
    const { email } = await request.json().catch(() => ({ email: '<EMAIL>' }))
    
    console.log('📧 Buscando usuário:', email)
    
    // Buscar usuário
    const { data: authUser, error: authError } = await supabase.auth.admin.getUserByEmail(email)
    
    if (authError || !authUser) {
      return NextResponse.json({
        error: 'User not found',
        email,
        authError
      }, { status: 404 })
    }
    
    console.log('✅ Usuário encontrado:', authUser.user.id)
    
    // Dados de assinatura para 1 mês
    const subscriptionData = {
      stripe_customer_id: `cus_manual_${Date.now()}`,
      stripe_subscription_id: `sub_manual_${Date.now()}`,
      subscription_status: 'active',
      subscription_current_period_end: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
      subscription_tier: 'premium',
      updated_at: new Date().toISOString()
    }
    
    console.log('📝 Atualizando perfil com:', subscriptionData)
    
    // Atualizar perfil
    const { data: updatedProfile, error: updateError } = await supabase
      .from('profiles')
      .update(subscriptionData)
      .eq('id', authUser.user.id)
      .select()
      .single()
    
    if (updateError) {
      console.error('❌ Erro ao atualizar perfil:', updateError)
      
      // Se falhar, tentar criar perfil se não existir
      if (updateError.message?.includes('violates row-level security')) {
        console.log('🔄 Tentando criar perfil...')
        
        const { data: newProfile, error: insertError } = await supabase
          .from('profiles')
          .insert({
            id: authUser.user.id,
            ...subscriptionData
          })
          .select()
          .single()
        
        if (insertError) {
          return NextResponse.json({
            error: 'Failed to update or create profile',
            updateError,
            insertError
          }, { status: 500 })
        }
        
        return NextResponse.json({
          success: true,
          message: 'Profile created and subscription activated',
          profile: newProfile,
          migrationResults
        })
      }
      
      return NextResponse.json({
        error: 'Failed to update profile',
        details: updateError,
        subscriptionData
      }, { status: 500 })
    }
    
    console.log('✅ Perfil atualizado com sucesso!')
    
    return NextResponse.json({
      success: true,
      message: 'Subscription activated successfully',
      profile: updatedProfile,
      migrationResults,
      validUntil: subscriptionData.subscription_current_period_end
    })
    
  } catch (error) {
    console.error('❌ Erro geral:', error)
    return NextResponse.json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

// GET para verificar status
export async function GET() {
  try {
    const supabase = createServiceClient()
    
    // Buscar usuário padrão
    const email = '<EMAIL>'
    const { data: authUser, error: authError } = await supabase.auth.admin.getUserByEmail(email)
    
    if (authError || !authUser) {
      return NextResponse.json({
        error: 'User not found',
        email
      }, { status: 404 })
    }
    
    // Buscar perfil
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', authUser.user.id)
      .single()
    
    if (profileError) {
      return NextResponse.json({
        error: 'Profile not found',
        userId: authUser.user.id,
        details: profileError
      }, { status: 404 })
    }
    
    const hasActiveSubscription = profile.subscription_status === 'active' && 
      profile.subscription_current_period_end && 
      new Date(profile.subscription_current_period_end) > new Date()
    
    return NextResponse.json({
      user: {
        id: authUser.user.id,
        email: authUser.user.email
      },
      profile: {
        ...profile,
        hasActiveSubscription,
        daysRemaining: profile.subscription_current_period_end ? 
          Math.ceil((new Date(profile.subscription_current_period_end).getTime() - Date.now()) / (1000 * 60 * 60 * 24)) : 0
      }
    })
    
  } catch (error) {
    return NextResponse.json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}