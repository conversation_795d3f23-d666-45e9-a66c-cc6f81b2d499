import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient()
    
    // Buscar os últimos 5 perfis atualizados (para encontrar pagamentos recentes)
    const { data: profiles, error } = await supabase
      .from('profiles')
      .select('*')
      .order('updated_at', { ascending: false })
      .limit(5)

    if (error) {
      return NextResponse.json({ 
        success: false, 
        error: 'Erro ao buscar perfis',
        details: error.message 
      })
    }

    // Buscar o perfil com stripe_customer_id (indica que fez pagamento)
    const paidProfiles = profiles?.filter(p => p.stripe_customer_id) || []
    const profile = paidProfiles[0] // Pegar o mais recente com pagamento

    if (!profile) {
      return NextResponse.json({ 
        success: false, 
        error: 'Nenhum perfil com pagamento encontrado',
        allProfiles: profiles?.map(p => ({
          id: p.id,
          tier: p.subscription_tier,
          hasStripeCustomer: !!p.stripe_customer_id,
          updatedAt: p.updated_at
        }))
      })
    }

    // Verificar se tem acesso
    const now = new Date()
    const hasActiveTrial = profile.trial_end_date ? new Date(profile.trial_end_date) > now : false
    const hasActiveSubscription = profile.subscription_tier === 'premium' && 
      profile.subscription_current_period_end && 
      new Date(profile.subscription_current_period_end) > now

    return NextResponse.json({
      success: true,
      profileId: profile.id,
      subscription: {
        tier: profile.subscription_tier,
        status: profile.subscription_status,
        currentPeriodEnd: profile.subscription_current_period_end,
        stripeCustomerId: profile.stripe_customer_id,
        stripeSubscriptionId: profile.stripe_subscription_id
      },
      access: {
        hasAccess: hasActiveSubscription || hasActiveTrial,
        hasActiveSubscription,
        hasActiveTrial,
        reason: hasActiveSubscription ? '✅ Assinatura Premium Ativa' : 
                hasActiveTrial ? '✅ Trial Ativo' : 
                '❌ Sem acesso ativo'
      },
      dates: {
        created: profile.created_at,
        updated: profile.updated_at,
        now: now.toISOString()
      }
    })

  } catch (error) {
    console.error('Erro ao verificar assinatura:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Erro ao verificar assinatura',
        details: error instanceof Error ? error.message : 'Erro desconhecido'
      },
      { status: 500 }
    )
  }
}