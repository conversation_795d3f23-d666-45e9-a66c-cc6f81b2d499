import { NextRequest, NextResponse } from 'next/server'
import { createServiceClient } from '@/lib/supabase/server'
import { createClient } from '@supabase/supabase-js'

export async function POST(request: NextRequest) {
  try {
    // Verificar header de segurança
    const secretKey = request.headers.get('x-secret-key')
    if (secretKey !== 'activate-premium-2025') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
    
    const { email } = await request.json()
    
    if (!email) {
      return NextResponse.json({ error: 'Email is required' }, { status: 400 })
    }
    
    // Criar cliente admin do Supabase
    const supabaseAdmin = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )
    
    console.log('🔍 Ativando premium para:', email)
    
    // Buscar usuário no auth usando admin API
    const { data: { users }, error: authError } = await supabaseAdmin.auth.admin.listUsers()
    
    if (authError) {
      console.log('❌ Erro ao buscar usuários:', authError)
      return NextResponse.json({
        error: 'Erro ao buscar usuários',
        details: authError.message
      }, { status: 500 })
    }
    
    const authUser = users.find(user => user.email === email)
    
    if (!authUser) {
      console.log('❌ Usuário não encontrado')
      return NextResponse.json({
        error: 'Usuário não encontrado',
        email
      }, { status: 404 })
    }
    
    const userId = authUser.id
    console.log('✅ Usuário encontrado:', userId)
    
    // Criar ou atualizar perfil com premium
    const subscriptionData = {
      email: authUser.email,
      subscription_tier: 'premium',
      subscription_status: 'active',
      subscription_current_period_end: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
      stripe_customer_id: `cus_manual_${Date.now()}`,
      stripe_subscription_id: `sub_manual_${Date.now()}`,
      updated_at: new Date().toISOString()
    }
    
    // Tentar atualizar primeiro usando supabaseAdmin
    const { data: updatedProfile, error: updateError } = await supabaseAdmin
      .from('profiles')
      .update(subscriptionData)
      .eq('id', userId)
      .select()
      .single()
    
    if (updateError) {
      console.log('⚠️  Update falhou, tentando insert...', updateError.message)
      
      // Se falhar, tentar criar
      const { data: newProfile, error: insertError } = await supabaseAdmin
        .from('profiles')
        .insert({
          id: userId,
          ...subscriptionData,
          created_at: new Date().toISOString()
        })
        .select()
        .single()
      
      if (insertError) {
        console.error('❌ Erro ao criar perfil:', insertError)
        return NextResponse.json({
          error: 'Erro ao criar perfil',
          details: insertError.message
        }, { status: 500 })
      }
      
      console.log('✅ Perfil criado com sucesso!')
      
      return NextResponse.json({
        success: true,
        message: 'Perfil criado e premium ativado!',
        profile: newProfile,
        validUntil: subscriptionData.subscription_current_period_end
      })
    }
    
    console.log('✅ Perfil atualizado com sucesso!')
    
    // Verificar se a atualização funcionou
    const { data: finalProfile } = await supabaseAdmin
      .from('profiles')
      .select('*')
      .eq('id', userId)
      .single()
    
    return NextResponse.json({
      success: true,
      message: 'Premium ativado com sucesso!',
      profile: finalProfile,
      subscription: {
        active: finalProfile?.subscription_tier === 'premium',
        tier: finalProfile?.subscription_tier,
        status: finalProfile?.subscription_status,
        expiresAt: finalProfile?.subscription_current_period_end
      }
    })
    
  } catch (error) {
    console.error('❌ Erro geral:', error)
    return NextResponse.json({
      error: 'Erro interno',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}