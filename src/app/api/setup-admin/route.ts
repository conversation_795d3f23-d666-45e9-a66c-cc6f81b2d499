import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

// Endpoint temporário para configurar admin
// REMOVER EM PRODUÇÃO!
export async function POST(request: NextRequest) {
  try {
    // Verificar token secreto
    const { secretToken } = await request.json()
    
    if (secretToken !== 'setup-admin-2025') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
    
    const supabase = await createClient()
    
    // Criar ou atualizar usuário admin
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    
    if (user) {
      // Atualizar perfil do usuário atual para admin
      const { error: updateError } = await supabase
        .from('user_profiles')
        .upsert({
          id: user.id,
          email: user.email,
          name: 'Administrador',
          role: 'admin',
          updated_at: new Date().toISOString()
        })
        .eq('id', user.id)
      
      if (updateError) {
        console.error('Error updating profile:', updateError)
        return NextResponse.json({ error: 'Failed to update profile' }, { status: 500 })
      }
      
      return NextResponse.json({ 
        success: true, 
        message: 'Usuario configurado como admin',
        email: user.email 
      })
    } else {
      return NextResponse.json({ 
        error: 'No user logged in. Please login first.' 
      }, { status: 400 })
    }
  } catch (error) {
    console.error('Setup admin error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}