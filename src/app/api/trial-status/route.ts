import { NextResponse } from 'next/server'

export async function GET() {
  // Este endpoint é apenas para testes
  // Em produção, o estado do trial é gerenciado no cliente
  return NextResponse.json({
    message: 'Trial status is managed client-side',
    info: {
      duration: '5 minutes',
      storage: 'localStorage key: trial-storage',
      features: [
        'Countdown timer persists across page reloads',
        'Warning animation when < 60 seconds',
        'Automatic expiration overlay',
        'Consistent across all channels'
      ]
    }
  })
}