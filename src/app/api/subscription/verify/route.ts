import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { stripe } from '@/lib/stripe'

export async function GET(request: NextRequest) {
  try {
    const supabase = createClient()
    
    // Obter usuário autenticado
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json({
        authenticated: false,
        message: 'Usuário não autenticado',
        details: 'Faça login para verificar sua assinatura'
      })
    }

    // Buscar perfil do usuário
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', user.id)
      .single()

    if (profileError || !profile) {
      return NextResponse.json({
        authenticated: true,
        user: user.email,
        profile: null,
        error: 'Perfil não encontrado'
      })
    }

    // Verificar status no Stripe se tiver customer_id
    let stripeInfo = null
    if (profile.stripe_customer_id) {
      try {
        // Buscar assinatura ativa
        const subscriptions = await stripe.subscriptions.list({
          customer: profile.stripe_customer_id,
          status: 'active',
          limit: 1
        })

        if (subscriptions.data.length > 0) {
          const sub = subscriptions.data[0]
          stripeInfo = {
            subscriptionId: sub.id,
            status: sub.status,
            currentPeriodEnd: new Date(sub.current_period_end * 1000).toISOString(),
            cancelAtPeriodEnd: sub.cancel_at_period_end
          }
        }

        // Buscar customer no Stripe
        const customer = await stripe.customers.retrieve(profile.stripe_customer_id)
        if (customer && !customer.deleted) {
          stripeInfo = {
            ...stripeInfo,
            customerEmail: customer.email,
            customerId: customer.id
          }
        }
      } catch (stripeError) {
        console.error('Erro ao buscar dados do Stripe:', stripeError)
      }
    }

    // Verificar se tem acesso (baseado no trial_end_date ou subscription)
    const now = new Date()
    const trialEndDate = profile.trial_end_date ? new Date(profile.trial_end_date) : null
    const hasActiveTrial = trialEndDate && trialEndDate > now
    const hasActiveSubscription = profile.subscription_status === 'active' && 
      profile.current_period_end && 
      new Date(profile.current_period_end) > now

    return NextResponse.json({
      success: true,
      authenticated: true,
      user: {
        id: user.id,
        email: user.email
      },
      profile: {
        displayName: profile.display_name,
        subscriptionTier: profile.subscription_tier,
        subscriptionStatus: profile.subscription_status,
        currentPeriodEnd: profile.current_period_end,
        trialEndDate: profile.trial_end_date,
        stripeCustomerId: profile.stripe_customer_id,
        createdAt: profile.created_at,
        updatedAt: profile.updated_at
      },
      access: {
        hasAccess: hasActiveSubscription || hasActiveTrial,
        hasActiveSubscription,
        hasActiveTrial,
        reason: hasActiveSubscription ? 'Assinatura ativa' : 
                hasActiveTrial ? 'Período de trial ativo' : 
                'Sem acesso ativo'
      },
      stripe: stripeInfo,
      debug: {
        now: now.toISOString(),
        trialEndDate: trialEndDate?.toISOString(),
        subscriptionEndDate: profile.current_period_end ? new Date(profile.current_period_end).toISOString() : null
      }
    })

  } catch (error) {
    console.error('Erro ao verificar assinatura:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Erro ao verificar assinatura',
        details: error instanceof Error ? error.message : 'Erro desconhecido'
      },
      { status: 500 }
    )
  }
}