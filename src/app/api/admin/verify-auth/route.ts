import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import { createClient } from '@/lib/supabase/server'

export async function GET(request: NextRequest) {
  try {
    const cookieStore = await cookies()
    const adminAuth = cookieStore.get('admin-auth')
    const adminUserId = cookieStore.get('admin-user-id')

    if (!adminAuth || adminAuth.value !== 'true' || !adminUserId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Verify the user is still an admin in the database
    const supabase = await createClient()
    const { data: profile, error } = await supabase
      .from('stack_profiles')
      .select('role')
      .eq('id', adminUserId.value)
      .single()

    if (error || profile?.role !== 'admin') {
      // Remove invalid cookies
      cookieStore.delete('admin-auth')
      cookieStore.delete('admin-user-id')
      
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Admin verify auth error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}