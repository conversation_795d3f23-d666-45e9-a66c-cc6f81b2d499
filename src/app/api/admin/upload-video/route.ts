import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import { writeFile, unlink } from 'fs/promises'
import { join } from 'path'

// POST - Upload de vídeo tutorial
export async function POST(request: NextRequest) {
  try {
    console.log('[UPLOAD-VIDEO] Iniciando upload de vídeo')
    
    // Verificar cookie de admin
    const cookieStore = await cookies()
    const authCookie = cookieStore.get('admin-auth')
    console.log('[UPLOAD-VIDEO] Cookie admin-auth:', authCookie?.value)
    
    if (!authCookie || authCookie.value !== 'true') {
      console.error('[UPLOAD-VIDEO] Acesso negado - cookie inválido')
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
    
    const formData = await request.formData()
    const file = formData.get('file') as File
    
    if (!file) {
      console.error('[UPLOAD-VIDEO] Nenhum arquivo enviado')
      return NextResponse.json({ error: 'No file provided' }, { status: 400 })
    }
    
    console.log('[UPLOAD-VIDEO] Arquivo recebido:', {
      name: file.name,
      type: file.type,
      size: file.size
    })
    
    // Validar tipo de arquivo
    const validTypes = ['video/mp4', 'video/webm', 'video/ogg', 'video/quicktime']
    if (!validTypes.includes(file.type)) {
      console.error('[UPLOAD-VIDEO] Tipo de arquivo inválido:', file.type)
      return NextResponse.json({ error: 'Invalid file type' }, { status: 400 })
    }
    
    // Validar tamanho (100MB)
    if (file.size > 104857600) {
      console.error('[UPLOAD-VIDEO] Arquivo muito grande:', file.size)
      return NextResponse.json({ error: 'File too large' }, { status: 400 })
    }
    
    // Converter arquivo para buffer
    const bytes = await file.arrayBuffer()
    const buffer = Buffer.from(bytes)
    
    // Gerar nome único para o arquivo
    const timestamp = Date.now()
    const fileName = `tutorial-${timestamp}-${file.name}`
    
    // Salvar arquivo no diretório public/uploads
    const uploadsDir = join(process.cwd(), 'public', 'uploads')
    const filePath = join(uploadsDir, fileName)
    
    // Criar diretório se não existir
    const fs = await import('fs')
    if (!fs.existsSync(uploadsDir)) {
      fs.mkdirSync(uploadsDir, { recursive: true })
    }
    
    // Salvar arquivo
    await writeFile(filePath, buffer)
    console.log('[UPLOAD-VIDEO] Arquivo salvo em:', filePath)
    
    // URL pública do arquivo
    const publicUrl = `/uploads/${fileName}`
    
    console.log('[UPLOAD-VIDEO] Upload concluído com sucesso')
    console.log('[UPLOAD-VIDEO] URL pública:', publicUrl)
    
    // Atualizar configuração na memória
    const { inMemoryConfig } = await import('../mobile-config/route')
    inMemoryConfig.tutorial_video_file = publicUrl
    inMemoryConfig.tutorial_video_url = null
    inMemoryConfig.updated_at = new Date().toISOString()
    
    console.log('[UPLOAD-VIDEO] Configuração atualizada na memória')
    
    return NextResponse.json({ 
      success: true,
      url: publicUrl,
      fileName,
      size: file.size,
      type: file.type
    })
    
  } catch (error) {
    console.error('[UPLOAD-VIDEO] Error:', error)
    return NextResponse.json({ error: 'Upload failed' }, { status: 500 })
  }
}

// DELETE - Remover vídeo tutorial
export async function DELETE(request: NextRequest) {
  try {
    console.log('[DELETE-VIDEO] Iniciando exclusão de vídeo')
    
    // Verificar cookie de admin
    const cookieStore = await cookies()
    const authCookie = cookieStore.get('admin-auth')
    console.log('[DELETE-VIDEO] Cookie admin-auth:', authCookie?.value)
    
    if (!authCookie || authCookie.value !== 'true') {
      console.error('[DELETE-VIDEO] Acesso negado - cookie inválido')
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
    
    // Por enquanto, apenas retornar sucesso
    // Em produção, você removeria o arquivo do servidor
    console.log('[DELETE-VIDEO] Exclusão simulada com sucesso')
    
    return NextResponse.json({ 
      success: true,
      message: 'Video deleted successfully'
    })
    
  } catch (error) {
    console.error('[DELETE-VIDEO] Error:', error)
    return NextResponse.json({ error: 'Delete failed' }, { status: 500 })
  }
}