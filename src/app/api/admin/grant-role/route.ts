import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import { createClient } from '@/lib/supabase/server'

export async function POST(request: NextRequest) {
  try {
    // Verify admin authentication
    const cookieStore = await cookies()
    const adminAuth = cookieStore.get('admin-auth')
    const adminUserId = cookieStore.get('admin-user-id')

    if (!adminAuth || adminAuth.value !== 'true' || !adminUserId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const supabase = await createClient()

    // Verify the requester is an admin
    const { data: adminProfile, error: adminError } = await supabase
      .from('stack_profiles')
      .select('role')
      .eq('id', adminUserId.value)
      .single()

    if (adminError || adminProfile?.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Get email from request body
    const { email } = await request.json()

    if (!email) {
      return NextResponse.json(
        { error: 'Email is required' },
        { status: 400 }
      )
    }

    // Grant admin role to the specified email
    const { data, error } = await supabase
      .from('stack_profiles')
      .update({ role: 'admin', updated_at: new Date().toISOString() })
      .eq('email', email)
      .select()
      .single()

    if (error) {
      console.error('Error granting admin role:', error)
      return NextResponse.json(
        { error: 'Failed to grant admin role. Make sure the user has logged in at least once.' },
        { status: 500 }
      )
    }

    return NextResponse.json({ 
      success: true, 
      message: `Admin role granted to ${email}`,
      profile: data 
    })
  } catch (error) {
    console.error('Grant admin role error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}