import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'

// Armazenamento temporário em memória (reinicia quando o servidor reinicia)
export let inMemoryConfig = {
  id: 'default',
  android_url: 'https://play.google.com/store/apps/details?id=com.streamplus.espana',
  ios_url: '#',
  qr_code_url: 'https://play.google.com/store/apps/details?id=com.streamplus.espana',
  tutorial_video_url: null as string | null,
  tutorial_video_file: null as string | null,
  android_badge_url: 'https://play.google.com/intl/en_us/badges/static/images/badges/es_badge_web_generic.png',
  ios_badge_url: 'https://developer.apple.com/assets/elements/badges/download-on-the-app-store.svg',
  ios_available: false,
  android_available: true,
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString()
}

// GET - Obter configuração atual
export async function GET(request: NextRequest) {
  try {
    console.log('[MOBILE-CONFIG GET] Retornando configuração da memória')
    return NextResponse.json(inMemoryConfig)
  } catch (error) {
    console.error('[MOBILE-CONFIG GET] Error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// PUT - Atualizar configuração (apenas admin)
export async function PUT(request: NextRequest) {
  try {
    console.log('[MOBILE-CONFIG PUT] Iniciando atualização de configuração')
    
    // Verificar cookie de admin
    const cookieStore = await cookies()
    const authCookie = cookieStore.get('admin-auth')
    console.log('[MOBILE-CONFIG PUT] Cookie admin-auth:', authCookie?.value)
    
    if (!authCookie || authCookie.value !== 'true') {
      console.error('[MOBILE-CONFIG PUT] Acesso negado - cookie inválido')
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
    
    // Obter dados do body
    const body = await request.json()
    console.log('[MOBILE-CONFIG PUT] Dados recebidos:', JSON.stringify(body, null, 2))
    
    // Atualizar configuração na memória
    inMemoryConfig = {
      ...inMemoryConfig,
      ...body,
      updated_at: new Date().toISOString()
    }
    
    console.log('[MOBILE-CONFIG PUT] Configuração atualizada com sucesso')
    console.log('[MOBILE-CONFIG PUT] Nova configuração:', JSON.stringify(inMemoryConfig, null, 2))
    
    return NextResponse.json({ 
      success: true, 
      data: inMemoryConfig,
      message: 'Configuración guardada exitosamente' 
    })
  } catch (error) {
    console.error('[MOBILE-CONFIG PUT] Error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}