import { NextRequest, NextResponse } from 'next/server'
import { createServiceClient } from '@/lib/supabase/server'

export async function POST(request: NextRequest) {
  const timestamp = new Date().toISOString()
  console.log(`[ACTIVATE-SUBSCRIPTION] ${timestamp} - ===== REQUEST RECEIVED =====`)
  
  try {
    const body = await request.json()
    const { email, tier = 'premium', months = 1 } = body
    
    console.log(`[ACTIVATE-SUBSCRIPTION] ${timestamp} - Parameters:`)
    console.log(`[ACTIVATE-SUBSCRIPTION] ${timestamp} -   Email:`, email)
    console.log(`[ACTIVATE-SUBSCRIPTION] ${timestamp} -   Tier:`, tier)
    console.log(`[ACTIVATE-SUBSCRIPTION] ${timestamp} -   Months:`, months)
    
    if (!email) {
      return NextResponse.json({ 
        success: false, 
        error: 'Email é obrigatório' 
      }, { status: 400 })
    }
    
    const supabase = createServiceClient()
    
    // 1. Buscar usuário pelo email
    console.log(`[ACTIVATE-SUBSCRIPTION] ${timestamp} - Buscando usuário...`)
    const { data: { users }, error: authError } = await supabase.auth.admin.listUsers()
    
    if (authError) {
      console.error(`[ACTIVATE-SUBSCRIPTION] ${timestamp} - ❌ Erro ao buscar usuários:`, authError)
      return NextResponse.json({ 
        success: false, 
        error: 'Erro ao buscar usuários' 
      }, { status: 500 })
    }
    
    const user = users.find(u => u.email?.toLowerCase() === email.toLowerCase())
    
    if (!user) {
      console.log(`[ACTIVATE-SUBSCRIPTION] ${timestamp} - ❌ Usuário não encontrado`)
      return NextResponse.json({ 
        success: false, 
        error: 'Usuário não encontrado' 
      }, { status: 404 })
    }
    
    console.log(`[ACTIVATE-SUBSCRIPTION] ${timestamp} - ✅ Usuário encontrado:`, user.id)
    
    // 2. Verificar se já tem perfil
    const { data: existingProfile } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', user.id)
      .single()
    
    const endDate = new Date()
    endDate.setMonth(endDate.getMonth() + months)
    
    const profileData = {
      id: user.id,
      email: user.email,
      full_name: user.user_metadata?.full_name || user.user_metadata?.name || email.split('@')[0],
      subscription_tier: tier,
      subscription_status: 'active',
      subscription_current_period_end: endDate.toISOString(),
      subscription_start_date: new Date().toISOString(),
      stripe_customer_id: `manual_${user.id}`,
      stripe_subscription_id: `manual_sub_${Date.now()}`,
      updated_at: new Date().toISOString()
    }
    
    let result
    
    if (existingProfile) {
      console.log(`[ACTIVATE-SUBSCRIPTION] ${timestamp} - Atualizando perfil existente...`)
      // Atualizar perfil existente
      const { data, error } = await supabase
        .from('profiles')
        .update({
          subscription_tier: profileData.subscription_tier,
          subscription_status: profileData.subscription_status,
          subscription_current_period_end: profileData.subscription_current_period_end,
          subscription_start_date: profileData.subscription_start_date,
          stripe_customer_id: profileData.stripe_customer_id,
          stripe_subscription_id: profileData.stripe_subscription_id,
          updated_at: profileData.updated_at
        })
        .eq('id', user.id)
        .select()
        .single()
      
      if (error) {
        console.error(`[ACTIVATE-SUBSCRIPTION] ${timestamp} - ❌ Erro ao atualizar perfil:`, error)
        return NextResponse.json({ 
          success: false, 
          error: 'Erro ao atualizar perfil',
          details: error.message 
        }, { status: 500 })
      }
      
      result = data
    } else {
      console.log(`[ACTIVATE-SUBSCRIPTION] ${timestamp} - Criando novo perfil...`)
      // Criar novo perfil
      const { data, error } = await supabase
        .from('profiles')
        .insert([profileData])
        .select()
        .single()
      
      if (error) {
        console.error(`[ACTIVATE-SUBSCRIPTION] ${timestamp} - ❌ Erro ao criar perfil:`, error)
        return NextResponse.json({ 
          success: false, 
          error: 'Erro ao criar perfil',
          details: error.message 
        }, { status: 500 })
      }
      
      result = data
    }
    
    console.log(`[ACTIVATE-SUBSCRIPTION] ${timestamp} - ✅ Premium ativado com sucesso`)
    console.log(`[ACTIVATE-SUBSCRIPTION] ${timestamp} -   User ID:`, user.id)
    console.log(`[ACTIVATE-SUBSCRIPTION] ${timestamp} -   Tier:`, tier)
    console.log(`[ACTIVATE-SUBSCRIPTION] ${timestamp} -   Expira em:`, endDate.toISOString())
    
    // 3. Registrar no histórico de pagamentos
    await supabase
      .from('payment_history')
      .insert({
        user_id: user.id,
        customer_id: profileData.stripe_customer_id,
        subscription_id: profileData.stripe_subscription_id,
        amount: 0, // Manual activation
        currency: 'brl',
        status: 'succeeded',
        payment_method: 'manual_activation',
        description: `Ativação manual - ${tier} - ${months} mês(es)`,
        metadata: {
          tier,
          months,
          activated_by: 'manual',
          activated_at: new Date().toISOString()
        }
      })
    
    return NextResponse.json({ 
      success: true, 
      message: `Premium ${tier} ativado com sucesso para ${email}`,
      profile: result,
      expiresAt: endDate.toISOString()
    })
    
  } catch (error) {
    console.error(`[ACTIVATE-SUBSCRIPTION] ${timestamp} - ❌ Erro geral:`, error)
    return NextResponse.json({ 
      success: false, 
      error: 'Erro interno do servidor',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}