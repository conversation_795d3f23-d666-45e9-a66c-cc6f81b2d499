import { NextRequest, NextResponse } from 'next/server'
import { createCustomerPortalSession } from '@/lib/stripe'
import { createClient } from '@/lib/supabase/server'

export async function POST(request: NextRequest) {
  try {
    // Verificar autenticação
    const supabase = await createClient()
    const { data: { user } } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json(
        { error: 'No autorizado' },
        { status: 401 }
      )
    }

    // Buscar customer ID do perfil
    const { data: profile } = await supabase
      .from('profiles')
      .select('stripe_customer_id')
      .eq('id', user.id)
      .single()

    if (!profile?.stripe_customer_id) {
      return NextResponse.json(
        { error: 'Cliente Stripe no encontrado' },
        { status: 404 }
      )
    }

    // Obter URL de retorno
    const { returnUrl = `${process.env.NEXT_PUBLIC_APP_URL}/account` } = await request.json()

    // <PERSON><PERSON>r sessão do portal
    const session = await createCustomerPortalSession({
      customerId: profile.stripe_customer_id,
      returnUrl,
    })

    return NextResponse.json({
      success: true,
      url: session.url,
    })

  } catch (error) {
    console.error('Error al crear portal del cliente:', error)
    return NextResponse.json(
      { error: 'Error al crear portal del cliente' },
      { status: 500 }
    )
  }
}