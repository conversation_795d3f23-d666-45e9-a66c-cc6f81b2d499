import { NextRequest, NextResponse } from 'next/server'
import { createCheckoutSession, createOrGetStripeCustomer, STRIPE_CONFIG } from '@/lib/stripe'
import { createClient } from '@/lib/supabase/server'

export async function POST(request: NextRequest) {
  try {
    // Obter dados do request
    const { 
      priceId = STRIPE_CONFIG.priceId,
      successUrl,
      cancelUrl,
      trialDays,
      couponId,
      email,
      name,
    } = await request.json()

    // Email é obrigatório
    if (!email) {
      return NextResponse.json(
        { error: 'Email es requerido' },
        { status: 400 }
      )
    }

    // Criar ou obter cliente Stripe
    const customer = await createOrGetStripeCustomer({
      email,
      name: name || email.split('@')[0],
    })

    // Criar sessão de checkout
    const session = await createCheckoutSession({
      customerId: customer.id,
      priceId,
      successUrl: successUrl || STRIPE_CONFIG.successUrl,
      cancelUrl: cancelUrl || STRIPE_CONFIG.cancelUrl,
      trialDays,
      couponId,
    })

    return NextResponse.json({
      success: true,
      sessionId: session.id,
      url: session.url,
    })

  } catch (error) {
    console.error('Error al crear sesión de checkout:', error)
    const errorMessage = error instanceof Error ? error.message : 'Error desconocido'
    const errorDetails = error instanceof Error && 'type' in error ? (error as any).type : ''
    
    return NextResponse.json(
      { 
        error: 'Error al crear sesión de pago',
        details: errorMessage,
        type: errorDetails,
        // Incluir URLs para debug
        urls: {
          success: STRIPE_CONFIG.successUrl,
          cancel: STRIPE_CONFIG.cancelUrl
        }
      },
      { status: 500 }
    )
  }
}