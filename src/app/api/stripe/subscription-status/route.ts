import { NextRequest, NextResponse } from 'next/server'
import { getSubscriptionStatus } from '@/lib/stripe'
import { createClient } from '@/lib/supabase/server'
import { stackServerApp } from '@/lib/auth/stack-server'

export async function GET(request: NextRequest) {
  const timestamp = new Date().toISOString()
  console.log(`[SUBSCRIPTION-STATUS-API] ${timestamp} - ===== CHECKING STATUS =====`)
  
  try {
    // Verificar autenticação
    const supabase = await createClient()
    const { data: { user } } = await supabase.auth.getUser()

    console.log(`[SUBSCRIPTION-STATUS-API] ${timestamp} - User authenticated:`, !!user)
    console.log(`[SUBSCRIPTION-STATUS-API] ${timestamp} - User ID:`, user?.id)

    if (!user) {
      console.log(`[SUBSCRIPTION-STATUS-API] ${timestamp} - ❌ No user authenticated`)
      return NextResponse.json(
        { error: 'No autorizado' },
        { status: 401 }
      )
    }

    // Buscar perfil com informações do Stripe
    console.log(`[SUBSCRIPTION-STATUS-API] ${timestamp} - Fetching profile...`)
    let profile = null
    let isStackAuth = false
    let stackUserId = null

    // Primeiro tentar buscar na tabela profiles (Supabase Auth)
    const { data: supabaseProfile, error: profileError } = await supabase
      .from('profiles')
      .select('stripe_customer_id, subscription_status, subscription_current_period_end, trial_end, subscription_tier')
      .eq('id', user.id)
      .single()

    if (profileError) {
      console.error(`[SUBSCRIPTION-STATUS-API] ${timestamp} - ❌ Error fetching profile:`, profileError)
    }

    profile = supabaseProfile

    // Se não encontrou ou está vazio, tentar buscar na stack_profiles
    if (!profile || (!profile.subscription_tier && !profile.subscription_status)) {
      console.log(`[SUBSCRIPTION-STATUS-API] ${timestamp} - Checking stack_profiles...`)
      
      // Buscar Stack Auth ID pelo email
      try {
        const users = await stackServerApp.listUsers()
        const stackUser = users.find(u => u.primaryEmail === user.email)
        
        if (stackUser) {
          stackUserId = stackUser.id
          console.log(`[SUBSCRIPTION-STATUS-API] ${timestamp} - Stack Auth ID found:`, stackUserId)
          
          // Buscar perfil na stack_profiles
          const { data: stackProfile } = await supabase
            .from('stack_profiles')
            .select('stripe_customer_id, subscription_status, subscription_current_period_end, trial_end, subscription_tier')
            .eq('id', stackUserId)
            .single()
          
          if (stackProfile) {
            profile = stackProfile
            isStackAuth = true
            console.log(`[SUBSCRIPTION-STATUS-API] ${timestamp} - ✅ Found in stack_profiles`)
          }
        }
      } catch (error) {
        console.error(`[SUBSCRIPTION-STATUS-API] ${timestamp} - Error checking stack_profiles:`, error)
      }
    }

    console.log(`[SUBSCRIPTION-STATUS-API] ${timestamp} - Profile data:`)
    console.log(`[SUBSCRIPTION-STATUS-API] ${timestamp} -   From table:`, isStackAuth ? 'stack_profiles' : 'profiles')
    console.log(`[SUBSCRIPTION-STATUS-API] ${timestamp} -   stripe_customer_id:`, profile?.stripe_customer_id)
    console.log(`[SUBSCRIPTION-STATUS-API] ${timestamp} -   subscription_status:`, profile?.subscription_status)
    console.log(`[SUBSCRIPTION-STATUS-API] ${timestamp} -   subscription_tier:`, profile?.subscription_tier)
    console.log(`[SUBSCRIPTION-STATUS-API] ${timestamp} -   subscription_current_period_end:`, profile?.subscription_current_period_end)
    console.log(`[SUBSCRIPTION-STATUS-API] ${timestamp} -   trial_end:`, profile?.trial_end)

    if (!profile) {
      console.log(`[SUBSCRIPTION-STATUS-API] ${timestamp} - ❌ No profile found in either table`)
      return NextResponse.json({
        success: true,
        hasAccess: false,
        isSubscribed: false,
        isInTrial: false,
      })
    }

    // Verificar se tem trial ativo
    const now = new Date()
    const isInTrial = profile.trial_end && new Date(profile.trial_end) > now
    
    console.log(`[SUBSCRIPTION-STATUS-API] ${timestamp} - Trial check:`)
    console.log(`[SUBSCRIPTION-STATUS-API] ${timestamp} -   Has trial_end:`, !!profile.trial_end)
    console.log(`[SUBSCRIPTION-STATUS-API] ${timestamp} -   Trial end date:`, profile.trial_end)
    console.log(`[SUBSCRIPTION-STATUS-API] ${timestamp} -   Current time:`, now.toISOString())
    console.log(`[SUBSCRIPTION-STATUS-API] ${timestamp} -   Is in trial:`, isInTrial)

    // Verificar se tem assinatura ativa no Stripe
    let hasActiveSubscription = false
    if (profile.stripe_customer_id) {
      console.log(`[SUBSCRIPTION-STATUS-API] ${timestamp} - Checking Stripe subscription...`)
      const stripeStatus = await getSubscriptionStatus(profile.stripe_customer_id)
      hasActiveSubscription = stripeStatus?.status === 'active'
      console.log(`[SUBSCRIPTION-STATUS-API] ${timestamp} - Stripe status:`, stripeStatus?.status)
      console.log(`[SUBSCRIPTION-STATUS-API] ${timestamp} - Has active Stripe subscription:`, hasActiveSubscription)
    } else {
      console.log(`[SUBSCRIPTION-STATUS-API] ${timestamp} - No Stripe customer ID`)
    }

    // Verificar se tem assinatura ativa local
    const hasLocalSubscription = 
      profile.subscription_status === 'active' && 
      profile.subscription_current_period_end &&
      new Date(profile.subscription_current_period_end) > now
    
    console.log(`[SUBSCRIPTION-STATUS-API] ${timestamp} - Local subscription check:`)
    console.log(`[SUBSCRIPTION-STATUS-API] ${timestamp} -   Status is active:`, profile.subscription_status === 'active')
    console.log(`[SUBSCRIPTION-STATUS-API] ${timestamp} -   Has period end:`, !!profile.subscription_current_period_end)
    if (profile.subscription_current_period_end) {
      console.log(`[SUBSCRIPTION-STATUS-API] ${timestamp} -   Period end date:`, profile.subscription_current_period_end)
      console.log(`[SUBSCRIPTION-STATUS-API] ${timestamp} -   Is valid:`, new Date(profile.subscription_current_period_end) > now)
    }
    console.log(`[SUBSCRIPTION-STATUS-API] ${timestamp} -   Has local subscription:`, hasLocalSubscription)

    // Verificar tier premium
    const hasPremiumTier = profile.subscription_tier === 'premium'
    console.log(`[SUBSCRIPTION-STATUS-API] ${timestamp} - Has premium tier:`, hasPremiumTier)
    
    // IMPORTANTE: Se tem premium, NÃO está em trial!
    const isPremiumUser = hasActiveSubscription || hasLocalSubscription || hasPremiumTier
    const isReallyInTrial = isInTrial && !isPremiumUser // Só está em trial se NÃO for premium

    const hasAccess = isInTrial || hasActiveSubscription || hasLocalSubscription || hasPremiumTier
    
    console.log(`[SUBSCRIPTION-STATUS-API] ${timestamp} - ===== FINAL RESULT =====`)
    console.log(`[SUBSCRIPTION-STATUS-API] ${timestamp} - Has access:`, hasAccess)
    console.log(`[SUBSCRIPTION-STATUS-API] ${timestamp} -   Is premium user:`, isPremiumUser)
    console.log(`[SUBSCRIPTION-STATUS-API] ${timestamp} -   Is really in trial:`, isReallyInTrial)
    console.log(`[SUBSCRIPTION-STATUS-API] ${timestamp} -   Has active Stripe subscription:`, hasActiveSubscription)
    console.log(`[SUBSCRIPTION-STATUS-API] ${timestamp} -   Has local subscription:`, hasLocalSubscription)
    console.log(`[SUBSCRIPTION-STATUS-API] ${timestamp} -   Has premium tier:`, hasPremiumTier)

    // Stack Auth ID já foi obtido anteriormente

    return NextResponse.json({
      success: true,
      hasAccess,
      isSubscribed: isPremiumUser,
      isInTrial: isReallyInTrial,
      trialEndsAt: profile.trial_end,
      subscriptionEndsAt: profile.subscription_current_period_end,
      subscriptionTier: profile.subscription_tier,
      userId: stackUserId || user.id,
      email: user.email,
      authType: stackUserId ? 'stack' : 'supabase'
    })

  } catch (error) {
    console.error('Error al verificar estado de la suscripción:', error)
    return NextResponse.json(
      { error: 'Error al verificar estado' },
      { status: 500 }
    )
  }
}