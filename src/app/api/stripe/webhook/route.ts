import { NextRequest, NextResponse } from 'next/server'
import { headers } from 'next/headers'
import Strip<PERSON> from 'stripe'
import { stripe } from '@/lib/stripe'
import { createClient } from '@/lib/supabase/server'

const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET!

function getTimestamp() {
  const now = new Date()
  const year = now.getFullYear()
  const month = String(now.getMonth() + 1).padStart(2, '0')
  const day = String(now.getDate()).padStart(2, '0')
  const hours = String(now.getHours()).padStart(2, '0')
  const minutes = String(now.getMinutes()).padStart(2, '0')
  const seconds = String(now.getSeconds()).padStart(2, '0')
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}

async function syncProfileTables(supabase: any, userId: string, updateData: any) {
  const timestamp = getTimestamp()
  console.log(`[STRIPE-WEBHOOK] ${timestamp} - Starting profile sync for user: ${userId}`)
  
  const profilesResult = await supabase
    .from('profiles')
    .update(updateData)
    .eq('id', userId)
    .select()
  
  if (profilesResult.error) {
    console.log(`[STRIPE-WEBHOOK] ${timestamp} - No profile found in 'profiles' table`)
  } else {
    console.log(`[STRIPE-WEBHOOK] ${timestamp} - ✅ Updated 'profiles' table`)
  }
  
  const stackProfilesResult = await supabase
    .from('stack_profiles')
    .update(updateData)
    .eq('id', userId)
    .select()
  
  if (stackProfilesResult.error) {
    console.log(`[STRIPE-WEBHOOK] ${timestamp} - No profile found in 'stack_profiles' table`)
  } else {
    console.log(`[STRIPE-WEBHOOK] ${timestamp} - ✅ Updated 'stack_profiles' table`)
  }
  
  return {
    profiles: profilesResult.data,
    stack_profiles: stackProfilesResult.data,
    hasProfile: !profilesResult.error && profilesResult.data?.length > 0,
    hasStackProfile: !stackProfilesResult.error && stackProfilesResult.data?.length > 0
  }
}

async function logWebhookEvent(supabase: any, event: Stripe.Event, status: 'success' | 'error', error?: string) {
  const timestamp = getTimestamp()
  console.log(`[STRIPE-WEBHOOK] ${timestamp} - Logging webhook event to stripe_webhook_events table`)
  
  const { error: logError } = await supabase
    .from('stripe_webhook_events')
    .insert({
      event_id: event.id,
      event_type: event.type,
      status: status,
      error_message: error,
      payload: event
    })
  
  if (logError) {
    console.error(`[STRIPE-WEBHOOK] ${timestamp} - ❌ Failed to log webhook event:`, logError)
  } else {
    console.log(`[STRIPE-WEBHOOK] ${timestamp} - ✅ Webhook event logged`)
  }
}

async function syncStripeSubscription(
  supabase: any, 
  userId: string, 
  customerId: string, 
  subscriptionId: string, 
  subscription: Stripe.Subscription
) {
  const timestamp = getTimestamp()
  console.log(`[STRIPE-WEBHOOK] ${timestamp} - Syncing Stripe subscription using sync_stripe_subscription function`)
  
  try {
    const { data, error } = await supabase.rpc('sync_stripe_subscription', {
      p_user_id: userId,
      p_stripe_customer_id: customerId,
      p_stripe_subscription_id: subscriptionId,
      p_status: subscription.status,
      p_current_period_end: new Date(subscription.current_period_end * 1000).toISOString(),
      p_cancel_at_period_end: subscription.cancel_at_period_end || false,
      p_canceled_at: subscription.canceled_at ? new Date(subscription.canceled_at * 1000).toISOString() : null,
      p_trial_end: subscription.trial_end ? new Date(subscription.trial_end * 1000).toISOString() : null
    })
    
    if (error) {
      console.error(`[STRIPE-WEBHOOK] ${timestamp} - ❌ Error calling sync_stripe_subscription:`, error)
      throw error
    }
    
    console.log(`[STRIPE-WEBHOOK] ${timestamp} - ✅ Subscription synced successfully`)
    return data
  } catch (err) {
    console.error(`[STRIPE-WEBHOOK] ${timestamp} - ❌ Failed to sync subscription:`, err)
    throw err
  }
}

export async function POST(request: NextRequest) {
  const timestamp = getTimestamp()
  console.log(`[STRIPE-WEBHOOK] ${timestamp} - ===== WEBHOOK RECEIVED =====`)
  
  const body = await request.text()
  const headersList = await headers()
  const signature = headersList.get('stripe-signature')!

  console.log(`[STRIPE-WEBHOOK] ${timestamp} - Webhook signature:`, signature?.substring(0, 20) + '...')

  let event: Stripe.Event

  try {
    event = stripe.webhooks.constructEvent(body, signature, webhookSecret)
    console.log(`[STRIPE-WEBHOOK] ${timestamp} - ✅ Webhook signature verified`)
    console.log(`[STRIPE-WEBHOOK] ${timestamp} - Event type:`, event.type)
    console.log(`[STRIPE-WEBHOOK] ${timestamp} - Event ID:`, event.id)
  } catch (err) {
    console.error(`[STRIPE-WEBHOOK] ${timestamp} - ❌ Error verifying webhook signature:`, err)
    return NextResponse.json(
      { error: 'Invalid signature' },
      { status: 400 }
    )
  }

  const supabase = await createClient()

  try {
    console.log(`[STRIPE-WEBHOOK] ${timestamp} - Processing event:`, event.type)
    
    // Log webhook event to database
    await logWebhookEvent(supabase, event, 'success')
    
    switch (event.type) {
      // Pagamento bem-sucedido
      case 'checkout.session.completed': {
        const session = event.data.object as Stripe.Checkout.Session
        const timestamp = getTimestamp()
        
        console.log(`[STRIPE-WEBHOOK] ${timestamp} - ===== PROCESSING checkout.session.completed =====`)
        console.log(`[STRIPE-WEBHOOK] ${timestamp} - Session ID:`, session.id)
        console.log(`[STRIPE-WEBHOOK] ${timestamp} - Session mode:`, session.mode)
        console.log(`[STRIPE-WEBHOOK] ${timestamp} - Customer ID:`, session.customer)
        console.log(`[STRIPE-WEBHOOK] ${timestamp} - Subscription ID:`, session.subscription)
        console.log(`[STRIPE-WEBHOOK] ${timestamp} - Payment status:`, session.payment_status)
        console.log(`[STRIPE-WEBHOOK] ${timestamp} - Amount total:`, session.amount_total)
        
        if (session.mode === 'subscription') {
          const customerId = session.customer as string
          const subscriptionId = session.subscription as string
          
          console.log(`[STRIPE-WEBHOOK] ${timestamp} - Fetching subscription details from Stripe...`)
          const subscription = await stripe.subscriptions.retrieve(subscriptionId)
          
          console.log(`[STRIPE-WEBHOOK] ${timestamp} - Subscription status:`, subscription.status)
          console.log(`[STRIPE-WEBHOOK] ${timestamp} - Subscription current_period_end:`, subscription.current_period_end)
          console.log(`[STRIPE-WEBHOOK] ${timestamp} - Subscription current_period_end (date):`, new Date(subscription.current_period_end * 1000).toISOString())
          console.log(`[STRIPE-WEBHOOK] ${timestamp} - Subscription items:`, subscription.items.data.map(i => i.price.product))
          
          console.log(`[STRIPE-WEBHOOK] ${timestamp} - Fetching customer details from Stripe...`)
          const customer = await stripe.customers.retrieve(customerId) as Stripe.Customer
          
          console.log(`[STRIPE-WEBHOOK] ${timestamp} - Customer email:`, customer.email)
          console.log(`[STRIPE-WEBHOOK] ${timestamp} - Customer name:`, customer.name)
          
          if (!customer.email) {
            console.error(`[STRIPE-WEBHOOK] ${timestamp} - ❌ Customer has no email`)
            throw new Error('Customer has no email')
          }
          
          console.log(`[STRIPE-WEBHOOK] ${timestamp} - ===== SEARCHING FOR USER =====`)
          console.log(`[STRIPE-WEBHOOK] ${timestamp} - Looking for user by email:`, customer.email)
          
          const { data: authUser, error: authError } = await supabase.auth.admin.getUserByEmail(customer.email)
          
          if (authError || !authUser) {
            console.error(`[STRIPE-WEBHOOK] ${timestamp} - ❌ User not found in auth.users table`)
            console.error(`[STRIPE-WEBHOOK] ${timestamp} - Auth error:`, authError)
            
            console.log(`[STRIPE-WEBHOOK] ${timestamp} - Checking stack_profiles table directly...`)
            // Buscar primeiro com email exato
            let { data: stackProfile, error: stackError } = await supabase
              .from('stack_profiles')
              .select('*')
              .eq('email', customer.email)
              .single()
            
            // Se não encontrar, tentar com ILIKE (case insensitive)
            if (stackError || !stackProfile) {
              console.log(`[STRIPE-WEBHOOK] ${timestamp} - Trying case-insensitive search...`)
              const { data: profiles } = await supabase
                .from('stack_profiles')
                .select('*')
                .ilike('email', customer.email)
              
              if (profiles && profiles.length > 0) {
                stackProfile = profiles[0]
                stackError = null
                console.log(`[STRIPE-WEBHOOK] ${timestamp} - Found with case-insensitive search`)
              }
            }
            
            if (stackError || !stackProfile) {
              console.error(`[STRIPE-WEBHOOK] ${timestamp} - ❌ User not found in stack_profiles either`)
              console.error(`[STRIPE-WEBHOOK] ${timestamp} - Stack error:`, stackError)
              throw new Error(`User not found for email: ${customer.email}`)
            }
            
            console.log(`[STRIPE-WEBHOOK] ${timestamp} - ✅ User found in stack_profiles:`, stackProfile.id)
            console.log(`[STRIPE-WEBHOOK] ${timestamp} - Stack profile email:`, stackProfile.email)
            
            // Use the sync function for stack_profiles user
            try {
              await syncStripeSubscription(
                supabase,
                stackProfile.id,
                customerId,
                subscriptionId,
                subscription
              )
              
              console.log(`[STRIPE-WEBHOOK] ${timestamp} - ✅ Subscription synced for stack_profiles user`)
              
              // Update the profile tables with subscription data
              const updateData = {
                stripe_customer_id: customerId,
                stripe_subscription_id: subscriptionId,
                subscription_status: subscription.status,
                subscription_current_period_end: new Date(subscription.current_period_end * 1000).toISOString(),
                subscription_expires_at: new Date(subscription.current_period_end * 1000).toISOString(), // IMPORTANTE: Campo usado pelo sistema
                subscription_tier: 'premium',
                updated_at: new Date().toISOString(),
              }
              
              await syncProfileTables(supabase, stackProfile.id, updateData)
              
              // Verify the update
              const { data: verifyStackProfile } = await supabase
                .from('stack_profiles')
                .select('*')
                .eq('id', stackProfile.id)
                .single()
              
              console.log(`[STRIPE-WEBHOOK] ${timestamp} - ===== STACK PROFILE VERIFICATION =====`)
              console.log(`[STRIPE-WEBHOOK] ${timestamp} - subscription_tier:`, verifyStackProfile?.subscription_tier)
              console.log(`[STRIPE-WEBHOOK] ${timestamp} - subscription_status:`, verifyStackProfile?.subscription_status)
              console.log(`[STRIPE-WEBHOOK] ${timestamp} - subscription_current_period_end:`, verifyStackProfile?.subscription_current_period_end)
              console.log(`[STRIPE-WEBHOOK] ${timestamp} - stripe_customer_id:`, verifyStackProfile?.stripe_customer_id)
              console.log(`[STRIPE-WEBHOOK] ${timestamp} - stripe_subscription_id:`, verifyStackProfile?.stripe_subscription_id)
            } catch (syncError) {
              console.error(`[STRIPE-WEBHOOK] ${timestamp} - ❌ Error syncing subscription for stack_profiles user:`, syncError)
              throw syncError
            }
          } else {
            console.log(`[STRIPE-WEBHOOK] ${timestamp} - ✅ User found in auth.users:`, authUser.user.id)
            console.log(`[STRIPE-WEBHOOK] ${timestamp} - User email verified:`, authUser.user.email_confirmed_at)
            
            // Use the sync function for auth.users user
            try {
              await syncStripeSubscription(
                supabase,
                authUser.user.id,
                customerId,
                subscriptionId,
                subscription
              )
              
              console.log(`[STRIPE-WEBHOOK] ${timestamp} - ✅ Subscription synced for auth.users user`)
              
              // Update the profile tables with subscription data
              const updateData = {
                stripe_customer_id: customerId,
                stripe_subscription_id: subscriptionId,
                subscription_status: subscription.status,
                subscription_current_period_end: new Date(subscription.current_period_end * 1000).toISOString(),
                subscription_expires_at: new Date(subscription.current_period_end * 1000).toISOString(), // IMPORTANTE: Campo usado pelo sistema
                subscription_tier: 'premium',
                updated_at: new Date().toISOString(),
              }
              
              console.log(`[STRIPE-WEBHOOK] ${timestamp} - ===== SYNCING BOTH TABLES =====`)
              const syncResult = await syncProfileTables(supabase, authUser.user.id, updateData)
              
              console.log(`[STRIPE-WEBHOOK] ${timestamp} - Sync result:`)
              console.log(`[STRIPE-WEBHOOK] ${timestamp} - - Has profile in 'profiles':`, syncResult.hasProfile)
              console.log(`[STRIPE-WEBHOOK] ${timestamp} - - Has profile in 'stack_profiles':`, syncResult.hasStackProfile)
              
              if (!syncResult.hasProfile && !syncResult.hasStackProfile) {
                console.error(`[STRIPE-WEBHOOK] ${timestamp} - ❌ User has no profile in either table`)
                throw new Error('User has no profile in either table')
              }
              
              console.log(`[STRIPE-WEBHOOK] ${timestamp} - ===== PROFILE VERIFICATION =====`)
              
              if (syncResult.hasProfile) {
                const { data: verifyProfile } = await supabase
                  .from('profiles')
                  .select('*')
                  .eq('id', authUser.user.id)
                  .single()
                
                console.log(`[STRIPE-WEBHOOK] ${timestamp} - Profiles table verification:`)
                console.log(`[STRIPE-WEBHOOK] ${timestamp} - - subscription_tier:`, verifyProfile?.subscription_tier)
                console.log(`[STRIPE-WEBHOOK] ${timestamp} - - subscription_status:`, verifyProfile?.subscription_status)
                console.log(`[STRIPE-WEBHOOK] ${timestamp} - - subscription_current_period_end:`, verifyProfile?.subscription_current_period_end)
              }
              
              if (syncResult.hasStackProfile) {
                const { data: verifyStackProfile } = await supabase
                  .from('stack_profiles')
                  .select('*')
                  .eq('id', authUser.user.id)
                  .single()
                
                console.log(`[STRIPE-WEBHOOK] ${timestamp} - Stack_profiles table verification:`)
                console.log(`[STRIPE-WEBHOOK] ${timestamp} - - subscription_tier:`, verifyStackProfile?.subscription_tier)
                console.log(`[STRIPE-WEBHOOK] ${timestamp} - - subscription_status:`, verifyStackProfile?.subscription_status)
                console.log(`[STRIPE-WEBHOOK] ${timestamp} - - subscription_current_period_end:`, verifyStackProfile?.subscription_current_period_end)
              }
            } catch (syncError) {
              console.error(`[STRIPE-WEBHOOK] ${timestamp} - ❌ Error syncing subscription for auth.users user:`, syncError)
              throw syncError
            }
          }
          
          console.log(`[STRIPE-WEBHOOK] ${timestamp} - ✅ Subscription activated for customer ${customerId}`)
          console.log(`[STRIPE-WEBHOOK] ${timestamp} - ===== END checkout.session.completed =====`)
        }
        break
      }

      // Renovação bem-sucedida
      case 'invoice.payment_succeeded': {
        const invoice = event.data.object as Stripe.Invoice
        const timestamp = getTimestamp()
        
        console.log(`[STRIPE-WEBHOOK] ${timestamp} - ===== PROCESSING invoice.payment_succeeded =====`)
        console.log(`[STRIPE-WEBHOOK] ${timestamp} - Invoice ID:`, invoice.id)
        console.log(`[STRIPE-WEBHOOK] ${timestamp} - Invoice subscription:`, invoice.subscription)
        console.log(`[STRIPE-WEBHOOK] ${timestamp} - Invoice amount paid:`, invoice.amount_paid)
        console.log(`[STRIPE-WEBHOOK] ${timestamp} - Invoice customer:`, invoice.customer)
        
        if (invoice.subscription) {
          const subscriptionId = invoice.subscription as string
          console.log(`[STRIPE-WEBHOOK] ${timestamp} - Fetching subscription for renewal...`)
          const subscription = await stripe.subscriptions.retrieve(subscriptionId)
          
          console.log(`[STRIPE-WEBHOOK] ${timestamp} - Subscription status:`, subscription.status)
          console.log(`[STRIPE-WEBHOOK] ${timestamp} - Subscription current_period_end:`, new Date(subscription.current_period_end * 1000).toISOString())
          
          const updateData = {
            subscription_status: subscription.status,
            subscription_current_period_end: new Date(subscription.current_period_end * 1000).toISOString(),
            subscription_expires_at: new Date(subscription.current_period_end * 1000).toISOString(), // IMPORTANTE: Campo usado pelo sistema
            subscription_tier: 'premium',
            updated_at: new Date().toISOString(),
          }
          
          console.log(`[STRIPE-WEBHOOK] ${timestamp} - Updating profile for renewal:`, JSON.stringify(updateData, null, 2))
          
          console.log(`[STRIPE-WEBHOOK] ${timestamp} - Updating profiles table...`)
          const { data: profilesUpdate, error: profilesError } = await supabase
            .from('profiles')
            .update(updateData)
            .eq('stripe_subscription_id', subscriptionId)
            .select()
          
          if (profilesError) {
            console.log(`[STRIPE-WEBHOOK] ${timestamp} - No profile found in 'profiles' with this subscription`)
          } else if (profilesUpdate && profilesUpdate.length > 0) {
            console.log(`[STRIPE-WEBHOOK] ${timestamp} - ✅ Updated ${profilesUpdate.length} profile(s) in 'profiles' table`)
          }
          
          console.log(`[STRIPE-WEBHOOK] ${timestamp} - Updating stack_profiles table...`)
          const { data: stackProfilesUpdate, error: stackProfilesError } = await supabase
            .from('stack_profiles')
            .update(updateData)
            .eq('stripe_subscription_id', subscriptionId)
            .select()
          
          if (stackProfilesError) {
            console.log(`[STRIPE-WEBHOOK] ${timestamp} - No profile found in 'stack_profiles' with this subscription`)
          } else if (stackProfilesUpdate && stackProfilesUpdate.length > 0) {
            console.log(`[STRIPE-WEBHOOK] ${timestamp} - ✅ Updated ${stackProfilesUpdate.length} profile(s) in 'stack_profiles' table`)
          }
          
          if (profilesError && stackProfilesError) {
            console.error(`[STRIPE-WEBHOOK] ${timestamp} - ❌ No profile found in either table with subscription ID:`, subscriptionId)
          }
          
          console.log(`[STRIPE-WEBHOOK] ${timestamp} - Looking up user for payment history...`)
          let userId = null
          
          if (profilesUpdate && profilesUpdate.length > 0) {
            userId = profilesUpdate[0].id
            console.log(`[STRIPE-WEBHOOK] ${timestamp} - Found user in profiles table:`, userId)
          } else if (stackProfilesUpdate && stackProfilesUpdate.length > 0) {
            userId = stackProfilesUpdate[0].id
            console.log(`[STRIPE-WEBHOOK] ${timestamp} - Found user in stack_profiles table:`, userId)
          }
          
          if (userId) {
            console.log(`[STRIPE-WEBHOOK] ${timestamp} - Recording payment for user:`, userId)
            const { error: paymentError } = await supabase
              .from('payment_history')
              .insert({
                user_id: userId,
                stripe_invoice_id: invoice.id,
                amount: (invoice.amount_paid / 100),
                currency: invoice.currency,
                status: 'paid',
                paid_at: new Date().toISOString(),
              })
            
            if (paymentError) {
              console.error(`[STRIPE-WEBHOOK] ${timestamp} - ❌ Error recording payment:`, paymentError)
            } else {
              console.log(`[STRIPE-WEBHOOK] ${timestamp} - ✅ Payment recorded successfully`)
            }
          } else {
            console.warn(`[STRIPE-WEBHOOK] ${timestamp} - ⚠️ User not found for payment history`)
          }
          
          console.log(`[STRIPE-WEBHOOK] ${timestamp} - ✅ Payment succeeded for subscription ${subscriptionId}`)
          console.log(`[STRIPE-WEBHOOK] ${timestamp} - ===== END invoice.payment_succeeded =====`)
        }
        break
      }

      // Falha no pagamento
      case 'invoice.payment_failed': {
        const invoice = event.data.object as Stripe.Invoice
        const timestamp = getTimestamp()
        
        console.log(`[STRIPE-WEBHOOK] ${timestamp} - ===== PROCESSING invoice.payment_failed =====`)
        console.log(`[STRIPE-WEBHOOK] ${timestamp} - Invoice ID:`, invoice.id)
        console.log(`[STRIPE-WEBHOOK] ${timestamp} - Invoice subscription:`, invoice.subscription)
        
        if (invoice.subscription) {
          const subscriptionId = invoice.subscription as string
          
          const updateData = {
            subscription_status: 'past_due',
            updated_at: new Date().toISOString(),
          }
          
          console.log(`[STRIPE-WEBHOOK] ${timestamp} - Updating status to past_due for subscription:`, subscriptionId)
          
          const { data: profilesUpdate, error: profilesError } = await supabase
            .from('profiles')
            .update(updateData)
            .eq('stripe_subscription_id', subscriptionId)
            .select()
          
          const { data: stackProfilesUpdate, error: stackProfilesError } = await supabase
            .from('stack_profiles')
            .update(updateData)
            .eq('stripe_subscription_id', subscriptionId)
            .select()
          
          if (profilesError && stackProfilesError) {
            console.error(`[STRIPE-WEBHOOK] ${timestamp} - ❌ Error updating subscription status in both tables`)
            throw new Error('Failed to update subscription status')
          }
          
          console.log(`[STRIPE-WEBHOOK] ${timestamp} - ⚠️ Payment failed for subscription ${subscriptionId}`)
          console.log(`[STRIPE-WEBHOOK] ${timestamp} - Updated ${(profilesUpdate?.length || 0) + (stackProfilesUpdate?.length || 0)} profile(s) total`)
        }
        break
      }

      // Assinatura cancelada
      case 'customer.subscription.deleted': {
        const subscription = event.data.object as Stripe.Subscription
        const timestamp = getTimestamp()
        
        console.log(`[STRIPE-WEBHOOK] ${timestamp} - ===== PROCESSING customer.subscription.deleted =====`)
        console.log(`[STRIPE-WEBHOOK] ${timestamp} - Subscription ID:`, subscription.id)
        console.log(`[STRIPE-WEBHOOK] ${timestamp} - Subscription status:`, subscription.status)
        
        const updateData = {
          subscription_status: 'canceled',
          subscription_tier: 'free',
          updated_at: new Date().toISOString(),
        }
        
        console.log(`[STRIPE-WEBHOOK] ${timestamp} - Removing premium access for subscription:`, subscription.id)
        
        const { data: profilesUpdate, error: profilesError } = await supabase
          .from('profiles')
          .update(updateData)
          .eq('stripe_subscription_id', subscription.id)
          .select()
        
        const { data: stackProfilesUpdate, error: stackProfilesError } = await supabase
          .from('stack_profiles')
          .update(updateData)
          .eq('stripe_subscription_id', subscription.id)
          .select()
        
        if (profilesError && stackProfilesError) {
          console.error(`[STRIPE-WEBHOOK] ${timestamp} - ❌ Error canceling subscription in both tables`)
          throw new Error('Failed to cancel subscription')
        }
        
        console.log(`[STRIPE-WEBHOOK] ${timestamp} - ❌ Subscription canceled: ${subscription.id}`)
        console.log(`[STRIPE-WEBHOOK] ${timestamp} - Updated ${(profilesUpdate?.length || 0) + (stackProfilesUpdate?.length || 0)} profile(s) total`)
        break
      }

      // Atualização de assinatura
      case 'customer.subscription.updated': {
        const subscription = event.data.object as Stripe.Subscription
        const timestamp = getTimestamp()
        
        console.log(`[STRIPE-WEBHOOK] ${timestamp} - ===== PROCESSING customer.subscription.updated =====`)
        console.log(`[STRIPE-WEBHOOK] ${timestamp} - Subscription ID:`, subscription.id)
        console.log(`[STRIPE-WEBHOOK] ${timestamp} - Subscription status:`, subscription.status)
        console.log(`[STRIPE-WEBHOOK] ${timestamp} - Current period end:`, new Date(subscription.current_period_end * 1000).toISOString())
        
        const updateData = {
          subscription_status: subscription.status,
          subscription_current_period_end: new Date(subscription.current_period_end * 1000).toISOString(),
          subscription_tier: subscription.status === 'active' ? 'premium' : 'free',
          updated_at: new Date().toISOString(),
        }
        
        console.log(`[STRIPE-WEBHOOK] ${timestamp} - Updating subscription data:`, JSON.stringify(updateData, null, 2))
        
        const { data: profilesUpdate, error: profilesError } = await supabase
          .from('profiles')
          .update(updateData)
          .eq('stripe_subscription_id', subscription.id)
          .select()
        
        const { data: stackProfilesUpdate, error: stackProfilesError } = await supabase
          .from('stack_profiles')
          .update(updateData)
          .eq('stripe_subscription_id', subscription.id)
          .select()
        
        if (profilesError && stackProfilesError) {
          console.error(`[STRIPE-WEBHOOK] ${timestamp} - ❌ Error updating subscription in both tables`)
          throw new Error('Failed to update subscription')
        }
        
        console.log(`[STRIPE-WEBHOOK] ${timestamp} - 🔄 Subscription updated: ${subscription.id}`)
        console.log(`[STRIPE-WEBHOOK] ${timestamp} - Updated ${(profilesUpdate?.length || 0) + (stackProfilesUpdate?.length || 0)} profile(s) total`)
        break
      }
    }

    return NextResponse.json({ received: true })
  } catch (error) {
    const timestamp = getTimestamp()
    console.error(`[STRIPE-WEBHOOK] ${timestamp} - ❌ Webhook error:`, error)
    
    // Log webhook error to database
    await logWebhookEvent(supabase, event, 'error', error instanceof Error ? error.message : String(error))
    
    return NextResponse.json(
      { error: 'Webhook handler failed' },
      { status: 500 }
    )
  }
}