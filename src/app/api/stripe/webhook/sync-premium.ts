import { createServiceClient } from '@/lib/supabase/server'
import <PERSON><PERSON> from 'stripe'

/**
 * Sincroniza status premium do Stripe com perfil no Supabase
 */
export async function syncStripePremiumToProfile(
  customerId: string,
  subscription: Stripe.Subscription
) {
  const timestamp = new Date().toISOString()
  console.log(`[SYNC-PREMIUM] ${timestamp} - ===== SYNCING STRIPE PREMIUM =====`)
  console.log(`[SYNC-PREMIUM] ${timestamp} - Customer ID:`, customerId)
  console.log(`[SYNC-PREMIUM] ${timestamp} - Subscription ID:`, subscription.id)
  console.log(`[SYNC-PREMIUM] ${timestamp} - Status:`, subscription.status)
  
  try {
    const supabase = createServiceClient()
    
    // Buscar perfil pelo stripe_customer_id
    const { data: profiles, error: searchError } = await supabase
      .from('profiles')
      .select('*')
      .eq('stripe_customer_id', customerId)
    
    if (searchError) {
      console.error(`[SYNC-PREMIUM] ${timestamp} - ❌ Erro ao buscar perfis:`, searchError)
      throw searchError
    }
    
    if (!profiles || profiles.length === 0) {
      console.log(`[SYNC-PREMIUM] ${timestamp} - ⚠️ Nenhum perfil encontrado para customer:`, customerId)
      
      // Tentar buscar pelo email se disponível
      if (subscription.customer && typeof subscription.customer === 'object' && 'email' in subscription.customer && subscription.customer.email) {
        const { data: profileByEmail, error: emailError } = await supabase
          .from('profiles')
          .select('*')
          .eq('email', subscription.customer.email)
          .single()
        
        if (!emailError && profileByEmail) {
          console.log(`[SYNC-PREMIUM] ${timestamp} - ✅ Perfil encontrado por email`)
          profiles.push(profileByEmail)
        }
      }
    }
    
    // Atualizar todos os perfis encontrados
    for (const profile of profiles || []) {
      console.log(`[SYNC-PREMIUM] ${timestamp} - Atualizando perfil:`, profile.id)
      
      const updateData = {
        stripe_customer_id: customerId,
        stripe_subscription_id: subscription.id,
        subscription_status: subscription.status,
        subscription_current_period_end: new Date(subscription.current_period_end * 1000).toISOString(),
        subscription_start_date: new Date(subscription.start_date * 1000).toISOString(),
        updated_at: new Date().toISOString()
      }
      
      // Definir tier baseado no produto/preço
      if (subscription.items.data.length > 0) {
        const item = subscription.items.data[0]
        // Aqui você pode mapear price_id para tier
        // Por enquanto, vamos assumir que qualquer assinatura ativa é premium
        if (subscription.status === 'active' || subscription.status === 'trialing') {
          updateData['subscription_tier'] = 'premium'
        }
      }
      
      const { error: updateError } = await supabase
        .from('profiles')
        .update(updateData)
        .eq('id', profile.id)
      
      if (updateError) {
        console.error(`[SYNC-PREMIUM] ${timestamp} - ❌ Erro ao atualizar perfil ${profile.id}:`, updateError)
      } else {
        console.log(`[SYNC-PREMIUM] ${timestamp} - ✅ Perfil ${profile.id} atualizado com premium`)
      }
    }
    
    return {
      success: true,
      profilesUpdated: profiles?.length || 0
    }
    
  } catch (error) {
    console.error(`[SYNC-PREMIUM] ${timestamp} - ❌ Erro geral:`, error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

/**
 * Remove premium quando assinatura é cancelada
 */
export async function removePremiumFromProfile(customerId: string) {
  const timestamp = new Date().toISOString()
  console.log(`[REMOVE-PREMIUM] ${timestamp} - Removendo premium do customer:`, customerId)
  
  try {
    const supabase = createServiceClient()
    
    const { error } = await supabase
      .from('profiles')
      .update({
        subscription_tier: 'free',
        subscription_status: 'canceled',
        updated_at: new Date().toISOString()
      })
      .eq('stripe_customer_id', customerId)
    
    if (error) {
      console.error(`[REMOVE-PREMIUM] ${timestamp} - ❌ Erro:`, error)
      return { success: false, error }
    }
    
    console.log(`[REMOVE-PREMIUM] ${timestamp} - ✅ Premium removido`)
    return { success: true }
    
  } catch (error) {
    console.error(`[REMOVE-PREMIUM] ${timestamp} - ❌ Erro geral:`, error)
    return { success: false, error }
  }
}