import { NextRequest, NextResponse } from 'next/server'
import { headers } from 'next/headers'
import <PERSON><PERSON> from 'stripe'
import { stripe } from '@/lib/stripe'
import { createClient } from '@/lib/supabase/server'

export async function POST(request: NextRequest) {
  const body = await request.text()
  const signature = (await headers()).get('stripe-signature')!

  let event: Stripe.Event

  try {
    event = stripe.webhooks.constructEvent(
      body,
      signature,
      process.env.STRIPE_WEBHOOK_SECRET!
    )
  } catch (error) {
    console.error('Error en webhook:', error)
    return NextResponse.json(
      { error: 'Firma de webhook inválida' },
      { status: 400 }
    )
  }

  const supabase = await createClient()

  try {
    switch (event.type) {
      // Assinatura criada com sucesso
      case 'customer.subscription.created':
      case 'customer.subscription.updated': {
        const subscription = event.data.object as Stripe.Subscription
        const customerId = subscription.customer as string
        
        // Buscar usuário pelo customer ID
        const { data: profile } = await supabase
          .from('profiles')
          .select('id')
          .eq('stripe_customer_id', customerId)
          .single()

        if (profile) {
          // Atualizar informações da assinatura
          await supabase
            .from('profiles')
            .update({
              stripe_subscription_id: subscription.id,
              subscription_status: subscription.status,
              subscription_current_period_end: new Date(subscription.current_period_end * 1000).toISOString(),
              subscription_cancel_at_period_end: subscription.cancel_at_period_end,
              trial_end: subscription.trial_end ? new Date(subscription.trial_end * 1000).toISOString() : null,
            })
            .eq('id', profile.id)
        }
        break
      }

      // Assinatura cancelada
      case 'customer.subscription.deleted': {
        const subscription = event.data.object as Stripe.Subscription
        const customerId = subscription.customer as string
        
        // Buscar usuário pelo customer ID
        const { data: profile } = await supabase
          .from('profiles')
          .select('id')
          .eq('stripe_customer_id', customerId)
          .single()

        if (profile) {
          // Atualizar status para cancelado
          await supabase
            .from('profiles')
            .update({
              subscription_status: 'canceled',
              subscription_current_period_end: null,
            })
            .eq('id', profile.id)
        }
        break
      }

      // Pagamento bem-sucedido
      case 'invoice.payment_succeeded': {
        const invoice = event.data.object as Stripe.Invoice
        const customerId = invoice.customer as string
        
        // Buscar usuário pelo customer ID
        const { data: profile } = await supabase
          .from('profiles')
          .select('id')
          .eq('stripe_customer_id', customerId)
          .single()

        if (profile) {
          // Registrar pagamento
          await supabase
            .from('payment_history')
            .insert({
              user_id: profile.id,
              stripe_invoice_id: invoice.id,
              amount: invoice.amount_paid / 100, // Converter de centavos
              currency: invoice.currency,
              status: 'paid',
              paid_at: new Date(invoice.status_transitions.paid_at! * 1000).toISOString(),
            })
        }
        break
      }

      // Falha no pagamento
      case 'invoice.payment_failed': {
        const invoice = event.data.object as Stripe.Invoice
        const customerId = invoice.customer as string
        
        // Buscar usuário pelo customer ID
        const { data: profile } = await supabase
          .from('profiles')
          .select('id, email')
          .eq('stripe_customer_id', customerId)
          .single()

        if (profile) {
          // Registrar falha de pagamento
          await supabase
            .from('payment_history')
            .insert({
              user_id: profile.id,
              stripe_invoice_id: invoice.id,
              amount: invoice.amount_due / 100,
              currency: invoice.currency,
              status: 'failed',
              failed_at: new Date().toISOString(),
            })

          // TODO: Enviar email de notificación
          console.log(`Pago fallido para el usuario ${profile.email}`)
        }
        break
      }

      // Trial terminando
      case 'customer.subscription.trial_will_end': {
        const subscription = event.data.object as Stripe.Subscription
        const customerId = subscription.customer as string
        
        // Buscar usuário pelo customer ID
        const { data: profile } = await supabase
          .from('profiles')
          .select('id, email')
          .eq('stripe_customer_id', customerId)
          .single()

        if (profile) {
          // TODO: Enviar email avisando que el trial está terminando
          console.log(`Trial terminando para el usuario ${profile.email}`)
        }
        break
      }
    }

    return NextResponse.json({ received: true })

  } catch (error) {
    console.error('Error al procesar webhook:', error)
    return NextResponse.json(
      { error: 'Error al procesar webhook' },
      { status: 500 }
    )
  }
}