import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { stripe } from '@/lib/stripe'

export async function POST(request: NextRequest) {
  try {
    const { email } = await request.json()
    
    if (!email) {
      return NextResponse.json(
        { error: 'Email é obrigatório' },
        { status: 400 }
      )
    }
    
    const supabase = await createClient()
    const timestamp = new Date().toISOString()
    
    console.log(`[FIX-ACCESS] ${timestamp} - ===== DIAGNÓSTICO E CORREÇÃO =====`)
    console.log(`[FIX-ACCESS] ${timestamp} - Email:`, email)
    
    // 1. Buscar cliente no Stripe
    console.log(`[FIX-ACCESS] ${timestamp} - Buscando cliente no Stripe...`)
    const customers = await stripe.customers.list({
      email: email,
      limit: 10
    })
    
    if (customers.data.length === 0) {
      return NextResponse.json({
        error: 'Cliente não encontrado no Stripe',
        suggestion: 'Verifique se o email está correto'
      }, { status: 404 })
    }
    
    const customer = customers.data[0]
    console.log(`[FIX-ACCESS] ${timestamp} - Cliente Stripe encontrado:`, customer.id)
    
    // 2. Buscar assinaturas ativas
    console.log(`[FIX-ACCESS] ${timestamp} - Buscando assinaturas...`)
    const subscriptions = await stripe.subscriptions.list({
      customer: customer.id,
      status: 'active'
    })
    
    if (subscriptions.data.length === 0) {
      console.log(`[FIX-ACCESS] ${timestamp} - Nenhuma assinatura ativa`)
      return NextResponse.json({
        error: 'Nenhuma assinatura ativa encontrada',
        customer: {
          id: customer.id,
          email: customer.email
        }
      }, { status: 404 })
    }
    
    const subscription = subscriptions.data[0]
    console.log(`[FIX-ACCESS] ${timestamp} - Assinatura ativa:`, subscription.id)
    console.log(`[FIX-ACCESS] ${timestamp} - Status:`, subscription.status)
    console.log(`[FIX-ACCESS] ${timestamp} - Período termina em:`, new Date(subscription.current_period_end * 1000).toISOString())
    
    // 3. Buscar usuário no banco
    console.log(`[FIX-ACCESS] ${timestamp} - Buscando usuário no banco...`)
    
    // Tentar auth.users primeiro
    const { data: authUser } = await supabase.auth.admin.getUserByEmail(email)
    
    let userId = null
    let userType = null
    let currentProfile = null
    
    if (authUser) {
      userId = authUser.user.id
      userType = 'supabase'
      console.log(`[FIX-ACCESS] ${timestamp} - Usuário encontrado no auth.users:`, userId)
      
      // Buscar perfil
      const { data: profile } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single()
      
      currentProfile = profile
    } else {
      // Buscar no stack_profiles
      console.log(`[FIX-ACCESS] ${timestamp} - Buscando no stack_profiles...`)
      const { data: stackProfiles } = await supabase
        .from('stack_profiles')
        .select('*')
        .ilike('email', email)
      
      if (stackProfiles && stackProfiles.length > 0) {
        userId = stackProfiles[0].id
        userType = 'stack'
        currentProfile = stackProfiles[0]
        console.log(`[FIX-ACCESS] ${timestamp} - Usuário encontrado no stack_profiles:`, userId)
      }
    }
    
    if (!userId) {
      return NextResponse.json({
        error: 'Usuário não encontrado no banco de dados',
        suggestion: 'O usuário precisa fazer login pelo menos uma vez',
        stripe: {
          customerId: customer.id,
          subscriptionId: subscription.id,
          status: subscription.status
        }
      }, { status: 404 })
    }
    
    // 4. Mostrar estado atual
    console.log(`[FIX-ACCESS] ${timestamp} - Estado atual do perfil:`)
    console.log(`[FIX-ACCESS] ${timestamp} -   subscription_tier:`, currentProfile?.subscription_tier)
    console.log(`[FIX-ACCESS] ${timestamp} -   subscription_status:`, currentProfile?.subscription_status)
    console.log(`[FIX-ACCESS] ${timestamp} -   stripe_customer_id:`, currentProfile?.stripe_customer_id)
    console.log(`[FIX-ACCESS] ${timestamp} -   stripe_subscription_id:`, currentProfile?.stripe_subscription_id)
    
    // 5. Corrigir o perfil
    const updateData = {
      stripe_customer_id: customer.id,
      stripe_subscription_id: subscription.id,
      subscription_status: subscription.status,
      subscription_current_period_end: new Date(subscription.current_period_end * 1000).toISOString(),
      subscription_expires_at: new Date(subscription.current_period_end * 1000).toISOString(),
      subscription_tier: 'premium',
      updated_at: new Date().toISOString()
    }
    
    console.log(`[FIX-ACCESS] ${timestamp} - Aplicando correção...`)
    
    if (userType === 'supabase') {
      // Atualizar profiles
      const { error: profileError } = await supabase
        .from('profiles')
        .update(updateData)
        .eq('id', userId)
      
      if (profileError) {
        console.error(`[FIX-ACCESS] ${timestamp} - Erro ao atualizar profiles:`, profileError)
        return NextResponse.json({
          error: 'Erro ao atualizar perfil',
          details: profileError
        }, { status: 500 })
      }
      
      // Tentar atualizar stack_profiles também (pode não existir)
      await supabase
        .from('stack_profiles')
        .update(updateData)
        .eq('id', userId)
    } else {
      // Atualizar stack_profiles
      const { error } = await supabase
        .from('stack_profiles')
        .update(updateData)
        .eq('id', userId)
      
      if (error) {
        console.error(`[FIX-ACCESS] ${timestamp} - Erro ao atualizar stack_profiles:`, error)
        return NextResponse.json({
          error: 'Erro ao atualizar perfil',
          details: error
        }, { status: 500 })
      }
    }
    
    console.log(`[FIX-ACCESS] ${timestamp} - ✅ Correção aplicada com sucesso`)
    
    // 6. Verificar correção
    const tableName = userType === 'supabase' ? 'profiles' : 'stack_profiles'
    const { data: updatedProfile } = await supabase
      .from(tableName)
      .select('*')
      .eq('id', userId)
      .single()
    
    return NextResponse.json({
      success: true,
      message: 'Acesso premium restaurado com sucesso',
      user: {
        id: userId,
        email: email,
        type: userType
      },
      stripe: {
        customerId: customer.id,
        subscriptionId: subscription.id,
        status: subscription.status,
        currentPeriodEnd: new Date(subscription.current_period_end * 1000).toISOString()
      },
      before: {
        subscription_tier: currentProfile?.subscription_tier,
        subscription_status: currentProfile?.subscription_status
      },
      after: {
        subscription_tier: updatedProfile?.subscription_tier,
        subscription_status: updatedProfile?.subscription_status,
        subscription_expires_at: updatedProfile?.subscription_expires_at
      }
    })
    
  } catch (error) {
    console.error('[FIX-ACCESS] Erro:', error)
    return NextResponse.json(
      { 
        error: 'Erro interno do servidor',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'API de correção de acesso premium',
    usage: 'POST /api/stripe/fix-subscription-access',
    body: {
      email: '<EMAIL>'
    },
    description: 'Esta API diagnostica e corrige problemas de usuários que fizeram pagamento mas não têm acesso premium'
  })
}