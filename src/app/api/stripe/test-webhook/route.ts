import { NextRequest, NextResponse } from 'next/server'
import { headers } from 'next/headers'
import { createClient } from '@/lib/supabase/server'

export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient()
    const headersList = await headers()
    
    // Verificar variáveis de ambiente
    const hasWebhookSecret = !!process.env.STRIPE_WEBHOOK_SECRET
    const hasStripeKey = !!process.env.STRIPE_SECRET_KEY
    const hasPublishableKey = !!process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY
    
    // Verificar tabelas no banco
    const { data: tables } = await supabase.rpc('get_tables', {
      schema_name: 'public'
    }).select('*')
    
    const requiredTables = ['stripe_subscriptions', 'stripe_webhook_events', 'payment_history']
    const existingTables = tables?.map((t: any) => t.table_name) || []
    const missingTables = requiredTables.filter(t => !existingTables.includes(t))
    
    // Verificar últimos webhooks
    const { data: recentWebhooks } = await supabase
      .from('stripe_webhook_events')
      .select('event_type, status, created_at')
      .order('created_at', { ascending: false })
      .limit(5)
    
    return NextResponse.json({
      status: 'ok',
      timestamp: new Date().toISOString(),
      environment: {
        webhook_secret_configured: hasWebhookSecret,
        stripe_key_configured: hasStripeKey,
        publishable_key_configured: hasPublishableKey,
        node_env: process.env.NODE_ENV
      },
      database: {
        required_tables: requiredTables,
        missing_tables: missingTables,
        all_tables_exist: missingTables.length === 0
      },
      recent_webhooks: recentWebhooks || [],
      webhook_endpoint: `${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/api/stripe/webhook`,
      test_commands: {
        stripe_cli: 'stripe listen --forward-to localhost:3000/api/stripe/webhook',
        trigger_test: 'stripe trigger checkout.session.completed'
      }
    })
  } catch (error) {
    console.error('[STRIPE-TEST-WEBHOOK] Error:', error)
    
    // Se o erro for sobre a função get_tables não existir, tentar alternativa
    try {
      const supabase = await createClient()
      
      // Verificar tabelas manualmente
      const checkTable = async (tableName: string) => {
        const { error } = await supabase.from(tableName).select('id').limit(1)
        return !error || error.code !== 'PGRST116' // PGRST116 = table not found
      }
      
      const tableChecks = await Promise.all([
        checkTable('stripe_subscriptions'),
        checkTable('stripe_webhook_events'),
        checkTable('payment_history')
      ])
      
      return NextResponse.json({
        status: 'ok',
        timestamp: new Date().toISOString(),
        environment: {
          webhook_secret_configured: !!process.env.STRIPE_WEBHOOK_SECRET,
          stripe_key_configured: !!process.env.STRIPE_SECRET_KEY,
          publishable_key_configured: !!process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY,
          node_env: process.env.NODE_ENV
        },
        database: {
          stripe_subscriptions_exists: tableChecks[0],
          stripe_webhook_events_exists: tableChecks[1],
          payment_history_exists: tableChecks[2]
        },
        webhook_endpoint: `${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/api/stripe/webhook`,
        error_details: 'Could not fetch full table list, showing basic checks only'
      })
    } catch (fallbackError) {
      return NextResponse.json({
        error: 'Failed to check webhook configuration',
        details: fallbackError instanceof Error ? fallbackError.message : String(fallbackError)
      }, { status: 500 })
    }
  }
}

export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient()
    const body = await request.json()
    
    // Simular um evento de teste
    const testEvent = {
      event_id: `test_${Date.now()}`,
      event_type: 'test.webhook',
      status: 'success' as const,
      payload: body || { test: true, timestamp: new Date().toISOString() }
    }
    
    const { data, error } = await supabase
      .from('stripe_webhook_events')
      .insert(testEvent)
      .select()
      .single()
    
    if (error) {
      throw error
    }
    
    return NextResponse.json({
      success: true,
      message: 'Test webhook event created',
      event: data
    })
  } catch (error) {
    console.error('[STRIPE-TEST-WEBHOOK] POST Error:', error)
    return NextResponse.json({
      error: 'Failed to create test webhook event',
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 })
  }
}