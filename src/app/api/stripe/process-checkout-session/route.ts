import { NextRequest, NextResponse } from 'next/server'
import { stripe } from '@/lib/stripe'
import { createClient } from '@/lib/supabase/server'

export async function POST(request: NextRequest) {
  try {
    const { sessionId } = await request.json()
    
    if (!sessionId) {
      return NextResponse.json(
        { error: 'Session ID é obrigatório' },
        { status: 400 }
      )
    }
    
    console.log(`[PROCESS-CHECKOUT] Processing session: ${sessionId}`)
    
    // Buscar a sessão no Stripe
    const session = await stripe.checkout.sessions.retrieve(sessionId, {
      expand: ['subscription', 'customer']
    })
    
    if (!session) {
      return NextResponse.json(
        { error: 'Sessão não encontrada' },
        { status: 404 }
      )
    }
    
    console.log(`[PROCESS-CHECKOUT] Session status: ${session.status}`)
    console.log(`[PROCESS-CHECKOUT] Payment status: ${session.payment_status}`)
    
    if (session.payment_status !== 'paid') {
      return NextResponse.json(
        { error: 'Pagamento não confirmado' },
        { status: 400 }
      )
    }
    
    const customer = session.customer as any
    const subscription = session.subscription as any
    
    if (!customer?.email) {
      return NextResponse.json(
        { error: 'Email do cliente não encontrado' },
        { status: 400 }
      )
    }
    
    console.log(`[PROCESS-CHECKOUT] Customer email: ${customer.email}`)
    console.log(`[PROCESS-CHECKOUT] Subscription ID: ${subscription?.id}`)
    
    const supabase = await createClient()
    
    // Buscar usuário por email
    const { data: authUser } = await supabase.auth.admin.getUserByEmail(customer.email)
    
    let userId = null
    let tableName = null
    
    if (authUser) {
      userId = authUser.user.id
      tableName = 'profiles'
    } else {
      // Buscar no stack_profiles
      const { data: stackProfiles } = await supabase
        .from('stack_profiles')
        .select('*')
        .ilike('email', customer.email)
      
      if (stackProfiles && stackProfiles.length > 0) {
        userId = stackProfiles[0].id
        tableName = 'stack_profiles'
      }
    }
    
    if (!userId) {
      return NextResponse.json(
        { error: 'Usuário não encontrado' },
        { status: 404 }
      )
    }
    
    console.log(`[PROCESS-CHECKOUT] User found: ${userId} in ${tableName}`)
    
    // Preparar dados de atualização
    const expirationDate = subscription?.current_period_end 
      ? new Date(subscription.current_period_end * 1000)
      : new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 dias
    
    const updateData = {
      stripe_customer_id: session.customer,
      stripe_subscription_id: subscription?.id || session.id,
      subscription_status: 'active',
      subscription_current_period_end: expirationDate.toISOString(),
      subscription_expires_at: expirationDate.toISOString(), // IMPORTANTE!
      subscription_tier: 'premium',
      updated_at: new Date().toISOString()
    }
    
    console.log(`[PROCESS-CHECKOUT] Updating profile with:`, updateData)
    
    // Atualizar o perfil apropriado
    if (tableName === 'profiles') {
      const { error } = await supabase
        .from('profiles')
        .update(updateData)
        .eq('id', userId)
      
      if (error) {
        console.error(`[PROCESS-CHECKOUT] Error updating profiles:`, error)
        throw error
      }
      
      // Tentar atualizar stack_profiles também
      await supabase
        .from('stack_profiles')
        .update(updateData)
        .eq('id', userId)
    } else {
      const { error } = await supabase
        .from('stack_profiles')
        .update(updateData)
        .eq('id', userId)
      
      if (error) {
        console.error(`[PROCESS-CHECKOUT] Error updating stack_profiles:`, error)
        throw error
      }
    }
    
    console.log(`[PROCESS-CHECKOUT] ✅ Profile updated successfully`)
    
    // Verificar a atualização
    const { data: verifiedProfile } = await supabase
      .from(tableName)
      .select('*')
      .eq('id', userId)
      .single()
    
    return NextResponse.json({
      success: true,
      message: 'Assinatura ativada com sucesso',
      user: {
        id: userId,
        email: customer.email,
        tableName
      },
      subscription: {
        tier: verifiedProfile?.subscription_tier,
        status: verifiedProfile?.subscription_status,
        expiresAt: verifiedProfile?.subscription_expires_at
      }
    })
    
  } catch (error) {
    console.error('[PROCESS-CHECKOUT] Error:', error)
    return NextResponse.json(
      { 
        error: 'Erro ao processar pagamento',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Use POST com { sessionId }'
  })
}