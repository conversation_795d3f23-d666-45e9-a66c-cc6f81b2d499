import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

export async function POST(request: NextRequest) {
  try {
    const { type } = await request.json()
    
    if (!type || !['apk', 'qrcode'].includes(type)) {
      return NextResponse.json(
        { error: 'Invalid download type' },
        { status: 400 }
      )
    }

    const supabase = await createClient()
    
    const ip = request.headers.get('x-forwarded-for') || 'unknown'
    const userAgent = request.headers.get('user-agent') || 'unknown'

    const { error } = await supabase
      .from('download_stats')
      .insert({
        download_type: type,
        ip_address: ip,
        user_agent: userAgent
      })

    if (error) throw error

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error tracking download:', error)
    return NextResponse.json(
      { error: 'Failed to track download' },
      { status: 500 }
    )
  }
}