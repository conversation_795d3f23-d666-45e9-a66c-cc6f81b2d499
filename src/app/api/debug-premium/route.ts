import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

function getRecommendations(profile: any, accessCheck: any, isExpired: boolean, accessError: any) {
  const recommendations = []
  
  if (!profile) {
    recommendations.push('❌ Perfil não existe - criar perfil')
  }
  
  if (profile && profile.subscription_tier !== 'premium') {
    recommendations.push('❌ Tier não é premium - atualizar para premium')
  }
  
  if (profile && profile.subscription_status !== 'active') {
    recommendations.push('❌ Status não é active - atualizar para active')
  }
  
  if (isExpired) {
    recommendations.push('❌ Assinatura expirada - atualizar data de expiração')
  }
  
  if (accessError) {
    recommendations.push(`❌ Erro na função check_user_access_unified: ${accessError.message}`)
  }
  
  if (!accessCheck?.has_access) {
    recommendations.push('❌ check_user_access_unified retornou false - verificar função')
  }
  
  if (recommendations.length === 0) {
    recommendations.push('✅ Tudo parece estar correto!')
  }
  
  return recommendations
}

export async function GET(request: NextRequest) {
  try {
    const email = request.nextUrl.searchParams.get('email') || '<EMAIL>'
    const stackId = request.nextUrl.searchParams.get('stackId') || '590b3ae5-92f2-47d2-91a6-8c2962d26910'
    
    // Criar cliente admin
    const supabaseAdmin = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )
    
    console.log('🔍 [DEBUG-PREMIUM] Iniciando debug para:', email)
    console.log('🔍 [DEBUG-PREMIUM] Stack ID:', stackId)
    
    // 1. Primeiro verificar Stack Auth profile
    const { data: stackProfile, error: stackError } = await supabaseAdmin
      .from('stack_profiles')
      .select('*')
      .or(`email.eq.${email},id.eq.${stackId}`)
      .single()
    
    if (stackProfile) {
      console.log('✅ [DEBUG-PREMIUM] Stack Profile encontrado:', stackProfile)
      
      // Testar função check_user_access_unified com Stack ID
      const { data: accessCheck, error: accessError } = await supabaseAdmin
        .rpc('check_user_access_unified', { p_user_id: stackProfile.id })
        .single()
      
      console.log('🔐 [DEBUG-PREMIUM] check_user_access_unified result:', accessCheck)
      console.log('🔐 [DEBUG-PREMIUM] check_user_access_unified error:', accessError)
      
      const now = new Date()
      const profileEndDate = stackProfile?.subscription_current_period_end ? new Date(stackProfile.subscription_current_period_end) : null
      const isExpired = profileEndDate ? profileEndDate < now : true
      
      return NextResponse.json({
        timestamp: new Date().toISOString(),
        email,
        stack_id: stackProfile.id,
        auth_type: 'stack_auth',
        stack_profile: stackProfile,
        profile_analysis: {
          has_profile: true,
          subscription_tier: stackProfile?.subscription_tier,
          subscription_status: stackProfile?.subscription_status,
          subscription_end_date: stackProfile?.subscription_current_period_end,
          subscription_end_date_formatted: profileEndDate?.toLocaleString('pt-BR'),
          is_expired: isExpired,
          days_remaining: profileEndDate ? Math.floor((profileEndDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24)) : null,
        },
        access_check: accessCheck || null,
        access_analysis: {
          has_access: accessCheck?.has_access || false,
          access_type: accessCheck?.access_type || null,
          expires_at: accessCheck?.expires_at,
          check_function_working: !accessError,
          error_message: accessError?.message || null
        },
        recommendations: getRecommendations(stackProfile, accessCheck, isExpired, accessError)
      })
    }
    
    // 2. Se não encontrou no Stack, buscar usuário no Supabase auth
    const { data: { users }, error: authError } = await supabaseAdmin.auth.admin.listUsers()
    
    if (authError) {
      console.error('❌ [DEBUG-PREMIUM] Erro ao buscar usuários:', authError)
      return NextResponse.json({ error: 'Erro ao buscar usuários', details: authError })
    }
    
    const authUser = users.find(u => u.email === email)
    
    if (!authUser) {
      console.log('❌ [DEBUG-PREMIUM] Usuário não encontrado em nenhum sistema')
      return NextResponse.json({ 
        error: 'Usuário não encontrado',
        details: 'Usuário não encontrado nem no Stack Auth nem no Supabase Auth',
        searched_email: email,
        searched_stack_id: stackId
      })
    }
    
    console.log('✅ [DEBUG-PREMIUM] Usuário encontrado:', authUser.id)
    
    // 2. Buscar perfil
    const { data: profile, error: profileError } = await supabaseAdmin
      .from('profiles')
      .select('*')
      .eq('id', authUser.id)
      .single()
    
    console.log('📊 [DEBUG-PREMIUM] Perfil:', profile)
    console.log('📊 [DEBUG-PREMIUM] Erro ao buscar perfil:', profileError)
    
    // 3. Testar função check_user_access
    const { data: accessCheck, error: accessError } = await supabaseAdmin
      .rpc('check_user_access', { p_user_id: authUser.id })
      .single()
    
    console.log('🔐 [DEBUG-PREMIUM] check_user_access result:', accessCheck)
    console.log('🔐 [DEBUG-PREMIUM] check_user_access error:', accessError)
    
    // 4. Verificar datas
    const now = new Date()
    const profileEndDate = profile?.subscription_current_period_end ? new Date(profile.subscription_current_period_end) : null
    const isExpired = profileEndDate ? profileEndDate < now : true
    
    // 5. Buscar todos os pagamentos do Stripe
    let stripeInfo = null
    if (profile?.stripe_customer_id) {
      console.log('💳 [DEBUG-PREMIUM] Buscando info do Stripe para customer:', profile.stripe_customer_id)
      
      // Buscar assinaturas no histórico de pagamentos
      const { data: payments } = await supabaseAdmin
        .from('payment_history')
        .select('*')
        .eq('customer_id', profile.stripe_customer_id)
        .order('created_at', { ascending: false })
        .limit(5)
      
      stripeInfo = {
        customer_id: profile.stripe_customer_id,
        subscription_id: profile.stripe_subscription_id,
        recent_payments: payments
      }
    }
    
    // Resultado completo
    const result = {
      timestamp: new Date().toISOString(),
      email,
      user_id: authUser.id,
      auth_user: {
        id: authUser.id,
        email: authUser.email,
        created_at: authUser.created_at,
        last_sign_in_at: authUser.last_sign_in_at
      },
      profile: profile || null,
      profile_analysis: {
        has_profile: !!profile,
        subscription_tier: profile?.subscription_tier,
        subscription_status: profile?.subscription_status,
        subscription_end_date: profile?.subscription_current_period_end,
        subscription_end_date_formatted: profileEndDate?.toLocaleString('pt-BR'),
        is_expired: isExpired,
        days_remaining: profileEndDate ? Math.floor((profileEndDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24)) : null,
        has_stripe_customer: !!profile?.stripe_customer_id,
        has_stripe_subscription: !!profile?.stripe_subscription_id
      },
      access_check: accessCheck || null,
      access_analysis: {
        has_access: accessCheck?.has_access || false,
        access_type: accessCheck?.access_type || null,
        expires_at: accessCheck?.expires_at,
        check_function_working: !accessError
      },
      stripe_info: stripeInfo,
      debug_info: {
        now: now.toISOString(),
        now_formatted: now.toLocaleString('pt-BR'),
        profile_created_at: profile?.created_at,
        profile_updated_at: profile?.updated_at,
        environment: process.env.NODE_ENV
      },
      recommendations: []
    }
    
    // Adicionar recomendações baseadas na análise
    if (!profile) {
      result.recommendations.push('❌ Perfil não existe - criar perfil')
    }
    
    if (profile && profile.subscription_tier !== 'premium') {
      result.recommendations.push('❌ Tier não é premium - atualizar para premium')
    }
    
    if (profile && profile.subscription_status !== 'active') {
      result.recommendations.push('❌ Status não é active - atualizar para active')
    }
    
    if (isExpired) {
      result.recommendations.push('❌ Assinatura expirada - atualizar data de expiração')
    }
    
    if (!accessCheck?.has_access) {
      result.recommendations.push('❌ check_user_access retornou false - verificar função')
    }
    
    if (result.recommendations.length === 0) {
      result.recommendations.push('✅ Tudo parece estar correto!')
    }
    
    console.log('🎯 [DEBUG-PREMIUM] Resultado final:', JSON.stringify(result, null, 2))
    
    return NextResponse.json(result, { 
      status: 200,
      headers: {
        'Content-Type': 'application/json'
      }
    })
    
  } catch (error) {
    console.error('❌ [DEBUG-PREMIUM] Erro geral:', error)
    return NextResponse.json({
      error: 'Erro ao debugar',
      details: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    }, { status: 500 })
  }
}