import { NextResponse } from 'next/server'
import { createServiceClient } from '@/lib/supabase/server'

export async function GET() {
  try {
    const supabase = createServiceClient()
    
    // Teste 1: Verificar conexão
    const { data: test, error: testError } = await supabase
      .from('profiles')
      .select('count')
      .limit(1)
    
    if (testError) {
      return NextResponse.json({
        error: 'Database connection error',
        details: testError.message
      }, { status: 500 })
    }
    
    // Teste 2: Verificar se as colunas existem
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('id, email, subscription_tier, subscription_status, stripe_customer_id, stripe_subscription_id, subscription_current_period_end')
      .limit(1)
      .maybeSingle()
    
    // Teste 3: Contar usuários
    const { count: userCount } = await supabase
      .from('profiles')
      .select('*', { count: 'exact', head: true })
    
    // Teste 4: Verificar assinaturas ativas
    const { data: activeSubscriptions } = await supabase
      .from('profiles')
      .select('id, email, subscription_tier, subscription_status')
      .eq('subscription_status', 'active')
      .eq('subscription_tier', 'premium')
    
    return NextResponse.json({
      success: true,
      tests: {
        connection: 'OK',
        columnsExist: !profileError,
        totalProfiles: userCount || 0,
        activeSubscriptions: activeSubscriptions?.length || 0,
        sampleProfile: profile,
        hasStripeColumns: profile ? {
          stripe_customer_id: !!profile.stripe_customer_id,
          stripe_subscription_id: !!profile.stripe_subscription_id,
          subscription_status: !!profile.subscription_status,
          subscription_current_period_end: !!profile.subscription_current_period_end
        } : null
      }
    })
    
  } catch (error) {
    return NextResponse.json({
      error: 'Server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}