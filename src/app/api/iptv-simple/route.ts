import { NextResponse } from 'next/server'
import { httpGet } from '@/lib/http'
import { logger } from '@/lib/logger'
import { LOG_TAGS } from '@/config/debug'
import { isSelectedSportsChannel } from '@/lib/selected-sports-channels'

export async function GET() {
  try {
    logger.info(LOG_TAGS.IPTV, 'Starting IPTV API request')
    
    // Buscar direto da API usando httpGet
    const [streams, channels] = await Promise.all([
      httpGet('https://iptv-org.github.io/api/streams.json', { tag: LOG_TAGS.IPTV }),
      httpGet('https://iptv-org.github.io/api/channels.json', { tag: LOG_TAGS.IPTV })
    ])
    
    logger.info(LOG_TAGS.IPTV, 'API data received', {
      streamsCount: streams.length,
      channelsCount: channels.length
    })
    
    // Criar mapa
    const channelsMap = new Map()
    channels.forEach(ch => channelsMap.set(ch.id, ch))
    
    // Filtrar esportes
    const sportsStreams = streams.filter(stream => {
      const channel = channelsMap.get(stream.channel)
      return channel?.categories?.includes('sports')
    })
    
    logger.info(LOG_TAGS.IPTV, 'Sports streams filtered', {
      sportsStreamsCount: sportsStreams.length,
      percentage: ((sportsStreams.length / streams.length) * 100).toFixed(2) + '%'
    })
    
    // Aplicar filtro dos 69 canais selecionados
    const selectedStreams = sportsStreams.filter(stream => 
      isSelectedSportsChannel(stream.channel)
    )
    
    logger.info(LOG_TAGS.IPTV, 'Selected sports channels filtered', {
      selectedCount: selectedStreams.length,
      totalSportsCount: sportsStreams.length
    })
    
    // Agrupar por canal e pegar apenas o primeiro stream de cada canal
    const uniqueChannels = new Map<string, typeof selectedStreams[0]>()
    selectedStreams.forEach(stream => {
      if (!uniqueChannels.has(stream.channel)) {
        uniqueChannels.set(stream.channel, stream)
      }
    })
    
    logger.info(LOG_TAGS.IPTV, 'Unique channels after deduplication', {
      uniqueCount: uniqueChannels.size,
      totalStreams: selectedStreams.length
    })
    
    // Log detalhado dos canais únicos
    console.log('[IPTV-SIMPLE] Unique channel IDs:', Array.from(uniqueChannels.keys()))
    console.log('[IPTV-SIMPLE] Sample channels:', Array.from(uniqueChannels.entries()).slice(0, 5).map(([id, stream]) => ({
      id,
      url: stream.url,
      http_referrer: stream.http_referrer
    })))
    
    // Enriquecer sem limite adicional
    const enriched = Array.from(uniqueChannels.values()).map(stream => {
      const channel = channelsMap.get(stream.channel)
      const enrichedChannel = {
        ...stream,
        uniqueId: stream.channel,
        name: channel?.name || stream.channel,
        logo: channel?.logo,
        country: channel?.country,
        categories: channel?.categories || []
      }
      
      // Log de debug para o primeiro canal
      if (stream.channel === '30AGolfKingdom.us') {
        console.log('[IPTV-SIMPLE] 30AGolfKingdom.us details:', {
          streamChannel: stream.channel,
          uniqueId: enrichedChannel.uniqueId,
          name: enrichedChannel.name,
          url: enrichedChannel.url,
          hasChannelMetadata: !!channel
        })
      }
      
      return enrichedChannel
    })
    
    return NextResponse.json({
      success: true,
      data: enriched,
      total: uniqueChannels.size
    })
    
  } catch (error: any) {
    logger.error(LOG_TAGS.IPTV, 'Failed to fetch IPTV data', {
      error: error.message,
      stack: error.stack
    })
    return NextResponse.json({ 
      success: false, 
      error: error.message 
    }, { status: 500 })
  }
}