import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { getUser } from '@stackframe/stack'

export async function POST(request: NextRequest) {
  try {
    // Verificar autenticação
    const user = await getUser({ or: 'redirect' })
    
    if (!user) {
      return NextResponse.json({ error: 'Não autenticado' }, { status: 401 })
    }

    const { codigo } = await request.json()

    if (!codigo) {
      return NextResponse.json({ error: 'Código não fornecido' }, { status: 400 })
    }

    const supabase = await createClient()

    // Chamar função RPC para aplicar cupom
    const { data, error } = await supabase
      .rpc('apply_coupon', { 
        p_user_id: user.id, 
        p_codigo: codigo.toUpperCase() 
      })
      .single()

    if (error) {
      console.error('Erro ao aplicar cupom:', error)
      return NextResponse.json({ 
        success: false,
        message: 'Erro ao processar cupom'
      }, { status: 500 })
    }

    return NextResponse.json(data)

  } catch (error) {
    console.error('Erro:', error)
    return NextResponse.json({ 
      success: false,
      message: 'Erro interno do servidor'
    }, { status: 500 })
  }
}