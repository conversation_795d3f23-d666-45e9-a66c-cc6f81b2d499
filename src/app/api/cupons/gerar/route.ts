import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { getUser } from '@stackframe/stack'

export async function POST(request: NextRequest) {
  try {
    // Verificar autenticação
    const user = await getUser({ or: 'redirect' })
    
    if (!user) {
      return NextResponse.json({ error: 'Não autenticado' }, { status: 401 })
    }

    const supabase = await createClient()

    // Verificar se já tem cupom
    const { data: cupomExistente } = await supabase
      .from('cupons')
      .select('*')
      .eq('user_id', user.id)
      .single()

    if (cupomExistente) {
      return NextResponse.json({ 
        codigo: cupomExistente.codigo,
        ja_existente: true,
        usado: cupomExistente.usado
      })
    }

    // Gerar código único
    const codigo = 'APP' + Math.random().toString(36).substr(2, 6).toUpperCase()
    
    // Criar cupom
    const { data, error } = await supabase
      .from('cupons')
      .insert({
        codigo,
        user_id: user.id
      })
      .select()
      .single()

    if (error) {
      console.error('Erro ao gerar cupom:', error)
      return NextResponse.json({ error: 'Erro ao gerar cupom' }, { status: 500 })
    }

    return NextResponse.json({ 
      codigo: data.codigo,
      ja_existente: false,
      usado: false
    })

  } catch (error) {
    console.error('Erro:', error)
    return NextResponse.json({ error: 'Erro interno' }, { status: 500 })
  }
}