import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { getUser } from '@stackframe/stack'

export async function GET(request: NextRequest) {
  try {
    // Verificar autenticação
    const user = await getUser({ or: 'redirect' })
    
    if (!user) {
      return NextResponse.json({ 
        hasAccess: false,
        accessType: null,
        expiresAt: null
      })
    }

    const supabase = await createClient()

    // Chamar função RPC para verificar acesso
    const { data, error } = await supabase
      .rpc('check_user_access', { p_user_id: user.id })
      .single()

    if (error) {
      console.error('Erro ao verificar acesso:', error)
      return NextResponse.json({ 
        hasAccess: false,
        accessType: null,
        expiresAt: null
      })
    }

    return NextResponse.json({
      hasAccess: data?.has_access || false,
      accessType: data?.access_type || null,
      expiresAt: data?.expires_at || null
    })

  } catch (error) {
    console.error('Erro:', error)
    return NextResponse.json({ 
      hasAccess: false,
      accessType: null,
      expiresAt: null
    })
  }
}