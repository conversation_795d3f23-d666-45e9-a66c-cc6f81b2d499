import { NextResponse } from 'next/server'

export async function GET() {
  try {
    console.log('🔍 [TEST] Testando IPTV-ORG API diretamente...')
    
    // 1. Buscar streams
    const streamsResponse = await fetch('https://iptv-org.github.io/api/streams.json')
    const allStreams = await streamsResponse.json()
    console.log(`📊 Total de streams: ${allStreams.length}`)
    
    // 2. Buscar channels
    const channelsResponse = await fetch('https://iptv-org.github.io/api/channels.json')
    const allChannels = await channelsResponse.json()
    console.log(`📊 Total de canais: ${allChannels.length}`)
    
    // 3. Criar mapa de canais
    const channelsMap = new Map()
    allChannels.forEach(ch => channelsMap.set(ch.id, ch))
    
    // 4. Contar streams por categoria
    const categoryCounts = {}
    allStreams.forEach(stream => {
      const channel = channelsMap.get(stream.channel)
      if (channel?.categories) {
        channel.categories.forEach(cat => {
          categoryCounts[cat] = (categoryCounts[cat] || 0) + 1
        })
      }
    })
    
    console.log('📈 Streams por categoria:', categoryCounts)
    
    // 5. Filtrar streams esportivos
    const sportsStreams = allStreams.filter(stream => {
      const channel = channelsMap.get(stream.channel)
      return channel?.categories?.includes('sports')
    })
    
    console.log(`⚽ Streams esportivos: ${sportsStreams.length}`)
    
    // 6. Amostrar alguns canais esportivos
    const sample = sportsStreams.slice(0, 10).map(stream => {
      const channel = channelsMap.get(stream.channel)
      return {
        id: stream.channel,
        name: channel?.name || stream.channel,
        country: channel?.country,
        categories: channel?.categories,
        url: stream.url,
        quality: stream.quality
      }
    })
    
    return NextResponse.json({
      success: true,
      stats: {
        totalStreams: allStreams.length,
        totalChannels: allChannels.length,
        sportsStreams: sportsStreams.length,
        categoryCounts
      },
      sample,
      debug: {
        // Verificar se categoria está em minúsculas ou maiúsculas
        sportsLowerCase: allStreams.filter(s => {
          const ch = channelsMap.get(s.channel)
          return ch?.categories?.includes('sports')
        }).length,
        sportsUpperCase: allStreams.filter(s => {
          const ch = channelsMap.get(s.channel)
          return ch?.categories?.includes('Sports')
        }).length
      }
    })
  } catch (error) {
    console.error('❌ Erro:', error)
    return NextResponse.json({ 
      success: false, 
      error: error.message 
    }, { status: 500 })
  }
}