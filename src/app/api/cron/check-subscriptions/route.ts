import { NextRequest, NextResponse } from 'next/server'
import { createServiceClient } from '@/lib/supabase/server'
import { stripe } from '@/lib/stripe'

// Este endpoint deve ser chamado periodicamente (diariamente) por um serviço de cron
export async function GET(request: NextRequest) {
  const timestamp = new Date().toISOString().replace('T', ' ').replace('Z', '')
  console.log(`[SUBSCRIPTION-CHECK] ${timestamp} - ===== INICIANDO VERIFICAÇÃO DE ASSINATURAS =====`)
  
  try {
    // Verificar autorização (adicionar uma chave secreta para o cron)
    const authHeader = request.headers.get('authorization')
    if (authHeader !== `Bearer ${process.env.CRON_SECRET}`) {
      console.log(`[SUBSCRIPTION-CHECK] ${timestamp} - ❌ Unauthorized request`)
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
    
    const supabase = createServiceClient()
    const now = new Date()
    const checkDate = new Date()
    checkDate.setDate(checkDate.getDate() + 3) // Verificar assinaturas que vencem em 3 dias
    
    console.log(`[SUBSCRIPTION-CHECK] ${timestamp} - Verificando assinaturas que vencem até:`, checkDate.toISOString())
    
    // 1. Buscar assinaturas próximas do vencimento em profiles
    const { data: expiringProfiles, error: profilesError } = await supabase
      .from('profiles')
      .select('*')
      .eq('subscription_status', 'active')
      .lte('subscription_current_period_end', checkDate.toISOString())
      .gt('subscription_current_period_end', now.toISOString())
    
    if (profilesError) {
      console.error(`[SUBSCRIPTION-CHECK] ${timestamp} - ❌ Erro ao buscar profiles:`, profilesError)
    }
    
    console.log(`[SUBSCRIPTION-CHECK] ${timestamp} - Profiles próximos do vencimento:`, expiringProfiles?.length || 0)
    
    // 2. Buscar assinaturas próximas do vencimento em stack_profiles
    const { data: expiringStackProfiles, error: stackError } = await supabase
      .from('stack_profiles')
      .select('*')
      .eq('subscription_status', 'active')
      .lte('subscription_current_period_end', checkDate.toISOString())
      .gt('subscription_current_period_end', now.toISOString())
    
    if (stackError) {
      console.error(`[SUBSCRIPTION-CHECK] ${timestamp} - ❌ Erro ao buscar stack_profiles:`, stackError)
    }
    
    console.log(`[SUBSCRIPTION-CHECK] ${timestamp} - Stack profiles próximos do vencimento:`, expiringStackProfiles?.length || 0)
    
    // 3. Verificar assinaturas vencidas
    const { data: expiredProfiles, error: expiredError } = await supabase
      .from('profiles')
      .select('*')
      .eq('subscription_status', 'active')
      .lt('subscription_current_period_end', now.toISOString())
    
    if (expiredError) {
      console.error(`[SUBSCRIPTION-CHECK] ${timestamp} - ❌ Erro ao buscar vencidos:`, expiredError)
    }
    
    console.log(`[SUBSCRIPTION-CHECK] ${timestamp} - Profiles vencidos:`, expiredProfiles?.length || 0)
    
    const { data: expiredStackProfiles, error: expiredStackError } = await supabase
      .from('stack_profiles')
      .select('*')
      .eq('subscription_status', 'active')
      .lt('subscription_current_period_end', now.toISOString())
    
    if (expiredStackError) {
      console.error(`[SUBSCRIPTION-CHECK] ${timestamp} - ❌ Erro ao buscar stack vencidos:`, expiredStackError)
    }
    
    console.log(`[SUBSCRIPTION-CHECK] ${timestamp} - Stack profiles vencidos:`, expiredStackProfiles?.length || 0)
    
    // 4. Processar assinaturas vencidas
    let processedCount = 0
    let blockedCount = 0
    
    // Processar profiles vencidos
    if (expiredProfiles && expiredProfiles.length > 0) {
      for (const profile of expiredProfiles) {
        console.log(`[SUBSCRIPTION-CHECK] ${timestamp} - Processando profile vencido:`, profile.id)
        
        if (profile.stripe_subscription_id) {
          try {
            // Verificar status no Stripe
            const subscription = await stripe.subscriptions.retrieve(profile.stripe_subscription_id)
            console.log(`[SUBSCRIPTION-CHECK] ${timestamp} - Status Stripe:`, subscription.status)
            
            if (subscription.status === 'active') {
              // Assinatura ainda ativa no Stripe, atualizar período
              console.log(`[SUBSCRIPTION-CHECK] ${timestamp} - Atualizando período para:`, new Date(subscription.current_period_end * 1000).toISOString())
              
              await supabase
                .from('profiles')
                .update({
                  subscription_current_period_end: new Date(subscription.current_period_end * 1000).toISOString(),
                  updated_at: new Date().toISOString()
                })
                .eq('id', profile.id)
              
              processedCount++
            } else {
              // Assinatura não está mais ativa, bloquear acesso
              console.log(`[SUBSCRIPTION-CHECK] ${timestamp} - Bloqueando acesso para:`, profile.id)
              
              await supabase
                .from('profiles')
                .update({
                  subscription_status: subscription.status,
                  subscription_tier: 'free',
                  updated_at: new Date().toISOString()
                })
                .eq('id', profile.id)
              
              blockedCount++
            }
          } catch (error) {
            console.error(`[SUBSCRIPTION-CHECK] ${timestamp} - ❌ Erro ao verificar Stripe:`, error)
          }
        } else {
          // Sem ID do Stripe, bloquear por segurança
          console.log(`[SUBSCRIPTION-CHECK] ${timestamp} - Sem Stripe ID, bloqueando:`, profile.id)
          
          await supabase
            .from('profiles')
            .update({
              subscription_status: 'canceled',
              subscription_tier: 'free',
              updated_at: new Date().toISOString()
            })
            .eq('id', profile.id)
          
          blockedCount++
        }
      }
    }
    
    // Processar stack_profiles vencidos (mesma lógica)
    if (expiredStackProfiles && expiredStackProfiles.length > 0) {
      for (const profile of expiredStackProfiles) {
        console.log(`[SUBSCRIPTION-CHECK] ${timestamp} - Processando stack_profile vencido:`, profile.id)
        
        if (profile.stripe_subscription_id) {
          try {
            const subscription = await stripe.subscriptions.retrieve(profile.stripe_subscription_id)
            console.log(`[SUBSCRIPTION-CHECK] ${timestamp} - Status Stripe:`, subscription.status)
            
            if (subscription.status === 'active') {
              console.log(`[SUBSCRIPTION-CHECK] ${timestamp} - Atualizando período para:`, new Date(subscription.current_period_end * 1000).toISOString())
              
              await supabase
                .from('stack_profiles')
                .update({
                  subscription_current_period_end: new Date(subscription.current_period_end * 1000).toISOString(),
                  updated_at: new Date().toISOString()
                })
                .eq('id', profile.id)
              
              processedCount++
            } else {
              console.log(`[SUBSCRIPTION-CHECK] ${timestamp} - Bloqueando acesso para:`, profile.id)
              
              await supabase
                .from('stack_profiles')
                .update({
                  subscription_status: subscription.status,
                  subscription_tier: 'free',
                  updated_at: new Date().toISOString()
                })
                .eq('id', profile.id)
              
              blockedCount++
            }
          } catch (error) {
            console.error(`[SUBSCRIPTION-CHECK] ${timestamp} - ❌ Erro ao verificar Stripe:`, error)
          }
        } else {
          console.log(`[SUBSCRIPTION-CHECK] ${timestamp} - Sem Stripe ID, bloqueando:`, profile.id)
          
          await supabase
            .from('stack_profiles')
            .update({
              subscription_status: 'canceled',
              subscription_tier: 'free',
              updated_at: new Date().toISOString()
            })
            .eq('id', profile.id)
          
          blockedCount++
        }
      }
    }
    
    const result = {
      timestamp,
      checked: {
        expiring: {
          profiles: expiringProfiles?.length || 0,
          stackProfiles: expiringStackProfiles?.length || 0
        },
        expired: {
          profiles: expiredProfiles?.length || 0,
          stackProfiles: expiredStackProfiles?.length || 0
        }
      },
      processed: processedCount,
      blocked: blockedCount
    }
    
    console.log(`[SUBSCRIPTION-CHECK] ${timestamp} - ===== VERIFICAÇÃO CONCLUÍDA =====`)
    console.log(`[SUBSCRIPTION-CHECK] ${timestamp} - Resultado:`, JSON.stringify(result, null, 2))
    
    return NextResponse.json(result)
    
  } catch (error) {
    console.error(`[SUBSCRIPTION-CHECK] ${timestamp} - ❌ Erro geral:`, error)
    return NextResponse.json({
      error: 'Internal error',
      message: error instanceof Error ? error.message : 'Unknown error',
      timestamp
    }, { status: 500 })
  }
}