import { NextRequest, NextResponse } from 'next/server'
import { createServiceClient } from '@/lib/supabase/server'

export async function POST(request: NextRequest) {
  const timestamp = new Date().toISOString()
  console.log(`[SYNC-USER-API] ${timestamp} - ===== REQUEST RECEIVED =====`)
  
  try {
    const body = await request.json()
    const { stackUser } = body
    
    if (!stackUser || !stackUser.id) {
      return NextResponse.json({ 
        success: false, 
        error: 'Stack user data is required' 
      }, { status: 400 })
    }
    
    console.log(`[SYNC-USER-API] ${timestamp} - Stack User ID:`, stackUser.id)
    console.log(`[SYNC-USER-API] ${timestamp} - Email:`, stackUser.primaryEmail)
    console.log(`[SYNC-USER-API] ${timestamp} - Display Name:`, stackUser.displayName)
    
    const supabase = createServiceClient()
    
    // Verificar se já existe perfil
    const { data: existingProfile, error: fetchError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', stackUser.id)
      .single()
    
    if (fetchError && fetchError.code !== 'PGRST116') { // PGRST116 = não encontrado
      console.error(`[SYNC-USER-API] ${timestamp} - ❌ Erro ao buscar perfil:`, fetchError)
      throw fetchError
    }
    
    const profileData = {
      id: stackUser.id,
      email: stackUser.primaryEmail,
      full_name: stackUser.displayName || stackUser.primaryEmail?.split('@')[0] || 'User',
      avatar_url: stackUser.profileImageUrl,
      // Preservar dados de assinatura se existirem
      subscription_tier: existingProfile?.subscription_tier || 'free',
      subscription_status: existingProfile?.subscription_status || 'inactive',
      subscription_current_period_end: existingProfile?.subscription_current_period_end,
      subscription_start_date: existingProfile?.subscription_start_date,
      stripe_customer_id: existingProfile?.stripe_customer_id,
      stripe_subscription_id: existingProfile?.stripe_subscription_id,
      updated_at: new Date().toISOString()
    }
    
    let result
    
    if (existingProfile) {
      console.log(`[SYNC-USER-API] ${timestamp} - Atualizando perfil existente...`)
      
      // Só atualizar campos básicos, preservar dados de assinatura
      const { data, error } = await supabase
        .from('profiles')
        .update({
          email: profileData.email,
          full_name: profileData.full_name,
          avatar_url: profileData.avatar_url,
          updated_at: profileData.updated_at
        })
        .eq('id', stackUser.id)
        .select()
        .single()
      
      if (error) {
        console.error(`[SYNC-USER-API] ${timestamp} - ❌ Erro ao atualizar perfil:`, error)
        throw error
      }
      
      result = data
      console.log(`[SYNC-USER-API] ${timestamp} - ✅ Perfil atualizado`)
    } else {
      console.log(`[SYNC-USER-API] ${timestamp} - Criando novo perfil...`)
      
      // Criar novo perfil
      const { data, error } = await supabase
        .from('profiles')
        .insert([profileData])
        .select()
        .single()
      
      if (error) {
        console.error(`[SYNC-USER-API] ${timestamp} - ❌ Erro ao criar perfil:`, error)
        throw error
      }
      
      result = data
      console.log(`[SYNC-USER-API] ${timestamp} - ✅ Perfil criado`)
    }
    
    return NextResponse.json({
      success: true,
      profile: result
    })
    
  } catch (error) {
    console.error(`[SYNC-USER-API] ${timestamp} - ❌ Erro geral:`, error)
    return NextResponse.json({ 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}