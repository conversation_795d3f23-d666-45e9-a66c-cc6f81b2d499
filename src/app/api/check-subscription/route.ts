import { NextRequest, NextResponse } from 'next/server'
import { createServiceClient } from '@/lib/supabase/server'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const email = searchParams.get('email') || '<EMAIL>'
    
    const supabase = createServiceClient()
    
    console.log('🔍 Verificando assinatura para:', email)
    
    // Buscar usuário no auth
    const { data: authUser, error: authError } = await supabase.auth.admin.getUserByEmail(email)
    
    if (authError || !authUser) {
      console.log('❌ Usuário não encontrado no auth')
      return NextResponse.json({
        error: 'Usuário não encontrado',
        email,
        authError: authError?.message
      }, { status: 404 })
    }
    
    const userId = authUser.user.id
    console.log('✅ Usuário encontrado:', userId)
    
    // Buscar perfil
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', userId)
      .maybeSingle()
    
    console.log('📋 Perfil:', profile)
    
    // Se não existir perfil, criar um
    if (!profile) {
      console.log('🔄 Criando perfil...')
      const { data: newProfile, error: createError } = await supabase
        .from('profiles')
        .insert({
          id: userId,
          email: authUser.user.email,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()
        .single()
      
      if (createError) {
        console.error('❌ Erro ao criar perfil:', createError)
        return NextResponse.json({
          error: 'Erro ao criar perfil',
          details: createError.message
        }, { status: 500 })
      }
      
      return NextResponse.json({
        user: {
          id: userId,
          email: authUser.user.email
        },
        profile: newProfile,
        subscription: {
          active: false,
          tier: 'free',
          message: 'Perfil criado. Aguardando pagamento.'
        }
      })
    }
    
    // Verificar status da assinatura
    const hasActiveSubscription = profile.subscription_status === 'active' && 
      profile.subscription_current_period_end && 
      new Date(profile.subscription_current_period_end) > new Date()
    
    return NextResponse.json({
      user: {
        id: userId,
        email: authUser.user.email
      },
      profile: {
        ...profile,
        hasActiveSubscription,
        daysRemaining: profile.subscription_current_period_end ? 
          Math.ceil((new Date(profile.subscription_current_period_end).getTime() - Date.now()) / (1000 * 60 * 60 * 24)) : 0
      },
      subscription: {
        active: hasActiveSubscription,
        tier: profile.subscription_tier || 'free',
        status: profile.subscription_status,
        stripe_customer_id: profile.stripe_customer_id,
        stripe_subscription_id: profile.stripe_subscription_id,
        expires_at: profile.subscription_current_period_end
      }
    })
    
  } catch (error) {
    console.error('❌ Erro geral:', error)
    return NextResponse.json({
      error: 'Erro interno',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}