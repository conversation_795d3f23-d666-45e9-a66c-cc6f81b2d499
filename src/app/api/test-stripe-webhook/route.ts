import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

export async function POST(request: NextRequest) {
  try {
    const { email, customerId, subscriptionId } = await request.json()
    
    if (!email || !customerId || !subscriptionId) {
      return NextResponse.json(
        { error: 'Email, customerId e subscriptionId são obrigatórios' },
        { status: 400 }
      )
    }
    
    const supabase = await createClient()
    const timestamp = new Date().toISOString()
    
    console.log(`[TEST-WEBHOOK] ${timestamp} - ===== TESTANDO ATUALIZAÇÃO =====`)
    console.log(`[TEST-WEBHOOK] ${timestamp} - Email:`, email)
    console.log(`[TEST-WEBHOOK] ${timestamp} - Customer ID:`, customerId)
    console.log(`[TEST-WEBHOOK] ${timestamp} - Subscription ID:`, subscriptionId)
    
    // Buscar usuário por email
    console.log(`[TEST-WEBHOOK] ${timestamp} - Buscando usuário no auth.users...`)
    const { data: authUser } = await supabase.auth.admin.getUserByEmail(email)
    
    let userId = null
    let tableName = null
    
    if (authUser) {
      userId = authUser.user.id
      tableName = 'profiles'
      console.log(`[TEST-WEBHOOK] ${timestamp} - ✅ Encontrado no auth.users:`, userId)
    } else {
      // Buscar no stack_profiles
      console.log(`[TEST-WEBHOOK] ${timestamp} - Buscando no stack_profiles...`)
      const { data: stackProfiles } = await supabase
        .from('stack_profiles')
        .select('*')
        .ilike('email', email)
      
      if (stackProfiles && stackProfiles.length > 0) {
        userId = stackProfiles[0].id
        tableName = 'stack_profiles'
        console.log(`[TEST-WEBHOOK] ${timestamp} - ✅ Encontrado no stack_profiles:`, userId)
      }
    }
    
    if (!userId) {
      console.error(`[TEST-WEBHOOK] ${timestamp} - ❌ Usuário não encontrado`)
      return NextResponse.json({
        error: 'Usuário não encontrado',
        searched: { email }
      }, { status: 404 })
    }
    
    // Simular dados de assinatura
    const expirationDate = new Date()
    expirationDate.setMonth(expirationDate.getMonth() + 1)
    
    const updateData = {
      stripe_customer_id: customerId,
      stripe_subscription_id: subscriptionId,
      subscription_status: 'active',
      subscription_current_period_end: expirationDate.toISOString(),
      subscription_expires_at: expirationDate.toISOString(),
      subscription_tier: 'premium',
      updated_at: new Date().toISOString()
    }
    
    console.log(`[TEST-WEBHOOK] ${timestamp} - Atualizando ${tableName}...`)
    console.log(`[TEST-WEBHOOK] ${timestamp} - Dados:`, updateData)
    
    // Atualizar ambas as tabelas se for auth.users
    if (tableName === 'profiles') {
      const { error: profileError } = await supabase
        .from('profiles')
        .update(updateData)
        .eq('id', userId)
      
      if (profileError) {
        console.error(`[TEST-WEBHOOK] ${timestamp} - ❌ Erro ao atualizar profiles:`, profileError)
      } else {
        console.log(`[TEST-WEBHOOK] ${timestamp} - ✅ Profiles atualizado`)
      }
      
      // Tentar atualizar stack_profiles também
      const { error: stackError } = await supabase
        .from('stack_profiles')
        .update(updateData)
        .eq('id', userId)
      
      if (stackError) {
        console.log(`[TEST-WEBHOOK] ${timestamp} - Stack profiles não atualizado (pode não existir)`)
      } else {
        console.log(`[TEST-WEBHOOK] ${timestamp} - ✅ Stack profiles também atualizado`)
      }
    } else {
      // Atualizar apenas stack_profiles
      const { error } = await supabase
        .from('stack_profiles')
        .update(updateData)
        .eq('id', userId)
      
      if (error) {
        console.error(`[TEST-WEBHOOK] ${timestamp} - ❌ Erro ao atualizar stack_profiles:`, error)
        return NextResponse.json({
          error: 'Erro ao atualizar perfil',
          details: error
        }, { status: 500 })
      }
      console.log(`[TEST-WEBHOOK] ${timestamp} - ✅ Stack profiles atualizado`)
    }
    
    // Verificar a atualização
    console.log(`[TEST-WEBHOOK] ${timestamp} - Verificando atualização...`)
    
    if (tableName === 'profiles') {
      const { data: profile } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single()
      
      console.log(`[TEST-WEBHOOK] ${timestamp} - Perfil após atualização:`)
      console.log(`[TEST-WEBHOOK] ${timestamp} -   subscription_tier:`, profile?.subscription_tier)
      console.log(`[TEST-WEBHOOK] ${timestamp} -   subscription_status:`, profile?.subscription_status)
      console.log(`[TEST-WEBHOOK] ${timestamp} -   subscription_expires_at:`, profile?.subscription_expires_at)
    }
    
    const { data: stackProfile } = await supabase
      .from('stack_profiles')
      .select('*')
      .eq('id', userId)
      .single()
    
    if (stackProfile) {
      console.log(`[TEST-WEBHOOK] ${timestamp} - Stack Profile após atualização:`)
      console.log(`[TEST-WEBHOOK] ${timestamp} -   subscription_tier:`, stackProfile?.subscription_tier)
      console.log(`[TEST-WEBHOOK] ${timestamp} -   subscription_status:`, stackProfile?.subscription_status)
      console.log(`[TEST-WEBHOOK] ${timestamp} -   subscription_current_period_end:`, stackProfile?.subscription_current_period_end)
    }
    
    return NextResponse.json({
      success: true,
      message: 'Teste executado com sucesso',
      userId,
      tableName,
      updateData,
      verificationUrl: '/api/stripe/subscription-status'
    })
    
  } catch (error) {
    console.error('[TEST-WEBHOOK] Erro:', error)
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Use POST com { email, customerId, subscriptionId }'
  })
}