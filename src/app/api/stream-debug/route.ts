import { NextRequest, NextResponse } from 'next/server'
import { logger } from '@/lib/logger'
import { LOG_TAGS } from '@/config/debug'

interface StreamTestResult {
  url: string
  accessible: boolean
  statusCode?: number
  contentType?: string
  headers?: Record<string, string>
  error?: string
  method: 'direct' | 'proxy'
  manifestContent?: string
  segments?: {
    url: string
    accessible: boolean
    statusCode?: number
    error?: string
  }[]
}

async function testDirectAccess(url: string): Promise<StreamTestResult> {
  try {
    const response = await fetch(url, {
      method: 'HEAD',
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Accept': '*/*',
      }
    })
    
    const result: StreamTestResult = {
      url,
      accessible: response.ok,
      statusCode: response.status,
      contentType: response.headers.get('content-type') || undefined,
      headers: Object.fromEntries(response.headers.entries()),
      method: 'direct'
    }
    
    // Se for M3U8, tentar baixar e analisar
    if (response.ok && url.includes('.m3u8')) {
      const fullResponse = await fetch(url)
      const content = await fullResponse.text()
      result.manifestContent = content.substring(0, 500) // Primeiros 500 chars
      
      // Extrair URLs de segmentos
      const segmentUrls = extractSegmentUrls(content, url)
      if (segmentUrls.length > 0) {
        // Testar apenas os primeiros 3 segmentos
        result.segments = await Promise.all(
          segmentUrls.slice(0, 3).map(segUrl => testSegment(segUrl))
        )
      }
    }
    
    return result
  } catch (error) {
    return {
      url,
      accessible: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      method: 'direct'
    }
  }
}

async function testSegment(url: string): Promise<any> {
  try {
    const response = await fetch(url, {
      method: 'HEAD',
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Accept': '*/*',
      }
    })
    
    return {
      url,
      accessible: response.ok,
      statusCode: response.status,
      error: response.ok ? undefined : `HTTP ${response.status}`
    }
  } catch (error) {
    return {
      url,
      accessible: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

function extractSegmentUrls(m3u8Content: string, baseUrl: string): string[] {
  const lines = m3u8Content.split('\n')
  const segments: string[] = []
  
  for (const line of lines) {
    const trimmed = line.trim()
    if (trimmed && !trimmed.startsWith('#')) {
      // É uma URL de segmento
      let segmentUrl: string
      
      if (trimmed.startsWith('http://') || trimmed.startsWith('https://')) {
        segmentUrl = trimmed
      } else if (trimmed.startsWith('/')) {
        const urlObj = new URL(baseUrl)
        segmentUrl = `${urlObj.protocol}//${urlObj.host}${trimmed}`
      } else {
        const baseWithoutFile = baseUrl.substring(0, baseUrl.lastIndexOf('/'))
        segmentUrl = `${baseWithoutFile}/${trimmed}`
      }
      
      segments.push(segmentUrl)
    }
  }
  
  return segments
}

export async function POST(request: NextRequest) {
  const { url } = await request.json()
  
  if (!url) {
    return NextResponse.json({ error: 'URL is required' }, { status: 400 })
  }
  
  logger.info(LOG_TAGS.HTTP, 'Stream debug request', { url })
  
  // Testar acesso direto
  const directResult = await testDirectAccess(url)
  
  // Se falhou, testar com diferentes headers
  const results: StreamTestResult[] = [directResult]
  
  if (!directResult.accessible) {
    // Tentar com diferentes User-Agents
    const userAgents = [
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
      'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15',
      'VLC/3.0.16 LibVLC/3.0.16',
      'Lavf/58.76.100', // FFmpeg
    ]
    
    for (const ua of userAgents) {
      const response = await fetch(url, {
        method: 'HEAD',
        headers: {
          'User-Agent': ua,
          'Accept': '*/*',
          'Accept-Language': 'en-US,en;q=0.9',
          'Connection': 'keep-alive',
        }
      }).catch(() => null)
      
      if (response && response.ok) {
        results.push({
          url,
          accessible: true,
          statusCode: response.status,
          contentType: response.headers.get('content-type') || undefined,
          method: 'direct',
          headers: { 'User-Agent': ua }
        })
        break
      }
    }
  }
  
  // Análise dos resultados
  const analysis = {
    url,
    directAccessible: directResult.accessible,
    requiresSpecialHeaders: results.some(r => r.accessible && r.headers),
    probableIssue: determineProbableIssue(results),
    recommendations: generateRecommendations(results),
    results
  }
  
  return NextResponse.json(analysis)
}

function determineProbableIssue(results: StreamTestResult[]): string {
  const firstResult = results[0]
  
  if (firstResult.statusCode === 403) {
    return 'Geoblocking or authentication required'
  } else if (firstResult.statusCode === 404) {
    return 'Stream URL is invalid or expired'
  } else if (firstResult.error?.includes('CORS')) {
    return 'CORS policy blocking - proxy required'
  } else if (firstResult.segments?.some(s => !s.accessible)) {
    return 'Manifest accessible but segments are protected'
  } else if (!firstResult.accessible && results.some(r => r.accessible)) {
    return 'Requires specific User-Agent or headers'
  }
  
  return 'Unknown issue'
}

function generateRecommendations(results: StreamTestResult[]): string[] {
  const recommendations: string[] = []
  const firstResult = results[0]
  
  if (firstResult.statusCode === 403) {
    recommendations.push('Use VPN or proxy server in allowed region')
    recommendations.push('Check if stream requires authentication token')
  }
  
  if (firstResult.statusCode === 404) {
    recommendations.push('Verify the stream URL is current')
    recommendations.push('Check with content provider for updated URLs')
  }
  
  if (!firstResult.accessible && results.some(r => r.accessible)) {
    const workingResult = results.find(r => r.accessible)
    if (workingResult?.headers) {
      recommendations.push(`Use specific headers: ${JSON.stringify(workingResult.headers)}`)
    }
  }
  
  if (firstResult.segments?.some(s => !s.accessible)) {
    recommendations.push('Implement segment-level proxy with header forwarding')
    recommendations.push('Consider using a streaming proxy service')
  }
  
  return recommendations
}