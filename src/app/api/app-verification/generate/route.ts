import { NextRequest, NextResponse } from 'next/server'
import { createServiceClient } from '@/lib/supabase/server'
import crypto from 'crypto'

export async function POST(request: NextRequest) {
  try {
    const { userId, platform } = await request.json()
    
    if (!userId) {
      return NextResponse.json({ 
        error: 'userId is required' 
      }, { status: 400 })
    }
    
    const supabase = createServiceClient()
    
    // IMPORTANTE: Verificar se o usuário já tem app_verified
    const isStackAuth = userId.includes('-')
    
    if (isStackAuth) {
      const { data: profile } = await supabase
        .from('stack_profiles')
        .select('app_verified, app_verified_at')
        .eq('id', userId)
        .single()
      
      if (profile?.app_verified) {
        console.log('Usuário já verificou app anteriormente:', userId)
        return NextResponse.json({ 
          error: 'Ya has activado tu cuenta con la aplicación anteriormente' 
        }, { status: 400 })
      }
    } else {
      const { data: profile } = await supabase
        .from('profiles')
        .select('app_verified, app_verified_at')
        .eq('id', userId)
        .single()
      
      if (profile?.app_verified) {
        console.log('Usuário já verificou app anteriormente:', userId)
        return NextResponse.json({ 
          error: 'Ya has activado tu cuenta con la aplicación anteriormente' 
        }, { status: 400 })
      }
    }
    
    // Verificar se já existe um código válido para o usuário
    const { data: existingCode } = await supabase
      .from('app_verification_codes')
      .select('*')
      .eq('user_id', userId)
      .eq('used', false)
      .gt('expires_at', new Date().toISOString())
      .single()
    
    if (existingCode) {
      console.log('Código existente encontrado:', existingCode.code)
      return NextResponse.json({ 
        code: existingCode.code,
        expires_at: existingCode.expires_at 
      })
    }
    
    // Gerar novo código único (6 caracteres alfanuméricos)
    const code = crypto.randomBytes(3).toString('hex').toUpperCase()
    
    // Código expira em 1 hora
    const expiresAt = new Date()
    expiresAt.setHours(expiresAt.getHours() + 1)
    
    // Salvar código no banco
    const { data, error } = await supabase
      .from('app_verification_codes')
      .insert({
        user_id: userId,
        code,
        platform,
        expires_at: expiresAt.toISOString(),
        used: false
      })
      .select()
      .single()
    
    if (error) {
      console.error('Erro ao salvar código:', error)
      return NextResponse.json({ 
        error: 'Erro ao gerar código de verificação' 
      }, { status: 500 })
    }
    
    console.log('Código gerado com sucesso:', code)
    
    return NextResponse.json({ 
      code: data.code,
      expires_at: data.expires_at 
    })
    
  } catch (error) {
    console.error('Erro ao gerar código:', error)
    return NextResponse.json({ 
      error: 'Erro interno do servidor' 
    }, { status: 500 })
  }
}