import { NextRequest, NextResponse } from 'next/server'
import { createServiceClient } from '@/lib/supabase/server'

export async function POST(request: NextRequest) {
  try {
    const { userId, code } = await request.json()
    
    if (!userId || !code) {
      return NextResponse.json({ 
        error: 'userId e code são obrigatórios' 
      }, { status: 400 })
    }
    
    const supabase = createServiceClient()
    
    // Verificar se o código é válido
    const { data: verificationCode, error: codeError } = await supabase
      .from('app_verification_codes')
      .select('*')
      .eq('user_id', userId)
      .eq('code', code.toUpperCase())
      .eq('used', false)
      .gt('expires_at', new Date().toISOString())
      .single()
    
    if (codeError || !verificationCode) {
      console.error('Código inválido ou expirado:', codeError)
      return NextResponse.json({ 
        error: 'Código inválido ou expirado' 
      }, { status: 400 })
    }
    
    // Marcar código como usado
    const { error: updateError } = await supabase
      .from('app_verification_codes')
      .update({ 
        used: true,
        used_at: new Date().toISOString()
      })
      .eq('id', verificationCode.id)
    
    if (updateError) {
      console.error('Erro ao marcar código como usado:', updateError)
    }
    
    // Adicionar 30 dias de acesso ao usuário
    const futureDate = new Date()
    futureDate.setDate(futureDate.getDate() + 30)
    
    // Verificar se é Stack Auth ou Supabase Auth
    const isStackAuth = userId.includes('-')
    
    if (isStackAuth) {
      // Atualizar perfil Stack Auth
      const { data: profile, error: profileError } = await supabase
        .from('stack_profiles')
        .update({
          app_verified: true,
          app_verified_at: new Date().toISOString(),
          app_platform: verificationCode.platform,
          subscription_tier: 'premium',
          subscription_status: 'active',
          subscription_current_period_end: futureDate.toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('id', userId)
        .select()
        .single()
      
      if (profileError) {
        console.error('Erro ao atualizar perfil Stack:', profileError)
        return NextResponse.json({ 
          error: 'Erro ao ativar acesso' 
        }, { status: 500 })
      }
      
      console.log('Perfil Stack atualizado:', profile)
    } else {
      // Atualizar perfil Supabase Auth
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .update({
          app_verified: true,
          app_verified_at: new Date().toISOString(),
          app_platform: verificationCode.platform,
          subscription_tier: 'premium',
          subscription_status: 'active',
          subscription_current_period_end: futureDate.toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('id', userId)
        .select()
        .single()
      
      if (profileError) {
        console.error('Erro ao atualizar perfil:', profileError)
        return NextResponse.json({ 
          error: 'Erro ao ativar acesso' 
        }, { status: 500 })
      }
      
      console.log('Perfil atualizado:', profile)
    }
    
    // Adicionar registro na tabela user_access para compatibilidade
    await supabase
      .from('user_access')
      .insert({
        user_id: userId,
        tipo: 'app_verified',
        concedido_em: new Date().toISOString(),
        ativo_ate: futureDate.toISOString(),
        criado_por: userId,
        observacoes: `App verificado - Plataforma: ${verificationCode.platform}`
      })
      .select()
    
    return NextResponse.json({ 
      success: true,
      message: '30 dias de acesso premium ativados com sucesso!',
      expires_at: futureDate.toISOString()
    })
    
  } catch (error) {
    console.error('Erro ao ativar acesso:', error)
    return NextResponse.json({ 
      error: 'Erro interno do servidor' 
    }, { status: 500 })
  }
}