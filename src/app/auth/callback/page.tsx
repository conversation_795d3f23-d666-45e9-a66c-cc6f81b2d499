'use client'

import { useEffect } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { Loader2 } from 'lucide-react'

export default function AuthCallbackPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const redirect = searchParams.get('redirect') || '/browse'
  
  useEffect(() => {
    // <PERSON>ack <PERSON>th handles the callback automatically
    // Just redirect to the intended page
    router.push(redirect)
  }, [redirect, router])
  
  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="text-center space-y-4">
        <Loader2 className="h-8 w-8 animate-spin mx-auto" />
        <p className="text-muted-foreground">Iniciando sesión...</p>
      </div>
    </div>
  )
}