'use client'

import { useUser, useStackApp } from '@stackframe/stack'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { useRouter } from 'next/navigation'

export default function StackAuthTestPage() {
  const user = useUser()
  const app = useStackApp()
  const router = useRouter()
  
  return (
    <div className="container max-w-2xl mx-auto py-10">
      <Card>
        <CardHeader>
          <CardTitle>Stack Auth - Status do Sistema</CardTitle>
          <CardDescription>
            Teste de integração com Stack Auth
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="rounded-lg bg-muted p-4">
            <h3 className="font-semibold mb-2">Estado da Autenticação:</h3>
            <p className="text-sm">
              {user ? (
                <>
                  <span className="text-green-500 font-medium">✓ Autenticado</span>
                  <br />
                  Email: {user.primaryEmail || 'Não disponível'}
                  <br />
                  ID: {user.id}
                </>
              ) : (
                <span className="text-yellow-500 font-medium">✗ Não autenticado</span>
              )}
            </p>
          </div>
          
          <div className="rounded-lg bg-muted p-4">
            <h3 className="font-semibold mb-2">Stack App:</h3>
            <p className="text-sm">
              {app ? (
                <span className="text-green-500 font-medium">✓ Inicializado corretamente</span>
              ) : (
                <span className="text-red-500 font-medium">✗ Erro na inicialização</span>
              )}
            </p>
          </div>
          
          <div className="flex gap-4">
            {user ? (
              <>
                <Button onClick={() => router.push('/browse')}>
                  Ir para Browse
                </Button>
                <Button variant="outline" onClick={() => user.signOut()}>
                  Sair
                </Button>
              </>
            ) : (
              <>
                <Button onClick={() => router.push('/login')}>
                  Login
                </Button>
                <Button variant="outline" onClick={() => router.push('/register')}>
                  Registrar
                </Button>
              </>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}