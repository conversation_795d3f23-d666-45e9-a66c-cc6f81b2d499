'use client'

import { Footer } from '@/components/footer'
import { BackToHome } from '@/components/back-to-home'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { 
  Newspaper, Download, Camera, FileText, 
  Award, Users, Globe, TrendingUp,
  Mail, Phone
} from 'lucide-react'
import Link from 'next/link'

export default function PressPage() {
  const pressReleases = [
    {
      date: '15 Enero 2024',
      title: 'StreamPlus España Alcanza 1 Millón de Suscriptores',
      description: 'La plataforma de streaming deportivo celebra un hito importante en su crecimiento.',
      type: 'milestone'
    },
    {
      date: '10 Diciembre 2023',
      title: 'Acuerdo Exclusivo con LaLiga para Temporada 2024-2025',
      description: 'StreamPlus asegura derechos de transmisión exclusivos para el mercado español.',
      type: 'partnership'
    },
    {
      date: '5 Noviembre 2023',
      title: 'Lanzamiento de Cobertura Completa de eSports',
      description: 'Nueva categoría incluye torneos principales de LoL, CS2 y Valorant.',
      type: 'product'
    },
    {
      date: '20 Septiembre 2023',
      title: 'StreamPlus Gana Premio a Mejor Plataforma de Streaming 2023',
      description: 'Reconocimiento por innovación y calidad en los Digital Media Awards.',
      type: 'award'
    }
  ]

  const mediaAssets = [
    {
      type: 'Logo',
      formats: ['PNG', 'SVG', 'EPS'],
      description: 'Logo oficial en varios formatos y colores'
    },
    {
      type: 'Screenshots',
      formats: ['PNG', 'JPG'],
      description: 'Capturas de pantalla de la plataforma y app móvil'
    },
    {
      type: 'Fotos Ejecutivos',
      formats: ['JPG'],
      description: 'Fotografías profesionales del equipo directivo'
    },
    {
      type: 'Videos',
      formats: ['MP4', 'MOV'],
      description: 'Videos promocionales y demos del producto'
    }
  ]

  const stats = [
    { label: 'Usuarios Activos', value: '1M+' },
    { label: 'Eventos Mensuales', value: '500+' },
    { label: 'Países', value: '15' },
    { label: 'Satisfacción', value: '98%' }
  ]

  const coverage = [
    { outlet: 'El País', date: 'Enero 2024', title: 'La revolución del streaming deportivo en España' },
    { outlet: 'Marca', date: 'Diciembre 2023', title: 'StreamPlus: el Netflix de los deportes' },
    { outlet: 'La Vanguardia', date: 'Noviembre 2023', title: 'Tecnología española conquista el streaming' },
    { outlet: 'Expansión', date: 'Octubre 2023', title: 'StreamPlus España cierra ronda de 50M€' }
  ]

  return (
    <div className="min-h-screen bg-gradient-to-b from-slate-950 to-slate-900">
      {/* Hero Section */}
      <section className="relative py-20 overflow-hidden">
        <div className="absolute inset-0">
          <div className="absolute inset-0 bg-gradient-to-r from-indigo-900/20 to-blue-900/20" />
        </div>
        
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <BackToHome />
          <div className="text-center">
            <Badge className="mb-4 bg-indigo-600 text-white">Sala de Prensa</Badge>
            <h1 className="text-5xl font-bold text-white mb-6">
              Centro de Prensa
            </h1>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Recursos, noticias y material para medios de comunicación sobre StreamPlus España
            </p>
          </div>
        </div>
      </section>

      {/* Quick Stats */}
      <section className="py-12 border-y border-slate-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            {stats.map((stat, index) => (
              <div key={index} className="text-center">
                <p className="text-3xl font-bold text-white">{stat.value}</p>
                <p className="text-gray-400">{stat.label}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Press Releases */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between mb-8">
            <h2 className="text-3xl font-bold text-white">Comunicados de Prensa</h2>
            <Button variant="outline">
              Ver todos
            </Button>
          </div>
          
          <div className="grid gap-6">
            {pressReleases.map((release, index) => (
              <Card key={index} className="bg-slate-800/50 border-slate-700 hover:border-slate-600 transition-all cursor-pointer">
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div>
                      <div className="flex items-center gap-3 mb-2">
                        <p className="text-sm text-gray-400">{release.date}</p>
                        <Badge 
                          className={
                            release.type === 'milestone' ? 'bg-green-600' :
                            release.type === 'partnership' ? 'bg-blue-600' :
                            release.type === 'product' ? 'bg-purple-600' :
                            'bg-yellow-600'
                          }
                        >
                          {release.type === 'milestone' ? 'Hito' :
                           release.type === 'partnership' ? 'Alianza' :
                           release.type === 'product' ? 'Producto' :
                           'Premio'}
                        </Badge>
                      </div>
                      <CardTitle className="text-white hover:text-blue-400 transition-colors">
                        {release.title}
                      </CardTitle>
                      <CardDescription className="mt-2">
                        {release.description}
                      </CardDescription>
                    </div>
                    <FileText className="h-5 w-5 text-gray-400" />
                  </div>
                </CardHeader>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Media Kit */}
      <section className="py-16 border-t border-slate-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold text-white mb-8">Kit de Prensa</h2>
          
          <div className="grid md:grid-cols-2 gap-8">
            <Card className="bg-slate-800/50 border-slate-700">
              <CardHeader>
                <CardTitle className="text-white flex items-center gap-2">
                  <Camera className="h-5 w-5" />
                  Recursos Multimedia
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {mediaAssets.map((asset, index) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-slate-900/50 rounded-lg">
                      <div>
                        <p className="text-white font-medium">{asset.type}</p>
                        <p className="text-sm text-gray-400">{asset.description}</p>
                        <div className="flex gap-2 mt-1">
                          {asset.formats.map((format, i) => (
                            <Badge key={i} variant="secondary" className="text-xs">
                              {format}
                            </Badge>
                          ))}
                        </div>
                      </div>
                      <Download className="h-5 w-5 text-gray-400" />
                    </div>
                  ))}
                </div>
                <Button className="w-full mt-4">
                  Descargar Kit Completo
                  <Download className="ml-2 h-4 w-4" />
                </Button>
              </CardContent>
            </Card>
            
            <Card className="bg-slate-800/50 border-slate-700">
              <CardHeader>
                <CardTitle className="text-white flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  Información Corporativa
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="p-4 bg-slate-900/50 rounded-lg">
                    <h4 className="font-semibold text-white mb-2">Fact Sheet</h4>
                    <p className="text-sm text-gray-400 mb-3">
                      Datos clave sobre StreamPlus España, historia, misión y visión.
                    </p>
                    <Button variant="outline" size="sm">
                      Descargar PDF
                    </Button>
                  </div>
                  
                  <div className="p-4 bg-slate-900/50 rounded-lg">
                    <h4 className="font-semibold text-white mb-2">Biografías Ejecutivos</h4>
                    <p className="text-sm text-gray-400 mb-3">
                      Perfiles completos del equipo directivo y fundadores.
                    </p>
                    <Button variant="outline" size="sm">
                      Descargar PDF
                    </Button>
                  </div>
                  
                  <div className="p-4 bg-slate-900/50 rounded-lg">
                    <h4 className="font-semibold text-white mb-2">Guía de Marca</h4>
                    <p className="text-sm text-gray-400 mb-3">
                      Manual de uso correcto de la marca StreamPlus.
                    </p>
                    <Button variant="outline" size="sm">
                      Descargar PDF
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Media Coverage */}
      <section className="py-16 border-t border-slate-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold text-white mb-8">StreamPlus en los Medios</h2>
          
          <div className="grid md:grid-cols-2 gap-6">
            {coverage.map((article, index) => (
              <Card key={index} className="bg-slate-800/50 border-slate-700">
                <CardContent className="p-6">
                  <div className="flex items-start justify-between">
                    <div>
                      <div className="flex items-center gap-3 mb-2">
                        <Newspaper className="h-4 w-4 text-gray-400" />
                        <span className="text-sm font-medium text-gray-400">{article.outlet}</span>
                        <span className="text-sm text-gray-500">• {article.date}</span>
                      </div>
                      <h3 className="text-lg font-medium text-white hover:text-blue-400 transition-colors cursor-pointer">
                        "{article.title}"
                      </h3>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Awards */}
      <section className="py-16 border-t border-slate-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold text-white mb-8">Premios y Reconocimientos</h2>
          
          <div className="grid md:grid-cols-3 gap-6">
            <Card className="bg-gradient-to-br from-yellow-900/20 to-orange-900/20 border-yellow-500/20">
              <CardHeader className="text-center">
                <Award className="h-16 w-16 text-yellow-500 mx-auto mb-4" />
                <CardTitle className="text-white">Mejor Plataforma de Streaming 2024</CardTitle>
                <CardDescription>Premios Tecnología España</CardDescription>
              </CardHeader>
            </Card>
            
            <Card className="bg-gradient-to-br from-blue-900/20 to-purple-900/20 border-blue-500/20">
              <CardHeader className="text-center">
                <TrendingUp className="h-16 w-16 text-blue-500 mx-auto mb-4" />
                <CardTitle className="text-white">Startup del Año 2023</CardTitle>
                <CardDescription>Tech Awards Europe</CardDescription>
              </CardHeader>
            </Card>
            
            <Card className="bg-gradient-to-br from-green-900/20 to-emerald-900/20 border-green-500/20">
              <CardHeader className="text-center">
                <Users className="h-16 w-16 text-green-500 mx-auto mb-4" />
                <CardTitle className="text-white">Elección del Público 2024</CardTitle>
                <CardDescription>Sports Tech Awards</CardDescription>
              </CardHeader>
            </Card>
          </div>
        </div>
      </section>

      {/* Contact Press */}
      <section className="py-20 border-t border-slate-800">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <Card className="bg-gradient-to-br from-indigo-900/20 to-blue-900/20 border-indigo-500/20">
            <CardHeader className="text-center">
              <CardTitle className="text-3xl text-white mb-4">
                Contacto de Prensa
              </CardTitle>
              <CardDescription className="text-lg">
                Para consultas de prensa, entrevistas o más información
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid md:grid-cols-2 gap-6 max-w-2xl mx-auto">
                <div className="text-center">
                  <Mail className="h-10 w-10 text-indigo-400 mx-auto mb-3" />
                  <p className="text-white font-medium mb-1">Email</p>
                  <a href="mailto:<EMAIL>" className="text-indigo-400 hover:text-indigo-300">
                    <EMAIL>
                  </a>
                </div>
                <div className="text-center">
                  <Phone className="h-10 w-10 text-indigo-400 mx-auto mb-3" />
                  <p className="text-white font-medium mb-1">Teléfono</p>
                  <p className="text-indigo-400">
                    +34 900 123 789
                  </p>
                </div>
              </div>
              
              <div className="mt-8 text-center">
                <p className="text-gray-300 mb-4">
                  Departamento de Comunicación disponible de lunes a viernes, 9:00 - 18:00 CET
                </p>
                <Button className="bg-indigo-600 hover:bg-indigo-700">
                  Solicitar Entrevista
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </section>

      <Footer />
    </div>
  )
}