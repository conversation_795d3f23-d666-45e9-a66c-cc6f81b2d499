'use client'

import { Foot<PERSON> } from '@/components/footer'
import { BackToHome } from '@/components/back-to-home'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { 
  CheckCircle, AlertCircle, XCircle, Activity,
  Wifi, Server, Database, Shield, Globe,
  Clock, TrendingUp, RefreshCw
} from 'lucide-react'
import { useState, useEffect } from 'react'

export default function StatusPage() {
  const [lastUpdate, setLastUpdate] = useState(new Date())

  useEffect(() => {
    const interval = setInterval(() => {
      setLastUpdate(new Date())
    }, 60000) // Actualizar cada minuto

    return () => clearInterval(interval)
  }, [])

  const services = [
    {
      name: 'Streaming de Video',
      status: 'operational',
      uptime: 99.98,
      latency: '12ms',
      icon: Wifi
    },
    {
      name: 'API Principal',
      status: 'operational',
      uptime: 99.99,
      latency: '8ms',
      icon: Server
    },
    {
      name: 'Base de Datos',
      status: 'operational',
      uptime: 100,
      latency: '3ms',
      icon: Database
    },
    {
      name: 'Autenticación',
      status: 'operational',
      uptime: 99.97,
      latency: '15ms',
      icon: Shield
    },
    {
      name: 'CDN Global',
      status: 'operational',
      uptime: 99.96,
      latency: '25ms',
      icon: Globe
    },
    {
      name: 'Datos Deportivos en Vivo',
      status: 'operational',
      uptime: 99.95,
      latency: '18ms',
      icon: Activity
    }
  ]

  const regions = [
    { name: 'Europa Occidental', status: 'operational', latency: '15ms' },
    { name: 'Europa Oriental', status: 'operational', latency: '28ms' },
    { name: 'América del Norte', status: 'operational', latency: '85ms' },
    { name: 'América del Sur', status: 'operational', latency: '120ms' },
    { name: 'Asia-Pacífico', status: 'operational', latency: '150ms' }
  ]

  const incidents = [
    {
      date: '2024-01-10',
      title: 'Mantenimiento programado completado',
      description: 'Actualización de infraestructura completada sin incidencias',
      severity: 'maintenance',
      duration: '2 horas'
    },
    {
      date: '2024-01-05',
      title: 'Degradación temporal del servicio',
      description: 'Latencia elevada en streaming debido a pico de tráfico',
      severity: 'minor',
      duration: '45 minutos'
    },
    {
      date: '2023-12-28',
      title: 'Actualización de seguridad',
      description: 'Parches de seguridad aplicados a todos los sistemas',
      severity: 'maintenance',
      duration: '1 hora'
    }
  ]

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'operational':
        return <CheckCircle className="h-5 w-5 text-green-500" />
      case 'degraded':
        return <AlertCircle className="h-5 w-5 text-yellow-500" />
      case 'outage':
        return <XCircle className="h-5 w-5 text-red-500" />
      default:
        return <Activity className="h-5 w-5 text-gray-500" />
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'operational':
        return <Badge className="bg-green-600 text-white">Operativo</Badge>
      case 'degraded':
        return <Badge className="bg-yellow-600 text-white">Degradado</Badge>
      case 'outage':
        return <Badge className="bg-red-600 text-white">Interrupción</Badge>
      default:
        return <Badge>Desconocido</Badge>
    }
  }

  const getSeverityBadge = (severity: string) => {
    switch (severity) {
      case 'maintenance':
        return <Badge className="bg-blue-600 text-white">Mantenimiento</Badge>
      case 'minor':
        return <Badge className="bg-yellow-600 text-white">Menor</Badge>
      case 'major':
        return <Badge className="bg-red-600 text-white">Mayor</Badge>
      default:
        return <Badge>Info</Badge>
    }
  }

  const allOperational = services.every(s => s.status === 'operational')

  return (
    <div className="min-h-screen bg-gradient-to-b from-slate-950 to-slate-900">
      {/* Hero Section */}
      <section className="py-16 border-b border-slate-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-8">
          <BackToHome />
            <Badge className="mb-4">Estado del Sistema</Badge>
            <h1 className="text-4xl font-bold text-white mb-4">Estado del Servicio</h1>
            <p className="text-gray-400">
              Última actualización: {lastUpdate.toLocaleTimeString('es-ES')}
              <RefreshCw className="inline-block ml-2 h-4 w-4 animate-spin" />
            </p>
          </div>

          {/* Overall Status */}
          <Card className={`${allOperational ? 'bg-green-900/20 border-green-500/20' : 'bg-yellow-900/20 border-yellow-500/20'}`}>
            <CardContent className="p-8 text-center">
              <div className="flex items-center justify-center gap-4 mb-4">
                {allOperational ? (
                  <CheckCircle className="h-12 w-12 text-green-500" />
                ) : (
                  <AlertCircle className="h-12 w-12 text-yellow-500" />
                )}
                <h2 className="text-3xl font-bold text-white">
                  {allOperational ? 'Todos los sistemas operativos' : 'Algunos sistemas afectados'}
                </h2>
              </div>
              <p className="text-gray-300">
                {allOperational 
                  ? 'StreamPlus España está funcionando con normalidad. No hay incidencias reportadas.'
                  : 'Estamos experimentando algunos problemas. Nuestro equipo está trabajando en ello.'}
              </p>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* Services Status */}
      <section className="py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-2xl font-bold text-white mb-8">Estado de Servicios</h2>
          <BackToHome />
          
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {services.map((service, index) => (
              <Card key={index} className="bg-slate-800/50 border-slate-700">
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <service.icon className="h-6 w-6 text-gray-400" />
                      <CardTitle className="text-white">{service.name}</CardTitle>
                    </div>
                    {getStatusIcon(service.status)}
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-400">Estado</span>
                      {getStatusBadge(service.status)}
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-400">Disponibilidad</span>
                      <span className="text-sm text-white font-medium">{service.uptime}%</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-400">Latencia</span>
                      <span className="text-sm text-white font-medium">{service.latency}</span>
                    </div>
                    <Progress value={service.uptime} className="h-2" />
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Regional Status */}
      <section className="py-12 border-t border-slate-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-2xl font-bold text-white mb-8">Estado por Región</h2>
          <BackToHome />
          
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
            {regions.map((region, index) => (
              <Card key={index} className="bg-slate-800/50 border-slate-700">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <Globe className="h-5 w-5 text-gray-400" />
                      <div>
                        <p className="text-white font-medium">{region.name}</p>
                        <p className="text-sm text-gray-400">Latencia: {region.latency}</p>
                      </div>
                    </div>
                    {getStatusIcon(region.status)}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Recent Incidents */}
      <section className="py-12 border-t border-slate-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-2xl font-bold text-white mb-8">Incidentes Recientes</h2>
          <BackToHome />
          
          <div className="space-y-4">
            {incidents.map((incident, index) => (
              <Card key={index} className="bg-slate-800/50 border-slate-700">
                <CardContent className="p-6">
                  <div className="flex items-start justify-between">
                    <div className="flex-grow">
                      <div className="flex items-center gap-3 mb-2">
                        <Clock className="h-4 w-4 text-gray-400" />
                        <span className="text-sm text-gray-400">{incident.date}</span>
                        {getSeverityBadge(incident.severity)}
                        <span className="text-sm text-gray-400">• Duración: {incident.duration}</span>
                      </div>
                      <h3 className="text-lg font-semibold text-white mb-1">{incident.title}</h3>
                      <p className="text-gray-300">{incident.description}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Metrics */}
      <section className="py-12 border-t border-slate-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-2xl font-bold text-white mb-8">Métricas del Sistema</h2>
          <BackToHome />
          
          <div className="grid md:grid-cols-4 gap-6">
            <Card className="bg-slate-800/50 border-slate-700">
              <CardContent className="p-6 text-center">
                <TrendingUp className="h-8 w-8 text-green-400 mx-auto mb-3" />
                <p className="text-3xl font-bold text-white">99.98%</p>
                <p className="text-sm text-gray-400">Disponibilidad General</p>
              </CardContent>
            </Card>
            
            <Card className="bg-slate-800/50 border-slate-700">
              <CardContent className="p-6 text-center">
                <Activity className="h-8 w-8 text-blue-400 mx-auto mb-3" />
                <p className="text-3xl font-bold text-white">18ms</p>
                <p className="text-sm text-gray-400">Latencia Media</p>
              </CardContent>
            </Card>
            
            <Card className="bg-slate-800/50 border-slate-700">
              <CardContent className="p-6 text-center">
                <Server className="h-8 w-8 text-purple-400 mx-auto mb-3" />
                <p className="text-3xl font-bold text-white">45</p>
                <p className="text-sm text-gray-400">Servidores Activos</p>
              </CardContent>
            </Card>
            
            <Card className="bg-slate-800/50 border-slate-700">
              <CardContent className="p-6 text-center">
                <Shield className="h-8 w-8 text-orange-400 mx-auto mb-3" />
                <p className="text-3xl font-bold text-white">0</p>
                <p className="text-sm text-gray-400">Incidentes de Seguridad</p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Subscribe to Updates */}
      <section className="py-16 border-t border-slate-800">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-2xl font-bold text-white mb-4">
          <BackToHome />
            Mantente Informado
          </h2>
          <p className="text-gray-300 mb-6">
            Suscríbete para recibir notificaciones sobre el estado del servicio y mantenimientos programados
          </p>
          <div className="flex gap-4 justify-center">
            <Badge variant="outline" className="text-gray-300">
              <Activity className="mr-2 h-4 w-4" />
              API Status: api.status.streamplus.es
            </Badge>
            <Badge variant="outline" className="text-gray-300">
              RSS Feed disponible
            </Badge>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  )
}