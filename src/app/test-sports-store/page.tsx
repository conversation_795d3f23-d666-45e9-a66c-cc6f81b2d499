'use client'

import { useEffect, useState } from 'react'
import { useSportsStore } from '@/stores/sports.store'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { sportsCacheService } from '@/services/cache/sports-cache.service'

export default function TestSportsStorePage() {
  const { 
    upcomingEvents, 
    liveFixtures,
    loadingUpcomingEvents,
    loadingLiveFixtures,
    fetchUpcomingEvents,
    fetchLiveFixtures 
  } = useSportsStore()
  
  const [logs, setLogs] = useState<string[]>([])
  
  // Interceptar console.log
  useEffect(() => {
    const originalLog = console.log
    const originalError = console.error
    
    console.log = (...args) => {
      originalLog(...args)
      setLogs(prev => [...prev, `[LOG] ${args.join(' ')}`])
    }
    
    console.error = (...args) => {
      originalError(...args)
      setLogs(prev => [...prev, `[ERROR] ${args.join(' ')}`])
    }
    
    return () => {
      console.log = originalLog
      console.error = originalError
    }
  }, [])
  
  const testUpcoming = async () => {
    setLogs(['🔄 Iniciando teste de fetchUpcomingEvents...'])
    await fetchUpcomingEvents()
  }
  
  const testLive = async () => {
    setLogs(['🔄 Iniciando teste de fetchLiveFixtures...'])
    await fetchLiveFixtures()
  }
  
  const clearCache = () => {
    setLogs(['🧹 Limpando cache...'])
    sportsCacheService.clearAll()
    setLogs(prev => [...prev, '✅ Cache limpo!'])
  }
  
  return (
    <div className="min-h-screen bg-black p-8">
      <h1 className="text-3xl font-bold text-white mb-8">Test Sports Store</h1>
      
      <div className="flex gap-4 mb-8">
        <Button 
          onClick={testUpcoming} 
          disabled={loadingUpcomingEvents}
        >
          {loadingUpcomingEvents ? 'Carregando...' : 'Testar Upcoming Events'}
        </Button>
        
        <Button 
          onClick={testLive} 
          disabled={loadingLiveFixtures}
        >
          {loadingLiveFixtures ? 'Carregando...' : 'Testar Live Fixtures'}
        </Button>
        
        <Button 
          onClick={clearCache}
          variant="outline"
        >
          Limpar Cache
        </Button>
      </div>
      
      <div className="grid grid-cols-2 gap-8">
        <Card className="p-4 bg-gray-900 border-gray-800">
          <h3 className="text-lg font-bold text-white mb-4">
            Upcoming Events ({upcomingEvents.length})
          </h3>
          <pre className="text-sm text-gray-300 overflow-auto max-h-[300px]">
            {JSON.stringify(upcomingEvents.slice(0, 3), null, 2)}
          </pre>
        </Card>
        
        <Card className="p-4 bg-gray-900 border-gray-800">
          <h3 className="text-lg font-bold text-white mb-4">
            Live Fixtures ({liveFixtures.length})
          </h3>
          <pre className="text-sm text-gray-300 overflow-auto max-h-[300px]">
            {JSON.stringify(liveFixtures.slice(0, 3), null, 2)}
          </pre>
        </Card>
      </div>
      
      <Card className="mt-8 p-4 bg-gray-900 border-gray-800">
        <h3 className="text-lg font-bold text-white mb-4">Console Logs</h3>
        <div className="space-y-1 max-h-[400px] overflow-auto">
          {logs.map((log, i) => (
            <div 
              key={i} 
              className={`text-sm font-mono ${
                log.includes('[ERROR]') ? 'text-red-400' : 'text-gray-300'
              }`}
            >
              {log}
            </div>
          ))}
        </div>
      </Card>
    </div>
  )
}