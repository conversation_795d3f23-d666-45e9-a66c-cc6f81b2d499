'use client'

import { Footer } from '@/components/footer'
import { BackToHome } from '@/components/back-to-home'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { 
  Briefcase, MapPin, Clock, Users, 
  Code, Palette, BarChart, Headphones,
  Heart, Rocket, Trophy, Star
} from 'lucide-react'
import Link from 'next/link'

export default function CareersPage() {
  const values = [
    {
      icon: Heart,
      title: 'Pasión',
      description: 'Amamos lo que hacemos y se nota en cada línea de código'
    },
    {
      icon: Rocket,
      title: 'Innovación',
      description: 'Siempre buscando la próxima gran idea'
    },
    {
      icon: Trophy,
      title: 'Excelencia',
      description: 'Nos conformamos solo con lo mejor'
    },
    {
      icon: Users,
      title: 'Equipo',
      description: 'Juntos llegamos más lejos'
    }
  ]

  const benefits = [
    '🏥 Seguro médico premium',
    '🏖️ 25 días de vacaciones + festivos',
    '💻 Equipo de última generación',
    '🏠 Trabajo remoto flexible',
    '📚 Presupuesto de formación',
    '🏋️ Gimnasio y wellness',
    '🍽️ Comida subsidiada',
    '🎮 Zona de juegos y relax'
  ]

  const openPositions = [
    {
      category: 'Ingeniería',
      icon: Code,
      color: 'text-blue-400',
      positions: [
        {
          title: 'Senior Backend Engineer',
          location: 'Madrid / Remoto',
          type: 'Tiempo completo',
          department: 'Ingeniería',
          level: 'Senior'
        },
        {
          title: 'Frontend Developer React',
          location: 'Barcelona / Remoto',
          type: 'Tiempo completo',
          department: 'Ingeniería',
          level: 'Mid-level'
        },
        {
          title: 'DevOps Engineer',
          location: 'Remoto',
          type: 'Tiempo completo',
          department: 'Infraestructura',
          level: 'Senior'
        }
      ]
    },
    {
      category: 'Diseño',
      icon: Palette,
      color: 'text-purple-400',
      positions: [
        {
          title: 'UX/UI Designer',
          location: 'Madrid',
          type: 'Tiempo completo',
          department: 'Diseño',
          level: 'Mid-level'
        },
        {
          title: 'Motion Designer',
          location: 'Barcelona / Remoto',
          type: 'Tiempo completo',
          department: 'Diseño',
          level: 'Junior'
        }
      ]
    },
    {
      category: 'Datos',
      icon: BarChart,
      color: 'text-green-400',
      positions: [
        {
          title: 'Data Scientist',
          location: 'Madrid',
          type: 'Tiempo completo',
          department: 'Datos',
          level: 'Senior'
        },
        {
          title: 'Analytics Engineer',
          location: 'Remoto',
          type: 'Tiempo completo',
          department: 'Datos',
          level: 'Mid-level'
        }
      ]
    },
    {
      category: 'Soporte',
      icon: Headphones,
      color: 'text-orange-400',
      positions: [
        {
          title: 'Customer Success Manager',
          location: 'Madrid',
          type: 'Tiempo completo',
          department: 'Soporte',
          level: 'Mid-level'
        }
      ]
    }
  ]

  return (
    <div className="min-h-screen bg-gradient-to-b from-slate-950 to-slate-900">
      {/* Hero Section */}
      <section className="relative py-20 overflow-hidden">
        <div className="absolute inset-0">
          <div className="absolute inset-0 bg-gradient-to-r from-blue-900/20 to-purple-900/20" />
        </div>
        
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <BackToHome align="left" />
          <Badge className="mb-4 bg-blue-600 text-white">Carreras</Badge>
          <h1 className="text-5xl md:text-6xl font-bold text-white mb-6">
            Únete al equipo que está
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-purple-400"> revolucionando el streaming</span>
          </h1>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto mb-8">
            En NewSpports estamos construyendo el futuro del entretenimiento deportivo y de noticias. 
            Si compartes nuestra pasión por la tecnología y el deporte, queremos conocerte.
          </p>
          <Button size="lg" className="bg-blue-600 hover:bg-blue-700">
            Ver Posiciones Abiertas
          </Button>
        </div>
      </section>

      {/* Company Values */}
      <section className="py-16 border-t border-slate-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-white mb-4">Nuestros Valores</h2>
            <p className="text-gray-400 max-w-2xl mx-auto">
              Los principios que guían nuestro trabajo diario
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {values.map((value, index) => (
              <Card key={index} className="bg-slate-800/50 border-slate-700 text-center">
                <CardContent className="p-6">
                  <value.icon className="h-12 w-12 text-blue-400 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-white mb-2">{value.title}</h3>
                  <p className="text-gray-400">{value.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Benefits */}
      <section className="py-16 border-t border-slate-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid md:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl font-bold text-white mb-6">Beneficios que Importan</h2>
              <p className="text-gray-300 mb-8">
                Creemos en cuidar a nuestro equipo. Por eso ofrecemos un paquete de beneficios 
                completo que va más allá del salario competitivo.
              </p>
              <div className="grid grid-cols-2 gap-4">
                {benefits.map((benefit, index) => (
                  <div key={index} className="flex items-center gap-2">
                    <span className="text-lg">{benefit}</span>
                  </div>
                ))}
              </div>
            </div>
            <div className="relative">
              <Card className="bg-gradient-to-br from-blue-900/20 to-purple-900/20 border-blue-500/20">
                <CardContent className="p-8">
                  <Star className="h-12 w-12 text-yellow-400 mb-4" />
                  <h3 className="text-2xl font-bold text-white mb-2">
                    Mejor Lugar para Trabajar 2024
                  </h3>
                  <p className="text-gray-300">
                    Reconocidos como una de las mejores empresas tecnológicas para trabajar en España
                  </p>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Open Positions */}
      <section className="py-16 border-t border-slate-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-white mb-4">Posiciones Abiertas</h2>
            <p className="text-gray-400 max-w-2xl mx-auto">
              Encuentra tu próximo desafío profesional con nosotros
            </p>
          </div>
          
          <div className="space-y-8">
            {openPositions.map((category, categoryIndex) => (
              <div key={categoryIndex}>
                <div className="flex items-center gap-3 mb-6">
                  <category.icon className={`h-6 w-6 ${category.color}`} />
                  <h3 className="text-xl font-semibold text-white">{category.category}</h3>
                  <Badge variant="secondary">{category.positions.length} posiciones</Badge>
                </div>
                
                <div className="grid gap-4">
                  {category.positions.map((position, posIndex) => (
                    <Card 
                      key={posIndex} 
                      className="bg-slate-800/50 border-slate-700 hover:border-slate-600 transition-all cursor-pointer"
                    >
                      <CardHeader>
                        <div className="flex items-start justify-between">
                          <div>
                            <CardTitle className="text-white hover:text-blue-400 transition-colors">
                              {position.title}
                            </CardTitle>
                            <CardDescription className="mt-2">
                              <div className="flex flex-wrap gap-4 text-sm">
                                <span className="flex items-center gap-1">
                                  <MapPin className="h-4 w-4" />
                                  {position.location}
                                </span>
                                <span className="flex items-center gap-1">
                                  <Briefcase className="h-4 w-4" />
                                  {position.department}
                                </span>
                                <span className="flex items-center gap-1">
                                  <Clock className="h-4 w-4" />
                                  {position.type}
                                </span>
                              </div>
                            </CardDescription>
                          </div>
                          <Badge className="bg-blue-600 text-white">
                            {position.level}
                          </Badge>
                        </div>
                      </CardHeader>
                    </Card>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 border-t border-slate-800">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-4xl font-bold text-white mb-6">
            ¿No encuentras tu posición ideal?
          </h2>
          <p className="text-xl text-gray-300 mb-8">
            Siempre estamos buscando talento excepcional. Envíanos tu CV y cuéntanos 
            cómo puedes contribuir a nuestra misión.
          </p>
          <div className="flex gap-4 justify-center">
            <Button size="lg" variant="outline">
              Enviar Candidatura Espontánea
            </Button>
          </div>
          <p className="text-sm text-gray-400 mt-4">
            <EMAIL>
          </p>
        </div>
      </section>

      <Footer />
    </div>
  )
}