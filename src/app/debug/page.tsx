'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { Switch } from '@/components/ui/switch'
import { logger } from '@/lib/logger'
import { DEBUG } from '@/config/debug'
import { checkEnvironment } from '@/lib/env-check'

export default function DebugPage() {
  const [debugEnabled, setDebugEnabled] = useState(DEBUG)
  const [logs, setLogs] = useState<string[]>([])
  const [envData, setEnvData] = useState<any>(null)
  
  useEffect(() => {
    // Verificar ambiente
    const env = checkEnvironment()
    setEnvData(env)
    
    // Carregar logs
    updateLogs()
    
    // Atualizar logs a cada 2 segundos
    const interval = setInterval(updateLogs, 2000)
    return () => clearInterval(interval)
  }, [])
  
  const updateLogs = () => {
    const history = logger.getHistory()
    const formattedLogs = history.slice(-50).map(log => 
      `[${log.timestamp}] [${log.level}] [${log.tag}] ${log.message} ${log.data ? JSON.stringify(log.data) : ''}`
    )
    setLogs(formattedLogs)
  }
  
  const toggleDebug = () => {
    const newValue = !debugEnabled
    setDebugEnabled(newValue)
    
    // Salvar no localStorage
    if (typeof window !== 'undefined') {
      localStorage.setItem('DEBUG', String(newValue))
      // Recarregar página para aplicar
      window.location.reload()
    }
  }
  
  const clearLogs = () => {
    logger.clear()
    setLogs([])
  }
  
  const exportLogs = () => {
    const logContent = logger.export()
    const blob = new Blob([logContent], { type: 'text/plain' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `streamplus-logs-${new Date().toISOString()}.txt`
    a.click()
  }
  
  const testAPIs = async () => {
    logger.info('debug', '=== Starting API Test ===')
    
    // Test IPTV
    try {
      logger.info('debug', 'Testing IPTV API...')
      const res = await fetch('/api/iptv-simple')
      const data = await res.json()
      logger.info('debug', 'IPTV API result', { 
        success: data.success, 
        total: data.total 
      })
    } catch (error: any) {
      logger.error('debug', 'IPTV API failed', { error: error.message })
    }
    
    // Test Sports
    try {
      logger.info('debug', 'Testing Sports API...')
      const res = await fetch('/api/sports/live-events')
      const data = await res.json()
      logger.info('debug', 'Sports API result', { 
        success: data.success, 
        total: data.total 
      })
    } catch (error: any) {
      logger.error('debug', 'Sports API failed', { error: error.message })
    }
    
    logger.info('debug', '=== API Test Complete ===')
    updateLogs()
  }
  
  return (
    <div className="min-h-screen bg-black p-8">
      <h1 className="text-3xl font-bold text-white mb-8">
        🔍 Debug Console
      </h1>
      
      {/* Debug Toggle */}
      <Card className="p-6 bg-gray-900 border-gray-800 mb-6">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-xl font-bold text-white">Debug Mode</h2>
            <p className="text-sm text-gray-400 mt-1">
              Enable detailed logging throughout the application
            </p>
          </div>
          <Switch
            checked={debugEnabled}
            onCheckedChange={toggleDebug}
          />
        </div>
        <div className="mt-4 text-sm text-gray-500">
          Current: {debugEnabled ? '✅ ENABLED' : '❌ DISABLED'}
        </div>
      </Card>
      
      {/* Environment Status */}
      <Card className="p-6 bg-gray-900 border-gray-800 mb-6">
        <h2 className="text-xl font-bold text-white mb-4">Environment Status</h2>
        {envData && (
          <div className="space-y-2">
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-400">Node Env:</span>
                <span className="text-white ml-2">{envData.envVars.NODE_ENV}</span>
              </div>
              <div>
                <span className="text-gray-400">App URL:</span>
                <span className="text-white ml-2">{envData.envVars.NEXT_PUBLIC_APP_URL}</span>
              </div>
            </div>
            
            <div className="mt-4">
              <h3 className="text-sm font-semibold text-gray-300 mb-2">API Status:</h3>
              <div className="grid grid-cols-2 gap-2 text-sm">
                {Object.entries(envData.criticalAPIs).map(([key, value]) => (
                  <div key={key} className="flex items-center gap-2">
                    <span className={value ? 'text-green-500' : 'text-red-500'}>
                      {value ? '✅' : '❌'}
                    </span>
                    <span className="text-gray-400">{key}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}
      </Card>
      
      {/* Actions */}
      <Card className="p-6 bg-gray-900 border-gray-800 mb-6">
        <h2 className="text-xl font-bold text-white mb-4">Actions</h2>
        <div className="flex gap-4">
          <Button onClick={testAPIs} variant="outline">
            Test APIs
          </Button>
          <Button onClick={clearLogs} variant="outline">
            Clear Logs
          </Button>
          <Button onClick={exportLogs} variant="outline">
            Export Logs
          </Button>
          <Button onClick={updateLogs} variant="outline">
            Refresh
          </Button>
        </div>
      </Card>
      
      {/* Logs */}
      <Card className="p-6 bg-gray-900 border-gray-800">
        <h2 className="text-xl font-bold text-white mb-4">
          Recent Logs ({logs.length})
        </h2>
        <div className="bg-black rounded p-4 h-[600px] overflow-auto font-mono text-xs">
          {logs.length === 0 ? (
            <div className="text-gray-500">No logs yet...</div>
          ) : (
            logs.map((log, i) => (
              <div 
                key={i} 
                className={`mb-1 ${
                  log.includes('[ERROR]') ? 'text-red-400' :
                  log.includes('[WARN]') ? 'text-yellow-400' :
                  log.includes('[INFO]') ? 'text-blue-400' :
                  log.includes('[DEBUG]') ? 'text-green-400' :
                  'text-gray-300'
                }`}
              >
                {log}
              </div>
            ))
          )}
        </div>
      </Card>
      
      {/* Instructions */}
      <div className="mt-8 text-sm text-gray-500">
        <h3 className="font-semibold mb-2">Debug Instructions:</h3>
        <ul className="list-disc list-inside space-y-1">
          <li>Enable debug mode to see detailed logs</li>
          <li>Open browser console (F12) for additional output</li>
          <li>Access logger globally: <code>window.__logger</code></li>
          <li>Filter by tag: <code>__logger.getHistory('player')</code></li>
          <li>Export logs for sharing or analysis</li>
        </ul>
      </div>
    </div>
  )
}