'use client'

import { Foot<PERSON> } from '@/components/footer'
import { BackToHome } from '@/components/back-to-home'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { 
  Smartphone, Download, Star, Shield, Zap, 
  Wifi, Trophy, Clock, Users, Check,
  QrCode, Copy, PlayCircle
} from 'lucide-react'
import { QRCodeSVG } from 'qrcode.react'
import { useState, useEffect } from 'react'
import { toast } from 'sonner'
import Link from 'next/link'

interface MobileAppConfig {
  android_url: string
  ios_url: string
  qr_code_url: string
  tutorial_video_url: string
  tutorial_video_file: string | null
  android_badge_url: string
  ios_badge_url: string
  ios_available: boolean
  android_available: boolean
}

export default function MobileAppPage() {
  const [copied, setCopied] = useState(false)
  const [config, setConfig] = useState<MobileAppConfig | null>(null)
  const [loading, setLoading] = useState(true)
  
  useEffect(() => {
    loadMobileConfig()
  }, [])
  
  const loadMobileConfig = async () => {
    try {
      const response = await fetch('/api/admin/mobile-config')
      if (response.ok) {
        const data = await response.json()
        setConfig(data)
      }
    } catch (error) {
      console.error('Error loading mobile config:', error)
    } finally {
      setLoading(false)
    }
  }
  
  // Valores padrão enquanto carrega ou se houver erro
  const androidUrl = config?.android_url || process.env.NEXT_PUBLIC_ANDROID_APP_URL || 'https://play.google.com/store/apps/details?id=com.streamplus.espana'
  const iosUrl = config?.ios_url || process.env.NEXT_PUBLIC_IOS_APP_URL || '#'
  const qrCodeUrl = config?.qr_code_url || androidUrl
  
  const handleCopyLink = async (url: string) => {
    try {
      await navigator.clipboard.writeText(url)
      setCopied(true)
      toast.success('Enlace copiado al portapapeles')
      setTimeout(() => setCopied(false), 3000)
    } catch (error) {
      toast.error('Error al copiar el enlace')
    }
  }

  const features = [
    {
      icon: Wifi,
      title: 'Descarga Offline',
      description: 'Descarga eventos para ver sin conexión'
    },
    {
      icon: Trophy,
      title: 'Notificaciones en Vivo',
      description: 'Alertas de tus equipos y eventos favoritos'
    },
    {
      icon: Clock,
      title: 'Continúa Donde lo Dejaste',
      description: 'Sincronización perfecta entre dispositivos'
    },
    {
      icon: Shield,
      title: 'Calidad Adaptativa',
      description: 'Streaming optimizado para móviles'
    }
  ]

  const platforms = [
    {
      name: 'iOS',
      requirements: ['iOS 14.0 o superior', 'iPhone 6s o posterior', 'iPad (6ª gen) o posterior'],
      storeUrl: iosUrl,
      storeBadge: config?.ios_badge_url || 'https://developer.apple.com/assets/elements/badges/download-on-the-app-store.svg',
      available: config?.ios_available || false
    },
    {
      name: 'Android',
      requirements: ['Android 7.0 o superior', '2GB RAM mínimo', 'Soporte ARMv7 o superior'],
      storeUrl: androidUrl,
      storeBadge: config?.android_badge_url || 'https://play.google.com/intl/en_us/badges/static/images/badges/es_badge_web_generic.png',
      available: config?.android_available !== false // Por padrão é true
    }
  ]

  const benefits = [
    '5 minutos de prueba gratis',
    'Sin anuncios',
    'Calidad hasta 4K',
    'Multi-idioma',
    'Chromecast y AirPlay',
    'Picture-in-Picture'
  ]

  return (
    <div className="min-h-screen bg-gradient-to-b from-slate-950 to-slate-900">
      {/* Back Button */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-8">
        <BackToHome align="left" />
      </div>

      {/* Hero Section */}
      <section className="relative py-20 overflow-hidden">
        <div className="absolute inset-0">
          <div className="absolute inset-0 bg-gradient-to-r from-purple-900/20 to-pink-900/20" />
        </div>
        
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-12 items-start">
            <div className="space-y-6">
              <div>
                <Badge className="mb-4 bg-purple-600 text-white">App Móvil</Badge>
                <h1 className="text-5xl font-bold text-white mb-6">
                  StreamPlus en tu
                  <span className="text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-pink-400"> bolsillo</span>
                </h1>
                <p className="text-xl text-gray-300 mb-8">
                  Disfruta de todo el contenido deportivo donde quieras. 
                  Descarga la app y obtén <span className="text-green-400 font-semibold">30 días gratis</span> al instalar y verificar tu dispositivo.
                </p>
                
                <div className="flex flex-col sm:flex-row gap-4 mb-8">
                  {platforms.map((platform) => (
                    <a 
                      key={platform.name}
                      href={platform.available ? platform.storeUrl : '#'} 
                      className={`inline-block ${!platform.available ? 'opacity-50 cursor-not-allowed' : ''}`}
                    >
                      <img 
                        src={platform.storeBadge} 
                        alt={`Descargar para ${platform.name}`} 
                        className="h-14"
                      />
                    </a>
                  ))}
                </div>
                
                <div className="flex items-center gap-6 text-sm text-gray-400">
                  <div className="flex items-center gap-1">
                    <Star className="h-4 w-4 text-yellow-400 fill-current" />
                    <span className="text-white font-medium">4.8</span>
                    <span>• 50K+ reseñas</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Download className="h-4 w-4" />
                    <span>1M+ descargas</span>
                  </div>
                </div>
              </div>
              
              {/* Tutorial Card - Embaixo do conteúdo esquerdo */}
              <div className="relative">
                <div className="absolute inset-0 bg-gradient-to-r from-purple-600 to-pink-600 rounded-lg blur opacity-25 animate-pulse" />
                <Card className="relative bg-gradient-to-r from-purple-900/90 to-pink-900/90 border-purple-500/50 backdrop-blur-sm">
                  <CardContent className="p-6">
                    <div className="flex items-center gap-6">
                      <div className="flex-shrink-0">
                        <div className="relative">
                          <div className="absolute inset-0 bg-white rounded-full blur-xl opacity-30 animate-ping" />
                          <PlayCircle className="relative h-16 w-16 text-white" />
                        </div>
                      </div>
                      <div className="flex-grow">
                        <h3 className="text-xl font-bold text-white mb-1">
                          ¿Primera vez instalando?
                        </h3>
                        <p className="text-purple-200">
                          Mira nuestro tutorial paso a paso
                        </p>
                      </div>
                      <div className="flex-shrink-0">
                        {(config?.tutorial_video_url || config?.tutorial_video_file) ? (
                          <Link href="/tutorial">
                            <Button className="bg-white text-purple-700 hover:bg-purple-100 font-semibold">
                              <PlayCircle className="mr-2 h-4 w-4" />
                              Ver Tutorial
                            </Button>
                          </Link>
                        ) : (
                          <Button disabled className="bg-white/20 text-white">
                            <PlayCircle className="mr-2 h-4 w-4" />
                            No Configurado
                          </Button>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
            
            <div className="flex justify-center">
              <div className="space-y-6">
                {/* QR Code Card */}
                <Card className="bg-slate-800/50 border-slate-700">
                  <CardHeader>
                    <CardTitle className="text-white flex items-center gap-2">
                      <QrCode className="h-5 w-5" />
                      Escanea para Descargar
                    </CardTitle>
                    <CardDescription>
                      Disponible para Android - iOS próximamente
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="bg-white p-6 rounded-lg mb-4">
                      <QRCodeSVG 
                        value={qrCodeUrl}
                        size={200}
                        level="H"
                        includeMargin
                      />
                    </div>
                    <Button 
                      variant="outline" 
                      className="w-full mb-4"
                      onClick={() => handleCopyLink(qrCodeUrl)}
                    >
                      {copied ? (
                        <>
                          <Check className="mr-2 h-4 w-4" />
                          Enlace Copiado
                        </>
                      ) : (
                        <>
                          <Copy className="mr-2 h-4 w-4" />
                          Copiar Enlace
                        </>
                      )}
                    </Button>
                    
                    <div className="space-y-3">
                      <p className="text-sm text-gray-400 text-center">O descarga directamente:</p>
                      
                      <a 
                        href={androidUrl}
                        className="block w-full"
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        <Button className="w-full bg-green-600 hover:bg-green-700">
                          <Download className="mr-2 h-4 w-4" />
                          Descargar para Android
                        </Button>
                      </a>
                      
                      <Button 
                        className="w-full" 
                        variant="outline"
                        disabled
                      >
                        <Download className="mr-2 h-4 w-4" />
                        iOS - Próximamente
                      </Button>
                      
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features */}
      <section className="py-16 border-t border-slate-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-white mb-4">
              Características Exclusivas de la App
            </h2>
            <p className="text-gray-400 max-w-2xl mx-auto">
              Diseñada especialmente para la mejor experiencia móvil
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {features.map((feature, index) => (
              <Card key={index} className="bg-slate-800/50 border-slate-700">
                <CardHeader>
                  <feature.icon className="h-10 w-10 text-purple-400 mb-4" />
                  <CardTitle className="text-white">{feature.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-gray-300">
                    {feature.description}
                  </CardDescription>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Platform Requirements */}
      <section className="py-16 border-t border-slate-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold text-white mb-12 text-center">
            Disponible en tus Dispositivos Favoritos
          </h2>
          
          <div className="grid md:grid-cols-2 gap-8">
            {platforms.map((platform, index) => (
              <Card 
                key={index} 
                className={`bg-slate-800/50 border-slate-700 ${!platform.available ? 'opacity-60' : ''}`}
              >
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-white text-2xl">{platform.name}</CardTitle>
                    <Smartphone className="h-8 w-8 text-gray-400" />
                  </div>
                  {!platform.available && (
                    <Badge className="bg-orange-600 text-white">Próximamente</Badge>
                  )}
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <p className="text-sm font-semibold text-gray-400 mb-2">Requisitos:</p>
                      <ul className="space-y-1">
                        {platform.requirements.map((req, i) => (
                          <li key={i} className="flex items-center gap-2 text-gray-300">
                            <Check className="h-4 w-4 text-green-400" />
                            {req}
                          </li>
                        ))}
                      </ul>
                    </div>
                    {platform.available && (
                      <a href={platform.storeUrl} className="block">
                        <img 
                          src={platform.storeBadge} 
                          alt={`Descargar para ${platform.name}`}
                          className="h-12"
                        />
                      </a>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Benefits */}
      <section className="py-16 border-t border-slate-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl font-bold text-white mb-6">
                Todo lo que Amas, Optimizado para Móvil
              </h2>
              <p className="text-gray-300 mb-8">
                La app de StreamPlus España ha sido diseñada desde cero para ofrecerte 
                la mejor experiencia en dispositivos móviles, con todas las características 
                que necesitas.
              </p>
              
              <div className="grid grid-cols-2 gap-4">
                {benefits.map((benefit, index) => (
                  <div key={index} className="flex items-center gap-2">
                    <Zap className="h-5 w-5 text-purple-400" />
                    <span className="text-gray-300">{benefit}</span>
                  </div>
                ))}
              </div>
              
              <div className="mt-8 p-6 bg-purple-900/20 rounded-lg border border-purple-500/20">
                <h3 className="text-lg font-semibold text-white mb-2">
                  🎁 Oferta Exclusiva App
                </h3>
                <p className="text-gray-300">
                  Descarga la app, instálala y verifica tu dispositivo para obtener 
                  <span className="text-purple-400 font-semibold"> 30 días completamente gratis</span>. 
                  ¡Sin tarjeta de crédito!
                </p>
              </div>
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <Card className="bg-gradient-to-br from-purple-900/20 to-pink-900/20 border-purple-500/20">
                <CardContent className="p-6 text-center">
                  <Users className="h-12 w-12 text-purple-400 mx-auto mb-3" />
                  <p className="text-2xl font-bold text-white">5</p>
                  <p className="text-sm text-gray-400">Perfiles por cuenta</p>
                </CardContent>
              </Card>
              
              <Card className="bg-gradient-to-br from-blue-900/20 to-purple-900/20 border-blue-500/20">
                <CardContent className="p-6 text-center">
                  <Download className="h-12 w-12 text-blue-400 mx-auto mb-3" />
                  <p className="text-2xl font-bold text-white">∞</p>
                  <p className="text-sm text-gray-400">Descargas offline</p>
                </CardContent>
              </Card>
              
              <Card className="bg-gradient-to-br from-green-900/20 to-teal-900/20 border-green-500/20">
                <CardContent className="p-6 text-center">
                  <Shield className="h-12 w-12 text-green-400 mx-auto mb-3" />
                  <p className="text-2xl font-bold text-white">100%</p>
                  <p className="text-sm text-gray-400">Seguro y privado</p>
                </CardContent>
              </Card>
              
              <Card className="bg-gradient-to-br from-orange-900/20 to-red-900/20 border-orange-500/20">
                <CardContent className="p-6 text-center">
                  <Star className="h-12 w-12 text-orange-400 mx-auto mb-3" />
                  <p className="text-2xl font-bold text-white">4.8</p>
                  <p className="text-sm text-gray-400">Calificación</p>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 border-t border-slate-800">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-4xl font-bold text-white mb-6">
            Descarga Ahora y Empieza a Disfrutar
          </h2>
          <p className="text-xl text-gray-300 mb-8">
            Únete a más de 1 millón de usuarios que ya disfrutan de StreamPlus 
            en sus dispositivos móviles.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8">
            {platforms.map((platform) => (
              <a 
                key={platform.name}
                href={platform.available ? platform.storeUrl : '#'} 
                className={`inline-block ${!platform.available ? 'opacity-50 cursor-not-allowed' : ''}`}
              >
                <img 
                  src={platform.storeBadge} 
                  alt={`Descargar para ${platform.name}`} 
                  className="h-16"
                />
              </a>
            ))}
          </div>
          
          <p className="text-sm text-gray-400">
            También disponible en tablets y dispositivos compatibles
          </p>
        </div>
      </section>

      <Footer />
    </div>
  )
}