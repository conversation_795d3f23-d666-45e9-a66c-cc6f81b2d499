'use client'

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { useAuth } from '@/hooks/use-auth'
import { useSubscription } from '@/hooks/use-subscription'
import { Loader2, CheckCircle, XCircle, AlertCircle } from 'lucide-react'
import { toast } from 'sonner'

export default function TestSubscriptionPage() {
  const { user } = useAuth()
  const subscription = useSubscription()
  const [loading, setLoading] = useState(false)
  const [debugInfo, setDebugInfo] = useState<any>(null)
  const [activateResult, setActivateResult] = useState<any>(null)

  const checkDebugInfo = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/debug/subscription-check')
      const data = await response.json()
      setDebugInfo(data)
      console.log('[TEST-PAGE] Debug info:', data)
    } catch (error) {
      console.error('[TEST-PAGE] Error:', error)
      toast.error('Erro ao verificar informações')
    }
    setLoading(false)
  }

  const activatePremium = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/test/activate-premium', { method: 'POST' })
      const data = await response.json()
      setActivateResult(data)
      console.log('[TEST-PAGE] Activate result:', data)
      
      if (data.success) {
        toast.success('Premium ativado com sucesso!')
        // Recarregar status da assinatura
        subscription.refresh()
        // Verificar debug info novamente
        await checkDebugInfo()
      } else {
        toast.error('Erro ao ativar premium')
      }
    } catch (error) {
      console.error('[TEST-PAGE] Error:', error)
      toast.error('Erro ao ativar premium')
    }
    setLoading(false)
  }

  const goToStripeCheckout = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/stripe/checkout', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ priceId: process.env.NEXT_PUBLIC_STRIPE_PRICE_ID })
      })
      
      const data = await response.json()
      if (data.url) {
        window.location.href = data.url
      } else {
        toast.error('Erro ao criar sessão de checkout')
      }
    } catch (error) {
      console.error('[TEST-PAGE] Error:', error)
      toast.error('Erro ao criar checkout')
    }
    setLoading(false)
  }

  return (
    <div className="min-h-screen bg-slate-950 p-8">
      <div className="max-w-4xl mx-auto space-y-6">
        <h1 className="text-3xl font-bold text-white mb-8">Teste de Assinatura</h1>
        
        {/* Status Atual */}
        <Card className="bg-slate-800 border-slate-700">
          <CardHeader>
            <CardTitle className="text-white">Status Atual</CardTitle>
            <CardDescription>Informações da assinatura em tempo real</CardDescription>
          </CardHeader>
          <CardContent className="space-y-2 text-sm">
            <div className="flex items-center gap-2">
              <span className="text-gray-400">Usuário:</span>
              <span className="text-white">{user?.primaryEmail || 'Não autenticado'}</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-gray-400">ID:</span>
              <span className="text-white font-mono text-xs">{user?.id || '-'}</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-gray-400">Tem Acesso:</span>
              {subscription.hasAccess ? (
                <CheckCircle className="h-4 w-4 text-green-500" />
              ) : (
                <XCircle className="h-4 w-4 text-red-500" />
              )}
              <span className={subscription.hasAccess ? 'text-green-500' : 'text-red-500'}>
                {subscription.hasAccess ? 'Sim' : 'Não'}
              </span>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-gray-400">Assinatura Ativa:</span>
              <span className={subscription.isSubscribed ? 'text-green-500' : 'text-gray-500'}>
                {subscription.isSubscribed ? 'Sim' : 'Não'}
              </span>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-gray-400">Em Trial:</span>
              <span className={subscription.isInTrial ? 'text-yellow-500' : 'text-gray-500'}>
                {subscription.isInTrial ? 'Sim' : 'Não'}
              </span>
            </div>
            {subscription.subscriptionEndsAt && (
              <div className="flex items-center gap-2">
                <span className="text-gray-400">Expira em:</span>
                <span className="text-white">
                  {new Date(subscription.subscriptionEndsAt).toLocaleDateString('pt-BR')}
                </span>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Ações de Teste */}
        <Card className="bg-slate-800 border-slate-700">
          <CardHeader>
            <CardTitle className="text-white">Ações de Teste</CardTitle>
            <CardDescription>Ferramentas para testar o fluxo de assinatura</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex gap-4">
              <Button
                onClick={checkDebugInfo}
                disabled={loading}
                variant="outline"
                className="flex-1"
              >
                {loading ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <AlertCircle className="mr-2 h-4 w-4" />}
                Verificar Debug Info
              </Button>
              
              <Button
                onClick={activatePremium}
                disabled={loading}
                className="flex-1 bg-green-600 hover:bg-green-700"
              >
                {loading ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <CheckCircle className="mr-2 h-4 w-4" />}
                Ativar Premium (Teste)
              </Button>
              
              <Button
                onClick={goToStripeCheckout}
                disabled={loading}
                className="flex-1 bg-primary hover:bg-primary/80"
              >
                {loading ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
                Ir para Checkout Stripe
              </Button>
            </div>
            
            <div className="text-xs text-gray-500">
              <p>• "Verificar Debug Info" - Mostra detalhes completos do status</p>
              <p>• "Ativar Premium (Teste)" - Simula ativação de premium por 30 dias</p>
              <p>• "Ir para Checkout Stripe" - Fluxo real de pagamento</p>
            </div>
          </CardContent>
        </Card>

        {/* Debug Info */}
        {debugInfo && (
          <Card className="bg-slate-800 border-slate-700">
            <CardHeader>
              <CardTitle className="text-white">Debug Info</CardTitle>
            </CardHeader>
            <CardContent>
              <pre className="text-xs text-gray-300 overflow-auto p-4 bg-slate-900 rounded">
                {JSON.stringify(debugInfo, null, 2)}
              </pre>
            </CardContent>
          </Card>
        )}

        {/* Resultado da Ativação */}
        {activateResult && (
          <Card className="bg-slate-800 border-slate-700">
            <CardHeader>
              <CardTitle className="text-white">Resultado da Ativação</CardTitle>
            </CardHeader>
            <CardContent>
              <pre className="text-xs text-gray-300 overflow-auto p-4 bg-slate-900 rounded">
                {JSON.stringify(activateResult, null, 2)}
              </pre>
            </CardContent>
          </Card>
        )}

        {/* Fluxo Explicado */}
        <Card className="bg-slate-800 border-slate-700">
          <CardHeader>
            <CardTitle className="text-white">Fluxo de Assinatura</CardTitle>
            <CardDescription>Como funciona o sistema de assinatura</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4 text-sm text-gray-300">
            <div>
              <h3 className="font-semibold text-white mb-2">1. Trial de 5 minutos</h3>
              <p>Novos usuários têm 5 minutos de degustação gratuita ao assistir qualquer canal.</p>
            </div>
            
            <div>
              <h3 className="font-semibold text-white mb-2">2. Após o Trial</h3>
              <p>O usuário pode:</p>
              <ul className="list-disc list-inside ml-4 space-y-1">
                <li>Pagar via Stripe para acesso premium</li>
                <li>Baixar o app e ganhar 1 mês grátis</li>
              </ul>
            </div>
            
            <div>
              <h3 className="font-semibold text-white mb-2">3. Renovação Automática</h3>
              <p>A assinatura é renovada automaticamente a cada 30 dias via Stripe.</p>
              <p className="mt-1">O webhook do Stripe atualiza o status no banco de dados.</p>
            </div>
            
            <div>
              <h3 className="font-semibold text-white mb-2">4. Verificação de Acesso</h3>
              <p>O sistema verifica em múltiplas tabelas:</p>
              <ul className="list-disc list-inside ml-4 space-y-1">
                <li><code className="bg-slate-900 px-1">profiles</code> - Usuários Supabase Auth</li>
                <li><code className="bg-slate-900 px-1">stack_profiles</code> - Usuários Stack Auth</li>
                <li><code className="bg-slate-900 px-1">user_access</code> - Acessos por cupom</li>
              </ul>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}