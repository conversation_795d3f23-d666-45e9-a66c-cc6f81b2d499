'use client'

import { Footer } from '@/components/footer'
import { BackToHome } from '@/components/back-to-home'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { But<PERSON> } from '@/components/ui/button'
import { 
  Handshake, Trophy, Globe, Tv, 
  Smartphone, Shield, Rocket, Users,
  CheckCircle, ArrowRight
} from 'lucide-react'
import Link from 'next/link'

export default function PartnersPage() {
  const partners = [
    {
      category: 'Ligas y Competiciones',
      icon: Trophy,
      color: 'text-yellow-400',
      partners: [
        { name: 'LaL<PERSON>', logo: '🇪🇸', description: 'Liga española de fútbol' },
        { name: 'UEFA', logo: '⚽', description: 'Competiciones europeas' },
        { name: 'Formula 1', logo: '🏎️', description: 'Campeonato mundial de F1' },
        { name: 'MotoGP', logo: '🏍️', description: 'Campeonato mundial de motociclismo' },
        { name: '<PERSON><PERSON>', logo: '🏀', description: 'Liga española de baloncesto' },
        { name: 'ATP Tour', logo: '🎾', description: 'Circuito profesional de tenis' }
      ]
    },
    {
      category: 'Tecnología',
      icon: Rocket,
      color: 'text-blue-400',
      partners: [
        { name: 'AWS', logo: '☁️', description: 'Infraestructura cloud' },
        { name: 'Akamai', logo: '🌐', description: 'CDN global' },
        { name: 'Dolby', logo: '🎵', description: 'Tecnología de audio' },
        { name: 'Google Cloud', logo: '🔧', description: 'Servicios de IA y análisis' }
      ]
    },
    {
      category: 'Dispositivos',
      icon: Tv,
      color: 'text-purple-400',
      partners: [
        { name: 'Samsung', logo: '📺', description: 'Smart TVs' },
        { name: 'LG', logo: '📺', description: 'Televisores OLED' },
        { name: 'Apple TV', logo: '🍎', description: 'Dispositivo de streaming' },
        { name: 'Chromecast', logo: '📱', description: 'Casting de Google' },
        { name: 'Fire TV', logo: '🔥', description: 'Amazon streaming' },
        { name: 'Roku', logo: '📺', description: 'Plataforma de streaming' }
      ]
    },
    {
      category: 'Telecomunicaciones',
      icon: Globe,
      color: 'text-green-400',
      partners: [
        { name: 'Movistar', logo: '📶', description: 'Operador principal' },
        { name: 'Vodafone', logo: '📡', description: 'Red móvil y fibra' },
        { name: 'Orange', logo: '🟠', description: 'Servicios de conectividad' },
        { name: 'MásMóvil', logo: '📱', description: 'Operador móvil' }
      ]
    }
  ]

  const partnerBenefits = [
    {
      icon: Users,
      title: 'Alcance Masivo',
      description: 'Acceso a más de 1 millón de usuarios activos'
    },
    {
      icon: Shield,
      title: 'Plataforma Segura',
      description: 'Infraestructura confiable y protegida'
    },
    {
      icon: Globe,
      title: 'Cobertura Global',
      description: 'Presencia en 15 países y creciendo'
    },
    {
      icon: Rocket,
      title: 'Innovación Continua',
      description: 'Tecnología de vanguardia en streaming'
    }
  ]

  const partnershipTypes = [
    {
      title: 'Derechos de Contenido',
      description: 'Colaboración para transmisión de eventos deportivos exclusivos',
      features: [
        'Transmisión en vivo HD/4K',
        'Análisis y estadísticas en tiempo real',
        'Contenido bajo demanda',
        'Multi-idioma y accesibilidad'
      ]
    },
    {
      title: 'Integración Tecnológica',
      description: 'Alianzas técnicas para mejorar la experiencia del usuario',
      features: [
        'APIs de integración',
        'SDKs personalizados',
        'Soporte técnico dedicado',
        'Co-innovación de productos'
      ]
    },
    {
      title: 'Distribución',
      description: 'Partnerships para expandir nuestro alcance de mercado',
      features: [
        'Bundles con operadores',
        'Pre-instalación en dispositivos',
        'Promociones conjuntas',
        'Acceso preferencial'
      ]
    }
  ]

  return (
    <div className="min-h-screen bg-gradient-to-b from-slate-950 to-slate-900">
      {/* Hero Section */}
      <section className="relative py-20 overflow-hidden">
        <div className="absolute inset-0">
          <div className="absolute inset-0 bg-gradient-to-r from-blue-900/20 to-purple-900/20" />
        </div>
        
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <BackToHome />
          <div className="text-center">
            <Badge className="mb-4 bg-blue-600 text-white">Partners</Badge>
            <h1 className="text-5xl font-bold text-white mb-6">
              Nuestros Socios
            </h1>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Trabajamos con los mejores para ofrecer la experiencia de streaming deportivo 
              más completa del mercado
            </p>
          </div>
        </div>
      </section>

      {/* Partner Benefits */}
      <section className="py-16 border-t border-slate-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold text-white mb-12 text-center">
            Por qué Asociarse con StreamPlus
          </h2>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {partnerBenefits.map((benefit, index) => (
              <Card key={index} className="bg-slate-800/50 border-slate-700 text-center">
                <CardContent className="p-6">
                  <benefit.icon className="h-12 w-12 text-blue-400 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-white mb-2">{benefit.title}</h3>
                  <p className="text-gray-400">{benefit.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Current Partners */}
      <section className="py-16 border-t border-slate-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold text-white mb-12 text-center">
            Nuestros Partners Actuales
          </h2>
          
          <div className="space-y-12">
            {partners.map((category, categoryIndex) => (
              <div key={categoryIndex}>
                <div className="flex items-center gap-3 mb-6">
                  <category.icon className={`h-6 w-6 ${category.color}`} />
                  <h3 className="text-xl font-semibold text-white">{category.category}</h3>
                </div>
                
                <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {category.partners.map((partner, index) => (
                    <Card key={index} className="bg-slate-800/50 border-slate-700 hover:border-slate-600 transition-all">
                      <CardContent className="p-6">
                        <div className="flex items-start gap-4">
                          <span className="text-3xl">{partner.logo}</span>
                          <div>
                            <h4 className="text-lg font-semibold text-white">{partner.name}</h4>
                            <p className="text-sm text-gray-400">{partner.description}</p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Partnership Types */}
      <section className="py-16 border-t border-slate-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold text-white mb-12 text-center">
            Tipos de Colaboración
          </h2>
          
          <div className="grid lg:grid-cols-3 gap-8">
            {partnershipTypes.map((type, index) => (
              <Card key={index} className="bg-slate-800/50 border-slate-700">
                <CardHeader>
                  <CardTitle className="text-white">{type.title}</CardTitle>
                  <CardDescription className="text-gray-300">
                    {type.description}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {type.features.map((feature, i) => (
                      <li key={i} className="flex items-center gap-2 text-gray-300">
                        <CheckCircle className="h-4 w-4 text-green-400 flex-shrink-0" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Success Stories */}
      <section className="py-16 border-t border-slate-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold text-white mb-12 text-center">
            Casos de Éxito
          </h2>
          
          <div className="grid md:grid-cols-2 gap-8">
            <Card className="bg-gradient-to-br from-yellow-900/20 to-orange-900/20 border-yellow-500/20">
              <CardHeader>
                <div className="flex items-center gap-3 mb-4">
                  <span className="text-4xl">🇪🇸</span>
                  <div>
                    <CardTitle className="text-white">LaLiga Partnership</CardTitle>
                    <CardDescription>Derechos exclusivos de transmisión</CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-gray-300 mb-4">
                  Nuestra alianza con LaLiga ha permitido llevar el fútbol español a 
                  millones de aficionados con calidad 4K y análisis en tiempo real.
                </p>
                <div className="grid grid-cols-2 gap-4 text-center">
                  <div>
                    <p className="text-2xl font-bold text-white">380+</p>
                    <p className="text-sm text-gray-400">Partidos/año</p>
                  </div>
                  <div>
                    <p className="text-2xl font-bold text-white">15</p>
                    <p className="text-sm text-gray-400">Idiomas</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card className="bg-gradient-to-br from-blue-900/20 to-purple-900/20 border-blue-500/20">
              <CardHeader>
                <div className="flex items-center gap-3 mb-4">
                  <span className="text-4xl">☁️</span>
                  <div>
                    <CardTitle className="text-white">AWS Infrastructure</CardTitle>
                    <CardDescription>Tecnología cloud escalable</CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-gray-300 mb-4">
                  La infraestructura de AWS nos permite escalar automáticamente durante 
                  eventos de alta demanda garantizando la mejor experiencia.
                </p>
                <div className="grid grid-cols-2 gap-4 text-center">
                  <div>
                    <p className="text-2xl font-bold text-white">99.99%</p>
                    <p className="text-sm text-gray-400">Uptime</p>
                  </div>
                  <div>
                    <p className="text-2xl font-bold text-white">&lt;100ms</p>
                    <p className="text-sm text-gray-400">Latencia</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Become a Partner CTA */}
      <section className="py-20 border-t border-slate-800">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <Card className="bg-gradient-to-br from-blue-900/20 to-purple-900/20 border-blue-500/20">
            <CardContent className="p-12 text-center">
              <Handshake className="h-16 w-16 text-blue-400 mx-auto mb-6" />
              <h2 className="text-3xl font-bold text-white mb-4">
                ¿Interesado en ser Partner?
              </h2>
              <p className="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
                Únete a nosotros para revolucionar el streaming deportivo. 
                Exploremos juntos nuevas oportunidades de colaboración.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button size="lg" className="bg-blue-600 hover:bg-blue-700">
                  Contactar Equipo de Partnerships
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
                <Button size="lg" variant="outline">
                  Descargar Media Kit
                </Button>
              </div>
              
              <p className="text-sm text-gray-400 mt-6">
                <EMAIL> • +34 900 123 456
              </p>
            </CardContent>
          </Card>
        </div>
      </section>

      <Footer />
    </div>
  )
}