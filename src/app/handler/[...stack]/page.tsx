import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@stackframe/stack";
import { stackServerApp } from "@/lib/auth/stack-server";

export default function Handler(props: { 
  params: { stack: string[] },
  searchParams: { [key: string]: string | string[] | undefined }
}) {
  return (
    <StackHandler 
      fullPage 
      app={stackServerApp} 
      routeProps={{
        params: props.params,
        searchParams: props.searchParams
      }}
    />
  );
}