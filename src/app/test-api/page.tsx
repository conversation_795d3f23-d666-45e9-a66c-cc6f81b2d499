'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'

export default function TestAPIPage() {
  const [results, setResults] = useState<any>({})
  const [loading, setLoading] = useState(false)

  const testAPIs = async () => {
    setLoading(true)
    setResults({})
    
    const endpoints = [
      '/api/test-sports',
      '/api/sports/live-events',
      '/api/sports/upcoming-events?days=7',
      '/api/iptv-org/streams?limit=5'
    ]
    
    for (const endpoint of endpoints) {
      try {
        console.log(`🔍 Testando ${endpoint}...`)
        const response = await fetch(endpoint)
        const data = await response.json()
        
        setResults(prev => ({
          ...prev,
          [endpoint]: {
            status: response.status,
            ok: response.ok,
            data: data
          }
        }))
        
        console.log(`✅ ${endpoint}:`, { status: response.status, data })
      } catch (error) {
        console.error(`❌ ${endpoint}:`, error)
        setResults(prev => ({
          ...prev,
          [endpoint]: {
            error: error.message
          }
        }))
      }
    }
    
    setLoading(false)
  }

  return (
    <div className="min-h-screen bg-black p-8">
      <h1 className="text-3xl font-bold text-white mb-8">Test API Endpoints</h1>
      
      <Button 
        onClick={testAPIs} 
        disabled={loading}
        className="mb-8"
      >
        {loading ? 'Testando...' : 'Testar APIs'}
      </Button>
      
      <div className="space-y-4">
        {Object.entries(results).map(([endpoint, result]) => (
          <Card key={endpoint} className="p-4 bg-gray-900 border-gray-800">
            <h3 className="text-lg font-mono text-white mb-2">{endpoint}</h3>
            <pre className="text-sm text-gray-300 overflow-auto">
              {JSON.stringify(result, null, 2)}
            </pre>
          </Card>
        ))}
      </div>
    </div>
  )
}