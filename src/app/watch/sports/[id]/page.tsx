'use client'

import { useEffect, useState } from 'react'
import { useParams, useRouter } from 'next/navigation'
import { EnhancedHlsPlayer } from '@/components/player/enhanced-hls-player'
import { Button } from '@/components/ui/button'
import { ArrowLeft, Tv, AlertCircle } from 'lucide-react'
import { getSportsChannelById } from '@/lib/working-sports-channels'

interface Channel {
  id: string
  name: string
  url: string
  category: string
  logo?: string
}

export default function WatchSportsPage() {
  const params = useParams()
  const router = useRouter()
  const [channel, setChannel] = useState<Channel | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    loadChannel()
  }, [params.id])

  const loadChannel = () => {
    try {
      // Primeiro tentar buscar do sessionStorage
      const storedChannel = sessionStorage.getItem('selectedChannel')
      if (storedChannel) {
        const parsedChannel = JSON.parse(storedChannel)
        if (parsedChannel.id === params.id) {
          setChannel(parsedChannel)
          setLoading(false)
          return
        }
      }

      // Se não encontrar no sessionStorage, buscar diretamente
      const foundChannel = getSportsChannelById(params.id as string)
      if (foundChannel) {
        setChannel(foundChannel)
      } else {
        setError('Canal no encontrado')
      }
    } catch (err) {
      console.error('Error loading channel:', err)
      setError('Error al cargar el canal')
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-white text-center">
          <Tv className="h-16 w-16 mx-auto mb-4 animate-pulse" />
          <p className="text-xl">Cargando canal...</p>
        </div>
      </div>
    )
  }

  if (error || !channel) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-white text-center">
          <AlertCircle className="h-16 w-16 mx-auto mb-4 text-red-500" />
          <h1 className="text-2xl font-bold mb-2">Error al cargar el canal</h1>
          <p className="text-gray-400 mb-6">{error || 'Canal no disponible'}</p>
          <Button
            onClick={() => router.push('/sports')}
            variant="secondary"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Volver a los canales
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-black">
      <div className="absolute top-4 left-4 z-50">
        <Button
          onClick={() => router.push('/sports')}
          variant="ghost"
          className="text-white hover:bg-white/10"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Volver
        </Button>
      </div>

      <div className="w-full h-screen">
        <EnhancedHlsPlayer
          src={channel.url}
          poster={channel.logo}
          title={channel.name}
          onError={(error) => {
            console.error('Player error:', error)
            setError('Error al reproducir el canal')
          }}
        />
      </div>

      <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-6">
        <h1 className="text-white text-2xl font-bold">{channel.name}</h1>
        <p className="text-gray-300">{channel.category}</p>
      </div>
    </div>
  )
}