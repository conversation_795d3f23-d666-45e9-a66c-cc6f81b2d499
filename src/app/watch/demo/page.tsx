import { VideoPlayer } from '@/components/player/video-player'
import { But<PERSON> } from '@/components/ui/button'
import { ArrowLeft } from 'lucide-react'
import Link from 'next/link'

export default function DemoPage() {
  // URL de stream de teste público
  const demoStreamUrl = 'https://test-streams.mux.dev/x36xhzz/x36xhzz.m3u8'
  
  // URLs de fallback (podem ser outros streams de teste)
  const fallbackUrls = [
    'https://bitdash-a.akamaihd.net/content/sintel/hls/playlist.m3u8',
    'https://demo.unified-streaming.com/k8s/features/stable/video/tears-of-steel/tears-of-steel.ism/.m3u8'
  ]
  
  return (
    <div className="min-h-screen bg-background">
      <div className="max-w-7xl mx-auto p-4 md:p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="icon" asChild>
            <Link href="/">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <div>
            <h1 className="text-2xl font-bold">Demo StreamPlus España</h1>
            <p className="text-muted-foreground">Descubre la calidad de nuestro streaming</p>
          </div>
        </div>

        {/* Video Player */}
        <div className="max-w-5xl mx-auto">
          <VideoPlayer 
            url={demoStreamUrl}
            fallbackUrls={fallbackUrls}
            channelName="Canal Demo - Big Buck Bunny"
            poster="/poster-demo.jpg"
          />
        </div>

        {/* Info Section */}
        <div className="max-w-5xl mx-auto space-y-6">
          <div className="bg-card rounded-lg p-6 space-y-4">
            <h2 className="text-xl font-semibold">¡Obtén 1 mes gratis!</h2>
            <div className="space-y-2 text-muted-foreground">
              <p>
                • Descarga nuestra app móvil y obtén 1 mes completamente gratis
              </p>
              <p>
                • Streaming en alta calidad HD y 4K
              </p>
              <p>
                • Acceso a más de 100 canales de deportes y entretenimiento
              </p>
              <p>
                • Sin compromiso, cancela cuando quieras
              </p>
            </div>
          </div>
          
          <div className="bg-card rounded-lg p-6 space-y-4">
            <h2 className="text-xl font-semibold">Canales disponibles</h2>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center space-y-2">
                <div className="bg-muted rounded-lg p-4">
                  <span className="text-2xl">⚽</span>
                </div>
                <p className="text-sm font-medium">LaLiga</p>
              </div>
              <div className="text-center space-y-2">
                <div className="bg-muted rounded-lg p-4">
                  <span className="text-2xl">🏆</span>
                </div>
                <p className="text-sm font-medium">Champions</p>
              </div>
              <div className="text-center space-y-2">
                <div className="bg-muted rounded-lg p-4">
                  <span className="text-2xl">🏎️</span>
                </div>
                <p className="text-sm font-medium">Formula 1</p>
              </div>
              <div className="text-center space-y-2">
                <div className="bg-muted rounded-lg p-4">
                  <span className="text-2xl">🎮</span>
                </div>
                <p className="text-sm font-medium">eSports</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}