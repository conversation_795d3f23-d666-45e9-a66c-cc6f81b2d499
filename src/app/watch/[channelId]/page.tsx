import { notFound } from 'next/navigation'
import { EnhancedHlsPlayer } from '@/components/player/enhanced-hls-player'
import { Badge } from '@/components/ui/badge'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { ArrowLeft, Trophy, Users, Star, Activity } from 'lucide-react'
import Link from 'next/link'
import { logger } from '@/lib/logger'
import { LOG_TAGS } from '@/config/debug'
import { buildApiUrl } from '@/lib/api-utils'

interface WatchPageProps {
  params: Promise<{
    channelId: string
  }>
}

export default async function WatchPage({ params }: WatchPageProps) {
  const timestamp = new Date().toISOString()
  
  // Tratamento de erro para params
  let channelId: string
  try {
    const resolvedParams = await params
    channelId = resolvedParams.channelId
    
    if (!channelId) {
      console.error(`[WATCH-PAGE] ${timestamp} - No channelId provided`)
      notFound()
    }
  } catch (error) {
    console.error(`[WATCH-PAGE] ${timestamp} - Error resolving params:`, error)
    notFound()
  }
  
  console.log(`[WATCH-PAGE] ${timestamp} - ===== NEW REQUEST =====`)
  console.log(`[WATCH-PAGE] ${timestamp} - Channel ID:`, channelId)
  console.log(`[WATCH-PAGE] ${timestamp} - Full params:`, await params)
  logger.info(LOG_TAGS.STREAM, 'Watch page loaded', { channelId })
  
  // Detectar tipo de canal
  // Log detalhado do channelId recebido
  console.log(`[WATCH-PAGE] ${timestamp} - Raw channel ID analysis:`)
  console.log(`[WATCH-PAGE] ${timestamp} -   Channel ID:`, channelId)
  console.log(`[WATCH-PAGE] ${timestamp} -   Channel ID type:`, typeof channelId)
  console.log(`[WATCH-PAGE] ${timestamp} -   Channel ID length:`, channelId.length)
  console.log(`[WATCH-PAGE] ${timestamp} -   Starts with iptv-:`, channelId.startsWith('iptv-'))
  console.log(`[WATCH-PAGE] ${timestamp} -   Starts with premium-:`, channelId.startsWith('premium-'))
  console.log(`[WATCH-PAGE] ${timestamp} -   Contains dots:`, channelId.includes('.'))
  
  // Por padrão, canais IPTV não têm prefixo iptv- quando vêm do browse
  const isIPTV = !channelId.startsWith('live-') && !channelId.startsWith('demo-')
  const isPremium = channelId.startsWith('premium-')
  const isLiveGame = channelId.startsWith('live-')
  
  console.log(`[WATCH-PAGE] ${timestamp} - Channel type detection:`)
  console.log(`[WATCH-PAGE] ${timestamp} -   isIPTV:`, isIPTV)
  console.log(`[WATCH-PAGE] ${timestamp} -   isPremium:`, isPremium)
  console.log(`[WATCH-PAGE] ${timestamp} -   isLiveGame:`, isLiveGame)
  console.log(`[WATCH-PAGE] ${timestamp} -   Channel prefix:`, channelId.split('-')[0])
  
  logger.debug(LOG_TAGS.STREAM, 'Channel type detected', { 
    channelId, 
    isIPTV, 
    isLiveGame 
  })
  
  // Buscar dados do canal
  let channelData = null
  
  if (isIPTV) {
    // Buscar dados do canal IPTV
    // Remover prefixo iptv- apenas se existir, caso contrário usar o ID como está
    const iptvId = channelId.startsWith('iptv-') 
      ? channelId.replace('iptv-', '')
      : channelId
    
    console.log(`[WATCH-PAGE] ${timestamp} - Processing IPTV channel`)
    console.log(`[WATCH-PAGE] ${timestamp} -   Original ID:`, channelId)
    console.log(`[WATCH-PAGE] ${timestamp} -   Cleaned ID:`, iptvId)
    console.log(`[WATCH-PAGE] ${timestamp} -   Had iptv- prefix:`, channelId.startsWith('iptv-'))
    
    logger.debug(LOG_TAGS.STREAM, 'Fetching IPTV channel data', { iptvId })
    
    // Construir URL da API de forma segura
    const apiUrl = await buildApiUrl(`/api/iptv/channel/${iptvId}`)
    
    console.log(`[WATCH-PAGE] ${timestamp} - API configuration:`)
    console.log(`[WATCH-PAGE] ${timestamp} -   API URL:`, apiUrl)
    
    logger.info(LOG_TAGS.STREAM, 'Fetching from API', { apiUrl })
    
    console.log(`[WATCH-PAGE] ${timestamp} - Fetching channel data...`)
    
    const response = await fetch(apiUrl, {
      cache: 'no-store'
    })
    
    console.log(`[WATCH-PAGE] ${timestamp} - API Response:`)
    console.log(`[WATCH-PAGE] ${timestamp} -   Status:`, response.status)
    console.log(`[WATCH-PAGE] ${timestamp} -   Status Text:`, response.statusText)
    console.log(`[WATCH-PAGE] ${timestamp} -   OK:`, response.ok)
    console.log(`[WATCH-PAGE] ${timestamp} -   Headers:`, Object.fromEntries(response.headers.entries()))
    
    if (response.ok) {
      const data = await response.json()
      console.log(`[WATCH-PAGE] ${timestamp} - API Response Data:`)
      console.log(`[WATCH-PAGE] ${timestamp} -   Success:`, data.success)
      console.log(`[WATCH-PAGE] ${timestamp} -   Has data:`, !!data.data)
      console.log(`[WATCH-PAGE] ${timestamp} -   Error:`, data.error || 'none')
      
      if (data.data) {
        console.log(`[WATCH-PAGE] ${timestamp} -   Channel ID:`, data.data.id)
        console.log(`[WATCH-PAGE] ${timestamp} -   Channel Name:`, data.data.name)
        console.log(`[WATCH-PAGE] ${timestamp} -   Stream URL:`, data.data.url?.substring(0, 100) + '...')
        console.log(`[WATCH-PAGE] ${timestamp} -   Has Headers:`, !!data.data.headers)
        console.log(`[WATCH-PAGE] ${timestamp} -   Headers:`, data.data.headers || {})
        console.log(`[WATCH-PAGE] ${timestamp} -   User Agent:`, data.data.userAgent || 'none')
        console.log(`[WATCH-PAGE] ${timestamp} -   Requires Proxy:`, data.data.requiresProxy)
        console.log(`[WATCH-PAGE] ${timestamp} -   Stream count:`, data.data.streams?.length || 0)
      }
      
      if (data.success) {
        channelData = data.data
        console.log(`[WATCH-PAGE] ${timestamp} - ✅ Channel data loaded successfully`)
        
        logger.info(LOG_TAGS.STREAM, 'IPTV channel data loaded', {
          id: channelData.id,
          name: channelData.name,
          url: channelData.url
        })
      } else {
        console.log(`[WATCH-PAGE] ${timestamp} - ❌ Failed to load channel`)
        console.log(`[WATCH-PAGE] ${timestamp} -   Error message:`, data.error)
        console.log(`[WATCH-PAGE] ${timestamp} -   Full response:`, JSON.stringify(data, null, 2))
        logger.error(LOG_TAGS.STREAM, 'Failed to load IPTV channel', { 
          iptvId, 
          error: data.error 
        })
      }
    } else {
      console.log(`[WATCH-PAGE] ${timestamp} - ❌ API request failed`)
      console.log(`[WATCH-PAGE] ${timestamp} -   Response status:`, response.status)
      console.log(`[WATCH-PAGE] ${timestamp} -   Response text:`, await response.text())
      logger.error(LOG_TAGS.STREAM, 'API request failed', { 
        iptvId, 
        status: response.status 
      })
    }
  }
  
  // Se não encontrou dados do canal e não é jogo ao vivo, retornar 404
  if (!channelData && !isLiveGame) {
    logger.error(LOG_TAGS.STREAM, 'Channel not found', { channelId })
    notFound()
  }
  
  // Dados default apenas para jogos ao vivo (não IPTV)
  const defaultGameData = isLiveGame ? {
    name: 'Partido en vivo',
    url: process.env.NEXT_PUBLIC_STREAM_URL || '',
    description: 'Transmisión en vivo'
  } : null
  
  // Log final data being used
  logger.info(LOG_TAGS.STREAM, 'Final channel data', {
    channelId,
    hasChannelData: !!channelData,
    channelName: channelData?.name,
    channelUrl: channelData?.url,
    isLiveGame,
    usingDefault: !channelData && isLiveGame
  })
  
  return (
    <div className="min-h-screen bg-gradient-to-b from-slate-900 to-slate-950">
      {/* Header */}
      <header className="sticky top-0 z-50 bg-slate-900/80 backdrop-blur-md border-b border-slate-800">
        <div className="max-w-7xl mx-auto px-4 py-4 flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button variant="ghost" size="sm" asChild>
              <Link href="/browse">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Volver
              </Link>
            </Button>
            
            <div className="hidden md:flex items-center gap-4">
              <h1 className="text-xl font-semibold text-white">
                {channelData?.name || defaultGameData?.name || 'Canal Deportivo'}
              </h1>
              <Badge className="bg-red-600 border-0">
                <Activity className="h-3 w-3 mr-1" />
                EN VIVO
              </Badge>
            </div>
          </div>
          
          <div className="flex items-center gap-3">
            <div className="flex items-center gap-2 text-sm text-gray-400">
              <Users className="h-4 w-4" />
              {Math.floor(10000 + Math.random() * 50000).toLocaleString()} viendo
            </div>
          </div>
        </div>
      </header>
      
      {/* Main content */}
      <main className="max-w-7xl mx-auto px-4 py-8">
        <div className="grid lg:grid-cols-3 gap-8">
          {/* Player */}
          <div className="lg:col-span-2">
            {(() => {
              const playerProps = {
                channelName: channelData?.name || defaultGameData?.name || 'Canal Deportivo',
                channelId: channelData?.id || (isIPTV ? channelId.replace('iptv-', '') : channelId),
                streamUrl: channelData?.url || defaultGameData?.url || '',
                streamUrls: channelData?.streams?.map((s: any) => s.url) || (channelData?.url ? [channelData.url] : []),
                isLive: true,
                headers: channelData?.headers,
                userAgent: channelData?.userAgent,
                requiresProxy: channelData?.requiresProxy
              }
              
              console.log(`[WATCH-PAGE] ${timestamp} - Player Props:`)
              console.log(`[WATCH-PAGE] ${timestamp} -   Channel Name:`, playerProps.channelName)
              console.log(`[WATCH-PAGE] ${timestamp} -   Channel ID:`, playerProps.channelId)
              console.log(`[WATCH-PAGE] ${timestamp} -   Stream URL:`, playerProps.streamUrl?.substring(0, 100) + '...')
              console.log(`[WATCH-PAGE] ${timestamp} -   Stream URLs count:`, playerProps.streamUrls.length)
              console.log(`[WATCH-PAGE] ${timestamp} -   Has Headers:`, !!playerProps.headers)
              console.log(`[WATCH-PAGE] ${timestamp} -   Headers:`, playerProps.headers || {})
              console.log(`[WATCH-PAGE] ${timestamp} -   User Agent:`, playerProps.userAgent || 'none')
              console.log(`[WATCH-PAGE] ${timestamp} -   Requires Proxy:`, playerProps.requiresProxy)
              console.log(`[WATCH-PAGE] ${timestamp} - ==============================`)
              
              return <EnhancedHlsPlayer {...playerProps} />
            })()}
            
            {/* Channel info */}
            <Card className="mt-6 p-6 bg-slate-800/50 border-slate-700">
              <div className="flex items-start justify-between">
                <div>
                  <h2 className="text-2xl font-bold text-white mb-2">
                    {channelData?.name || defaultGameData?.name || 'Canal Deportivo'}
                  </h2>
                  <p className="text-gray-400">
                    {channelData?.country ? `Canal de ${channelData.country}` : defaultGameData?.description || 'Transmisión en vivo'}
                  </p>
                  
                  {channelData?.categories && (
                    <div className="flex gap-2 mt-3">
                      {channelData.categories.map((cat: string) => (
                        <Badge key={cat} variant="secondary" className="bg-slate-700 text-white">
                          {cat}
                        </Badge>
                      ))}
                    </div>
                  )}
                </div>
                
                <div className="flex items-center gap-1">
                  <Star className="h-5 w-5 text-yellow-500 fill-yellow-500" />
                  <span className="text-white font-medium">
                    {(4.5 + Math.random() * 0.5).toFixed(1)}
                  </span>
                </div>
              </div>
              
              {channelData?.epg && (
                <div className="mt-4 pt-4 border-t border-slate-700">
                  <p className="text-sm text-gray-400">
                    Guía de programación disponible en {channelData.epg.lang.toUpperCase()} • 
                    {channelData.epg.days} días
                  </p>
                </div>
              )}
            </Card>
          </div>
          
          {/* Sidebar */}
          <div className="space-y-6">
            {/* Información del canal */}
            <Card className="p-6 bg-slate-800/50 border-slate-700">
              <h3 className="text-lg font-semibold text-white mb-4">
                Información del canal
              </h3>
              
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-gray-400">Estado</span>
                  <Badge className="bg-green-600 border-0">
                    Transmitiendo
                  </Badge>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-gray-400">Calidad</span>
                  <span className="text-white">HD 720p</span>
                </div>
                
                {channelData?.languages && (
                  <div className="flex items-center justify-between">
                    <span className="text-gray-400">Idiomas</span>
                    <span className="text-white">
                      {channelData.languages.join(', ').toUpperCase()}
                    </span>
                  </div>
                )}
                
                <div className="flex items-center justify-between">
                  <span className="text-gray-400">Tipo</span>
                  <span className="text-white">
                    {isIPTV ? 'Canal IPTV' : 'Evento en vivo'}
                  </span>
                </div>
              </div>
            </Card>
            
            {/* CTA Premium */}
            <Card className="p-6 bg-gradient-to-br from-red-900/20 to-orange-900/20 border-red-500/20">
              <Trophy className="h-10 w-10 text-yellow-500 mb-3" />
              <h3 className="text-lg font-semibold text-white mb-2">
                Acceso Premium
              </h3>
              <p className="text-gray-300 text-sm mb-4">
                Descarga la app y obtén 1 mes gratis de acceso ilimitado
              </p>
              <Button className="w-full bg-gradient-to-r from-red-600 to-orange-600" asChild>
                <Link href="/mobile-app">
                  Obtener Premium
                </Link>
              </Button>
            </Card>
            
            {/* Canales relacionados */}
            <Card className="p-6 bg-slate-800/50 border-slate-700">
              <h3 className="text-lg font-semibold text-white mb-4">
                Canales similares
              </h3>
              <p className="text-gray-400 text-sm">
                Explora más canales deportivos en nuestra plataforma
              </p>
              <Button variant="outline" className="w-full mt-4" asChild>
                <Link href="/browse?category=sports">
                  Ver más canales
                </Link>
              </Button>
            </Card>
          </div>
        </div>
      </main>
    </div>
  )
}