'use client'

import { useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { AlertCircle, ArrowLeft, RefreshCw } from 'lucide-react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'

export default function WatchError({
  error,
  reset,
}: {
  error: Error & { digest?: string }
  reset: () => void
}) {
  const router = useRouter()

  useEffect(() => {
    // Log error em produção
    console.error('[WATCH ERROR]:', error)
  }, [error])

  return (
    <div className="min-h-screen bg-gradient-to-b from-slate-900 to-slate-950 flex items-center justify-center p-4">
      <Card className="max-w-lg w-full p-8 bg-slate-800/50 border-slate-700 text-center">
        <div className="inline-flex items-center justify-center w-20 h-20 bg-red-500/20 rounded-full mb-6">
          <AlertCircle className="h-10 w-10 text-red-500" />
        </div>

        <h1 className="text-2xl font-bold text-white mb-4">
          Error al cargar el canal
        </h1>

        <p className="text-gray-400 mb-8">
          Hubo un problema al cargar el canal. Por favor, intenta de nuevo o selecciona otro canal.
        </p>

        {/* Error details en desarrollo */}
        {process.env.NODE_ENV === 'development' && (
          <div className="mb-6 p-4 bg-slate-900/50 rounded-lg text-left">
            <p className="text-xs text-gray-500 font-mono break-all">
              {error.message}
            </p>
            {error.digest && (
              <p className="text-xs text-gray-600 mt-2">
                Digest: {error.digest}
              </p>
            )}
          </div>
        )}

        <div className="space-y-3">
          <Button
            onClick={() => reset()}
            className="w-full bg-gradient-to-r from-red-600 to-orange-600"
          >
            <RefreshCw className="mr-2 h-4 w-4" />
            Intentar de nuevo
          </Button>

          <Button
            variant="outline"
            className="w-full"
            asChild
          >
            <Link href="/browse">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Volver a los canales
            </Link>
          </Button>
        </div>
      </Card>
    </div>
  )
}