'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2, Shield, Key } from 'lucide-react'
import { createClient } from '@/lib/supabase/client'
import { toast } from 'sonner'

export default function AdminAccessPage() {
  const router = useRouter()
  const [loading, setLoading] = useState(false)
  const [accessKey, setAccessKey] = useState('')
  
  const handleQuickAccess = async () => {
    if (!accessKey) {
      toast.error('Por favor, ingresa la clave de acceso')
      return
    }
    
    setLoading(true)
    
    try {
      // Verificar clave de acceso rápido
      if (accessKey === 'admin2025' || accessKey === 'streamplus2025') {
        // Crear sesión temporal de admin
        const supabase = createClient()
        
        // Hacer login con credenciales de admin
        const { data, error } = await supabase.auth.signInWithPassword({
          email: '<EMAIL>',
          password: 'Admin2025!@#'
        })
        
        if (error) {
          // Si no existe, crear usuario admin
          const { data: signUpData, error: signUpError } = await supabase.auth.signUp({
            email: '<EMAIL>',
            password: 'Admin2025!@#',
            options: {
              data: {
                role: 'admin'
              }
            }
          })
          
          if (signUpError) {
            throw new Error('Error al crear acceso de admin')
          }
        }
        
        toast.success('Acceso concedido')
        router.push('/admin')
      } else {
        toast.error('Clave de acceso incorrecta')
      }
    } catch (error) {
      console.error('Error:', error)
      toast.error('Error al acceder')
    } finally {
      setLoading(false)
    }
  }
  
  const handleEmailAccess = () => {
    router.push('/login?redirect=/admin')
  }
  
  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-b from-slate-950 to-slate-900 p-4">
      <Card className="w-full max-w-md bg-slate-800/50 border-slate-700">
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            <div className="p-3 bg-purple-900/30 rounded-full">
              <Shield className="h-8 w-8 text-purple-400" />
            </div>
          </div>
          <CardTitle className="text-2xl text-white">Acceso Administrativo</CardTitle>
          <CardDescription>
            Ingresa con tu clave de acceso o email autorizado
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Acceso rápido con clave */}
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="access-key">Clave de Acceso Rápido</Label>
              <div className="flex gap-2">
                <Input
                  id="access-key"
                  type="password"
                  placeholder="Ingresa la clave"
                  value={accessKey}
                  onChange={(e) => setAccessKey(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') handleQuickAccess()
                  }}
                />
                <Button 
                  onClick={handleQuickAccess}
                  disabled={loading}
                  className="bg-purple-600 hover:bg-purple-700"
                >
                  {loading ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <Key className="h-4 w-4" />
                  )}
                </Button>
              </div>
            </div>
            
            <Alert className="bg-slate-900/50 border-slate-700">
              <AlertDescription className="text-xs">
                Si no tienes la clave, contacta al administrador del sistema
              </AlertDescription>
            </Alert>
          </div>
          
          {/* Divisor */}
          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <span className="w-full border-t border-slate-700" />
            </div>
            <div className="relative flex justify-center text-xs uppercase">
              <span className="bg-slate-800 px-2 text-slate-400">O</span>
            </div>
          </div>
          
          {/* Acceso con email */}
          <div className="space-y-4">
            <Button
              onClick={handleEmailAccess}
              variant="outline"
              className="w-full"
            >
              Acceder con Email Autorizado
            </Button>
            
            <p className="text-xs text-center text-slate-500">
              Emails autorizados: <EMAIL>, <EMAIL>
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}