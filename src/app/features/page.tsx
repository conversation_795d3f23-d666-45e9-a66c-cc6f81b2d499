'use client'

import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Footer } from '@/components/footer'
import { BackToHome } from '@/components/back-to-home'
import { <PERSON><PERSON> } from '@/components/ui/button'
import Link from 'next/link'
import { 
  Tv, Smartphone, Shield, Zap, Trophy, Activity, 
  Clock, Star, Users, ArrowRight, Lock, Wifi,
  Globe, Download, Gamepad2, TrendingUp
} from 'lucide-react'

export default function FeaturesPage() {
  const features = [
    {
      icon: Tv,
      title: "Streaming en 4K Ultra HD",
      description: "Disfruta de una calidad de imagen excepcional con soporte para 4K, HDR10 y Dolby Vision en dispositivos compatibles.",
      color: "text-purple-500"
    },
    {
      icon: Trophy,
      title: "Deportes en Vivo 24/7",
      description: "Acceso completo a LaLiga, Champions League, F1, NBA y más de 50 competiciones deportivas internacionales.",
      color: "text-yellow-500"
    },
    {
      icon: Gamepad2,
      title: "eSports Exclusivos",
      description: "Cobertura completa de los principales torneos de eSports: LoL, CS2, Valorant, Dota 2 y más.",
      color: "text-green-500"
    },
    {
      icon: Clock,
      title: "Repeticiones Ilimitadas",
      description: "No te pierdas nada. Accede a las repeticiones de eventos hasta 7 días después de su emisión.",
      color: "text-blue-500"
    },
    {
      icon: Shield,
      title: "Sin Anuncios",
      description: "Experiencia de visualización ininterrumpida. Sin publicidad en ningún contenido premium.",
      color: "text-red-500"
    },
    {
      icon: Smartphone,
      title: "Multi-dispositivo",
      description: "Ve contenido en hasta 4 dispositivos simultáneamente. Compatible con Smart TV, móvil, tablet y PC.",
      color: "text-indigo-500"
    },
    {
      icon: Download,
      title: "Descarga Offline",
      description: "Descarga tus eventos favoritos y disfrútalos sin conexión a internet en cualquier momento.",
      color: "text-orange-500"
    },
    {
      icon: Globe,
      title: "Acceso Global",
      description: "Disfruta de NewSpports desde cualquier lugar del mundo sin restricciones geográficas.",
      color: "text-teal-500"
    },
    {
      icon: Activity,
      title: "Estadísticas en Tiempo Real",
      description: "Datos en vivo, estadísticas avanzadas y análisis detallados durante los eventos deportivos.",
      color: "text-pink-500"
    },
    {
      icon: Users,
      title: "Perfiles Personalizados",
      description: "Crea hasta 5 perfiles diferentes con recomendaciones personalizadas para cada miembro de la familia.",
      color: "text-cyan-500"
    },
    {
      icon: Lock,
      title: "Control Parental",
      description: "Configura restricciones de contenido y horarios para crear un ambiente seguro para los más pequeños.",
      color: "text-gray-500"
    },
    {
      icon: TrendingUp,
      title: "Análisis Avanzados",
      description: "Accede a análisis pre y post partido con expertos, highlights automáticos y momentos clave.",
      color: "text-emerald-500"
    }
  ]

  const techSpecs = [
    { label: "Calidad de Video", value: "Hasta 4K Ultra HD + HDR" },
    { label: "Calidad de Audio", value: "Dolby Atmos 5.1" },
    { label: "Dispositivos Simultáneos", value: "Hasta 4 dispositivos" },
    { label: "Perfiles por Cuenta", value: "5 perfiles" },
    { label: "Descarga Offline", value: "Ilimitada" },
    { label: "Soporte", value: "24/7 en español" }
  ]

  return (
    <div className="min-h-screen bg-gradient-to-b from-slate-950 to-slate-900">
      {/* Hero Section */}
      <section className="relative py-20 overflow-hidden">
        <div className="absolute inset-0">
          <div className="absolute inset-0 bg-gradient-to-r from-purple-900/20 to-pink-900/20" />
        </div>
        
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <BackToHome />
          <div className="text-center">
          <Badge className="mb-4 bg-purple-600 text-white">Características Premium</Badge>
          <h1 className="text-5xl md:text-6xl font-bold text-white mb-6">
            Todo lo que necesitas para
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-pink-400"> disfrutar al máximo</span>
          </h1>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto mb-8">
            NewSpports combina la mejor tecnología de streaming con el contenido deportivo y de noticias más exclusivo. 
            Descubre todas las características que hacen de nuestra plataforma la mejor opción.
          </p>
          <div className="flex gap-4 justify-center">
            <Button size="lg" asChild>
              <Link href="/register">
                Empezar Prueba Gratis
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>
            <Button size="lg" variant="outline" asChild>
              <Link href="/subscription">
                Ver Planes
              </Link>
            </Button>
          </div>
          </div>
        </div>
      </section>

      {/* Features Grid */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-white mb-4">Características Principales</h2>
            <p className="text-gray-400 max-w-2xl mx-auto">
              Diseñado para ofrecer la mejor experiencia de streaming deportivo
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {features.map((feature, index) => (
              <Card 
                key={index} 
                className="bg-slate-800/50 border-slate-700 hover:border-slate-600 transition-all hover:transform hover:scale-105"
              >
                <CardHeader>
                  <feature.icon className={`h-12 w-12 ${feature.color} mb-4`} />
                  <CardTitle className="text-white">{feature.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-gray-300">
                    {feature.description}
                  </CardDescription>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Tech Specs */}
      <section className="py-16 border-t border-slate-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-white mb-4">Especificaciones Técnicas</h2>
            <p className="text-gray-400 max-w-2xl mx-auto">
              La mejor tecnología para garantizar una experiencia perfecta
            </p>
          </div>
          
          <div className="bg-slate-800/50 rounded-xl p-8 backdrop-blur-sm">
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {techSpecs.map((spec, index) => (
                <div key={index} className="text-center">
                  <p className="text-gray-400 mb-2">{spec.label}</p>
                  <p className="text-2xl font-semibold text-white">{spec.value}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 border-t border-slate-800">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-4xl font-bold text-white mb-6">
            ¿Listo para experimentar NewSpports?
          </h2>
          <p className="text-xl text-gray-300 mb-8">
            Únete a miles de usuarios que ya disfrutan del mejor contenido deportivo en streaming.
          </p>
          <div className="flex gap-4 justify-center">
            <Button size="lg" className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700">
              Comenzar Prueba de 5 Minutos
              <Zap className="ml-2 h-5 w-5" />
            </Button>
          </div>
          <p className="text-sm text-gray-400 mt-4">
            Sin tarjeta de crédito • Sin compromisos • Cancela cuando quieras
          </p>
        </div>
      </section>

      <Footer />
    </div>
  )
}