'use client'

import { useEffect, useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { useAuth } from '@/hooks/use-auth'
import { useTrialStore } from '@/stores/trial.store'
import { useAccessStore } from '@/stores/access.store'
import { useSubscription } from '@/hooks/use-subscription'
import { Badge } from '@/components/ui/badge'
import { RefreshCw, Play, XCircle, CheckCircle } from 'lucide-react'

export default function TestAccessSystemPage() {
  const { user } = useAuth()
  const trialStore = useTrialStore()
  const accessStore = useAccessStore()
  const subscription = useSubscription()
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  if (!mounted) {
    return null
  }

  const handleClearAll = () => {
    localStorage.clear()
    window.location.reload()
  }

  const handleForceExpireTrial = () => {
    trialStore.expireTrial()
  }

  const handleStartNewTrial = () => {
    trialStore.startTrial(user?.id)
  }

  const handleCheckAccess = () => {
    if (user) {
      accessStore.checkAccess(user.id, true)
    }
  }

  const handleClearAccess = () => {
    accessStore.clearAccess()
  }

  return (
    <div className="p-8 max-w-6xl mx-auto space-y-6">
      <h1 className="text-3xl font-bold mb-8">Sistema de Acesso - Teste e Debug</h1>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Estado do Usuário */}
        <Card>
          <CardHeader>
            <CardTitle>Estado do Usuário</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="text-sm">
              <strong>Email:</strong> {user?.primaryEmail || 'Não logado'}
            </div>
            <div className="text-sm">
              <strong>ID:</strong> {user?.id || 'N/A'}
            </div>
            <div className="text-sm">
              <strong>Provider:</strong> {user?.signedInWithOAuth ? 'OAuth' : 'Email'}
            </div>
          </CardContent>
        </Card>

        {/* Estado da Subscription */}
        <Card>
          <CardHeader>
            <CardTitle>Estado da Subscription (useSubscription)</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="flex items-center gap-2">
              <strong>Loading:</strong>
              {subscription.loading ? <Badge variant="secondary">Sim</Badge> : <Badge>Não</Badge>}
            </div>
            <div className="flex items-center gap-2">
              <strong>Has Access:</strong>
              {subscription.hasAccess ? <CheckCircle className="h-4 w-4 text-green-500" /> : <XCircle className="h-4 w-4 text-red-500" />}
            </div>
            <div className="flex items-center gap-2">
              <strong>Is Subscribed:</strong>
              {subscription.isSubscribed ? <CheckCircle className="h-4 w-4 text-green-500" /> : <XCircle className="h-4 w-4 text-red-500" />}
            </div>
            <div className="flex items-center gap-2">
              <strong>Is In Trial:</strong>
              {subscription.isInTrial ? <CheckCircle className="h-4 w-4 text-green-500" /> : <XCircle className="h-4 w-4 text-red-500" />}
            </div>
            <div className="text-sm">
              <strong>Subscription Tier:</strong> {subscription.subscriptionTier || 'N/A'}
            </div>
            <div className="text-sm">
              <strong>Ends At:</strong> {subscription.subscriptionEndsAt || 'N/A'}
            </div>
          </CardContent>
        </Card>

        {/* Estado do Trial */}
        <Card>
          <CardHeader>
            <CardTitle>Estado do Trial (useTrialStore)</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="text-sm">
              <strong>Start Time:</strong> {trialStore.startTime ? new Date(trialStore.startTime).toLocaleString() : 'Não iniciado'}
            </div>
            <div className="text-sm">
              <strong>Time Remaining:</strong> {trialStore.timeRemaining}s ({Math.floor(trialStore.timeRemaining / 60)}:{(trialStore.timeRemaining % 60).toString().padStart(2, '0')})
            </div>
            <div className="flex items-center gap-2">
              <strong>Is Expired:</strong>
              {trialStore.isExpired ? <Badge variant="destructive">Sim</Badge> : <Badge variant="secondary">Não</Badge>}
            </div>
            <div className="text-sm">
              <strong>Session ID:</strong> {trialStore.sessionId || 'N/A'}
            </div>
            <div className="text-sm">
              <strong>Trial ID:</strong> {trialStore.trialId || 'N/A'}
            </div>
            <div className="flex items-center gap-2">
              <strong>Has Seen Offer:</strong>
              {trialStore.hasSeenOffer ? <CheckCircle className="h-4 w-4 text-green-500" /> : <XCircle className="h-4 w-4 text-red-500" />}
            </div>
          </CardContent>
        </Card>

        {/* Estado do Access */}
        <Card>
          <CardHeader>
            <CardTitle>Estado do Access (useAccessStore)</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="flex items-center gap-2">
              <strong>Has Access:</strong>
              {accessStore.hasAccess ? <CheckCircle className="h-4 w-4 text-green-500" /> : <XCircle className="h-4 w-4 text-red-500" />}
            </div>
            <div className="text-sm">
              <strong>Access Type:</strong> {accessStore.accessType || 'N/A'}
            </div>
            <div className="text-sm">
              <strong>Expires At:</strong> {accessStore.expiresAt ? new Date(accessStore.expiresAt).toLocaleString() : 'N/A'}
            </div>
            <div className="text-sm">
              <strong>Last Check:</strong> {accessStore.lastCheck ? new Date(accessStore.lastCheck).toLocaleString() : 'Never'}
            </div>
            <div className="flex items-center gap-2">
              <strong>Is Loading:</strong>
              {accessStore.isLoading ? <Badge variant="secondary">Sim</Badge> : <Badge>Não</Badge>}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Análise do Sistema */}
      <Card className="border-yellow-500">
        <CardHeader>
          <CardTitle className="text-yellow-500">Análise do Sistema de Bloqueio</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <h3 className="font-semibold mb-2">Estado Atual:</h3>
              <div className="space-y-1 text-sm">
                <div>• Trial Expirado: {trialStore.isExpired ? 'Sim' : 'Não'}</div>
                <div>• Tem Acesso Premium: {accessStore.hasAccess ? 'Sim' : 'Não'}</div>
                <div>• É Assinante: {subscription.isSubscribed ? 'Sim' : 'Não'}</div>
                <div>• Está em Trial: {subscription.isInTrial ? 'Sim' : 'Não'}</div>
              </div>
            </div>

            <div className="p-4 rounded-lg bg-slate-800">
              <h3 className="font-semibold mb-2">Deveria estar bloqueado?</h3>
              <div className="text-2xl font-bold">
                {trialStore.isExpired && !accessStore.hasAccess ? (
                  <span className="text-red-500">SIM - BLOQUEAR</span>
                ) : (
                  <span className="text-green-500">NÃO - LIBERADO</span>
                )}
              </div>
              <div className="text-sm mt-2 text-gray-400">
                Lógica: (Trial Expirado && Sem Acesso Premium) = Bloquear
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Ações de Teste */}
      <Card>
        <CardHeader>
          <CardTitle>Ações de Teste</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
            <Button onClick={handleClearAll} variant="destructive">
              <RefreshCw className="mr-2 h-4 w-4" />
              Limpar Tudo
            </Button>
            
            <Button onClick={handleForceExpireTrial} variant="secondary">
              <XCircle className="mr-2 h-4 w-4" />
              Expirar Trial
            </Button>
            
            <Button onClick={handleStartNewTrial} variant="secondary">
              <Play className="mr-2 h-4 w-4" />
              Iniciar Trial
            </Button>
            
            <Button onClick={handleCheckAccess} variant="secondary">
              <RefreshCw className="mr-2 h-4 w-4" />
              Verificar Acesso
            </Button>
            
            <Button onClick={handleClearAccess} variant="secondary">
              <XCircle className="mr-2 h-4 w-4" />
              Limpar Access
            </Button>
            
            <Button onClick={() => subscription.refresh()} variant="secondary">
              <RefreshCw className="mr-2 h-4 w-4" />
              Refresh Subscription
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Logs do Console */}
      <Card>
        <CardHeader>
          <CardTitle>Instruções para Debug</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2 text-sm">
          <p>1. Abra o console do navegador (F12) para ver os logs detalhados</p>
          <p>2. Procure por logs com prefixos:</p>
          <ul className="list-disc list-inside ml-4">
            <li>[TRIAL-PLAYER] - Lógica do player</li>
            <li>[TRIAL-STORE] - Estado do trial</li>
            <li>[ACCESS-STORE] - Verificação de acesso</li>
            <li>[SUBSCRIPTION-STATUS-API] - API de status</li>
          </ul>
          <p>3. Use os botões acima para simular diferentes cenários</p>
          <p>4. Teste em /browse para ver o comportamento real</p>
        </CardContent>
      </Card>
    </div>
  )
}