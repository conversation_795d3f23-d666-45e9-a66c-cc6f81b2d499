'use client'

import { liveStreamsService } from '@/services/api/sports/live-streams.service'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { ArrowRight, Play, Zap, Shield, Smartphone, Star, Users, Tv, Clock, Trophy, Activity, Lock } from 'lucide-react'
import Link from 'next/link'
import { useAllSportsData } from '@/hooks/use-all-sports-data'
import { UpcomingEventCard } from '@/components/sports/upcoming-event-card'
import { Skeleton } from '@/components/ui/skeleton'
import { LiveSportsResults } from '@/components/sports/live-sports-results'
import { AllSportsEvents } from '@/components/sports/all-sports-events'
import { useIPTVChannels } from '@/hooks/use-iptv-channels'
import { SportsPlayerModal } from '@/components/sports/sports-player-modal'
import { useSimpleAuth } from '@/hooks/use-simple-auth'
import { useRouter } from 'next/navigation'
import { toast } from 'sonner'
import { Footer } from '@/components/footer'
import { LiveMatchBanner } from '@/components/live-match-banner'

export default function HomePage() {
  const { allCategories, loading } = useAllSportsData()
  const { channels: iptvChannels, loading: iptvLoading } = useIPTVChannels(true, 8)
  const [selectedChannel, setSelectedChannel] = useState<any>(null)
  const [isPlayerOpen, setIsPlayerOpen] = useState(false)
  const { user, requireAuth } = useSimpleAuth()
  const router = useRouter()
  
  const features = [
    {
      icon: Zap,
      title: 'Streaming Ultra Rápido',
      description: 'Tecnología de última generación para streaming sin interrupciones',
      gradient: 'from-yellow-500 to-orange-500'
    },
    {
      icon: Shield,
      title: 'Contenido Premium',
      description: 'Acceso exclusivo a los mejores eventos deportivos',
      gradient: 'from-blue-500 to-purple-500'
    },
    {
      icon: Smartphone,
      title: 'Multi-dispositivo',
      description: 'Ve desde cualquier dispositivo, cuando quieras',
      gradient: 'from-green-500 to-teal-500'
    }
  ]


  // Remover dados mock - usar dados reais das APIs
  
  const handleChannelClick = (channel: any) => {
    // Evitar qualquer processamento se não houver usuário
    console.log('🔐 Verificando autenticação:', { hasUser: !!user, channel: channel.name })
    
    // Verificação imediata - sem definir estados
    if (!user) {
      console.log('❌ Usuário não autenticado - redirecionando imediatamente')
      // Garantir que nenhum estado seja alterado
      setSelectedChannel(null)
      setIsPlayerOpen(false)
      // Redirecionar imediatamente
      window.location.replace('/login')
      return false
    }
    
    // Se estiver logado, abre o player
    console.log('✅ Usuário autenticado - abrindo player')
    setSelectedChannel({
      id: channel.id,
      name: channel.name,
      streamUrl: channel.url,
      isIPTV: true,
      headers: channel.headers,
      logo: channel.logo
    })
    setIsPlayerOpen(true)
  }
  
  return (
    <div className="min-h-screen bg-slate-950">
      {/* Hero Section con imagen de fondo */}
      <section className="relative min-h-[90vh] flex items-center overflow-hidden">
        {/* Background Image con overlay */}
        <div className="absolute inset-0">
          <div 
            className="absolute inset-0 bg-cover bg-center"
            style={{
              backgroundImage: `url('https://images.unsplash.com/photo-1522778119026-d647f0596c20?w=1920&h=1080&fit=crop')`,
            }}
          />
          <div className="absolute inset-0 bg-gradient-to-r from-slate-900 via-slate-900/80 to-transparent" />
          <div className="absolute inset-0 bg-gradient-to-t from-slate-950 via-transparent to-transparent" />
        </div>

        {/* Content */}
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-3xl space-y-8">
            <LiveMatchBanner />
            
            <div className="space-y-4">
              <h1 className="text-5xl md:text-7xl font-bold tracking-tight text-white">
                Todo el deporte
                <span className="block bg-gradient-to-r from-red-500 to-yellow-500 bg-clip-text text-transparent">
                  en un solo lugar
                </span>
              </h1>
              <p className="text-xl text-gray-300 max-w-2xl">
                LaLiga, Champions, F1, MotoGP y más de 100 canales deportivos en HD y 4K.
                Descarga la app y obtén <span className="text-yellow-400 font-semibold">1 mes gratis</span>.
              </p>
            </div>
            
            <div className="flex flex-col sm:flex-row gap-4">
              <Button size="lg" className="bg-red-600 hover:bg-red-700" asChild>
                <Link href="/browse">
                  <Play className="mr-2 h-5 w-5" />
                  Ver canales en vivo
                </Link>
              </Button>
              <Button size="lg" variant="outline" className="bg-white/10 backdrop-blur-sm border-white/20 text-white hover:bg-white/20" asChild>
                <Link href="/mobile-app">
                  <Smartphone className="mr-2 h-5 w-5" />
                  Descargar app
                </Link>
              </Button>
            </div>
            
            <div className="flex items-center gap-6 text-sm text-gray-400">
              <div className="flex items-center gap-2">
                <span className="flex h-2 w-2 rounded-full bg-green-500 animate-pulse" />
                15,234 en vivo
              </div>
              <div className="flex items-center gap-2">
                <Star className="h-4 w-4 text-yellow-500" />
                4.8/5 (2.5K reseñas)
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Canais IPTV Esportivos */}
      <section className="py-20 relative bg-gradient-to-b from-slate-950 to-slate-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between mb-12">
            <div>
              <h2 className="text-3xl font-bold text-white mb-2 flex items-center gap-3">
                <Tv className="h-8 w-8 text-red-500" />
                Canales Deportivos en Vivo
              </h2>
              <p className="text-gray-400">Accede a los mejores canales deportivos del mundo</p>
            </div>
            <Button variant="outline" className="text-white border-white/20 hover:bg-white/10" asChild>
              <Link href="/browse">
                Ver todos los canales
                <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
          </div>
          
          {/* Grid de canales IPTV */}
          {iptvLoading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {[...Array(8)].map((_, i) => (
                <Skeleton key={i} className="h-32 bg-slate-800/50" />
              ))}
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {iptvChannels.map((channel, index) => (
                <Card
                  key={`${channel.id}-${index}`}
                  className={`group cursor-pointer bg-slate-800/50 border-slate-700 transition-all overflow-hidden ${!user ? 'hover:border-yellow-500/50' : 'hover:border-red-500/50'}`}
                  onClick={() => handleChannelClick(channel)}
                >
                  <CardContent className="p-4 relative">
                      {!user && (
                        <div className="absolute inset-0 bg-black/40 backdrop-blur-[1px] flex items-center justify-center z-10">
                          <Lock className="h-6 w-6 text-yellow-500" />
                        </div>
                      )}
                      <div className="flex items-center gap-4">
                        {channel.logo && channel.logo !== 'null' ? (
                          <img 
                            src={channel.logo} 
                            alt={channel.name}
                            className="h-12 w-12 object-contain rounded"
                            onError={(e) => {
                              (e.target as HTMLImageElement).style.display = 'none'
                              const parent = (e.target as HTMLImageElement).parentElement
                              if (parent) {
                                const placeholder = document.createElement('div')
                                placeholder.className = 'h-12 w-12 bg-red-600/20 rounded flex items-center justify-center'
                                placeholder.innerHTML = '<svg class="h-6 w-6 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" /></svg>'
                                parent.appendChild(placeholder)
                              }
                            }}
                          />
                        ) : (
                          <div className="h-12 w-12 bg-red-600/20 rounded flex items-center justify-center">
                            <Tv className="h-6 w-6 text-red-500" />
                          </div>
                        )}
                        <div className="flex-1 min-w-0">
                          <h3 className="font-semibold text-white truncate group-hover:text-red-400 transition-colors">
                            {channel.name}
                          </h3>
                          <p className="text-sm text-gray-400">
                            {channel.country || 'Internacional'}
                          </p>
                        </div>
                        <div className="flex items-center gap-1 text-red-500">
                          <div className="h-2 w-2 bg-red-500 rounded-full animate-pulse" />
                          <span className="text-xs font-medium">LIVE</span>
                        </div>
                      </div>
                    </CardContent>
                </Card>
              ))}
            </div>
          )}
          
          {iptvChannels.length === 0 && !iptvLoading && (
            <Card className="bg-slate-800/50 border-slate-700 p-8 text-center">
              <Tv className="h-12 w-12 text-gray-500 mx-auto mb-4" />
              <p className="text-gray-400">No hay canales disponibles en este momento</p>
            </Card>
          )}
          
          {/* Mensaje para usuarios no logueados */}
          {!user && iptvChannels.length > 0 && (
            <div className="mt-8 text-center">
              <Card className="bg-yellow-500/10 border-yellow-500/30 p-6 max-w-2xl mx-auto">
                <div className="flex items-center justify-center gap-3 mb-3">
                  <Lock className="h-5 w-5 text-yellow-500" />
                  <h3 className="text-lg font-semibold text-yellow-500">Acceso Exclusivo para Miembros</h3>
                </div>
                <p className="text-gray-300 mb-4">
                  Inicia sesión para disfrutar de más de 100 canales deportivos en vivo
                </p>
                <div className="flex gap-3 justify-center">
                  <Button 
                    size="sm" 
                    className="bg-yellow-500 hover:bg-yellow-600 text-black"
                    onClick={() => router.push('/login')}
                  >
                    Iniciar Sesión
                  </Button>
                  <Button 
                    size="sm" 
                    variant="outline" 
                    className="border-yellow-500/50 text-yellow-500 hover:bg-yellow-500/10"
                    onClick={() => router.push('/register')}
                  >
                    Crear Cuenta
                  </Button>
                </div>
              </Card>
            </div>
          )}
        </div>
      </section>

      {/* Resultados ao Vivo */}
      <section className="py-20 relative bg-gradient-to-b from-slate-900 to-slate-950">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between mb-12">
            <div>
              <h2 className="text-3xl font-bold text-white mb-2 flex items-center gap-3">
                <span className="animate-pulse h-3 w-3 rounded-full bg-red-500" />
                Resultados en Vivo
              </h2>
              <p className="text-gray-400">Sigue los marcadores y resultados en tiempo real</p>
            </div>
            <Button variant="outline" className="text-white border-white/20 hover:bg-white/10" asChild>
              <Link href="/sports">
                Ver calendario completo
                <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
          </div>
          
          <LiveSportsResults />
        </div>
      </section>
      
      {/* Eventos Deportivos Globales */}
      <section className="py-20 relative bg-gradient-to-b from-slate-950 to-slate-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between mb-12">
            <div>
              <h2 className="text-3xl font-bold text-white mb-2 flex items-center gap-3">
                <Trophy className="h-8 w-8 text-yellow-500" />
                Eventos Deportivos Destacados
              </h2>
              <p className="text-gray-400">Los mejores eventos deportivos del mundo</p>
            </div>
          </div>
          
          <AllSportsEvents />
        </div>
      </section>

      {/* Nueva Sección de Deportes en Vivo */}
      <section className="py-20 bg-gradient-to-r from-red-900/10 to-orange-900/10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <Badge className="mb-4 bg-red-600 border-0 animate-pulse">
              <Activity className="mr-1 h-3 w-3" />
              NUEVO
            </Badge>
            <h2 className="text-4xl font-bold text-white mb-4">
              Centro de Deportes en Vivo
            </h2>
            <p className="text-xl text-gray-300 max-w-2xl mx-auto">
              Accede a todos los deportes en tiempo real: fútbol, F1, NBA, UFC y más.
              Estadísticas en vivo, calendario de eventos y guía de programación.
            </p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8 mb-12">
            <Card className="bg-slate-800/50 border-slate-700 hover:border-red-500/50 transition-all">
              <CardContent className="p-6 text-center">
                <div className="h-16 w-16 rounded-full bg-red-500/20 flex items-center justify-center mx-auto mb-4">
                  <Trophy className="h-8 w-8 text-red-500" />
                </div>
                <h3 className="text-xl font-semibold text-white mb-2">Resultados en Vivo</h3>
                <p className="text-gray-400">Sigue todos los partidos con estadísticas en tiempo real</p>
              </CardContent>
            </Card>
            
            <Card className="bg-slate-800/50 border-slate-700 hover:border-orange-500/50 transition-all">
              <CardContent className="p-6 text-center">
                <div className="h-16 w-16 rounded-full bg-orange-500/20 flex items-center justify-center mx-auto mb-4">
                  <Clock className="h-8 w-8 text-orange-500" />
                </div>
                <h3 className="text-xl font-semibold text-white mb-2">Calendario Global</h3>
                <p className="text-gray-400">Todos los eventos deportivos con links de transmisión</p>
              </CardContent>
            </Card>
            
            <Card className="bg-slate-800/50 border-slate-700 hover:border-yellow-500/50 transition-all">
              <CardContent className="p-6 text-center">
                <div className="h-16 w-16 rounded-full bg-yellow-500/20 flex items-center justify-center mx-auto mb-4">
                  <Tv className="h-8 w-8 text-yellow-500" />
                </div>
                <h3 className="text-xl font-semibold text-white mb-2">Guía TV Deportiva</h3>
                <p className="text-gray-400">Programación completa de todos los canales deportivos</p>
              </CardContent>
            </Card>
          </div>
          
          <div className="text-center">
            <Button size="lg" className="bg-gradient-to-r from-red-600 to-orange-600 hover:from-red-700 hover:to-orange-700" asChild>
              <Link href="/sports">
                <Trophy className="mr-2 h-5 w-5" />
                Explorar Deportes en Vivo
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>
          </div>
        </div>
      </section>

      {/* Todas as Categorias de Esportes */}
      <section className="py-16 bg-slate-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-white mb-4">
              Todos os Esportes Disponíveis
            </h2>
            <p className="text-gray-400 max-w-2xl mx-auto">
              Acompanhe eventos ao vivo e próximas partidas de todos os esportes
            </p>
          </div>
          
          {loading ? (
            <div className="space-y-16">
              {[...Array(4)].map((_, i) => (
                <div key={i}>
                  <Skeleton className="h-8 w-48 mb-6" />
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {[...Array(3)].map((_, j) => (
                      <Skeleton key={j} className="h-32 bg-slate-800/50" />
                    ))}
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="space-y-16">
              {allCategories
                .filter(({ events }) => events.length > 0)
                .map(({ category, events }) => {
                  // Determinar número de colunas baseado na quantidade de eventos
                  const gridCols = events.length >= 3 ? 'lg:grid-cols-3' : events.length === 2 ? 'lg:grid-cols-2' : 'lg:grid-cols-1'
                  
                  return (
                    <div key={category.id}>
                      <div className="flex items-center justify-between mb-6">
                        <div>
                          <h3 className="text-2xl font-bold text-white flex items-center gap-2">
                            {category.emoji} {category.name}
                          </h3>
                          <p className="text-gray-400 mt-1">
                            {events.length} {events.length === 1 ? 'evento' : 'eventos'} disponíveis
                          </p>
                        </div>
                        <Button variant="outline" className="text-white border-white/20 hover:bg-white/10" asChild>
                          <Link href={`/sports?category=${category.id}`}>
                            Ver todo
                            <ArrowRight className="ml-2 h-4 w-4" />
                          </Link>
                        </Button>
                      </div>
                      
                      <div className={`grid grid-cols-1 md:grid-cols-2 ${gridCols} gap-4`}>
                        {events.slice(0, 6).map((event) => (
                          <UpcomingEventCard key={event.id} event={event} compact />
                        ))}
                      </div>
                    </div>
                  )
                })}
              
              {allCategories.filter(({ events }) => events.length > 0).length === 0 && (
                <Card className="bg-slate-800/50 border-slate-700 p-12 text-center">
                  <Trophy className="h-16 w-16 text-gray-500 mx-auto mb-4" />
                  <p className="text-gray-400 text-lg mb-4">
                    Nenhum evento disponível no momento
                  </p>
                  <p className="text-gray-500 text-sm">
                    Os eventos serão atualizados automaticamente em breve
                  </p>
                </Card>
              )}
            </div>
          )}
        </div>
      </section>

      {/* Features con gradientes */}
      <section className="py-20 bg-gradient-to-b from-slate-900 to-slate-950">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-white mb-4">
              ¿Por qué elegir NewSpports?
            </h2>
            <p className="text-gray-400 max-w-2xl mx-auto">
              La mejor plataforma de streaming deportivo en España
            </p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8">
            {features.map((feature, index) => {
              const Icon = feature.icon
              return (
                <Card key={index} className="group cursor-pointer bg-slate-800/30 border-slate-700 hover:border-slate-600 transition-all overflow-hidden">
                  <div className={`absolute inset-0 bg-gradient-to-br ${feature.gradient} opacity-0 group-hover:opacity-10 transition-opacity`} />
                  <CardContent className="relative p-8">
                    <div className={`h-16 w-16 rounded-2xl bg-gradient-to-br ${feature.gradient} flex items-center justify-center mb-6 group-hover:scale-110 transition-transform`}>
                      <Icon className="h-8 w-8 text-white" />
                    </div>
                    <h3 className="text-xl font-semibold text-white mb-3">{feature.title}</h3>
                    <p className="text-gray-400">{feature.description}</p>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </div>
      </section>

      {/* CTA Section con imagen */}
      <section className="py-20 relative overflow-hidden">
        <div className="absolute inset-0">
          <div 
            className="absolute inset-0 bg-cover bg-center"
            style={{
              backgroundImage: `url('https://images.unsplash.com/photo-1512621776951-a57141f2eefd?w=1920&h=600&fit=crop')`,
            }}
          />
          <div className="absolute inset-0 bg-gradient-to-r from-red-900/90 to-orange-900/90" />
        </div>
        
        <div className="relative z-10 max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <Trophy className="h-16 w-16 text-yellow-400 mx-auto mb-6" />
          <h2 className="text-4xl font-bold text-white mb-4">
            Oferta especial: 1 mes gratis
          </h2>
          <p className="text-xl text-gray-100 mb-8 max-w-2xl mx-auto">
            Descarga nuestra app móvil y obtén 1 mes completamente gratis.
            Sin compromisos, cancela cuando quieras.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" className="bg-white text-red-600 hover:bg-gray-100" asChild>
              <Link href="/mobile-app">
                <Smartphone className="mr-2 h-5 w-5" />
                Descargar App
              </Link>
            </Button>
            <Button size="lg" variant="outline" className="border-white text-white hover:bg-white/10" asChild>
              <Link href="/register">
                Crear cuenta
              </Link>
            </Button>
          </div>
        </div>
      </section>

      {/* Stats con iconos */}
      <section className="py-20 bg-slate-950">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            <div className="text-center group">
              <div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-gradient-to-br from-red-500 to-orange-500 mb-4 group-hover:scale-110 transition-transform">
                <Tv className="h-8 w-8 text-white" />
              </div>
              <p className="text-4xl font-bold bg-gradient-to-r from-red-500 to-orange-500 bg-clip-text text-transparent">100+</p>
              <p className="text-gray-400">Canales</p>
            </div>
            <div className="text-center group">
              <div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-gradient-to-br from-blue-500 to-purple-500 mb-4 group-hover:scale-110 transition-transform">
                <Users className="h-8 w-8 text-white" />
              </div>
              <p className="text-4xl font-bold bg-gradient-to-r from-blue-500 to-purple-500 bg-clip-text text-transparent">50K+</p>
              <p className="text-gray-400">Usuarios activos</p>
            </div>
            <div className="text-center group">
              <div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-gradient-to-br from-green-500 to-teal-500 mb-4 group-hover:scale-110 transition-transform">
                <Zap className="h-8 w-8 text-white" />
              </div>
              <p className="text-4xl font-bold bg-gradient-to-r from-green-500 to-teal-500 bg-clip-text text-transparent">4K</p>
              <p className="text-gray-400">Calidad máxima</p>
            </div>
            <div className="text-center group">
              <div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-gradient-to-br from-yellow-500 to-red-500 mb-4 group-hover:scale-110 transition-transform">
                <Shield className="h-8 w-8 text-white" />
              </div>
              <p className="text-4xl font-bold bg-gradient-to-r from-yellow-500 to-red-500 bg-clip-text text-transparent">24/7</p>
              <p className="text-gray-400">Soporte</p>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <Footer />
      
      {/* Player Modal - Só renderizar se usuário estiver logado */}
      {selectedChannel && user && (
        <SportsPlayerModal
          isOpen={isPlayerOpen}
          onClose={() => {
            setIsPlayerOpen(false)
            setSelectedChannel(null)
          }}
          event={selectedChannel}
        />
      )}
    </div>
  )
}