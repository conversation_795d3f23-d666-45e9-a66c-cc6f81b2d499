'use client'

import { Footer } from '@/components/footer'
import { BackToHome } from '@/components/back-to-home'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Shield, Lock, Eye, Database, Globe, Users, Mail, Settings } from 'lucide-react'

export default function PrivacyPage() {
  const sections = [
    {
      icon: Database,
      title: "1. Información que Recopilamos",
      content: `Recopilamos información que usted nos proporciona directamente:

• Información de cuenta: nombre, dirección de correo electrónico, contraseña
• Información de pago: datos de tarjeta de crédito (procesados de forma segura por terceros)
• Preferencias de visualización e historial
• Información del dispositivo y datos de uso
• Comunicaciones con nuestro servicio de atención al cliente`
    },
    {
      icon: Eye,
      title: "2. Cómo Utilizamos su Información",
      content: `Utilizamos la información recopilada para:

• Proporcionar, mantener y mejorar nuestros servicios
• Procesar transacciones y enviar notificaciones relacionadas
• Personalizar su experiencia y recomendar contenido
• Responder a sus comentarios, preguntas y solicitudes
• Enviar información técnica, actualizaciones y mensajes de seguridad
• Detectar, investigar y prevenir actividades fraudulentas`
    },
    {
      icon: Users,
      title: "3. Compartir Información",
      content: `No vendemos, alquilamos ni compartimos su información personal con terceros, excepto en las siguientes circunstancias:

• Con su consentimiento explícito
• Con proveedores de servicios que trabajan en nuestro nombre
• Para cumplir con obligaciones legales
• Para proteger los derechos, propiedad o seguridad de StreamPlus España
• En caso de fusión, adquisición o venta de activos`
    },
    {
      icon: Shield,
      title: "4. Seguridad de Datos",
      content: `Implementamos medidas de seguridad técnicas y organizativas apropiadas:

• Encriptación SSL/TLS para todas las transmisiones de datos
• Almacenamiento seguro de contraseñas con hash y salt
• Acceso restringido a información personal
• Auditorías de seguridad regulares
• Cumplimiento con estándares de seguridad PCI DSS para pagos`
    },
    {
      icon: Globe,
      title: "5. Transferencias Internacionales",
      content: `Sus datos pueden ser transferidos y mantenidos en servidores ubicados fuera de su país de residencia. Al utilizar nuestro servicio, usted consiente estas transferencias. Nos aseguramos de que dichas transferencias cumplan con las leyes de protección de datos aplicables.`
    },
    {
      icon: Lock,
      title: "6. Sus Derechos",
      content: `Bajo el RGPD, usted tiene los siguientes derechos:

• Acceso: solicitar una copia de sus datos personales
• Rectificación: corregir datos inexactos o incompletos
• Eliminación: solicitar la eliminación de sus datos
• Portabilidad: recibir sus datos en un formato estructurado
• Oposición: oponerse al procesamiento de sus datos
• Limitación: solicitar la restricción del procesamiento`
    },
    {
      icon: Mail,
      title: "7. Cookies y Tecnologías Similares",
      content: `Utilizamos cookies y tecnologías similares para:

• Mantener su sesión activa
• Recordar sus preferencias
• Analizar el uso del servicio
• Prevenir fraudes

Puede gestionar las preferencias de cookies a través de la configuración de su navegador.`
    },
    {
      icon: Settings,
      title: "8. Cambios en esta Política",
      content: `Podemos actualizar esta Política de Privacidad periódicamente. Le notificaremos cualquier cambio publicando la nueva política en esta página y actualizando la fecha de "última actualización". Se recomienda revisar esta política periódicamente.`
    }
  ]

  return (
    <div className="min-h-screen bg-gradient-to-b from-slate-950 to-slate-900">
      {/* Hero Section */}
      <section className="py-16 border-b border-slate-800">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <BackToHome align="left" />
          <Badge className="mb-4 bg-purple-600 text-white">Legal</Badge>
          <h1 className="text-4xl font-bold text-white mb-4">Política de Privacidad</h1>
          <p className="text-gray-400">
            Última actualización: {new Date().toLocaleDateString('es-ES', { 
              year: 'numeric', 
              month: 'long', 
              day: 'numeric' 
            })}
          </p>
          <p className="text-gray-300 mt-4">
            En StreamPlus España, nos comprometemos a proteger su privacidad y garantizar 
            la seguridad de sus datos personales.
          </p>
        </div>
      </section>

      {/* Quick Info Cards */}
      <section className="py-8 border-b border-slate-800">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid md:grid-cols-3 gap-4">
            <Card className="bg-green-900/20 border-green-500/20">
              <CardContent className="p-4 text-center">
                <Shield className="h-8 w-8 text-green-400 mx-auto mb-2" />
                <p className="text-sm text-gray-300">Cumplimiento RGPD</p>
              </CardContent>
            </Card>
            <Card className="bg-blue-900/20 border-blue-500/20">
              <CardContent className="p-4 text-center">
                <Lock className="h-8 w-8 text-blue-400 mx-auto mb-2" />
                <p className="text-sm text-gray-300">Datos Encriptados</p>
              </CardContent>
            </Card>
            <Card className="bg-purple-900/20 border-purple-500/20">
              <CardContent className="p-4 text-center">
                <Eye className="h-8 w-8 text-purple-400 mx-auto mb-2" />
                <p className="text-sm text-gray-300">Transparencia Total</p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Content */}
      <section className="py-12">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <Card className="bg-slate-800/50 border-slate-700">
            <CardHeader>
              <CardTitle className="text-white">
                Su privacidad es importante para nosotros
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-[600px] pr-4">
                <div className="space-y-8">
                  {sections.map((section, index) => (
                    <div key={index} className="flex gap-4">
                      <div className="flex-shrink-0">
                        <div className="w-10 h-10 bg-slate-700 rounded-lg flex items-center justify-center">
                          <section.icon className="h-5 w-5 text-purple-400" />
                        </div>
                      </div>
                      <div className="flex-grow">
                        <h2 className="text-xl font-semibold text-white mb-3">
                          {section.title}
                        </h2>
                        <p className="text-gray-300 whitespace-pre-line">
                          {section.content}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </CardContent>
          </Card>

          <div className="mt-8 grid md:grid-cols-2 gap-6">
            <Card className="bg-blue-900/20 border-blue-500/20">
              <CardContent className="p-6">
                <h3 className="text-lg font-semibold text-white mb-2">Contacto de Privacidad</h3>
                <p className="text-gray-300 text-sm mb-2">
                  Para cualquier consulta sobre privacidad:
                </p>
                <a href="mailto:<EMAIL>" className="text-blue-400 hover:text-blue-300">
                  <EMAIL>
                </a>
              </CardContent>
            </Card>
            
            <Card className="bg-purple-900/20 border-purple-500/20">
              <CardContent className="p-6">
                <h3 className="text-lg font-semibold text-white mb-2">Delegado de Protección de Datos</h3>
                <p className="text-gray-300 text-sm mb-2">
                  DPO disponible en:
                </p>
                <a href="mailto:<EMAIL>" className="text-purple-400 hover:text-purple-300">
                  <EMAIL>
                </a>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  )
}