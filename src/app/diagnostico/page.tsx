'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'

export default function DiagnosticoPage() {
  const [results, setResults] = useState<any>({})
  const [loading, setLoading] = useState(false)
  
  const runDiagnostics = async () => {
    setLoading(true)
    setResults({})
    
    console.log('🔍 === INICIANDO DIAGNÓSTICO COMPLETO ===')
    
    const diagnostics = {
      iptv: { status: 'checking', data: null },
      sports: { status: 'checking', data: null },
      channels: { status: 'checking', data: null }
    }
    
    // 1. Testar IPTV-ORG
    try {
      console.log('📡 [DIAG] Testando IPTV-ORG...')
      const iptvRes = await fetch('/api/iptv-simple')
      const iptvData = await iptvRes.json()
      
      diagnostics.iptv = {
        status: iptvData.success ? 'success' : 'error',
        data: {
          total: iptvData.total,
          samples: iptvData.data?.slice(0, 3)
        }
      }
      
      console.log('✅ [DIAG] IPTV-ORG:', {
        total: iptvData.total,
        firstChannel: iptvData.data?.[0]?.name
      })
    } catch (error) {
      diagnostics.iptv = { status: 'error', data: error.message }
      console.error('❌ [DIAG] Erro IPTV:', error)
    }
    
    // 2. Testar Sports API
    try {
      console.log('🏟️ [DIAG] Testando Sports API...')
      const sportsRes = await fetch('/api/sports/live-events')
      const sportsData = await sportsRes.json()
      
      diagnostics.sports = {
        status: sportsData.success ? 'success' : 'error',
        data: {
          total: sportsData.total,
          isLive: sportsData.isLive,
          samples: sportsData.data?.slice(0, 3)
        }
      }
      
      console.log('✅ [DIAG] Sports API:', {
        total: sportsData.total,
        isLive: sportsData.isLive
      })
    } catch (error) {
      diagnostics.sports = { status: 'error', data: error.message }
      console.error('❌ [DIAG] Erro Sports:', error)
    }
    
    // 3. Verificar canais carregados
    try {
      console.log('📺 [DIAG] Verificando canais na página...')
      
      // Simular o que a página browse faz
      const [iptvResponse, fixturesResponse] = await Promise.allSettled([
        fetch('/api/iptv-simple'),
        fetch('/api/sports/live-fixtures')
      ])
      
      let iptvChannels = 0
      let liveGames = 0
      
      if (iptvResponse.status === 'fulfilled') {
        const data = await iptvResponse.value.json()
        if (data.success) {
          iptvChannels = data.data?.length || 0
        }
      }
      
      if (fixturesResponse.status === 'fulfilled') {
        const data = await fixturesResponse.value.json()
        if (data.success) {
          liveGames = data.data?.length || 0
        }
      }
      
      diagnostics.channels = {
        status: 'success',
        data: {
          iptvChannels,
          liveGames,
          total: iptvChannels + liveGames
        }
      }
      
      console.log('✅ [DIAG] Canais:', {
        iptv: iptvChannels,
        live: liveGames,
        total: iptvChannels + liveGames
      })
    } catch (error) {
      diagnostics.channels = { status: 'error', data: error.message }
      console.error('❌ [DIAG] Erro Canais:', error)
    }
    
    // 4. Verificar se é mock
    const isMock = diagnostics.iptv.data?.samples?.some((ch: any) => 
      ch.name?.includes('Demo') || 
      ch.url?.includes('example.com')
    )
    
    console.log('🎯 [DIAG] Dados são mock?', isMock ? 'SIM ❌' : 'NÃO ✅')
    
    setResults({
      ...diagnostics,
      isMock,
      timestamp: new Date().toISOString()
    })
    
    console.log('🏁 === DIAGNÓSTICO COMPLETO ===')
    setLoading(false)
  }
  
  useEffect(() => {
    // Auto-executar ao carregar
    runDiagnostics()
  }, [])
  
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success': return 'text-green-500'
      case 'error': return 'text-red-500'
      case 'checking': return 'text-yellow-500'
      default: return 'text-gray-500'
    }
  }
  
  return (
    <div className="min-h-screen bg-black p-8">
      <h1 className="text-3xl font-bold text-white mb-8">
        Diagnóstico do Sistema
      </h1>
      
      <Button 
        onClick={runDiagnostics} 
        disabled={loading}
        className="mb-8"
      >
        {loading ? 'Executando...' : 'Executar Diagnóstico'}
      </Button>
      
      <div className="grid gap-6">
        {/* IPTV-ORG */}
        <Card className="p-6 bg-gray-900 border-gray-800">
          <h2 className="text-xl font-bold text-white mb-4 flex items-center gap-2">
            IPTV-ORG API
            <span className={getStatusColor(results.iptv?.status)}>
              {results.iptv?.status === 'success' ? '✅' : 
               results.iptv?.status === 'error' ? '❌' : '⏳'}
            </span>
          </h2>
          {results.iptv?.data && (
            <div className="text-sm text-gray-300">
              <p>Total de canais: {results.iptv.data.total}</p>
              {results.iptv.data.samples && (
                <div className="mt-2">
                  <p>Amostras:</p>
                  <ul className="list-disc list-inside ml-4">
                    {results.iptv.data.samples.map((ch: any, i: number) => (
                      <li key={i}>{ch.name} - {ch.country}</li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          )}
        </Card>
        
        {/* Sports API */}
        <Card className="p-6 bg-gray-900 border-gray-800">
          <h2 className="text-xl font-bold text-white mb-4 flex items-center gap-2">
            Sports API
            <span className={getStatusColor(results.sports?.status)}>
              {results.sports?.status === 'success' ? '✅' : 
               results.sports?.status === 'error' ? '❌' : '⏳'}
            </span>
          </h2>
          {results.sports?.data && (
            <div className="text-sm text-gray-300">
              <p>Total de eventos: {results.sports.data.total}</p>
              <p>Eventos ao vivo: {results.sports.data.isLive ? 'Sim' : 'Não'}</p>
            </div>
          )}
        </Card>
        
        {/* Canais Carregados */}
        <Card className="p-6 bg-gray-900 border-gray-800">
          <h2 className="text-xl font-bold text-white mb-4 flex items-center gap-2">
            Canais na Página
            <span className={getStatusColor(results.channels?.status)}>
              {results.channels?.status === 'success' ? '✅' : 
               results.channels?.status === 'error' ? '❌' : '⏳'}
            </span>
          </h2>
          {results.channels?.data && (
            <div className="text-sm text-gray-300">
              <p>Canais IPTV: {results.channels.data.iptvChannels}</p>
              <p>Jogos ao vivo: {results.channels.data.liveGames}</p>
              <p>Total: {results.channels.data.total}</p>
            </div>
          )}
        </Card>
        
        {/* Status Geral */}
        <Card className="p-6 bg-gray-900 border-gray-800">
          <h2 className="text-xl font-bold text-white mb-4">
            Status Geral
          </h2>
          <div className="text-sm text-gray-300">
            <p>Usando dados mock: {results.isMock ? '❌ SIM' : '✅ NÃO'}</p>
            <p>Última verificação: {results.timestamp}</p>
          </div>
        </Card>
      </div>
      
      <div className="mt-8 text-sm text-gray-500">
        <p>Abra o console do navegador (F12) para ver logs detalhados</p>
      </div>
    </div>
  )
}