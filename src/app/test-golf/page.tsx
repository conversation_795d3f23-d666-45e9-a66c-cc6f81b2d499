'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'

export default function TestGolfPage() {
  const router = useRouter()
  
  useEffect(() => {
    // Redirecionar automaticamente para o canal que sabemos que funciona
    const channelId = 'iptv-30AGolfKingdom.us'
    console.log('🏌️ Redirecionando para 30A Golf Kingdom...')
    router.push(`/watch/${channelId}`)
  }, [router])
  
  return (
    <div className="min-h-screen bg-black flex items-center justify-center">
      <div className="text-center">
        <h1 className="text-2xl font-bold text-white mb-4">
          Redirecionando para 30A Golf Kingdom...
        </h1>
        <p className="text-gray-400">
          Este é o canal que está funcionando no /hls-test
        </p>
      </div>
    </div>
  )
}