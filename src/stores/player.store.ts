import { create } from 'zustand'
import { immer } from 'zustand/middleware/immer'

interface PlayerState {
  isPlaying: boolean
  volume: number
  muted: boolean
  quality: 'auto' | number
  currentTime: number
  duration: number
  buffering: boolean
}

interface PlayerActions {
  setPlaying: (playing: boolean) => void
  setVolume: (volume: number) => void
  setMuted: (muted: boolean) => void
  setQuality: (quality: 'auto' | number) => void
  setCurrentTime: (time: number) => void
  setDuration: (duration: number) => void
  setBuffering: (buffering: boolean) => void
}

export const usePlayerStore = create<PlayerState & PlayerActions>()(
  immer((set) => ({
    isPlaying: false,
    volume: 1,
    muted: false,
    quality: 'auto',
    currentTime: 0,
    duration: 0,
    buffering: false,
    
    setPlaying: (playing) =>
      set((state) => {
        state.isPlaying = playing
      }),
      
    setVolume: (volume) =>
      set((state) => {
        state.volume = volume
        state.muted = volume === 0
      }),
      
    setMuted: (muted) =>
      set((state) => {
        state.muted = muted
      }),
      
    setQuality: (quality) =>
      set((state) => {
        state.quality = quality
      }),
      
    setCurrentTime: (time) =>
      set((state) => {
        state.currentTime = time
      }),
      
    setDuration: (duration) =>
      set((state) => {
        state.duration = duration
      }),
      
    setBuffering: (buffering) =>
      set((state) => {
        state.buffering = buffering
      }),
  }))
)