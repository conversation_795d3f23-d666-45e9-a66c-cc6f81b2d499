import { create } from 'zustand'
import { immer } from 'zustand/middleware/immer'
import { createClient } from '@/lib/supabase/client'

interface TrialState {
  startTime: number | null
  timeRemaining: number
  isExpired: boolean
  hasSeenOffer: boolean
  sessionId: string | null
  trialId: string | null
  isLoading: boolean
}

interface TrialActions {
  startTrial: (userId?: string) => Promise<void>
  updateTimeRemaining: (seconds: number) => void
  expireTrial: () => void
  markOfferSeen: () => void
  reset: () => void
  resetTrial: () => void
  checkTrialStatus: (userId?: string) => Promise<void>
}

// Gerar session ID único para usuários não logados
const generateSessionId = () => {
  const id = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  console.log('[TRIAL-STORE] Generating new session ID:', id)
  return id
}

// Função para verificar se deve resetar o trial baseado no tempo decorrido
const shouldResetTrial = (startTime: number | null): boolean => {
  if (!startTime) return false
  
  // Se passou mais de 24 horas desde o último trial, permitir novo trial
  const hoursSinceStart = (Date.now() - startTime) / (1000 * 60 * 60)
  return hoursSinceStart > 24
}

export const useTrialStore = create<TrialState & TrialActions>()(
  immer((set, get) => ({
      startTime: null,
      timeRemaining: 5 * 60, // 5 minutos em segundos
      isExpired: false,
      hasSeenOffer: false,
      sessionId: null,
      trialId: null,
      isLoading: false,
      
      checkTrialStatus: async (userId?: string) => {
        console.log('[TRIAL-STORE] checkTrialStatus called', {
          userId,
          currentState: {
            startTime: get().startTime,
            timeRemaining: get().timeRemaining,
            isExpired: get().isExpired,
            sessionId: get().sessionId,
            trialId: get().trialId
          }
        })
        
        // NÃO retornar se não tem startTime - isso é esperado para novos usuários
        const currentStartTime = get().startTime
        
        if (!currentStartTime) {
          console.log('[TRIAL-STORE] No startTime - new user, will check/create trial in database')
          // Continuar execução ao invés de retornar
        }
        
        set((state) => {
          state.isLoading = true
        })
        
        try {
          const supabase = createClient()
          let sessionId = get().sessionId
          
          // Se não tem sessionId, gerar um novo
          if (!sessionId && !userId) {
            sessionId = generateSessionId()
            console.log('[TRIAL-STORE] Generated new sessionId:', sessionId)
            set((state) => {
              state.sessionId = sessionId
            })
          }
          
          // Chamar função RPC no banco de dados
          const { data, error } = await supabase
            .rpc('get_or_create_trial_session', {
              p_user_id: userId || null,
              p_session_id: !userId ? sessionId : null
            })
            .single()
          
          if (error) {
            console.error('[TRIAL-STORE] Erro ao verificar trial no banco:', error)
            set((state) => {
              state.isLoading = false
            })
            throw error
          }
          
          console.log('[TRIAL-STORE] RPC response received:', data)
          
          set((state) => {
            state.trialId = data.trial_id
            state.timeRemaining = data.time_remaining
            state.isExpired = data.is_expired
            state.isLoading = false
            
            if (!state.startTime && data.time_remaining > 0) {
              state.startTime = Date.now()
              console.log('[TRIAL-STORE] Set startTime from RPC response:', state.startTime)
            }
            
            console.log('[TRIAL-STORE] State after RPC update:', {
              trialId: state.trialId,
              timeRemaining: state.timeRemaining,
              isExpired: state.isExpired,
              startTime: state.startTime
            })
          })
        } catch (error) {
          console.error('Erro ao verificar trial:', error)
          set((state) => {
            state.isLoading = false
          })
        }
      },
      
      startTrial: async (userId?: string) => {
        console.log('[TRIAL-STORE] startTrial called', { userId })
        
        // Sempre verificar no banco de dados
        await get().checkTrialStatus(userId)
        
        console.log('[TRIAL-STORE] startTrial completed. State from database:', {
          startTime: get().startTime,
          timeRemaining: get().timeRemaining,
          isExpired: get().isExpired,
          trialId: get().trialId
        })
      },
        
      updateTimeRemaining: (seconds) =>
        set((state) => {
          const previousRemaining = state.timeRemaining
          const wasExpired = state.isExpired
          state.timeRemaining = Math.max(0, seconds) // Garantir que nunca seja negativo
          
          // Log toda atualização de tempo
          if (seconds % 10 === 0 || seconds <= 10) {
            console.log('⏱️ [TRIAL-STORE] Timer update:', {
              seconds,
              formatted: `${Math.floor(seconds / 60)}:${(seconds % 60).toString().padStart(2, '0')}`,
              isExpired: state.isExpired
            })
          }
          
          if (seconds <= 0 && !state.isExpired) {
            console.log('🚨 [TRIAL-STORE] 💥 TRIAL EXPIRED! 💥', {
              previousRemaining,
              newRemaining: seconds,
              wasExpired,
              nowExpired: true,
              timestamp: new Date().toISOString()
            })
            state.isExpired = true
            state.timeRemaining = 0 // Forçar para 0
            
            // Marcar como expirado no banco
            const supabase = createClient()
            const sessionId = get().sessionId
            
            // Tentar obter o usuário autenticado
            supabase.auth.getUser().then(({ data: { user } }) => {
              const userId = user?.id || null
              
              console.log('[TRIAL-STORE] Expiring trial in database:', {
                userId,
                sessionId,
                hasUser: !!user
              })
              
              if (userId || sessionId) {
                supabase
                  .rpc('expire_trial_session', {
                    p_user_id: userId,
                    p_session_id: !userId ? sessionId : null
                  })
                  .then(({ error }) => {
                    if (error) {
                      console.error('[TRIAL-STORE] Erro ao expirar trial no banco:', error)
                    } else {
                      console.log('[TRIAL-STORE] Trial expirado no banco com sucesso')
                    }
                  })
              }
            })
          }
        }),
        
      expireTrial: () =>
        set((state) => {
          console.log('🚫 [TRIAL-STORE] expireTrial() called - FORCING EXPIRATION')
          const wasExpired = state.isExpired
          state.isExpired = true
          state.timeRemaining = 0
          console.log('📉 [TRIAL-STORE] Trial force expired:', {
            wasExpired,
            isExpired: state.isExpired,
            timeRemaining: state.timeRemaining
          })
        }),
        
      markOfferSeen: () =>
        set((state) => {
          state.hasSeenOffer = true
        }),
        
      reset: () =>
        set((state) => {
          state.startTime = Date.now()
          state.timeRemaining = 5 * 60 // 5 minutos
          state.isExpired = false
          state.hasSeenOffer = false
          state.sessionId = null
          state.trialId = null
        }),
        
      resetTrial: () =>
        set((state) => {
          state.startTime = Date.now()
          state.timeRemaining = 5 * 60 // 5 minutos
          state.isExpired = false
          state.hasSeenOffer = false
          state.sessionId = null
          state.trialId = null
        }),
    }))
)