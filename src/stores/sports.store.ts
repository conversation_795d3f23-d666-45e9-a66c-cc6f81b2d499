import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { immer } from 'zustand/middleware/immer'
import { SportsState, Fixture, AllSportEvent, EPGProgram } from '@/types/sports'
import { footballClient, allSportDBClient, epgClient } from '@/services/api/sports'
// REMOVED: Mock data imports - using only real API data
import { sportsCacheService } from '@/services/cache/sports-cache.service'
import { makeAPIRequest } from '@/services/api/sports/request-manager'
import { UnifiedSportsService } from '@/services/api/sports/unified-sports.service'

export const useSportsStore = create<SportsState>()(
  persist(
    immer((set, get) => ({
      // State
      liveFixtures: [],
      loadingLiveFixtures: false,
      upcomingEvents: [],
      loadingUpcomingEvents: false,
      epgChannels: new Map(),
      loadingEPG: false,
      favoriteTeams: [],
      favoriteLeagues: [],
      favoriteSports: [],

      // Actions
      fetchLiveFixtures: async (sport = 'football') => {
        set((state) => {
          state.loadingLiveFixtures = true
        })

        try {
          // Usar cache com atualização a cada 15 minutos
          const fixtures = await sportsCacheService.getLiveFixtures(async () => {
            console.log(`[API CALL] Buscando fixtures ao vivo de ${sport}`)
            
            try {
              if (sport === 'football') {
                return await makeAPIRequest(
                  'api-football',
                  'live-fixtures',
                  () => footballClient.getLiveFixtures()
                )
              }
              // Se falhar ou não houver dados, usar AllSportDB como fallback
              const events = await makeAPIRequest(
                'allsportdb',
                'today-events',
                () => allSportDBClient.getTodayEvents()
              )
              return events
                .filter(e => e.sport.toLowerCase() === sport.toLowerCase())
                .map(e => ({
                  // Converter formato AllSportDB para Fixture
                  fixture: {
                    id: e.id,
                    date: e.dateFrom,
                    timestamp: new Date(e.dateFrom).getTime() / 1000,
                    venue: { name: e.location },
                    status: { long: 'Match', short: 'FT', elapsed: 90 }
                  },
                  teams: {
                    home: { name: e.name.split(' vs ')[0] || 'Home' },
                    away: { name: e.name.split(' vs ')[1] || 'Away' }
                  },
                  goals: { home: 0, away: 0 },
                  league: {
                    name: e.competition,
                    country: e.country
                  }
                } as any))
            } catch (apiError) {
              console.error('Erro na API:', apiError)
              // Se todas as APIs falharem, retornar array vazio
              // NUNCA usar dados mock em produção
              return []
            }
          })

          set((state) => {
            state.liveFixtures = fixtures
            state.loadingLiveFixtures = false
          })
        } catch (error) {
          console.error('Error fetching live fixtures:', error)
          set((state) => {
            state.loadingLiveFixtures = false
          })
        }
      },

      fetchUpcomingEvents: async (params) => {
        console.log('🚀 [SPORTS STORE] fetchUpcomingEvents chamado com params:', params)
        
        set((state) => {
          state.loadingUpcomingEvents = true
        })

        try {
          // Usar cache com atualização a cada 1 minuto para AllSportDB e 15 minutos para API Sports
          const events = await sportsCacheService.getUpcomingEvents(async () => {
            console.log('[API CALL] Buscando eventos próximos de TODOS os esportes')
            
            try {
              console.log('[API CALL] Fazendo chamada real para API de eventos')
              
              // Fazer chamada via API route
              const response = await fetch('/api/sports/upcoming-events?days=7')
              
              if (!response.ok) {
                console.error('[API ERROR] Status:', response.status, response.statusText)
                throw new Error(`HTTP error! status: ${response.status}`)
              }
              
              const data = await response.json()
              console.log('[API SUCCESS] Eventos recebidos:', data)
              
              if (data.success && data.data && data.data.length > 0) {
                const allEvents = data.data
                console.log('[API] Total de eventos:', allEvents.length)
                
                // Filtrar por esporte se especificado
                if (params?.sport) {
                  const filteredEvents = allEvents.filter(e => 
                    e.sport.toLowerCase() === params.sport.toLowerCase()
                  )
                  console.log(`[API] Eventos filtrados para ${params.sport}:`, filteredEvents.length)
                  return filteredEvents
                }
                
                return allEvents
              } else {
                console.log('[API] Nenhum evento encontrado')
                // NUNCA usar dados mock em produção
                return []
              }
            } catch (apiError) {
              console.error('Erro nas APIs:', apiError)
              // Se todas as APIs falharem, retornar array vazio
              // NUNCA usar dados mock em produção
              return []
            }
          })

          // Filtrar solo eventos futuros
          const now = new Date()
          const upcomingEvents = events.filter(event => new Date(event.dateFrom) >= now)
          
          // Ordenar por fecha
          upcomingEvents.sort((a, b) => new Date(a.dateFrom).getTime() - new Date(b.dateFrom).getTime())

          console.log(`✅ [SPORTS STORE] Atualizando estado com ${upcomingEvents.length} eventos`)
          console.log('[SPORTS STORE] Primeiros 2 eventos:', upcomingEvents.slice(0, 2))
          
          set((state) => {
            state.upcomingEvents = upcomingEvents
            state.loadingUpcomingEvents = false
          })
        } catch (error) {
          console.error('Error fetching upcoming events:', error)
          set((state) => {
            state.loadingUpcomingEvents = false
          })
        }
      },

      fetchEPGData: async (channelId: string) => {
        set((state) => {
          state.loadingEPG = true
        })

        try {
          const programs = await epgClient.getChannelPrograms(channelId)
          
          set((state) => {
            state.epgChannels.set(channelId, programs)
            state.loadingEPG = false
          })
        } catch (error) {
          console.error('Error fetching EPG data:', error)
          set((state) => {
            state.loadingEPG = false
          })
        }
      },

      toggleFavoriteTeam: (teamId: number) => {
        set((state) => {
          const index = state.favoriteTeams.indexOf(teamId)
          if (index === -1) {
            state.favoriteTeams.push(teamId)
          } else {
            state.favoriteTeams.splice(index, 1)
          }
        })
      },

      toggleFavoriteLeague: (leagueId: number) => {
        set((state) => {
          const index = state.favoriteLeagues.indexOf(leagueId)
          if (index === -1) {
            state.favoriteLeagues.push(leagueId)
          } else {
            state.favoriteLeagues.splice(index, 1)
          }
        })
      },

      toggleFavoriteSport: (sport: string) => {
        set((state) => {
          const index = state.favoriteSports.indexOf(sport)
          if (index === -1) {
            state.favoriteSports.push(sport)
          } else {
            state.favoriteSports.splice(index, 1)
          }
        })
      }
    })),
    {
      name: 'sports-storage',
      partialize: (state) => ({
        favoriteTeams: state.favoriteTeams,
        favoriteLeagues: state.favoriteLeagues,
        favoriteSports: state.favoriteSports
      })
    }
  )
)