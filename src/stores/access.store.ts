import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { immer } from 'zustand/middleware/immer'
import { createClient } from '@/lib/supabase/client'

interface AccessState {
  hasAccess: boolean
  accessType: 'trial' | 'cupom' | 'assinatura' | 'app_verified' | null
  expiresAt: Date | null
  isLoading: boolean
  lastCheck: Date | null
}

interface AccessActions {
  checkAccess: (userId: string, forceRefresh?: boolean) => Promise<void>
  applyCoupon: (userId: string, code: string) => Promise<{ success: boolean; message: string }>
  clearAccess: () => void
}

export const useAccessStore = create<AccessState & AccessActions>()(
  persist(
    immer((set, get) => ({
      hasAccess: false,
      accessType: null,
      expiresAt: null,
      isLoading: false,
      lastCheck: null,
      
      checkAccess: async (userId: string, forceRefresh: boolean = false) => {
        const timestamp = new Date().toISOString()
        console.log(`[ACCESS-STORE] ${timestamp} - ===== CHECK ACCESS CALLED =====`)
        console.log(`[ACCESS-STORE] ${timestamp} - User ID:`, userId)
        console.log(`[ACCESS-STORE] ${timestamp} - Force Refresh:`, forceRefresh)
        console.log(`[ACCESS-STORE] ${timestamp} - Current state:`, {
          hasAccess: get().hasAccess,
          accessType: get().accessType,
          expiresAt: get().expiresAt,
          lastCheck: get().lastCheck
        })
        
        // Verificar cache (máximo 5 minutos)
        const lastCheck = get().lastCheck
        if (lastCheck && !forceRefresh) {
          try {
            // Garantir que lastCheck é um Date object válido
            let lastCheckDate: Date | null = null
            
            if (lastCheck instanceof Date && !isNaN(lastCheck.getTime())) {
              lastCheckDate = lastCheck
            } else if (typeof lastCheck === 'string' || typeof lastCheck === 'number') {
              const tempDate = new Date(lastCheck)
              if (!isNaN(tempDate.getTime())) {
                lastCheckDate = tempDate
              }
            }
            
            if (!lastCheckDate) {
              throw new Error('Invalid lastCheck date')
            }
            
            const cacheAge = new Date().getTime() - lastCheckDate.getTime()
            const cacheAgeMinutes = Math.floor(cacheAge / (1000 * 60))
            console.log(`[ACCESS-STORE] ${timestamp} - Last check:`, lastCheckDate.toISOString())
            console.log(`[ACCESS-STORE] ${timestamp} - Cache age:`, cacheAgeMinutes, 'minutes')
            
            if (cacheAge < 5 * 60 * 1000) {
              // Verificar se o acesso ainda é válido
              const expiresAt = get().expiresAt
              if (expiresAt) {
                const expiresDate = expiresAt instanceof Date ? expiresAt : new Date(expiresAt)
                if (!isNaN(expiresDate.getTime()) && expiresDate < new Date()) {
                  console.log(`[ACCESS-STORE] ${timestamp} - ⚠️ Cached access expired, forcing refresh`)
                  throw new Error('Cached access expired')
                }
              }
              
              console.log(`[ACCESS-STORE] ${timestamp} - ✅ Using cached access data`)
              console.log(`[ACCESS-STORE] ${timestamp} - Cached hasAccess:`, get().hasAccess)
              console.log(`[ACCESS-STORE] ${timestamp} - Cached accessType:`, get().accessType)
              console.log(`[ACCESS-STORE] ${timestamp} - Cached expiresAt:`, get().expiresAt)
              return
            }
          } catch (error) {
            console.error(`[ACCESS-STORE] ${timestamp} - ❌ Error checking cache:`, error)
            console.log(`[ACCESS-STORE] ${timestamp} - Clearing invalid cache and forcing refresh`)
            // Se houver erro, limpar o cache e continuar com a verificação
            set((state) => {
              state.lastCheck = null
              state.hasAccess = false
              state.accessType = null
            })
          }
        }
        
        console.log(`[ACCESS-STORE] ${timestamp} - Setting loading state...`)
        set((state) => {
          state.isLoading = true
        })
        
        try {
          const supabase = createClient()
          
          // Chamar função RPC unificada que aceita Stack Auth IDs
          console.log(`[ACCESS-STORE] ${timestamp} - Calling RPC check_user_access_unified...`)
          console.log(`[ACCESS-STORE] ${timestamp} - RPC Parameters:`, { p_user_id: userId })
          
          const { data, error } = await supabase
            .rpc('check_user_access_unified', { p_user_id: userId })
            .single()
          
          console.log(`[ACCESS-STORE] ${timestamp} - RPC Raw Response:`, { data, error })
          
          if (error) {
            console.error(`[ACCESS-STORE] ${timestamp} - ❌ Error calling RPC:`, error)
            console.error(`[ACCESS-STORE] ${timestamp} - Error details:`, JSON.stringify(error, null, 2))
            set((state) => {
              state.hasAccess = false
              state.accessType = null
              state.expiresAt = null
              state.isLoading = false
            })
            return
          }
          
          console.log(`[ACCESS-STORE] ${timestamp} - ✅ RPC Response:`)
          console.log(`[ACCESS-STORE] ${timestamp} -   has_access:`, data?.has_access)
          console.log(`[ACCESS-STORE] ${timestamp} -   access_type:`, data?.access_type)
          console.log(`[ACCESS-STORE] ${timestamp} -   expires_at:`, data?.expires_at)
          console.log(`[ACCESS-STORE] ${timestamp} -   Full data:`, JSON.stringify(data, null, 2))
          
          console.log(`[ACCESS-STORE] ${timestamp} - Updating store state...`)
          set((state) => {
            state.hasAccess = data?.has_access || false
            state.accessType = data?.access_type || null
            state.expiresAt = data?.expires_at ? new Date(data.expires_at) : null
            state.lastCheck = new Date()
            state.isLoading = false
          })
          
          console.log(`[ACCESS-STORE] ${timestamp} - Store updated:`)
          console.log(`[ACCESS-STORE] ${timestamp} -   hasAccess:`, get().hasAccess)
          console.log(`[ACCESS-STORE] ${timestamp} -   accessType:`, get().accessType)
          console.log(`[ACCESS-STORE] ${timestamp} -   expiresAt:`, get().expiresAt)
          console.log(`[ACCESS-STORE] ${timestamp} -   lastCheck:`, get().lastCheck)
          console.log(`[ACCESS-STORE] ${timestamp} - ===== CHECK ACCESS COMPLETED =====`)
          
        } catch (error) {
          console.error(`[ACCESS-STORE] ${timestamp} - ❌ Unexpected error:`, error)
          console.error(`[ACCESS-STORE] ${timestamp} - Error stack:`, error instanceof Error ? error.stack : 'N/A')
          set((state) => {
            state.isLoading = false
          })
        }
      },
      
      applyCoupon: async (userId: string, code: string) => {
        try {
          const supabase = createClient()
          
          // Chamar função RPC para aplicar cupom
          const { data, error } = await supabase
            .rpc('apply_coupon', { 
              p_user_id: userId, 
              p_codigo: code.toUpperCase() 
            })
            .single()
          
          if (error) {
            console.error('Erro ao aplicar cupom:', error)
            return { 
              success: false, 
              message: 'Erro ao processar cupom. Tente novamente.' 
            }
          }
          
          if (data.success) {
            // Atualizar estado local
            set((state) => {
              state.hasAccess = true
              state.accessType = 'cupom'
              state.expiresAt = data.expires_at ? new Date(data.expires_at) : null
              state.lastCheck = new Date()
            })
          }
          
          return {
            success: data.success,
            message: data.message
          }
        } catch (error) {
          console.error('Erro ao aplicar cupom:', error)
          return { 
            success: false, 
            message: 'Erro ao processar cupom. Tente novamente.' 
          }
        }
      },
      
      clearAccess: () => {
        set((state) => {
          state.hasAccess = false
          state.accessType = null
          state.expiresAt = null
          state.lastCheck = null
        })
      }
    })),
    {
      name: 'access-storage',
      partialize: (state) => ({
        hasAccess: state.hasAccess,
        accessType: state.accessType,
        expiresAt: state.expiresAt,
        lastCheck: state.lastCheck
      }),
      // Converter strings de volta para Date ao hidratar
      onRehydrateStorage: () => (state) => {
        console.log('[ACCESS-STORE] Rehydrating from localStorage:', state)
        if (state) {
          try {
            // Função auxiliar para converter para Date de forma segura
            const safeToDate = (value: any): Date | null => {
              if (!value) return null
              
              // Se já é uma Date válida
              if (value instanceof Date && !isNaN(value.getTime())) {
                return value
              }
              
              // Se é string, tentar converter
              if (typeof value === 'string') {
                const date = new Date(value)
                if (!isNaN(date.getTime())) {
                  return date
                }
              }
              
              // Se é objeto com _seconds (Firestore timestamp)
              if (value && typeof value === 'object' && '_seconds' in value) {
                const date = new Date(value._seconds * 1000)
                if (!isNaN(date.getTime())) {
                  return date
                }
              }
              
              // Se é número (timestamp)
              if (typeof value === 'number') {
                const date = new Date(value)
                if (!isNaN(date.getTime())) {
                  return date
                }
              }
              
              console.warn('[ACCESS-STORE] Could not convert to Date:', value)
              return null
            }
            
            // Converter expiresAt com segurança
            state.expiresAt = safeToDate(state.expiresAt)
            if (state.expiresAt) {
              console.log('[ACCESS-STORE] Converted expiresAt to Date:', state.expiresAt.toISOString())
            }
            
            // Converter lastCheck com segurança
            state.lastCheck = safeToDate(state.lastCheck)
            if (state.lastCheck) {
              console.log('[ACCESS-STORE] Converted lastCheck to Date:', state.lastCheck.toISOString())
            }
            
            // Validar estado após conversão
            if (state.hasAccess && state.expiresAt) {
              const now = new Date()
              if (state.expiresAt < now) {
                console.log('[ACCESS-STORE] Access expired during offline, clearing access')
                state.hasAccess = false
                state.accessType = null
                state.expiresAt = null
              }
            }
          } catch (error) {
            console.error('[ACCESS-STORE] Error during rehydration:', error)
            // Em caso de erro, limpar TUDO para forçar nova verificação
            console.log('[ACCESS-STORE] Clearing all access data due to error')
            state.hasAccess = false
            state.accessType = null
            state.expiresAt = null
            state.lastCheck = null
          }
        }
      }
    }
  )
)