#!/usr/bin/env tsx

/**
 * Script para resetar as quotas das APIs
 * 
 * Uso:
 * - Resetar todas: pnpm reset-quotas
 * - Resetar específica: pnpm reset-quotas api-football
 */

import { requestManager } from '../services/api/sports/request-manager'

const args = process.argv.slice(2)
const apiToReset = args[0]

if (apiToReset) {
  console.log(`🔄 Resetando quota para ${apiToReset}...`)
  // Implementar reset individual
  const quotas = requestManager.getStats().quotas
  const quota = quotas.find(q => q.apiName === apiToReset)
  
  if (quota) {
    // Forçar reset através do método público
    console.log(`📊 Estado atual: ${quota.usedToday}/${quota.dailyLimit} requisições`)
    // Por enquanto, resetar todas
    requestManager.resetAllQuotas()
    console.log('✅ Quota resetada!')
  } else {
    console.error(`❌ API ${apiToReset} não encontrada`)
    console.log('APIs disponíveis:')
    quotas.forEach(q => console.log(`  - ${q.apiName}`))
  }
} else {
  console.log('🔄 Resetando TODAS as quotas...')
  requestManager.resetAllQuotas()
  console.log('✅ Todas as quotas foram resetadas!')
}

// Mostrar status
console.log('\n📊 Status das quotas:')
const stats = requestManager.getStats()
stats.quotas.forEach(quota => {
  console.log(`${quota.apiName}: ${quota.usedToday}/${quota.dailyLimit} (reset em ${stats.nextResetIn})`)
})