import { NextRequest, NextResponse } from 'next/server'
import { 
  applySecurityHeaders, 
  isProtectedRoute, 
  isAuthRoute,
  logAccess 
} from './middleware/security'

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl
  
  console.log(`\n🎯 [Middleware] Processando: ${pathname}`)
  console.log(`🎯 [Middleware] Método: ${request.method}`)
  
  // Check admin routes first
  if (pathname.startsWith('/admin')) {
    // Allow admin-login page
    if (pathname === '/admin-login') {
      return NextResponse.next()
    }

    // Check for admin cookie
    const adminAuth = request.cookies.get('admin-auth')
    
    if (!adminAuth || adminAuth.value !== 'true') {
      // Redirect to admin login
      console.log(`❌ [Middleware] Admin não autenticado - redirecionando para /admin-login`)
      return NextResponse.redirect(new URL('/admin-login', request.url))
    }
    
    console.log(`✅ [Middleware] Admin autenticado - permitindo acesso`)
  }
  
  // Aplicar headers de segurança em todas as respostas
  let response = NextResponse.next()
  response = applySecurityHeaders(response)
  
  // Rotas públicas que não requerem autenticação
  const publicRoutes = [
    '/',
    '/mobile-app',
    '/watch', // Permitir todas as rotas /watch temporariamente
    '/api/public',
    '/api/sports', // Permitir acesso às APIs de sports
    '/api/iptv-org', // Permitir acesso às APIs IPTV
    '/api/iptv-simple', // Permitir API IPTV simples
    '/api/admin-auth', // Permitir API de autenticação admin
    '/auth/callback',
    '/auth/verify',
    '/handler', // Stack Auth OAuth callbacks
    '/test-route', // Página de teste
    '/admin-login', // Página de login do admin
    '/subscription', // Página de assinatura
    '/api/admin-config', // API de configurações admin
    '/api/track-download', // API de rastreamento de downloads
    '/premio', // Página de prêmio para app
    '/api/cupons', // APIs de cupons
  ]
  
  // Verificar se é rota pública
  const isPublicRoute = publicRoutes.some(route => 
    pathname === route || pathname.startsWith(route + '/')
  )
  
  console.log(`🎯 [Middleware] Rota pública? ${isPublicRoute}`)
  
  if (isPublicRoute) {
    console.log(`✅ [Middleware] Permitindo acesso à rota pública: ${pathname}`)
    return response
  }
  
  // Para rotas protegidas - verificar autenticação
  if (isProtectedRoute(pathname)) {
    console.log(`🔒 [Middleware] Rota protegida detectada: ${pathname}`)
    
    
    // Verificar cookies de autenticação Stack Auth
    const allCookies = request.cookies.getAll();
    const stackAuthCookie = allCookies.find(cookie => 
      cookie.name.includes('stack-') || 
      cookie.name.includes('auth-session') ||
      cookie.name.includes('access-token')
    );
    
    console.log(`🍪 [Middleware] Cookies de autenticação:`, {
      'stack-auth': stackAuthCookie ? `✅ ${stackAuthCookie.name}` : '❌ Nenhum cookie Stack Auth encontrado'
    })
    
    // Debug: mostrar todos os cookies (sem valores por segurança)
    console.log(`🔍 [Middleware] Total de cookies:`, allCookies.length);
    console.log(`🔍 [Middleware] Nomes dos cookies:`, allCookies.map(c => c.name).join(', ') || 'Nenhum');
    
    if (!stackAuthCookie) {
      console.log(`❌ [Middleware] Nenhuma autenticação encontrada - redirecionando para login`)
      console.log(`❌ [Middleware] URL de origem:`, request.url);
      
      const redirectUrl = new URL('/login', request.url);
      redirectUrl.searchParams.set('redirect', pathname);
      console.log(`➡️ [Middleware] Redirecionando para:`, redirectUrl.toString());
      
      return NextResponse.redirect(redirectUrl);
    }
    
    console.log(`✅ [Middleware] Autenticação encontrada - permitindo acesso`)
  }
  
  // Redirecionar usuários autenticados de páginas de auth
  if (isAuthRoute(pathname)) {
    console.log(`🔐 [Middleware] Rota de autenticação: ${pathname}`)
    // TODO: Implementar verificação de autenticação
    // Por agora, permitir acesso
  }
  
  console.log(`✔️ [Middleware] Processamento concluído para: ${pathname}\n`)
  return response
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public (public files)
     */
    '/((?!_next/static|_next/image|favicon.ico|public).*)',
  ],
}