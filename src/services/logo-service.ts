import { CHANNEL_LOGOS, getChannelLogo } from '@/lib/channel-logos'

// Cache em memória para logos já buscados
const logoCache = new Map<string, string>()

// URLs de APIs alternativas para buscar logos
const LOGO_APIS = [
  {
    name: 'GitHub IPTV-org Logos',
    baseUrl: 'https://raw.githubusercontent.com/tv-logo/tv-logos/main/countries/',
    getUrl: (channelId: string): string => {
      // Extrair país do ID do canal (ex: ESPNews.us -> us)
      const country = channelId.split('.').pop()?.toLowerCase() || 'us'
      const channelName = channelId.split('.')[0].toLowerCase()
      return `${LOGO_APIS[0].baseUrl}${country}/${channelName}.png`
    }
  },
  {
    name: 'Lyngsat',
    baseUrl: 'https://www.lyngsat.com/logo/tv/',
    getUrl: (channelId: string): string => {
      const channelName = channelId.split('.')[0].toLowerCase()
      const firstChar = channelName[0]
      return `${LOGO_APIS[1].baseUrl}${firstChar}${firstChar}/${channelName}.png`
    }
  }
]

// Função para tentar buscar logo de várias fontes
export async function fetchChannelLogo(channelId: string): Promise<string> {
  // Verificar cache primeiro
  if (logoCache.has(channelId)) {
    return logoCache.get(channelId)!
  }

  // Tentar obter do mapeamento local primeiro
  const localLogo = CHANNEL_LOGOS[channelId]
  if (localLogo) {
    logoCache.set(channelId, localLogo)
    return localLogo
  }

  // Tentar buscar de APIs externas
  for (const api of LOGO_APIS) {
    try {
      const url = api.getUrl(channelId)
      const response = await fetch(url, { method: 'HEAD' })
      
      if (response.ok) {
        console.log(`[LOGO-SERVICE] Found logo for ${channelId} at ${api.name}`)
        logoCache.set(channelId, url)
        return url
      }
    } catch (error) {
      // Silently continue to next API
    }
  }

  // Se nada funcionar, usar a função de fallback
  const fallbackLogo = getChannelLogo(channelId)
  logoCache.set(channelId, fallbackLogo)
  return fallbackLogo
}

// Função para pré-carregar logos dos canais selecionados
export async function preloadChannelLogos(channelIds: string[]): Promise<void> {
  console.log(`[LOGO-SERVICE] Pre-loading logos for ${channelIds.length} channels`)
  
  const promises = channelIds.map(id => 
    fetchChannelLogo(id).catch(() => {
      // Se falhar, apenas usar o fallback
      const fallback = getChannelLogo(id)
      logoCache.set(id, fallback)
      return fallback
    })
  )
  
  await Promise.all(promises)
  console.log(`[LOGO-SERVICE] Pre-loading complete. Cache size: ${logoCache.size}`)
}

// Função para obter logo do cache ou buscar
export function getChannelLogoWithCache(channelId: string): string {
  // Se já está no cache, retornar imediatamente
  if (logoCache.has(channelId)) {
    return logoCache.get(channelId)!
  }
  
  // Caso contrário, retornar o padrão e buscar assincronamente
  const defaultLogo = getChannelLogo(channelId)
  
  // Buscar em background sem bloquear
  fetchChannelLogo(channelId).then(logo => {
    if (logo !== defaultLogo) {
      console.log(`[LOGO-SERVICE] Updated logo for ${channelId}`)
    }
  })
  
  return defaultLogo
}

// Exportar o cache para debug
export function getLogoCache(): Map<string, string> {
  return new Map(logoCache)
}