// Cache centralizado para APIs esportivas
interface CacheEntry {
  data: any
  timestamp: number
  expiresAt: number
}

class APICache {
  private static instance: APICache
  private cache = new Map<string, CacheEntry>()
  
  // Tempos de cache por tipo de API
  private readonly TTL = {
    apiSports: 15 * 60 * 1000,      // 15 minutos para API Sports
    allSportDB: 30 * 1000,          // 30 segundos para AllSportDB
    liveFixtures: 15 * 60 * 1000,   // 15 minutos para fixtures ao vivo
    todayEvents: 30 * 1000          // 30 segundos para eventos do dia
  }
  
  static getInstance(): APICache {
    if (!APICache.instance) {
      APICache.instance = new APICache()
    }
    return APICache.instance
  }
  
  get(key: string): any | null {
    const entry = this.cache.get(key)
    
    if (!entry) {
      console.log(`[Cache MISS] ${key} - Não encontrado no cache`)
      return null
    }
    
    if (entry.expiresAt < Date.now()) {
      console.log(`[Cache EXPIRED] ${key} - Cache expirado`)
      this.cache.delete(key)
      return null
    }
    
    const timeLeft = Math.floor((entry.expiresAt - Date.now()) / 1000)
    console.log(`[Cache HIT] ${key} - Válido por mais ${timeLeft}s`)
    return entry.data
  }
  
  set(key: string, data: any, ttl?: number): void {
    const defaultTTL = key.includes('allsport') ? this.TTL.allSportDB : this.TTL.apiSports
    const actualTTL = ttl || defaultTTL
    
    const entry: CacheEntry = {
      data,
      timestamp: Date.now(),
      expiresAt: Date.now() + actualTTL
    }
    
    this.cache.set(key, entry)
    console.log(`[Cache SET] ${key} - TTL: ${actualTTL / 1000}s`)
  }
  
  clear(key?: string): void {
    if (key) {
      this.cache.delete(key)
      console.log(`[Cache CLEAR] ${key}`)
    } else {
      this.cache.clear()
      console.log('[Cache CLEAR] Todo o cache limpo')
    }
  }
  
  getStats(): { key: string; size: number; expiresIn: number }[] {
    const now = Date.now()
    return Array.from(this.cache.entries()).map(([key, entry]) => ({
      key,
      size: JSON.stringify(entry.data).length,
      expiresIn: Math.max(0, Math.floor((entry.expiresAt - now) / 1000))
    }))
  }
}

export const apiCache = APICache.getInstance()