import { Fixture, AllSportEvent } from '@/types/sports'
import { makeAPIRequest } from '@/services/api/sports/request-manager'

interface CacheEntry<T> {
  data: T
  timestamp: number
  expiresAt: number
}

interface CacheConfig {
  ttl: number // Time to live in milliseconds
  key: string
}

class SportsCacheService {
  private cache: Map<string, CacheEntry<any>> = new Map()
  private updateTimers: Map<string, NodeJS.Timeout> = new Map()
  
  // Configurações de cache para diferentes tipos de dados
  private cacheConfigs: Record<string, CacheConfig> = {
    liveFixtures: {
      ttl: 15 * 60 * 1000, // 15 minutos (API Sports)
      key: 'sports:live:fixtures'
    },
    upcomingEvents: {
      ttl: 30 * 1000, // 30 segundos (AllSportDB)
      key: 'sports:upcoming:events'
    },
    todayEvents: {
      ttl: 30 * 1000, // 30 segundos (AllSportDB)
      key: 'sports:today:events'
    },
    allSportLive: {
      ttl: 30 * 1000, // 30 segundos (AllSportDB)
      key: 'sports:allsport:live'
    },
    leagueStandings: {
      ttl: 60 * 60 * 1000, // 1 hora (menos crítico)
      key: 'sports:standings'
    },
    teamStats: {
      ttl: 24 * 60 * 60 * 1000, // 24 horas (muda pouco)
      key: 'sports:team:stats'
    }
  }

  // Pegar dados do cache ou buscar se expirado
  async get<T>(
    key: string,
    fetcher: () => Promise<T>,
    config?: Partial<CacheConfig>
  ): Promise<T> {
    const cacheKey = config?.key || key
    const ttl = config?.ttl || 15 * 60 * 1000 // 15 min padrão
    
    const cached = this.cache.get(cacheKey)
    
    // Se existe no cache e não expirou, retorna
    if (cached && cached.expiresAt > Date.now()) {
      console.log(`[Cache HIT] ${cacheKey} - Próxima atualização em ${this.getTimeUntilUpdate(cached.expiresAt)}`)
      return cached.data as T
    }
    
    // Se não existe ou expirou, busca novos dados
    console.log(`[Cache MISS] ${cacheKey} - Buscando dados da API...`)
    
    try {
      const data = await fetcher()
      
      // Salva no cache
      this.set(cacheKey, data, ttl)
      
      // Agenda próxima atualização automática
      this.scheduleUpdate(cacheKey, fetcher, ttl)
      
      return data
    } catch (error) {
      console.error(`[Cache ERROR] ${cacheKey}:`, error)
      
      // Se houver erro e tivermos dados antigos, retorna eles
      if (cached) {
        console.log(`[Cache FALLBACK] ${cacheKey} - Retornando dados antigos devido a erro`)
        return cached.data as T
      }
      
      throw error
    }
  }

  // Salvar dados no cache
  private set<T>(key: string, data: T, ttl: number): void {
    const timestamp = Date.now()
    const expiresAt = timestamp + ttl
    
    this.cache.set(key, {
      data,
      timestamp,
      expiresAt
    })
    
    console.log(`[Cache SET] ${key} - Expira em ${new Date(expiresAt).toLocaleTimeString()}`)
  }

  // Agendar atualização automática
  private scheduleUpdate<T>(
    key: string,
    fetcher: () => Promise<T>,
    ttl: number
  ): void {
    // Cancela timer anterior se existir
    const existingTimer = this.updateTimers.get(key)
    if (existingTimer) {
      clearTimeout(existingTimer)
    }
    
    // Agenda nova atualização
    const timer = setTimeout(async () => {
      console.log(`[Cache AUTO-UPDATE] ${key} - Iniciando atualização automática`)
      
      try {
        const data = await fetcher()
        this.set(key, data, ttl)
        
        // Reagenda próxima atualização
        this.scheduleUpdate(key, fetcher, ttl)
      } catch (error) {
        console.error(`[Cache AUTO-UPDATE ERROR] ${key}:`, error)
        
        // Tenta novamente em 1 minuto se falhar
        setTimeout(() => this.scheduleUpdate(key, fetcher, ttl), 60 * 1000)
      }
    }, ttl)
    
    this.updateTimers.set(key, timer)
  }

  // Limpar cache específico
  clear(key: string): void {
    this.cache.delete(key)
    
    const timer = this.updateTimers.get(key)
    if (timer) {
      clearTimeout(timer)
      this.updateTimers.delete(key)
    }
  }

  // Limpar todo o cache
  clearAll(): void {
    this.cache.clear()
    
    // Cancela todos os timers
    this.updateTimers.forEach(timer => clearTimeout(timer))
    this.updateTimers.clear()
  }

  // Obter estatísticas do cache
  getStats(): {
    totalEntries: number
    entries: Array<{
      key: string
      size: number
      age: string
      expiresIn: string
    }>
  } {
    const now = Date.now()
    const entries = Array.from(this.cache.entries()).map(([key, entry]) => ({
      key,
      size: JSON.stringify(entry.data).length,
      age: this.formatDuration(now - entry.timestamp),
      expiresIn: this.formatDuration(entry.expiresAt - now)
    }))
    
    return {
      totalEntries: this.cache.size,
      entries
    }
  }

  // Helpers
  private getTimeUntilUpdate(expiresAt: number): string {
    const ms = expiresAt - Date.now()
    return this.formatDuration(ms)
  }

  private formatDuration(ms: number): string {
    if (ms < 0) return 'expirado'
    
    const seconds = Math.floor(ms / 1000)
    const minutes = Math.floor(seconds / 60)
    const hours = Math.floor(minutes / 60)
    
    if (hours > 0) {
      return `${hours}h ${minutes % 60}m`
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`
    } else {
      return `${seconds}s`
    }
  }

  // Métodos específicos para cada tipo de dado
  async getLiveFixtures(fetcher: () => Promise<Fixture[]>): Promise<Fixture[]> {
    return this.get(
      'liveFixtures',
      fetcher,
      this.cacheConfigs.liveFixtures
    )
  }

  async getUpcomingEvents(fetcher: () => Promise<AllSportEvent[]>): Promise<AllSportEvent[]> {
    return this.get(
      'upcomingEvents',
      fetcher,
      this.cacheConfigs.upcomingEvents
    )
  }

  async getTodayEvents(fetcher: () => Promise<AllSportEvent[]>): Promise<AllSportEvent[]> {
    return this.get(
      'todayEvents',
      fetcher,
      this.cacheConfigs.todayEvents
    )
  }

  // Forçar atualização (ignora cache)
  async forceUpdate<T>(key: string, fetcher: () => Promise<T>): Promise<T> {
    console.log(`[Cache FORCE UPDATE] ${key}`)
    this.clear(key)
    return this.get(key, fetcher)
  }
}

// Singleton instance
export const sportsCacheService = new SportsCacheService()

// Hook para usar em componentes React
export function useSportsCache() {
  return {
    getLiveFixtures: sportsCacheService.getLiveFixtures.bind(sportsCacheService),
    getUpcomingEvents: sportsCacheService.getUpcomingEvents.bind(sportsCacheService),
    getTodayEvents: sportsCacheService.getTodayEvents.bind(sportsCacheService),
    forceUpdate: sportsCacheService.forceUpdate.bind(sportsCacheService),
    clearCache: sportsCacheService.clearAll.bind(sportsCacheService),
    getCacheStats: sportsCacheService.getStats.bind(sportsCacheService)
  }
}