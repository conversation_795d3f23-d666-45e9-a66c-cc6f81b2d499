/**
 * Serviço para integração com IPTV-ORG API
 * Documentação: https://github.com/iptv-org/iptv
 */

interface IPTVStream {
  channel: string
  url: string
  quality?: string
  referrer?: string
  user_agent?: string
  http_referrer?: string
  http_user_agent?: string
}

interface IPTVChannel {
  id: string
  name: string
  alt_names?: string[]
  network?: string
  owners?: string[]
  country?: string
  subdivision?: string
  city?: string
  broadcast_area?: string[]
  languages?: string[]
  categories?: string[]
  is_nsfw?: boolean
  launched?: string
  closed?: string
  replaced_by?: string
  website?: string
  logo?: string
}

interface IPTVGuide {
  channel: string
  site: string
  lang: string
  url: string
}

interface EPGProgram {
  start: string
  stop: string
  title: string
  description?: string
  category?: string[]
  icon?: string
  episodeNum?: string
  rating?: {
    system: string
    value: string
  }
}

class IPTVOrgService {
  private baseUrl = 'https://iptv-org.github.io/api'
  private cacheTime = 30 * 60 * 1000 // 30 minutos
  private cache = new Map<string, { data: any; timestamp: number }>()

  /**
   * Busca todos os streams disponíveis
   */
  async getStreams(): Promise<IPTVStream[]> {
    const cacheKey = 'streams'
    const cached = this.getFromCache(cacheKey)
    if (cached) return cached

    try {
      const response = await fetch(`${this.baseUrl}/streams.json`)
      if (!response.ok) throw new Error('Failed to fetch streams')
      
      const data = await response.json()
      this.setCache(cacheKey, data)
      return data
    } catch (error) {
      console.error('Error fetching IPTV streams:', error)
      return []
    }
  }

  /**
   * Busca streams esportivos
   */
  async getSportsStreams(): Promise<IPTVStream[]> {
    const [streams, channels] = await Promise.all([
      this.getStreams(),
      this.getChannels()
    ])

    // Criar mapa de canais por ID
    const channelsMap = new Map<string, IPTVChannel>()
    channels.forEach(ch => channelsMap.set(ch.id, ch))

    // Filtrar streams que pertencem a canais esportivos
    const sportsStreams = streams.filter(stream => {
      const channel = channelsMap.get(stream.channel)
      return channel?.categories?.includes('sports')
    })

    // Remover duplicatas, mantendo o stream de melhor qualidade
    const streamsByChannel = new Map<string, IPTVStream>()
    
    console.log(`📊 Total sports streams before deduplication: ${sportsStreams.length}`)
    
    sportsStreams.forEach(stream => {
      const existing = streamsByChannel.get(stream.channel)
      
      // Se não existe ou o novo tem melhor qualidade, substituir
      if (!existing || this.compareQuality(stream.quality, existing.quality) > 0) {
        streamsByChannel.set(stream.channel, stream)
      }
    })

    const dedupedStreams = Array.from(streamsByChannel.values())
    console.log(`✅ Streams after deduplication: ${dedupedStreams.length}`)
    
    return dedupedStreams
  }

  /**
   * Compara qualidades de stream
   * @returns 1 se a > b, -1 se a < b, 0 se iguais
   */
  private compareQuality(a?: string, b?: string): number {
    const qualityOrder = ['4k', '1080p', '720p', '480p', '360p', '240p']
    const aIndex = a ? qualityOrder.findIndex(q => q === a.toLowerCase()) : 999
    const bIndex = b ? qualityOrder.findIndex(q => q === b.toLowerCase()) : 999
    
    if (aIndex === -1) return bIndex === -1 ? 0 : -1
    if (bIndex === -1) return 1
    
    return bIndex - aIndex
  }

  /**
   * Busca metadados dos canais
   */
  async getChannels(): Promise<IPTVChannel[]> {
    const cacheKey = 'channels'
    const cached = this.getFromCache(cacheKey)
    if (cached) return cached

    try {
      const response = await fetch(`${this.baseUrl}/channels.json`)
      if (!response.ok) throw new Error('Failed to fetch channels')
      
      const data = await response.json()
      this.setCache(cacheKey, data)
      return data
    } catch (error) {
      console.error('Error fetching IPTV channels:', error)
      return []
    }
  }

  /**
   * Busca canais esportivos
   */
  async getSportsChannels(): Promise<IPTVChannel[]> {
    const channels = await this.getChannels()
    return channels.filter(ch => ch.categories?.includes('sports'))
  }

  /**
   * Busca informações de EPG (guia de programação)
   */
  async getGuides(): Promise<IPTVGuide[]> {
    const cacheKey = 'guides'
    const cached = this.getFromCache(cacheKey)
    if (cached) return cached

    try {
      const response = await fetch(`${this.baseUrl}/guides.json`)
      if (!response.ok) throw new Error('Failed to fetch guides')
      
      const data = await response.json()
      this.setCache(cacheKey, data)
      return data
    } catch (error) {
      console.error('Error fetching IPTV guides:', error)
      return []
    }
  }

  /**
   * Busca programação EPG de um canal específico
   */
  async getChannelEPG(channelId: string): Promise<EPGProgram[]> {
    const guides = await this.getGuides()
    const guide = guides.find(g => g.channel === channelId)
    
    if (!guide) return []

    try {
      // EPG URLs geralmente são XML, precisaríamos parsear
      // Por enquanto retornamos array vazio
      // TODO: Implementar parser XML para EPG
      return []
    } catch (error) {
      console.error('Error fetching channel EPG:', error)
      return []
    }
  }

  /**
   * Busca streams por país
   */
  async getStreamsByCountry(countryCode: string): Promise<IPTVStream[]> {
    const [streams, channels] = await Promise.all([
      this.getSportsStreams(),
      this.getSportsChannels()
    ])

    // Criar mapa de canais por ID
    const channelsMap = new Map<string, IPTVChannel>()
    channels.forEach(ch => channelsMap.set(ch.id, ch))

    // Filtrar streams por país
    return streams.filter(stream => {
      const channel = channelsMap.get(stream.channel)
      return channel?.country === countryCode.toUpperCase()
    })
  }

  /**
   * Busca streams por qualidade
   */
  async getStreamsByQuality(quality: string): Promise<IPTVStream[]> {
    const streams = await this.getSportsStreams()
    return streams.filter(s => s.quality === quality)
  }

  /**
   * Busca um stream específico por ID do canal
   */
  async getStreamByChannelId(channelId: string): Promise<IPTVStream | null> {
    const streams = await this.getStreams()
    return streams.find(s => s.channel === channelId) || null
  }

  /**
   * Verifica se um stream está ativo
   */
  async checkStreamHealth(url: string): Promise<boolean> {
    try {
      const response = await fetch(url, {
        method: 'HEAD',
        signal: AbortSignal.timeout(5000)
      })
      return response.ok
    } catch {
      return false
    }
  }

  /**
   * Busca canais populares (ESPN, Fox Sports, etc)
   */
  async getPopularSportsChannels(): Promise<IPTVChannel[]> {
    const channels = await this.getSportsChannels()
    
    const popularNetworks = [
      'ESPN', 'Fox Sports', 'beIN Sports', 'Sky Sports', 
      'TNT Sports', 'DAZN', 'Eurosport', 'Sport TV',
      'Premier Sports', 'BT Sport', 'NBC Sports'
    ]
    
    return channels.filter(ch => 
      popularNetworks.some(network => 
        ch.name.toLowerCase().includes(network.toLowerCase()) ||
        ch.network?.toLowerCase().includes(network.toLowerCase())
      )
    )
  }

  // Métodos auxiliares de cache
  private getFromCache(key: string): any {
    const cached = this.cache.get(key)
    if (!cached) return null
    
    if (Date.now() - cached.timestamp > this.cacheTime) {
      this.cache.delete(key)
      return null
    }
    
    return cached.data
  }

  private setCache(key: string, data: any): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now()
    })
  }

  /**
   * Limpa o cache
   */
  clearCache(): void {
    this.cache.clear()
  }
}

export const iptvOrgService = new IPTVOrgService()
export type { IPTVStream, IPTVChannel, IPTVGuide, EPGProgram }