import { EPGProgram } from '@/types/sports'

const BASE_URL = 'https://epg.pw/api/epg.json'

// Canais esportivos populares
export const SPORTS_CHANNELS = [
  { id: '12476', name: 'BBC R n Gael', category: 'sports' },
  { id: '13047', name: 'ESPN', category: 'sports' },
  { id: '13048', name: 'ESPN 2', category: 'sports' },
  { id: '13049', name: 'Fox Sports', category: 'sports' },
  { id: '13050', name: 'Sky Sports', category: 'sports' },
  { id: '13051', name: 'BT Sport', category: 'sports' },
  { id: '13052', name: 'Eurosport', category: 'sports' },
  { id: '13053', name: 'DAZN', category: 'sports' },
  { id: '13054', name: 'beIN Sports', category: 'sports' },
  { id: '13055', name: 'Movistar LaLiga', category: 'football' },
  { id: '13056', name: 'Movistar Liga de Campeones', category: 'football' },
  { id: '13057', name: 'Movistar Deportes', category: 'sports' },
  { id: '13058', name: 'TUDN', category: 'sports' },
  { id: '13059', name: 'TNT Sports', category: 'sports' },
  { id: '13060', name: 'Sport TV', category: 'sports' }
]

export class EPGClient {
  private cache: Map<string, { data: EPGProgram[], timestamp: number }> = new Map()
  private cacheTimeout = 30 * 60 * 1000 // 30 minutos

  async getChannelPrograms(channelId: string): Promise<EPGProgram[]> {
    // Verificar cache
    const cached = this.cache.get(channelId)
    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      return cached.data
    }

    try {
      const response = await fetch(`${BASE_URL}?channel_id=${channelId}`)
      
      if (!response.ok) {
        throw new Error(`EPG error: ${response.status} ${response.statusText}`)
      }

      const data: EPGProgram[] = await response.json()
      
      // Guardar en cache
      this.cache.set(channelId, {
        data,
        timestamp: Date.now()
      })

      return data
    } catch (error) {
      console.error('Error fetching EPG data:', error)
      // Retornar cache antiguo si existe
      if (cached) {
        return cached.data
      }
      throw error
    }
  }

  // Obtener programación actual
  async getCurrentProgram(channelId: string): Promise<EPGProgram | null> {
    const programs = await this.getChannelPrograms(channelId)
    const now = new Date()

    for (let i = 0; i < programs.length - 1; i++) {
      const start = new Date(programs[i].start)
      const nextStart = new Date(programs[i + 1].start)
      
      if (now >= start && now < nextStart) {
        return programs[i]
      }
    }

    // Si es el último programa del día
    if (programs.length > 0) {
      const lastProgram = programs[programs.length - 1]
      const lastStart = new Date(lastProgram.start)
      if (now >= lastStart) {
        return lastProgram
      }
    }

    return null
  }

  // Obtener próximos programas
  async getUpcomingPrograms(channelId: string, limit: number = 5): Promise<EPGProgram[]> {
    const programs = await this.getChannelPrograms(channelId)
    const now = new Date()

    const upcoming = programs.filter(program => {
      const start = new Date(program.start)
      return start > now
    })

    return upcoming.slice(0, limit)
  }

  // Obtener programación de múltiples canales
  async getMultiChannelPrograms(channelIds: string[]): Promise<Map<string, EPGProgram[]>> {
    const results = new Map<string, EPGProgram[]>()
    
    // Usar Promise.all para cargar en paralelo
    const promises = channelIds.map(async (channelId) => {
      try {
        const programs = await this.getChannelPrograms(channelId)
        return { channelId, programs }
      } catch (error) {
        console.error(`Error loading channel ${channelId}:`, error)
        return { channelId, programs: [] }
      }
    })

    const channelData = await Promise.all(promises)
    
    channelData.forEach(({ channelId, programs }) => {
      results.set(channelId, programs)
    })

    return results
  }

  // Buscar programas por título
  async searchPrograms(searchTerm: string, channelIds?: string[]): Promise<Array<{
    channelId: string
    channelName: string
    program: EPGProgram
  }>> {
    const channels = channelIds || SPORTS_CHANNELS.map(ch => ch.id)
    const results: Array<{ channelId: string; channelName: string; program: EPGProgram }> = []
    
    const searchLower = searchTerm.toLowerCase()
    
    for (const channelId of channels) {
      try {
        const programs = await this.getChannelPrograms(channelId)
        const channel = SPORTS_CHANNELS.find(ch => ch.id === channelId)
        
        programs.forEach(program => {
          if (program.title.toLowerCase().includes(searchLower)) {
            results.push({
              channelId,
              channelName: channel?.name || 'Unknown Channel',
              program
            })
          }
        })
      } catch (error) {
        console.error(`Error searching channel ${channelId}:`, error)
      }
    }

    return results
  }

  // Obtener programación por categoría (deporte específico)
  async getProgramsByCategory(category: string): Promise<Array<{
    channelId: string
    channelName: string
    programs: EPGProgram[]
  }>> {
    const results: Array<{ channelId: string; channelName: string; programs: EPGProgram[] }> = []
    const categoryLower = category.toLowerCase()
    
    for (const channel of SPORTS_CHANNELS) {
      try {
        const programs = await this.getChannelPrograms(channel.id)
        
        // Filtrar programas que coincidan con la categoría
        const filteredPrograms = programs.filter(program => {
          const titleLower = program.title.toLowerCase()
          return titleLower.includes(categoryLower) ||
                 (program.category && program.category.toLowerCase().includes(categoryLower))
        })
        
        if (filteredPrograms.length > 0) {
          results.push({
            channelId: channel.id,
            channelName: channel.name,
            programs: filteredPrograms
          })
        }
      } catch (error) {
        console.error(`Error loading channel ${channel.id}:`, error)
      }
    }

    return results
  }

  // Limpiar cache
  clearCache(): void {
    this.cache.clear()
  }
}

// Export singleton instance
export const epgClient = new EPGClient()