/**
 * Gerenciador de Fila para Requisições de API
 * 
 * Garante que as requisições respeitem os limites de taxa:
 * - API Sports: 10 requisições por minuto (1 a cada 6 segundos)
 * - AllSportsDB: sem limite específico de taxa
 */

interface QueueItem {
  id: string
  api: string
  fn: () => Promise<any>
  resolve: (value: any) => void
  reject: (error: any) => void
  timestamp: number
}

class QueueManager {
  private queue: QueueItem[] = []
  private processing = false
  private lastApiSportsRequest = 0
  private readonly API_SPORTS_DELAY = 6000 // 6 segundos entre requisições (10/min)
  
  /**
   * Adiciona uma requisição à fila
   */
  async enqueue<T>(api: string, fn: () => Promise<T>): Promise<T> {
    return new Promise((resolve, reject) => {
      const item: QueueItem = {
        id: `${api}-${Date.now()}-${Math.random()}`,
        api,
        fn,
        resolve,
        reject,
        timestamp: Date.now()
      }
      
      this.queue.push(item)
      console.log(`📥 Adicionado à fila: ${api} (${this.queue.length} itens na fila)`)
      
      // Iniciar processamento se não estiver rodando
      if (!this.processing) {
        this.processQueue()
      }
    })
  }
  
  /**
   * Processa a fila respeitando os limites de taxa
   */
  private async processQueue() {
    if (this.processing || this.queue.length === 0) {
      return
    }
    
    this.processing = true
    
    while (this.queue.length > 0) {
      const item = this.queue.shift()!
      
      try {
        // Verificar se é API Sports e aplicar delay se necessário
        if (item.api.startsWith('api-')) {
          const timeSinceLastRequest = Date.now() - this.lastApiSportsRequest
          const remainingDelay = this.API_SPORTS_DELAY - timeSinceLastRequest
          
          if (remainingDelay > 0) {
            console.log(`⏱️ Aguardando ${Math.ceil(remainingDelay / 1000)}s para próxima requisição API Sports...`)
            await new Promise(resolve => setTimeout(resolve, remainingDelay))
          }
          
          this.lastApiSportsRequest = Date.now()
        }
        
        console.log(`🚀 Processando: ${item.api}`)
        const result = await item.fn()
        item.resolve(result)
        
      } catch (error) {
        console.error(`❌ Erro ao processar ${item.api}:`, error)
        item.reject(error)
      }
      
      // Pequeno delay entre requisições diferentes
      if (this.queue.length > 0) {
        await new Promise(resolve => setTimeout(resolve, 100))
      }
    }
    
    this.processing = false
    console.log('✅ Fila processada')
  }
  
  /**
   * Retorna o status da fila
   */
  getStatus() {
    return {
      queueLength: this.queue.length,
      processing: this.processing,
      items: this.queue.map(item => ({
        api: item.api,
        waitingTime: Date.now() - item.timestamp
      }))
    }
  }
  
  /**
   * Limpa a fila
   */
  clear() {
    this.queue = []
    console.log('🗑️ Fila limpa')
  }
}

// Singleton
export const queueManager = new QueueManager()

// Wrapper para requisições com fila
export async function queuedRequest<T>(
  api: string,
  fn: () => Promise<T>
): Promise<T> {
  return queueManager.enqueue(api, fn)
}