import { AllSportEvent, AllSportSport, AllSportCompetition, AllSportLocation } from '@/types/sports'

const API_KEY = process.env.ALLSPORT_DB_KEY || process.env.ALLSPORTDB_API_KEY || '20d8be22-0948-4131-ae61-37dd981028bd'
const BASE_URL = 'https://api.allsportdb.com/v3'

export class AllSportDBClient {
  private async request<T>(endpoint: string, params?: Record<string, string | number>): Promise<T> {
    const url = new URL(`${BASE_URL}${endpoint}`)
    
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          url.searchParams.append(key, value.toString())
        }
      })
    }

    const response = await fetch(url.toString(), {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'Authorization': `Bearer ${API_KEY}`
      }
    })

    if (!response.ok) {
      throw new Error(`AllSportDB error: ${response.status} ${response.statusText}`)
    }

    const data = await response.json()
    return data
  }

  // Calendar/Events
  async getEvents(params?: {
    week?: number
    dateFrom?: string
    dateTo?: string
    dateToday?: string
    year?: number
    eventId?: number
    event?: string
    locationId?: number
    location?: string
    competitionId?: number
    competition?: string
    sportId?: number
    sport?: string
    countryId?: number
    country?: string
    regionId?: number
    region?: string
    continentId?: number
    continent?: string
    locationContinentId?: number
    locationContinent?: string
    liveUrlExists?: 0 | 1
    filter?: string
    page?: number
  }): Promise<AllSportEvent[]> {
    return this.request<AllSportEvent[]>('/calendar', params)
  }

  // Get today's events
  async getTodayEvents(): Promise<AllSportEvent[]> {
    const today = new Date().toISOString().split('T')[0]
    return this.getEvents({
      dateFrom: today,
      dateTo: today
    })
  }

  // Get events with live streams
  async getLiveStreamEvents(): Promise<AllSportEvent[]> {
    return this.getEvents({
      liveUrlExists: 1,
      dateFrom: new Date().toISOString().split('T')[0],
      dateTo: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] // Next 7 days
    })
  }
  
  // Get live events happening now
  async getLiveEvents(): Promise<AllSportEvent[]> {
    const now = new Date()
    const today = now.toISOString().split('T')[0]
    
    // Buscar eventos de hoje
    const todayEvents = await this.getEvents({
      dateFrom: today,
      dateTo: today
    })
    
    // Filtrar apenas eventos que estão acontecendo agora
    return todayEvents.filter(event => {
      const eventDate = new Date(event.dateFrom)
      const eventEnd = event.dateTo ? new Date(event.dateTo) : new Date(eventDate.getTime() + 3 * 60 * 60 * 1000) // Assumir 3 horas de duração
      
      // Verificar se o evento está dentro do horário atual
      return now >= eventDate && now <= eventEnd
    })
  }

  // Sports
  async getSports(params?: {
    id?: number
    name?: string
    season?: 'summer' | 'winter' | 'other'
    page?: number
  }): Promise<AllSportSport[]> {
    return this.request<AllSportSport[]>('/sports', params)
  }

  // Competitions
  async getCompetitions(params?: {
    id?: number
    name?: string
    sportId?: number
    sport?: string
    continentId?: number
    continent?: string
    page?: number
  }): Promise<AllSportCompetition[]> {
    return this.request<AllSportCompetition[]>('/competitions', params)
  }

  // Countries
  async getCountries(params?: {
    id?: number
    name?: string
    continentId?: number
    continent?: string
    page?: number
  }): Promise<unknown[]> {
    return this.request<unknown[]>('/countries', params)
  }

  // Continents
  async getContinents(params?: {
    id?: number
    name?: string
    page?: number
  }): Promise<unknown[]> {
    return this.request<unknown[]>('/continents', params)
  }

  // Locations
  async getLocations(params?: {
    id?: number
    name?: string
    countryId?: number
    country?: string
    regionId?: number
    region?: string
  }): Promise<AllSportLocation[]> {
    return this.request<AllSportLocation[]>('/locations', params)
  }

  // Helper methods for common queries
  async getEventsBySport(sport: string, dateFrom?: string, dateTo?: string): Promise<AllSportEvent[]> {
    return this.getEvents({
      sport,
      dateFrom: dateFrom || new Date().toISOString().split('T')[0],
      dateTo: dateTo || new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
    })
  }

  async getEventsByCountry(country: string): Promise<AllSportEvent[]> {
    return this.getEvents({
      country,
      dateFrom: new Date().toISOString().split('T')[0],
      dateTo: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] // Next 30 days
    })
  }

  async getUpcomingEvents(days: number = 7): Promise<AllSportEvent[]> {
    const dateFrom = new Date().toISOString().split('T')[0]
    const dateTo = new Date(Date.now() + days * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
    
    return this.getEvents({
      dateFrom,
      dateTo,
      liveUrlExists: 1 // Only events with live streams
    })
  }

  // Get events by specific date
  async getEventsByDate(date: string): Promise<AllSportEvent[]> {
    return this.getEvents({
      dateFrom: date,
      dateTo: date
    })
  }
}

// Export singleton instance
export const allSportDBClient = new AllSportDBClient()