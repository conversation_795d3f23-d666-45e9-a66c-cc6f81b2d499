import { Fixture, Event } from '@/types/sports'
import { DEMO_STREAMS, getStreamForChannel } from '@/config/demo-streams'

export interface LiveStream {
  id: string
  name: string
  url: string
  type: 'hls' | 'dash' | 'iframe'
  quality?: string
  language?: string
  geo?: string[]
}

// Usar streams de demonstração que funcionam
const getRandomDemoStream = () => {
  const streams = DEMO_STREAMS.sports
  return streams[Math.floor(Math.random() * streams.length)]
}

class LiveStreamsService {
  /**
   * Busca stream para uma partida de futebol
   */
  async getFootballStream(fixture: Fixture): Promise<LiveStream | null> {
    const leagueName = fixture.league.name.toLowerCase()
    
    // Usar stream de demonstração baseado na liga
    const demoStream = getRandomDemoStream()
    
    return {
      id: fixture.fixture.id.toString(),
      name: `${fixture.teams.home.name} vs ${fixture.teams.away.name}`,
      url: demoStream.url,
      type: 'hls',
      quality: 'HD',
      language: 'es'
    }
  }
  
  /**
   * Busca stream para outros eventos esportivos
   */
  async getSportsEventStream(event: Event): Promise<LiveStream | null> {
    const sportName = event.strSport?.toLowerCase() || ''
    
    // Usar stream de demonstração
    const demoStream = getRandomDemoStream()
    
    return {
      id: event.idEvent,
      name: event.strEvent,
      url: demoStream.url,
      type: 'hls',
      quality: 'HD',
      language: 'es'
    }
  }
  
  /**
   * Busca stream por nome do canal
   */
  async getChannelStream(channelName: string): Promise<LiveStream | null> {
    // Usar função do config para obter stream apropriado
    const streamUrl = getStreamForChannel(channelName)
    
    return {
      id: channelName.toLowerCase().replace(/\s+/g, '-'),
      name: channelName,
      url: streamUrl,
      type: 'hls',
      quality: 'HD',
      language: 'es'
    }
  }
  
  /**
   * Verifica se um stream está disponível
   */
  async checkStreamAvailability(url: string): Promise<boolean> {
    try {
      const response = await fetch(url, {
        method: 'HEAD',
        mode: 'no-cors' // Evitar CORS
      })
      return true // Se não der erro, assumimos que está disponível
    } catch (error) {
      console.error('Stream não disponível:', url, error)
      return false
    }
  }
}

export const liveStreamsService = new LiveStreamsService()