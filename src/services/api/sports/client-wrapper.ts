// Wrapper para fazer requisições para as APIs esportivas
// Detecta se está no cliente ou servidor e usa a estratégia apropriada

export class ClientWrapper {
  private baseUrl: string
  
  constructor() {
    // Se estiver no cliente, usar as API routes
    // Se estiver no servidor, será usado diretamente pelos clients
    this.baseUrl = typeof window !== 'undefined' ? '/api/sports' : ''
  }
  
  private async fetchFromAPI<T>(endpoint: string): Promise<T> {
    const response = await fetch(`${this.baseUrl}${endpoint}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    })
    
    if (!response.ok) {
      throw new Error(`API error: ${response.status} ${response.statusText}`)
    }
    
    const data = await response.json()
    
    if (!data.success) {
      throw new Error(data.error || 'Unknown error')
    }
    
    return data.data
  }
  
  async getLiveFixtures() {
    return this.fetchFromAPI('/live-fixtures')
  }
  
  async getUpcomingEvents(days: number = 7) {
    return this.fetchFromAPI(`/upcoming-events?days=${days}`)
  }
  
  async getTodayEvents() {
    return this.fetchFromAPI('/today-events')
  }
  
  async getLiveEvents() {
    return this.fetchFromAPI('/live-events')
  }
}

export const clientWrapper = new ClientWrapper()