/**
 * Serviço de Rotação de APIs
 * Garante que apenas 1 API Sports seja chamada por vez
 * Respeita intervalo de 15 minutos entre requisições
 */

interface RotationState {
  currentIndex: number
  lastApiCall: Record<string, Date>
  apiOrder: string[]
}

class APIRotationService {
  private state: RotationState
  private readonly INTERVAL_MINUTES = 15 // 15 minutos entre requisições
  private readonly STORAGE_KEY = 'api-rotation-state'
  
  // Lista de todas as APIs Sports
  private readonly API_ORDER = [
    'football',
    'basketball', 
    'baseball',
    'hockey',
    'volleyball',
    'handball',
    'rugby',
    'formula1',
    'mma',
    'nfl',
    'nba',
    'afl'
  ]
  
  constructor() {
    this.state = this.loadState()
  }
  
  private loadState(): RotationState {
    if (typeof window === 'undefined') {
      // No servidor, começar do zero
      return {
        currentIndex: 0,
        lastApiCall: {},
        apiOrder: [...this.API_ORDER]
      }
    }
    
    const stored = localStorage.getItem(this.STORAGE_KEY)
    if (stored) {
      try {
        const data = JSON.parse(stored)
        return {
          ...data,
          lastApiCall: Object.fromEntries(
            Object.entries(data.lastApiCall).map(([api, date]: [string, any]) => 
              [api, new Date(date)]
            )
          )
        }
      } catch (error) {
        console.error('Erro ao carregar estado de rotação:', error)
      }
    }
    
    return {
      currentIndex: 0,
      lastApiCall: {},
      apiOrder: [...this.API_ORDER]
    }
  }
  
  private saveState(): void {
    if (typeof window === 'undefined') return
    
    localStorage.setItem(this.STORAGE_KEY, JSON.stringify(this.state))
  }
  
  /**
   * Obter próxima API para chamar
   * Retorna null se nenhuma API pode ser chamada agora
   */
  getNextAPI(): string | null {
    const now = new Date()
    
    // Procurar por uma API que pode ser chamada
    for (let i = 0; i < this.API_ORDER.length; i++) {
      const index = (this.state.currentIndex + i) % this.API_ORDER.length
      const api = this.state.apiOrder[index]
      
      const lastCall = this.state.lastApiCall[api]
      if (!lastCall || this.canCallAPI(api, now)) {
        // Atualizar índice e registrar chamada
        this.state.currentIndex = (index + 1) % this.API_ORDER.length
        this.state.lastApiCall[api] = now
        this.saveState()
        
        console.log(`🎯 Rotação API Sports: Selecionada ${api} (próxima: ${this.state.apiOrder[this.state.currentIndex]})`)
        return api
      }
    }
    
    // Nenhuma API disponível
    const nextAvailable = this.getNextAvailableTime()
    if (nextAvailable) {
      const minutesUntil = Math.ceil((nextAvailable.getTime() - now.getTime()) / 1000 / 60)
      console.log(`⏳ Todas as APIs em cooldown. Próxima disponível em ${minutesUntil} minutos`)
    }
    
    return null
  }
  
  /**
   * Verificar se pode chamar uma API específica
   */
  canCallAPI(api: string, now: Date = new Date()): boolean {
    const lastCall = this.state.lastApiCall[api]
    if (!lastCall) return true
    
    const timeSinceLastCall = now.getTime() - lastCall.getTime()
    const intervalMs = this.INTERVAL_MINUTES * 60 * 1000
    
    return timeSinceLastCall >= intervalMs
  }
  
  /**
   * Obter próximo horário disponível
   */
  getNextAvailableTime(): Date | null {
    let earliestTime: Date | null = null
    
    for (const api of this.API_ORDER) {
      const lastCall = this.state.lastApiCall[api]
      if (!lastCall) return new Date() // Se alguma API nunca foi chamada
      
      const nextTime = new Date(lastCall.getTime() + this.INTERVAL_MINUTES * 60 * 1000)
      if (!earliestTime || nextTime < earliestTime) {
        earliestTime = nextTime
      }
    }
    
    return earliestTime
  }
  
  /**
   * Obter estatísticas de rotação
   */
  getStats(): {
    currentAPI: string
    nextAPI: string
    apisAvailable: number
    nextAvailableIn: string
    callHistory: Record<string, string>
  } {
    const now = new Date()
    const available = this.API_ORDER.filter(api => this.canCallAPI(api, now)).length
    const nextTime = this.getNextAvailableTime()
    
    return {
      currentAPI: this.state.apiOrder[this.state.currentIndex],
      nextAPI: this.state.apiOrder[(this.state.currentIndex + 1) % this.API_ORDER.length],
      apisAvailable: available,
      nextAvailableIn: nextTime ? 
        `${Math.ceil((nextTime.getTime() - now.getTime()) / 1000 / 60)} minutos` : 
        'Agora',
      callHistory: Object.fromEntries(
        Object.entries(this.state.lastApiCall).map(([api, date]) => 
          [api, date.toLocaleTimeString()]
        )
      )
    }
  }
  
  /**
   * Resetar estado de rotação
   */
  reset(): void {
    this.state = {
      currentIndex: 0,
      lastApiCall: {},
      apiOrder: [...this.API_ORDER]
    }
    this.saveState()
    console.log('✅ Estado de rotação resetado')
  }
}

// Singleton
export const apiRotation = new APIRotationService()