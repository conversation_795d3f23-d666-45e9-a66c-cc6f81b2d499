import { Fixture, AllSportEvent } from '@/types/sports'
import { 
  footballClient, 
  basketballClient, 
  baseballClient, 
  hockeyClient, 
  volleyballClient, 
  handballClient, 
  rugbyClient,
  formula1Client,
  mmaClient,
  nflClient,
  nbaClient,
  aflClient,
  allSportDBClient
} from './index'
import { makeAPIRequest } from './request-manager'
import { SportsDataService } from './sports-data.service'

// Mapeamento de clientes API por esporte
const API_CLIENTS = {
  'football': footballClient,
  'basketball': basketballClient,
  'baseball': baseballClient,
  'hockey': hockeyClient,
  'volleyball': volleyballClient,
  'handball': handballClient,
  'rugby': rugbyClient,
  'formula-1': formula1Client,
  'mma': mmaClient,
  'nfl': nflClient,
  'nba': nbaClient,
  'afl': aflClient
}

// Mapeamento de esporte para API provider
const SPORT_TO_API_PROVIDER = {
  'football': 'api-football',
  'basketball': 'api-basketball',
  'baseball': 'api-baseball',
  'hockey': 'api-hockey',
  'volleyball': 'api-volleyball',
  'handball': 'api-handball',
  'rugby': 'api-rugby',
  'formula-1': 'api-formula1',
  'mma': 'allsportdb',
  'nfl': 'api-nfl',
  'nba': 'api-nba',
  'afl': 'api-afl',
  'tennis': 'allsportdb',
  'boxing': 'allsportdb',
  'motogp': 'allsportdb',
  'esports': 'allsportdb'
}

export class UnifiedSportsService {
  /**
   * Busca eventos ao vivo de múltiplos esportes
   */
  static async getAllLiveEvents(): Promise<AllSportEvent[]> {
    const allEvents: AllSportEvent[] = []
    
    // Buscar eventos de cada esporte em paralelo
    const promises = Object.entries(API_CLIENTS).map(async ([sport, client]) => {
      try {
        const provider = SPORT_TO_API_PROVIDER[sport]
        
        if (sport === 'football') {
          const fixtures = await makeAPIRequest(
            provider,
            'live-fixtures',
            () => client.getLiveFixtures()
          )
          
          // Converter fixtures para AllSportEvent
          return fixtures.map(f => this.fixtureToEvent(f, sport))
        } else {
          // Para outros esportes, usar endpoint genérico
          const today = new Date().toISOString().split('T')[0]
          const fixtures = await makeAPIRequest(
            provider,
            `${sport}-fixtures`,
            () => client.getFixturesByDate(today)
          )
          
          return fixtures.map(f => this.fixtureToEvent(f, sport))
        }
      } catch (error) {
        console.warn(`Erro ao buscar eventos ao vivo de ${sport}:`, error)
        return []
      }
    })
    
    // Também buscar do AllSportDB
    promises.push(
      makeAPIRequest(
        'allsportdb',
        'today-events',
        () => allSportDBClient.getTodayEvents()
      ).catch(() => [])
    )
    
    const results = await Promise.allSettled(promises)
    
    results.forEach(result => {
      if (result.status === 'fulfilled' && Array.isArray(result.value)) {
        allEvents.push(...result.value)
      }
    })
    
    return allEvents
  }
  
  /**
   * Busca resultados ao vivo de múltiplos esportes
   */
  static async getLiveResults(): Promise<AllSportEvent[]> {
    console.log('🔴 UnifiedSportsService: Buscando eventos ao vivo...')
    
    try {
      // Usar o SportsDataService que já tem a lógica implementada
      const liveEvents = await SportsDataService.getAllLiveResults()
      console.log(`✅ UnifiedSportsService: ${liveEvents.length} eventos ao vivo encontrados`)
      return liveEvents
    } catch (error) {
      console.error('❌ UnifiedSportsService: Erro ao buscar eventos ao vivo:', error)
      return []
    }
  }
  
  /**
   * Busca próximos eventos de múltiplos esportes
   */
  static async getAllUpcomingEvents(): Promise<AllSportEvent[]> {
    const allEvents: AllSportEvent[] = []
    const today = new Date().toISOString().split('T')[0]
    const nextWeek = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
    
    // Buscar eventos de cada esporte via API Sports
    const apiSportsPromises = Object.entries(API_CLIENTS).map(async ([sport, client]) => {
      try {
        const provider = SPORT_TO_API_PROVIDER[sport]
        
        const fixtures = await makeAPIRequest(
          provider,
          `${sport}-upcoming`,
          () => client.getFixturesByDate(today),
          60 * 1000 // Cache de 1 minuto para testes
        )
        
        return fixtures
          .filter(f => new Date(f.fixture.date) >= new Date())
          .map(f => this.fixtureToEvent(f, sport))
      } catch (error) {
        console.warn(`Erro ao buscar próximos eventos de ${sport}:`, error)
        return []
      }
    })
    
    // Buscar todos os esportes do AllSportDB
    const allSportPromise = makeAPIRequest(
      'allsportdb',
      'all-upcoming-events',
      () => allSportDBClient.getEvents({
        dateFrom: today,
        dateTo: nextWeek,
        liveUrlExists: 1
      }),
      60 * 1000 // Cache de 1 minuto
    ).catch(() => [])
    
    const results = await Promise.allSettled([...apiSportsPromises, allSportPromise])
    
    results.forEach(result => {
      if (result.status === 'fulfilled' && Array.isArray(result.value)) {
        allEvents.push(...result.value)
      }
    })
    
    // Remover duplicatas e ordenar
    const uniqueEvents = this.removeDuplicates(allEvents)
    return uniqueEvents.sort((a, b) => 
      new Date(a.dateFrom).getTime() - new Date(b.dateFrom).getTime()
    )
  }
  
  /**
   * Converte uma Fixture para AllSportEvent
   */
  private static fixtureToEvent(fixture: Fixture, sport: string): AllSportEvent {
    const sportEmojis: Record<string, string> = {
      'football': '⚽',
      'basketball': '🏀',
      'baseball': '⚾',
      'hockey': '🏒',
      'volleyball': '🏐',
      'handball': '🤾',
      'rugby': '🏉',
      'formula-1': '🏎️',
      'mma': '🥊',
      'nfl': '🏈',
      'nba': '🏀',
      'afl': '🏈'
    }
    
    const sportNames: Record<string, string> = {
      'football': 'Futebol',
      'basketball': 'Basquete',
      'baseball': 'Baseball',
      'hockey': 'Hockey',
      'volleyball': 'Vôlei',
      'handball': 'Handball',
      'rugby': 'Rugby',
      'formula-1': 'Fórmula 1',
      'mma': 'MMA',
      'nfl': 'NFL',
      'nba': 'NBA',
      'afl': 'AFL'
    }
    
    return {
      id: fixture.fixture.id,
      name: `${fixture.teams.home.name} vs ${fixture.teams.away.name}`,
      dateFrom: fixture.fixture.date,
      dateTo: new Date(new Date(fixture.fixture.date).getTime() + 2 * 60 * 60 * 1000).toISOString(),
      competition: fixture.league.name,
      sport: sportNames[sport] || sport,
      country: fixture.league.country,
      location: fixture.fixture.venue?.name || '',
      liveUrl: '/watch/demo',
      emoji: sportEmojis[sport] || '🏆'
    }
  }
  
  /**
   * Remove eventos duplicados baseado no nome e data
   */
  private static removeDuplicates(events: AllSportEvent[]): AllSportEvent[] {
    const seen = new Set<string>()
    const uniqueEvents: AllSportEvent[] = []
    
    events.forEach((event, index) => {
      // Primeiro tentar usar o ID do evento
      let key = event.id?.toString() || ''
      
      // Se o ID já foi visto ou não existe, criar uma chave única
      if (seen.has(key) || !key) {
        key = `${event.name}-${event.dateFrom}-${index}`
      }
      
      if (!seen.has(key)) {
        seen.add(key)
        // Garantir que cada evento tenha um ID único
        uniqueEvents.push({
          ...event,
          id: event.id || `generated-${Date.now()}-${index}`
        })
      }
    })
    
    console.log(`✅ Removidas duplicatas: ${events.length} -> ${uniqueEvents.length} eventos`)
    return uniqueEvents
  }
}