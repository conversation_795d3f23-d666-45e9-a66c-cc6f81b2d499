import { allSportDBClient } from './allsport-db.client'
import { AllSportEvent } from '@/types/sports'

/**
 * Rotacionador de endpoints do AllSportDB
 * Distribui requisições entre diferentes endpoints para maximizar dados frescos
 */
export class AllSportRotator {
  private rotationIndex = 0
  private lastRotation = new Date()
  
  // Endpoints para rotacionar (1 por minuto)
  private readonly endpoints = [
    {
      name: 'live-now',
      fetcher: async () => {
        const events = await allSportDBClient.getTodayEvents()
        const now = new Date()
        return events.filter(e => {
          const start = new Date(e.dateFrom)
          const end = new Date(e.dateTo || new Date(start.getTime() + 3 * 60 * 60 * 1000))
          return now >= start && now <= end
        })
      }
    },
    {
      name: 'starting-soon',
      fetcher: async () => {
        const events = await allSportDBClient.getTodayEvents()
        const now = new Date()
        const in30min = new Date(now.getTime() + 30 * 60 * 1000)
        return events.filter(e => {
          const start = new Date(e.dateFrom)
          return start > now && start <= in30min
        })
      }
    },
    {
      name: 'football-today',
      fetcher: () => allSportDBClient.getEventsBySport('football')
    },
    {
      name: 'basketball-today',
      fetcher: () => allSportDBClient.getEventsBySport('basketball')
    },
    {
      name: 'baseball-today',
      fetcher: () => allSportDBClient.getEventsBySport('baseball')
    },
    {
      name: 'tennis-today',
      fetcher: () => allSportDBClient.getEventsBySport('tennis')
    },
    {
      name: 'mma-boxing-today',
      fetcher: async () => {
        const [mma, boxing] = await Promise.all([
          allSportDBClient.getEventsBySport('mma'),
          allSportDBClient.getEventsBySport('boxing')
        ])
        return [...mma, ...boxing]
      }
    },
    {
      name: 'motorsports-today',
      fetcher: async () => {
        const [f1, motogp, nascar] = await Promise.all([
          allSportDBClient.getEventsBySport('formula 1'),
          allSportDBClient.getEventsBySport('motogp'),
          allSportDBClient.getEventsBySport('nascar')
        ])
        return [...f1, ...motogp, ...nascar]
      }
    },
    {
      name: 'esports-today',
      fetcher: async () => {
        const [csgo, lol, dota, valorant] = await Promise.all([
          allSportDBClient.getEventsBySport('counter-strike'),
          allSportDBClient.getEventsBySport('league of legends'),
          allSportDBClient.getEventsBySport('dota 2'),
          allSportDBClient.getEventsBySport('valorant')
        ])
        return [...csgo, ...lol, ...dota, ...valorant]
      }
    },
    {
      name: 'popular-leagues',
      fetcher: async () => {
        const competitions = [
          'Premier League',
          'La Liga',
          'Champions League',
          'NBA',
          'NFL',
          'UFC'
        ]
        
        const results = await Promise.all(
          competitions.map(comp => 
            allSportDBClient.getEvents({ competition: comp })
          )
        )
        
        return results.flat()
      }
    },
    {
      name: 'weekend-events',
      fetcher: async () => {
        const friday = new Date()
        const dayOfWeek = friday.getDay()
        const daysUntilFriday = (5 - dayOfWeek + 7) % 7 || 7
        friday.setDate(friday.getDate() + daysUntilFriday)
        
        const sunday = new Date(friday)
        sunday.setDate(sunday.getDate() + 2)
        
        return allSportDBClient.getEvents({
          dateFrom: friday.toISOString().split('T')[0],
          dateTo: sunday.toISOString().split('T')[0]
        })
      }
    },
    {
      name: 'high-profile',
      fetcher: async () => {
        // Buscar eventos com muitos espectadores esperados
        const events = await allSportDBClient.getUpcomingEvents(7)
        return events.filter(e => 
          e.name.toLowerCase().includes('final') ||
          e.name.toLowerCase().includes('championship') ||
          e.name.toLowerCase().includes('derby') ||
          e.name.toLowerCase().includes('clasico') ||
          e.competition.toLowerCase().includes('world cup') ||
          e.competition.toLowerCase().includes('super bowl')
        )
      }
    }
  ]
  
  // Obter próximo conjunto de dados
  async getNextRotation(): Promise<{
    endpoint: string
    events: AllSportEvent[]
    nextRotation: Date
  }> {
    const endpoint = this.endpoints[this.rotationIndex]
    console.log(`🔄 AllSportDB Rotation: ${endpoint.name}`)
    
    try {
      const events = await endpoint.fetcher()
      
      // Avançar para próximo endpoint
      this.rotationIndex = (this.rotationIndex + 1) % this.endpoints.length
      this.lastRotation = new Date()
      
      const nextRotation = new Date(this.lastRotation.getTime() + 60 * 1000)
      
      console.log(`✅ ${events.length} eventos encontrados em ${endpoint.name}`)
      
      return {
        endpoint: endpoint.name,
        events,
        nextRotation
      }
    } catch (error) {
      console.error(`❌ Erro em ${endpoint.name}:`, error)
      
      // Avançar mesmo com erro
      this.rotationIndex = (this.rotationIndex + 1) % this.endpoints.length
      
      return {
        endpoint: endpoint.name,
        events: [],
        nextRotation: new Date(Date.now() + 60 * 1000)
      }
    }
  }
  
  // Obter status atual
  getStatus(): {
    currentEndpoint: string
    nextEndpoint: string
    rotationProgress: number
    lastRotation: Date
  } {
    const currentEndpoint = this.endpoints[this.rotationIndex]?.name || 'unknown'
    const nextIndex = (this.rotationIndex + 1) % this.endpoints.length
    const nextEndpoint = this.endpoints[nextIndex]?.name || 'unknown'
    
    return {
      currentEndpoint,
      nextEndpoint,
      rotationProgress: (this.rotationIndex / this.endpoints.length) * 100,
      lastRotation: this.lastRotation
    }
  }
  
  // Buscar todos os endpoints de uma vez (para inicialização)
  async fetchAll(): Promise<AllSportEvent[]> {
    console.log('🚀 Buscando todos os endpoints do AllSportDB...')
    
    const results = await Promise.allSettled(
      this.endpoints.map(async (endpoint) => {
        try {
          return await endpoint.fetcher()
        } catch (error) {
          console.error(`Erro em ${endpoint.name}:`, error)
          return []
        }
      })
    )
    
    const allEvents = results
      .filter(r => r.status === 'fulfilled')
      .flatMap(r => (r as PromiseFulfilledResult<AllSportEvent[]>).value)
    
    // Remover duplicados
    const uniqueEvents = Array.from(
      new Map(allEvents.map(e => [e.id, e])).values()
    )
    
    console.log(`✅ Total: ${uniqueEvents.length} eventos únicos encontrados`)
    
    return uniqueEvents
  }
}

// Singleton instance
export const allSportRotator = new AllSportRotator()

// Exportar para debug
if (typeof window !== 'undefined') {
  (window as any).allSportRotator = allSportRotator
}