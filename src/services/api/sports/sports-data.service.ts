import { AllSportEvent } from '@/types/sports'
import { allSportDBClient } from './allsport-db.client'
import { 
  footballClient,
  basketballClient,
  baseballClient,
  hockeyClient,
  volleyballClient,
  handballClient,
  rugbyClient,
  formula1Client,
  mmaClient,
  nflClient,
  nbaClient,
  aflClient
} from './api-sports.client'
import { makeAPIRequest } from './request-manager'
import { MockDataService } from './mock-data.service'

// Mapeamento de clientes e informações
const SPORTS_CLIENTS = {
  football: { client: footballClient, name: 'Futebol', emoji: '⚽', provider: 'api-football' },
  basketball: { client: basketballClient, name: '<PERSON><PERSON>', emoji: '🏀', provider: 'api-basketball' },
  baseball: { client: baseballClient, name: 'Baseball', emoji: '⚾', provider: 'api-baseball' },
  hockey: { client: hockeyClient, name: 'Hockey', emoji: '🏒', provider: 'api-hockey' },
  volleyball: { client: volleyballClient, name: '<PERSON><PERSON><PERSON><PERSON>', emoji: '🏐', provider: 'api-volleyball' },
  handball: { client: handballClient, name: 'Handball', emoji: '🤾', provider: 'api-handball' },
  rugby: { client: rugbyClient, name: 'Rugby', emoji: '🏉', provider: 'api-rugby' },
  formula1: { client: formula1Client, name: 'Fórmula 1', emoji: '🏎️', provider: 'api-formula1' },
  mma: { client: mmaClient, name: 'MMA', emoji: '🥊', provider: 'api-mma' },
  nfl: { client: nflClient, name: 'NFL', emoji: '🏈', provider: 'api-nfl' },
  nba: { client: nbaClient, name: 'NBA', emoji: '🏀', provider: 'api-nba' },
  afl: { client: aflClient, name: 'AFL', emoji: '🏈', provider: 'api-afl' }
}

export class SportsDataService {
  /**
   * Busca resultados ao vivo de uma API específica
   * @param apiName Nome da API (ex: 'api-football', 'api-basketball', etc)
   */
  static async getLiveResultsForAPI(apiName: string): Promise<AllSportEvent[]> {
    const sportInfo = Object.entries(SPORTS_CLIENTS).find(
      ([_, info]) => info.provider === apiName
    )
    
    if (!sportInfo) {
      console.warn(`API ${apiName} não encontrada`)
      return []
    }
    
    const [sport, info] = sportInfo
    
    try {
      console.log(`📡 Buscando eventos ao vivo de ${apiName}...`)
      
      const events = await makeAPIRequest(
        info.provider,
        `${sport}-live-single`,
        () => info.client.getLiveEvents(),
        15 * 60 * 1000 // Cache de 15 minutos
      )
      
      console.log(`✅ ${apiName}: ${events.length} eventos ao vivo`)
      
      // Converter para formato AllSportEvent
      return events.map((event: any) => 
        this.convertToAllSportEvent(event, sport, info)
      )
    } catch (error: any) {
      console.error(`❌ Erro em ${apiName}:`, error.message)
      return []
    }
  }
  /**
   * Busca resultados ao vivo - PRIORIZA AllSportDB (100k requisições/mês)
   * Usa API Sports apenas como fallback
   */
  static async getAllLiveResults(): Promise<AllSportEvent[]> {
    const allResults: AllSportEvent[] = []
    
    console.log('🚀 Buscando eventos ao vivo - Priorizando AllSportDB...')
    
    // PRIORIDADE 1: Buscar do AllSportDB (100k requisições/mês)
    try {
      const allSportEvents = await makeAPIRequest(
        'allsportdb',
        'live-results-all',
        async () => {
          const today = new Date().toISOString().split('T')[0]
          const events = await allSportDBClient.getEvents({
            dateFrom: today,
            dateTo: today,
            liveUrlExists: 1
          })
          
          // Filtrar apenas eventos ao vivo E que são partidas individuais (não campeonatos)
          const now = new Date()
          return events.filter(event => {
            const eventStart = new Date(event.dateFrom)
            const eventEnd = event.dateTo ? new Date(event.dateTo) : new Date(eventStart.getTime() + 4 * 60 * 60 * 1000)
            
            // Calcular duração do evento em horas
            const durationHours = (eventEnd.getTime() - eventStart.getTime()) / (1000 * 60 * 60)
            
            // Filtrar apenas eventos com duração menor que 12 horas (partidas, não campeonatos)
            // E que estão acontecendo agora
            const isShortEvent = durationHours <= 12
            const isLive = eventStart <= now && now <= eventEnd
            
            // Filtrar esportes específicos que têm partidas ao vivo
            const validSports = ['Football', 'Soccer', 'Basketball', 'Tennis', 'Hockey', 'Baseball', 'Volleyball', 'Handball']
            const isValidSport = event.sport && validSports.some(sport => 
              event.sport!.name.toLowerCase().includes(sport.toLowerCase())
            )
            
            if (isLive && isShortEvent) {
              console.log(`🔴 Evento ao vivo: ${event.name} (${event.sport?.name}) - Duração: ${durationHours.toFixed(1)}h`)
            }
            
            return isLive && isShortEvent && isValidSport
          })
        },
        30 * 1000
      )
      
      console.log(`✅ AllSportDB retornou ${allSportEvents.length} eventos ao vivo`)
      allResults.push(...allSportEvents)
      
      // Se AllSportDB retornou dados suficientes, não usar API Sports
      if (allSportEvents.length >= 10) {
        console.log('✨ AllSportDB forneceu dados suficientes, economizando requisições da API Sports')
        return allResults
      }
    } catch (error) {
      console.warn('⚠️ Erro ao buscar do AllSportDB, usando API Sports como fallback:', error)
    }
    
    // FALLBACK: Usar API Sports - TODAS as 12 APIs (SEQUENCIALMENTE)
    console.log('📡 Chamando TODAS as 12 APIs Sports SEQUENCIALMENTE...')
    
    // Processar APIs uma por vez para respeitar rate limits
    for (const [sport, info] of Object.entries(SPORTS_CLIENTS)) {
      try {
        console.log(`📡 Processando ${sport} (${info.provider})...`)
        
        const events = await makeAPIRequest(
          info.provider,
          `${sport}-live`,
          () => info.client.getLiveEvents(),
          15 * 60 * 1000 // Cache de 15 minutos - IMPORTANTE!
        )
        
        console.log(`✅ ${sport}: ${events.length} eventos ao vivo`)
        
        // Converter para formato AllSportEvent
        const convertedEvents = events.map((event: any) => 
          this.convertToAllSportEvent(event, sport, info)
        )
        allResults.push(...convertedEvents)
        
        // Aguardar 1 segundo entre APIs diferentes
        await new Promise(resolve => setTimeout(resolve, 1000))
        
      } catch (error: any) {
        // Se for erro de limite, pular esta API
        if (error.message?.includes('Limite de requisições')) {
          console.log(`⏭️ Pulando ${sport} - aguardando intervalo de 15 minutos`)
        } else {
          console.warn(`❌ Erro em ${sport}:`, error.message)
        }
      }
    }
    
    // Remover duplicatas e ordenar
    const uniqueResults = this.removeDuplicates(allResults)
    
    console.log(`📊 Total de eventos ao vivo encontrados: ${uniqueResults.length}`)
    
    return uniqueResults
  }
  
  /**
   * Busca próximos eventos - USA API Sports (calendário de eventos)
   * AllSportDB apenas como complemento
   */
  static async getAllUpcomingEvents(days: number = 7): Promise<AllSportEvent[]> {
    const allEvents: AllSportEvent[] = []
    
    // Limitar para 3 dias para reduzir requisições
    const limitedDays = Math.min(days, 3)
    console.log(`🚀 Buscando calendário de próximos eventos (${limitedDays} dias) - SEQUENCIALMENTE...`)
    
    // Processar APIs sequencialmente para respeitar rate limits
    for (const [sport, info] of Object.entries(SPORTS_CLIENTS)) {
      try {
        console.log(`📡 Processando ${sport} (${info.provider})...`)
        
        const events = await makeAPIRequest(
          info.provider,
          `${sport}-upcoming-${limitedDays}d`,
          () => info.client.getUpcomingEvents(limitedDays),
          15 * 60 * 1000 // Cache de 15 minutos
        )
        
        console.log(`✅ ${sport}: ${events.length} eventos futuros`)
        
        const convertedEvents = events.map((event: any) => 
          this.convertToAllSportEvent(event, sport, info)
        )
        allEvents.push(...convertedEvents)
        
        // Aguardar 1 segundo entre APIs diferentes
        await new Promise(resolve => setTimeout(resolve, 1000))
        
      } catch (error: any) {
        // Se for erro de limite, pular esta API
        if (error.message?.includes('Limite de requisições')) {
          console.log(`⏭️ Pulando ${sport} - aguardando intervalo`)
        } else {
          console.warn(`❌ Erro em ${sport}:`, error.message)
        }
      }
    }
    
    // COMPLEMENTO: AllSportDB apenas para esportes não cobertos
    const allSportPromise = makeAPIRequest(
      'allsportdb',
      `upcoming-complement-${days}d`,
      async () => {
        const events = await allSportDBClient.getUpcomingEvents(days)
        // Filtrar apenas esportes não cobertos pela API Sports
        return events.filter(event => {
          const sportName = event.sport.toLowerCase()
          return !Object.values(SPORTS_CLIENTS).some(client => 
            client.name.toLowerCase() === sportName
          )
        })
      },
      5 * 60 * 1000
    ).catch(() => {
      console.log('⚠️ AllSportDB não disponível para complemento')
      return []
    })
    
    // Tentar AllSportDB se disponível
    try {
      const allSportResults = await allSportPromise
      if (Array.isArray(allSportResults)) {
        allEvents.push(...allSportResults)
      }
    } catch (error) {
      console.log('⚠️ AllSportDB não disponível')
    }
    
    const uniqueEvents = this.removeDuplicates(allEvents)
    
    console.log(`📊 Total de eventos futuros encontrados: ${uniqueEvents.length}`)
    
    return uniqueEvents.sort((a, b) => 
      new Date(a.dateFrom).getTime() - new Date(b.dateFrom).getTime()
    )
  }
  
  /**
   * Busca eventos por esporte específico (de qualquer API)
   */
  static async getEventsBySport(sport: string, days: number = 7): Promise<AllSportEvent[]> {
    // Verificar se temos cliente API Sports para este esporte
    const sportInfo = SPORTS_CLIENTS[sport as keyof typeof SPORTS_CLIENTS]
    
    if (sportInfo) {
      // Usar API Sports
      try {
        const events = await makeAPIRequest(
          sportInfo.provider,
          `${sport}-events-${days}d`,
          () => sportInfo.client.getUpcomingEvents(days),
          5 * 60 * 1000
        )
        
        return events.map((event: any) => this.convertToAllSportEvent(event, sport, sportInfo))
      } catch (error) {
        console.error(`Erro ao buscar eventos de ${sport} via API Sports:`, error)
      }
    }
    
    // Fallback para AllSportDB
    try {
      return await makeAPIRequest(
        'allsportdb',
        `events-${sport}-${days}d`,
        () => allSportDBClient.getEventsBySport(sport, 
          new Date().toISOString().split('T')[0],
          new Date(Date.now() + days * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
        ),
        5 * 60 * 1000
      )
    } catch (error) {
      console.error(`Erro ao buscar eventos de ${sport}:`, error)
      return []
    }
  }
  
  /**
   * Converte evento da API Sports para formato AllSportEvent
   */
  private static convertToAllSportEvent(event: any, sport: string, info: any): AllSportEvent {
    // Converter baseado no tipo de esporte
    if (sport === 'football' && event.fixture) {
      return {
        id: event.fixture.id,
        name: `${event.teams.home.name} vs ${event.teams.away.name}`,
        dateFrom: event.fixture.date,
        dateTo: new Date(new Date(event.fixture.date).getTime() + 2 * 60 * 60 * 1000).toISOString(),
        competition: event.league.name,
        sport: info.name,
        country: event.league.country,
        location: event.fixture.venue?.name || '',
        liveUrl: '/watch/demo',
        emoji: info.emoji,
        score: event.goals ? `${event.goals.home}-${event.goals.away}` : undefined,
        status: event.fixture.status?.short,
        homeLogo: event.teams?.home?.logo,
        awayLogo: event.teams?.away?.logo,
        leagueLogo: event.league?.logo
      }
    }
    
    // Formato genérico para outros esportes
    return {
      id: event.id || Math.random(),
      name: event.name || `${event.home_team || event.home} vs ${event.away_team || event.away}`,
      dateFrom: event.date || event.datetime || event.start_time,
      dateTo: new Date(new Date(event.date || event.datetime || event.start_time).getTime() + 2 * 60 * 60 * 1000).toISOString(),
      competition: event.league?.name || event.competition || event.tournament || '',
      sport: info.name,
      country: event.country || '',
      location: event.venue || event.location || '',
      liveUrl: '/watch/demo',
      emoji: info.emoji,
      score: event.score || event.result,
      status: event.status
    }
  }
  
  /**
   * Remove eventos duplicados
   */
  private static removeDuplicates(events: AllSportEvent[]): AllSportEvent[] {
    const seen = new Set<string>()
    const uniqueEvents: AllSportEvent[] = []
    
    events.forEach((event, index) => {
      // Primeiro tentar usar o ID do evento
      let key = event.id?.toString() || ''
      
      // Se o ID já foi visto, criar uma chave única baseada em outros campos
      if (seen.has(key) || !key) {
        key = `${event.name}-${event.dateFrom}-${index}`
      }
      
      if (!seen.has(key)) {
        seen.add(key)
        // Garantir que cada evento tenha um ID único
        uniqueEvents.push({
          ...event,
          id: event.id || `generated-${Date.now()}-${index}`
        })
      }
    })
    
    return uniqueEvents
  }
  
  /**
   * Obter contagem de eventos por esporte
   */
  static async getSportsStats(): Promise<Record<string, number>> {
    const events = await this.getAllUpcomingEvents(30)
    const stats: Record<string, number> = {}
    
    events.forEach(event => {
      stats[event.sport] = (stats[event.sport] || 0) + 1
    })
    
    return stats
  }
}