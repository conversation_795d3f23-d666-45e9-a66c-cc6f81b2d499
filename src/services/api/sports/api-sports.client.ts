import { APIFootballResponse, Fixture, League, Team, Standing } from '@/types/sports'

const API_KEY = process.env.API_SPORTS_KEY || process.env.NEXT_PUBLIC_API_SPORTS_KEY || 'eaf42062f90877281cf06a2ad2e1d11e'

// URLs corretas para cada esporte (ENDPOINTS DIRETOS)
const SPORT_URLS: Record<string, string> = {
  'football': 'https://v3.football.api-sports.io',
  'basketball': 'https://v1.basketball.api-sports.io',
  'baseball': 'https://v1.baseball.api-sports.io',
  'hockey': 'https://v1.hockey.api-sports.io',
  'volleyball': 'https://v1.volleyball.api-sports.io',
  'handball': 'https://v1.handball.api-sports.io',
  'rugby': 'https://v1.rugby.api-sports.io',
  'formula1': 'https://v1.formula-1.api-sports.io', // Confirmado: formula-1 com hífen
  'mma': 'https://v1.mma.api-sports.io',
  'nfl': 'https://v1.american-football.api-sports.io',  // NFL usa endpoint american-football
  'nba': 'https://v1.basketball.api-sports.io',  // NBA usa o mesmo endpoint de basketball
  'afl': 'https://v1.afl.api-sports.io'
}

// Endpoints corretos para cada esporte
const SPORT_ENDPOINTS: Record<string, {
  fixtures: string,
  live: string,
  standings?: string,
  teams?: string,
  leagues?: string
}> = {
  'football': {
    fixtures: '/fixtures',
    live: '/fixtures?live=all',
    standings: '/standings',
    teams: '/teams',
    leagues: '/leagues'
  },
  'basketball': {
    fixtures: '/games',
    live: '/games?live=all',
    standings: '/standings',
    teams: '/teams',
    leagues: '/leagues'
  },
  'nba': {
    fixtures: '/games',
    live: '/games?live=all',
    standings: '/standings',
    teams: '/teams',
    leagues: '/leagues'
  },
  'baseball': {
    fixtures: '/games',
    live: '/games?live=all',
    standings: '/standings',
    teams: '/teams',
    leagues: '/leagues'
  },
  'hockey': {
    fixtures: '/games',
    live: '/games?live=all',
    standings: '/standings',
    teams: '/teams',
    leagues: '/leagues'
  },
  'volleyball': {
    fixtures: '/games',
    live: '/games?live=all',
    standings: '/standings',
    teams: '/teams',
    leagues: '/leagues'
  },
  'handball': {
    fixtures: '/games',
    live: '/games?live=all',
    standings: '/standings',
    teams: '/teams',
    leagues: '/leagues'
  },
  'rugby': {
    fixtures: '/games',
    live: '/games?live=all',
    standings: '/standings',
    teams: '/teams',
    leagues: '/leagues'
  },
  'formula1': {
    fixtures: '/races',
    live: '/races?type=race&last=1',
    standings: '/rankings/drivers',
    teams: '/rankings/teams',
    leagues: '/competitions'
  },
  'mma': {
    fixtures: '/fights',
    live: '/fights?live=all',
    teams: '/fighters',
    leagues: '/leagues'
  },
  'nfl': {
    fixtures: '/games',
    live: '/games?live=all',
    standings: '/standings',
    teams: '/teams',
    leagues: '/leagues'
  },
  'afl': {
    fixtures: '/games',
    live: '/games?live=all',
    standings: '/standings',
    teams: '/teams',
    leagues: '/leagues'
  }
}

export class APISportsClient {
  private sport: string
  private baseUrl: string

  constructor(sport: string = 'football') {
    this.sport = sport
    this.baseUrl = SPORT_URLS[sport] || SPORT_URLS['football']
    
    // Log detalhado para debug
    console.log(`
🏆 APISportsClient inicializado:
   Esporte: ${sport}
   URL Base: ${this.baseUrl}
   API Key: ${API_KEY ? '✅ Configurada' : '❌ Não configurada'}
    `)
  }

  // Método genérico para requisições
  private async request<T>(endpoint: string, params?: Record<string, string | number | boolean>): Promise<T> {
    const url = new URL(`${this.baseUrl}${endpoint}`)
    
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          url.searchParams.append(key, value.toString())
        }
      })
    }

    console.log(`
📡 Requisição API Sports:
   Esporte: ${this.sport}
   URL: ${url.toString()}
   Endpoint: ${endpoint}
   Timestamp: ${new Date().toISOString()}
    `)

    const response = await fetch(url.toString(), {
      method: 'GET',
      headers: {
        'x-apisports-key': API_KEY
      }
    })

    if (!response.ok) {
      console.error(`❌ Erro na API ${this.sport}: ${response.status} ${response.statusText}`)
      throw new Error(`API Sports error: ${response.status} ${response.statusText}`)
    }

    const data = await response.json()
    
    if (data.errors && Object.keys(data.errors).length > 0) {
      console.error(`❌ Erro na resposta da API ${this.sport}:`, data.errors)
      throw new Error(`API Sports error: ${JSON.stringify(data.errors)}`)
    }

    return data
  }

  // Buscar eventos/jogos/corridas ao vivo
  async getLiveEvents(): Promise<any[]> {
    try {
      // Para football, usar endpoint específico de live
      if (this.sport === 'football') {
        const response = await this.request<any>('/fixtures', { live: 'all' })
        return response.response || []
      }
      
      // Para Formula 1, buscar corridas de hoje
      if (this.sport === 'formula1') {
        try {
          // Buscar corridas de hoje
          const today = new Date().toISOString().split('T')[0]
          const response = await this.request<any>('/races', { 
            date: today
          })
          
          const races = response.response || []
          
          // Verificar se há corridas de qualificação ou sprint hoje também
          const qualifyingResponse = await this.request<any>('/races', { 
            date: today,
            type: 'qualifying'
          }).catch(() => ({ response: [] }))
          
          const sprintResponse = await this.request<any>('/races', { 
            date: today,
            type: 'sprint'
          }).catch(() => ({ response: [] }))
          
          // Combinar todos os eventos
          const allEvents = [
            ...races,
            ...(qualifyingResponse.response || []),
            ...(sprintResponse.response || [])
          ]
          
          console.log(`🏎️ F1: ${allEvents.length} eventos hoje (corridas/quali/sprint)`)
          return allEvents
          
        } catch (error) {
          console.log('F1: Sem corridas hoje')
          return []
        }
      }
      
      // Para outros esportes, buscar jogos de hoje e filtrar os ao vivo
      const today = new Date().toISOString().split('T')[0]
      const endpoints = SPORT_ENDPOINTS[this.sport]
      
      if (!endpoints?.fixtures) {
        console.warn(`⚠️ Endpoint fixtures não configurado para ${this.sport}`)
        return []
      }
      
      // Buscar jogos de hoje
      const response = await this.request<any>(endpoints.fixtures, { date: today })
      const games = response.response || []
      
      // Filtrar apenas jogos ao vivo (status indica jogo em andamento)
      const liveGames = games.filter((game: any) => {
        // Cada esporte tem estrutura diferente
        const status = game.status || game.game?.status || game.fixture?.status
        
        // Status que indicam jogo ao vivo (varia por esporte)
        const liveStatuses = ['LIVE', 'IN_PLAY', 'INPLAY', '1H', '2H', 'HT', 'ET', 'P', 
                             'BREAK', 'OT', 'Q1', 'Q2', 'Q3', 'Q4', 'IN PROGRESS']
        
        if (typeof status === 'string') {
          return liveStatuses.includes(status.toUpperCase())
        }
        
        if (status?.short) {
          return liveStatuses.includes(status.short.toUpperCase())
        }
        
        if (status?.long) {
          return status.long.toLowerCase().includes('progress') || 
                 status.long.toLowerCase().includes('live')
        }
        
        return false
      })
      
      console.log(`🏃 ${this.sport}: ${liveGames.length} jogos ao vivo de ${games.length} jogos hoje`)
      return liveGames
      
    } catch (error) {
      console.error(`❌ Erro ao buscar eventos ao vivo de ${this.sport}:`, error)
      return []
    }
  }

  // Buscar eventos por data
  async getEventsByDate(date: string): Promise<any[]> {
    try {
      const endpoints = SPORT_ENDPOINTS[this.sport]
      if (!endpoints?.fixtures) {
        console.warn(`⚠️ Endpoint fixtures não configurado para ${this.sport}`)
        return []
      }

      const endpoint = endpoints.fixtures
      const response = await this.request<any>(endpoint, { date })
      return response.response || []
    } catch (error) {
      console.error(`❌ Erro ao buscar eventos por data de ${this.sport}:`, error)
      return []
    }
  }

  // Buscar próximos eventos
  async getUpcomingEvents(days: number = 7): Promise<any[]> {
    const events = []
    const today = new Date()
    
    // Para planos gratuitos, limitar para 2 dias
    const maxDays = Math.min(days, 2)
    
    // Buscar eventos sequencialmente para evitar rate limit
    for (let i = 0; i < maxDays; i++) {
      const date = new Date(today)
      date.setDate(date.getDate() + i)
      const dateStr = date.toISOString().split('T')[0]
      
      try {
        const dayEvents = await this.getEventsByDate(dateStr)
        events.push(...dayEvents)
        
        // Aguardar entre requisições
        if (i < maxDays - 1) {
          await new Promise(resolve => setTimeout(resolve, 500))
        }
      } catch (error: any) {
        // Se for erro de plano, parar
        if (error.message?.includes('Free plans')) {
          console.log(`⚠️ ${this.sport}: Limitado a ${i} dias no plano gratuito`)
          break
        }
        console.error(`❌ Erro em ${this.sport} dia ${i}:`, error.message)
      }
    }
    
    return events
  }

  // Métodos específicos para futebol (mantidos para compatibilidade)
  async getLiveFixtures(): Promise<Fixture[]> {
    if (this.sport !== 'football') {
      return this.getLiveEvents() as any
    }
    
    const response = await this.request<APIFootballResponse<Fixture[]>>('/fixtures', {
      live: 'all'
    })
    return response.response
  }

  async getFixturesByDate(date: string): Promise<Fixture[]> {
    if (this.sport !== 'football') {
      return this.getEventsByDate(date) as any
    }
    
    const response = await this.request<APIFootballResponse<Fixture[]>>('/fixtures', {
      date
    })
    return response.response
  }

  async getFixturesByLeague(leagueId: number, season: number): Promise<Fixture[]> {
    const response = await this.request<APIFootballResponse<Fixture[]>>('/fixtures', {
      league: leagueId,
      season
    })
    return response.response
  }

  async getFixtureById(fixtureId: number): Promise<Fixture> {
    const response = await this.request<APIFootballResponse<Fixture[]>>('/fixtures', {
      id: fixtureId
    })
    return response.response[0]
  }

  // Leagues
  async getLeagues(countryCode?: string): Promise<League[]> {
    const endpoints = SPORT_ENDPOINTS[this.sport]
    if (!endpoints?.leagues) {
      return []
    }
    
    const params: Record<string, string> = {}
    if (countryCode) params.code = countryCode
    
    const response = await this.request<APIFootballResponse<League[]>>(endpoints.leagues, params)
    return response.response
  }

  async getLeagueById(leagueId: number): Promise<League> {
    const endpoints = SPORT_ENDPOINTS[this.sport]
    if (!endpoints?.leagues) {
      throw new Error(`Leagues endpoint não disponível para ${this.sport}`)
    }
    
    const response = await this.request<APIFootballResponse<League[]>>(endpoints.leagues, {
      id: leagueId
    })
    return response.response[0]
  }

  // Teams
  async getTeamsByLeague(leagueId: number, season: number): Promise<Team[]> {
    const endpoints = SPORT_ENDPOINTS[this.sport]
    if (!endpoints?.teams) {
      return []
    }
    
    const response = await this.request<APIFootballResponse<Team[]>>(endpoints.teams, {
      league: leagueId,
      season
    })
    return response.response
  }

  async getTeamById(teamId: number): Promise<Team> {
    const endpoints = SPORT_ENDPOINTS[this.sport]
    if (!endpoints?.teams) {
      throw new Error(`Teams endpoint não disponível para ${this.sport}`)
    }
    
    const response = await this.request<APIFootballResponse<Team[]>>(endpoints.teams, {
      id: teamId
    })
    return response.response[0]
  }

  // Standings
  async getStandings(leagueId: number, season: number): Promise<Standing[][]> {
    const endpoints = SPORT_ENDPOINTS[this.sport]
    if (!endpoints?.standings) {
      return []
    }
    
    const response = await this.request<APIFootballResponse<{ league: League; standings: Standing[][] }[]>>(endpoints.standings, {
      league: leagueId,
      season
    })
    return response.response[0]?.standings || []
  }

  // Countries
  async getCountries(): Promise<{ name: string; code: string; flag: string }[]> {
    const response = await this.request<APIFootballResponse<{ name: string; code: string; flag: string }[]>>('/countries')
    return response.response
  }

  // Timezone
  async getTimezones(): Promise<string[]> {
    const response = await this.request<APIFootballResponse<string[]>>('/timezone')
    return response.response
  }

  // Search teams
  async searchTeams(search: string): Promise<Team[]> {
    const endpoints = SPORT_ENDPOINTS[this.sport]
    if (!endpoints?.teams) {
      return []
    }
    
    const response = await this.request<APIFootballResponse<Team[]>>(endpoints.teams, {
      search
    })
    return response.response
  }

  // Top scorers (apenas futebol)
  async getTopScorers(leagueId: number, season: number): Promise<unknown[]> {
    if (this.sport !== 'football') {
      return []
    }
    
    const response = await this.request<APIFootballResponse<unknown[]>>('/players/topscorers', {
      league: leagueId,
      season
    })
    return response.response
  }
}

// Export pre-configured clients for each sport
export const footballClient = new APISportsClient('football')
export const basketballClient = new APISportsClient('basketball')
export const baseballClient = new APISportsClient('baseball')
export const hockeyClient = new APISportsClient('hockey')
export const volleyballClient = new APISportsClient('volleyball')
export const handballClient = new APISportsClient('handball')
export const rugbyClient = new APISportsClient('rugby')
export const formula1Client = new APISportsClient('formula1')
export const mmaClient = new APISportsClient('mma')
export const nflClient = new APISportsClient('nfl')
export const nbaClient = new APISportsClient('nba')
export const aflClient = new APISportsClient('afl')