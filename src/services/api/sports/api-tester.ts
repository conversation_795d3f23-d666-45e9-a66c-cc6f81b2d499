import { 
  footballClient, 
  basketballClient, 
  baseballClient, 
  hockeyClient, 
  volleyballClient, 
  handballClient, 
  rugbyClient,
  allSportDBClient,
  epgClient,
  SPORTS_CHANNELS
} from './index'

interface APITestResult {
  api: string
  sport: string
  endpoint: string
  success: boolean
  dataCount: number
  error?: string
  timestamp: Date
  responseTime: number
}

export class APISportsTester {
  private results: APITestResult[] = []

  // Testar todas as APIs
  async testAllAPIs(): Promise<APITestResult[]> {
    console.log('🧪 Iniciando teste de todas as APIs...\n')
    
    // Testar API-FOOTBALL (Football)
    await this.testAPIFootball()
    
    // Testar outras APIs esportivas
    await this.testAPIBasketball()
    await this.testAPIBaseball()
    await this.testAPIHockey()
    await this.testAPIVolleyball()
    await this.testAPIHandball()
    await this.testAPIRugby()
    
    // Testar AllSportDB
    await this.testAllSportDB()
    
    // Testar EPG
    await this.testEPG()
    
    // Gerar relatório
    this.generateReport()
    
    return this.results
  }

  // Testar API Football
  private async testAPIFootball() {
    console.log('⚽ Testando API-FOOTBALL...')
    
    // Test 1: Live fixtures
    await this.testEndpoint(
      'API-FOOTBALL',
      'Football',
      'Live Fixtures',
      async () => {
        const fixtures = await footballClient.getLiveFixtures()
        return fixtures.length
      }
    )
    
    // Test 2: Fixtures by date
    await this.testEndpoint(
      'API-FOOTBALL',
      'Football',
      'Today Fixtures',
      async () => {
        const today = new Date().toISOString().split('T')[0]
        const fixtures = await footballClient.getFixturesByDate(today)
        return fixtures.length
      }
    )
    
    // Test 3: Major leagues
    await this.testEndpoint(
      'API-FOOTBALL',
      'Football',
      'Leagues',
      async () => {
        const leagues = await footballClient.getLeagues()
        return leagues.length
      }
    )
  }

  // Testar API Basketball
  private async testAPIBasketball() {
    console.log('🏀 Testando API-BASKETBALL...')
    
    await this.testEndpoint(
      'API-BASKETBALL',
      'Basketball',
      'Live Games',
      async () => {
        const games = await basketballClient.getLiveGames()
        return games.length
      }
    )
    
    await this.testEndpoint(
      'API-BASKETBALL',
      'Basketball',
      'Leagues',
      async () => {
        const leagues = await basketballClient.getLeagues()
        return leagues.length
      }
    )
  }

  // Testar API Baseball
  private async testAPIBaseball() {
    console.log('⚾ Testando API-BASEBALL...')
    
    await this.testEndpoint(
      'API-BASEBALL',
      'Baseball',
      'Live Games',
      async () => {
        const games = await baseballClient.getLiveGames()
        return games.length
      }
    )
  }

  // Testar API Hockey
  private async testAPIHockey() {
    console.log('🏒 Testando API-HOCKEY...')
    
    await this.testEndpoint(
      'API-HOCKEY',
      'Hockey',
      'Live Games',
      async () => {
        const games = await hockeyClient.getLiveGames()
        return games.length
      }
    )
  }

  // Testar API Volleyball
  private async testAPIVolleyball() {
    console.log('🏐 Testando API-VOLLEYBALL...')
    
    await this.testEndpoint(
      'API-VOLLEYBALL',
      'Volleyball',
      'Live Games',
      async () => {
        const games = await volleyballClient.getLiveGames()
        return games.length
      }
    )
  }

  // Testar API Handball
  private async testAPIHandball() {
    console.log('🤾 Testando API-HANDBALL...')
    
    await this.testEndpoint(
      'API-HANDBALL',
      'Handball',
      'Live Games',
      async () => {
        const games = await handballClient.getLiveGames()
        return games.length
      }
    )
  }

  // Testar API Rugby
  private async testAPIRugby() {
    console.log('🏉 Testando API-RUGBY...')
    
    await this.testEndpoint(
      'API-RUGBY',
      'Rugby',
      'Live Games',
      async () => {
        const games = await rugbyClient.getLiveGames()
        return games.length
      }
    )
  }

  // Testar AllSportDB
  private async testAllSportDB() {
    console.log('🌍 Testando ALLSPORTDB...')
    
    // Test 1: Today events
    await this.testEndpoint(
      'ALLSPORTDB',
      'All Sports',
      'Today Events',
      async () => {
        const events = await allSportDBClient.getTodayEvents()
        return events.length
      }
    )
    
    // Test 2: Upcoming events with live streams
    await this.testEndpoint(
      'ALLSPORTDB',
      'All Sports',
      'Live Stream Events',
      async () => {
        const events = await allSportDBClient.getLiveStreamEvents()
        return events.length
      }
    )
    
    // Test 3: Sports list
    await this.testEndpoint(
      'ALLSPORTDB',
      'All Sports',
      'Sports List',
      async () => {
        const sports = await allSportDBClient.getSports()
        return sports.length
      }
    )
  }

  // Testar EPG
  private async testEPG() {
    console.log('📺 Testando EPG...')
    
    // Testar alguns canais
    for (const channel of SPORTS_CHANNELS.slice(0, 3)) {
      await this.testEndpoint(
        'EPG',
        'TV Guide',
        `Channel ${channel.name}`,
        async () => {
          const programs = await epgClient.getChannelPrograms(channel.id)
          return programs.length
        }
      )
    }
  }

  // Helper para testar um endpoint
  private async testEndpoint(
    api: string,
    sport: string,
    endpoint: string,
    fetcher: () => Promise<number>
  ): Promise<void> {
    const startTime = Date.now()
    
    try {
      const dataCount = await fetcher()
      const responseTime = Date.now() - startTime
      
      this.results.push({
        api,
        sport,
        endpoint,
        success: true,
        dataCount,
        timestamp: new Date(),
        responseTime
      })
      
      console.log(`✅ ${endpoint}: ${dataCount} resultados (${responseTime}ms)`)
    } catch (error) {
      const responseTime = Date.now() - startTime
      
      this.results.push({
        api,
        sport,
        endpoint,
        success: false,
        dataCount: 0,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date(),
        responseTime
      })
      
      console.log(`❌ ${endpoint}: ERRO - ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  // Gerar relatório dos testes
  private generateReport(): void {
    console.log('\n📊 RELATÓRIO DE TESTES\n')
    console.log('='.repeat(80))
    
    // Agrupar por API
    const apiGroups = this.results.reduce((acc, result) => {
      if (!acc[result.api]) {
        acc[result.api] = []
      }
      acc[result.api].push(result)
      return acc
    }, {} as Record<string, APITestResult[]>)
    
    // Estatísticas por API
    Object.entries(apiGroups).forEach(([api, results]) => {
      const successful = results.filter(r => r.success).length
      const total = results.length
      const successRate = (successful / total * 100).toFixed(1)
      const avgResponseTime = (
        results.reduce((sum, r) => sum + r.responseTime, 0) / total
      ).toFixed(0)
      
      console.log(`\n${api}:`)
      console.log(`  Taxa de sucesso: ${successRate}% (${successful}/${total})`)
      console.log(`  Tempo médio de resposta: ${avgResponseTime}ms`)
      
      results.forEach(result => {
        const status = result.success ? '✅' : '❌'
        const info = result.success 
          ? `${result.dataCount} resultados`
          : `ERRO: ${result.error}`
        console.log(`  ${status} ${result.endpoint}: ${info}`)
      })
    })
    
    console.log('\n' + '='.repeat(80))
    
    // Resumo geral
    const totalSuccess = this.results.filter(r => r.success).length
    const totalTests = this.results.length
    const overallSuccessRate = (totalSuccess / totalTests * 100).toFixed(1)
    
    console.log(`\nRESUMO GERAL:`)
    console.log(`  Total de testes: ${totalTests}`)
    console.log(`  Sucessos: ${totalSuccess}`)
    console.log(`  Falhas: ${totalTests - totalSuccess}`)
    console.log(`  Taxa de sucesso geral: ${overallSuccessRate}%`)
    
    // APIs com problemas
    const failedAPIs = this.results
      .filter(r => !r.success)
      .map(r => r.api)
      .filter((v, i, a) => a.indexOf(v) === i)
    
    if (failedAPIs.length > 0) {
      console.log(`\n⚠️  APIs com problemas: ${failedAPIs.join(', ')}`)
    }
  }

  // Obter resumo dos resultados
  getSummary(): {
    totalTests: number
    successful: number
    failed: number
    successRate: number
    failedAPIs: string[]
  } {
    const successful = this.results.filter(r => r.success).length
    const totalTests = this.results.length
    
    const failedAPIs = this.results
      .filter(r => !r.success)
      .map(r => r.api)
      .filter((v, i, a) => a.indexOf(v) === i)
    
    return {
      totalTests,
      successful,
      failed: totalTests - successful,
      successRate: (successful / totalTests * 100),
      failedAPIs
    }
  }
}

// Função utilitária para testar todas as APIs
export async function testAllSportsAPIs(): Promise<APITestResult[]> {
  const tester = new APISportsTester()
  return await tester.testAllAPIs()
}

// Exportar para uso no console do navegador
if (typeof window !== 'undefined') {
  (window as any).testSportsAPIs = testAllSportsAPIs
}