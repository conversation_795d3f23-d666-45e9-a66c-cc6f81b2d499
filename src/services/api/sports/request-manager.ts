/**
 * Gerenciador de Requisições para APIs Esportivas
 * Controla o limite de requisições diárias e distribui as chamadas
 */

interface APIQuota {
  apiName: string
  dailyLimit: number
  usedToday: number
  resetsAt: Date
  lastRequest: Date | null
}

interface RequestLog {
  timestamp: Date
  api: string
  endpoint: string
  success: boolean
  responseTime: number
}

class RequestManager {
  private quotas: Map<string, APIQuota> = new Map()
  private requestLogs: RequestLog[] = []
  private readonly STORAGE_KEY = 'sports-api-quotas'
  
  // Limites diários por API (12 APIs da API Sports)
  private readonly API_LIMITS = {
    'api-football': 100,      // API Sports Football - 100/dia
    'api-basketball': 100,    // API Sports Basketball - 100/dia
    'api-baseball': 100,      // API Sports Baseball - 100/dia
    'api-hockey': 100,        // API Sports Hockey - 100/dia
    'api-volleyball': 100,    // API Sports Volleyball - 100/dia
    'api-handball': 100,      // API Sports Handball - 100/dia
    'api-rugby': 100,         // API Sports Rugby - 100/dia
    'api-formula1': 100,      // API Sports Formula 1 - 100/dia
    'api-mma': 100,           // API Sports MMA - 100/dia
    'api-nfl': 100,           // API Sports NFL (American Football) - 100/dia
    'api-nba': 100,           // API Sports NBA - 100/dia
    'api-afl': 100,           // API Sports AFL - 100/dia
    'allsportdb': 3333,       // ~100k/mês (distribuído entre 3 tipos de chamadas)
    'epg': 1000              // Estimado
  }
  
  // Distribuição de requisições (total: 100 req/dia por API Sports)
  private readonly REQUEST_DISTRIBUTION = {
    'live-fixtures': 50,      // 50% - Jogos ao vivo (prioritário)
    'upcoming-events': 25,    // 25% - Próximos eventos
    'today-events': 12,       // 12% - Eventos de hoje
    'other': 13              // 13% - Outras requisições
  }
  
  constructor() {
    this.loadQuotas()
    this.setupDailyReset()
    
    // Auto-reset quotas se os limites mudaram
    this.checkAndUpdateLimits()
  }
  
  // Carregar quotas do localStorage
  private loadQuotas(): void {
    if (typeof window === 'undefined') return
    
    const stored = localStorage.getItem(this.STORAGE_KEY)
    if (stored) {
      try {
        const data = JSON.parse(stored)
        Object.entries(data).forEach(([api, quota]: [string, any]) => {
          this.quotas.set(api, {
            ...quota,
            resetsAt: new Date(quota.resetsAt),
            lastRequest: quota.lastRequest ? new Date(quota.lastRequest) : null
          })
        })
      } catch (error) {
        console.error('Erro ao carregar quotas:', error)
        this.initializeQuotas()
      }
    } else {
      this.initializeQuotas()
    }
  }
  
  // Inicializar quotas
  private initializeQuotas(): void {
    const tomorrow = new Date()
    tomorrow.setDate(tomorrow.getDate() + 1)
    tomorrow.setHours(0, 0, 0, 0)
    
    Object.entries(this.API_LIMITS).forEach(([api, limit]) => {
      this.quotas.set(api, {
        apiName: api,
        dailyLimit: limit,
        usedToday: 0,
        resetsAt: tomorrow,
        lastRequest: null
      })
    })
    
    this.saveQuotas()
  }
  
  // Salvar quotas no localStorage
  private saveQuotas(): void {
    if (typeof window === 'undefined') return
    
    const data: Record<string, APIQuota> = {}
    this.quotas.forEach((quota, api) => {
      data[api] = quota
    })
    
    localStorage.setItem(this.STORAGE_KEY, JSON.stringify(data))
  }
  
  // Verificar e atualizar limites se mudaram
  private checkAndUpdateLimits(): void {
    let needsUpdate = false
    
    this.quotas.forEach((quota, api) => {
      const currentLimit = this.API_LIMITS[api]
      if (currentLimit && quota.dailyLimit !== currentLimit) {
        console.log(`🔄 Atualizando limite de ${api}: ${quota.dailyLimit} -> ${currentLimit}`)
        quota.dailyLimit = currentLimit
        needsUpdate = true
      }
    })
    
    if (needsUpdate) {
      console.log('✅ Limites atualizados com sucesso!')
      this.saveQuotas()
    }
  }
  
  // Método público para resetar todas as quotas
  resetAllQuotas(): void {
    console.log('🔄 Resetando todas as quotas...')
    this.initializeQuotas()
    console.log('✅ Todas as quotas foram resetadas!')
  }
  
  // Configurar reset diário
  private setupDailyReset(): void {
    // Verificar e resetar se necessário
    this.checkAndResetQuotas()
    
    // Verificar a cada hora
    setInterval(() => {
      this.checkAndResetQuotas()
    }, 60 * 60 * 1000)
  }
  
  // Verificar e resetar quotas se necessário
  private checkAndResetQuotas(): void {
    const now = new Date()
    let needsSave = false
    
    this.quotas.forEach((quota) => {
      if (now >= quota.resetsAt) {
        console.log(`🔄 Resetando quota para ${quota.apiName}`)
        quota.usedToday = 0
        quota.resetsAt = new Date(now)
        quota.resetsAt.setDate(quota.resetsAt.getDate() + 1)
        quota.resetsAt.setHours(0, 0, 0, 0)
        needsSave = true
      }
    })
    
    if (needsSave) {
      this.saveQuotas()
    }
  }
  
  // Verificar se pode fazer requisição
  canMakeRequest(api: string): boolean {
    const quota = this.quotas.get(api)
    if (!quota) return false
    
    // Verificar limite diário
    if (quota.usedToday >= quota.dailyLimit) {
      console.warn(`⚠️ Limite diário atingido para ${api}: ${quota.usedToday}/${quota.dailyLimit}`)
      return false
    }
    
    // Verificar intervalo mínimo entre requisições
    if (quota.lastRequest) {
      const timeSinceLastRequest = Date.now() - quota.lastRequest.getTime()
      
      // Intervalos mínimos por API
      let minInterval: number
      if (api === 'allsportdb') {
        // AllSportsDB: 100k/mês = ~3.333/dia = ~138/hora = ~2.3/min
        // Vamos usar 30 segundos para ser conservador e não esgotar o limite
        minInterval = 30 * 1000 // 30 segundos para AllSportDB (100k requisições/mês)
      } else if (api.startsWith('api-')) {
        // API Sports: 100 req/dia por API = 1 req a cada 15 minutos
        minInterval = 15 * 60 * 1000 // 15 minutos para API Sports
      } else {
        minInterval = 60 * 1000 // 1 minuto para outros
      }
      
      if (timeSinceLastRequest < minInterval) {
        const waitTime = minInterval < 60000 
          ? Math.ceil((minInterval - timeSinceLastRequest) / 1000) + ' segundos'
          : Math.ceil((minInterval - timeSinceLastRequest) / 1000 / 60) + ' minutos'
        console.log(`⏳ Aguarde ${waitTime} para próxima requisição de ${api}`)
        return false
      }
    }
    
    return true
  }
  
  // Registrar requisição
  registerRequest(api: string, endpoint: string, success: boolean, responseTime: number): void {
    const quota = this.quotas.get(api)
    if (!quota) return
    
    // Atualizar quota
    quota.usedToday++
    quota.lastRequest = new Date()
    
    // Registrar log
    this.requestLogs.push({
      timestamp: new Date(),
      api,
      endpoint,
      success,
      responseTime
    })
    
    // Manter apenas últimas 1000 entradas
    if (this.requestLogs.length > 1000) {
      this.requestLogs = this.requestLogs.slice(-1000)
    }
    
    this.saveQuotas()
    
    console.log(`📊 ${api}: ${quota.usedToday}/${quota.dailyLimit} requisições hoje`)
  }
  
  // Obter próximo horário disponível para requisição
  getNextAvailableTime(api: string): Date | null {
    const quota = this.quotas.get(api)
    if (!quota) return null
    
    // Se atingiu limite diário, retorna horário de reset
    if (quota.usedToday >= quota.dailyLimit) {
      return quota.resetsAt
    }
    
    // Se tem que esperar intervalo mínimo
    if (quota.lastRequest) {
      let minInterval: number
      if (api === 'allsportdb') {
        minInterval = 30 * 1000 // 30 segundos
      } else if (api.startsWith('api-')) {
        minInterval = 15 * 60 * 1000 // 15 minutos
      } else {
        minInterval = 60 * 1000 // 1 minuto para outros
      }
      
      const nextTime = new Date(quota.lastRequest.getTime() + minInterval)
      if (nextTime > new Date()) {
        return nextTime
      }
    }
    
    return new Date() // Pode fazer agora
  }
  
  // Obter estatísticas
  getStats(): {
    quotas: APIQuota[]
    totalRequestsToday: number
    apiSportsUsage: number
    apiSportsRemaining: number
    nextResetIn: string
    requestsLast24h: number
    successRate: number
  } {
    const quotas = Array.from(this.quotas.values())
    const apiSportsQuotas = quotas.filter(q => q.apiName.startsWith('api-'))
    const apiSportsUsage = apiSportsQuotas.reduce((sum, q) => sum + q.usedToday, 0)
    const apiSportsLimit = apiSportsQuotas.reduce((sum, q) => sum + q.dailyLimit, 0)
    
    // Calcular tempo até próximo reset
    const now = new Date()
    const nextReset = quotas[0]?.resetsAt || new Date()
    const timeUntilReset = nextReset.getTime() - now.getTime()
    const hoursUntilReset = Math.floor(timeUntilReset / (1000 * 60 * 60))
    const minutesUntilReset = Math.floor((timeUntilReset % (1000 * 60 * 60)) / (1000 * 60))
    
    // Calcular requisições nas últimas 24h
    const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000)
    const requestsLast24h = this.requestLogs.filter(log => log.timestamp > oneDayAgo).length
    
    // Calcular taxa de sucesso
    const recentLogs = this.requestLogs.slice(-100)
    const successCount = recentLogs.filter(log => log.success).length
    const successRate = recentLogs.length > 0 ? (successCount / recentLogs.length) * 100 : 100
    
    return {
      quotas,
      totalRequestsToday: quotas.reduce((sum, q) => sum + q.usedToday, 0),
      apiSportsUsage,
      apiSportsRemaining: apiSportsLimit - apiSportsUsage,
      nextResetIn: `${hoursUntilReset}h ${minutesUntilReset}m`,
      requestsLast24h,
      successRate
    }
  }
  
  // Planejar distribuição otimizada de requisições
  getOptimalSchedule(): {
    api: string
    endpoint: string
    nextTime: Date
    priority: number
  }[] {
    const schedule = []
    const now = new Date()
    
    // Prioridade 1: Jogos ao vivo (a cada 15 minutos durante horários de pico)
    const peakHours = [14, 15, 16, 17, 18, 19, 20, 21, 22] // 14h-22h
    const currentHour = now.getHours()
    
    if (peakHours.includes(currentHour)) {
      // Durante horário de pico, priorizar jogos ao vivo
      schedule.push({
        api: 'api-football',
        endpoint: 'live-fixtures',
        nextTime: this.getNextAvailableTime('api-football') || now,
        priority: 1
      })
    }
    
    // Prioridade 2: Próximos eventos
    schedule.push({
      api: 'allsportdb',
      endpoint: 'upcoming-events',
      nextTime: new Date(now.getTime() + 15 * 60 * 1000),
      priority: 2
    })
    
    // Prioridade 3: Eventos de hoje
    schedule.push({
      api: 'allsportdb',
      endpoint: 'today-events',
      nextTime: new Date(now.getTime() + 30 * 60 * 1000),
      priority: 3
    })
    
    return schedule.sort((a, b) => a.nextTime.getTime() - b.nextTime.getTime())
  }
}

// Singleton instance
export const requestManager = new RequestManager()

// Wrapper para requisições com controle de quota
export async function makeAPIRequest<T>(
  api: string,
  endpoint: string,
  fetcher: () => Promise<T>,
  cacheTTL?: number // Tempo de cache em milissegundos (não implementado ainda)
): Promise<T> {
  // Verificar se pode fazer requisição
  if (!requestManager.canMakeRequest(api)) {
    const nextTime = requestManager.getNextAvailableTime(api)
    const now = Date.now()
    const waitTime = nextTime ? nextTime.getTime() - now : 0
    
    // Calcular tempo de espera mais preciso
    let waitMessage: string
    if (waitTime <= 0) {
      waitMessage = "agora"
    } else if (waitTime < 60000) {
      waitMessage = `${Math.ceil(waitTime / 1000)} segundos`
    } else {
      waitMessage = `${Math.ceil(waitTime / 1000 / 60)} minutos`
    }
    
    throw new Error(
      `Limite de requisições atingido para ${api}. ` +
      `Próxima requisição disponível em ${waitMessage}.`
    )
  }
  
  const startTime = Date.now()
  let success = false
  
  try {
    const result = await fetcher()
    success = true
    return result
  } finally {
    const responseTime = Date.now() - startTime
    requestManager.registerRequest(api, endpoint, success, responseTime)
  }
}

// Exportar para debug
if (typeof window !== 'undefined') {
  (window as any).requestManager = requestManager
}