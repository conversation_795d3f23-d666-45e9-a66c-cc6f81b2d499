import { IPTVStream, IPTVChannel, IPTVGuide, EnrichedIPTVStream, IPTVFilters } from '@/types/iptv-org.types'
import { SELECTED_SPORTS_CHANNELS, isSelectedSportsChannel } from '@/lib/selected-sports-channels'

class IPTVOrgService {
  private streams: IPTVStream[] = []
  private channels: IPTVChannel[] = []
  private guides: IPTVGuide[] = []
  private lastFetch: Date | null = null
  private cacheTimeout = 6 * 60 * 60 * 1000 // 6 horas de cache
  
  private readonly STREAMS_URL = 'https://iptv-org.github.io/api/streams.json'
  private readonly CHANNELS_URL = 'https://iptv-org.github.io/api/channels.json'
  private readonly GUIDES_URL = 'https://iptv-org.github.io/api/guides.json'

  constructor() {
    console.log('🎬 IPTVOrgService inicializado')
  }

  private shouldRefetch(): boolean {
    if (!this.lastFetch) return true
    return Date.now() - this.lastFetch.getTime() > this.cacheTimeout
  }

  async fetchData(forceRefresh = false): Promise<void> {
    if (!forceRefresh && !this.shouldRefetch() && this.streams.length > 0) {
      console.log('📦 Usando dados IPTV em cache')
      return
    }

    console.log('🔄 Buscando dados IPTV-ORG...')
    
    try {
      // Buscar todos os dados em paralelo
      const [streamsRes, channelsRes, guidesRes] = await Promise.all([
        fetch(this.STREAMS_URL),
        fetch(this.CHANNELS_URL),
        fetch(this.GUIDES_URL)
      ])

      if (!streamsRes.ok || !channelsRes.ok) {
        throw new Error('Erro ao buscar dados IPTV')
      }

      this.streams = await streamsRes.json()
      this.channels = await channelsRes.json()
      
      // Guides é opcional
      if (guidesRes.ok) {
        this.guides = await guidesRes.json()
      }

      this.lastFetch = new Date()
      
      console.log(`✅ Dados IPTV carregados:`)
      console.log(`   - ${this.streams.length} streams`)
      console.log(`   - ${this.channels.length} canais`)
      console.log(`   - ${this.guides.length} guias EPG`)
      
    } catch (error) {
      console.error('❌ Erro ao buscar dados IPTV:', error)
      throw error
    }
  }

  async getSportsChannels(filters: IPTVFilters = {}): Promise<EnrichedIPTVStream[]> {
    await this.fetchData()

    // Filtrar apenas canais esportivos E que estão na lista selecionada
    const sportsChannels = this.channels.filter(channel => 
      channel.categories?.includes('sports') && isSelectedSportsChannel(channel.id)
    )

    console.log(`🏆 ${sportsChannels.length} canais esportivos selecionados (de ${SELECTED_SPORTS_CHANNELS.size} no total)`)

    // Aplicar filtros adicionais
    let filteredChannels = sportsChannels

    if (filters.country) {
      filteredChannels = filteredChannels.filter(ch => 
        ch.country.toLowerCase() === filters.country?.toLowerCase()
      )
    }

    if (filters.language) {
      filteredChannels = filteredChannels.filter(ch => 
        ch.languages?.includes(filters.language!)
      )
    }

    if (filters.search) {
      const searchLower = filters.search.toLowerCase()
      filteredChannels = filteredChannels.filter(ch => 
        ch.name.toLowerCase().includes(searchLower) ||
        ch.alt_names?.some(alt => alt.toLowerCase().includes(searchLower))
      )
    }

    // Criar um mapa de canais para lookup rápido
    const channelMap = new Map(filteredChannels.map(ch => [ch.id, ch]))

    // Encontrar streams correspondentes
    const enrichedStreams: EnrichedIPTVStream[] = []
    const seenChannels = new Set<string>()
    
    for (const stream of this.streams) {
      const channelData = channelMap.get(stream.channel)
      if (channelData && !seenChannels.has(stream.channel)) {
        // Evitar duplicatas - pegar apenas o primeiro stream de cada canal
        seenChannels.add(stream.channel)
        // Combinar dados do stream com dados do canal
        enrichedStreams.push({
          ...stream,
          channelData,
          isLive: true // Assumir que todos os streams estão ao vivo
        })
      }
    }

    console.log(`📺 ${enrichedStreams.length} streams esportivos disponíveis`)

    // Filtrar por qualidade se especificado
    if (filters.quality) {
      const qualityFilter = filters.quality.toLowerCase()
      return enrichedStreams.filter(stream => {
        const url = stream.url.toLowerCase()
        if (qualityFilter === 'hd' || qualityFilter === '720p') {
          return url.includes('720') || url.includes('hd')
        }
        if (qualityFilter === 'fhd' || qualityFilter === '1080p') {
          return url.includes('1080') || url.includes('fhd')
        }
        if (qualityFilter === '4k') {
          return url.includes('4k') || url.includes('2160')
        }
        return true
      })
    }

    return enrichedStreams
  }

  async getChannelsByIds(channelIds: string[]): Promise<EnrichedIPTVStream[]> {
    await this.fetchData()

    const channelMap = new Map(this.channels.map(ch => [ch.id, ch]))
    const enrichedStreams: EnrichedIPTVStream[] = []

    for (const stream of this.streams) {
      if (channelIds.includes(stream.channel)) {
        const channelData = channelMap.get(stream.channel)
        enrichedStreams.push({
          ...stream,
          channelData,
          isLive: true
        })
      }
    }

    return enrichedStreams
  }

  async getPopularSportsChannels(limit = 20): Promise<EnrichedIPTVStream[]> {
    try {
      // Buscar canais esportivos reais da API
      const sportsChannels = await this.getSportsChannels({
        // Priorizar canais em espanhol e português
        language: 'spa'
      })
      
      // Se não encontrar em espanhol, buscar todos os esportivos
      if (sportsChannels.length < limit) {
        const allSports = await this.getSportsChannels({})
        
        // Priorizar canais conhecidos
        const priorityChannels = ['ESPN', 'Fox Sports', 'Sky Sports', 'beIN Sports', 'DAZN', 'TNT Sports', 'Eurosport']
        
        const sorted = allSports.sort((a, b) => {
          const aName = a.channelData?.name || ''
          const bName = b.channelData?.name || ''
          
          // Priorizar canais conhecidos
          const aPriority = priorityChannels.findIndex(p => aName.toLowerCase().includes(p.toLowerCase()))
          const bPriority = priorityChannels.findIndex(p => bName.toLowerCase().includes(p.toLowerCase()))
          
          if (aPriority !== -1 && bPriority !== -1) {
            return aPriority - bPriority
          }
          if (aPriority !== -1) return -1
          if (bPriority !== -1) return 1
          
          return 0
        })
        
        return sorted.slice(0, limit)
      }
      
      return sportsChannels.slice(0, limit)
      
    } catch (error) {
      console.error('Erro ao buscar canais esportivos populares:', error)
      
      // Se falhar, retornar alguns canais de demonstração
      return [
        {
          channel: 'demo-sports',
          url: 'https://test-streams.mux.dev/x36xhzz/x36xhzz.m3u8',
          channelData: {
            id: 'demo-sports',
            name: 'Sports Channel Demo',
            country: 'INT',
            languages: ['eng'],
            categories: ['sports'],
            is_nsfw: false,
            logo: null
          },
          isLive: true
        }
      ]
    }
  }

  async getStreamByChannelId(channelId: string): Promise<EnrichedIPTVStream | null> {
    await this.fetchData()

    const stream = this.streams.find(s => s.channel === channelId)
    if (!stream) return null

    const channelData = this.channels.find(ch => ch.id === channelId)
    
    return {
      ...stream,
      channelData,
      isLive: true
    }
  }

  async getChannelEPG(channelId: string): Promise<IPTVGuide | null> {
    await this.fetchData()
    return this.guides.find(g => g.channel === channelId) || null
  }

  // Método para validar se um stream está funcionando
  async validateStream(streamUrl: string): Promise<boolean> {
    try {
      const response = await fetch(streamUrl, {
        method: 'HEAD',
        signal: AbortSignal.timeout(5000) // 5 segundos de timeout
      })
      return response.ok
    } catch {
      return false
    }
  }

  // Limpar cache manualmente
  clearCache(): void {
    this.streams = []
    this.channels = []
    this.guides = []
    this.lastFetch = null
    console.log('🧹 Cache IPTV limpo')
  }
}

// Singleton
export const iptvOrgService = new IPTVOrgService()