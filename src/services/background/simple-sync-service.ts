/**
 * Serviço simplificado de sincronização
 * Versão minimalista para evitar erros de importação
 */

class SimpleSyncService {
  private isRunning = false
  private interval: NodeJS.Timeout | null = null
  
  start() {
    if (this.isRunning) {
      console.log('⚠️ Serviço já está em execução')
      return
    }
    
    this.isRunning = true
    console.log('🚀 Serviço de sincronização iniciado (simplificado)')
    
    // Chamar TODAS as APIs a cada 15 minutos
    this.interval = setInterval(async () => {
      console.log('📊 Chamando TODAS as 12 APIs Sports:', new Date().toLocaleTimeString())
      
      try {
        const response = await fetch('/api/sports/live-fixtures')
        const data = await response.json()
        
        if (data.summary) {
          console.log('📋 Resumo das chamadas:', {
            total: data.summary.total,
            sucesso: data.summary.apisSuccess,
            falhas: data.summary.apisFailed
          })
          
          if (data.summary.details) {
            console.log('📊 Detalhes por API:')
            data.summary.details.forEach((detail: any) => {
              console.log(`  - ${detail.api}: ${detail.success ? '✅' : '❌'} (${detail.count} fixtures)`)
            })
          }
        }
      } catch (error) {
        console.error('❌ Erro ao chamar APIs:', error)
      }
    }, 15 * 60 * 1000) // 15 minutos
    
    // Fazer primeira chamada imediatamente
    setTimeout(() => {
      if (this.isRunning) {
        console.log('🚀 Fazendo primeira chamada para todas as APIs...')
        fetch('/api/sports/live-fixtures')
          .then(res => res.json())
          .then(data => {
            console.log('✅ Primeira chamada concluída!')
            if (data.summary) {
              console.log(`📊 ${data.summary.apisSuccess}/12 APIs responderam com sucesso`)
              console.log(`📋 Total de ${data.summary.total} fixtures ao vivo encontrados`)
            }
          })
          .catch(err => console.error('❌ Erro na primeira chamada:', err))
      }
    }, 1000)
  }
  
  stop() {
    this.isRunning = false
    if (this.interval) {
      clearInterval(this.interval)
      this.interval = null
    }
    console.log('🛑 Serviço de sincronização parado')
  }
  
  getStatus() {
    return {
      isRunning: this.isRunning,
      message: this.isRunning ? 'Serviço ativo' : 'Serviço parado'
    }
  }
}

// Criar instância global apenas no cliente
let service: SimpleSyncService | null = null

export function getSimpleSyncService() {
  if (typeof window === 'undefined') {
    return null
  }
  
  if (!service) {
    service = new SimpleSyncService()
  }
  
  return service
}

// Comandos globais
if (typeof window !== 'undefined') {
  (window as any).simpleSyncStart = () => {
    const svc = getSimpleSyncService()
    if (svc) svc.start()
  }
  
  (window as any).simpleSyncStop = () => {
    const svc = getSimpleSyncService()
    if (svc) svc.stop()
  }
  
  (window as any).simpleSyncStatus = () => {
    const svc = getSimpleSyncService()
    if (svc) console.log(svc.getStatus())
  }
  
  // Comando para resetar rotação e forçar chamada
  (window as any).resetRotation = async () => {
    console.log('🔄 Resetando rotação de APIs...')
    localStorage.removeItem('api-rotation-state')
    
    // Fazer uma chamada imediata
    try {
      const response = await fetch('/api/sports/live-fixtures')
      const data = await response.json()
      console.log('✅ Reset completo! Primeira API chamada:', data.source)
      if (data.rotation) {
        console.log('📊 Estado da rotação:', data.rotation)
      }
    } catch (error) {
      console.error('❌ Erro ao resetar:', error)
    }
  }
  
  // Comando para chamar todas as APIs manualmente
  (window as any).callAllAPIs = async () => {
    console.log('📡 Chamando todas as 12 APIs simultaneamente...')
    
    try {
      const response = await fetch('/api/sports/live-fixtures')
      const data = await response.json()
      
      if (data.summary) {
        console.log('✅ Chamada concluída!')
        console.log(`📊 ${data.summary.apisSuccess}/12 APIs responderam com sucesso`)
        console.log(`📋 Total de ${data.summary.total} fixtures ao vivo encontrados`)
        
        if (data.summary.details) {
          console.log('\n📊 Detalhes por API:')
          data.summary.details.forEach((detail: any) => {
            console.log(`  ${detail.api}: ${detail.success ? '✅' : '❌'} (${detail.count} fixtures)`)
          })
        }
      }
    } catch (error) {
      console.error('❌ Erro ao chamar APIs:', error)
    }
  }
}