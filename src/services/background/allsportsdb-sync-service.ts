/**
 * Serviço de sincronização para AllSportsDB
 * Intervalo: 30 segundos (API permite mais requisições)
 */

class AllSportsDBSyncService {
  private isRunning = false
  private interval: NodeJS.Timeout | null = null
  
  start() {
    if (this.isRunning) {
      console.log('⚠️ AllSportsDB sync já está em execução')
      return
    }
    
    this.isRunning = true
    console.log('🏃 AllSportsDB sync iniciado (30 segundos)')
    
    // Chamar AllSportsDB a cada 30 segundos
    this.interval = setInterval(async () => {
      console.log('🏆 Buscando eventos ao vivo AllSportsDB:', new Date().toLocaleTimeString())
      
      try {
        const response = await fetch('/api/sports/live-events')
        const data = await response.json()
        
        if (data.success) {
          console.log(`✅ AllSportsDB: ${data.total} eventos encontrados`)
        } else {
          console.log('❌ AllSportsDB: Erro na resposta')
        }
      } catch (error) {
        console.error('❌ Erro ao chamar AllSportsDB:', error)
      }
    }, 30 * 1000) // 30 segundos
    
    // Primeira chamada após 1 segundo
    setTimeout(() => {
      if (this.isRunning) {
        console.log('🏆 Primeira chamada AllSportsDB...')
        fetch('/api/sports/live-events')
          .then(res => res.json())
          .then(data => {
            if (data.success) {
              console.log(`✅ AllSportsDB inicial: ${data.total} eventos`)
            }
          })
          .catch(err => console.error('❌ Erro:', err))
      }
    }, 1000)
  }
  
  stop() {
    this.isRunning = false
    if (this.interval) {
      clearInterval(this.interval)
      this.interval = null
    }
    console.log('🛑 AllSportsDB sync parado')
  }
  
  getStatus() {
    return {
      isRunning: this.isRunning,
      service: 'AllSportsDB',
      interval: '30 segundos',
      message: this.isRunning ? 'Serviço ativo' : 'Serviço parado'
    }
  }
}

// Instância global
let service: AllSportsDBSyncService | null = null

export function getAllSportsDBSyncService() {
  if (typeof window === 'undefined') {
    return null
  }
  
  if (!service) {
    service = new AllSportsDBSyncService()
  }
  
  return service
}

// Comandos globais para AllSportsDB
if (typeof window !== 'undefined') {
  (window as any).allSportsStart = () => {
    const svc = getAllSportsDBSyncService()
    if (svc) svc.start()
  }
  
  (window as any).allSportsStop = () => {
    const svc = getAllSportsDBSyncService()
    if (svc) svc.stop()
  }
  
  (window as any).allSportsStatus = () => {
    const svc = getAllSportsDBSyncService()
    if (svc) console.log(svc.getStatus())
  }
}