/**
 * Serviço de Sincronização de Dados Esportivos
 * 
 * Gerencia as atualizações automáticas respeitando os limites:
 * - API Sports: 1 requisição a cada 15 minutos por API (12 APIs = 96 req/dia cada)
 * - AllSportsDB: 100k/mês (~3.333/dia)
 */

import { requestManager } from '@/services/api/sports/request-manager'
import { SportsDataService } from '@/services/api/sports/sports-data.service'

interface APISchedule {
  api: string
  type: 'api-sports' | 'allsportdb'
  lastUpdate: Date | null
  nextUpdate: Date | null
  isProcessing: boolean
}

class SportsSyncService {
  private schedules: Map<string, APISchedule> = new Map()
  private mainInterval: NodeJS.Timeout | null = null
  private isRunning = false
  
  // Lista das 12 APIs Sports
  private readonly API_SPORTS_APIS = [
    'api-football',
    'api-basketball', 
    'api-baseball',
    'api-hockey',
    'api-volleyball',
    'api-handball',
    'api-rugby',
    'api-formula1',
    'api-mma',
    'api-nfl',
    'api-nba',
    'api-afl'
  ]
  
  constructor() {
    this.initializeSchedules()
  }
  
  private initializeSchedules() {
    // Inicializar schedules para cada API Sports
    this.API_SPORTS_APIS.forEach((api, index) => {
      this.schedules.set(api, {
        api,
        type: 'api-sports',
        lastUpdate: null,
        nextUpdate: null,
        isProcessing: false
      })
    })
    
    // AllSportsDB
    this.schedules.set('allsportdb', {
      api: 'allsportdb',
      type: 'allsportdb',
      lastUpdate: null,
      nextUpdate: null,
      isProcessing: false
    })
  }
  
  /**
   * Inicia o serviço de sincronização
   */
  start() {
    if (this.isRunning) {
      console.log('⚠️ Serviço de sincronização já está em execução')
      return
    }
    
    this.isRunning = true
    console.log('🚀 Iniciando serviço de sincronização de dados esportivos')
    console.log('📊 Configuração:')
    console.log('  • 12 APIs Sports: 1 req a cada 15 minutos por API')
    console.log('  • AllSportsDB: 1 req a cada 30 segundos')
    console.log('  • Total: ~1.200 req/dia para todas as APIs')
    
    // Processar uma vez imediatamente
    this.processNextAPI()
    
    // Configurar intervalo principal - verifica a cada 10 segundos
    this.mainInterval = setInterval(() => {
      if (this.isRunning) {
        this.processNextAPI()
      }
    }, 10 * 1000) // 10 segundos
  }
  
  /**
   * Para o serviço
   */
  stop() {
    console.log('🛑 Parando serviço de sincronização...')
    this.isRunning = false
    
    if (this.mainInterval) {
      clearInterval(this.mainInterval)
      this.mainInterval = null
    }
  }
  
  /**
   * Processa a próxima API disponível
   */
  private async processNextAPI() {
    if (!this.isRunning) return
    
    const now = new Date()
    
    // Encontrar próxima API que pode ser atualizada
    for (const [apiName, schedule] of this.schedules) {
      // Skip se já está processando
      if (schedule.isProcessing) continue
      
      // Verificar se pode fazer requisição
      if (requestManager.canMakeRequest(apiName)) {
        // Se nunca foi atualizada ou já passou do tempo
        if (!schedule.lastUpdate || !schedule.nextUpdate || now >= schedule.nextUpdate) {
          // Processar esta API
          await this.updateAPI(apiName, schedule)
          break // Processar apenas uma por vez
        }
      }
    }
  }
  
  /**
   * Atualiza uma API específica
   */
  private async updateAPI(apiName: string, schedule: APISchedule) {
    if (schedule.isProcessing) return
    
    schedule.isProcessing = true
    console.log(`\n🔄 Processando ${apiName}...`)
    
    try {
      const startTime = Date.now()
      
      if (apiName === 'allsportdb') {
        // AllSportsDB - buscar eventos ao vivo
        const events = await SportsDataService.getAllLiveResults()
        console.log(`✅ AllSportsDB: ${events.length} eventos ao vivo`)
      } else {
        // API Sports - fazer chamada individual
        const events = await SportsDataService.getLiveResultsForAPI(apiName)
        console.log(`✅ ${apiName}: ${events.length} eventos ao vivo`)
      }
      
      const responseTime = Date.now() - startTime
      
      // Atualizar schedule
      schedule.lastUpdate = new Date()
      
      // Calcular próxima atualização
      if (apiName === 'allsportdb') {
        // AllSportsDB: próxima em 30 segundos
        schedule.nextUpdate = new Date(Date.now() + 30 * 1000)
      } else {
        // API Sports: próxima em 15 minutos
        schedule.nextUpdate = new Date(Date.now() + 15 * 60 * 1000)
      }
      
      console.log(`⏱️ Tempo de resposta: ${responseTime}ms`)
      console.log(`⏰ Próxima atualização: ${schedule.nextUpdate.toLocaleTimeString()}`)
      
    } catch (error: any) {
      console.error(`❌ Erro ao processar ${apiName}:`, error.message)
      
      // Se for erro de limite, ajustar próxima tentativa
      if (error.message?.includes('Limite de requisições')) {
        const nextTime = requestManager.getNextAvailableTime(apiName)
        if (nextTime) {
          schedule.nextUpdate = nextTime
          console.log(`⏳ Reagendado para: ${nextTime.toLocaleTimeString()}`)
        }
      }
    } finally {
      schedule.isProcessing = false
    }
  }
  
  /**
   * Força atualização de todas as APIs
   */
  async forceUpdateAll() {
    console.log('🔄 Forçando atualização de TODAS as APIs...')
    
    // Chamar o método que já processa todas as APIs sequencialmente
    try {
      const events = await SportsDataService.getAllLiveResults()
      console.log(`✅ Total de eventos encontrados: ${events.length}`)
      
      // Resetar schedules
      const now = new Date()
      this.schedules.forEach((schedule, api) => {
        schedule.lastUpdate = now
        if (api === 'allsportdb') {
          schedule.nextUpdate = new Date(now.getTime() + 30 * 1000)
        } else {
          schedule.nextUpdate = new Date(now.getTime() + 15 * 60 * 1000)
        }
      })
      
    } catch (error) {
      console.error('❌ Erro durante atualização forçada:', error)
    }
  }
  
  /**
   * Retorna o status do serviço
   */
  getStatus() {
    const schedules = Array.from(this.schedules.entries()).map(([api, schedule]) => {
      const quota = requestManager.getStats().quotas.find(q => q.apiName === api)
      
      return {
        api,
        type: schedule.type,
        lastUpdate: schedule.lastUpdate,
        nextUpdate: schedule.nextUpdate,
        isProcessing: schedule.isProcessing,
        quotaUsed: quota?.usedToday || 0,
        quotaLimit: quota?.dailyLimit || 0,
        canUpdate: requestManager.canMakeRequest(api)
      }
    })
    
    const stats = requestManager.getStats()
    
    return {
      isRunning: this.isRunning,
      schedules,
      totalRequests: stats.totalRequestsToday,
      apiSportsUsage: stats.apiSportsUsage,
      apiSportsRemaining: stats.apiSportsRemaining,
      nextReset: stats.nextResetIn
    }
  }
  
  /**
   * Exibe estatísticas no console
   */
  logStatus() {
    const status = this.getStatus()
    
    console.log('\n📊 STATUS DO SERVIÇO DE SINCRONIZAÇÃO')
    console.log('=====================================')
    console.log(`Estado: ${status.isRunning ? '🟢 ATIVO' : '🔴 PARADO'}`)
    console.log(`Total de requisições hoje: ${status.totalRequests}`)
    console.log(`API Sports: ${status.apiSportsUsage}/${status.apiSportsUsage + status.apiSportsRemaining} usadas`)
    console.log(`Próximo reset: ${status.nextReset}`)
    
    console.log('\n📋 APIS:')
    status.schedules.forEach(schedule => {
      const statusIcon = schedule.isProcessing ? '🔄' : schedule.canUpdate ? '✅' : '⏳'
      const lastUpdate = schedule.lastUpdate ? schedule.lastUpdate.toLocaleTimeString() : 'Nunca'
      const nextUpdate = schedule.nextUpdate ? schedule.nextUpdate.toLocaleTimeString() : 'Agora'
      
      console.log(
        `${statusIcon} ${schedule.api.padEnd(15)} | ` +
        `Última: ${lastUpdate.padEnd(10)} | ` +
        `Próxima: ${nextUpdate.padEnd(10)} | ` +
        `Quota: ${schedule.quotaUsed}/${schedule.quotaLimit}`
      )
    })
    console.log('=====================================\n')
  }
}

// Singleton
export const sportsSyncService = new SportsSyncService()

// Comandos para debug no console
if (typeof window !== 'undefined') {
  // Certificar que não está sendo chamado como função
  if (typeof sportsSyncService === 'object' && sportsSyncService !== null) {
    (window as any).sportsSyncService = sportsSyncService
    (window as any).syncStatus = () => sportsSyncService.logStatus()
    (window as any).syncStart = () => sportsSyncService.start()
    (window as any).syncStop = () => sportsSyncService.stop()
    (window as any).syncForce = () => sportsSyncService.forceUpdateAll()
    
    console.log('🎮 Comandos disponíveis:')
    console.log('  • syncStatus() - Ver status do serviço')
    console.log('  • syncStart() - Iniciar sincronização')
    console.log('  • syncStop() - Parar sincronização')
    console.log('  • syncForce() - Forçar atualização de todas as APIs')
  }
}