import { sportsCacheService } from '@/services/cache/sports-cache.service'
import { allSportDBClient, footballClient } from '@/services/api/sports'
import { clientWrapper } from '@/services/api/sports/client-wrapper'

interface UpdateSchedule {
  interval: number // em minutos
  lastUpdate: Date | null
  isRunning: boolean
}

class AutoUpdateService {
  private schedules: Map<string, UpdateSchedule> = new Map()
  private timers: Map<string, NodeJS.Timeout> = new Map()
  
  // Configuração de intervalos (em minutos)
  private readonly UPDATE_INTERVALS = {
    liveFixtures: 15,      // Jogos ao vivo - 15 minutos (API Sports - 100 req/dia por API)
    upcomingEvents: 15,    // Próximos eventos - 15 minutos (API Sports)
    todayEvents: 30,       // Eventos de hoje - 30 minutos (AllSportDB complementar)
    allSportLive: 5,       // Eventos ao vivo - 5 minutos (AllSportDB prioritário)
    standings: 60,         // Classificações - 1 hora
    statistics: 1440       // Estatísticas - 24 horas
  }
  
  constructor() {
    // Inicializar schedules
    Object.entries(this.UPDATE_INTERVALS).forEach(([key, interval]) => {
      this.schedules.set(key, {
        interval,
        lastUpdate: null,
        isRunning: false
      })
    })
  }
  
  // Iniciar todas as atualizações automáticas
  startAll(): void {
    console.log('🚀 Iniciando serviço de atualização automática')
    console.log('📊 Configuração de intervalos:')
    console.log('  ⚽ API Sports: 15 minutos (100 req/dia por API)')
    console.log('  🔴 AllSportDB Live: 5 minutos')
    console.log('  📅 AllSportDB Today: 30 minutos')
    console.log('⚠️  Primeira atualização será feita gradualmente')
    
    // NÃO fazer todas as atualizações imediatamente!
    // Agendar primeira execução com delays escalonados
    setTimeout(() => this.updateAllSportLive(), 1000)      // AllSportDB primeiro (1s)
    setTimeout(() => this.updateTodayEvents(), 5000)       // Depois eventos hoje (5s)
    setTimeout(() => this.updateUpcomingEvents(), 10000)   // Depois próximos (10s)
    setTimeout(() => this.updateLiveFixtures(), 15000)     // Por último API Sports (15s)
    
    // Agendar atualizações periódicas
    this.scheduleUpdates()
  }
  
  // Parar todas as atualizações
  stopAll(): void {
    console.log('🛑 Parando serviço de atualização automática')
    
    this.timers.forEach((timer, key) => {
      clearInterval(timer)
      this.timers.delete(key)
    })
    
    this.schedules.forEach((schedule) => {
      schedule.isRunning = false
    })
  }
  
  // Agendar atualizações periódicas
  private scheduleUpdates(): void {
    // Live Fixtures (API Sports) - A cada 15 minutos
    const liveTimer = setInterval(() => {
      this.updateLiveFixtures()
    }, this.UPDATE_INTERVALS.liveFixtures * 60 * 1000)
    this.timers.set('liveFixtures', liveTimer)
    
    // Upcoming Events (AllSportDB) - A cada 1 minuto
    const upcomingTimer = setInterval(() => {
      this.updateUpcomingEvents()
    }, this.UPDATE_INTERVALS.upcomingEvents * 60 * 1000)
    this.timers.set('upcomingEvents', upcomingTimer)
    
    // Today Events (AllSportDB) - A cada 1 minuto
    const todayTimer = setInterval(() => {
      this.updateTodayEvents()
    }, this.UPDATE_INTERVALS.todayEvents * 60 * 1000)
    this.timers.set('todayEvents', todayTimer)
    
    // AllSport Live Events - A cada 1 minuto
    const allSportLiveTimer = setInterval(() => {
      this.updateAllSportLive()
    }, this.UPDATE_INTERVALS.allSportLive * 60 * 1000)
    this.timers.set('allSportLive', allSportLiveTimer)
  }
  
  // Atualizar fixtures ao vivo
  private async updateLiveFixtures(): Promise<void> {
    const schedule = this.schedules.get('liveFixtures')
    if (!schedule || schedule.isRunning) return
    
    schedule.isRunning = true
    console.log('📺 Atualizando fixtures ao vivo...')
    
    try {
      await sportsCacheService.forceUpdate('liveFixtures', async () => {
        // Se estiver no cliente, usar o wrapper
        if (typeof window !== 'undefined') {
          const fixtures = await clientWrapper.getLiveFixtures()
          console.log(`✅ ${fixtures.length} fixtures ao vivo encontrados`)
          return fixtures
        }
        
        // Se estiver no servidor, usar diretamente
        try {
          const fixtures = await footballClient.getLiveFixtures()
          console.log(`✅ ${fixtures.length} fixtures ao vivo encontrados (API-Football)`)
          return fixtures
        } catch (error) {
          console.log('⚠️ API-Football falhou, tentando AllSportDB...')
          
          // Fallback para AllSportDB
          const events = await allSportDBClient.getTodayEvents()
          const now = new Date()
          
          // Filtrar apenas eventos que estão acontecendo agora
          const liveEvents = events.filter(event => {
            const start = new Date(event.dateFrom)
            const end = new Date(event.dateTo)
            return now >= start && now <= end
          })
          
          console.log(`✅ ${liveEvents.length} eventos ao vivo encontrados (AllSportDB)`)
          
          // Converter para formato Fixture
          return liveEvents.map(e => ({
            fixture: {
              id: e.id,
              date: e.dateFrom,
              timestamp: new Date(e.dateFrom).getTime() / 1000,
              venue: { name: e.location },
              status: { long: 'Em andamento', short: 'LIVE', elapsed: null }
            },
            teams: {
              home: { name: e.name.split(' vs ')[0] || e.name },
              away: { name: e.name.split(' vs ')[1] || '' }
            },
            goals: { home: null, away: null },
            league: {
              name: e.competition,
              country: e.country
            }
          } as any))
        }
      })
      
      schedule.lastUpdate = new Date()
    } catch (error) {
      console.error('❌ Erro ao atualizar fixtures ao vivo:', error)
    } finally {
      schedule.isRunning = false
    }
  }
  
  // Atualizar próximos eventos
  private async updateUpcomingEvents(): Promise<void> {
    const schedule = this.schedules.get('upcomingEvents')
    if (!schedule || schedule.isRunning) return
    
    schedule.isRunning = true
    console.log('📅 Atualizando próximos eventos...')
    
    try {
      await sportsCacheService.forceUpdate('upcomingEvents', async () => {
        // Se estiver no cliente, usar o wrapper
        if (typeof window !== 'undefined') {
          const events = await clientWrapper.getUpcomingEvents(7)
          console.log(`✅ ${events.length} eventos próximos encontrados`)
          return events
        }
        
        // Se estiver no servidor, usar diretamente
        const events = await allSportDBClient.getUpcomingEvents(7) // Próximos 7 dias
        console.log(`✅ ${events.length} eventos próximos encontrados`)
        return events
      })
      
      schedule.lastUpdate = new Date()
    } catch (error) {
      console.error('❌ Erro ao atualizar próximos eventos:', error)
    } finally {
      schedule.isRunning = false
    }
  }
  
  // Atualizar eventos de hoje
  private async updateTodayEvents(): Promise<void> {
    const schedule = this.schedules.get('todayEvents')
    if (!schedule || schedule.isRunning) return
    
    schedule.isRunning = true
    console.log('📆 Atualizando eventos de hoje...')
    
    try {
      await sportsCacheService.forceUpdate('todayEvents', async () => {
        // Se estiver no cliente, usar o wrapper
        if (typeof window !== 'undefined') {
          const events = await clientWrapper.getTodayEvents()
          console.log(`✅ ${events.length} eventos hoje encontrados`)
          return events
        }
        
        // Se estiver no servidor, usar diretamente
        const events = await allSportDBClient.getTodayEvents()
        console.log(`✅ ${events.length} eventos hoje encontrados`)
        return events
      })
      
      schedule.lastUpdate = new Date()
    } catch (error) {
      console.error('❌ Erro ao atualizar eventos de hoje:', error)
    } finally {
      schedule.isRunning = false
    }
  }
  
  // Atualizar eventos ao vivo do AllSportDB
  private async updateAllSportLive(): Promise<void> {
    const schedule = this.schedules.get('allSportLive')
    if (!schedule || schedule.isRunning) return
    
    schedule.isRunning = true
    console.log('🔴 Atualizando eventos ao vivo AllSportDB...')
    
    try {
      await sportsCacheService.forceUpdate('allSportLive', async () => {
        // Se estiver no cliente, usar o wrapper
        if (typeof window !== 'undefined') {
          const liveEvents = await clientWrapper.getLiveEvents()
          console.log(`✅ ${liveEvents.length} eventos ao vivo encontrados`)
          return liveEvents
        }
        
        // Se estiver no servidor, usar diretamente
        const events = await allSportDBClient.getTodayEvents()
        const now = new Date()
        
        // Filtrar apenas eventos que estão acontecendo agora
        const liveEvents = events.filter(event => {
          const start = new Date(event.dateFrom)
          const end = new Date(event.dateTo || new Date(start.getTime() + 3 * 60 * 60 * 1000)) // 3h padrão
          return now >= start && now <= end
        })
        
        console.log(`✅ ${liveEvents.length} eventos ao vivo encontrados (AllSportDB)`)
        return liveEvents
      })
      
      schedule.lastUpdate = new Date()
    } catch (error) {
      console.error('❌ Erro ao atualizar eventos ao vivo AllSportDB:', error)
    } finally {
      schedule.isRunning = false
    }
  }
  
  // Obter status do serviço
  getStatus(): {
    isActive: boolean
    schedules: Array<{
      name: string
      interval: number
      lastUpdate: Date | null
      isRunning: boolean
      nextUpdate: Date | null
    }>
  } {
    const scheduleStatus = Array.from(this.schedules.entries()).map(([name, schedule]) => {
      let nextUpdate = null
      
      if (schedule.lastUpdate) {
        nextUpdate = new Date(schedule.lastUpdate.getTime() + schedule.interval * 60 * 1000)
      }
      
      return {
        name,
        ...schedule,
        nextUpdate
      }
    })
    
    return {
      isActive: this.timers.size > 0,
      schedules: scheduleStatus
    }
  }
  
  // Forçar atualização manual
  async forceUpdateAll(): Promise<void> {
    console.log('🔄 Forçando atualização de todos os dados (SEQUENCIALMENTE)...')
    
    // Executar sequencialmente para respeitar rate limits
    try {
      console.log('1/4 - Atualizando eventos ao vivo AllSportDB...')
      await this.updateAllSportLive()
      
      console.log('2/4 - Atualizando eventos de hoje...')
      await this.updateTodayEvents()
      
      console.log('3/4 - Atualizando próximos eventos...')
      await this.updateUpcomingEvents()
      
      console.log('4/4 - Atualizando fixtures ao vivo API Sports...')
      await this.updateLiveFixtures()
      
      console.log('✅ Atualização forçada concluída')
    } catch (error) {
      console.error('❌ Erro durante atualização forçada:', error)
    }
  }
}

// Singleton instance
export const autoUpdateService = new AutoUpdateService()

// NÃO iniciar automaticamente - deixar o AutoUpdateProvider controlar
// Isso evita múltiplas inicializações e requisições desnecessárias
if (typeof window !== 'undefined') {
  // Apenas configurar o listener para parar ao sair
  window.addEventListener('beforeunload', () => {
    autoUpdateService.stopAll()
  })
}

// Exportar para debug no console
if (typeof window !== 'undefined') {
  (window as any).autoUpdateService = autoUpdateService
}