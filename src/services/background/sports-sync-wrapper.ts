/**
 * Wrapper seguro para o serviço de sincronização
 * Evita erros de importação e inicialização
 */

class SportsSyncWrapper {
  private service: any = null
  private isInitialized = false
  
  async initialize() {
    if (this.isInitialized) return
    
    try {
      // Importar dinamicamente apenas quando necessário
      const module = await import('./sports-sync.service')
      if (module && module.sportsSyncService) {
        this.service = module.sportsSyncService
        this.isInitialized = true
        console.log('✅ Serviço de sincronização inicializado')
      }
    } catch (error) {
      console.error('❌ Erro ao inicializar serviço de sincronização:', error)
    }
  }
  
  async start() {
    await this.initialize()
    if (this.service && typeof this.service.start === 'function') {
      return this.service.start()
    }
    console.warn('⚠️ Serviço de sincronização não disponível')
  }
  
  async stop() {
    if (this.service && typeof this.service.stop === 'function') {
      return this.service.stop()
    }
  }
  
  async getStatus() {
    await this.initialize()
    if (this.service && typeof this.service.getStatus === 'function') {
      return this.service.getStatus()
    }
    // Retornar status padrão se não inicializado
    return {
      isRunning: false,
      schedules: [],
      totalRequests: 0,
      apiSportsUsage: 0,
      apiSportsRemaining: 0,
      nextReset: 'N/A'
    }
  }
  
  async forceUpdateAll() {
    await this.initialize()
    if (this.service && typeof this.service.forceUpdateAll === 'function') {
      return this.service.forceUpdateAll()
    }
    console.warn('⚠️ Serviço de sincronização não disponível')
  }
  
  async logStatus() {
    await this.initialize()
    if (this.service && typeof this.service.logStatus === 'function') {
      return this.service.logStatus()
    }
    console.warn('⚠️ Serviço de sincronização não disponível')
  }
}

// Exportar uma instância única
export const syncServiceWrapper = new SportsSyncWrapper()

// Adicionar comandos globais apenas no cliente
if (typeof window !== 'undefined') {
  (window as any).syncWrapper = syncServiceWrapper
  (window as any).syncStart = () => syncServiceWrapper.start()
  (window as any).syncStop = () => syncServiceWrapper.stop()
  (window as any).syncStatus = () => syncServiceWrapper.logStatus()
  (window as any).syncForce = () => syncServiceWrapper.forceUpdateAll()
}