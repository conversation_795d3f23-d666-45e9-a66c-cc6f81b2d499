/**
 * Serviço de Atualização Automática de Dados Esportivos
 * 
 * Este serviço gerencia as atualizações automáticas das APIs respeitando os intervalos:
 * - API Sports: 1 requisição a cada 15 minutos por API (12 APIs)
 * - AllSportsDB: 1 requisição a cada 30 segundos
 */

import { SportsDataService } from '@/services/api/sports/sports-data.service'
import { requestManager } from '@/services/api/sports/request-manager'

class SportsUpdateService {
  private updateIntervals: Map<string, NodeJS.Timeout> = new Map()
  private isRunning = false
  
  /**
   * Inicia o serviço de atualização automática
   */
  start() {
    if (this.isRunning) {
      console.log('⚠️ Serviço de atualização já está em execução')
      return
    }
    
    this.isRunning = true
    console.log('🚀 Iniciando serviço de atualização automática de dados esportivos...')
    
    // Agendar primeira execução
    this.scheduleUpdates()
    
    // Agendar atualizações periódicas
    this.setupPeriodicUpdates()
  }
  
  /**
   * Para o serviço de atualização
   */
  stop() {
    console.log('🛑 Parando serviço de atualização...')
    this.isRunning = false
    
    // Limpar todos os intervalos
    this.updateIntervals.forEach((interval) => {
      clearInterval(interval)
    })
    this.updateIntervals.clear()
  }
  
  /**
   * Agenda a primeira execução respeitando os intervalos
   */
  private async scheduleUpdates() {
    console.log('📅 Agendando atualizações...')
    
    // 1. AllSportsDB - Eventos ao vivo (a cada 30 segundos)
    this.updateAllSportsDB()
    
    // 2. API Sports - Uma por vez a cada 15 minutos
    this.updateAPISports()
  }
  
  /**
   * Configura as atualizações periódicas
   */
  private setupPeriodicUpdates() {
    // AllSportsDB - A cada 30 segundos
    const allSportsInterval = setInterval(() => {
      if (this.isRunning) {
        this.updateAllSportsDB()
      }
    }, 30 * 1000) // 30 segundos
    
    this.updateIntervals.set('allsportdb', allSportsInterval)
    
    // API Sports - A cada 15 minutos
    const apiSportsInterval = setInterval(() => {
      if (this.isRunning) {
        this.updateAPISports()
      }
    }, 15 * 60 * 1000) // 15 minutos
    
    this.updateIntervals.set('api-sports', apiSportsInterval)
  }
  
  /**
   * Atualiza dados do AllSportsDB
   */
  private async updateAllSportsDB() {
    try {
      console.log('🔄 Atualizando AllSportsDB...')
      
      // Verificar se pode fazer requisição
      if (!requestManager.canMakeRequest('allsportdb')) {
        const nextTime = requestManager.getNextAvailableTime('allsportdb')
        console.log(`⏳ AllSportsDB: Aguardando até ${nextTime?.toLocaleTimeString()}`)
        return
      }
      
      // Buscar eventos ao vivo
      const events = await SportsDataService.getAllLiveResults()
      console.log(`✅ AllSportsDB: ${events.length} eventos ao vivo encontrados`)
      
    } catch (error) {
      console.error('❌ Erro ao atualizar AllSportsDB:', error)
    }
  }
  
  /**
   * Atualiza dados da API Sports (uma API por vez)
   */
  private async updateAPISports() {
    const apis = [
      'api-football',
      'api-basketball',
      'api-baseball',
      'api-hockey',
      'api-volleyball',
      'api-handball',
      'api-rugby',
      'api-formula1',
      'api-mma',
      'api-nfl',
      'api-nba',
      'api-afl'
    ]
    
    // Encontrar próxima API disponível
    for (const api of apis) {
      if (requestManager.canMakeRequest(api)) {
        try {
          console.log(`🔄 Atualizando ${api}...`)
          
          // Fazer requisição através do SportsDataService
          await SportsDataService.getAllLiveResults()
          
          console.log(`✅ ${api} atualizado com sucesso`)
          break // Atualizar apenas uma API por vez
          
        } catch (error) {
          console.error(`❌ Erro ao atualizar ${api}:`, error)
        }
      }
    }
    
    // Mostrar próximos horários disponíveis
    this.logNextAvailableTimes()
  }
  
  /**
   * Exibe os próximos horários disponíveis para cada API
   */
  private logNextAvailableTimes() {
    const stats = requestManager.getStats()
    console.log('\n📊 Status das APIs:')
    
    stats.quotas.forEach(quota => {
      if (quota.apiName.startsWith('api-')) {
        const nextTime = requestManager.getNextAvailableTime(quota.apiName)
        console.log(
          `${quota.apiName}: ${quota.usedToday}/${quota.dailyLimit} requisições | ` +
          `Próxima: ${nextTime?.toLocaleTimeString() || 'Agora'}`
        )
      }
    })
    
    console.log(`\n📈 Total de requisições hoje: ${stats.totalRequestsToday}`)
    console.log(`⏰ Reset das quotas em: ${stats.nextResetIn}\n`)
  }
  
  /**
   * Força atualização manual de uma API específica
   */
  async forceUpdate(api?: string) {
    if (api) {
      console.log(`🔄 Forçando atualização de ${api}...`)
      
      if (!requestManager.canMakeRequest(api)) {
        const nextTime = requestManager.getNextAvailableTime(api)
        console.warn(`⚠️ ${api} não pode ser atualizada agora. Próxima: ${nextTime?.toLocaleTimeString()}`)
        return
      }
      
      // Fazer requisição específica
      await SportsDataService.getAllLiveResults()
      
    } else {
      // Atualizar tudo que estiver disponível
      await this.updateAllSportsDB()
      await this.updateAPISports()
    }
  }
  
  /**
   * Retorna o status atual do serviço
   */
  getStatus() {
    return {
      isRunning: this.isRunning,
      stats: requestManager.getStats(),
      nextSchedule: requestManager.getOptimalSchedule()
    }
  }
}

// Singleton
export const sportsUpdateService = new SportsUpdateService()

// Auto-iniciar em produção
if (process.env.NODE_ENV === 'production') {
  sportsUpdateService.start()
}