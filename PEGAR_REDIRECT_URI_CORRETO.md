# 🎯 PEGAR O REDIRECT URI EXATO DO STACK AUTH

## 📋 PASSO A PASSO:

### 1️⃣ **NO STACK AUTH**:

1. Entre em: https://app.stack-auth.com
2. Vá em **Authentication → OAuth Providers**
3. Clique em **Google** (onde você colocou as chaves)
4. **PROCURE POR ESTE TEXTO**:
   ```
   Redirect URI
   Copy this exact URL and add it to your OAuth application
   ```

5. **COPIE O URI COMPLETO** que aparece na caixa
   - Será algo como:
   ```
   https://api.stack-auth.com/api/v1/auth/oauth/callback/google?project_id=b5d6eebd-aa16-4101-9cad-8717743c9343&publishable_client_key=pck_m988zs8n54pjh8rjgdv70b9crgspcpy3vq0zwwgy72pbr&after_callback_redirect_to=https%3A%2F%2Fnewspports.com%2Fhandler%2Foauth-callback
   ```

### 2️⃣ **NO GOOGLE CONSOLE**:

1. Vá em: https://console.cloud.google.com
2. **APIs & Services → Credentials**
3. Clique no seu OAuth Client
4. Em **URIs de redirecionamento autorizados**:
   - **REMOVA** todos os URIs antigos relacionados ao Stack Auth
   - **ADICIONE** apenas o URI que você copiou do Stack Auth
   - Mantenha também:
     ```
     https://newspports.com/handler/oauth-callback
     http://localhost:3000/handler/oauth-callback
     ```

### 3️⃣ **IMPORTANTE - O URI MUDA!**

⚠️ O Redirect URI é **ÚNICO** para cada projeto e configuração. Ele inclui:
- Seu project_id
- Sua publishable_key
- Seu domínio codificado

**NÃO USE** exemplos genéricos! Use o URI **EXATO** que o Stack Auth mostra!

## 🔍 EXEMPLO DO QUE PROCURAR NO STACK AUTH:

```
┌─────────────────────────────────────────────────┐
│ Google OAuth Configuration                       │
├─────────────────────────────────────────────────┤
│                                                 │
│ Client ID: 585790794492-3dq...                 │
│ Client Secret: ••••••••                        │
│                                                 │
│ ⚠️ Redirect URI                                │
│ ┌─────────────────────────────────────────────┐│
│ │https://api.stack-auth.com/api/v1/auth/...  ││
│ │[COPY BUTTON]                                ││
│ └─────────────────────────────────────────────┘│
│                                                 │
│ ℹ️ Copy this exact URL and add it to Google   │
└─────────────────────────────────────────────────┘
```

## ✅ CHECKLIST:

1. [ ] Copiou o URI EXATO do Stack Auth (não um exemplo)
2. [ ] Colou no Google Console EXATAMENTE como está
3. [ ] Salvou no Google Console
4. [ ] Aguardou 5 minutos

## 🆘 SE NÃO ENCONTRAR:

Me envie um screenshot da tela do Google OAuth no Stack Auth (pode esconder o client secret) que eu te mostro onde está!

## 💡 DICA IMPORTANTE:

O URI correto sempre tem estes parâmetros:
- `project_id=` (seu ID do projeto)
- `publishable_client_key=` (sua chave)
- `after_callback_redirect_to=` (seu domínio codificado)

Se faltar algum desses, está errado!