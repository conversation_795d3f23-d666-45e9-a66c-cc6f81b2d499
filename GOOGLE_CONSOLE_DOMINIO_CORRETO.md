# 🎯 CONFIGURAÇÃO CORRETA COM SEU DOMÍNIO

## 🌐 SEU DOMÍNIO: newspports.com

### 1️⃣ **Origens JavaScript autorizadas**

Adicione TODAS estas URLs:

```
https://newspports.com
https://www.newspports.com
https://api.stack-auth.com
http://localhost:3000
```

### 2️⃣ **URIs de redirecionamento autorizados**

Adicione TODAS estas URLs:

```
https://api.stack-auth.com/api/v1/auth/oauth/callback/google
https://newspports.com/handler/oauth-callback
https://www.newspports.com/handler/oauth-callback
http://localhost:3000/handler/oauth-callback
```

## ⚠️ IMPORTANTE - ATUALIZAR NO RAILWAY TAMBÉM:

### Você precisa atualizar a variável no Railway:

```bash
NEXT_PUBLIC_APP_URL=https://newspports.com
```

(ou https://www.newspports.com se preferir usar www)

## 📋 CHECKLIST COMPLETO:

### No Google Console:
- [ ] Adicionar origens JavaScript (4 URLs)
- [ ] Adicionar URIs de redirecionamento (4 URLs)
- [ ] Salvar alterações

### No Railway:
- [ ] Atualizar NEXT_PUBLIC_APP_URL para https://newspports.com
- [ ] Fazer novo deploy

### No Stack Auth:
- [ ] Verificar se as URLs estão atualizadas
- [ ] Em Settings > URLs, atualizar App URL para https://newspports.com

## 🔄 RESUMO DAS MUDANÇAS:

### ANTES (errado):
```
https://newspports-production.up.railway.app
```

### DEPOIS (correto):
```
https://newspports.com
https://www.newspports.com
```

## 💡 DICA:

Se você tem certificado SSL para ambos (com e sem www), adicione os dois. Se não, use apenas o que funciona (geralmente sem www).

## 🎊 APÓS CONFIGURAR:

1. Salve no Google Console
2. Atualize no Railway
3. Aguarde 5 minutos
4. Teste o login em https://newspports.com

Agora sim está com o domínio correto! 🚀