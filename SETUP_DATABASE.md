# Configuração do Banco de Dados - StreamPlus España

## Instruções para criar as tabelas no Supabase

1. Acesse o dashboard do Supabase em: https://supabase.com/dashboard/project/bgeecdlhnwkgdujoyont

2. Vá para o SQL Editor no menu lateral

3. Execute cada um dos seguintes blocos SQL em ordem:

### 1. Habilitar Extensões
```sql
-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";
```

### 2. Criar Tipos Customizados
```sql
-- <PERSON>reate custom types
DO $$ BEGIN
    CREATE TYPE channel_category AS ENUM ('sports', 'esports', 'entertainment', 'news', 'kids');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE subscription_tier AS ENUM ('free', 'basic', 'premium', 'vip');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE content_rating AS ENUM ('G', 'PG', 'PG-13', 'R', 'NC-17');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;
```

### 3. Criar Tabelas
```sql
-- Create profiles table (extends auth.users)
CREATE TABLE IF NOT EXISTS profiles (
    id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    username TEXT UNIQUE,
    full_name TEXT,
    avatar_url TEXT,
    subscription_tier subscription_tier DEFAULT 'free',
    subscription_expires_at TIMESTAMPTZ,
    trial_used BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create channels table
CREATE TABLE IF NOT EXISTS channels (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL,
    slug TEXT UNIQUE NOT NULL,
    description TEXT,
    logo_url TEXT,
    banner_url TEXT,
    stream_url TEXT NOT NULL,
    fallback_urls TEXT[] DEFAULT '{}',
    category channel_category NOT NULL,
    is_premium BOOLEAN DEFAULT FALSE,
    required_tier subscription_tier DEFAULT 'free',
    is_active BOOLEAN DEFAULT TRUE,
    viewer_count INTEGER DEFAULT 0,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create viewing_sessions table
CREATE TABLE IF NOT EXISTS viewing_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
    channel_id UUID REFERENCES channels(id) ON DELETE CASCADE,
    started_at TIMESTAMPTZ DEFAULT NOW(),
    ended_at TIMESTAMPTZ,
    duration_seconds INTEGER,
    quality TEXT,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create app_verification_tokens table
CREATE TABLE IF NOT EXISTS app_verification_tokens (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
    token TEXT UNIQUE NOT NULL,
    used BOOLEAN DEFAULT FALSE,
    used_at TIMESTAMPTZ,
    expires_at TIMESTAMPTZ NOT NULL,
    platform TEXT CHECK (platform IN ('ios', 'android')),
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create favorites table
CREATE TABLE IF NOT EXISTS favorites (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
    channel_id UUID REFERENCES channels(id) ON DELETE CASCADE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(user_id, channel_id)
);

-- Create channel_schedule table
CREATE TABLE IF NOT EXISTS channel_schedule (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    channel_id UUID REFERENCES channels(id) ON DELETE CASCADE,
    title TEXT NOT NULL,
    description TEXT,
    starts_at TIMESTAMPTZ NOT NULL,
    ends_at TIMESTAMPTZ NOT NULL,
    is_live BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

### 4. Criar Índices
```sql
-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_channels_category ON channels(category);
CREATE INDEX IF NOT EXISTS idx_channels_is_active ON channels(is_active);
CREATE INDEX IF NOT EXISTS idx_channels_slug ON channels(slug);
CREATE INDEX IF NOT EXISTS idx_viewing_sessions_user_id ON viewing_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_viewing_sessions_channel_id ON viewing_sessions(channel_id);
CREATE INDEX IF NOT EXISTS idx_viewing_sessions_started_at ON viewing_sessions(started_at);
CREATE INDEX IF NOT EXISTS idx_app_verification_tokens_token ON app_verification_tokens(token);
CREATE INDEX IF NOT EXISTS idx_app_verification_tokens_expires_at ON app_verification_tokens(expires_at);
CREATE INDEX IF NOT EXISTS idx_favorites_user_id ON favorites(user_id);
CREATE INDEX IF NOT EXISTS idx_channel_schedule_channel_id ON channel_schedule(channel_id);
CREATE INDEX IF NOT EXISTS idx_channel_schedule_starts_at ON channel_schedule(starts_at);
```

### 5. Criar Função e Triggers
```sql
-- Create update timestamp function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
DROP TRIGGER IF EXISTS update_profiles_updated_at ON profiles;
CREATE TRIGGER update_profiles_updated_at BEFORE UPDATE ON profiles
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_channels_updated_at ON channels;
CREATE TRIGGER update_channels_updated_at BEFORE UPDATE ON channels
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_channel_schedule_updated_at ON channel_schedule;
CREATE TRIGGER update_channel_schedule_updated_at BEFORE UPDATE ON channel_schedule
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

### 6. Inserir Canais Demo
```sql
-- Insert demo channels (usando stream de teste público)
INSERT INTO channels (name, slug, description, logo_url, stream_url, category, is_premium, required_tier) VALUES
    ('LaLiga TV', 'laliga-tv', 'Todos los partidos de LaLiga en directo', '/logos/laliga.png', 'https://test-streams.mux.dev/x36xhzz/x36xhzz.m3u8', 'sports', true, 'premium'),
    ('Champions League', 'champions-league', 'Los mejores partidos de Europa', '/logos/champions.png', 'https://test-streams.mux.dev/x36xhzz/x36xhzz.m3u8', 'sports', true, 'premium'),
    ('Formula 1', 'formula-1', 'Todas las carreras de F1 en directo', '/logos/f1.png', 'https://test-streams.mux.dev/x36xhzz/x36xhzz.m3u8', 'sports', true, 'vip'),
    ('ESPN Deportes', 'espn-deportes', 'Lo mejor del deporte internacional', '/logos/espn.png', 'https://test-streams.mux.dev/x36xhzz/x36xhzz.m3u8', 'sports', false, 'basic'),
    ('eSports Arena', 'esports-arena', 'Torneos de League of Legends, CS:GO y más', '/logos/esports.png', 'https://test-streams.mux.dev/x36xhzz/x36xhzz.m3u8', 'esports', false, 'free')
ON CONFLICT (slug) DO NOTHING;
```

### 7. Habilitar RLS (Row Level Security)
```sql
-- Enable RLS on all tables
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE channels ENABLE ROW LEVEL SECURITY;
ALTER TABLE viewing_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE app_verification_tokens ENABLE ROW LEVEL SECURITY;
ALTER TABLE favorites ENABLE ROW LEVEL SECURITY;
ALTER TABLE channel_schedule ENABLE ROW LEVEL SECURITY;
```

### 8. Criar Políticas RLS
```sql
-- Profiles policies
CREATE POLICY "Public profiles are viewable by everyone" ON profiles
    FOR SELECT USING (true);

CREATE POLICY "Users can update own profile" ON profiles
    FOR UPDATE USING ((SELECT auth.uid()) = id);

CREATE POLICY "Users can insert own profile" ON profiles
    FOR INSERT WITH CHECK ((SELECT auth.uid()) = id);

-- Channels policies
CREATE POLICY "Channels are viewable by everyone" ON channels
    FOR SELECT USING (is_active = true);

-- Viewing sessions policies
CREATE POLICY "Users can view own sessions" ON viewing_sessions
    FOR SELECT USING ((SELECT auth.uid()) = user_id);

CREATE POLICY "Users can create own sessions" ON viewing_sessions
    FOR INSERT WITH CHECK ((SELECT auth.uid()) = user_id);

CREATE POLICY "Users can update own sessions" ON viewing_sessions
    FOR UPDATE USING ((SELECT auth.uid()) = user_id);

-- App verification tokens policies
CREATE POLICY "Users can view own tokens" ON app_verification_tokens
    FOR SELECT USING ((SELECT auth.uid()) = user_id);

CREATE POLICY "Users can create own tokens" ON app_verification_tokens
    FOR INSERT WITH CHECK ((SELECT auth.uid()) = user_id);

-- Favorites policies
CREATE POLICY "Users can view own favorites" ON favorites
    FOR SELECT USING ((SELECT auth.uid()) = user_id);

CREATE POLICY "Users can create own favorites" ON favorites
    FOR INSERT WITH CHECK ((SELECT auth.uid()) = user_id);

CREATE POLICY "Users can delete own favorites" ON favorites
    FOR DELETE USING ((SELECT auth.uid()) = user_id);

-- Channel schedule policies
CREATE POLICY "Schedule is viewable by everyone" ON channel_schedule
    FOR SELECT USING (true);
```

### 9. Criar Função para Novos Usuários
```sql
-- Create function to handle new user creation
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.profiles (id, full_name, avatar_url)
    VALUES (
        NEW.id,
        NEW.raw_user_meta_data->>'full_name',
        NEW.raw_user_meta_data->>'avatar_url'
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for new user creation
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION handle_new_user();
```

## Verificação

Após executar todos os comandos, você pode verificar se tudo foi criado corretamente com:

```sql
-- Verificar tabelas
SELECT table_name FROM information_schema.tables 
WHERE table_schema = 'public' 
ORDER BY table_name;

-- Verificar canais
SELECT * FROM channels;

-- Verificar políticas RLS
SELECT schemaname, tablename, policyname FROM pg_policies 
WHERE schemaname = 'public';
```

## Próximos Passos

1. Configure a autenticação no dashboard do Supabase
2. Habilite os provedores OAuth desejados
3. Configure as URLs de redirecionamento
4. Teste a criação de usuários e perfis