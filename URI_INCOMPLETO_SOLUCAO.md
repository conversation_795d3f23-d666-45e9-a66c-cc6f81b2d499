# 🚨 PROBLEMA: URI DO STACK AUTH INCOMPLETO

## ❌ O QUE ESTÁ ERRADO:

Você tem:
```
https://api.stack-auth.com/api/v1/auth/oauth/callback/google
```

Ma<PERSON> de<PERSON>ia ter algo como:
```
https://api.stack-auth.com/api/v1/auth/oauth/callback/google?project_id=b5d6eebd-aa16-4101-9cad-8717743c9343&publishable_client_key=pck_m988zs8n54pjh8rjgdv70b9crgspcpy3vq0zwwgy72pbr
```

**FALTAM OS PARÂMETROS!**

## 🔍 ONDE PEGAR O URI COMPLETO:

### NO STACK AUTH:

1. Vá em **Authentication → OAuth Providers**
2. Clique em **Google**
3. **IMPORTANTE**: Depo<PERSON> de preencher Client ID e Secret, o Stack Auth gera um URI COMPLETO
4. Procure por uma seção que diz:
   - "Redirect URI" ou
   - "Callback URL" ou  
   - "OAuth Redirect URL"

5. O URI CORRETO terá SEMPRE estes parâmetros:
   - `?project_id=...`
   - `&publishable_client_key=...`

## 🛠️ SOLUÇÃO ALTERNATIVA:

Se não encontrar, vamos construir manualmente:

### 1. Pegue seu Project ID e Publishable Key:

No Stack Auth Dashboard → Settings → API Keys:
- Project ID: `b5d6eebd-aa16-4101-9cad-8717743c9343`
- Publishable Key: `pck_m988zs8n54pjh8rjgdv70b9crgspcpy3vq0zwwgy72pbr`

### 2. Monte o URI completo:

```
https://api.stack-auth.com/api/v1/auth/oauth/callback/google?project_id=b5d6eebd-aa16-4101-9cad-8717743c9343&publishable_client_key=pck_m988zs8n54pjh8rjgdv70b9crgspcpy3vq0zwwgy72pbr
```

### 3. No Google Console:

SUBSTITUA o primeiro URI pelo URI completo acima.

## ✅ COMO DEVE FICAR:

```
URIs de redirecionamento autorizados:

1. https://api.stack-auth.com/api/v1/auth/oauth/callback/google?project_id=b5d6eebd-aa16-4101-9cad-8717743c9343&publishable_client_key=pck_m988zs8n54pjh8rjgdv70b9crgspcpy3vq0zwwgy72pbr

2. https://newspports.com/handler/oauth-callback

3. https://www.newspports.com/handler/oauth-callback

4. http://localhost:3000/handler/oauth-callback
```

## 🎯 AÇÃO IMEDIATA:

1. **No Google Console**, edite o primeiro URI
2. **Substitua** por um que tenha os parâmetros `project_id` e `publishable_client_key`
3. **Salve** as alterações
4. **Aguarde 5 minutos**
5. **Teste novamente**

## 💡 DICA:

No Stack Auth, o URI completo geralmente aparece:
- Após salvar as configurações do Google
- Em uma caixa cinza com botão "Copy"
- Com um aviso "Copy this exact URL..."

Se ainda não conseguir encontrar, me mande um print da tela completa do Google OAuth no Stack Auth!