# Stack Auth - Exemplo de configuração
# Para teste local, você pode usar estas credenciais de demonstração
# IMPORTANTE: Para produção, crie seu próprio projeto em https://app.stack-auth.com

# Stack Auth
NEXT_PUBLIC_STACK_PROJECT_ID=prj_test_streamplus_demo_2024
NEXT_PUBLIC_STACK_PUBLISHABLE_CLIENT_KEY=pk_test_streamplus_publishable_key_2024
STACK_SECRET_SERVER_KEY=sk_test_streamplus_secret_key_2024

# URLs da Aplicação
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_ANDROID_APP_URL=https://play.google.com/store/apps/details?id=com.streamplus.espana
NEXT_PUBLIC_IOS_APP_URL=https://apps.apple.com/app/streamplus-espana/id123456789

# Servidores de Streaming
NEXT_PUBLIC_STREAM_URL=https://test.unified.stream.prod.ott.irdeto.com/playback/f67418e6-6bf5-45e6-9fc0-39fbc0e3b804/master.m3u8
NEXT_PUBLIC_FALLBACK_STREAM_URL=https://demo.unified-streaming.com/k8s/features/stable/video/tears-of-steel/tears-of-steel.ism/.m3u8

# Chave secreta para JWT
JWT_SECRET=streamplus-jwt-secret-key-2024

# APIs de Esportes
API_SPORTS_KEY=eaf42062f90877281cf06a2ad2e1d11e
ALLSPORT_DB_KEY=20d8be22-0948-4131-ae61-37dd981028bd