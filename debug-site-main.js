// Script para debug do site principal
// Execute no console quando estiver em /browse ou /watch/[id]

console.clear();
console.log('🔍 DIAGNÓSTICO DO SITE PRINCIPAL\n');

// 1. Verificar em qual página estamos
const currentPath = window.location.pathname;
console.log('📍 Página atual:', currentPath);

// 2. Se estamos em /browse
if (currentPath === '/browse' || currentPath.includes('/browse')) {
    console.log('\n=== PÁGINA BROWSE ===');
    
    // Verificar se há canais carregados
    const cards = document.querySelectorAll('[class*="Card"]');
    console.log(`📺 Canais encontrados: ${cards.length}`);
    
    if (cards.length > 0) {
        console.log('\n🔍 Primeiros 3 canais:');
        Array.from(cards).slice(0, 3).forEach((card, i) => {
            const name = card.querySelector('h3')?.textContent || 'Sem nome';
            const onClick = card.onclick;
            console.log(`${i+1}. ${name}`);
            console.log(`   - Tem onClick: ${!!onClick}`);
            
            // Verificar ID do canal
            const reactKey = Object.keys(card).find(key => key.startsWith('__react'));
            if (reactKey) {
                const props = card[reactKey]?.memoizedProps || {};
                console.log(`   - Props:`, props);
            }
        });
        
        // Tentar clicar no primeiro canal
        console.log('\n🖱️ Tentando clicar no primeiro canal...');
        const firstCard = cards[0];
        if (firstCard) {
            console.log('Canal:', firstCard.querySelector('h3')?.textContent);
            // Simular clique
            firstCard.click();
            console.log('✅ Clique executado - verifique se navegou');
        }
    } else {
        console.log('❌ Nenhum canal carregado!');
        
        // Verificar loading
        const loading = document.querySelector('[class*="animate-pulse"]');
        if (loading) {
            console.log('⏳ Ainda carregando...');
        }
        
        // Verificar erro
        const errorElement = document.querySelector('[class*="error"]');
        if (errorElement) {
            console.log('❌ Erro encontrado:', errorElement.textContent);
        }
    }
}

// 3. Se estamos em /watch
if (currentPath.includes('/watch/')) {
    console.log('\n=== PÁGINA WATCH ===');
    
    const channelId = currentPath.split('/').pop();
    console.log('📺 ID do canal:', channelId);
    
    // Verificar elementos de vídeo
    const videos = document.querySelectorAll('video');
    console.log(`🎥 Elementos de vídeo: ${videos.length}`);
    
    if (videos.length > 0) {
        videos.forEach((video, i) => {
            console.log(`\nVídeo ${i+1}:`);
            console.log('- src:', video.src || 'VAZIO');
            console.log('- readyState:', video.readyState);
            console.log('- error:', video.error);
            console.log('- paused:', video.paused);
        });
    }
    
    // Verificar ReactPlayer
    const reactPlayer = document.querySelector('[class*="react-player"]');
    if (reactPlayer) {
        console.log('\n✅ ReactPlayer encontrado');
    } else {
        console.log('\n❌ ReactPlayer não encontrado');
    }
    
    // Verificar trial overlay
    const trialOverlay = document.querySelector('[class*="trial"]');
    if (trialOverlay) {
        console.log('\n⏰ Trial overlay presente - tempo expirado?');
    }
    
    // Verificar loading
    const loading = document.querySelector('[class*="Loader"]');
    if (loading) {
        console.log('\n⏳ Carregando...');
    }
}

// 4. Verificar chamadas de API
console.log('\n\n=== VERIFICAR REDE ===');
console.log('1. Abra a aba Network (F12)');
console.log('2. Filtre por: Fetch/XHR');
console.log('3. Procure por:');
console.log('   - /api/iptv-simple');
console.log('   - /api/iptv/channel/');
console.log('   - /api/proxy');
console.log('4. Verifique se há erros 404, 500, etc.');

// 5. Verificar localStorage/sessionStorage
console.log('\n\n=== STORAGE ===');
const trialData = localStorage.getItem('trial-storage');
if (trialData) {
    try {
        const trial = JSON.parse(trialData);
        console.log('⏰ Trial data:', trial.state);
    } catch (e) {
        console.log('❌ Erro ao ler trial data');
    }
}

// 6. Tentar chamar API diretamente
console.log('\n\n=== TESTE DE API ===');
console.log('Testando /api/iptv-simple...');
fetch('/api/iptv-simple')
    .then(r => r.json())
    .then(data => {
        console.log('✅ API respondeu:', {
            success: data.success,
            total: data.total,
            canais: data.data?.length || 0
        });
        if (data.data && data.data.length > 0) {
            console.log('Primeiro canal:', {
                name: data.data[0].name,
                url: data.data[0].url?.substring(0, 50) + '...'
            });
        }
    })
    .catch(err => {
        console.error('❌ Erro na API:', err);
    });

// 7. Sugestões
console.log('\n\n💡 AÇÕES SUGERIDAS:');
console.log('1. Se está em /browse e não carrega:');
console.log('   - Recarregue a página com Ctrl+R');
console.log('   - Execute: location.reload()');
console.log('2. Se clica no canal mas não navega:');
console.log('   - Verifique o console por erros');
console.log('   - Tente navegar manualmente: location.href = "/watch/iptv-30AGolfKingdom.us"');
console.log('3. Se está em /watch mas não carrega vídeo:');
console.log('   - Verifique se o channelId está correto');
console.log('   - Tente a página de teste: location.href = "/hls-test"');