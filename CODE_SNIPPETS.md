# StreamPlus España - Code Snippets e Padrões

Este arquivo contém os snippets de código e padrões para garantir consistência no desenvolvimento.

## Índice

1. [Next.js 15 App Router](#nextjs-15-app-router)
2. [React 19 Hooks](#react-19-hooks)
3. [Supabase](#supabase)
4. [Zustand State Management](#zustand-state-management)
5. [Video Player com HLS](#video-player-com-hls)
6. [Componentes UI](#componentes-ui)
7. [Tailwind CSS 4](#tailwind-css-4)

## Next.js 15 App Router

### Layout Base
```typescript
// app/layout.tsx
import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'
import { ThemeProvider } from '@/components/theme-provider'
import { Toaster } from '@/components/ui/sonner'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: {
    template: '%s | StreamPlus España',
    default: 'StreamPlus España - Streaming Premium',
  },
  description: 'Plataforma premium de streaming de deportes y eSports',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="es" suppressHydrationWarning>
      <body className={inter.className}>
        <ThemeProvider
          attribute="class"
          defaultTheme="dark"
          enableSystem
          disableTransitionOnChange
        >
          {children}
          <Toaster />
        </ThemeProvider>
      </body>
    </html>
  )
}
```

### Server Component com Fetch
```typescript
// app/channels/[id]/page.tsx
import { createClient } from '@/lib/supabase/server'
import { notFound } from 'next/navigation'

export default async function ChannelPage({
  params,
}: {
  params: Promise<{ id: string }>
}) {
  const { id } = await params
  const supabase = await createClient()
  
  const { data: channel, error } = await supabase
    .from('channels')
    .select('*')
    .eq('id', id)
    .single()
    
  if (error || !channel) {
    notFound()
  }
  
  return (
    <div>
      <h1>{channel.name}</h1>
    </div>
  )
}
```

### Route Handler
```typescript
// app/api/generate-token/route.ts
import { NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

export async function POST(request: Request) {
  const supabase = await createClient()
  const { data: { user } } = await supabase.auth.getUser()
  
  if (!user) {
    return NextResponse.json(
      { error: 'Unauthorized' },
      { status: 401 }
    )
  }
  
  // Lógica do token
  
  return NextResponse.json({ token })
}
```

## React 19 Hooks

### Custom Hook para Auth
```typescript
// hooks/use-auth.ts
import { useEffect, useState } from 'react'
import { createClient } from '@/lib/supabase/client'
import { Session, User } from '@supabase/supabase-js'

export function useAuth() {
  const [user, setUser] = useState<User | null>(null)
  const [session, setSession] = useState<Session | null>(null)
  const [loading, setLoading] = useState(true)
  
  useEffect(() => {
    const supabase = createClient()
    
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session)
      setUser(session?.user ?? null)
      setLoading(false)
    })
    
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      (_event, session) => {
        setSession(session)
        setUser(session?.user ?? null)
      }
    )
    
    return () => subscription.unsubscribe()
  }, [])
  
  return { user, session, loading }
}
```

## Supabase

### Client Setup
```typescript
// lib/supabase/client.ts
import { createBrowserClient } from '@supabase/ssr'
import type { Database } from '@/types/supabase'

export function createClient() {
  return createBrowserClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  )
}
```

### Server Setup
```typescript
// lib/supabase/server.ts
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'
import type { Database } from '@/types/supabase'

export async function createClient() {
  const cookieStore = await cookies()
  
  return createServerClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return cookieStore.getAll()
        },
        setAll(cookiesToSet) {
          cookiesToSet.forEach(({ name, value, options }) =>
            cookieStore.set(name, value, options)
          )
        },
      },
    }
  )
}
```

### Realtime Subscription
```typescript
// components/realtime-viewer-count.tsx
'use client'

import { useEffect, useState } from 'react'
import { createClient } from '@/lib/supabase/client'

export function RealtimeViewerCount({ channelId }: { channelId: string }) {
  const [viewers, setViewers] = useState(0)
  
  useEffect(() => {
    const supabase = createClient()
    
    const channel = supabase
      .channel(`viewers:${channelId}`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'viewing_sessions',
          filter: `channel_id=eq.${channelId}`,
        },
        (payload) => {
          // Atualizar contagem
        }
      )
      .subscribe()
      
    return () => {
      channel.unsubscribe()
    }
  }, [channelId])
  
  return <span>{viewers} espectadores</span>
}
```

## Zustand State Management

### Trial Store
```typescript
// stores/trial.store.ts
import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { immer } from 'zustand/middleware/immer'

interface TrialState {
  startTime: number | null
  timeRemaining: number
  isExpired: boolean
  hasSeenOffer: boolean
}

interface TrialActions {
  startTrial: () => void
  updateTimeRemaining: (seconds: number) => void
  expireTrial: () => void
  markOfferSeen: () => void
  reset: () => void
}

export const useTrialStore = create<TrialState & TrialActions>()(
  persist(
    immer((set) => ({
      startTime: null,
      timeRemaining: 300, // 5 minutos
      isExpired: false,
      hasSeenOffer: false,
      
      startTrial: () =>
        set((state) => {
          state.startTime = Date.now()
          state.timeRemaining = 300
          state.isExpired = false
        }),
        
      updateTimeRemaining: (seconds) =>
        set((state) => {
          state.timeRemaining = Math.max(0, seconds)
          if (state.timeRemaining === 0) {
            state.isExpired = true
          }
        }),
        
      expireTrial: () =>
        set((state) => {
          state.isExpired = true
          state.timeRemaining = 0
        }),
        
      markOfferSeen: () =>
        set((state) => {
          state.hasSeenOffer = true
        }),
        
      reset: () =>
        set((state) => {
          state.startTime = null
          state.timeRemaining = 300
          state.isExpired = false
          state.hasSeenOffer = false
        }),
    })),
    {
      name: 'trial-storage',
    }
  )
)
```

### Player Store
```typescript
// stores/player.store.ts
import { create } from 'zustand'
import { immer } from 'zustand/middleware/immer'

interface PlayerState {
  isPlaying: boolean
  volume: number
  muted: boolean
  quality: 'auto' | number
  currentTime: number
  duration: number
  buffering: boolean
}

interface PlayerActions {
  setPlaying: (playing: boolean) => void
  setVolume: (volume: number) => void
  setMuted: (muted: boolean) => void
  setQuality: (quality: 'auto' | number) => void
  setCurrentTime: (time: number) => void
  setDuration: (duration: number) => void
  setBuffering: (buffering: boolean) => void
}

export const usePlayerStore = create<PlayerState & PlayerActions>()(
  immer((set) => ({
    isPlaying: false,
    volume: 1,
    muted: false,
    quality: 'auto',
    currentTime: 0,
    duration: 0,
    buffering: false,
    
    setPlaying: (playing) =>
      set((state) => {
        state.isPlaying = playing
      }),
      
    setVolume: (volume) =>
      set((state) => {
        state.volume = volume
        state.muted = volume === 0
      }),
      
    setMuted: (muted) =>
      set((state) => {
        state.muted = muted
      }),
      
    setQuality: (quality) =>
      set((state) => {
        state.quality = quality
      }),
      
    setCurrentTime: (time) =>
      set((state) => {
        state.currentTime = time
      }),
      
    setDuration: (duration) =>
      set((state) => {
        state.duration = duration
      }),
      
    setBuffering: (buffering) =>
      set((state) => {
        state.buffering = buffering
      }),
  }))
)
```

## Video Player com HLS

### Video Player Component
```typescript
// components/player/video-player.tsx
'use client'

import { useRef, useEffect } from 'react'
import ReactPlayer from 'react-player'
import Hls from 'hls.js'
import { useTrialStore } from '@/stores/trial.store'
import { usePlayerStore } from '@/stores/player.store'
import { TrialOverlay } from './trial-overlay'

interface VideoPlayerProps {
  url: string
  fallbackUrls?: string[]
  poster?: string
}

export function VideoPlayer({ url, fallbackUrls = [], poster }: VideoPlayerProps) {
  const playerRef = useRef<ReactPlayer>(null)
  const hlsRef = useRef<Hls | null>(null)
  
  const { startTrial, isExpired, timeRemaining } = useTrialStore()
  const { 
    isPlaying, 
    setPlaying, 
    setDuration,
    setCurrentTime,
    setBuffering,
    quality
  } = usePlayerStore()
  
  useEffect(() => {
    // Iniciar trial quando o player carregar
    if (!isExpired && timeRemaining === 300) {
      startTrial()
    }
  }, [])
  
  const handleReady = () => {
    const player = playerRef.current
    if (!player) return
    
    const internalPlayer = player.getInternalPlayer()
    if (internalPlayer && Hls.isSupported()) {
      const hls = new Hls({
        autoStartLoad: true,
        startLevel: quality === 'auto' ? -1 : quality as number,
        capLevelToPlayerSize: true,
        capLevelOnFPSDrop: true,
      })
      
      hlsRef.current = hls
      
      hls.on(Hls.Events.MANIFEST_PARSED, (event, data) => {
        console.log('Qualidades disponíveis:', data.levels)
      })
      
      hls.on(Hls.Events.ERROR, (event, data) => {
        if (data.fatal) {
          console.error('Erro fatal HLS:', data)
          // Tentar fallback
          if (fallbackUrls.length > 0) {
            // Implementar lógica de fallback
          }
        }
      })
    }
  }
  
  return (
    <div className="relative aspect-video bg-black rounded-lg overflow-hidden">
      <ReactPlayer
        ref={playerRef}
        url={url}
        playing={isPlaying && !isExpired}
        controls={!isExpired}
        width="100%"
        height="100%"
        onReady={handleReady}
        onPlay={() => setPlaying(true)}
        onPause={() => setPlaying(false)}
        onDuration={setDuration}
        onProgress={({ playedSeconds }) => setCurrentTime(playedSeconds)}
        onBuffer={() => setBuffering(true)}
        onBufferEnd={() => setBuffering(false)}
        config={{
          file: {
            forceHLS: true,
            hlsOptions: {
              maxLoadingDelay: 4,
              minAutoBitrate: 0,
              lowLatencyMode: true,
            },
          },
        }}
      />
      
      {isExpired && <TrialOverlay />}
    </div>
  )
}
```

### Trial Overlay
```typescript
// components/player/trial-overlay.tsx
'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { createClient } from '@/lib/supabase/client'
import { useRouter } from 'next/navigation'
import { Loader2 } from 'lucide-react'

export function TrialOverlay() {
  const [loading, setLoading] = useState(false)
  const router = useRouter()
  const supabase = createClient()
  
  const handleGenerateToken = async () => {
    setLoading(true)
    
    try {
      const response = await fetch('/api/generate-token', {
        method: 'POST',
      })
      
      const { token } = await response.json()
      
      // Detectar plataforma
      const isIOS = /iPhone|iPad|iPod/.test(navigator.userAgent)
      const isAndroid = /Android/.test(navigator.userAgent)
      
      if (isIOS) {
        window.location.href = `${process.env.NEXT_PUBLIC_IOS_APP_URL}?token=${token}`
      } else if (isAndroid) {
        window.location.href = `${process.env.NEXT_PUBLIC_ANDROID_APP_URL}?token=${token}`
      } else {
        // Desktop - mostrar QR code ou instruções
        router.push(`/mobile-app?token=${token}`)
      }
    } catch (error) {
      console.error('Erro ao gerar token:', error)
    } finally {
      setLoading(false)
    }
  }
  
  return (
    <div className="absolute inset-0 bg-black/90 flex items-center justify-center p-8">
      <div className="max-w-md w-full text-center space-y-6">
        <h2 className="text-3xl font-bold text-white">
          Tu prueba gratuita ha terminado
        </h2>
        
        <p className="text-gray-300">
          ¡Disfruta de 1 mes gratis al descargar nuestra app!
        </p>
        
        <div className="space-y-4">
          <Button
            size="lg"
            className="w-full"
            onClick={handleGenerateToken}
            disabled={loading}
          >
            {loading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Generando acceso...
              </>
            ) : (
              'Obtener 1 Mes Gratis'
            )}
          </Button>
          
          <Button
            variant="outline"
            size="lg"
            className="w-full"
            onClick={() => router.push('/pricing')}
          >
            Ver Planes de Suscripción
          </Button>
        </div>
      </div>
    </div>
  )
}
```

## Componentes UI

### Card de Canal
```typescript
// components/channel-card.tsx
import { Card, CardContent, CardFooter } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import Image from 'next/image'
import Link from 'next/link'

interface ChannelCardProps {
  channel: {
    id: string
    name: string
    logo_url: string
    category: string
    is_premium: boolean
    viewer_count?: number
  }
}

export function ChannelCard({ channel }: ChannelCardProps) {
  return (
    <Link href={`/watch/${channel.id}`}>
      <Card className="group cursor-pointer transition-all hover:scale-105">
        <CardContent className="p-0">
          <div className="relative aspect-video">
            <Image
              src={channel.logo_url}
              alt={channel.name}
              fill
              className="object-cover rounded-t-lg"
            />
            {channel.is_premium && (
              <Badge className="absolute top-2 right-2" variant="secondary">
                Premium
              </Badge>
            )}
          </div>
        </CardContent>
        <CardFooter className="flex justify-between items-center p-4">
          <div>
            <h3 className="font-semibold">{channel.name}</h3>
            <p className="text-sm text-muted-foreground">{channel.category}</p>
          </div>
          {channel.viewer_count && (
            <span className="text-sm text-muted-foreground">
              {channel.viewer_count} viendo
            </span>
          )}
        </CardFooter>
      </Card>
    </Link>
  )
}
```

### Form de Login
```typescript
// components/auth/login-form.tsx
'use client'

import { zodResolver } from '@hookform/resolvers/zod'
import { useForm } from 'react-hook-form'
import * as z from 'zod'
import { Button } from '@/components/ui/button'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { createClient } from '@/lib/supabase/client'
import { useRouter } from 'next/navigation'
import { toast } from 'sonner'

const formSchema = z.object({
  email: z.string().email('Email inválido'),
  password: z.string().min(6, 'La contraseña debe tener al menos 6 caracteres'),
})

export function LoginForm() {
  const router = useRouter()
  const supabase = createClient()
  
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: '',
      password: '',
    },
  })
  
  async function onSubmit(values: z.infer<typeof formSchema>) {
    const { error } = await supabase.auth.signInWithPassword({
      email: values.email,
      password: values.password,
    })
    
    if (error) {
      toast.error('Error al iniciar sesión', {
        description: error.message,
      })
      return
    }
    
    toast.success('¡Bienvenido de vuelta!')
    router.push('/browse')
  }
  
  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Email</FormLabel>
              <FormControl>
                <Input placeholder="<EMAIL>" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <FormField
          control={form.control}
          name="password"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Contraseña</FormLabel>
              <FormControl>
                <Input type="password" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <Button type="submit" className="w-full">
          Iniciar Sesión
        </Button>
      </form>
    </Form>
  )
}
```

## Tailwind CSS 4

### Configuração do Tema
```css
/* app/globals.css */
@import "tailwindcss";

@theme {
  /* Cores da marca */
  --color-brand-50: oklch(0.97 0.02 20);
  --color-brand-100: oklch(0.95 0.04 20);
  --color-brand-200: oklch(0.91 0.08 20);
  --color-brand-300: oklch(0.84 0.13 20);
  --color-brand-400: oklch(0.73 0.19 20);
  --color-brand-500: oklch(0.61 0.21 20); /* Cor principal */
  --color-brand-600: oklch(0.51 0.20 20);
  --color-brand-700: oklch(0.41 0.17 20);
  --color-brand-800: oklch(0.34 0.14 20);
  --color-brand-900: oklch(0.28 0.11 20);
  
  /* Animações customizadas */
  --animate-slide-up: slide-up 0.3s ease-out;
  --animate-fade-in: fade-in 0.5s ease-out;
  
  @keyframes slide-up {
    from {
      transform: translateY(20px);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }
  
  @keyframes fade-in {
    from { opacity: 0; }
    to { opacity: 1; }
  }
  
  /* Sombras customizadas */
  --shadow-premium: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  
  /* Breakpoints customizados */
  --breakpoint-3xl: 120rem;
}

/* Dark mode usando classe */
@custom-variant dark (&:where(.dark, .dark *));

/* Utilitários customizados */
@utility gradient-premium {
  background: linear-gradient(135deg, var(--color-brand-500), var(--color-brand-700));
}

@utility text-gradient {
  background: linear-gradient(135deg, var(--color-brand-400), var(--color-brand-600));
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}
```

## Estrutura de Pastas Recomendada

```
streamplus-espana/
├── src/
│   ├── app/
│   │   ├── (auth)/
│   │   │   ├── login/
│   │   │   ├── register/
│   │   │   └── layout.tsx
│   │   ├── (platform)/
│   │   │   ├── browse/
│   │   │   ├── watch/[id]/
│   │   │   ├── profile/
│   │   │   └── layout.tsx
│   │   ├── api/
│   │   │   ├── generate-token/
│   │   │   └── verify-token/
│   │   └── layout.tsx
│   ├── components/
│   │   ├── auth/
│   │   ├── player/
│   │   ├── ui/
│   │   └── shared/
│   ├── hooks/
│   ├── lib/
│   │   └── supabase/
│   ├── stores/
│   └── types/
├── public/
├── .env.local
└── package.json
```

Este arquivo contém todos os padrões e snippets necessários para manter consistência no desenvolvimento do StreamPlus España.