// Debug final completo
// Execute no console em qualquer página

console.clear();
console.log('🔍 DEBUG FINAL COMPLETO\n');

// 1. Testar se a API está respondendo
console.log('1️⃣ Testando API /api/iptv-simple...');
fetch('/api/iptv-simple')
  .then(r => r.json())
  .then(data => {
    if (data.success && data.data.length > 0) {
      console.log('✅ API funcionando!');
      console.log(`- Total de canais: ${data.data.length}`);
      console.log('- Primeiro canal:', data.data[0].name);
      
      // Encontrar o canal Golf
      const golfChannel = data.data.find(ch => ch.name && ch.name.includes('Golf'));
      if (golfChannel) {
        console.log('\n✅ Canal Golf encontrado:', golfChannel.name);
        console.log('- ID:', golfChannel.uniqueId);
        console.log('- URL:', golfChannel.url);
        
        // Montar o ID correto
        const fullChannelId = `iptv-${golfChannel.uniqueId}`;
        console.log('\n🎯 Para acessar este canal:');
        console.log(`window.location.href = '/watch/${fullChannelId}'`);
        
        // Criar botão para navegar
        const btn = document.createElement('button');
        btn.innerText = '🏌️ Ir para Golf Channel';
        btn.style.cssText = 'position:fixed;top:20px;right:20px;z-index:9999;padding:10px 20px;background:#22c55e;color:white;border:none;border-radius:8px;font-size:16px;cursor:pointer';
        btn.onclick = () => window.location.href = `/watch/${fullChannelId}`;
        document.body.appendChild(btn);
        console.log('\n✅ Botão criado no canto superior direito!');
      }
    } else {
      console.error('❌ API não retornou dados');
    }
  })
  .catch(err => console.error('❌ Erro na API:', err));

// 2. Se estamos em uma página de watch
if (window.location.pathname.includes('/watch/')) {
  console.log('\n2️⃣ Analisando página Watch...');
  
  setTimeout(() => {
    // Verificar se há vídeo
    const video = document.querySelector('video');
    if (video) {
      console.log('✅ Elemento video encontrado');
      console.log('- src:', video.src || 'VAZIO');
      console.log('- readyState:', video.readyState);
      
      // Tentar dar play
      if (video.paused && video.src) {
        console.log('\n🎬 Tentando dar play...');
        video.play().catch(e => console.error('Erro:', e.message));
      }
    } else {
      console.log('❌ Nenhum elemento video encontrado');
      
      // Verificar se há erro
      const error = document.querySelector('[class*="error"]');
      if (error) {
        console.log('❌ Erro na página:', error.textContent);
      }
    }
  }, 2000);
}

// 3. Criar atalhos úteis
console.log('\n3️⃣ ATALHOS CRIADOS:');
console.log('testGolf() - Navega para o canal Golf');
console.log('testHLS() - Vai para página de teste HLS');
console.log('reloadPage() - Recarrega a página');

window.testGolf = async () => {
  console.log('Buscando canal Golf...');
  const r = await fetch('/api/iptv-simple');
  const data = await r.json();
  const golf = data.data.find(ch => ch.name && ch.name.includes('Golf'));
  if (golf) {
    window.location.href = `/watch/iptv-${golf.uniqueId}`;
  } else {
    console.log('Canal Golf não encontrado');
  }
};

window.testHLS = () => {
  window.location.href = '/hls-test';
};

window.reloadPage = () => {
  window.location.reload();
};

console.log('\n✅ Debug carregado com sucesso!');