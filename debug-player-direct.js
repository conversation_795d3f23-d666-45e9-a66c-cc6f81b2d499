// Script direto para diagnosticar o player
// Execute no console do navegador

console.clear();
console.log('🔍 DIAGNÓSTICO DO PLAYER\n');

// 1. Verificar elementos de vídeo
const videos = document.querySelectorAll('video');
console.log(`📺 Elementos <video> encontrados: ${videos.length}`);

if (videos.length > 0) {
    videos.forEach((video, index) => {
        console.log(`\n--- Video ${index + 1} ---`);
        console.log('src:', video.src || 'VAZIO');
        console.log('currentSrc:', video.currentSrc || 'VAZIO');
        console.log('readyState:', video.readyState, '(0=nada, 1=metadata, 2=dados atuais, 3=dados futuros, 4=pronto)');
        console.log('networkState:', video.networkState, '(0=vazio, 1=ocioso, 2=carregando, 3=sem fonte)');
        console.log('paused:', video.paused);
        console.log('muted:', video.muted);
        console.log('volume:', video.volume);
        console.log('error:', video.error);
        console.log('duration:', video.duration);
        console.log('currentTime:', video.currentTime);
        
        // Verificar se há source elements
        const sources = video.querySelectorAll('source');
        if (sources.length > 0) {
            console.log('Sources:', sources.length);
            sources.forEach((source, i) => {
                console.log(`  Source ${i+1}: ${source.src}`);
            });
        }
    });
} else {
    console.log('❌ Nenhum elemento <video> encontrado!');
}

// 2. Verificar ReactPlayer
const reactPlayerDivs = document.querySelectorAll('[style*="width: 100%"][style*="height: 100%"]');
console.log(`\n⚛️ Possíveis ReactPlayer divs: ${reactPlayerDivs.length}`);

// 3. Verificar HLS
if (typeof Hls !== 'undefined') {
    console.log('\n📡 HLS.js está carregado');
    console.log('HLS.isSupported():', Hls.isSupported());
} else {
    console.log('\n⚠️ HLS.js NÃO está carregado');
}

// 4. Verificar botões de play
const playButtons = [];
document.querySelectorAll('button').forEach(btn => {
    const svg = btn.querySelector('svg');
    if (svg && (svg.classList.contains('fill-white') || btn.textContent.includes('play'))) {
        playButtons.push(btn);
    }
});

console.log(`\n🎮 Botões de play encontrados: ${playButtons.length}`);
if (playButtons.length > 0) {
    console.log('Para clicar no primeiro botão, execute:');
    console.log('document.querySelectorAll("button")[' + Array.from(document.querySelectorAll("button")).indexOf(playButtons[0]) + '].click()');
    
    // Salvar referência global
    window._playButton = playButtons[0];
    console.log('Ou use: window._playButton.click()');
}

// 5. Verificar estado da aplicação
console.log('\n📊 ESTADO DA APLICAÇÃO:');
console.log('URL atual:', window.location.href);
console.log('Canal ID:', window.location.pathname.split('/').pop());

// 6. Tentar encontrar dados do canal no DOM
const scripts = document.querySelectorAll('script');
let channelData = null;
scripts.forEach(script => {
    if (script.textContent && script.textContent.includes('channelData')) {
        console.log('\n📄 Script com channelData encontrado!');
        // Tentar extrair dados
        const match = script.textContent.match(/channelData[:\s]*({[^}]+})/);
        if (match) {
            try {
                channelData = JSON.parse(match[1]);
                console.log('Channel data:', channelData);
            } catch (e) {
                console.log('Erro ao parsear channel data');
            }
        }
    }
});

// 7. Verificar network requests
console.log('\n🌐 DICAS DE DEBUG:');
console.log('1. Abra a aba Network do DevTools');
console.log('2. Filtre por "m3u8" ou "ts" para ver streams');
console.log('3. Verifique se há erros de CORS (bloqueio)');
console.log('4. Se o vídeo está sem src, clique no botão de play');

// 8. Função helper para tocar vídeo
window.playVideo = function() {
    const video = document.querySelector('video');
    if (video) {
        console.log('Tentando tocar vídeo...');
        video.muted = true; // Necessário para autoplay
        video.play()
            .then(() => console.log('✅ Vídeo tocando!'))
            .catch(err => console.log('❌ Erro:', err.message));
    } else {
        console.log('Nenhum vídeo encontrado');
    }
};

console.log('\n💡 Comandos úteis:');
console.log('- window.playVideo() - Tentar tocar o vídeo');
console.log('- window._playButton.click() - Clicar no botão de play');
console.log('- location.reload() - Recarregar a página');