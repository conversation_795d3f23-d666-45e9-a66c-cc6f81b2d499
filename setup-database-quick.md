# Configuração Rápida do Banco de Dados

## Opção 1: Via Dashboard Supabase (Recomendado)

1. **Acesse seu projeto Supabase:**
   https://supabase.com/dashboard/project/bgeecdlhnwkgdujoyont

2. **Vá para o SQL Editor** (ícone de código no menu lateral)

3. **<PERSON> e execute TODO o conteúdo do arquivo:**
   `supabase/migrations/001_initial_schema.sql`

4. **Verifique se funcionou:**
   ```sql
   SELECT table_name FROM information_schema.tables 
   WHERE table_schema = 'public';
   ```
   
   Deve mostrar:
   - profiles
   - channels
   - viewing_sessions
   - app_verification_tokens
   - favorites
   - channel_schedule

## Opção 2: Via API Route (Automático)

1. **Com o servidor rodando, acesse:**
   http://localhost:3000/api/setup-database

2. **Se der erro de permissão**, use a Opção 1

## Verificação Rápida

Execute no SQL Editor:
```sql
-- Ver canais demo
SELECT name, category, required_tier FROM channels;

-- Ver tipos criados
SELECT typname FROM pg_type WHERE typnamespace = 'public'::regnamespace;
```

## Status Atual

✅ Projeto Supabase: **bgeecdlhnwkgdujoyont**
✅ Credenciais: Configuradas em `.env.local`
❌ Tabelas: Ainda não criadas
❌ RLS Policies: Ainda não aplicadas

Após executar o SQL, todas as tabelas e políticas de segurança estarão configuradas!