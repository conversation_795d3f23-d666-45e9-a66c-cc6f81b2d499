-- Execute este script no SQL Editor do Supabase

-- Create admin_users table
CREATE TABLE IF NOT EXISTS admin_users (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  email TEXT UNIQUE NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id)
);

-- Enable RLS
ALTER TABLE admin_users ENABLE ROW LEVEL SECURITY;

-- Admin users policies
CREATE POLICY "Ad<PERSON> can view admin_users" ON admin_users
  FOR SELECT USING (
    auth.uid() IN (SELECT user_id FROM admin_users)
  );

CREATE POLICY "Admins can insert admin_users" ON admin_users
  FOR INSERT WITH CHECK (
    auth.uid() IN (SELECT user_id FROM admin_users)
  );

CREATE POLICY "Admins can delete admin_users" ON admin_users
  FOR DELETE USING (
    auth.uid() IN (SELECT user_id FROM admin_users)
    AND id != (SELECT id FROM admin_users WHERE email = '<EMAIL>') -- Protect main admin
  );

-- Insert initial admin
INSERT INTO admin_users (email) VALUES ('<EMAIL>')
ON CONFLICT (email) DO NOTHING;

-- Update admin_config policies to use admin_users table
DROP POLICY IF EXISTS "Admins can view admin_config" ON admin_config;
DROP POLICY IF EXISTS "Admins can update admin_config" ON admin_config;
DROP POLICY IF EXISTS "Admins can view download_stats" ON download_stats;

CREATE POLICY "Admins can view admin_config" ON admin_config
  FOR SELECT USING (
    auth.uid() IN (SELECT user_id FROM admin_users)
  );

CREATE POLICY "Admins can update admin_config" ON admin_config
  FOR UPDATE USING (
    auth.uid() IN (SELECT user_id FROM admin_users)
  );

CREATE POLICY "Admins can view download_stats" ON download_stats
  FOR SELECT USING (
    auth.uid() IN (SELECT user_id FROM admin_users)
  );