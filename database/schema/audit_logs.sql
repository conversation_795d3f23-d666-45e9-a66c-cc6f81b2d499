-- Schema de Auditoria e Logs de Segurança
-- Para usar com Neon Database (PostgreSQL)

-- <PERSON><PERSON>r extensão para UUIDs se não existir
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Tabela principal de logs de auditoria
CREATE TABLE IF NOT EXISTS audit_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id TEXT NOT NULL,
  action VARCHAR(50) NOT NULL,
  path TEXT,
  method VARCHAR(10),
  data JSONB,
  ip_address INET,
  user_agent TEXT,
  timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  severity VARCHAR(20) NOT NULL CHECK (severity IN ('info', 'warning', 'error', 'critical')),
  metadata JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Índices para performance
CREATE INDEX IF NOT EXISTS idx_audit_logs_user_id ON audit_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_action ON audit_logs(action);
CREATE INDEX IF NOT EXISTS idx_audit_logs_timestamp ON audit_logs(timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_audit_logs_severity ON audit_logs(severity);
CREATE INDEX IF NOT EXISTS idx_audit_logs_ip_address ON audit_logs(ip_address);

-- Índice composto para queries comuns
CREATE INDEX IF NOT EXISTS idx_audit_logs_user_action_timestamp 
  ON audit_logs(user_id, action, timestamp DESC);

-- Tabela para tentativas de login (análise de segurança)
CREATE TABLE IF NOT EXISTS login_attempts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email VARCHAR(255) NOT NULL,
  ip_address INET NOT NULL,
  success BOOLEAN NOT NULL DEFAULT false,
  failure_reason VARCHAR(100),
  user_agent TEXT,
  timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Índices para login_attempts
CREATE INDEX IF NOT EXISTS idx_login_attempts_email ON login_attempts(email);
CREATE INDEX IF NOT EXISTS idx_login_attempts_ip ON login_attempts(ip_address);
CREATE INDEX IF NOT EXISTS idx_login_attempts_timestamp ON login_attempts(timestamp DESC);

-- Tabela para sessões ativas (monitoramento)
CREATE TABLE IF NOT EXISTS active_sessions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  session_id VARCHAR(255) UNIQUE NOT NULL,
  user_id TEXT NOT NULL,
  ip_address INET,
  user_agent TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  last_activity TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
  is_active BOOLEAN DEFAULT true
);

-- Índices para active_sessions
CREATE INDEX IF NOT EXISTS idx_active_sessions_user_id ON active_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_active_sessions_expires_at ON active_sessions(expires_at);
CREATE INDEX IF NOT EXISTS idx_active_sessions_is_active ON active_sessions(is_active);

-- Tabela para rate limiting persistente (opcional para produção)
CREATE TABLE IF NOT EXISTS rate_limit_buckets (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  identifier VARCHAR(255) NOT NULL,
  endpoint VARCHAR(255) NOT NULL,
  request_count INTEGER NOT NULL DEFAULT 1,
  window_start TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  window_end TIMESTAMP WITH TIME ZONE NOT NULL,
  UNIQUE(identifier, endpoint, window_start)
);

-- Índices para rate_limit_buckets
CREATE INDEX IF NOT EXISTS idx_rate_limit_identifier ON rate_limit_buckets(identifier);
CREATE INDEX IF NOT EXISTS idx_rate_limit_window_end ON rate_limit_buckets(window_end);

-- View para análise de atividades suspeitas
CREATE OR REPLACE VIEW suspicious_activities AS
SELECT 
  al.user_id,
  al.ip_address,
  COUNT(*) as activity_count,
  COUNT(DISTINCT al.action) as unique_actions,
  MAX(al.timestamp) as last_activity,
  MIN(al.timestamp) as first_activity,
  ARRAY_AGG(DISTINCT al.action) as actions
FROM audit_logs al
WHERE 
  al.timestamp > NOW() - INTERVAL '1 hour'
  AND al.severity IN ('warning', 'error', 'critical')
GROUP BY al.user_id, al.ip_address
HAVING COUNT(*) > 10;

-- View para análise de tentativas de login falhadas
CREATE OR REPLACE VIEW failed_login_analysis AS
SELECT 
  la.email,
  la.ip_address,
  COUNT(*) as failure_count,
  MAX(la.timestamp) as last_attempt,
  MIN(la.timestamp) as first_attempt,
  ARRAY_AGG(DISTINCT la.failure_reason) as reasons
FROM login_attempts la
WHERE 
  la.success = false
  AND la.timestamp > NOW() - INTERVAL '24 hours'
GROUP BY la.email, la.ip_address
HAVING COUNT(*) > 3;

-- Função para limpar logs antigos (executar periodicamente)
CREATE OR REPLACE FUNCTION cleanup_old_logs() RETURNS void AS $$
BEGIN
  -- Remover logs de info mais antigos que 30 dias
  DELETE FROM audit_logs 
  WHERE severity = 'info' 
    AND timestamp < NOW() - INTERVAL '30 days';
  
  -- Remover logs de warning mais antigos que 90 dias
  DELETE FROM audit_logs 
  WHERE severity = 'warning' 
    AND timestamp < NOW() - INTERVAL '90 days';
  
  -- Logs de error e critical são mantidos por 1 ano
  DELETE FROM audit_logs 
  WHERE severity IN ('error', 'critical') 
    AND timestamp < NOW() - INTERVAL '365 days';
  
  -- Limpar tentativas de login antigas
  DELETE FROM login_attempts 
  WHERE timestamp < NOW() - INTERVAL '90 days';
  
  -- Limpar sessões expiradas
  DELETE FROM active_sessions 
  WHERE expires_at < NOW() - INTERVAL '30 days';
  
  -- Limpar buckets de rate limit antigos
  DELETE FROM rate_limit_buckets 
  WHERE window_end < NOW() - INTERVAL '1 day';
END;
$$ LANGUAGE plpgsql;

-- Trigger para atualizar last_activity em sessões
CREATE OR REPLACE FUNCTION update_session_activity() RETURNS TRIGGER AS $$
BEGIN
  UPDATE active_sessions 
  SET last_activity = NOW() 
  WHERE user_id = NEW.user_id 
    AND is_active = true;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_session_activity
AFTER INSERT ON audit_logs
FOR EACH ROW
EXECUTE FUNCTION update_session_activity();

-- Políticas de Row Level Security (RLS) - se habilitado
-- ALTER TABLE audit_logs ENABLE ROW LEVEL SECURITY;
-- ALTER TABLE login_attempts ENABLE ROW LEVEL SECURITY;
-- ALTER TABLE active_sessions ENABLE ROW LEVEL SECURITY;

-- Política exemplo: usuários só podem ver seus próprios logs
-- CREATE POLICY "Users can view own logs" ON audit_logs
--   FOR SELECT USING (auth.uid()::text = user_id);

-- Política exemplo: apenas admins podem ver todos os logs
-- CREATE POLICY "Admins can view all logs" ON audit_logs
--   FOR ALL USING (
--     EXISTS (
--       SELECT 1 FROM users 
--       WHERE id = auth.uid()::text 
--       AND role = 'admin'
--     )
--   );

-- Comentários nas tabelas
COMMENT ON TABLE audit_logs IS 'Logs de auditoria de todas as ações do sistema';
COMMENT ON TABLE login_attempts IS 'Registro de todas as tentativas de login para análise de segurança';
COMMENT ON TABLE active_sessions IS 'Sessões ativas dos usuários para monitoramento';
COMMENT ON TABLE rate_limit_buckets IS 'Controle de rate limiting persistente';

COMMENT ON COLUMN audit_logs.severity IS 'Níveis: info (operações normais), warning (ações suspeitas), error (erros), critical (violações de segurança)';
COMMENT ON COLUMN audit_logs.metadata IS 'Dados adicionais em formato JSON para contexto específico da ação';