-- Execute este script completo no SQL Editor do Supabase
-- <PERSON>ste script configura todo o sistema de administração

-- 1. Create admin_config table
CREATE TABLE IF NOT EXISTS admin_config (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  config_key TEXT UNIQUE NOT NULL,
  config_value TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. Create download_stats table
CREATE TABLE IF NOT EXISTS download_stats (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  download_type TEXT NOT NULL CHECK (download_type IN ('apk', 'qrcode')),
  ip_address TEXT,
  user_agent TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. Create admin_users table
CREATE TABLE IF NOT EXISTS admin_users (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  email TEXT UNIQUE NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id)
);

-- 4. Insert default config values
INSERT INTO admin_config (config_key, config_value) VALUES
  ('apk_url', ''),
  ('qrcode_url', '')
ON CONFLICT (config_key) DO NOTHING;

-- 5. Insert initial admin
INSERT INTO admin_users (email) VALUES ('<EMAIL>')
ON CONFLICT (email) DO NOTHING;

-- 6. Enable RLS on all tables
ALTER TABLE admin_config ENABLE ROW LEVEL SECURITY;
ALTER TABLE download_stats ENABLE ROW LEVEL SECURITY;
ALTER TABLE admin_users ENABLE ROW LEVEL SECURITY;

-- 7. Admin config policies
CREATE POLICY "Admins can view admin_config" ON admin_config
  FOR SELECT USING (
    auth.uid() IN (SELECT user_id FROM admin_users)
  );

CREATE POLICY "Admins can update admin_config" ON admin_config
  FOR UPDATE USING (
    auth.uid() IN (SELECT user_id FROM admin_users)
  );

-- 8. Download stats policies
CREATE POLICY "Anyone can insert download_stats" ON download_stats
  FOR INSERT WITH CHECK (true);

CREATE POLICY "Admins can view download_stats" ON download_stats
  FOR SELECT USING (
    auth.uid() IN (SELECT user_id FROM admin_users)
  );

-- 9. Admin users policies
CREATE POLICY "Admins can view admin_users" ON admin_users
  FOR SELECT USING (
    auth.uid() IN (SELECT user_id FROM admin_users)
  );

CREATE POLICY "Admins can insert admin_users" ON admin_users
  FOR INSERT WITH CHECK (
    auth.uid() IN (SELECT user_id FROM admin_users)
  );

CREATE POLICY "Admins can delete admin_users" ON admin_users
  FOR DELETE USING (
    auth.uid() IN (SELECT user_id FROM admin_users)
    AND id != (SELECT id FROM admin_users WHERE email = '<EMAIL>')
  );

-- 10. Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 11. Trigger to update updated_at
CREATE TRIGGER update_admin_config_updated_at
  BEFORE UPDATE ON admin_config
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- 12. Function to update admin_users.user_id when a user registers
CREATE OR REPLACE FUNCTION update_admin_user_id()
RETURNS TRIGGER AS $$
BEGIN
  -- Update admin_users table if email matches
  UPDATE admin_users 
  SET user_id = NEW.id 
  WHERE email = NEW.email 
  AND user_id IS NULL;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 13. Trigger to call the function on user creation
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW
  EXECUTE FUNCTION update_admin_user_id();

-- 14. Update existing admin user for the main admin email
UPDATE admin_users 
SET user_id = (SELECT id FROM auth.users WHERE email = '<EMAIL>' LIMIT 1)
WHERE email = '<EMAIL>' 
AND user_id IS NULL;