-- Ta<PERSON><PERSON> para rastrear sessões de trial
-- O trial é global para todo o site, não por canal
CREATE TABLE IF NOT EXISTS trial_sessions (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) UNIQUE,
  session_id VARCHAR(100) UNIQUE, -- Para usuários não logados
  started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
  time_remaining INTEGER DEFAULT 300, -- 5 minutos em segundos
  is_expired BOOLEAN DEFAULT false,
  device_info JSONB, -- Armazenar info do dispositivo
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Índices
CREATE INDEX IF NOT EXISTS idx_trial_sessions_user ON trial_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_trial_sessions_session ON trial_sessions(session_id);
CREATE INDEX IF NOT EXISTS idx_trial_sessions_expired ON trial_sessions(is_expired, expires_at);

-- Enable RLS
ALTER TABLE trial_sessions ENABLE ROW LEVEL SECURITY;

-- Políticas RLS
CREATE POLICY "Usuários veem próprias sessões" ON trial_sessions
  FOR SELECT USING (auth.uid() = user_id OR session_id IS NOT NULL);

CREATE POLICY "Sistema pode criar sessões" ON trial_sessions
  FOR INSERT WITH CHECK (true);

CREATE POLICY "Sistema pode atualizar sessões" ON trial_sessions
  FOR UPDATE USING (true);

-- Função para verificar ou criar sessão de trial
CREATE OR REPLACE FUNCTION get_or_create_trial_session(
  p_user_id UUID DEFAULT NULL,
  p_session_id VARCHAR DEFAULT NULL,
  p_device_info JSONB DEFAULT NULL
)
RETURNS TABLE (
  trial_id UUID,
  time_remaining INTEGER,
  is_expired BOOLEAN,
  expires_at TIMESTAMP WITH TIME ZONE
) AS $$
DECLARE
  v_existing_trial RECORD;
  v_new_expires_at TIMESTAMP WITH TIME ZONE;
BEGIN
  -- Buscar trial existente
  IF p_user_id IS NOT NULL THEN
    SELECT * INTO v_existing_trial
    FROM trial_sessions ts
    WHERE ts.user_id = p_user_id
    LIMIT 1;
  ELSIF p_session_id IS NOT NULL THEN
    SELECT * INTO v_existing_trial
    FROM trial_sessions ts
    WHERE ts.session_id = p_session_id
    LIMIT 1;
  END IF;
  
  -- Se encontrou trial existente
  IF v_existing_trial.id IS NOT NULL THEN
    -- Verificar se expirou
    IF v_existing_trial.expires_at < NOW() OR v_existing_trial.is_expired THEN
      -- Marcar como expirado
      UPDATE trial_sessions
      SET is_expired = true, time_remaining = 0
      WHERE id = v_existing_trial.id;
      
      RETURN QUERY SELECT 
        v_existing_trial.id,
        0::INTEGER,
        true,
        v_existing_trial.expires_at;
    ELSE
      -- Calcular tempo restante
      RETURN QUERY SELECT 
        v_existing_trial.id,
        GREATEST(0, EXTRACT(EPOCH FROM (v_existing_trial.expires_at - NOW())))::INTEGER,
        false,
        v_existing_trial.expires_at;
    END IF;
  ELSE
    -- Criar nova sessão de trial
    v_new_expires_at := NOW() + INTERVAL '5 minutes';
    
    INSERT INTO trial_sessions (user_id, session_id, expires_at, device_info)
    VALUES (p_user_id, p_session_id, v_new_expires_at, p_device_info)
    RETURNING id INTO v_existing_trial;
    
    RETURN QUERY SELECT 
      v_existing_trial.id,
      300::INTEGER, -- 5 minutos
      false,
      v_new_expires_at;
  END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Função para marcar trial como expirado
CREATE OR REPLACE FUNCTION expire_trial_session(
  p_user_id UUID DEFAULT NULL,
  p_session_id VARCHAR DEFAULT NULL
)
RETURNS BOOLEAN AS $$
BEGIN
  IF p_user_id IS NOT NULL THEN
    UPDATE trial_sessions
    SET is_expired = true, time_remaining = 0
    WHERE user_id = p_user_id;
  ELSIF p_session_id IS NOT NULL THEN
    UPDATE trial_sessions
    SET is_expired = true, time_remaining = 0
    WHERE session_id = p_session_id;
  END IF;
  
  RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Função para limpar trials expirados (pode ser usada em cron job)
CREATE OR REPLACE FUNCTION cleanup_expired_trials()
RETURNS INTEGER AS $$
DECLARE
  v_deleted_count INTEGER;
BEGIN
  DELETE FROM trial_sessions
  WHERE expires_at < NOW() - INTERVAL '7 days'
  OR (is_expired = true AND created_at < NOW() - INTERVAL '7 days');
  
  GET DIAGNOSTICS v_deleted_count = ROW_COUNT;
  RETURN v_deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;