-- Update RLS policies to work without auth.uid()
-- This should be run in Supabase SQL Editor

-- Drop existing policies
DROP POLICY IF EXISTS "Ad<PERSON> can view admin_config" ON admin_config;
DROP POLICY IF EXISTS "Ad<PERSON> can update admin_config" ON admin_config;
DROP POLICY IF EXISTS "Ad<PERSON> can view download_stats" ON download_stats;
DROP POLICY IF EXISTS "Ad<PERSON> can view admin_users" ON admin_users;
DROP POLICY IF EXISTS "Admins can insert admin_users" ON admin_users;
DROP POLICY IF EXISTS "Admins can delete admin_users" ON admin_users;

-- Create new simpler policies

-- Admin config - allow all authenticated requests (protected by middleware)
CREATE POLICY "Allow all admin_config select" ON admin_config
  FOR SELECT USING (true);

CREATE POLICY "Allow all admin_config update" ON admin_config
  FOR UPDATE USING (true);

-- Download stats
CREATE POLICY "Allow all download_stats insert" ON download_stats
  FOR INSERT WITH CHECK (true);

CREATE POLICY "Allow all download_stats select" ON download_stats
  FOR SELECT USING (true);

-- Admin users
CREATE POLICY "Allow all admin_users select" ON admin_users
  FOR SELECT USING (true);

CREATE POLICY "Allow all admin_users insert" ON admin_users
  FOR INSERT WITH CHECK (true);

CREATE POLICY "Allow all admin_users delete" ON admin_users
  FOR DELETE USING (
    id != (SELECT id FROM admin_users WHERE email = '<EMAIL>')
  );