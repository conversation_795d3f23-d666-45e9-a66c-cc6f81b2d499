-- Script para ativar assinatura manualmente
-- Execute este script no SQL Editor do Supabase

-- 1. Buscar o ID do usuário pelo email
SELECT id, email FROM auth.users WHERE email = '<EMAIL>';

-- 2. Se o usuário existe, usar o ID para atualizar o perfil
-- Substitua 'USER_ID_HERE' pelo ID encontrado acima
UPDATE profiles 
SET 
  stripe_customer_id = 'cus_manual_' || extract(epoch from now())::text,
  stripe_subscription_id = 'sub_manual_' || extract(epoch from now())::text,
  subscription_status = 'active',
  subscription_current_period_end = NOW() + INTERVAL '30 days',
  subscription_tier = 'premium',
  updated_at = NOW()
WHERE id = 'USER_ID_HERE';

-- 3. Verificar se a atualização foi bem sucedida
SELECT 
  id,
  email,
  subscription_tier,
  subscription_status,
  stripe_customer_id,
  stripe_subscription_id,
  subscription_current_period_end,
  subscription_current_period_end > NOW() as is_active
FROM profiles 
WHERE id = 'USER_ID_HERE';

-- 4. Se o perfil não existir, criar um novo
INSERT INTO profiles (
  id, 
  email,
  stripe_customer_id,
  stripe_subscription_id,
  subscription_status,
  subscription_current_period_end,
  subscription_tier,
  created_at,
  updated_at
) VALUES (
  'USER_ID_HERE',
  '<EMAIL>',
  'cus_manual_' || extract(epoch from now())::text,
  'sub_manual_' || extract(epoch from now())::text,
  'active',
  NOW() + INTERVAL '30 days',
  'premium',
  NOW(),
  NOW()
) ON CONFLICT (id) DO UPDATE SET
  stripe_customer_id = EXCLUDED.stripe_customer_id,
  stripe_subscription_id = EXCLUDED.stripe_subscription_id,
  subscription_status = EXCLUDED.subscription_status,
  subscription_current_period_end = EXCLUDED.subscription_current_period_end,
  subscription_tier = EXCLUDED.subscription_tier,
  updated_at = NOW();

-- 5. Verificar resultado final
SELECT 
  p.id,
  p.email,
  p.subscription_tier,
  p.subscription_status,
  p.stripe_customer_id,
  p.stripe_subscription_id,
  p.subscription_current_period_end,
  p.subscription_current_period_end > NOW() as is_active,
  u.email as auth_email
FROM profiles p
JOIN auth.users u ON u.id = p.id
WHERE u.email = '<EMAIL>';