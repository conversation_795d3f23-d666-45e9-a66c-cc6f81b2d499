-- Adici<PERSON>r colunas do Stripe na tabela profiles
-- Execute este script no SQL Editor do Supabase

-- 1. <PERSON>ici<PERSON>r colunas se não existirem
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS stripe_customer_id TEXT;
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS stripe_subscription_id TEXT;
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS subscription_status TEXT;
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS subscription_current_period_end TIMESTAMPTZ;
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS subscription_cancel_at_period_end BOOLEAN DEFAULT FALSE;
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS trial_end TIMESTAMPTZ;

-- 2. <PERSON><PERSON><PERSON> índices para melhor performance
CREATE INDEX IF NOT EXISTS idx_profiles_stripe_customer_id ON profiles(stripe_customer_id);
CREATE INDEX IF NOT EXISTS idx_profiles_subscription_status ON profiles(subscription_status);

-- 3. <PERSON><PERSON><PERSON> tabela de histórico de pagamentos se não existir
CREATE TABLE IF NOT EXISTS payment_history (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  stripe_invoice_id TEXT UNIQUE,
  amount DECIMAL(10, 2) NOT NULL,
  currency TEXT NOT NULL DEFAULT 'eur',
  status TEXT NOT NULL CHECK (status IN ('paid', 'failed', 'pending', 'refunded')),
  paid_at TIMESTAMPTZ,
  failed_at TIMESTAMPTZ,
  refunded_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);

-- 4. Criar índices na tabela payment_history
CREATE INDEX IF NOT EXISTS idx_payment_history_user_id ON payment_history(user_id);
CREATE INDEX IF NOT EXISTS idx_payment_history_status ON payment_history(status);

-- 5. Habilitar RLS
ALTER TABLE payment_history ENABLE ROW LEVEL SECURITY;

-- 6. Criar políticas RLS
DROP POLICY IF EXISTS "Users can view own payment history" ON payment_history;
CREATE POLICY "Users can view own payment history" ON payment_history
  FOR SELECT USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "System can insert payments" ON payment_history;
CREATE POLICY "System can insert payments" ON payment_history
  FOR INSERT WITH CHECK (true);

-- 7. Criar trigger para atualizar updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS update_payment_history_updated_at ON payment_history;
CREATE TRIGGER update_payment_history_updated_at 
  BEFORE UPDATE ON payment_history
  FOR EACH ROW 
  EXECUTE FUNCTION update_updated_at_column();

-- 8. Verificar estrutura final
SELECT 
  column_name, 
  data_type, 
  is_nullable,
  column_default
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name = 'profiles'
AND column_name IN (
  'stripe_customer_id',
  'stripe_subscription_id',
  'subscription_status',
  'subscription_current_period_end',
  'subscription_cancel_at_period_end',
  'subscription_tier',
  'trial_end'
)
ORDER BY ordinal_position;

-- 9. Testar atualizando um usuário específico (substitua o email)
-- UPDATE profiles 
-- SET 
--   stripe_customer_id = 'cus_test_' || gen_random_uuid(),
--   stripe_subscription_id = 'sub_test_' || gen_random_uuid(),
--   subscription_status = 'active',
--   subscription_current_period_end = NOW() + INTERVAL '30 days',
--   subscription_tier = 'premium'
-- WHERE id = (SELECT id FROM auth.users WHERE email = '<EMAIL>' LIMIT 1)
-- RETURNING *;