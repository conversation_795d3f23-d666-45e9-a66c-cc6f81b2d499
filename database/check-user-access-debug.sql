-- Função check_user_access com logs detalhados para debug
-- Execute este script no SQL Editor do Supabase

DROP FUNCTION IF EXISTS check_user_access(UUID);

CREATE OR REPLACE FUNCTION check_user_access(p_user_id UUID)
RETURNS TABLE (
  has_access BOOLEAN,
  access_type VARCHAR,
  expires_at TIMESTAMP WITH TIME ZONE,
  debug_info JSONB
) AS $$
DECLARE
  v_profile RECORD;
  v_user_access RECORD;
  v_debug JSONB;
  v_now TIMESTAMP WITH TIME ZONE;
BEGIN
  v_now := NOW();
  v_debug := jsonb_build_object(
    'function', 'check_user_access',
    'user_id', p_user_id,
    'current_time', v_now,
    'checks', jsonb_build_array()
  );

  -- Verificar se o usuário existe
  v_debug := v_debug || jsonb_build_object(
    'checks', v_debug->'checks' || jsonb_build_array(
      jsonb_build_object(
        'type', 'user_exists',
        'user_id', p_user_id,
        'exists', EXISTS(SELECT 1 FROM profiles WHERE id = p_user_id)
      )
    )
  );

  -- Buscar dados do profile
  SELECT * INTO v_profile FROM profiles WHERE id = p_user_id;
  
  IF v_profile IS NOT NULL THEN
    v_debug := v_debug || jsonb_build_object(
      'profile_data', jsonb_build_object(
        'subscription_tier', v_profile.subscription_tier,
        'subscription_status', v_profile.subscription_status,
        'subscription_current_period_end', v_profile.subscription_current_period_end,
        'trial_end_date', v_profile.trial_end_date,
        'stripe_customer_id', v_profile.stripe_customer_id,
        'stripe_subscription_id', v_profile.stripe_subscription_id
      )
    );
  END IF;

  -- Verificar assinatura premium
  v_debug := v_debug || jsonb_build_object(
    'checks', v_debug->'checks' || jsonb_build_array(
      jsonb_build_object(
        'type', 'premium_subscription',
        'has_premium_tier', v_profile.subscription_tier = 'premium',
        'subscription_status', v_profile.subscription_status,
        'is_active', v_profile.subscription_status = 'active',
        'subscription_end', v_profile.subscription_current_period_end,
        'is_valid', v_profile.subscription_current_period_end > v_now,
        'days_remaining', 
          CASE 
            WHEN v_profile.subscription_current_period_end IS NOT NULL 
            THEN EXTRACT(DAY FROM (v_profile.subscription_current_period_end - v_now))
            ELSE NULL
          END
      )
    )
  );

  -- Verificar se tem premium ativo
  IF v_profile.subscription_tier = 'premium' THEN
    -- Se tem tier premium e data de expiração válida
    IF v_profile.subscription_current_period_end IS NOT NULL AND v_profile.subscription_current_period_end > v_now THEN
      RETURN QUERY
      SELECT 
        true as has_access,
        'assinatura'::VARCHAR as access_type,
        v_profile.subscription_current_period_end as expires_at,
        v_debug || jsonb_build_object(
          'result', 'ACTIVE_PREMIUM_WITH_VALID_DATE',
          'reason', 'User has premium tier with valid expiration date'
        ) as debug_info;
      RETURN;
    -- Se tem tier premium mas sem data ou expirada, ainda considerar ativo se status = active
    ELSIF v_profile.subscription_status = 'active' THEN
      RETURN QUERY
      SELECT 
        true as has_access,
        'assinatura'::VARCHAR as access_type,
        COALESCE(v_profile.subscription_current_period_end, v_now + INTERVAL '30 days') as expires_at,
        v_debug || jsonb_build_object(
          'result', 'ACTIVE_PREMIUM_NO_EXPIRY',
          'reason', 'User has premium tier with active status but no/expired date'
        ) as debug_info;
      RETURN;
    END IF;
  END IF;

  -- Verificar trial
  v_debug := v_debug || jsonb_build_object(
    'checks', v_debug->'checks' || jsonb_build_array(
      jsonb_build_object(
        'type', 'trial',
        'has_trial_date', v_profile.trial_end_date IS NOT NULL,
        'trial_end', v_profile.trial_end_date,
        'is_valid', v_profile.trial_end_date > v_now,
        'days_remaining', 
          CASE 
            WHEN v_profile.trial_end_date IS NOT NULL 
            THEN EXTRACT(DAY FROM (v_profile.trial_end_date - v_now))
            ELSE NULL
          END
      )
    )
  );

  IF v_profile.trial_end_date IS NOT NULL AND v_profile.trial_end_date > v_now THEN
    RETURN QUERY
    SELECT 
      true as has_access,
      'trial'::VARCHAR as access_type,
      v_profile.trial_end_date as expires_at,
      v_debug || jsonb_build_object(
        'result', 'ACTIVE_TRIAL',
        'reason', 'User has active trial'
      ) as debug_info;
    RETURN;
  END IF;

  -- Verificar na tabela user_access (cupons, etc)
  SELECT * INTO v_user_access 
  FROM user_access 
  WHERE user_id = p_user_id 
    AND ativo_ate > v_now
  ORDER BY ativo_ate DESC
  LIMIT 1;

  v_debug := v_debug || jsonb_build_object(
    'checks', v_debug->'checks' || jsonb_build_array(
      jsonb_build_object(
        'type', 'user_access_table',
        'found', v_user_access IS NOT NULL,
        'access_type', v_user_access.tipo,
        'expires_at', v_user_access.ativo_ate,
        'days_remaining', 
          CASE 
            WHEN v_user_access.ativo_ate IS NOT NULL 
            THEN EXTRACT(DAY FROM (v_user_access.ativo_ate - v_now))
            ELSE NULL
          END
      )
    )
  );

  IF v_user_access IS NOT NULL THEN
    RETURN QUERY
    SELECT 
      true as has_access,
      v_user_access.tipo::VARCHAR as access_type,
      v_user_access.ativo_ate as expires_at,
      v_debug || jsonb_build_object(
        'result', 'ACTIVE_USER_ACCESS',
        'reason', 'User has active access from user_access table',
        'access_source', v_user_access.tipo
      ) as debug_info;
    RETURN;
  END IF;

  -- Se não tem nenhum acesso
  RETURN QUERY SELECT 
    false as has_access,
    NULL::VARCHAR as access_type,
    NULL::TIMESTAMP WITH TIME ZONE as expires_at,
    v_debug || jsonb_build_object(
      'result', 'NO_ACCESS',
      'reason', 'No active subscription, trial, or user access found'
    ) as debug_info;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Criar versão simples sem debug para produção
CREATE OR REPLACE FUNCTION check_user_access_simple(p_user_id UUID)
RETURNS TABLE (
  has_access BOOLEAN,
  access_type VARCHAR,
  expires_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    (cua.debug_info->>'result' != 'NO_ACCESS')::BOOLEAN as has_access,
    cua.access_type,
    cua.expires_at
  FROM check_user_access(p_user_id) cua;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Teste com debug
-- SELECT * FROM check_user_access('590b3ae5-92f2-47d2-91a6-8c29c23dc9d4');

-- Teste simples
-- SELECT * FROM check_user_access_simple('590b3ae5-92f2-47d2-91a6-8c29c23dc9d4');