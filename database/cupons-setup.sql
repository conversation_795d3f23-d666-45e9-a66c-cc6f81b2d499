-- Sistema de Cupons para NewSpports
-- Este arquivo cria as tabelas necessárias para o sistema de cupons

-- Tabela de códigos de cupom
CREATE TABLE IF NOT EXISTS cupons (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  codigo VARCHAR(10) NOT NULL UNIQUE,
  user_id UUID REFERENCES auth.users(id) NOT NULL UNIQUE, -- Um cupom por usuário
  criado_em TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  usado BOOLEAN DEFAULT false,
  usado_em TIMESTAMP WITH TIME ZONE,
  expira_em TIMESTAMP WITH TIME ZONE -- Data que o acesso expira (30 dias após uso)
);

-- Tabela de controle de acesso
CREATE TABLE IF NOT EXISTS user_access (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) NOT NULL UNIQUE,
  tipo VARCHAR(20) NOT NULL, -- 'trial', 'cupom', 'assinatura'
  ativo_ate TIMESTAMP WITH TIME ZONE NOT NULL,
  criado_em TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  atualizado_em TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Índices para performance
CREATE INDEX IF NOT EXISTS idx_cupons_codigo ON cupons(codigo);
CREATE INDEX IF NOT EXISTS idx_cupons_user ON cupons(user_id);
CREATE INDEX IF NOT EXISTS idx_user_access_user ON user_access(user_id);
CREATE INDEX IF NOT EXISTS idx_user_access_ativo ON user_access(ativo_ate);

-- Enable RLS
ALTER TABLE cupons ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_access ENABLE ROW LEVEL SECURITY;

-- Políticas RLS para cupons
CREATE POLICY "Usuários veem próprios cupons" ON cupons
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Sistema pode criar cupons" ON cupons
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Sistema pode atualizar cupons" ON cupons
  FOR UPDATE USING (auth.uid() = user_id);

-- Políticas RLS para user_access
CREATE POLICY "Usuários veem próprio acesso" ON user_access
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Sistema pode criar acesso" ON user_access
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Sistema pode atualizar acesso" ON user_access
  FOR UPDATE USING (auth.uid() = user_id);

-- Função para verificar acesso ativo
CREATE OR REPLACE FUNCTION check_user_access(p_user_id UUID)
RETURNS TABLE (
  has_access BOOLEAN,
  access_type VARCHAR,
  expires_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    CASE WHEN ua.ativo_ate > NOW() THEN true ELSE false END as has_access,
    ua.tipo as access_type,
    ua.ativo_ate as expires_at
  FROM user_access ua
  WHERE ua.user_id = p_user_id
  ORDER BY ua.ativo_ate DESC
  LIMIT 1;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Função para aplicar cupom
CREATE OR REPLACE FUNCTION apply_coupon(p_user_id UUID, p_codigo VARCHAR)
RETURNS TABLE (
  success BOOLEAN,
  message TEXT,
  expires_at TIMESTAMP WITH TIME ZONE
) AS $$
DECLARE
  v_cupom_id UUID;
  v_expira_em TIMESTAMP WITH TIME ZONE;
BEGIN
  -- Verificar se cupom existe e pertence ao usuário
  SELECT id INTO v_cupom_id
  FROM cupons
  WHERE codigo = p_codigo 
    AND user_id = p_user_id 
    AND usado = false;
  
  IF v_cupom_id IS NULL THEN
    RETURN QUERY SELECT false, 'Cupom inválido ou já utilizado', NULL::TIMESTAMP WITH TIME ZONE;
    RETURN;
  END IF;
  
  -- Calcular data de expiração (30 dias)
  v_expira_em := NOW() + INTERVAL '30 days';
  
  -- Marcar cupom como usado
  UPDATE cupons
  SET 
    usado = true,
    usado_em = NOW(),
    expira_em = v_expira_em
  WHERE id = v_cupom_id;
  
  -- Criar ou atualizar acesso do usuário
  INSERT INTO user_access (user_id, tipo, ativo_ate)
  VALUES (p_user_id, 'cupom', v_expira_em)
  ON CONFLICT (user_id) 
  DO UPDATE SET 
    tipo = 'cupom',
    ativo_ate = v_expira_em,
    atualizado_em = NOW();
  
  RETURN QUERY SELECT true, 'Cupom aplicado com sucesso!', v_expira_em;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger para atualizar timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.atualizado_em = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_user_access_updated_at 
  BEFORE UPDATE ON user_access
  FOR EACH ROW 
  EXECUTE FUNCTION update_updated_at_column();