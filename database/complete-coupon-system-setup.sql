-- <PERSON><PERSON>t completo para setup do sistema de cupons e trial
-- Execute este arquivo no Supabase SQL Editor

-- 1. TABELA DE CÓDIGOS DE CUPOM
CREATE TABLE IF NOT EXISTS cupons (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  codigo VARCHAR(10) NOT NULL UNIQUE,
  user_id UUID REFERENCES auth.users(id) NOT NULL UNIQUE, -- Um cupom por usuário
  criado_em TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  usado BOOLEAN DEFAULT false,
  usado_em TIMESTAMP WITH TIME ZONE,
  expira_em TIMESTAMP WITH TIME ZONE -- Data que o acesso expira (30 dias após uso)
);

-- 2. TABELA DE CONTROLE DE ACESSO
CREATE TABLE IF NOT EXISTS user_access (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) NOT NULL UNIQUE,
  tipo VARCHAR(20) NOT NULL, -- 'trial', 'cupom', 'assinatura'
  ativo_ate TIMESTAMP WITH TIME ZONE NOT NULL,
  criado_em TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  atualizado_em TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. TABELA PARA RASTREAR SESSÕES DE TRIAL
CREATE TABLE IF NOT EXISTS trial_sessions (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) UNIQUE,
  session_id VARCHAR(100) UNIQUE, -- Para usuários não logados
  started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
  time_remaining INTEGER DEFAULT 300, -- 5 minutos em segundos
  is_expired BOOLEAN DEFAULT false,
  device_info JSONB, -- Armazenar info do dispositivo
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. CRIAR ÍNDICES PARA PERFORMANCE
CREATE INDEX IF NOT EXISTS idx_cupons_codigo ON cupons(codigo);
CREATE INDEX IF NOT EXISTS idx_cupons_user ON cupons(user_id);
CREATE INDEX IF NOT EXISTS idx_user_access_user ON user_access(user_id);
CREATE INDEX IF NOT EXISTS idx_user_access_ativo ON user_access(ativo_ate);
CREATE INDEX IF NOT EXISTS idx_trial_sessions_user ON trial_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_trial_sessions_session ON trial_sessions(session_id);
CREATE INDEX IF NOT EXISTS idx_trial_sessions_expired ON trial_sessions(is_expired, expires_at);

-- 5. HABILITAR RLS (Row Level Security)
ALTER TABLE cupons ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_access ENABLE ROW LEVEL SECURITY;
ALTER TABLE trial_sessions ENABLE ROW LEVEL SECURITY;

-- 6. POLÍTICAS RLS PARA CUPONS
DROP POLICY IF EXISTS "Usuários veem próprios cupons" ON cupons;
CREATE POLICY "Usuários veem próprios cupons" ON cupons
  FOR SELECT USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Sistema pode criar cupons" ON cupons;
CREATE POLICY "Sistema pode criar cupons" ON cupons
  FOR INSERT WITH CHECK (auth.uid() = user_id);

DROP POLICY IF EXISTS "Sistema pode atualizar cupons" ON cupons;
CREATE POLICY "Sistema pode atualizar cupons" ON cupons
  FOR UPDATE USING (auth.uid() = user_id);

-- 7. POLÍTICAS RLS PARA USER_ACCESS
DROP POLICY IF EXISTS "Usuários veem próprio acesso" ON user_access;
CREATE POLICY "Usuários veem próprio acesso" ON user_access
  FOR SELECT USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Sistema pode criar acesso" ON user_access;
CREATE POLICY "Sistema pode criar acesso" ON user_access
  FOR INSERT WITH CHECK (auth.uid() = user_id);

DROP POLICY IF EXISTS "Sistema pode atualizar acesso" ON user_access;
CREATE POLICY "Sistema pode atualizar acesso" ON user_access
  FOR UPDATE USING (auth.uid() = user_id);

-- 8. POLÍTICAS RLS PARA TRIAL_SESSIONS
DROP POLICY IF EXISTS "Usuários veem próprias sessões" ON trial_sessions;
CREATE POLICY "Usuários veem próprias sessões" ON trial_sessions
  FOR SELECT USING (auth.uid() = user_id OR session_id IS NOT NULL);

DROP POLICY IF EXISTS "Sistema pode criar sessões" ON trial_sessions;
CREATE POLICY "Sistema pode criar sessões" ON trial_sessions
  FOR INSERT WITH CHECK (true);

DROP POLICY IF EXISTS "Sistema pode atualizar sessões" ON trial_sessions;
CREATE POLICY "Sistema pode atualizar sessões" ON trial_sessions
  FOR UPDATE USING (true);

-- 9. FUNÇÃO PARA VERIFICAR ACESSO ATIVO
DROP FUNCTION IF EXISTS check_user_access(UUID);
CREATE OR REPLACE FUNCTION check_user_access(p_user_id UUID)
RETURNS TABLE (
  has_access BOOLEAN,
  access_type VARCHAR,
  expires_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    CASE WHEN ua.ativo_ate > NOW() THEN true ELSE false END as has_access,
    ua.tipo as access_type,
    ua.ativo_ate as expires_at
  FROM user_access ua
  WHERE ua.user_id = p_user_id
  ORDER BY ua.ativo_ate DESC
  LIMIT 1;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 10. FUNÇÃO PARA APLICAR CUPOM
DROP FUNCTION IF EXISTS apply_coupon(UUID, VARCHAR);
CREATE OR REPLACE FUNCTION apply_coupon(p_user_id UUID, p_codigo VARCHAR)
RETURNS TABLE (
  success BOOLEAN,
  message TEXT,
  expires_at TIMESTAMP WITH TIME ZONE
) AS $$
DECLARE
  v_cupom_id UUID;
  v_expira_em TIMESTAMP WITH TIME ZONE;
BEGIN
  -- Verificar se cupom existe e pertence ao usuário
  SELECT id INTO v_cupom_id
  FROM cupons
  WHERE codigo = p_codigo 
    AND user_id = p_user_id 
    AND usado = false;
  
  IF v_cupom_id IS NULL THEN
    RETURN QUERY SELECT false, 'Cupom inválido ou já utilizado', NULL::TIMESTAMP WITH TIME ZONE;
    RETURN;
  END IF;
  
  -- Calcular data de expiração (30 dias)
  v_expira_em := NOW() + INTERVAL '30 days';
  
  -- Marcar cupom como usado
  UPDATE cupons
  SET 
    usado = true,
    usado_em = NOW(),
    expira_em = v_expira_em
  WHERE id = v_cupom_id;
  
  -- Criar ou atualizar acesso do usuário
  INSERT INTO user_access (user_id, tipo, ativo_ate)
  VALUES (p_user_id, 'cupom', v_expira_em)
  ON CONFLICT (user_id) 
  DO UPDATE SET 
    tipo = 'cupom',
    ativo_ate = v_expira_em,
    atualizado_em = NOW();
  
  RETURN QUERY SELECT true, 'Cupom aplicado com sucesso!', v_expira_em;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 11. FUNÇÃO PARA VERIFICAR OU CRIAR SESSÃO DE TRIAL
DROP FUNCTION IF EXISTS get_or_create_trial_session(UUID, VARCHAR, JSONB);
CREATE OR REPLACE FUNCTION get_or_create_trial_session(
  p_user_id UUID DEFAULT NULL,
  p_session_id VARCHAR DEFAULT NULL,
  p_device_info JSONB DEFAULT NULL
)
RETURNS TABLE (
  trial_id UUID,
  time_remaining INTEGER,
  is_expired BOOLEAN,
  expires_at TIMESTAMP WITH TIME ZONE
) AS $$
DECLARE
  v_existing_trial RECORD;
  v_new_expires_at TIMESTAMP WITH TIME ZONE;
BEGIN
  -- Buscar trial existente
  IF p_user_id IS NOT NULL THEN
    SELECT * INTO v_existing_trial
    FROM trial_sessions ts
    WHERE ts.user_id = p_user_id
    LIMIT 1;
  ELSIF p_session_id IS NOT NULL THEN
    SELECT * INTO v_existing_trial
    FROM trial_sessions ts
    WHERE ts.session_id = p_session_id
    LIMIT 1;
  END IF;
  
  -- Se encontrou trial existente
  IF v_existing_trial.id IS NOT NULL THEN
    -- Verificar se expirou
    IF v_existing_trial.expires_at < NOW() OR v_existing_trial.is_expired THEN
      -- Marcar como expirado
      UPDATE trial_sessions
      SET is_expired = true, time_remaining = 0
      WHERE id = v_existing_trial.id;
      
      RETURN QUERY SELECT 
        v_existing_trial.id,
        0::INTEGER,
        true,
        v_existing_trial.expires_at;
    ELSE
      -- Calcular tempo restante
      RETURN QUERY SELECT 
        v_existing_trial.id,
        GREATEST(0, EXTRACT(EPOCH FROM (v_existing_trial.expires_at - NOW())))::INTEGER,
        false,
        v_existing_trial.expires_at;
    END IF;
  ELSE
    -- Criar nova sessão de trial
    v_new_expires_at := NOW() + INTERVAL '5 minutes';
    
    INSERT INTO trial_sessions (user_id, session_id, expires_at, device_info)
    VALUES (p_user_id, p_session_id, v_new_expires_at, p_device_info)
    RETURNING id INTO v_existing_trial;
    
    RETURN QUERY SELECT 
      v_existing_trial.id,
      300::INTEGER, -- 5 minutos
      false,
      v_new_expires_at;
  END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 12. FUNÇÃO PARA MARCAR TRIAL COMO EXPIRADO
DROP FUNCTION IF EXISTS expire_trial_session(UUID, VARCHAR);
CREATE OR REPLACE FUNCTION expire_trial_session(
  p_user_id UUID DEFAULT NULL,
  p_session_id VARCHAR DEFAULT NULL
)
RETURNS BOOLEAN AS $$
BEGIN
  IF p_user_id IS NOT NULL THEN
    UPDATE trial_sessions
    SET is_expired = true, time_remaining = 0
    WHERE user_id = p_user_id;
  ELSIF p_session_id IS NOT NULL THEN
    UPDATE trial_sessions
    SET is_expired = true, time_remaining = 0
    WHERE session_id = p_session_id;
  END IF;
  
  RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 13. FUNÇÃO PARA LIMPAR TRIALS EXPIRADOS (pode ser usada em cron job)
DROP FUNCTION IF EXISTS cleanup_expired_trials();
CREATE OR REPLACE FUNCTION cleanup_expired_trials()
RETURNS INTEGER AS $$
DECLARE
  v_deleted_count INTEGER;
BEGIN
  DELETE FROM trial_sessions
  WHERE expires_at < NOW() - INTERVAL '7 days'
  OR (is_expired = true AND created_at < NOW() - INTERVAL '7 days');
  
  GET DIAGNOSTICS v_deleted_count = ROW_COUNT;
  RETURN v_deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 14. TRIGGER PARA ATUALIZAR TIMESTAMP
DROP FUNCTION IF EXISTS update_updated_at_column() CASCADE;
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.atualizado_em = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS update_user_access_updated_at ON user_access;
CREATE TRIGGER update_user_access_updated_at 
  BEFORE UPDATE ON user_access
  FOR EACH ROW 
  EXECUTE FUNCTION update_updated_at_column();

-- 15. GRANT PERMISSIONS (opcional, se necessário)
-- GRANT ALL ON cupons TO authenticated;
-- GRANT ALL ON user_access TO authenticated;
-- GRANT ALL ON trial_sessions TO authenticated;

-- Fim do script de setup
-- Execute este script no Supabase SQL Editor para criar todo o sistema de cupons e trial