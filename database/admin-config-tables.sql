-- Execute este script no SQL Editor do Supabase

-- Create admin_config table
CREATE TABLE IF NOT EXISTS admin_config (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  config_key TEXT UNIQUE NOT NULL,
  config_value TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create download_stats table
CREATE TABLE IF NOT EXISTS download_stats (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  download_type TEXT NOT NULL CHECK (download_type IN ('apk', 'qrcode')),
  ip_address TEXT,
  user_agent TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert default config values
INSERT INTO admin_config (config_key, config_value) VALUES
  ('apk_url', ''),
  ('qrcode_url', '')
ON CONFLICT (config_key) DO NOTHING;

-- Enable RLS
ALTER TABLE admin_config ENABLE ROW LEVEL SECURITY;
ALTER TABLE download_stats ENABLE ROW LEVEL SECURITY;

-- Admin config policies (only authenticated admins can access)
CREATE POLICY "Admins can view admin_config" ON admin_config
  FOR SELECT USING (auth.uid() IS NOT NULL);

CREATE POLICY "Admins can update admin_config" ON admin_config
  FOR UPDATE USING (auth.uid() IS NOT NULL);

-- Download stats policies
CREATE POLICY "Anyone can insert download_stats" ON download_stats
  FOR INSERT WITH CHECK (true);

CREATE POLICY "Admins can view download_stats" ON download_stats
  FOR SELECT USING (auth.uid() IS NOT NULL);

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to update updated_at
CREATE TRIGGER update_admin_config_updated_at
  BEFORE UPDATE ON admin_config
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();