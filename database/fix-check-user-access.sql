-- Atualizar função check_user_access para verificar também a tabela profiles
-- Execute este script no SQL Editor do Supabase

DROP FUNCTION IF EXISTS check_user_access(UUID);

CREATE OR REPLACE FUNCTION check_user_access(p_user_id UUID)
RETURNS TABLE (
  has_access BOOLEAN,
  access_type VARCHAR,
  expires_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
  -- Primeiro verificar na tabela profiles (assinaturas premium)
  IF EXISTS (
    SELECT 1 FROM profiles 
    WHERE id = p_user_id 
    AND subscription_tier = 'premium'
    AND subscription_status = 'active'
    AND subscription_current_period_end > NOW()
  ) THEN
    RETURN QUERY
    SELECT 
      true as has_access,
      'assinatura'::VARCHAR as access_type,
      subscription_current_period_end as expires_at
    FROM profiles
    WHERE id = p_user_id;
    RETURN;
  END IF;

  -- Se não tem premium, verificar na tabela user_access (cupons, etc)
  IF EXISTS (
    SELECT 1 FROM user_access 
    WHERE user_id = p_user_id 
    AND ativo_ate > NOW()
  ) THEN
    RETURN QUERY
    SELECT 
      true as has_access,
      tipo::VARCHAR as access_type,
      ativo_ate as expires_at
    FROM user_access
    WHERE user_id = p_user_id
    ORDER BY ativo_ate DESC
    LIMIT 1;
    RETURN;
  END IF;

  -- Se não tem nenhum acesso
  RETURN QUERY SELECT 
    false as has_access,
    NULL::VARCHAR as access_type,
    NULL::TIMESTAMP WITH TIME ZONE as expires_at;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Testar a função
SELECT * FROM check_user_access('590b3ae5-92f2-47d2-91a6-8c29c23dc9d4');