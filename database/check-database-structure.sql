-- Script para verificar estrutura do banco de dados
-- Execute este script no SQL Editor do Supabase para diagnosticar problemas

-- 1. Verificar se as colunas do Stripe existem na tabela profiles
SELECT 
  column_name, 
  data_type, 
  is_nullable,
  column_default
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name = 'profiles'
ORDER BY ordinal_position;

-- 2. Verificar quantos usuários existem
SELECT COUNT(*) as total_users FROM auth.users;

-- 3. Verificar quantos perfis existem
SELECT COUNT(*) as total_profiles FROM profiles;

-- 4. Buscar usuários sem perfil
SELECT u.id, u.email, u.created_at
FROM auth.users u
LEFT JOIN profiles p ON u.id = p.id
WHERE p.id IS NULL
ORDER BY u.created_at DESC;

-- 5. Verificar status das assinaturas ativas
SELECT 
  subscription_tier,
  subscription_status,
  COUNT(*) as count
FROM profiles
GROUP BY subscription_tier, subscription_status
ORDER BY count DESC;

-- 6. Verificar políticas RLS na tabela profiles
SELECT 
  schemaname,
  tablename,
  policyname,
  permissive,
  roles,
  cmd,
  qual,
  with_check
FROM pg_policies
WHERE tablename = 'profiles';

-- 7. Verificar se as colunas necessárias existem
SELECT 
  EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'stripe_customer_id') as has_stripe_customer_id,
  EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'stripe_subscription_id') as has_stripe_subscription_id,
  EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'subscription_status') as has_subscription_status,
  EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'subscription_current_period_end') as has_subscription_current_period_end,
  EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'subscription_tier') as has_subscription_tier;

-- 8. Verificar últimas atualizações em profiles
SELECT 
  id,
  email,
  subscription_tier,
  subscription_status,
  updated_at
FROM profiles
ORDER BY updated_at DESC
LIMIT 10;