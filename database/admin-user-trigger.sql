-- Execute este script no SQL Editor do Supabase

-- Function to update admin_users.user_id when a user registers
CREATE OR REPLACE FUNCTION update_admin_user_id()
RETURNS TRIGGER AS $$
BEGIN
  -- Update admin_users table if email matches
  UPDATE admin_users 
  SET user_id = NEW.id 
  WHERE email = NEW.email 
  AND user_id IS NULL;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to call the function on user creation
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW
  EXECUTE FUNCTION update_admin_user_id();

-- Also update existing admin user for the main admin email
UPDATE admin_users 
SET user_id = (SELECT id FROM auth.users WHERE email = '<EMAIL>' LIMIT 1)
WHERE email = '<EMAIL>' 
AND user_id IS NULL;