# 🔍 ONDE ENCONTRAR O REDIRECT URI CORRETO

## ❌ NÃO É ISSO:
```
JWKS URL: https://api.stack-auth.com/api/v1/projects/b5d6eebd-aa16-4101-9cad-8717743c9343/.well-known/jwks.json
```
**JWKS** = JSON Web Key Set (usado para verificar tokens, não para login)

## ✅ O QUE VOCÊ PRECISA ENCONTRAR:

### 📍 LOCALIZAÇÃO EXATA NO STACK AUTH:

1. **Entre no Stack Auth Dashboard**
   https://app.stack-auth.com

2. **No menu lateral, clique em:**
   ```
   Authentication
   └── OAuth Providers
   ```

3. **Você verá uma lista de providers:**
   ```
   Google     [Enabled]    Configure →
   GitHub     [Disabled]   Configure →
   Facebook   [Disabled]   Configure →
   ```

4. **Clique em "Configure" ao lado de "Google"**

5. **Na tela que abrir, procure por:**
   ```
   OAuth Configuration
   ─────────────────────
   
   Redirect URI:
   [Uma caixa com o URI completo para copiar]
   
   ⚠️ Copy this redirect URI and add it to your OAuth provider
   ```

## 🎯 O REDIRECT URI SERÁ ALGO ASSIM:

```
https://api.stack-auth.com/api/v1/auth/oauth/callback/google?after_callback_redirect_to=https%3A%2F%2Fnewspports-production.up.railway.app%2Fhandler%2Foauth-callback
```

## 📸 VISUAL DO QUE PROCURAR:

```
┌─────────────────────────────────────────────┐
│ Google OAuth Provider                        │
├─────────────────────────────────────────────┤
│                                             │
│ Status: ● Enabled ○ Disabled                │
│                                             │
│ Client ID:                                  │
│ [seu-google-client-id]                      │
│                                             │
│ Client Secret:                              │
│ [seu-google-client-secret]                  │
│                                             │
│ ⚠️ Redirect URI (Copy this):               │
│ ┌─────────────────────────────────────────┐ │
│ │https://api.stack-auth.com/api/v1/auth...│ │
│ │[BOTÃO COPY]                             │ │
│ └─────────────────────────────────────────┘ │
│                                             │
│ [Save Changes]                              │
└─────────────────────────────────────────────┘
```

## 🚨 SE NÃO ENCONTRAR:

### Alternativa 1 - Pode estar em:
```
Settings
└── URLs
    └── OAuth Redirect URL
```

### Alternativa 2 - Construir manualmente:
Se o seu project ID é `b5d6eebd-aa16-4101-9cad-8717743c9343`, o redirect URI seria:
```
https://api.stack-auth.com/api/v1/auth/oauth/callback/google?after_callback_redirect_to=https%3A%2F%2Fnewspports-production.up.railway.app%2Fhandler%2Foauth-callback
```

## 📋 PASSOS RESUMIDOS:

1. **Stack Auth Dashboard**
2. **Authentication → OAuth Providers**
3. **Google → Configure**
4. **Copie o "Redirect URI"**
5. **Cole no Google Cloud Console**

## 💡 DICA:

Se você compartilhar uma screenshot da tela do Google OAuth Provider no Stack Auth (escondendo as secrets), posso te mostrar exatamente onde está!