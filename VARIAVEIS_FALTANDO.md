# Variáveis de Ambiente Faltando no Railway

## Variável Essencial Faltando:

```bash
PORT=3000
```

O Railway precisa dessa variável para saber em qual porta executar a aplicação.

## Variáveis que Você Já Te<PERSON> (✅):

### Supabase
- ✅ NEXT_PUBLIC_SUPABASE_URL
- ✅ NEXT_PUBLIC_SUPABASE_ANON_KEY
- ✅ SUPABASE_SERVICE_ROLE_KEY

### URLs da Aplicação
- ✅ NEXT_PUBLIC_APP_URL
- ✅ NEXT_PUBLIC_ANDROID_APP_URL
- ✅ NEXT_PUBLIC_IOS_APP_URL

### Streaming
- ✅ NEXT_PUBLIC_STREAM_URL
- ✅ NEXT_PUBLIC_FALLBACK_STREAM_URL

### Autenticação
- ✅ JWT_SECRET
- ✅ NEXT_PUBLIC_STACK_PROJECT_ID
- ✅ NEXT_PUBLIC_STACK_PUBLISHABLE_CLIENT_KEY
- ✅ STACK_SECRET_SERVER_KEY

### APIs Esportivas
- ✅ SPORTS_API_KEY
- ✅ ALLSPORTDB_API_KEY
- ✅ ALLSPORT_DB_KEY (duplicado, pode remover um)

### Ambiente
- ✅ NODE_ENV

## Observações:

1. **ALLSPORTDB_API_KEY** e **ALLSPORT_DB_KEY** parecem ser duplicados. Você pode manter apenas um.

2. **NEXT_PUBLIC_APP_URL** deve apontar para o domínio do Railway:
   ```
   NEXT_PUBLIC_APP_URL=https://seu-app.up.railway.app
   ```

3. **NODE_ENV** deve estar como `production` no Railway.

## Adicionar no Railway:

```bash
PORT=3000
```

Isso deve resolver os problemas de deploy!