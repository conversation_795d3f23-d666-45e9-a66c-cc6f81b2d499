# 🔍 ANÁLISE DOS LOGS DO SISTEMA DE TRIAL - IPTV

## 📊 Resumo Executivo

**PROBLEMA IDENTIFICADO**: O sistema de trial não está bloqueando usuários após 5 minutos porque o trial não está sendo iniciado corretamente quando um usuário anônimo clica em um canal.

## 🚨 Problemas Críticos Identificados

### 1. **Trial Não Inicia Automaticamente** ❌
- **Sintoma**: Quando usuário clica em um canal, o trial não é iniciado
- **Causa**: A lógica em `checkTrialStatus` (linha 67 do trial.store.ts) retorna sem iniciar trial se não há `startTime`
- **Código problemático**:
```typescript
// trial.store.ts linha 67
if (!get().startTime) {
  console.log('[TRIAL-STORE] New user detected (no startTime), waiting for access check')
  set((state) => {
    state.isLoading = false
  })
  return; // ❌ RETORNA SEM INICIAR TRIAL!
}
```

### 2. **Condição de Início do Trial Muito Restritiva** ❌
- **Sintoma**: `startTrial` só é chamado em condições muito específicas
- **Causa**: No video-player.tsx (linha 155), a condição para iniciar trial é:
```typescript
if (!currentState.startTime || (!currentState.isExpired && currentState.timeRemaining === 300)) {
  // Inicia trial
}
```
- **Problema**: `timeRemaining === 300` é muito específico e pode não ser verdadeiro

### 3. **Estado Inicial Não Garante Trial** ❌
- **Sintoma**: Novos usuários começam com `isExpired: false` mas sem `startTime`
- **Causa**: Estado inicial permite usuário assistir indefinidamente sem trial
- **Estado problemático**:
```typescript
// Estado inicial
{
  startTime: null,      // Sem tempo de início
  timeRemaining: 300,   // 5 minutos
  isExpired: false,     // Não expirado
  // Resultado: usuário pode assistir sem bloqueio!
}
```

### 4. **Lógica de Bloqueio Falha** ❌
- **Sintoma**: Bloqueio só ocorre se `isExpired === true`
- **Causa**: Em video-player.tsx (linha 211):
```typescript
const shouldBlock = currentTrialState.isExpired && !currentAccessState.hasAccess
```
- **Problema**: Se trial não inicia, `isExpired` permanece `false` para sempre

## 🔄 Fluxo Atual (Quebrado)

```mermaid
graph TD
    A[Usuário clica no canal] --> B[VideoPlayer monta]
    B --> C[checkAccessAndTrial]
    C --> D[checkAccess - verifica premium]
    D --> E{Tem acesso premium?}
    E -->|Sim| F[Libera vídeo]
    E -->|Não| G[checkTrialStatus]
    G --> H{Tem startTime?}
    H -->|Não| I[❌ RETORNA SEM INICIAR TRIAL]
    I --> J[isExpired = false]
    J --> K[❌ VÍDEO LIBERADO INDEFINIDAMENTE]
```

## 🛠️ Correções Necessárias

### 1. **Corrigir `checkTrialStatus` no trial.store.ts**
```typescript
checkTrialStatus: async (userId?: string) => {
  console.log('[TRIAL-STORE] checkTrialStatus called', { userId })
  
  // NÃO retornar se não tem startTime - isso é esperado para novos usuários
  const currentStartTime = get().startTime
  
  if (!currentStartTime) {
    console.log('[TRIAL-STORE] No startTime - new user, will check/create trial')
    // Continuar execução ao invés de retornar
  }
  
  // ... resto do código
}
```

### 2. **Simplificar Condição de Início do Trial**
```typescript
// video-player.tsx
if (!currentState.startTime) {
  console.log('🎬 [TRIAL-PLAYER] Starting NEW TRIAL - no startTime')
  await startTrial(user?.id)
} else if (currentState.isExpired && shouldResetTrial(currentState.startTime)) {
  console.log('🎬 [TRIAL-PLAYER] Resetting expired trial')
  await startTrial(user?.id)
}
```

### 3. **Garantir que Trial Sempre Inicie**
```typescript
// video-player.tsx - após verificar acesso
if (!currentAccessState.hasAccess) {
  // SEMPRE iniciar trial se não tem acesso premium
  const currentState = useTrialStore.getState()
  
  if (!currentState.startTime || currentState.startTime === 0) {
    console.log('🚀 [TRIAL-PLAYER] No premium access, starting trial NOW')
    await startTrial(user?.id)
  }
}
```

### 4. **Adicionar Verificação de Segurança**
```typescript
// video-player.tsx - na lógica de bloqueio
useEffect(() => {
  // Verificação de segurança: se não tem startTime e não tem acesso, bloquear
  const shouldBlockNoTrial = !currentTrialState.startTime && !currentAccessState.hasAccess && !isCheckingAccess
  const shouldBlockExpired = currentTrialState.isExpired && !currentAccessState.hasAccess
  
  const shouldBlock = shouldBlockNoTrial || shouldBlockExpired
  
  if (shouldBlock !== isBlocked) {
    console.log('🚨 [TRIAL-PLAYER] Block status changing:', {
      from: isBlocked,
      to: shouldBlock,
      reason: shouldBlockNoTrial ? 'No trial started' : 'Trial expired'
    })
  }
  
  setIsBlocked(shouldBlock)
}, [currentTrialState, currentAccessState, isCheckingAccess])
```

## 📋 Logs Esperados (Após Correção)

```
[VIDEO-PLAYER] Component rendered
[TRIAL-PLAYER] CHECK ACCESS AND TRIAL START
[ACCESS-STORE] CHECK ACCESS CALLED
[ACCESS-STORE] hasAccess: false
[TRIAL-PLAYER] No premium access, checking trial status
[TRIAL-STORE] checkTrialStatus called
[TRIAL-STORE] No startTime - new user, will check/create trial
[TRIAL-PLAYER] Starting NEW TRIAL - no startTime
[TRIAL-STORE] startTrial called
[TRIAL-STORE] Trial started: startTime: 1234567890
[TRIAL-TIMER] Timer update: 299 seconds
[TRIAL-TIMER] Timer update: 298 seconds
... (após 5 minutos) ...
[TRIAL-TIMER] Timer update: 1 seconds
[TRIAL-TIMER] Timer reached 0, calling expireTrial()
[TRIAL-STORE] TRIAL EXPIRED!
[TRIAL-PLAYER] BLOCK DECISION: shouldBlock: true
[TRIAL-PLAYER] BLOCKING USER!
```

## 🧪 Como Testar

1. **Limpar todos os dados** (localStorage, cookies):
   ```javascript
   localStorage.clear()
   sessionStorage.clear()
   ```

2. **Abrir aba anônima**

3. **Adicionar logs temporários** no início do video-player.tsx:
   ```typescript
   console.log('🎯 [VIDEO-PLAYER] === MOUNTED ===', {
     timestamp: new Date().toISOString(),
     url: window.location.href,
     channelName
   })
   ```

4. **Clicar em um canal e observar**:
   - Trial deve iniciar imediatamente
   - Timer deve aparecer contando de 5:00
   - Após 5 minutos, overlay de bloqueio deve aparecer

## 🎯 Resultado Esperado

1. **Usuário anônimo clica em canal** → Trial inicia automaticamente
2. **Timer conta regressivamente** → 5:00, 4:59, 4:58...
3. **Após 5 minutos** → Tela de bloqueio aparece
4. **Usuário com premium** → Sem timer, sem bloqueio

## 📝 Arquivos a Modificar

1. `/src/stores/trial.store.ts` - Linha 67 (remover return precoce)
2. `/src/components/player/video-player.tsx` - Linha 155 (simplificar condição)
3. `/src/components/player/video-player.tsx` - Linha 211 (adicionar verificação de segurança)

## 🚀 Próximos Passos

1. Implementar as correções acima
2. Testar em ambiente local com localStorage limpo
3. Verificar logs usando o script `analyze-trial-logs.js`
4. Confirmar que bloqueio ocorre após 5 minutos
5. Testar com usuários premium para garantir que não são afetados