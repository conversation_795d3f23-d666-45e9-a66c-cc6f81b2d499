<!DOCTYPE html>
<html>
<head>
    <title>Test Video Upload</title>
</head>
<body>
    <h1>Test Video Upload</h1>
    <form id="uploadForm" enctype="multipart/form-data">
        <input type="file" id="fileInput" accept="video/*" required>
        <button type="submit">Upload</button>
    </form>
    <div id="result"></div>

    <script>
        document.getElementById('uploadForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const fileInput = document.getElementById('fileInput');
            const file = fileInput.files[0];
            
            if (!file) {
                alert('Please select a file');
                return;
            }
            
            const formData = new FormData();
            formData.append('file', file);
            
            try {
                const response = await fetch('/api/admin/upload-video', {
                    method: 'POST',
                    body: formData,
                    credentials: 'include'
                });
                
                const result = await response.json();
                document.getElementById('result').innerHTML = `<pre>${JSON.stringify(result, null, 2)}</pre>`;
                
                if (response.ok) {
                    alert('Upload successful!');
                } else {
                    alert('Upload failed: ' + result.error);
                }
            } catch (error) {
                alert('Error: ' + error.message);
            }
        });
    </script>
</body>
</html>