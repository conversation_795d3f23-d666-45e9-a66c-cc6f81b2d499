// Execute isso no console para verificar o player

// 1. Ver o que o player carregou
console.log('🎥 ESTADO DO PLAYER:');
const playerLogs = window.__logger.getHistory('player').slice(-5);
playerLogs.forEach(log => {
    if (log.data?.streamUrl || log.data?.playbackUrl) {
        console.log('URL carregada:', log.data.streamUrl || log.data.playbackUrl);
    }
});

// 2. Verificar elemento de vídeo
const video = document.querySelector('video');
if (video) {
    console.log('\n📺 ELEMENTO DE VÍDEO:');
    console.log('- src:', video.src || 'Vazio');
    console.log('- currentSrc:', video.currentSrc || 'Vazio');
    console.log('- readyState:', video.readyState, '(4 = pronto)');
    console.log('- paused:', video.paused);
    console.log('- error:', video.error);
    
    // 3. Tentar tocar
    if (video.paused) {
        console.log('\n▶️ Tentando tocar...');
        video.play().then(() => {
            console.log('✅ Vídeo tocando!');
        }).catch(err => {
            console.log('❌ Erro ao tocar:', err.message);
        });
    }
}

// 4. Clicar no botão de play se existir
const playButton = document.querySelector('button svg.fill-white')?.closest('button');
if (playButton) {
    console.log('\n🎮 Botão de play encontrado - clicando...');
    playButton.click();
    
    // Verificar logs após clicar
    setTimeout(() => {
        const newLogs = window.__logger.getHistory('player').slice(-3);
        console.log('\nNovos logs após clicar:');
        newLogs.forEach(log => console.log(`[${log.level}] ${log.message}`));
    }, 1000);
}