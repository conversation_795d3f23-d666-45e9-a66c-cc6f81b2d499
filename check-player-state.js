// Execute este código no console do browser quando estiver na página do player

console.log('=== VERIFICANDO ESTADO DO PLAYER ===');

// 1. Ver logs do player
const playerLogs = window.__logger.getHistory('player');
console.log('\n📺 LOGS DO PLAYER:');
playerLogs.slice(-10).forEach(log => {
    console.log(`[${log.level}] ${log.message}`, log.data || '');
});

// 2. Verificar se o HLS está sendo configurado
const hlsLogs = playerLogs.filter(log => 
    log.message.includes('HLS') || 
    log.message.includes('Setting up') ||
    log.message.includes('Loading')
);
console.log('\n🎬 HLS LOGS:');
hlsLogs.forEach(log => {
    console.log(log.message, log.data || '');
});

// 3. Verificar URLs
const mountLogs = playerLogs.filter(log => log.message.includes('mounted'));
console.log('\n🔗 URLs NO PLAYER:');
mountLogs.forEach(log => {
    if (log.data?.streamUrl) {
        console.log('Stream URL:', log.data.streamUrl);
        console.log('É .m3u8?', log.data.streamUrl.includes('.m3u8'));
    }
});

// 4. Verificar elementos de vídeo na página
const videos = document.querySelectorAll('video');
console.log('\n🎥 ELEMENTOS DE VÍDEO:', videos.length);
videos.forEach((video, i) => {
    console.log(`Video ${i + 1}:`, {
        src: video.src,
        currentSrc: video.currentSrc,
        readyState: video.readyState,
        paused: video.paused,
        error: video.error
    });
});

// 5. Verificar se React Player está na página
const reactPlayerDiv = document.querySelector('[data-react-player]');
console.log('\n⚛️ React Player encontrado?', !!reactPlayerDiv);

// 6. Sugestão de teste manual
console.log('\n💡 TESTE MANUAL:');
console.log('1. Clique no botão de play grande');
console.log('2. Verifique os novos logs com: window.__logger.getHistory("player").slice(-5)');
console.log('3. Se não funcionar, tente outro canal:');
console.log('   window.location.href = "/watch/iptv-SanMarinoRTVSport.sm"');