{"total": 69, "channels": [{"id": "30AGolfKingdom.us", "name": "30A Golf Kingdom", "source": "tested_working"}, {"id": "ACCDigitalNetwork.us", "name": "ACC Digital Network", "source": "user_requested"}, {"id": "Adjarasport1.ge", "name": "Adjarasport 1", "source": "tested_working"}, {"id": "ADOTV.bj", "name": "ADO TV", "source": "user_requested"}, {"id": "Africa24Sport.fr", "name": "Africa 24 Sport", "source": "user_requested"}, {"id": "AfroSportNigeria.ng", "name": "AfroSport Nigeria", "source": "tested_working"}, {"id": "AntenaSport.hr", "name": "AntenaSport", "source": "tested_working"}, {"id": "AntenaSport.ro", "name": "AntenaSport", "source": "tested_working"}, {"id": "Arryadia.ma", "name": "<PERSON><PERSON><PERSON>", "source": "user_requested"}, {"id": "AstrahanRuSport.ru", "name": "Astrahan.Ru Sport", "source": "user_requested"}, {"id": "BahrainSports1.bh", "name": "Bahrain Sports 1", "source": "user_requested"}, {"id": "BahrainSports2.bh", "name": "Bahrain Sports 2", "source": "user_requested"}, {"id": "Belarus5.by", "name": "Belarus-5", "source": "user_requested"}, {"id": "BellatorMMA.us", "name": "Bellator MMA", "source": "user_requested"}, {"id": "BilliardTV.us", "name": "Billiard TV", "source": "user_requested"}, {"id": "CanaldoInter.br", "name": "Canal do Inter", "source": "user_requested"}, {"id": "CBSSportsNetworkUSA.us", "name": "CBS Sports Network USA", "source": "user_requested"}, {"id": "CTSport.cz", "name": "CT Sport", "source": "user_requested"}, {"id": "DongNaiTV2.vn", "name": "Dong Nai TV 2", "source": "user_requested"}, {"id": "DubaiRacing.ae", "name": "Dubai Racing", "source": "user_requested"}, {"id": "DubaiRacing2.ae", "name": "Dubai Racing 2", "source": "user_requested"}, {"id": "DubaiRacing3.ae", "name": "Dubai Racing 3", "source": "user_requested"}, {"id": "DubaiSports1.ae", "name": "Dubai Sports 1", "source": "user_requested"}, {"id": "DubaiSports2.ae", "name": "Dubai Sports 2", "source": "user_requested"}, {"id": "DubaiSports3.ae", "name": "Dubai Sports 3", "source": "user_requested"}, {"id": "ElevenSports1.pl", "name": "Eleven Sports 1", "source": "user_requested"}, {"id": "ESPNews.us", "name": "ESPNews", "source": "tested_working"}, {"id": "ESPNU.us", "name": "ESPNU", "source": "tested_working"}, {"id": "FanDuelSportsNetwork.us", "name": "FanDuel Sports Network", "source": "tested_working"}, {"id": "FanDuelTV.us", "name": "FanDuel TV", "source": "tested_working"}, {"id": "FIFAPlus.pl", "name": "FIFA+", "source": "tested_working"}, {"id": "FITE247.us", "name": "FITE 24/7", "source": "tested_working"}, {"id": "GloryKickboxing.us", "name": "Glory Kickboxing", "source": "tested_working"}, {"id": "InsightTV.nl", "name": "Insight TV", "source": "tested_working"}, {"id": "InterTV.it", "name": "Inter TV", "source": "tested_working"}, {"id": "IRIB3.ir", "name": "IRIB 3", "source": "tested_working"}, {"id": "ITVDeportes.mx", "name": "ITV Deportes", "source": "tested_working"}, {"id": "K19.at", "name": "K19", "source": "tested_working"}, {"id": "KHLPrime.ru", "name": "KHL Prime", "source": "tested_working"}, {"id": "KSASports1.sa", "name": "KSA Sports 1", "source": "tested_working"}, {"id": "LacrosseTV.us", "name": "Lacrosse TV", "source": "tested_working"}, {"id": "MadeinBOTV.it", "name": "MadeinBO TV", "source": "tested_working"}, {"id": "MatchArena.ru", "name": "Match! Arena", "source": "tested_working"}, {"id": "MAVTVSelect.us", "name": "MAVTV Select", "source": "tested_working"}, {"id": "MLBNetwork.us", "name": "MLB Network", "source": "tested_working"}, {"id": "MoreThanSportsTV.de", "name": "More Than Sports TV", "source": "tested_working"}, {"id": "MSG.us", "name": "MSG", "source": "tested_working"}, {"id": "NFLNetwork.us", "name": "NFL Network", "source": "tested_working"}, {"id": "NFLRedZone.us", "name": "NFL RedZone", "source": "tested_working"}, {"id": "NHLNetwork.us", "name": "NHL Network", "source": "tested_working"}, {"id": "NitroCircus.us", "name": "Nitro Circus", "source": "tested_working"}, {"id": "ONFootball.vn", "name": "ON Football", "source": "tested_working"}, {"id": "RacingAmerica.us", "name": "Racing America", "source": "tested_working"}, {"id": "RedBullTV.at", "name": "Red Bull TV", "source": "tested_working"}, {"id": "SOSKanalPlus.rs", "name": "SOS Kanal Plus", "source": "tested_working"}, {"id": "SportsGrid.us", "name": "SportsGrid", "source": "tested_working"}, {"id": "SportsmanChannel.us", "name": "Sportsman Channel", "source": "tested_working"}, {"id": "SportsNetNewYork.us", "name": "SportsNet New York", "source": "tested_working"}, {"id": "StarSports1Tamil.in", "name": "Star Sports 1 Tamil", "source": "tested_working"}, {"id": "SwerveSports.us", "name": "Swerve Sports", "source": "tested_working"}, {"id": "talkSPORT.uk", "name": "talkSPORT", "source": "tested_working"}, {"id": "TDMSports.mo", "name": "TDM Sports", "source": "tested_working"}, {"id": "TRSport.it", "name": "TR Sport", "source": "tested_working"}, {"id": "TraceSportStars.fr", "name": "Trace Sport Stars", "source": "tested_working"}, {"id": "TSNTheOcho.ca", "name": "TSN The Ocho", "source": "tested_working"}, {"id": "TSN1.ca", "name": "TSN1", "source": "tested_working"}, {"id": "TSN2.ca", "name": "TSN2", "source": "tested_working"}, {"id": "TSN3.ca", "name": "TSN3", "source": "tested_working"}, {"id": "TSN5.ca", "name": "TSN5", "source": "tested_working"}], "notFound": []}