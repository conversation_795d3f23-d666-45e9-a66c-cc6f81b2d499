-- <PERSON><PERSON><PERSON> para manter apenas os 69 canais selecionados
-- Data: 2025-07-26T19:39:45.595Z

BEGIN;

-- <PERSON><PERSON>, vamos contar quantos canais serão removidos
SELECT COUNT(*) as canais_a_remover 
FROM channels 
WHERE category = 'sports' 
  AND id NOT IN (
  '30AGolfKingdom.us',
  'ACCDigitalNetwork.us',
  'Adjarasport1.ge',
  'ADOTV.bj',
  'Africa24Sport.fr',
  'AfroSportNigeria.ng',
  'AntenaSport.hr',
  'AntenaSport.ro',
  'Arryadia.ma',
  'AstrahanRuSport.ru',
  'BahrainSports1.bh',
  'BahrainSports2.bh',
  'Belarus5.by',
  'BellatorMMA.us',
  'BilliardTV.us',
  'CanaldoInter.br',
  'CBSSportsNetworkUSA.us',
  'CTSport.cz',
  'DongNaiTV2.vn',
  'DubaiRacing.ae',
  'DubaiRacing2.ae',
  'DubaiRacing3.ae',
  'DubaiSports1.ae',
  'DubaiSports2.ae',
  'DubaiSports3.ae',
  'ElevenSports1.pl',
  'ESPNews.us',
  'ESPNU.us',
  'FanDuelSportsNetwork.us',
  'FanDuelTV.us',
  'FIFAPlus.pl',
  'FITE247.us',
  'GloryKickboxing.us',
  'InsightTV.nl',
  'InterTV.it',
  'IRIB3.ir',
  'ITVDeportes.mx',
  'K19.at',
  'KHLPrime.ru',
  'KSASports1.sa',
  'LacrosseTV.us',
  'MadeinBOTV.it',
  'MatchArena.ru',
  'MAVTVSelect.us',
  'MLBNetwork.us',
  'MoreThanSportsTV.de',
  'MSG.us',
  'NFLNetwork.us',
  'NFLRedZone.us',
  'NHLNetwork.us',
  'NitroCircus.us',
  'ONFootball.vn',
  'RacingAmerica.us',
  'RedBullTV.at',
  'SOSKanalPlus.rs',
  'SportsGrid.us',
  'SportsmanChannel.us',
  'SportsNetNewYork.us',
  'StarSports1Tamil.in',
  'SwerveSports.us',
  'talkSPORT.uk',
  'TDMSports.mo',
  'TRSport.it',
  'TraceSportStars.fr',
  'TSNTheOcho.ca',
  'TSN1.ca',
  'TSN2.ca',
  'TSN3.ca',
  'TSN5.ca'
);

-- Remover todos os canais de esportes EXCETO os selecionados
DELETE FROM channels 
WHERE category = 'sports' 
  AND id NOT IN (
  '30AGolfKingdom.us',
  'ACCDigitalNetwork.us',
  'Adjarasport1.ge',
  'ADOTV.bj',
  'Africa24Sport.fr',
  'AfroSportNigeria.ng',
  'AntenaSport.hr',
  'AntenaSport.ro',
  'Arryadia.ma',
  'AstrahanRuSport.ru',
  'BahrainSports1.bh',
  'BahrainSports2.bh',
  'Belarus5.by',
  'BellatorMMA.us',
  'BilliardTV.us',
  'CanaldoInter.br',
  'CBSSportsNetworkUSA.us',
  'CTSport.cz',
  'DongNaiTV2.vn',
  'DubaiRacing.ae',
  'DubaiRacing2.ae',
  'DubaiRacing3.ae',
  'DubaiSports1.ae',
  'DubaiSports2.ae',
  'DubaiSports3.ae',
  'ElevenSports1.pl',
  'ESPNews.us',
  'ESPNU.us',
  'FanDuelSportsNetwork.us',
  'FanDuelTV.us',
  'FIFAPlus.pl',
  'FITE247.us',
  'GloryKickboxing.us',
  'InsightTV.nl',
  'InterTV.it',
  'IRIB3.ir',
  'ITVDeportes.mx',
  'K19.at',
  'KHLPrime.ru',
  'KSASports1.sa',
  'LacrosseTV.us',
  'MadeinBOTV.it',
  'MatchArena.ru',
  'MAVTVSelect.us',
  'MLBNetwork.us',
  'MoreThanSportsTV.de',
  'MSG.us',
  'NFLNetwork.us',
  'NFLRedZone.us',
  'NHLNetwork.us',
  'NitroCircus.us',
  'ONFootball.vn',
  'RacingAmerica.us',
  'RedBullTV.at',
  'SOSKanalPlus.rs',
  'SportsGrid.us',
  'SportsmanChannel.us',
  'SportsNetNewYork.us',
  'StarSports1Tamil.in',
  'SwerveSports.us',
  'talkSPORT.uk',
  'TDMSports.mo',
  'TRSport.it',
  'TraceSportStars.fr',
  'TSNTheOcho.ca',
  'TSN1.ca',
  'TSN2.ca',
  'TSN3.ca',
  'TSN5.ca'
);

-- Verificar resultado
SELECT COUNT(*) as canais_esportes_restantes 
FROM channels 
WHERE category = 'sports';

-- Listar os canais mantidos
SELECT id, name 
FROM channels 
WHERE category = 'sports'
ORDER BY name;

COMMIT;
