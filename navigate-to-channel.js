// Navegar para um canal específico
console.log('🔍 Procurando canais disponíveis...\n');

// Buscar cards de canais na página
const channelCards = document.querySelectorAll('a[href^="/watch/"]');
console.log(`📺 ${channelCards.length} canais encontrados`);

if (channelCards.length > 0) {
    // Pegar informações dos primeiros 5 canais
    console.log('\nPrimeiros canais disponíveis:');
    Array.from(channelCards).slice(0, 5).forEach((card, index) => {
        const href = card.getAttribute('href');
        const title = card.querySelector('h3')?.textContent || 'Sem título';
        console.log(`${index + 1}. ${title}`);
        console.log(`   URL: ${href}`);
    });
    
    console.log('\n💡 Para assistir um canal, clique em um dos links acima');
    console.log('Ou execute: channelCards[0].click() para o primeiro canal');
    
    // Salvar referência global
    window.channelCards = channelCards;
    
    // Ir automaticamente para o primeiro canal
    console.log('\n▶️ Indo para o primeiro canal...');
    channelCards[0].click();
} else {
    console.log('❌ Nenhum canal encontrado na página');
    console.log('\n💡 Tente acessar diretamente:');
    console.log('window.location.href = "/test-player"');
}