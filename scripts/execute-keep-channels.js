const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });
const fs = require('fs');
const path = require('path');

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  console.log('Please ensure NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY are set in .env.local');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function executeKeepChannelsScript() {
  console.log('🚀 Executing script to keep only 69 selected sports channels...');
  console.log('📅 Date:', new Date().toISOString());

  try {
    // Read the SQL file
    const sqlPath = path.join(__dirname, '..', 'keep-only-69-channels.sql');
    const sqlContent = fs.readFileSync(sqlPath, 'utf8');
    console.log('✅ SQL file loaded successfully');

    // First, let's count current sports channels
    const { data: currentCount, error: countError } = await supabase
      .from('channels')
      .select('id', { count: 'exact', head: true })
      .eq('category', 'sports');

    if (countError) {
      console.error('❌ Error counting current sports channels:', countError);
    } else {
      console.log(`📊 Current sports channels count: ${currentCount || 0}`);
    }

    // Execute the transaction
    console.log('\n🔧 Starting transaction...');
    
    // Extract the channel IDs from the SQL
    const channelIds = [
      '30AGolfKingdom.us',
      'ACCDigitalNetwork.us',
      'Adjarasport1.ge',
      'ADOTV.bj',
      'Africa24Sport.fr',
      'AfroSportNigeria.ng',
      'AntenaSport.hr',
      'AntenaSport.ro',
      'Arryadia.ma',
      'AstrahanRuSport.ru',
      'BahrainSports1.bh',
      'BahrainSports2.bh',
      'Belarus5.by',
      'BellatorMMA.us',
      'BilliardTV.us',
      'CanaldoInter.br',
      'CBSSportsNetworkUSA.us',
      'CTSport.cz',
      'DongNaiTV2.vn',
      'DubaiRacing.ae',
      'DubaiRacing2.ae',
      'DubaiRacing3.ae',
      'DubaiSports1.ae',
      'DubaiSports2.ae',
      'DubaiSports3.ae',
      'ElevenSports1.pl',
      'ESPNews.us',
      'ESPNU.us',
      'FanDuelSportsNetwork.us',
      'FanDuelTV.us',
      'FIFAPlus.pl',
      'FITE247.us',
      'GloryKickboxing.us',
      'InsightTV.nl',
      'InterTV.it',
      'IRIB3.ir',
      'ITVDeportes.mx',
      'K19.at',
      'KHLPrime.ru',
      'KSASports1.sa',
      'LacrosseTV.us',
      'MadeinBOTV.it',
      'MatchArena.ru',
      'MAVTVSelect.us',
      'MLBNetwork.us',
      'MoreThanSportsTV.de',
      'MSG.us',
      'NFLNetwork.us',
      'NFLRedZone.us',
      'NHLNetwork.us',
      'NitroCircus.us',
      'ONFootball.vn',
      'RacingAmerica.us',
      'RedBullTV.at',
      'SOSKanalPlus.rs',
      'SportsGrid.us',
      'SportsmanChannel.us',
      'SportsNetNewYork.us',
      'StarSports1Tamil.in',
      'SwerveSports.us',
      'talkSPORT.uk',
      'TDMSports.mo',
      'TRSport.it',
      'TraceSportStars.fr',
      'TSNTheOcho.ca',
      'TSN1.ca',
      'TSN2.ca',
      'TSN3.ca',
      'TSN5.ca'
    ];

    // Count channels to be removed
    const { data: toRemove, error: toRemoveError } = await supabase
      .from('channels')
      .select('id', { count: 'exact', head: true })
      .eq('category', 'sports')
      .not('id', 'in', `(${channelIds.join(',')})`);

    if (!toRemoveError) {
      console.log(`📊 Channels to be removed: ${toRemove || 0}`);
    }

    // Delete channels not in the list
    console.log('\n🗑️  Deleting channels not in the selected list...');
    const { error: deleteError, count: deletedCount } = await supabase
      .from('channels')
      .delete()
      .eq('category', 'sports')
      .not('id', 'in', `(${channelIds.join(',')})`);

    if (deleteError) {
      console.error('❌ Error deleting channels:', deleteError);
      process.exit(1);
    }

    console.log(`✅ Deleted ${deletedCount || 0} channels`);

    // Verify final count
    const { data: finalCount, error: finalCountError } = await supabase
      .from('channels')
      .select('id', { count: 'exact', head: true })
      .eq('category', 'sports');

    if (!finalCountError) {
      console.log(`\n📊 Final sports channels count: ${finalCount || 0}`);
    }

    // List the remaining channels
    console.log('\n📋 Listing remaining sports channels:');
    const { data: remainingChannels, error: listError } = await supabase
      .from('channels')
      .select('id, name')
      .eq('category', 'sports')
      .order('name');

    if (listError) {
      console.error('❌ Error listing channels:', listError);
    } else if (remainingChannels) {
      console.log(`\n✅ Successfully kept ${remainingChannels.length} sports channels:\n`);
      remainingChannels.forEach((channel, index) => {
        console.log(`${index + 1}. ${channel.name} (${channel.id})`);
      });
    }

    console.log('\n✅ Script executed successfully!');
    console.log(`🎉 Database now contains only the 69 selected sports channels.`);

  } catch (error) {
    console.error('❌ Script execution failed:', error);
    process.exit(1);
  }
}

// Execute the script
executeKeepChannelsScript();