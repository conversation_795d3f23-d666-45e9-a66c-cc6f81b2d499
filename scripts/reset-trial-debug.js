// Script para executar no console do navegador
// Este script limpa o trial e verifica o status premium

console.log('🔄 Resetando trial e verificando premium...');

// 1. Limpar trial do localStorage
localStorage.removeItem('trial-store');
console.log('✅ Trial removido do localStorage');

// 2. Verificar status da assinatura
async function checkPremiumStatus() {
  try {
    const response = await fetch('/api/stripe/subscription-status');
    const data = await response.json();
    
    console.log('📊 Status da assinatura:', data);
    
    if (data.hasActiveSubscription) {
      console.log('✅ ASSINATURA ATIVA!');
      console.log('  - Tier:', data.subscriptionTier);
      console.log('  - Status:', data.subscriptionStatus);
      console.log('  - Expira em:', new Date(data.subscriptionEnd).toLocaleString('pt-BR'));
    } else {
      console.log('❌ SEM ASSINATURA ATIVA');
      console.log('  - Motivo:', data);
    }
    
    // 3. Verificar access store
    const accessStoreStr = localStorage.getItem('access-store');
    if (accessStoreStr) {
      const accessStore = JSON.parse(accessStoreStr);
      console.log('🔐 Access Store:', accessStore.state);
    }
    
    // 4. Forçar check de acesso
    console.log('🔄 Forçando verificação de acesso...');
    const checkResponse = await fetch('/api/check-access', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ userId: data.userId })
    });
    
    if (checkResponse.ok) {
      const checkData = await checkResponse.json();
      console.log('🔐 Check Access Result:', checkData);
    }
    
  } catch (error) {
    console.error('❌ Erro ao verificar status:', error);
  }
}

// 5. Executar verificação
checkPremiumStatus().then(() => {
  console.log('');
  console.log('📝 PRÓXIMOS PASSOS:');
  console.log('1. Recarregue a página (F5)');
  console.log('2. Tente acessar um canal novamente');
  console.log('3. Se ainda não funcionar, execute o endpoint de debug');
});

// 6. Mostrar informações do usuário
console.log('');
console.log('👤 Usuário atual:');
console.log('  - ID:', '590b3ae5-92f2-47d2-91a6-8c29c23dc9d4');
console.log('  - Email:', '<EMAIL>');