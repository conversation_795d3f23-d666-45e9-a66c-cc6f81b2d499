-- Script para corrigir a estrutura da tabela trial_sessions
-- Execute este script no Supabase SQL Editor

-- 1. Verificar se a tabela existe e suas colunas
SELECT 
    column_name, 
    data_type, 
    is_nullable
FROM 
    information_schema.columns
WHERE 
    table_schema = 'public' 
    AND table_name = 'trial_sessions'
ORDER BY 
    ordinal_position;

-- 2. Se a tabela não tem a coluna session_id, adicionar
DO $$ 
BEGIN
    -- Verificar se a coluna session_id existe
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'trial_sessions' 
        AND column_name = 'session_id'
    ) THEN
        -- Se não existe, adicionar a coluna
        ALTER TABLE public.trial_sessions 
        ADD COLUMN session_id UUID UNIQUE NOT NULL DEFAULT gen_random_uuid();
        
        RAISE NOTICE 'Coluna session_id adicionada com sucesso';
    ELSE
        RAISE NOTICE 'Coluna session_id já existe';
    END IF;
END $$;

-- 3. Verificar e adicionar outras colunas necessárias
DO $$ 
BEGIN
    -- Adicionar user_id se não existir
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'trial_sessions' 
        AND column_name = 'user_id'
    ) THEN
        ALTER TABLE public.trial_sessions 
        ADD COLUMN user_id TEXT;
        
        RAISE NOTICE 'Coluna user_id adicionada';
    END IF;
    
    -- Adicionar trial_duration_minutes se não existir
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'trial_sessions' 
        AND column_name = 'trial_duration_minutes'
    ) THEN
        ALTER TABLE public.trial_sessions 
        ADD COLUMN trial_duration_minutes INTEGER DEFAULT 5;
        
        RAISE NOTICE 'Coluna trial_duration_minutes adicionada';
    END IF;
END $$;

-- 4. Criar índices necessários
CREATE INDEX IF NOT EXISTS idx_trial_sessions_session_id ON public.trial_sessions(session_id);
CREATE INDEX IF NOT EXISTS idx_trial_sessions_trial_end ON public.trial_sessions(trial_end);
CREATE INDEX IF NOT EXISTS idx_trial_sessions_user_id ON public.trial_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_trial_sessions_user_created ON public.trial_sessions(user_id, created_at DESC);

-- 5. Verificar estrutura final
SELECT 
    column_name, 
    data_type, 
    is_nullable,
    column_default
FROM 
    information_schema.columns
WHERE 
    table_schema = 'public' 
    AND table_name = 'trial_sessions'
ORDER BY 
    ordinal_position;