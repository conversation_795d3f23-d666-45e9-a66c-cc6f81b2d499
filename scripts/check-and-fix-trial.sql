-- <PERSON>ript para verificar e corrigir a estrutura da tabela trial_sessions
-- Execute cada bloco separadamente no Supabase SQL Editor

-- BLOCO 1: Verificar estrutura atual
SELECT 
    table_name,
    column_name, 
    data_type, 
    is_nullable,
    column_default
FROM 
    information_schema.columns
WHERE 
    table_schema = 'public' 
    AND table_name = 'trial_sessions'
ORDER BY 
    ordinal_position;

-- BLOCO 2: Se a tabela não existe, criar ela
CREATE TABLE IF NOT EXISTS public.trial_sessions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  session_id UUID UNIQUE NOT NULL DEFAULT gen_random_uuid(),
  user_id TEXT,
  trial_start TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  trial_end TIMESTAMPTZ NOT NULL,
  trial_duration_minutes INTEGER DEFAULT 5,
  ip_address TEXT,
  user_agent TEXT,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- BLOCO 3: Se a tabela existe mas falta colunas, adicionar
-- Adicionar session_id se não existir
ALTER TABLE public.trial_sessions 
ADD COLUMN IF NOT EXISTS session_id UUID UNIQUE NOT NULL DEFAULT gen_random_uuid();

-- Adicionar user_id se não existir
ALTER TABLE public.trial_sessions 
ADD COLUMN IF NOT EXISTS user_id TEXT;

-- Adicionar trial_duration_minutes se não existir
ALTER TABLE public.trial_sessions 
ADD COLUMN IF NOT EXISTS trial_duration_minutes INTEGER DEFAULT 5;

-- BLOCO 4: Criar índices
CREATE INDEX IF NOT EXISTS idx_trial_sessions_session_id ON public.trial_sessions(session_id);
CREATE INDEX IF NOT EXISTS idx_trial_sessions_trial_end ON public.trial_sessions(trial_end);
CREATE INDEX IF NOT EXISTS idx_trial_sessions_user_id ON public.trial_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_trial_sessions_user_created ON public.trial_sessions(user_id, created_at DESC);

-- BLOCO 5: Verificar estrutura final
SELECT 
    column_name, 
    data_type, 
    is_nullable,
    column_default
FROM 
    information_schema.columns
WHERE 
    table_schema = 'public' 
    AND table_name = 'trial_sessions'
ORDER BY 
    ordinal_position;