const { createClient } = require('@supabase/supabase-js')
const fs = require('fs')
const path = require('path')

// Load environment variables
require('dotenv').config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing required environment variables')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function applyMigrations() {
  console.log('🚀 Aplicando migrações...\n')
  
  // Migration 025: Fix app activation
  console.log('📝 Aplicando migration 025_fix_app_activation.sql...')
  
  try {
    // Alterar coluna token_id para permitir NULL
    const { error: alterError } = await supabase.rpc('exec_sql', {
      sql: `ALTER TABLE anonymous_access ALTER COLUMN token_id DROP NOT NULL;`
    })
    
    if (alterError) {
      console.log('  ⚠️ Erro ao alterar anonymous_access (pode já estar alterada):', alterError.message)
    } else {
      console.log('  ✅ Coluna token_id alterada para permitir NULL')
    }
    
    // Criar tabela activation_logs
    const { error: createError } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS activation_logs (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          user_id TEXT,
          activation_type VARCHAR(50),
          device_id TEXT,
          metadata JSONB,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
      `
    })
    
    if (createError) {
      console.log('  ⚠️ Erro ao criar activation_logs:', createError.message)
    } else {
      console.log('  ✅ Tabela activation_logs criada')
    }
    
  } catch (error) {
    console.error('❌ Erro na migration 025:', error)
  }
  
  // Migration 026: Fix check_user_access_ambiguity
  console.log('\n📝 Aplicando migration 026_fix_check_user_access_ambiguity.sql...')
  
  try {
    const { error: funcError } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE OR REPLACE FUNCTION check_user_access_unified(p_user_id TEXT, p_now TIMESTAMP WITH TIME ZONE DEFAULT NOW())
        RETURNS TABLE(
          has_access BOOLEAN,
          access_type TEXT,
          expires_at TIMESTAMP WITH TIME ZONE
        ) AS $$
        DECLARE
          v_profile_exists BOOLEAN;
          v_has_premium BOOLEAN;
          v_premium_expires TIMESTAMP WITH TIME ZONE;
          v_has_anonymous BOOLEAN;
          v_anonymous_expires TIMESTAMP WITH TIME ZONE;
        BEGIN
          -- Check for profile-based access first (both tables)
          SELECT 
            CASE 
              WHEN COUNT(*) > 0 THEN true 
              ELSE false 
            END,
            CASE 
              WHEN COUNT(*) > 0 AND MAX(subscription_status) = 'active' THEN true 
              ELSE false 
            END,
            MAX(subscription_current_period_end)
          INTO v_profile_exists, v_has_premium, v_premium_expires
          FROM (
            SELECT subscription_status, subscription_current_period_end
            FROM profiles 
            WHERE id::text = p_user_id
            UNION ALL
            SELECT subscription_status, subscription_current_period_end
            FROM stack_profiles
            WHERE id = p_user_id
          ) combined_profiles;

          -- Check for anonymous access
          SELECT 
            COUNT(*) > 0,
            MAX(expires_at)
          INTO v_has_anonymous, v_anonymous_expires
          FROM anonymous_access
          WHERE user_id = p_user_id
            AND expires_at > p_now
            AND is_active = true;

          -- Return results based on what was found
          IF v_has_premium AND v_premium_expires > p_now THEN
            RETURN QUERY SELECT true, 'authenticated'::TEXT, v_premium_expires;
          ELSIF v_has_anonymous THEN
            RETURN QUERY SELECT true, 'anonymous'::TEXT, v_anonymous_expires;
          ELSE
            RETURN QUERY SELECT false, NULL::TEXT, NULL::TIMESTAMP WITH TIME ZONE;
          END IF;
        END;
        $$ LANGUAGE plpgsql SECURITY DEFINER;
      `
    })
    
    if (funcError) {
      console.log('  ⚠️ Erro ao atualizar função:', funcError.message)
    } else {
      console.log('  ✅ Função check_user_access_unified atualizada')
    }
    
  } catch (error) {
    console.error('❌ Erro na migration 026:', error)
  }
  
  console.log('\n✅ Processo de migração concluído!')
}

// Função auxiliar para executar SQL diretamente
async function execSQL(sql) {
  const { data, error } = await supabase.rpc('exec_sql', { sql })
  if (error) throw error
  return data
}

// Se a função exec_sql não existir, criar ela primeiro
async function ensureExecSQL() {
  try {
    const { error } = await supabase.rpc('exec_sql', { sql: 'SELECT 1' })
    if (error && error.code === 'PGRST202') {
      console.log('⚙️ Criando função exec_sql...')
      
      // Usar diretamente o cliente postgres
      const { createClient: createPostgresClient } = require('@supabase/supabase-js')
      const postgresUrl = process.env.DATABASE_URL || process.env.POSTGRES_URL
      
      if (!postgresUrl) {
        console.error('❌ DATABASE_URL ou POSTGRES_URL não encontrada')
        process.exit(1)
      }
      
      console.log('📝 Por favor, execute as seguintes migrações manualmente no Supabase Dashboard:\n')
      
      console.log('-- Migration 025: Fix app activation')
      console.log('ALTER TABLE anonymous_access ALTER COLUMN token_id DROP NOT NULL;')
      console.log('')
      console.log('CREATE TABLE IF NOT EXISTS activation_logs (')
      console.log('  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,')
      console.log('  user_id TEXT,')
      console.log('  activation_type VARCHAR(50),')
      console.log('  device_id TEXT,')
      console.log('  metadata JSONB,')
      console.log('  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()')
      console.log(');')
      console.log('')
      console.log('-- Migration 026: Fix check_user_access_ambiguity')
      console.log(fs.readFileSync(path.join(__dirname, '..', 'supabase', 'migrations', '026_fix_check_user_access_ambiguity.sql'), 'utf8'))
      
      process.exit(0)
    }
  } catch (error) {
    console.error('❌ Erro ao verificar exec_sql:', error)
  }
}

// Executar
ensureExecSQL().then(() => applyMigrations())