#!/usr/bin/env node

import { chromium } from 'playwright'

async function testTrialForNewUser() {
  console.log('🧪 Iniciando teste de trial para novo usuário...')
  
  const browser = await chromium.launch({
    headless: false,
    devtools: true
  })
  
  const context = await browser.newContext()
  const page = await context.newPage()
  
  // Limpar todo localStorage para simular novo usuário
  await page.addInitScript(() => {
    localStorage.clear()
    sessionStorage.clear()
    console.log('🧹 Storage limpo - simulando novo usuário')
  })
  
  // Capturar logs do console
  page.on('console', msg => {
    const text = msg.text()
    if (text.includes('[TRIAL-') || text.includes('[ACCESS-')) {
      console.log(`📋 ${text}`)
    }
  })
  
  // Navegar para a página inicial
  console.log('🌐 Navegando para http://localhost:3000...')
  await page.goto('http://localhost:3000')
  await page.waitForTimeout(2000)
  
  // Verificar estado inicial no console
  const initialState = await page.evaluate(() => {
    const storage = localStorage.getItem('trial-storage')
    return storage ? JSON.parse(storage) : null
  })
  console.log('💾 Estado inicial do localStorage:', initialState)
  
  // Navegar para um canal
  console.log('📺 Navegando para canal de teste...')
  await page.goto('http://localhost:3000/watch/ESPNPremiumHD.us')
  
  // Aguardar carregamento do player
  await page.waitForTimeout(5000)
  
  // Verificar estado após navegação
  const afterNavState = await page.evaluate(() => {
    const storage = localStorage.getItem('trial-storage')
    const parsed = storage ? JSON.parse(storage) : null
    
    // Verificar estado do trial diretamente
    const trialStore = (window as any).__zustand_trial_store
    const currentState = trialStore ? trialStore.getState() : null
    
    return {
      localStorage: parsed,
      zustandState: currentState
    }
  })
  
  console.log('\n📊 Estado após navegação para o canal:')
  console.log('localStorage:', JSON.stringify(afterNavState.localStorage, null, 2))
  console.log('Zustand state:', JSON.stringify(afterNavState.zustandState, null, 2))
  
  // Verificar se o timer está visível
  const timerVisible = await page.locator('[data-trial-timer]').isVisible().catch(() => false)
  console.log(`\n⏱️  Timer visível: ${timerVisible ? 'SIM ✅' : 'NÃO ❌'}`)
  
  // Verificar se o overlay de bloqueio está visível
  const overlayVisible = await page.locator('[data-trial-overlay]').isVisible().catch(() => false)
  console.log(`🚫 Overlay de bloqueio visível: ${overlayVisible ? 'SIM ❌' : 'NÃO ✅'}`)
  
  // Aguardar 10 segundos para ver se o timer está funcionando
  console.log('\n⏳ Aguardando 10 segundos para verificar funcionamento do timer...')
  await page.waitForTimeout(10000)
  
  // Verificar estado final
  const finalState = await page.evaluate(() => {
    const storage = localStorage.getItem('trial-storage')
    const parsed = storage ? JSON.parse(storage) : null
    
    return {
      localStorage: parsed?.state,
      timerText: document.querySelector('[data-trial-timer]')?.textContent
    }
  })
  
  console.log('\n📊 Estado final após 10 segundos:')
  console.log('startTime:', finalState.localStorage?.startTime)
  console.log('timeRemaining:', finalState.localStorage?.timeRemaining)
  console.log('isExpired:', finalState.localStorage?.isExpired)
  console.log('Timer text:', finalState.timerText)
  
  // Análise do resultado
  console.log('\n🔍 Análise do teste:')
  if (finalState.localStorage?.startTime && !finalState.localStorage?.isExpired) {
    console.log('✅ Trial iniciado corretamente!')
    console.log('✅ Usuário tem acesso ao conteúdo!')
  } else {
    console.log('❌ Problema detectado no trial!')
    if (!finalState.localStorage?.startTime) {
      console.log('   - startTime não foi definido')
    }
    if (finalState.localStorage?.isExpired) {
      console.log('   - Trial marcado como expirado prematuramente')
    }
  }
  
  // Manter navegador aberto para análise manual
  console.log('\n💡 Navegador mantido aberto para análise manual.')
  console.log('   Pressione Ctrl+C para encerrar.')
}

// Executar teste
testTrialForNewUser().catch(console.error)