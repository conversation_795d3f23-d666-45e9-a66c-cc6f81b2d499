#!/usr/bin/env node
import puppeteer from 'puppeteer'
import { promises as fs } from 'fs'
import path from 'path'

const TEST_URL = 'http://localhost:3001'
const SCREENSHOTS_DIR = path.join(process.cwd(), 'trial-timer-tests')

async function ensureDir(dir: string) {
  try {
    await fs.mkdir(dir, { recursive: true })
  } catch (error) {
    // Diretório já existe
  }
}

async function takeScreenshot(page: any, name: string) {
  const filename = path.join(SCREENSHOTS_DIR, `${name}.png`)
  await page.screenshot({ path: filename, fullPage: false })
  console.log(`📸 Screenshot salva: ${filename}`)
}

async function testTrialTimer() {
  console.log('🚀 Iniciando teste do Trial Timer...')
  
  await ensureDir(SCREENSHOTS_DIR)
  
  const browser = await puppeteer.launch({
    headless: false,
    defaultViewport: { width: 1920, height: 1080 }
  })
  
  try {
    const page = await browser.newPage()
    
    // Limpar localStorage para começar com trial fresh
    await page.goto(TEST_URL)
    await page.evaluate(() => {
      localStorage.removeItem('trial-storage')
    })
    
    console.log('\n📺 1. Testando canal Real Madrid TV...')
    await page.goto(`${TEST_URL}/watch/real-madrid-tv`)
    await page.waitForSelector('body', { timeout: 3000 }).catch(() => {})
    await new Promise(resolve => setTimeout(resolve, 3000))
    
    // Verificar se o timer está visível
    const timerExists = await page.evaluate(() => {
      const timer = document.querySelector('[class*="Trial:"]')
      return timer !== null
    })
    
    if (timerExists) {
      console.log('✅ Timer encontrado!')
      await takeScreenshot(page, '1-timer-inicial-5-00')
      
      // Clicar no play se necessário
      const playButton = await page.$('[class*="Play"]')
      if (playButton) {
        await playButton.click()
        console.log('▶️ Play clicado')
      }
      
      // Aguardar 15 segundos
      console.log('⏱️ Aguardando 15 segundos...')
      await page.waitForTimeout(15000)
      await takeScreenshot(page, '2-timer-contando-4-45')
      
      // Recarregar página
      console.log('🔄 Recarregando página...')
      await page.reload()
      await page.waitForSelector('body', { timeout: 3000 }).catch(() => {})
    await new Promise(resolve => setTimeout(resolve, 3000))
      await takeScreenshot(page, '3-timer-persistente-apos-reload')
      
      // Testar em outro canal
      console.log('\n📺 2. Mudando para canal Teledeporte...')
      await page.goto(`${TEST_URL}/watch/teledeporte`)
      await page.waitForSelector('body', { timeout: 3000 }).catch(() => {})
    await new Promise(resolve => setTimeout(resolve, 3000))
      await takeScreenshot(page, '4-timer-outro-canal')
      
      // Aguardar até < 60 segundos (aproximadamente 4 minutos)
      console.log('⏱️ Aguardando até timer < 1:00 (pode demorar)...')
      
      // Loop para verificar o tempo restante
      let timeRemaining = 300 // 5 minutos
      while (timeRemaining > 60) {
        await page.waitForTimeout(10000) // Verificar a cada 10 segundos
        
        timeRemaining = await page.evaluate(() => {
          const timerText = document.querySelector('[class*="Trial:"]')?.textContent || ''
          const match = timerText.match(/(\d+):(\d+)/)
          if (match) {
            return parseInt(match[1]) * 60 + parseInt(match[2])
          }
          return 0
        })
        
        console.log(`⏱️ Tempo restante: ${Math.floor(timeRemaining / 60)}:${(timeRemaining % 60).toString().padStart(2, '0')}`)
      }
      
      await takeScreenshot(page, '5-timer-warning-menos-60s')
      
      // Verificar animação de pulse
      const hasWarning = await page.evaluate(() => {
        const timer = document.querySelector('[class*="animate-pulse"]')
        return timer !== null
      })
      
      console.log(hasWarning ? '✅ Animação de warning ativa!' : '❌ Animação de warning não encontrada')
      
      // Aguardar até expirar
      console.log('⏱️ Aguardando expiração total...')
      while (timeRemaining > 0) {
        await page.waitForTimeout(5000)
        timeRemaining = await page.evaluate(() => {
          const timerText = document.querySelector('[class*="Trial:"]')?.textContent || ''
          const match = timerText.match(/(\d+):(\d+)/)
          if (match) {
            return parseInt(match[1]) * 60 + parseInt(match[2])
          }
          return 0
        })
      }
      
      await page.waitForTimeout(2000)
      await takeScreenshot(page, '6-trial-expirado')
      
      console.log('\n✅ Teste concluído com sucesso!')
      console.log(`📁 Screenshots salvas em: ${SCREENSHOTS_DIR}`)
      
    } else {
      console.log('❌ Timer não encontrado na página!')
    }
    
  } catch (error) {
    console.error('❌ Erro durante teste:', error)
  } finally {
    await browser.close()
  }
}

// Executar teste
testTrialTimer().catch(console.error)