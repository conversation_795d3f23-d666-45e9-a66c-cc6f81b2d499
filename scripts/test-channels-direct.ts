import axios from 'axios'
import * as HLS from 'hls-parser'
import { promises as fs } from 'fs'
import path from 'path'

interface StreamTest {
  url: string
  name: string
  status: 'working' | 'failed'
  error?: string
  details?: {
    httpStatus?: number
    responseTime?: number
    contentType?: string
    isHLS?: boolean
    errorType?: string
  }
}

async function testStreamUrl(url: string, name: string): Promise<StreamTest> {
  const startTime = Date.now()
  
  try {
    const response = await axios.get(url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Accept': '*/*'
      },
      timeout: 15000,
      maxRedirects: 5,
      validateStatus: () => true // Accept any status
    })
    
    const responseTime = Date.now() - startTime
    
    if (response.status >= 200 && response.status < 300) {
      // Check if it's HLS
      let isHLS = false
      try {
        if (typeof response.data === 'string' && response.data.includes('#EXTM3U')) {
          HLS.parse(response.data)
          isHLS = true
        }
      } catch {
        // Not valid HLS but might still be a valid stream
      }
      
      return {
        url,
        name,
        status: 'working',
        details: {
          httpStatus: response.status,
          responseTime,
          contentType: response.headers['content-type'],
          isHLS
        }
      }
    } else {
      return {
        url,
        name,
        status: 'failed',
        error: `HTTP ${response.status}`,
        details: {
          httpStatus: response.status,
          responseTime,
          errorType: `HTTP_${response.status}`
        }
      }
    }
  } catch (error: any) {
    let errorType = 'Unknown'
    let errorMessage = error.message
    
    if (error.code === 'ECONNABORTED') {
      errorType = 'Timeout'
      errorMessage = 'Request timeout'
    } else if (error.code === 'ENOTFOUND') {
      errorType = 'DNS_Error'
      errorMessage = 'Domain not found'
    } else if (error.code === 'ECONNREFUSED') {
      errorType = 'Connection_Refused'
      errorMessage = 'Connection refused'
    } else if (error.message.includes('certificate')) {
      errorType = 'SSL_Error'
      errorMessage = 'SSL certificate error'
    }
    
    return {
      url,
      name,
      status: 'failed',
      error: errorMessage,
      details: {
        responseTime: Date.now() - startTime,
        errorType
      }
    }
  }
}

async function main() {
  console.log('🔍 Testing channel streams directly...\n')
  
  // Load working channels
  const workingChannelsData = await fs.readFile(
    path.join(process.cwd(), 'working-channels-list.json'),
    'utf8'
  )
  const workingChannels = JSON.parse(workingChannelsData)
  
  // Load premium channels
  const { premiumFootballChannels } = await import('../src/lib/premium-football-channels')
  
  // Load channel fallbacks
  const { channelFallbacks } = await import('../src/lib/channel-fallbacks')
  
  const results: StreamTest[] = []
  
  // Test premium channels
  console.log('📺 Testing Premium Channels...')
  for (const channel of premiumFootballChannels) {
    for (const stream of channel.streams) {
      process.stdout.write(`Testing ${channel.name}... `)
      const result = await testStreamUrl(stream.url, channel.name)
      results.push(result)
      console.log(result.status === 'working' ? '✅' : `❌ ${result.error}`)
    }
  }
  
  // Test working channels from list
  console.log('\n📡 Testing IPTV Channels...')
  for (const channel of workingChannels.slice(0, 30)) {
    process.stdout.write(`Testing ${channel.name}... `)
    const result = await testStreamUrl(channel.url, channel.name)
    results.push(result)
    console.log(result.status === 'working' ? '✅' : `❌ ${result.error}`)
  }
  
  // Test some fallback URLs
  console.log('\n🔄 Testing Fallback Channels...')
  const fallbackChannels = Object.entries(channelFallbacks).slice(0, 10)
  for (const [channelId, fallback] of fallbackChannels) {
    // Test primary
    process.stdout.write(`Testing ${channelId} (primary)... `)
    const primaryResult = await testStreamUrl(fallback.primary, `${channelId} (primary)`)
    results.push(primaryResult)
    console.log(primaryResult.status === 'working' ? '✅' : `❌ ${primaryResult.error}`)
    
    // Test first fallback
    if (fallback.fallbacks.length > 0) {
      process.stdout.write(`Testing ${channelId} (fallback)... `)
      const fallbackResult = await testStreamUrl(fallback.fallbacks[0], `${channelId} (fallback)`)
      results.push(fallbackResult)
      console.log(fallbackResult.status === 'working' ? '✅' : `❌ ${fallbackResult.error}`)
    }
  }
  
  // Generate summary
  const workingStreams = results.filter(r => r.status === 'working')
  const failedStreams = results.filter(r => r.status === 'failed')
  
  const errorTypes: Record<string, number> = {}
  failedStreams.forEach(stream => {
    const errorType = stream.details?.errorType || 'Unknown'
    errorTypes[errorType] = (errorTypes[errorType] || 0) + 1
  })
  
  const summary = {
    timestamp: new Date().toISOString(),
    total: results.length,
    working: workingStreams.length,
    failed: failedStreams.length,
    successRate: `${(workingStreams.length / results.length * 100).toFixed(1)}%`,
    errorTypes,
    workingStreams: workingStreams.map(s => ({
      name: s.name,
      url: s.url,
      responseTime: s.details?.responseTime,
      isHLS: s.details?.isHLS
    })),
    failedStreams: failedStreams.map(s => ({
      name: s.name,
      url: s.url,
      error: s.error,
      errorType: s.details?.errorType
    }))
  }
  
  // Save results
  await fs.writeFile(
    path.join(process.cwd(), 'channel-direct-test-results.json'),
    JSON.stringify(summary, null, 2)
  )
  
  // Print summary
  console.log('\n' + '='.repeat(60))
  console.log('📊 TEST SUMMARY')
  console.log('='.repeat(60))
  console.log(`Total streams tested: ${summary.total}`)
  console.log(`✅ Working: ${summary.working} (${summary.successRate})`)
  console.log(`❌ Failed: ${summary.failed}`)
  
  console.log('\n🚨 Error Types:')
  Object.entries(errorTypes)
    .sort((a, b) => b[1] - a[1])
    .forEach(([type, count]) => {
      console.log(`  ${type}: ${count}`)
    })
  
  console.log('\n✅ Working Streams:')
  workingStreams.slice(0, 10).forEach(stream => {
    console.log(`  - ${stream.name} (${stream.details?.responseTime}ms)`)
  })
  
  if (workingStreams.length > 10) {
    console.log(`  ... and ${workingStreams.length - 10} more`)
  }
  
  console.log('\n💾 Full results saved to channel-direct-test-results.json')
}

main().catch(console.error)