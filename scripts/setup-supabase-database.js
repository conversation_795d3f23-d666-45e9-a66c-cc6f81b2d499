const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function setupDatabase() {
  console.log('🚀 Setting up Supabase database...');

  try {
    // Read the migration file
    const fs = require('fs');
    const migrationSQL = fs.readFileSync('./supabase/migrations/001_initial_schema.sql', 'utf8');

    // Split the migration into individual statements
    const statements = migrationSQL
      .split(';')
      .map(s => s.trim())
      .filter(s => s.length > 0 && !s.startsWith('--'));

    console.log(`📝 Found ${statements.length} SQL statements to execute`);

    // Execute each statement
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i] + ';';
      console.log(`\n🔧 Executing statement ${i + 1}/${statements.length}...`);
      
      try {
        const { data, error } = await supabase.rpc('exec_sql', {
          sql: statement
        });

        if (error) {
          // Try direct execution if RPC fails
          const { data: directData, error: directError } = await supabase
            .from('_sql')
            .insert({ query: statement })
            .select();

          if (directError) {
            console.error(`❌ Error in statement ${i + 1}:`, directError.message);
            console.log('Statement:', statement.substring(0, 100) + '...');
            // Continue with next statement
          } else {
            console.log(`✅ Statement ${i + 1} executed successfully`);
          }
        } else {
          console.log(`✅ Statement ${i + 1} executed successfully`);
        }
      } catch (err) {
        console.error(`❌ Error in statement ${i + 1}:`, err.message);
        console.log('Statement:', statement.substring(0, 100) + '...');
        // Continue with next statement
      }
    }

    // Verify tables were created
    console.log('\n🔍 Verifying tables...');
    const { data: tables, error: tablesError } = await supabase
      .rpc('get_tables', { schema_name: 'public' });

    if (tablesError) {
      // Try alternative method
      const { data: altTables } = await supabase
        .from('information_schema.tables')
        .select('table_name')
        .eq('table_schema', 'public');

      if (altTables) {
        console.log('✅ Tables found:', altTables.map(t => t.table_name).join(', '));
      }
    } else if (tables) {
      console.log('✅ Tables created:', tables.map(t => t.table_name).join(', '));
    }

    console.log('\n✅ Database setup completed!');

  } catch (error) {
    console.error('❌ Setup failed:', error);
    process.exit(1);
  }
}

// Execute setup
setupDatabase();