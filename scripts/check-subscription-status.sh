#!/bin/bash

echo "🔍 Verificando status da assinatura..."
echo "=================================="

# Verificar status do perfil
echo "📋 1. Verificando perfil do usuário:"
curl -s https://newspports.com/api/process-payment?email=<EMAIL> | jq .

echo ""
echo "🔄 2. Ativando assinatura manualmente:"
curl -X POST https://newspports.com/api/update-profile \
  -H "Content-Type: application/json" \
  -H "x-secret-key: update-profile-manual-2025" \
  -d '{"email": "<EMAIL>"}' | jq .

echo ""
echo "✅ 3. Verificando status final:"
sleep 2
curl -s https://newspports.com/api/process-payment?email=<EMAIL> | jq .

echo ""
echo "📝 Próximos passos:"
echo "1. Faça logout e login novamente"
echo "2. Limpe o cache do navegador (Ctrl+Shift+R)"
echo "3. Acesse um canal premium para testar"