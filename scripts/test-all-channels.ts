#!/usr/bin/env node
import { SELECTED_SPORTS_CHANNELS } from '../src/lib/selected-sports-channels'
import { premiumFootballChannels } from '../src/lib/premium-football-channels'

interface TestResult {
  id: string
  name: string
  status: 'success' | 'error'
  error?: string
  url?: string
  type: 'iptv' | 'premium'
}

async function testChannel(channelId: string, channelType: 'iptv' | 'premium' = 'iptv'): Promise<TestResult> {
  try {
    const apiUrl = channelType === 'premium' 
      ? `http://localhost:3000/api/iptv/channel/premium-${channelId}`
      : `http://localhost:3000/api/iptv/channel/${channelId}`
      
    const response = await fetch(apiUrl)
    const data = await response.json()
    
    if (!response.ok || !data.success) {
      return {
        id: channelId,
        name: channelId,
        status: 'error',
        error: `API returned ${response.status}: ${data.error || 'Unknown error'}`,
        type: channelType
      }
    }
    
    // Quick HEAD request to check if stream is accessible
    try {
      const streamCheck = await fetch(data.data.url, {
        method: 'HEAD',
        headers: {
          ...data.data.headers,
          'User-Agent': data.data.userAgent || 'Mozilla/5.0'
        },
        signal: AbortSignal.timeout(5000) // 5 second timeout
      })
      
      if (!streamCheck.ok && streamCheck.status !== 405) { // 405 is ok for HEAD
        return {
          id: channelId,
          name: data.data.name,
          status: 'error',
          error: `Stream returned ${streamCheck.status}`,
          url: data.data.url,
          type: channelType
        }
      }
    } catch (streamError) {
      // Some streams block HEAD requests, so we'll consider them ok
      if (streamError instanceof Error && streamError.name !== 'AbortError') {
        console.log(`  ⚠️  Stream check failed but might still work: ${streamError.message}`)
      }
    }
    
    return {
      id: channelId,
      name: data.data.name,
      status: 'success',
      url: data.data.url,
      type: channelType
    }
  } catch (error) {
    return {
      id: channelId,
      name: channelId,
      status: 'error',
      error: error instanceof Error ? error.message : 'Unknown error',
      type: channelType
    }
  }
}

async function main() {
  console.log('🔍 Iniciando teste de todos os canais...\n')
  
  const results: TestResult[] = []
  
  // Testar canais premium
  console.log('📺 CANAIS PREMIUM DE FUTEBOL')
  console.log('='.repeat(50))
  
  for (const channel of premiumFootballChannels) {
    console.log(`\nTestando: ${channel.name}`)
    const result = await testChannel(channel.id, 'premium')
    results.push(result)
    console.log(`${result.status === 'success' ? '✅' : '❌'} ${result.name}: ${result.status === 'success' ? 'OK' : result.error}`)
    if (result.url) console.log(`   URL: ${result.url}`)
    
    await new Promise(resolve => setTimeout(resolve, 500))
  }
  
  console.log('\n\n📺 CANAIS IPTV SELECIONADOS')
  console.log('='.repeat(50))
  
  // Testar alguns canais IPTV selecionados
  const iptvChannels = Array.from(SELECTED_SPORTS_CHANNELS).slice(0, 20)
  
  for (const channelId of iptvChannels) {
    console.log(`\nTestando: ${channelId}`)
    const result = await testChannel(channelId, 'iptv')
    results.push(result)
    console.log(`${result.status === 'success' ? '✅' : '❌'} ${result.name}: ${result.status === 'success' ? 'OK' : result.error}`)
    
    await new Promise(resolve => setTimeout(resolve, 500))
  }
  
  // Resumo
  console.log('\n\n📊 RESUMO')
  console.log('='.repeat(80))
  
  const premiumResults = results.filter(r => r.type === 'premium')
  const iptvResults = results.filter(r => r.type === 'iptv')
  
  const premiumSuccess = premiumResults.filter(r => r.status === 'success').length
  const iptvSuccess = iptvResults.filter(r => r.status === 'success').length
  
  console.log(`\n📺 Canais Premium:`)
  console.log(`   ✅ Funcionando: ${premiumSuccess}/${premiumResults.length} (${((premiumSuccess / premiumResults.length) * 100).toFixed(1)}%)`)
  console.log(`   ❌ Com erro: ${premiumResults.length - premiumSuccess}`)
  
  console.log(`\n📺 Canais IPTV:`)
  console.log(`   ✅ Funcionando: ${iptvSuccess}/${iptvResults.length} (${((iptvSuccess / iptvResults.length) * 100).toFixed(1)}%)`)
  console.log(`   ❌ Com erro: ${iptvResults.length - iptvSuccess}`)
  
  console.log(`\n📊 Total Geral:`)
  console.log(`   ✅ Funcionando: ${premiumSuccess + iptvSuccess}/${results.length} (${(((premiumSuccess + iptvSuccess) / results.length) * 100).toFixed(1)}%)`)
  console.log(`   ❌ Com erro: ${results.length - premiumSuccess - iptvSuccess}`)
  
  // Salvar resultados
  const fs = await import('fs')
  fs.writeFileSync(
    './channel-test-results.json',
    JSON.stringify({
      timestamp: new Date().toISOString(),
      summary: {
        total: results.length,
        success: premiumSuccess + iptvSuccess,
        error: results.length - premiumSuccess - iptvSuccess,
        premium: {
          total: premiumResults.length,
          success: premiumSuccess,
          error: premiumResults.length - premiumSuccess
        },
        iptv: {
          total: iptvResults.length,
          success: iptvSuccess,
          error: iptvResults.length - iptvSuccess
        }
      },
      results
    }, null, 2)
  )
  console.log('\n💾 Resultados salvos em channel-test-results.json')
}

main().catch(console.error)