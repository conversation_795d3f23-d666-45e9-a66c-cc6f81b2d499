#!/bin/bash

echo "🧪 Teste rápido do sistema de trial"
echo "=================================="
echo ""
echo "1. Limpando localStorage no navegador..."
echo "2. Navegando para um canal..."
echo ""
echo "⚠️  IMPORTANTE: <PERSON><PERSON> o DevTools (F12) antes de continuar!"
echo ""
echo "Pressione ENTER quando o DevTools estiver aberto..."
read

# Abrir navegador com DevTools e limpar localStorage
open -a "Google Chrome" "http://localhost:3000" --args --auto-open-devtools-for-tabs

echo ""
echo "📋 No Console do DevTools, execute:"
echo ""
echo "localStorage.clear(); location.href = '/watch/ESPNPremiumHD.us'"
echo ""
echo "📊 Observe os logs que começam com:"
echo "- [TRIAL-STORE]"
echo "- [TRIAL-TIMER]"
echo "- [TRIAL-PLAYER]"
echo "- [ACCESS-STORE]"
echo ""
echo "✅ O trial deve iniciar com 5:00 minutos"
echo "❌ Se aparecer bloqueado imediatamente, há um bug"