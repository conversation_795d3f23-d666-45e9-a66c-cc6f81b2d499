#!/usr/bin/env tsx

// Script para atualizar produto Stripe para espanhol e criar preço em EUR

import { config } from 'dotenv'
import { resolve } from 'path'

// Carregar variáveis de ambiente
config({ path: resolve(process.cwd(), '.env.local') })

const STRIPE_SECRET_KEY = process.env.STRIPE_SECRET_KEY

if (!STRIPE_SECRET_KEY || !STRIPE_SECRET_KEY.startsWith('sk_')) {
  console.error('❌ STRIPE_SECRET_KEY não configurada ou inválida')
  process.exit(1)
}

async function updateProductAndCreateEuroPrice() {
  try {
    console.log('🔄 Atualizando produto para espanhol e criando preço em EUR...\n')
    
    const productId = 'prod_SlBxDaGOJCF0ZT'
    
    // 1. Atualizar produto para espanhol
    console.log('1️⃣ Atualizando descrição do produto...')
    
    const updateProductResponse = await fetch(
      `https://api.stripe.com/v1/products/${productId}`,
      {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${STRIPE_SECRET_KEY}`,
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
          'name': 'NewSpports Premium España',
          'description': 'Acceso ilimitado a todos los canales deportivos premium en HD y 4K, sin anuncios y con soporte 24/7. La mejor experiencia de streaming deportivo en España.',
          'metadata[locale]': 'es_ES',
          'metadata[country]': 'ES',
        })
      }
    )
    
    if (!updateProductResponse.ok) {
      const error = await updateProductResponse.json()
      throw new Error(`Erro ao atualizar produto: ${JSON.stringify(error)}`)
    }
    
    const updatedProduct = await updateProductResponse.json()
    console.log('✅ Produto atualizado:', updatedProduct.name)
    console.log('   Descrição:', updatedProduct.description)
    
    // 2. Criar novo preço em EUR
    console.log('\n2️⃣ Criando novo preço em EUR...')
    
    const createPriceResponse = await fetch(
      'https://api.stripe.com/v1/prices',
      {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${STRIPE_SECRET_KEY}`,
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
          'product': productId,
          'unit_amount': '2000', // 20.00 EUR em centavos
          'currency': 'eur',
          'recurring[interval]': 'month',
          'recurring[interval_count]': '1',
          'nickname': 'Premium Mensual España',
          'metadata[locale]': 'es_ES',
          'metadata[description]': 'Suscripción mensual premium para España',
        })
      }
    )
    
    if (!createPriceResponse.ok) {
      const error = await createPriceResponse.json()
      throw new Error(`Erro ao criar preço: ${JSON.stringify(error)}`)
    }
    
    const newPrice = await createPriceResponse.json()
    console.log('✅ Novo preço criado com sucesso!')
    console.log('   ID:', newPrice.id)
    console.log('   Valor:', `€${newPrice.unit_amount / 100}/${newPrice.recurring.interval}`)
    console.log('   Moeda:', newPrice.currency.toUpperCase())
    
    // 3. Desativar preço antigo em USD (opcional)
    console.log('\n3️⃣ Desativando preço antigo em USD...')
    const oldPriceId = 'price_1RpgkBDMzmKMrtWZSwjgIzt9'
    
    const deactivatePriceResponse = await fetch(
      `https://api.stripe.com/v1/prices/${oldPriceId}`,
      {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${STRIPE_SECRET_KEY}`,
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
          'active': 'false'
        })
      }
    )
    
    if (deactivatePriceResponse.ok) {
      console.log('✅ Preço antigo em USD desativado')
    } else {
      console.log('⚠️  Não foi possível desativar o preço antigo')
    }
    
    console.log('\n🎉 Configuração concluída!')
    console.log('\n📝 IMPORTANTE: Atualize o arquivo .env.local com:')
    console.log(`STRIPE_PRICE_ID=${newPrice.id}`)
    console.log(`\nE também no Railway!`)
    
  } catch (error) {
    console.error('❌ Erro:', error)
    process.exit(1)
  }
}

updateProductAndCreateEuroPrice()