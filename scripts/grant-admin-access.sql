-- Sc<PERSON>t to grant admin access to a Stack Auth user
-- Run this in Supabase SQL Editor after the user has logged in at least once

-- Grant admin <NAME_EMAIL>
UPDATE stack_profiles
SET role = 'admin',
    updated_at = NOW()
WHERE email = '<EMAIL>';

-- Check if the update was successful
SELECT id, email, role, created_at, updated_at
FROM stack_profiles
WHERE email = '<EMAIL>';

-- List all admin users
SELECT id, email, role, created_at
FROM stack_profiles
WHERE role = 'admin'
ORDER BY created_at DESC;