#!/usr/bin/env node
import { SELECTED_SPORTS_CHANNELS } from '../src/lib/selected-sports-channels'
import { premiumFootballChannels } from '../src/lib/premium-football-channels'

interface TestResult {
  id: string
  name: string
  status: 'success' | 'error'
  error?: string
  url?: string
  type: 'iptv' | 'premium'
  responseTime?: number
  httpStatus?: number
}

async function testChannel(channelId: string, channelType: 'iptv' | 'premium' = 'iptv'): Promise<TestResult> {
  const startTime = Date.now()
  
  try {
    const apiUrl = channelType === 'premium' 
      ? `http://localhost:3000/api/iptv/channel/premium-${channelId}`
      : `http://localhost:3000/api/iptv/channel/${channelId}`
      
    const response = await fetch(apiUrl)
    const data = await response.json()
    
    if (!response.ok || !data.success) {
      return {
        id: channelId,
        name: channelId,
        status: 'error',
        error: `API returned ${response.status}: ${data.error || 'Unknown error'}`,
        type: channelType,
        httpStatus: response.status
      }
    }
    
    // Testar acesso ao stream
    try {
      const streamCheck = await fetch(data.data.url, {
        method: 'HEAD',
        headers: {
          ...data.data.headers,
          'User-Agent': data.data.userAgent || 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        },
        signal: AbortSignal.timeout(10000) // 10 segundos
      })
      
      const responseTime = Date.now() - startTime
      
      if (!streamCheck.ok && streamCheck.status !== 405) { // 405 é ok para HEAD
        return {
          id: channelId,
          name: data.data.name,
          status: 'error',
          error: `Stream returned ${streamCheck.status}`,
          url: data.data.url,
          type: channelType,
          responseTime,
          httpStatus: streamCheck.status
        }
      }
      
      return {
        id: channelId,
        name: data.data.name,
        status: 'success',
        url: data.data.url,
        type: channelType,
        responseTime,
        httpStatus: streamCheck.status
      }
    } catch (streamError) {
      const responseTime = Date.now() - startTime
      
      // Alguns streams bloqueiam HEAD, mas ainda funcionam
      if (streamError instanceof Error && streamError.name === 'AbortError') {
        return {
          id: channelId,
          name: data.data.name,
          status: 'error',
          error: 'Timeout ao acessar stream',
          url: data.data.url,
          type: channelType,
          responseTime
        }
      }
      
      // Tentar GET com range limitado
      try {
        const getCheck = await fetch(data.data.url, {
          headers: {
            ...data.data.headers,
            'User-Agent': data.data.userAgent || 'Mozilla/5.0',
            'Range': 'bytes=0-1024' // Pegar apenas 1KB
          },
          signal: AbortSignal.timeout(10000)
        })
        
        if (getCheck.ok || getCheck.status === 206) { // 206 é Partial Content
          return {
            id: channelId,
            name: data.data.name,
            status: 'success',
            url: data.data.url,
            type: channelType,
            responseTime: Date.now() - startTime,
            httpStatus: getCheck.status
          }
        }
      } catch {}
      
      return {
        id: channelId,
        name: data.data.name,
        status: 'error',
        error: `Stream inacessível: ${streamError instanceof Error ? streamError.message : 'Unknown'}`,
        url: data.data.url,
        type: channelType,
        responseTime
      }
    }
  } catch (error) {
    return {
      id: channelId,
      name: channelId,
      status: 'error',
      error: error instanceof Error ? error.message : 'Unknown error',
      type: channelType,
      responseTime: Date.now() - startTime
    }
  }
}

async function testAllChannels() {
  console.log('🔍 Teste Completo de Canais NewSpports')
  console.log('='*80)
  console.log(`Data: ${new Date().toLocaleString('pt-BR')}\n`)
  
  const results: TestResult[] = []
  
  // 1. Testar canais premium
  console.log('\n📺 CANAIS PREMIUM DE FUTEBOL')
  console.log('-'*60)
  
  for (const channel of premiumFootballChannels) {
    process.stdout.write(`Testando ${channel.name}...`)
    const result = await testChannel(channel.id, 'premium')
    results.push(result)
    
    if (result.status === 'success') {
      console.log(` ✅ OK (${result.responseTime}ms)`)
    } else {
      console.log(` ❌ ERRO: ${result.error}`)
    }
    
    if (result.url) {
      console.log(`  └─ URL: ${result.url}`)
    }
    
    await new Promise(resolve => setTimeout(resolve, 200)) // Delay entre testes
  }
  
  // 2. Testar canais IPTV selecionados
  console.log('\n\n📺 CANAIS IPTV DE ESPORTES')
  console.log('-'*60)
  
  const iptvChannels = Array.from(SELECTED_SPORTS_CHANNELS)
  let count = 0
  
  for (const channelId of iptvChannels) {
    count++
    process.stdout.write(`[${count}/${iptvChannels.length}] ${channelId}...`)
    const result = await testChannel(channelId, 'iptv')
    results.push(result)
    
    if (result.status === 'success') {
      console.log(` ✅ ${result.name} (${result.responseTime}ms)`)
    } else {
      console.log(` ❌ ${result.error}`)
    }
    
    await new Promise(resolve => setTimeout(resolve, 200))
  }
  
  // 3. Análise detalhada
  console.log('\n\n📊 ANÁLISE DETALHADA')
  console.log('='*80)
  
  // Canais Premium
  const premiumResults = results.filter(r => r.type === 'premium')
  const premiumWorking = premiumResults.filter(r => r.status === 'success')
  const premiumFailed = premiumResults.filter(r => r.status === 'error')
  
  console.log('\n🏆 Canais Premium:')
  console.log(`├─ Total: ${premiumResults.length}`)
  console.log(`├─ ✅ Funcionando: ${premiumWorking.length} (${((premiumWorking.length / premiumResults.length) * 100).toFixed(1)}%)`)
  console.log(`└─ ❌ Com erro: ${premiumFailed.length}`)
  
  if (premiumWorking.length > 0) {
    console.log('\n  Canais Premium Funcionando:')
    premiumWorking.forEach(ch => {
      console.log(`  • ${ch.name} (${ch.responseTime}ms)`)
    })
  }
  
  if (premiumFailed.length > 0) {
    console.log('\n  Canais Premium com Erro:')
    premiumFailed.forEach(ch => {
      console.log(`  • ${ch.name}: ${ch.error}`)
    })
  }
  
  // Canais IPTV
  const iptvResults = results.filter(r => r.type === 'iptv')
  const iptvWorking = iptvResults.filter(r => r.status === 'success')
  const iptvFailed = iptvResults.filter(r => r.status === 'error')
  
  console.log('\n\n📡 Canais IPTV:')
  console.log(`├─ Total: ${iptvResults.length}`)
  console.log(`├─ ✅ Funcionando: ${iptvWorking.length} (${((iptvWorking.length / iptvResults.length) * 100).toFixed(1)}%)`)
  console.log(`└─ ❌ Com erro: ${iptvFailed.length}`)
  
  // Top 10 canais mais rápidos
  const fastestChannels = [...results]
    .filter(r => r.status === 'success' && r.responseTime)
    .sort((a, b) => (a.responseTime || 0) - (b.responseTime || 0))
    .slice(0, 10)
  
  if (fastestChannels.length > 0) {
    console.log('\n\n⚡ Top 10 Canais Mais Rápidos:')
    fastestChannels.forEach((ch, idx) => {
      console.log(`  ${idx + 1}. ${ch.name} (${ch.responseTime}ms)`)
    })
  }
  
  // Estatísticas de erro
  const errorTypes = new Map<string, number>()
  iptvFailed.forEach(ch => {
    const errorKey = ch.error?.includes('404') ? '404 Not Found' :
                    ch.error?.includes('403') ? '403 Forbidden' :
                    ch.error?.includes('Timeout') ? 'Timeout' :
                    ch.error?.includes('CORS') ? 'CORS Error' : 'Outros'
    errorTypes.set(errorKey, (errorTypes.get(errorKey) || 0) + 1)
  })
  
  if (errorTypes.size > 0) {
    console.log('\n\n🔍 Tipos de Erro (IPTV):')
    Array.from(errorTypes.entries())
      .sort((a, b) => b[1] - a[1])
      .forEach(([type, count]) => {
        console.log(`  • ${type}: ${count} canais`)
      })
  }
  
  // Resumo final
  console.log('\n\n📊 RESUMO FINAL')
  console.log('='*80)
  const totalWorking = premiumWorking.length + iptvWorking.length
  const totalChannels = results.length
  const successRate = (totalWorking / totalChannels * 100).toFixed(1)
  
  console.log(`Total de canais testados: ${totalChannels}`)
  console.log(`✅ Funcionando: ${totalWorking} (${successRate}%)`)
  console.log(`❌ Com erro: ${totalChannels - totalWorking} (${(100 - parseFloat(successRate)).toFixed(1)}%)`)
  
  // Salvar resultados detalhados
  const fs = await import('fs')
  const reportData = {
    timestamp: new Date().toISOString(),
    summary: {
      total: totalChannels,
      working: totalWorking,
      failed: totalChannels - totalWorking,
      successRate: parseFloat(successRate),
      premium: {
        total: premiumResults.length,
        working: premiumWorking.length,
        failed: premiumFailed.length
      },
      iptv: {
        total: iptvResults.length,
        working: iptvWorking.length,
        failed: iptvFailed.length
      }
    },
    errorTypes: Array.from(errorTypes.entries()).map(([type, count]) => ({ type, count })),
    fastestChannels: fastestChannels.map(ch => ({
      name: ch.name,
      type: ch.type,
      responseTime: ch.responseTime
    })),
    results
  }
  
  fs.writeFileSync(
    './channel-test-complete-results.json',
    JSON.stringify(reportData, null, 2)
  )
  
  console.log('\n💾 Relatório completo salvo em: channel-test-complete-results.json')
  console.log('\n✅ Teste concluído!')
}

// Executar
testAllChannels().catch(console.error)