#!/usr/bin/env node

import { fetchIPTVStreams, fetchIPTVChannels, fetchSportsStreams } from '../src/lib/iptv-org-server'

async function debugIPTVFetch() {
  console.log('🔍 Debug IPTV-ORG Fetch')
  console.log('=' .repeat(60))
  
  try {
    // Buscar todos os streams
    console.log('\n📡 Buscando todos os streams...')
    const streams = await fetchIPTVStreams()
    console.log(`✅ Total de streams: ${streams.length}`)
    
    // Mostrar alguns exemplos de canais ESPN
    const espnStreams = streams.filter(s => s.channel && s.channel.toLowerCase().includes('espn'))
    console.log(`\n📺 Canais ESPN encontrados: ${espnStreams.length}`)
    espnStreams.slice(0, 5).forEach(stream => {
      console.log(`  - ${stream.channel}: ${stream.url}`)
    })
    
    // Buscar canais
    console.log('\n📋 Buscando metadados dos canais...')
    const channels = await fetchIPTVChannels()
    console.log(`✅ Total de canais: ${channels.length}`)
    
    // Verificar canais esportivos
    const sportsChannels = channels.filter(ch => ch.categories?.includes('sports'))
    console.log(`\n🏆 Canais esportivos: ${sportsChannels.length}`)
    
    // Buscar streams esportivos filtrados
    console.log('\n⚽ Buscando streams esportivos filtrados...')
    const sportsStreams = await fetchSportsStreams()
    console.log(`✅ Streams esportivos selecionados: ${sportsStreams.length}`)
    
    // Verificar se ESPNPremiumHD.us existe
    console.log('\n🔎 Procurando por ESPNPremiumHD.us...')
    const espnPremium = streams.find(s => s.channel === 'ESPNPremiumHD.us')
    if (espnPremium) {
      console.log('✅ Encontrado!')
      console.log('   URL:', espnPremium.url)
      console.log('   Quality:', espnPremium.quality)
    } else {
      console.log('❌ Não encontrado!')
      
      // Procurar variações
      const variations = [
        'ESPNPremium.us',
        'ESPN.us',
        'ESPNNews.us',
        'ESPNU.us'
      ]
      
      console.log('\n🔍 Procurando variações:')
      variations.forEach(v => {
        const found = streams.find(s => s.channel === v)
        console.log(`  ${v}: ${found ? '✅ Encontrado' : '❌ Não encontrado'}`)
      })
    }
    
    // Mostrar primeiros 20 canais disponíveis
    console.log('\n📺 Primeiros 20 canais disponíveis:')
    streams.slice(0, 20).forEach((stream, i) => {
      console.log(`  ${i + 1}. ${stream.channel}`)
    })
    
  } catch (error) {
    console.error('❌ Erro:', error)
  }
}

debugIPTVFetch().catch(console.error)