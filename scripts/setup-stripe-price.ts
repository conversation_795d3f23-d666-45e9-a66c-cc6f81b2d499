#!/usr/bin/env tsx

import 'dotenv/config'
import { stripe } from '../src/lib/stripe'

async function setupStripePrice() {
  try {
    console.log('🔧 Configurando preço mensal no Stripe...\n')

    // ID do produto criado via MCP
    const productId = 'prod_SlBxDaGOJCF0ZT'

    // Buscar produto para confirmar
    const product = await stripe.products.retrieve(productId)
    console.log('✅ Produto encontrado:', product.name)

    // Criar preço recorrente mensal de $20
    const price = await stripe.prices.create({
      product: productId,
      unit_amount: 2000, // $20.00 em centavos
      currency: 'usd',
      recurring: {
        interval: 'month',
        interval_count: 1,
      },
      nickname: 'Monthly Premium',
      metadata: {
        description: 'Assinatura mensal NewSpports Premium',
      }
    })

    console.log('\n✅ Preço criado com sucesso!')
    console.log('   ID:', price.id)
    console.log('   Valor:', `$${price.unit_amount! / 100} USD`)
    console.log('   Intervalo:', price.recurring?.interval)
    console.log('   Tipo:', price.type)

    // Definir como preço padrão
    await stripe.products.update(productId, {
      default_price: price.id,
    })

    console.log('\n✅ Preço definido como padrão do produto')

    console.log('\n📝 Adicione estas variáveis ao seu .env.local:')
    console.log(`STRIPE_PRODUCT_ID=${productId}`)
    console.log(`STRIPE_PRICE_ID=${price.id}`)

  } catch (error) {
    console.error('❌ Erro ao configurar preço:', error)
    process.exit(1)
  }
}

// Executar
setupStripePrice()