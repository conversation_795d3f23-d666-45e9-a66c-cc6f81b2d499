#!/usr/bin/env node

const { createClient } = require('@supabase/supabase-js')
const fs = require('fs')
const path = require('path')

// Load environment variables
require('dotenv').config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing required environment variables')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey)

// Função para executar SQL
async function executeSQL(sql, description) {
  console.log(`\n🔧 ${description}...`)
  
  try {
    const { data, error } = await supabase.rpc('exec_sql', { sql }).single()
    
    if (error) {
      // Se a função exec_sql não existir, executar SQL diretamente
      if (error.code === 'PGRST202' || error.code === '42883') {
        console.log('⚠️ Função exec_sql não encontrada. Executando SQL diretamente...')
        
        // Usar uma abordagem alternativa para executar SQL
        const result = await fetch(`${supabaseUrl}/rest/v1/rpc/exec_sql`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'apikey': supabaseServiceKey,
            'Authorization': `Bearer ${supabaseServiceKey}`
          },
          body: JSON.stringify({ sql })
        })
        
        if (!result.ok) {
          throw new Error(`HTTP error! status: ${result.status}`)
        }
      } else {
        throw error
      }
    }
    
    console.log(`✅ ${description} - Concluído`)
    return true
  } catch (error) {
    console.error(`❌ ${description} - Erro:`, error.message)
    return false
  }
}

// Migração SQL
const migrationSteps = [
  {
    description: 'Criando tabela mobile_app_config',
    sql: `
      CREATE TABLE IF NOT EXISTS public.mobile_app_config (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        android_url TEXT NOT NULL,
        ios_url TEXT,
        qr_code_url TEXT,
        tutorial_video_url TEXT,
        tutorial_video_file TEXT,
        android_badge_url TEXT DEFAULT 'https://play.google.com/intl/en_us/badges/static/images/badges/es_badge_web_generic.png',
        ios_badge_url TEXT DEFAULT 'https://developer.apple.com/assets/elements/badges/download-on-the-app-store.svg',
        ios_available BOOLEAN DEFAULT false,
        android_available BOOLEAN DEFAULT true,
        created_at TIMESTAMPTZ DEFAULT NOW(),
        updated_at TIMESTAMPTZ DEFAULT NOW()
      );
    `
  },
  {
    description: 'Inserindo configuração padrão do app móvel',
    sql: `
      INSERT INTO public.mobile_app_config (
        android_url,
        ios_url,
        qr_code_url
      ) VALUES (
        'https://play.google.com/store/apps/details?id=com.streamplus.espana',
        '#',
        'https://play.google.com/store/apps/details?id=com.streamplus.espana'
      )
      ON CONFLICT DO NOTHING;
    `
  },
  {
    description: 'Habilitando RLS na tabela mobile_app_config',
    sql: `
      ALTER TABLE public.mobile_app_config ENABLE ROW LEVEL SECURITY;
    `
  },
  {
    description: 'Criando política de leitura pública',
    sql: `
      CREATE POLICY "Mobile app config readable by all" ON public.mobile_app_config
        FOR SELECT USING (true);
    `
  },
  {
    description: 'Criando política de edição para admins',
    sql: `
      CREATE POLICY "Mobile app config editable by admins only" ON public.mobile_app_config
        FOR ALL USING (
          EXISTS (
            SELECT 1 FROM public.user_profiles
            WHERE user_profiles.id = auth.uid()
            AND user_profiles.role = 'admin'
          )
        );
    `
  },
  {
    description: 'Criando função para atualizar timestamp',
    sql: `
      CREATE OR REPLACE FUNCTION public.update_mobile_app_config_timestamp()
      RETURNS TRIGGER AS $$
      BEGIN
        NEW.updated_at = NOW();
        RETURN NEW;
      END;
      $$ LANGUAGE plpgsql;
    `
  },
  {
    description: 'Criando trigger para atualizar timestamp',
    sql: `
      CREATE TRIGGER update_mobile_app_config_timestamp
      BEFORE UPDATE ON public.mobile_app_config
      FOR EACH ROW
      EXECUTE FUNCTION public.update_mobile_app_config_timestamp();
    `
  },
  {
    description: 'Criando bucket para vídeos tutoriais',
    sql: `
      INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
      VALUES (
        'tutorial-videos',
        'tutorial-videos',
        true,
        104857600, -- 100MB limit
        ARRAY['video/mp4', 'video/webm', 'video/ogg', 'video/quicktime']
      )
      ON CONFLICT (id) DO NOTHING;
    `
  },
  {
    description: 'Criando política de visualização pública para vídeos',
    sql: `
      CREATE POLICY "Vídeos tutoriais são públicos para visualização" 
      ON storage.objects FOR SELECT 
      USING (bucket_id = 'tutorial-videos');
    `
  },
  {
    description: 'Criando política de upload para admins',
    sql: `
      CREATE POLICY "Apenas admins podem fazer upload de vídeos tutoriais" 
      ON storage.objects FOR INSERT 
      WITH CHECK (
        bucket_id = 'tutorial-videos' AND
        EXISTS (
          SELECT 1 FROM public.user_profiles
          WHERE user_profiles.id = auth.uid()
          AND user_profiles.role = 'admin'
        )
      );
    `
  },
  {
    description: 'Criando política de deleção para admins',
    sql: `
      CREATE POLICY "Apenas admins podem deletar vídeos tutoriais" 
      ON storage.objects FOR DELETE 
      USING (
        bucket_id = 'tutorial-videos' AND
        EXISTS (
          SELECT 1 FROM public.user_profiles
          WHERE user_profiles.id = auth.uid()
          AND user_profiles.role = 'admin'
        )
      );
    `
  }
]

// Executar migração
async function runMigration() {
  console.log('🚀 Iniciando migração para configuração do app móvel...')
  console.log(`📌 Conectando ao Supabase...`)
  
  let successCount = 0
  let errorCount = 0
  
  for (const step of migrationSteps) {
    const success = await executeSQL(step.sql, step.description)
    if (success) {
      successCount++
    } else {
      errorCount++
    }
  }
  
  // Verificar se a tabela foi criada
  console.log('\n📋 Verificando tabela mobile_app_config...')
  try {
    const { data, error } = await supabase
      .from('mobile_app_config')
      .select('*')
      .limit(1)
    
    if (error) {
      throw error
    }
    
    console.log('✅ Tabela mobile_app_config criada com sucesso!')
    if (data && data.length > 0) {
      console.log('📱 Configuração padrão inserida:')
      console.log(`   - Android URL: ${data[0].android_url}`)
      console.log(`   - iOS URL: ${data[0].ios_url}`)
      console.log(`   - QR Code URL: ${data[0].qr_code_url}`)
    }
  } catch (error) {
    console.error('❌ Erro ao verificar tabela:', error.message)
  }
  
  console.log('\n📊 Resumo da migração:')
  console.log(`   ✅ Sucesso: ${successCount}`)
  console.log(`   ❌ Erros: ${errorCount}`)
  
  if (errorCount === 0) {
    console.log('\n🎉 Migração concluída com sucesso!')
  } else {
    console.log('\n⚠️  Migração concluída com alguns erros')
    console.log('   Verifique os logs acima para mais detalhes')
  }
}

// Executar
runMigration().catch(console.error)