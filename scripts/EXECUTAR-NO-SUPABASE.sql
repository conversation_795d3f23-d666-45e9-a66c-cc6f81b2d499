-- <PERSON><PERSON><PERSON><PERSON><PERSON> ESTE SCRIPT NO SUPABASE SQL EDITOR
-- <PERSON><PERSON> e cole todo o conteúdo abaixo no SQL Editor do Supabase

-- PARTE 0: CRIAR TIPOS NECESSÁRIOS (se não existirem)
DO $$ 
BEGIN
    -- Criar tipo subscription_tier se não existir
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'subscription_tier') THEN
        CREATE TYPE subscription_tier AS ENUM ('free', 'basic', 'premium', 'vip');
    END IF;
END $$;

-- PARTE 1: CRIAR TABELA PARA USUÁRIOS STACK AUTH (OAuth)
-- Necessário porque usuários OAuth não existem em auth.users

-- Criar tabela específica para perfis Stack Auth (sem foreign key para auth.users)
CREATE TABLE IF NOT EXISTS stack_profiles (
  id TEXT PRIMARY KEY, -- Stack Auth user ID
  email TEXT,
  full_name TEXT,
  avatar_url TEXT,
  subscription_tier subscription_tier DEFAULT 'free',
  subscription_status TEXT DEFAULT 'inactive',
  subscription_current_period_end TIMESTAMPTZ,
  subscription_start_date TIMESTAMPTZ,
  stripe_customer_id TEXT UNIQUE,
  stripe_subscription_id TEXT,
  trial_used BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Criar índices
CREATE INDEX IF NOT EXISTS idx_stack_profiles_email ON stack_profiles(email);
CREATE INDEX IF NOT EXISTS idx_stack_profiles_stripe_customer_id ON stack_profiles(stripe_customer_id);
CREATE INDEX IF NOT EXISTS idx_stack_profiles_subscription_status ON stack_profiles(subscription_status);

-- Habilitar RLS
ALTER TABLE stack_profiles ENABLE ROW LEVEL SECURITY;

-- Políticas de segurança
CREATE POLICY "Stack profiles are viewable by everyone" ON stack_profiles
  FOR SELECT USING (true);

CREATE POLICY "Stack profiles can be created by system" ON stack_profiles
  FOR INSERT WITH CHECK (true);

CREATE POLICY "Stack profiles can be updated by system" ON stack_profiles
  FOR UPDATE USING (true);

-- PARTE 2: CRIAR FUNÇÃO UNIFICADA PARA VERIFICAR ACESSO
CREATE OR REPLACE FUNCTION check_user_access_unified(p_user_id TEXT)
RETURNS TABLE (
  has_access BOOLEAN,
  access_type VARCHAR,
  expires_at TIMESTAMP WITH TIME ZONE,
  subscription_tier VARCHAR
) AS $$
BEGIN
  -- Primeiro verificar na tabela stack_profiles (usuários Stack Auth)
  IF EXISTS (
    SELECT 1 FROM stack_profiles 
    WHERE id = p_user_id 
    AND subscription_tier = 'premium'
    AND subscription_status = 'active'
    AND subscription_current_period_end > NOW()
  ) THEN
    RETURN QUERY
    SELECT 
      true as has_access,
      'assinatura'::VARCHAR as access_type,
      subscription_current_period_end as expires_at,
      subscription_tier::VARCHAR
    FROM stack_profiles
    WHERE id = p_user_id;
    RETURN;
  END IF;

  -- Tentar converter para UUID e verificar na tabela profiles (Supabase Auth)
  BEGIN
    IF EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = p_user_id::UUID 
      AND subscription_tier = 'premium'
      AND subscription_status = 'active'
      AND subscription_current_period_end > NOW()
    ) THEN
      RETURN QUERY
      SELECT 
        true as has_access,
        'assinatura'::VARCHAR as access_type,
        subscription_current_period_end as expires_at,
        subscription_tier::VARCHAR
      FROM profiles
      WHERE id = p_user_id::UUID;
      RETURN;
    END IF;
  EXCEPTION
    WHEN invalid_text_representation THEN
      -- Não é um UUID válido, continuar
      NULL;
  END;

  -- Se não tem premium, verificar na tabela user_access
  BEGIN
    IF EXISTS (
      SELECT 1 FROM user_access 
      WHERE user_id = p_user_id::UUID 
      AND ativo_ate > NOW()
    ) THEN
      RETURN QUERY
      SELECT 
        true as has_access,
        tipo::VARCHAR as access_type,
        ativo_ate as expires_at,
        'premium'::VARCHAR as subscription_tier
      FROM user_access
      WHERE user_id = p_user_id::UUID
      ORDER BY ativo_ate DESC
      LIMIT 1;
      RETURN;
    END IF;
  EXCEPTION
    WHEN invalid_text_representation THEN
      -- Não é um UUID válido, continuar
      NULL;
  END;

  -- Se não tem nenhum acesso
  RETURN QUERY SELECT 
    false as has_access,
    NULL::VARCHAR as access_type,
    NULL::TIMESTAMP WITH TIME ZONE as expires_at,
    'free'::VARCHAR as subscription_tier;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- PARTE 3: Adicionar colunas à tabela profiles original (se ainda não existirem)
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS email TEXT;
CREATE INDEX IF NOT EXISTS idx_profiles_email ON profiles(email);
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS subscription_start_date TIMESTAMPTZ;

-- Comentários explicativos
COMMENT ON COLUMN profiles.email IS 'Email do usuário para sincronização com Stack Auth e Stripe';
COMMENT ON COLUMN profiles.subscription_start_date IS 'Data de início da assinatura atual';
COMMENT ON TABLE stack_profiles IS 'Perfis de usuários autenticados via Stack Auth (OAuth)';
COMMENT ON FUNCTION check_user_access_unified IS 'Função unificada para verificar acesso de usuários Stack Auth e Supabase Auth';

-- 1. Primeiro, corrigir a função check_user_access
DROP FUNCTION IF EXISTS check_user_access(UUID);

CREATE OR REPLACE FUNCTION check_user_access(p_user_id UUID)
RETURNS TABLE (
  has_access BOOLEAN,
  access_type VARCHAR,
  expires_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
  -- Primeiro verificar na tabela profiles (assinaturas premium)
  IF EXISTS (
    SELECT 1 FROM profiles 
    WHERE id = p_user_id 
    AND subscription_tier = 'premium'
    AND subscription_status = 'active'
    AND subscription_current_period_end > NOW()
  ) THEN
    RETURN QUERY
    SELECT 
      true as has_access,
      'assinatura'::VARCHAR as access_type,
      subscription_current_period_end as expires_at
    FROM profiles
    WHERE id = p_user_id;
    RETURN;
  END IF;

  -- Se não tem premium, verificar na tabela user_access (cupons, etc)
  IF EXISTS (
    SELECT 1 FROM user_access 
    WHERE user_id = p_user_id 
    AND ativo_ate > NOW()
  ) THEN
    RETURN QUERY
    SELECT 
      true as has_access,
      tipo::VARCHAR as access_type,
      ativo_ate as expires_at
    FROM user_access
    WHERE user_id = p_user_id
    ORDER BY ativo_ate DESC
    LIMIT 1;
    RETURN;
  END IF;

  -- Se não tem nenhum acesso
  RETURN QUERY SELECT 
    false as has_access,
    NULL::VARCHAR as access_type,
    NULL::TIMESTAMP WITH TIME ZONE as expires_at;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 2. Ativar premium para o usuário específico
-- Primeiro, buscar o ID do usuário
DO $$
DECLARE
  v_user_id UUID;
  v_email TEXT := '<EMAIL>';
BEGIN
  -- Buscar ID do usuário
  SELECT id INTO v_user_id 
  FROM auth.users 
  WHERE email = v_email;
  
  IF v_user_id IS NULL THEN
    RAISE NOTICE 'Usuário não encontrado para o email: %', v_email;
  ELSE
    -- Criar ou atualizar perfil com premium
    INSERT INTO profiles (
      id,
      subscription_tier,
      subscription_status,
      subscription_current_period_end,
      stripe_customer_id,
      stripe_subscription_id,
      created_at,
      updated_at
    ) VALUES (
      v_user_id,
      'premium',
      'active',
      NOW() + INTERVAL '30 days',
      'cus_manual_supabase',
      'sub_manual_supabase',
      NOW(),
      NOW()
    ) ON CONFLICT (id) DO UPDATE SET
      subscription_tier = 'premium',
      subscription_status = 'active',
      subscription_current_period_end = NOW() + INTERVAL '30 days',
      stripe_customer_id = COALESCE(profiles.stripe_customer_id, 'cus_manual_supabase'),
      stripe_subscription_id = COALESCE(profiles.stripe_subscription_id, 'sub_manual_supabase'),
      updated_at = NOW();
    
    RAISE NOTICE 'Premium ativado com sucesso para: %', v_email;
  END IF;
END $$;

-- 3. Verificar se funcionou
SELECT 
  p.id,
  u.email,
  p.subscription_tier,
  p.subscription_status,
  p.subscription_current_period_end,
  p.subscription_current_period_end > NOW() as is_active,
  p.stripe_customer_id,
  p.stripe_subscription_id,
  ca.* 
FROM profiles p
LEFT JOIN auth.users u ON u.id = p.id
LEFT JOIN LATERAL check_user_access(p.id) ca ON true
WHERE u.email = '<EMAIL>';

-- Se a query acima retornar:
-- has_access = true
-- access_type = 'assinatura'
-- subscription_tier = 'premium'
-- is_active = true
-- Então a ativação foi bem sucedida!

-- PARTE 4: TESTAR A NOVA FUNÇÃO UNIFICADA
-- Teste com um ID de exemplo (substitua pelo seu Stack Auth ID real)
SELECT * FROM check_user_access_unified('YOUR_STACK_AUTH_ID_HERE');

-- PARTE 5: VISUALIZAR TODOS OS PERFIS (Stack + Supabase)
SELECT 
  'stack' as source,
  id,
  email,
  subscription_tier,
  subscription_status,
  subscription_current_period_end,
  subscription_current_period_end > NOW() as is_active
FROM stack_profiles
UNION ALL
SELECT 
  'supabase' as source,
  id::TEXT,
  email,
  subscription_tier,
  subscription_status,
  subscription_current_period_end,
  subscription_current_period_end > NOW() as is_active
FROM profiles;

-- IMPORTANTE: Após executar este script:
-- 1. Teste a ativação premium em https://newspports.com/test-premium.html
-- 2. Se ainda houver erro, verifique os logs do console do navegador
-- 3. O sistema agora suporta tanto usuários Supabase Auth quanto Stack Auth (OAuth)