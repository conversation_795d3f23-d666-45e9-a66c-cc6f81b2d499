import { chromium } from 'playwright';

interface TestResult {
  channelId: string;
  channelName: string;
  status: 'working' | 'failed';
  error?: string;
  timestamp: string;
}

async function testChannels() {
  const results: TestResult[] = [];
  const browser = await chromium.launch({ headless: false });
  const context = await browser.newContext();
  const page = await context.newPage();

  try {
    // Navegar para a página de browse
    await page.goto('http://localhost:3000/browse');
    await page.waitForLoadState('networkidle');

    // Aguardar os canais carregarem
    await page.waitForSelector('[href^="/watch/"]', { timeout: 10000 });

    // Obter todos os links de canais
    const channelLinks = await page.$$eval('[href^="/watch/"]', links => 
      links.map(link => ({
        href: link.getAttribute('href') || '',
        name: link.querySelector('h3')?.textContent || 'Unknown',
        id: (link.getAttribute('href') || '').split('/').pop() || ''
      }))
    );

    console.log(`Found ${channelLinks.length} channels to test`);

    // Testar apenas os primeiros 5 canais inicialmente
    const channelsToTest = channelLinks.slice(0, 5);

    for (const [index, channel] of channelsToTest.entries()) {
      console.log(`\nTesting channel ${index + 1}/${channelsToTest.length}: ${channel.name}`);

      try {
        // Navegar para o canal
        await page.goto(`http://localhost:3000${channel.href}`);
        await page.waitForLoadState('networkidle');

        // Aguardar o player carregar
        await page.waitForSelector('video, iframe', { timeout: 10000 });

        // Verificar se o vídeo está tocando
        const isPlaying = await page.evaluate(() => {
          const video = document.querySelector('video') as HTMLVideoElement;
          if (video) {
            return !video.paused && !video.ended && video.readyState > 2;
          }
          // Se não houver elemento video, verificar se há iframe (player externo)
          const iframe = document.querySelector('iframe');
          return iframe !== null;
        });

        if (isPlaying) {
          results.push({
            channelId: channel.id,
            channelName: channel.name,
            status: 'working',
            timestamp: new Date().toISOString()
          });
          console.log(`✅ ${channel.name} - Working`);
        } else {
          // Aguardar mais um pouco caso o vídeo esteja carregando
          await page.waitForTimeout(5000);
          
          const isPlayingAfterWait = await page.evaluate(() => {
            const video = document.querySelector('video') as HTMLVideoElement;
            if (video) {
              return !video.paused && !video.ended && video.readyState > 2;
            }
            return false;
          });

          if (isPlayingAfterWait) {
            results.push({
              channelId: channel.id,
              channelName: channel.name,
              status: 'working',
              timestamp: new Date().toISOString()
            });
            console.log(`✅ ${channel.name} - Working (after delay)`);
          } else {
            results.push({
              channelId: channel.id,
              channelName: channel.name,
              status: 'failed',
              error: 'Video not playing',
              timestamp: new Date().toISOString()
            });
            console.log(`❌ ${channel.name} - Failed: Video not playing`);
            
            // Tirar screenshot do erro
            await page.screenshot({ 
              path: `screenshots/error-${channel.id}.png`,
              fullPage: false 
            });
          }
        }

      } catch (error) {
        results.push({
          channelId: channel.id,
          channelName: channel.name,
          status: 'failed',
          error: error instanceof Error ? error.message : 'Unknown error',
          timestamp: new Date().toISOString()
        });
        console.log(`❌ ${channel.name} - Failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        
        // Tirar screenshot do erro
        await page.screenshot({ 
          path: `screenshots/error-${channel.id}.png`,
          fullPage: false 
        });
      }

      // Voltar para a página de browse
      await page.goto('http://localhost:3000/browse');
      await page.waitForLoadState('networkidle');
    }

    // Salvar resultados
    const summary = {
      testDate: new Date().toISOString(),
      totalTested: results.length,
      working: results.filter(r => r.status === 'working').length,
      failed: results.filter(r => r.status === 'failed').length,
      results
    };

    console.log('\n\n=== TEST SUMMARY ===');
    console.log(`Total channels tested: ${summary.totalTested}`);
    console.log(`Working channels: ${summary.working}`);
    console.log(`Failed channels: ${summary.failed}`);
    console.log('\nDetailed results:');
    results.forEach(r => {
      console.log(`- ${r.channelName}: ${r.status}${r.error ? ` (${r.error})` : ''}`);
    });

  } catch (error) {
    console.error('Test failed:', error);
  } finally {
    await browser.close();
  }
}

// Executar os testes
testChannels().catch(console.error);