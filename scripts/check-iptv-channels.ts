#!/usr/bin/env tsx

// Script para verificar quais canais estão disponíveis na API IPTV-ORG
// e comparar com nossa lista de canais selecionados

import { SELECTED_SPORTS_CHANNELS } from '../src/lib/selected-sports-channels'
import { WORKING_CHANNELS } from '../src/lib/real-working-channels'

const IPTV_BASE_URL = 'https://iptv-org.github.io/api'

async function checkChannels() {
  console.log('🔍 Verificando canais na API IPTV-ORG...\n')
  
  try {
    // Buscar streams
    console.log('📡 Buscando streams...')
    const streamsResponse = await fetch(`${IPTV_BASE_URL}/streams.json`)
    const streams = await streamsResponse.json()
    console.log(`✅ Total de streams encontrados: ${streams.length}`)
    
    // Buscar canais
    console.log('\n📺 Buscando metadados dos canais...')
    const channelsResponse = await fetch(`${IPTV_BASE_URL}/channels.json`)
    const channels = await channelsResponse.json()
    console.log(`✅ Total de canais encontrados: ${channels.length}`)
    
    // Filtrar apenas canais esportivos
    const sportsChannels = channels.filter(ch => ch.categories?.includes('sports'))
    console.log(`🏃 Canais esportivos: ${sportsChannels.length}`)
    
    // Criar mapa de streams por canal
    const streamsByChannel = new Map()
    streams.forEach(stream => {
      if (!streamsByChannel.has(stream.channel)) {
        streamsByChannel.set(stream.channel, [])
      }
      streamsByChannel.get(stream.channel).push(stream)
    })
    
    // Verificar canais selecionados
    console.log('\n🎯 Verificando canais selecionados...')
    const selectedArray = Array.from(SELECTED_SPORTS_CHANNELS)
    let foundCount = 0
    let notFoundCount = 0
    
    const foundChannels = []
    const notFoundChannels = []
    
    selectedArray.forEach(channelId => {
      const streamsForChannel = streamsByChannel.get(channelId)
      if (streamsForChannel && streamsForChannel.length > 0) {
        foundCount++
        foundChannels.push({
          id: channelId,
          streams: streamsForChannel.length
        })
      } else {
        notFoundCount++
        notFoundChannels.push(channelId)
      }
    })
    
    console.log(`\n✅ Canais encontrados: ${foundCount}`)
    console.log(`❌ Canais não encontrados: ${notFoundCount}`)
    
    // Verificar canais working
    console.log('\n🔧 Verificando canais na lista WORKING...')
    let workingFound = 0
    let workingNotFound = 0
    
    WORKING_CHANNELS.forEach(channel => {
      const streamsForChannel = streamsByChannel.get(channel.id)
      if (streamsForChannel && streamsForChannel.length > 0) {
        workingFound++
      } else {
        workingNotFound++
      }
    })
    
    console.log(`✅ Working channels encontrados na API: ${workingFound}`)
    console.log(`❌ Working channels NÃO encontrados na API: ${workingNotFound}`)
    
    // Listar alguns canais esportivos disponíveis
    console.log('\n📊 Amostra de canais esportivos disponíveis:')
    const sportsChannelsWithStreams = sportsChannels.filter(ch => 
      streamsByChannel.has(ch.id)
    ).slice(0, 20)
    
    sportsChannelsWithStreams.forEach(ch => {
      const streamCount = streamsByChannel.get(ch.id).length
      console.log(`  - ${ch.id} (${ch.name}) - ${streamCount} streams`)
    })
    
    // Buscar canais ESPN
    console.log('\n🔍 Buscando canais ESPN...')
    const espnChannels = channels.filter(ch => 
      ch.id.toLowerCase().includes('espn') || 
      ch.name?.toLowerCase().includes('espn')
    )
    
    console.log(`\n📺 Canais ESPN encontrados: ${espnChannels.length}`)
    espnChannels.forEach(ch => {
      const streamCount = streamsByChannel.get(ch.id)?.length || 0
      console.log(`  - ${ch.id} (${ch.name}) - ${streamCount} streams`)
    })
    
    // Salvar lista de canais não encontrados
    if (notFoundChannels.length > 0) {
      console.log('\n❌ Canais selecionados NÃO encontrados na API:')
      notFoundChannels.forEach(id => console.log(`  - ${id}`))
    }
    
  } catch (error) {
    console.error('❌ Erro ao verificar canais:', error)
  }
}

// Executar verificação
checkChannels()