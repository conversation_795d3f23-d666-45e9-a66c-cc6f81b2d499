#!/bin/bash

# Script para verificar premium localmente antes de fazer push
# Uso: ./scripts/verify-premium-local.sh

echo "🔍 Verificando configuração do premium localmente..."
echo "================================================"

# Cores para output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 1. Verificar se o servidor está rodando
echo -e "\n${YELLOW}1. Verificando servidor local...${NC}"
if curl -s http://localhost:3000 > /dev/null; then
    echo -e "${GREEN}✅ Servidor rodando em localhost:3000${NC}"
else
    echo -e "${RED}❌ Servidor não está rodando. Execute: pnpm dev${NC}"
    exit 1
fi

# 2. Verificar API de debug
echo -e "\n${YELLOW}2. Testando API de debug...${NC}"
RESPONSE=$(curl -s "http://localhost:3000/api/debug-premium?email=<EMAIL>&stackId=590b3ae5-92f2-47d2-91a6-8c2962d26910")

if echo "$RESPONSE" | grep -q "has_access"; then
    echo -e "${GREEN}✅ API de debug funcionando${NC}"
    
    # Extrair informações importantes
    HAS_ACCESS=$(echo "$RESPONSE" | grep -o '"has_access":[^,]*' | cut -d':' -f2)
    ACCESS_TYPE=$(echo "$RESPONSE" | grep -o '"access_type":"[^"]*"' | cut -d'"' -f4)
    SUBSCRIPTION_TIER=$(echo "$RESPONSE" | grep -o '"subscription_tier":"[^"]*"' | cut -d'"' -f4)
    
    echo "   - has_access: $HAS_ACCESS"
    echo "   - access_type: $ACCESS_TYPE"
    echo "   - subscription_tier: $SUBSCRIPTION_TIER"
else
    echo -e "${RED}❌ API de debug com erro${NC}"
    echo "$RESPONSE"
fi

# 3. Verificar página de debug
echo -e "\n${YELLOW}3. Verificando página de debug...${NC}"
if curl -s http://localhost:3000/debug-premium.html | grep -q "NewSpports"; then
    echo -e "${GREEN}✅ Página de debug acessível${NC}"
    echo "   Acesse: http://localhost:3000/debug-premium.html"
else
    echo -e "${RED}❌ Página de debug não encontrada${NC}"
fi

# 4. Verificar arquivos importantes
echo -e "\n${YELLOW}4. Verificando arquivos críticos...${NC}"

FILES_TO_CHECK=(
    "src/stores/access.store.ts"
    "src/components/player/trial-timer.tsx"
    "src/hooks/use-trial-timer.ts"
    "src/hooks/use-premium-check.ts"
    "supabase/migrations/009_fix_ambiguous_column.sql"
    "scripts/fix-premium-access.sql"
)

for file in "${FILES_TO_CHECK[@]}"; do
    if [ -f "$file" ]; then
        echo -e "${GREEN}✅ $file${NC}"
    else
        echo -e "${RED}❌ $file não encontrado${NC}"
    fi
done

# 5. Verificar logs recentes
echo -e "\n${YELLOW}5. Últimos logs relacionados ao premium:${NC}"
if [ -f ".next/server/app/api/debug-premium/route.js" ]; then
    echo "Logs do servidor (últimas 10 linhas com 'premium' ou 'access'):"
    tail -n 100 .next/server/app/api/debug-premium/route.js 2>/dev/null | grep -i -E "(premium|access)" | tail -n 10 || echo "Nenhum log encontrado"
fi

# 6. Instruções finais
echo -e "\n${YELLOW}📋 PRÓXIMOS PASSOS:${NC}"
echo "1. Execute as migrações SQL no Supabase local:"
echo "   - supabase/migrations/009_fix_ambiguous_column.sql"
echo "   - scripts/fix-premium-access.sql"
echo ""
echo "2. Teste o fluxo completo:"
echo "   a) Acesse http://localhost:3000/debug-premium.html"
echo "   b) Verifique se o status está correto"
echo "   c) Clique em 'Force Fix Premium' se necessário"
echo "   d) Acesse uma página de vídeo e verifique:"
echo "      - Timer não deve aparecer"
echo "      - Vídeo deve tocar normalmente"
echo ""
echo "3. Use o painel de debug (canto inferior direito) para monitorar:"
echo "   - hasAccess deve ser true"
echo "   - accessType deve ser 'assinatura'"
echo "   - shouldShowTimer deve ser false"
echo "   - shouldBlock deve ser false"
echo ""
echo "4. Após confirmar que está funcionando:"
echo "   - git add ."
echo "   - git commit -m 'fix: Corrigir acesso premium para Stack Auth users'"
echo "   - git push"

echo -e "\n${GREEN}✨ Script concluído!${NC}"