-- Script para ativar premium <NAME_EMAIL>
-- Execute este script no SQL Editor do Supabase

-- 1. Verificar se o usuário existe na tabela stack_profiles
SELECT 
  id,
  email,
  subscription_tier,
  subscription_status,
  subscription_current_period_end,
  subscription_current_period_end > NOW() as is_active
FROM stack_profiles
WHERE email = '<EMAIL>';

-- 2. Atualizar o perfil para premium ativo
UPDATE stack_profiles
SET 
  subscription_tier = 'premium',
  subscription_status = 'active',
  subscription_current_period_end = NOW() + INTERVAL '30 days',
  subscription_start_date = NOW(),
  updated_at = NOW()
WHERE email = '<EMAIL>';

-- 3. Verificar a atualização
SELECT 
  id,
  email,
  subscription_tier,
  subscription_status,
  subscription_current_period_end,
  subscription_current_period_end > NOW() as is_active
FROM stack_profiles
WHERE email = '<EMAIL>';

-- 4. Testar a função de acesso com o ID do usuário
-- Substitua 'STACK_USER_ID' pelo ID real do usuário (590b3ae5-92f2-47d2-91a6-8c2962d26910)
SELECT * FROM check_user_access_unified('590b3ae5-92f2-47d2-91a6-8c2962d26910');

-- 5. Se você executou a migration com logs, verifique os logs
SELECT * FROM access_check_logs 
WHERE user_id = '590b3ae5-92f2-47d2-91a6-8c2962d26910'
ORDER BY checked_at DESC
LIMIT 10;

-- 6. Verificar todos os usuários premium ativos
SELECT 
  'stack' as source,
  id,
  email,
  subscription_tier,
  subscription_status,
  subscription_current_period_end,
  subscription_current_period_end > NOW() as is_active
FROM stack_profiles
WHERE subscription_tier = 'premium'
  AND subscription_status = 'active'
  AND subscription_current_period_end > NOW();

-- RESULTADO ESPERADO:
-- Após executar este script, o usuário deve ter:
-- - subscription_tier = 'premium'
-- - subscription_status = 'active'
-- - subscription_current_period_end = data 30 dias no futuro
-- - A função check_user_access_unified deve retornar has_access = true