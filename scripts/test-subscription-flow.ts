#!/usr/bin/env tsx

// Script para testar o fluxo de assinatura e trial

const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'

async function testDirectPayment() {
  console.log('\n🔵 Testando pagamento direto (sem trial)...\n')
  
  try {
    const response = await fetch(`${baseUrl}/api/stripe/checkout`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        name: 'Test User',
        trialDays: 0, // Sem trial
      }),
    })

    const data = await response.json()
    
    if (response.ok) {
      console.log('✅ Checkout criado com sucesso!')
      console.log('   URL:', data.url)
      console.log('   Session ID:', data.sessionId)
      console.log('   Trial Days: 0 (pagamento direto)')
    } else {
      console.error('❌ Erro:', data.error)
      console.error('   Status:', response.status)
    }
  } catch (error) {
    console.error('❌ Erro na requisição:', error)
  }
}

async function testTrialFlow() {
  console.log('\n🟢 Testando fluxo de trial (30 dias)...\n')
  
  // 1. Simular redirecionamento para /mobile-app
  console.log('1️⃣ Usuário clica no botão de trial')
  console.log('   → Redirecionando para /mobile-app')
  
  // 2. Testar geração de token
  console.log('\n2️⃣ Testando geração de token (requer autenticação)...')
  
  try {
    const response = await fetch(`${baseUrl}/api/generate-token`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    })

    if (response.status === 401) {
      console.log('   ⚠️  Não autenticado (esperado)')
      console.log('   → Usuário precisa fazer login primeiro')
    } else {
      const data = await response.json()
      console.log('   ✅ Token gerado:', data.token)
      console.log('   → Plataforma:', data.platform)
      console.log('   → Expira em:', data.expiresAt)
    }
  } catch (error) {
    console.error('   ❌ Erro:', error)
  }
  
  // 3. Verificar fluxo do cupom
  console.log('\n3️⃣ Fluxo do cupom após instalar app:')
  console.log('   → Usuário acessa /premio?ref=app-install')
  console.log('   → Sistema gera cupom de 30 dias')
  console.log('   → Usuário pode usar agora ou guardar')
}

async function checkPricing() {
  console.log('\n💰 Verificando configuração de preços...\n')
  
  console.log('Configuração esperada:')
  console.log('- Produto: NewSpports Premium')
  console.log('- Preço: $20/mês (recorrente)')
  console.log('- Trial com app: 30 dias grátis')
  console.log('- Pagamento direto: Sem trial')
}

async function main() {
  console.log('=== TESTE DO FLUXO DE ASSINATURA ===\n')
  
  await checkPricing()
  await testDirectPayment()
  await testTrialFlow()
  
  console.log('\n=== RESUMO DO FLUXO ===\n')
  console.log('1. Página /subscription tem 2 botões:')
  console.log('   - "Suscribirse Ahora - $20/mes" → Checkout direto sem trial')
  console.log('   - "Probar 30 Días Gratis (Descarga la App)" → Redireciona para /mobile-app')
  console.log('\n2. Página /mobile-app:')
  console.log('   - Mostra QR Code e links para download')
  console.log('   - Enfatiza oferta de 30 dias grátis')
  console.log('\n3. Após instalar app:')
  console.log('   - App abre /premio?ref=app-install')
  console.log('   - Usuário recebe cupom de 30 dias')
  console.log('   - Pode usar imediatamente ou guardar')
}

main().catch(console.error)