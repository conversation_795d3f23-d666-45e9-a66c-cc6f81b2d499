#!/usr/bin/env node
/**
 * Script para testar streams IPTV
 * 
 * Uso: pnpm tsx scripts/test-streams.ts
 */

import { httpGet } from '../src/lib/http'

async function testIPTVStreams() {
  console.log('🔍 Testando streams IPTV...\n')
  
  try {
    // Buscar dados da API
    const [streams, channels] = await Promise.all([
      httpGet('https://iptv-org.github.io/api/streams.json'),
      httpGet('https://iptv-org.github.io/api/channels.json')
    ])
    
    console.log(`✅ APIs carregadas:`)
    console.log(`   - Streams: ${streams.length}`)
    console.log(`   - Channels: ${channels.length}\n`)
    
    // Criar mapa de canais
    const channelsMap = new Map()
    channels.forEach((ch: any) => channelsMap.set(ch.id, ch))
    
    // Filtrar esportes
    const sportsStreams = streams.filter((stream: any) => {
      const channel = channelsMap.get(stream.channel)
      return channel?.categories?.includes('sports')
    })
    
    console.log(`⚽ Streams de esportes encontrados: ${sportsStreams.length}\n`)
    
    // Pegar 5 exemplos aleatórios
    const samples = sportsStreams
      .sort(() => Math.random() - 0.5)
      .slice(0, 5)
    
    console.log('📺 Exemplos de streams:')
    console.log('━'.repeat(80))
    
    for (const stream of samples) {
      const channel = channelsMap.get(stream.channel)
      console.log(`\nCanal: ${channel?.name || stream.channel}`)
      console.log(`ID: ${stream.channel}`)
      console.log(`País: ${channel?.country || 'N/A'}`)
      console.log(`URL: ${stream.url}`)
      
      if (stream.http_referrer) {
        console.log(`Referrer: ${stream.http_referrer}`)
      }
      if (stream.user_agent) {
        console.log(`User-Agent: ${stream.user_agent}`)
      }
      
      // Testar se a URL está acessível
      try {
        const response = await fetch(stream.url, {
          method: 'HEAD',
          signal: AbortSignal.timeout(5000)
        })
        console.log(`Status: ${response.status} ${response.ok ? '✅' : '❌'}`)
      } catch (error) {
        console.log(`Status: ❌ Erro ao acessar`)
      }
      
      console.log('─'.repeat(80))
    }
    
    // Estatísticas
    console.log('\n📊 Estatísticas:')
    const countries = new Map()
    sportsStreams.forEach((stream: any) => {
      const channel = channelsMap.get(stream.channel)
      const country = channel?.country || 'Unknown'
      countries.set(country, (countries.get(country) || 0) + 1)
    })
    
    const topCountries = Array.from(countries.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 10)
    
    console.log('\nTop 10 países com mais streams:')
    topCountries.forEach(([country, count]) => {
      console.log(`  ${country}: ${count} streams`)
    })
    
  } catch (error) {
    console.error('❌ Erro:', error)
  }
}

// Executar
testIPTVStreams()