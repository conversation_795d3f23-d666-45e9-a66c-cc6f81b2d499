import { promises as fs } from 'fs';
import path from 'path';

interface Channel {
  name: string;
  logo: string;
  url: string;
  type: string;
  number: number;
  id: string;
}

interface TestResult {
  id: string;
  name: string;
  url: string;
  status: 'working' | '404' | 'cors' | 'timeout' | 'other_error';
  statusCode?: number;
  error?: string;
  needsProxy: boolean;
  directAccess: boolean;
  proxyAccess: boolean;
  responseTime?: number;
  headers?: Record<string, string>;
  alternativeUrl?: string;
  suggestion?: string;
}

interface TestReport {
  timestamp: string;
  totalChannels: number;
  workingChannels: number;
  failedChannels: number;
  channels404: TestResult[];
  channelsWorking: TestResult[];
  channelsCORS: TestResult[];
  channelsTimeout: TestResult[];
  channelsOtherError: TestResult[];
  channelsNeedingProxy: TestResult[];
  summary: {
    working: number;
    error404: number;
    cors: number;
    timeout: number;
    otherErrors: number;
    needProxy: number;
  };
}

// Test channel URL directly
async function testDirectAccess(url: string, timeout: number = 10000): Promise<{
  success: boolean;
  statusCode?: number;
  error?: string;
  responseTime?: number;
}> {
  const startTime = Date.now();
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), timeout);

  try {
    const response = await fetch(url, {
      method: 'HEAD',
      signal: controller.signal,
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      }
    });

    clearTimeout(timeoutId);
    const responseTime = Date.now() - startTime;

    return {
      success: response.ok,
      statusCode: response.status,
      responseTime
    };
  } catch (error: any) {
    clearTimeout(timeoutId);
    const responseTime = Date.now() - startTime;

    if (error.name === 'AbortError') {
      return {
        success: false,
        error: 'timeout',
        responseTime
      };
    }

    return {
      success: false,
      error: error.message || 'unknown error',
      responseTime
    };
  }
}

// Test channel through proxy
async function testProxyAccess(channelId: string, timeout: number = 10000): Promise<{
  success: boolean;
  statusCode?: number;
  error?: string;
  responseTime?: number;
}> {
  const startTime = Date.now();
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), timeout);

  try {
    const response = await fetch(`http://localhost:3000/api/iptv/channel/${channelId}`, {
      signal: controller.signal
    });

    clearTimeout(timeoutId);
    const responseTime = Date.now() - startTime;

    if (!response.ok) {
      const errorData = await response.text();
      return {
        success: false,
        statusCode: response.status,
        error: errorData,
        responseTime
      };
    }

    return {
      success: true,
      statusCode: response.status,
      responseTime
    };
  } catch (error: any) {
    clearTimeout(timeoutId);
    const responseTime = Date.now() - startTime;

    if (error.name === 'AbortError') {
      return {
        success: false,
        error: 'timeout',
        responseTime
      };
    }

    return {
      success: false,
      error: error.message || 'unknown error',
      responseTime
    };
  }
}

// Determine channel status based on test results
function determineStatus(directResult: any, error?: string): TestResult['status'] {
  if (directResult.success) return 'working';
  if (directResult.statusCode === 404) return '404';
  if (directResult.error === 'timeout') return 'timeout';
  if (directResult.error?.includes('CORS') || directResult.error?.includes('Failed to fetch')) return 'cors';
  return 'other_error';
}

// Generate suggestion based on test results
function generateSuggestion(result: TestResult): string {
  if (result.status === 'working') {
    return 'Canal funcionando corretamente';
  }

  if (result.status === '404') {
    return 'URL do canal não existe mais. Necessário encontrar URL alternativa ou remover canal.';
  }

  if (result.status === 'cors' && result.proxyAccess) {
    return 'Canal requer proxy devido a CORS. Já configurado para usar proxy.';
  }

  if (result.status === 'cors' && !result.proxyAccess) {
    return 'Canal bloqueado por CORS. Configurar para usar proxy.';
  }

  if (result.status === 'timeout') {
    return 'Canal muito lento ou indisponível temporariamente. Verificar novamente mais tarde.';
  }

  return 'Erro desconhecido. Verificar logs para mais detalhes.';
}

async function testAllChannels() {
  console.log('🚀 Iniciando teste de todos os canais...\n');

  try {
    // Fetch channels from API
    console.log('📡 Buscando lista de canais...');
    const response = await fetch('http://localhost:3000/api/iptv-simple');
    
    if (!response.ok) {
      throw new Error(`Erro ao buscar canais: ${response.status}`);
    }

    const data = await response.json();
    const channels: Channel[] = data.data.map((ch: any, index: number) => ({
      name: ch.name,
      logo: ch.logo || '',
      url: ch.url,
      type: 'hls',
      number: index + 1,
      id: ch.uniqueId || ch.channel
    }));

    console.log(`✅ ${channels.length} canais encontrados\n`);

    const results: TestResult[] = [];
    const report: TestReport = {
      timestamp: new Date().toISOString(),
      totalChannels: channels.length,
      workingChannels: 0,
      failedChannels: 0,
      channels404: [],
      channelsWorking: [],
      channelsCORS: [],
      channelsTimeout: [],
      channelsOtherError: [],
      channelsNeedingProxy: [],
      summary: {
        working: 0,
        error404: 0,
        cors: 0,
        timeout: 0,
        otherErrors: 0,
        needProxy: 0
      }
    };

    // Test each channel
    for (let i = 0; i < channels.length; i++) {
      const channel = channels[i];
      console.log(`\n[${i + 1}/${channels.length}] Testando: ${channel.name}`);
      console.log(`URL: ${channel.url}`);

      // Test direct access
      console.log('  → Testando acesso direto...');
      const directResult = await testDirectAccess(channel.url);

      // Test proxy access
      console.log('  → Testando acesso via proxy...');
      const proxyResult = await testProxyAccess(channel.id);

      const status = determineStatus(directResult);
      const needsProxy = !directResult.success && proxyResult.success;

      const result: TestResult = {
        id: channel.id,
        name: channel.name,
        url: channel.url,
        status,
        statusCode: directResult.statusCode,
        error: directResult.error,
        needsProxy,
        directAccess: directResult.success,
        proxyAccess: proxyResult.success,
        responseTime: directResult.responseTime,
        suggestion: ''
      };

      result.suggestion = generateSuggestion(result);

      results.push(result);

      // Update report
      if (result.directAccess || result.proxyAccess) {
        report.workingChannels++;
        report.channelsWorking.push(result);
        report.summary.working++;
      } else {
        report.failedChannels++;
      }

      if (result.status === '404') {
        report.channels404.push(result);
        report.summary.error404++;
      } else if (result.status === 'cors') {
        report.channelsCORS.push(result);
        report.summary.cors++;
      } else if (result.status === 'timeout') {
        report.channelsTimeout.push(result);
        report.summary.timeout++;
      } else if (result.status === 'other_error' && !result.directAccess && !result.proxyAccess) {
        report.channelsOtherError.push(result);
        report.summary.otherErrors++;
      }

      if (result.needsProxy) {
        report.channelsNeedingProxy.push(result);
        report.summary.needProxy++;
      }

      // Status
      const statusEmoji = result.directAccess || result.proxyAccess ? '✅' : '❌';
      const statusText = result.directAccess ? 'Direto OK' : result.proxyAccess ? 'Proxy OK' : result.status.toUpperCase();
      console.log(`  ${statusEmoji} Status: ${statusText}`);
      
      if (result.responseTime) {
        console.log(`  ⏱️  Tempo de resposta: ${result.responseTime}ms`);
      }
    }

    // Save detailed results
    const resultsPath = path.join(process.cwd(), 'channel-test-results-404.json');
    await fs.writeFile(resultsPath, JSON.stringify(results, null, 2));
    console.log(`\n💾 Resultados detalhados salvos em: ${resultsPath}`);

    // Save report
    const reportPath = path.join(process.cwd(), 'channel-404-report.json');
    await fs.writeFile(reportPath, JSON.stringify(report, null, 2));
    console.log(`📊 Relatório salvo em: ${reportPath}`);

    // Print summary
    console.log('\n' + '='.repeat(60));
    console.log('📊 RESUMO DO TESTE');
    console.log('='.repeat(60));
    console.log(`Total de canais testados: ${report.totalChannels}`);
    console.log(`✅ Canais funcionando: ${report.summary.working} (${(report.summary.working / report.totalChannels * 100).toFixed(1)}%)`);
    console.log(`❌ Canais com erro 404: ${report.summary.error404} (${(report.summary.error404 / report.totalChannels * 100).toFixed(1)}%)`);
    console.log(`🚫 Canais com CORS: ${report.summary.cors} (${(report.summary.cors / report.totalChannels * 100).toFixed(1)}%)`);
    console.log(`⏱️  Canais com timeout: ${report.summary.timeout} (${(report.summary.timeout / report.totalChannels * 100).toFixed(1)}%)`);
    console.log(`❓ Outros erros: ${report.summary.otherErrors} (${(report.summary.otherErrors / report.totalChannels * 100).toFixed(1)}%)`);
    console.log(`🔄 Canais que precisam proxy: ${report.summary.needProxy}`);
    console.log('='.repeat(60));

    // List 404 channels
    if (report.channels404.length > 0) {
      console.log('\n❌ CANAIS COM ERRO 404:');
      console.log('-'.repeat(60));
      report.channels404.forEach(ch => {
        console.log(`- ${ch.name} (${ch.id})`);
        console.log(`  URL: ${ch.url}`);
        console.log(`  Sugestão: ${ch.suggestion}`);
      });
    }

    // List working channels
    if (report.channelsWorking.length > 0) {
      console.log('\n✅ CANAIS FUNCIONANDO:');
      console.log('-'.repeat(60));
      report.channelsWorking.forEach(ch => {
        const accessType = ch.directAccess ? 'Acesso direto' : 'Via proxy';
        console.log(`- ${ch.name} (${ch.id}) - ${accessType}`);
      });
    }

    console.log('\n✅ Teste concluído!');

  } catch (error) {
    console.error('❌ Erro durante o teste:', error);
    process.exit(1);
  }
}

// Run tests
testAllChannels().catch(console.error);