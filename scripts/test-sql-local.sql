-- Script de teste SQL para verificar se tudo está funcionando localmente
-- Execute este script no SQL Editor do Supabase local

-- 1. Verificar se as tabelas existem
SELECT 
  'profiles' as table_name,
  COUNT(*) as count 
FROM profiles
UNION ALL
SELECT 
  'stack_profiles' as table_name,
  COUNT(*) as count 
FROM stack_profiles
UNION ALL
SELECT 
  'user_access' as table_name,
  COUNT(*) as count 
FROM user_access;

-- 2. Verificar o usuário específico
SELECT 
  'Stack Profile' as source,
  id,
  email,
  subscription_tier,
  subscription_status,
  subscription_current_period_end,
  subscription_current_period_end > NOW() as is_active,
  updated_at
FROM stack_profiles
WHERE email = '<EMAIL>'
   OR id = '590b3ae5-92f2-47d2-91a6-8c2962d26910';

-- 3. Testar a função check_user_access_unified
-- Deve retornar has_access = true se o usuário tem premium ativo
SELECT * FROM check_user_access_unified('590b3ae5-92f2-47d2-91a6-8c2962d26910');

-- 4. Verificar se a tabela de logs existe e tem registros
SELECT 
  COUNT(*) as total_logs,
  COUNT(DISTINCT user_id) as unique_users,
  MAX(checked_at) as last_check
FROM access_check_logs
WHERE checked_at > NOW() - INTERVAL '1 hour';

-- 5. Ver os últimos logs do usuário
SELECT 
  user_id,
  has_access,
  access_type,
  checked_at,
  metadata
FROM access_check_logs
WHERE user_id = '590b3ae5-92f2-47d2-91a6-8c2962d26910'
ORDER BY checked_at DESC
LIMIT 10;

-- 6. Testar a função com logging
SELECT * FROM check_user_access_unified_with_log('590b3ae5-92f2-47d2-91a6-8c2962d26910');

-- 7. Verificar se há algum erro nas funções
SELECT 
  proname as function_name,
  prosrc as source_code
FROM pg_proc
WHERE proname IN ('check_user_access_unified', 'check_user_access_unified_with_log')
LIMIT 1;

-- RESULTADO ESPERADO:
-- 1. Todas as tabelas devem existir
-- 2. O usuário deve ter subscription_tier = 'premium' e subscription_status = 'active'
-- 3. check_user_access_unified deve retornar has_access = true
-- 4. Os logs devem mostrar as verificações recentes
-- 5. Não deve haver erros nas funções