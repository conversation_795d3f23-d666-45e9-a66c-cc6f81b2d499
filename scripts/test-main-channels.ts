import { MAIN_CHANNELS } from '../src/lib/main-channels'
import https from 'https'
import http from 'http'
import { URL } from 'url'

interface TestResult {
  id: string
  name: string
  url: string
  status: 'working' | 'failed' | 'timeout'
  responseTime?: number
  error?: string
  requiresProxy: boolean
  successRate: number
}

async function testChannel(channel: typeof MAIN_CHANNELS[0]): Promise<TestResult> {
  const startTime = Date.now()
  
  try {
    console.log(`\n🔍 Testando: ${channel.name}`)
    console.log(`   URL: ${channel.url}`)
    console.log(`   Proxy necessário: ${channel.requiresProxy ? 'SIM' : 'NÃO'}`)
    
    // Se requer proxy, marcar como "precisa de proxy" mas funcional
    if (channel.requiresProxy) {
      console.log(`   ⚠️  Canal requer proxy - marcado como funcional com proxy`)
      return {
        id: channel.id,
        name: channel.name,
        url: channel.url,
        status: 'working',
        responseTime: 0,
        requiresProxy: true,
        successRate: channel.successRate,
        error: 'Requer proxy para funcionar'
      }
    }
    
    // Testar URL diretamente
    const urlObj = new URL(channel.url)
    const protocol = urlObj.protocol === 'https:' ? https : http
    
    return new Promise((resolve) => {
      const timeout = setTimeout(() => {
        console.log(`   ⏱️  Timeout após 10 segundos`)
        resolve({
          id: channel.id,
          name: channel.name,
          url: channel.url,
          status: 'timeout',
          error: 'Timeout após 10 segundos',
          requiresProxy: false,
          successRate: 0
        })
      }, 10000)
      
      const req = protocol.get(channel.url, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
      }, (res) => {
        clearTimeout(timeout)
        const responseTime = Date.now() - startTime
        
        if (res.statusCode === 200 || res.statusCode === 302 || res.statusCode === 301) {
          console.log(`   ✅ Funcionando! (${res.statusCode}) - ${responseTime}ms`)
          resolve({
            id: channel.id,
            name: channel.name,
            url: channel.url,
            status: 'working',
            responseTime,
            requiresProxy: false,
            successRate: 100
          })
        } else if (res.statusCode === 403) {
          console.log(`   🔒 Bloqueado geograficamente (403)`)
          resolve({
            id: channel.id,
            name: channel.name,
            url: channel.url,
            status: 'failed',
            responseTime,
            error: `HTTP ${res.statusCode} - Bloqueado geograficamente`,
            requiresProxy: true,
            successRate: 0
          })
        } else {
          console.log(`   ❌ Falhou com código ${res.statusCode}`)
          resolve({
            id: channel.id,
            name: channel.name,
            url: channel.url,
            status: 'failed',
            responseTime,
            error: `HTTP ${res.statusCode}`,
            requiresProxy: false,
            successRate: 0
          })
        }
        
        // Consumir a resposta para evitar memory leak
        res.resume()
      })
      
      req.on('error', (err) => {
        clearTimeout(timeout)
        console.log(`   ❌ Erro: ${err.message}`)
        resolve({
          id: channel.id,
          name: channel.name,
          url: channel.url,
          status: 'failed',
          error: err.message,
          requiresProxy: false,
          successRate: 0
        })
      })
      
      req.end()
    })
  } catch (error) {
    console.log(`   ❌ Erro: ${error}`)
    return {
      id: channel.id,
      name: channel.name,
      url: channel.url,
      status: 'failed',
      error: error instanceof Error ? error.message : 'Erro desconhecido',
      requiresProxy: false,
      successRate: 0
    }
  }
}

async function testAllChannels() {
  console.log('🚀 Iniciando teste dos canais principais')
  console.log(`📊 Total de canais: ${MAIN_CHANNELS.length}`)
  console.log('=' .repeat(80))
  
  const results: TestResult[] = []
  
  // Testar todos os canais
  for (const channel of MAIN_CHANNELS) {
    const result = await testChannel(channel)
    results.push(result)
    
    // Pequena pausa entre testes para não sobrecarregar
    await new Promise(resolve => setTimeout(resolve, 500))
  }
  
  // Resumo dos resultados
  console.log('\n' + '=' .repeat(80))
  console.log('📊 RESUMO DOS TESTES')
  console.log('=' .repeat(80))
  
  const working = results.filter(r => r.status === 'working')
  const failed = results.filter(r => r.status === 'failed')
  const timeout = results.filter(r => r.status === 'timeout')
  const needsProxy = results.filter(r => r.requiresProxy)
  
  console.log(`\n✅ Funcionando: ${working.length}`)
  working.forEach(r => {
    console.log(`   - ${r.name}${r.requiresProxy ? ' (com proxy)' : ''} - ${r.responseTime}ms`)
  })
  
  console.log(`\n❌ Falharam: ${failed.length}`)
  failed.forEach(r => {
    console.log(`   - ${r.name}: ${r.error}`)
  })
  
  console.log(`\n⏱️  Timeout: ${timeout.length}`)
  timeout.forEach(r => {
    console.log(`   - ${r.name}`)
  })
  
  console.log(`\n🔐 Precisam de Proxy: ${needsProxy.length}`)
  needsProxy.forEach(r => {
    console.log(`   - ${r.name}`)
  })
  
  // Salvar resultados em arquivo
  const report = {
    timestamp: new Date().toISOString(),
    totalChannels: MAIN_CHANNELS.length,
    summary: {
      working: working.length,
      failed: failed.length,
      timeout: timeout.length,
      needsProxy: needsProxy.length
    },
    results: results.sort((a, b) => {
      // Ordenar por status (working primeiro) e depois por nome
      if (a.status === 'working' && b.status !== 'working') return -1
      if (a.status !== 'working' && b.status === 'working') return 1
      return a.name.localeCompare(b.name)
    })
  }
  
  await require('fs').promises.writeFile(
    'main-channels-test-report.json',
    JSON.stringify(report, null, 2)
  )
  
  console.log('\n📄 Relatório salvo em: main-channels-test-report.json')
  
  // Recomendações finais
  console.log('\n' + '=' .repeat(80))
  console.log('🎯 RECOMENDAÇÕES')
  console.log('=' .repeat(80))
  
  if (working.length < 5) {
    console.log('⚠️  Poucos canais funcionando! Considere:')
    console.log('   1. Adicionar mais canais alternativos')
    console.log('   2. Implementar sistema de proxy robusto')
    console.log('   3. Buscar novas fontes de streams')
  } else {
    console.log('✅ Boa quantidade de canais funcionando!')
  }
  
  if (needsProxy.length > 3) {
    console.log('\n🔐 Muitos canais precisam de proxy:')
    console.log('   - Considere implementar proxy automático')
    console.log('   - Ou buscar URLs alternativas sem restrições')
  }
}

// Executar testes
testAllChannels().catch(console.error)