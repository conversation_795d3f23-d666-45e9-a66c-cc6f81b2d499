import jwt from 'jsonwebtoken'

// Configurações
const JWT_SECRET = 'your-jwt-secret-key'
const DEEP_LINK_URL = 'streamplus://verify'

// Dados do token
const tokenData = {
  userId: 'test-user-123',
  email: '<EMAIL>',
  tokenId: `token_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
  type: 'app_verification',
  exp: Math.floor(Date.now() / 1000) + (24 * 60 * 60) // Expira em 24 horas
}

// Gerar o token
const token = jwt.sign(tokenData, JWT_SECRET)

// Gerar o link de ativação
const activationLink = `${DEEP_LINK_URL}?token=${token}`

console.log('\n🎯 Token de Ativação do App\n')
console.log('Token JWT:', token)
console.log('\n📱 Link de Ativação (Deep Link):')
console.log(activationLink)
console.log('\n🌐 Link para testar no navegador:')
console.log(`http://localhost:3000/activar?token=${token}`)
console.log('\n📋 Dados do Token:')
console.log(JSON.stringify(tokenData, null, 2))
console.log('\n✅ Use o link acima para testar o fluxo de ativação!')