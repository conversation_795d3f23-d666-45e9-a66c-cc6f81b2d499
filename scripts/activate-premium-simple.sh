#!/bin/bash

echo "🔄 Ativando assinatura premium (método simples)..."
echo "==========================================="

# Email e ID do usuário
EMAIL="<EMAIL>"
USER_ID="590b3ae5-92f2-47d2-91a6-8c29c23dc9d4"
SECRET_KEY="activate-premium-2025"
API_URL="https://newspports.com/api/activate-premium-simple"

echo "📧 Email: $EMAIL"
echo "🆔 User ID: $USER_ID"
echo "🔗 URL: $API_URL"
echo ""

# Fazer a requisição com userId
echo "📡 Enviando requisição com userId..."
RESPONSE=$(curl -s -X POST "$API_URL" \
  -H "Content-Type: application/json" \
  -H "x-secret-key: $SECRET_KEY" \
  -d "{\"userId\": \"$USER_ID\", \"email\": \"$EMAIL\"}")

# Verificar se a resposta é JSON válido
if echo "$RESPONSE" | jq . >/dev/null 2>&1; then
  echo "✅ Resposta recebida:"
  echo "$RESPONSE" | jq .
else
  echo "❌ Erro: Resposta não é JSON válido"
  echo "Resposta bruta:"
  echo "$RESPONSE"
fi

echo ""
echo "📝 Próximos passos:"
echo "1. Faça logout e login novamente no site"
echo "2. Limpe o cache do navegador (Ctrl+Shift+R)"
echo "3. Acesse um canal premium para testar"