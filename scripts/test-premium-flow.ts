import { chromium } from 'playwright'

async function testPremiumFlow() {
  const browser = await chromium.launch({ headless: false })
  const context = await browser.newContext()
  const page = await context.newPage()
  
  const testEmail = '<EMAIL>'
  const baseUrl = 'http://localhost:3000'
  
  console.log('🎭 Iniciando teste do fluxo premium...')
  
  try {
    // 1. Ir para página de debug
    console.log('📍 Acessando página de debug...')
    await page.goto(`${baseUrl}/debug-premium.html`)
    await page.waitForTimeout(2000)
    
    // Capturar screenshot da página de debug
    await page.screenshot({ path: 'debug-premium-before.png' })
    console.log('📸 Screenshot inicial capturado')
    
    // 2. Verificar status inicial
    const debugInfo = await page.evaluate(() => {
      const elements = document.querySelectorAll('.info-item')
      const info: any = {}
      elements.forEach(el => {
        const label = el.querySelector('.label')?.textContent || ''
        const value = el.querySelector('.value')?.textContent || ''
        info[label] = value
      })
      return info
    })
    
    console.log('📊 Status inicial:', debugInfo)
    
    // 3. Clicar no botão de Force Fix se necessário
    const needsFix = await page.evaluate(() => {
      const hasAccess = document.querySelector('.value.has-access')?.textContent
      return hasAccess !== 'true'
    })
    
    if (needsFix) {
      console.log('🔧 Aplicando fix premium...')
      await page.click('#forceFix')
      await page.waitForTimeout(3000)
      await page.screenshot({ path: 'debug-premium-after-fix.png' })
    }
    
    // 4. Navegar para a página de watch
    console.log('🎬 Navegando para página de vídeo...')
    await page.goto(`${baseUrl}/watch/1`)
    await page.waitForTimeout(5000)
    
    // 5. Verificar se o timer aparece
    const timerVisible = await page.isVisible('.trial-timer')
    console.log('⏱️  Timer visível:', timerVisible)
    
    // 6. Verificar se o overlay de bloqueio aparece
    const overlayVisible = await page.isVisible('.trial-overlay')
    console.log('🚫 Overlay de bloqueio visível:', overlayVisible)
    
    // 7. Capturar informações do debug panel
    const debugPanelInfo = await page.evaluate(() => {
      const panel = document.querySelector('.access-debug')
      if (!panel) return null
      
      const getInfo = (selector: string) => {
        const el = panel.querySelector(selector)
        return el?.textContent?.split(': ')[1] || null
      }
      
      return {
        hasAccess: getInfo('[data-testid="has-access"]'),
        accessType: getInfo('[data-testid="access-type"]'),
        isExpired: getInfo('[data-testid="is-expired"]'),
        shouldShowTimer: getInfo('[data-testid="should-show-timer"]'),
        shouldBlock: getInfo('[data-testid="should-block"]')
      }
    })
    
    console.log('🔍 Debug Panel Info:', debugPanelInfo)
    
    // 8. Tirar screenshot final
    await page.screenshot({ path: 'watch-page-premium.png', fullPage: true })
    
    // 9. Validar resultados
    const results = {
      success: true,
      issues: [] as string[]
    }
    
    if (timerVisible) {
      results.issues.push('❌ Timer ainda está visível para usuário premium')
    }
    
    if (overlayVisible) {
      results.issues.push('❌ Overlay de bloqueio está visível para usuário premium')
    }
    
    if (debugPanelInfo?.hasAccess !== 'true') {
      results.issues.push('❌ hasAccess não está true')
    }
    
    if (debugPanelInfo?.accessType !== 'assinatura') {
      results.issues.push('❌ accessType não é assinatura')
    }
    
    if (results.issues.length > 0) {
      results.success = false
    }
    
    // Relatório final
    console.log('\n📋 RELATÓRIO DO TESTE:')
    console.log('========================')
    console.log(`Email testado: ${testEmail}`)
    console.log(`Status: ${results.success ? '✅ PASSOU' : '❌ FALHOU'}`)
    
    if (results.issues.length > 0) {
      console.log('\nProblemas encontrados:')
      results.issues.forEach(issue => console.log(issue))
    } else {
      console.log('\n✅ Todos os testes passaram!')
      console.log('- Timer não está visível')
      console.log('- Overlay de bloqueio não está visível')
      console.log('- hasAccess = true')
      console.log('- accessType = assinatura')
    }
    
  } catch (error) {
    console.error('❌ Erro durante o teste:', error)
  } finally {
    await browser.close()
  }
}

// Executar o teste
testPremiumFlow()