-- SCRIPT SIMPLIFICADO PARA RESOLVER PROBLEMA DE <PERSON><PERSON><PERSON><PERSON>OS OAUTH
-- Execute este script no SQL Editor do Supabase

-- 1. <PERSON><PERSON>r tipo se não existir
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'subscription_tier') THEN
        CREATE TYPE subscription_tier AS ENUM ('free', 'basic', 'premium', 'vip');
    END IF;
END $$;

-- 2. <PERSON><PERSON><PERSON> tabela para usuários Stack Auth (sem foreign key)
CREATE TABLE IF NOT EXISTS stack_profiles (
  id TEXT PRIMARY KEY,
  email TEXT,
  full_name TEXT,
  avatar_url TEXT,
  subscription_tier subscription_tier DEFAULT 'free',
  subscription_status TEXT DEFAULT 'inactive',
  subscription_current_period_end TIMESTAMPTZ,
  subscription_start_date TIMESTAMPTZ,
  stripe_customer_id TEXT UNIQUE,
  stripe_subscription_id TEXT,
  trial_used BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 3. <PERSON><PERSON><PERSON> índices
CREATE INDEX IF NOT EXISTS idx_stack_profiles_email ON stack_profiles(email);
CREATE INDEX IF NOT EXISTS idx_stack_profiles_stripe_customer_id ON stack_profiles(stripe_customer_id);

-- 4. Habilitar RLS com políticas abertas
ALTER TABLE stack_profiles ENABLE ROW LEVEL SECURITY;

CREATE POLICY "stack_profiles_select" ON stack_profiles FOR SELECT USING (true);
CREATE POLICY "stack_profiles_insert" ON stack_profiles FOR INSERT WITH CHECK (true);
CREATE POLICY "stack_profiles_update" ON stack_profiles FOR UPDATE USING (true);

-- 5. Criar função para verificar acesso (versão simplificada)
CREATE OR REPLACE FUNCTION check_user_access_unified(p_user_id TEXT)
RETURNS TABLE (
  has_access BOOLEAN,
  access_type VARCHAR,
  expires_at TIMESTAMP WITH TIME ZONE,
  subscription_tier VARCHAR
) AS $$
BEGIN
  -- Verificar na tabela stack_profiles
  IF EXISTS (
    SELECT 1 FROM stack_profiles 
    WHERE id = p_user_id 
    AND subscription_tier = 'premium'
    AND subscription_status = 'active'
    AND subscription_current_period_end > NOW()
  ) THEN
    RETURN QUERY
    SELECT 
      true as has_access,
      'assinatura'::VARCHAR as access_type,
      subscription_current_period_end as expires_at,
      subscription_tier::VARCHAR
    FROM stack_profiles
    WHERE id = p_user_id;
    RETURN;
  END IF;

  -- Se não tem acesso
  RETURN QUERY SELECT 
    false as has_access,
    NULL::VARCHAR as access_type,
    NULL::TIMESTAMP WITH TIME ZONE as expires_at,
    'free'::VARCHAR as subscription_tier;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 6. TESTE: Visualizar a tabela criada
SELECT * FROM stack_profiles;

-- 7. TESTE: Testar a função (substitua pelo seu Stack Auth ID)
-- SELECT * FROM check_user_access_unified('SEU_STACK_AUTH_ID_AQUI');

-- PRONTO! Agora teste em https://newspports.com/test-premium.html