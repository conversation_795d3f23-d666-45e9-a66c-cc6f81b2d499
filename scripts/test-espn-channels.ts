#!/usr/bin/env tsx

// Script para testar canais ESPN rapidamente

const ESPN_CHANNELS = [
  { id: 'ESPNews.us', name: 'ESPN News' },
  { id: 'ESPNU.us', name: 'ESPN U' },
  { id: 'ESPN.us', name: 'ESPN' },
  { id: 'ESPN2.us', name: 'ESPN 2' },
]

async function testChannel(channelId: string, channelName: string) {
  console.log(`\n🎯 Testando ${channelName} (${channelId})...`)
  
  try {
    // 1. Testar API
    const apiResponse = await fetch(`http://localhost:3000/api/iptv/channel/${channelId}`)
    const apiData = await apiResponse.json()
    
    if (!apiData.success) {
      console.log(`❌ API falhou: ${apiData.error}`)
      return false
    }
    
    console.log(`✅ API respondeu - URL: ${apiData.data.url}`)
    
    // 2. Testar stream direto
    const streamUrl = apiData.data.url
    const streamResponse = await fetch(streamUrl, {
      method: 'HEAD',
      headers: {
        'Referer': 'https://moveonjoy.com/',
        'Origin': 'https://moveonjoy.com'
      }
    })
    
    if (streamResponse.ok) {
      console.log(`✅ Stream direto funcionando (${streamResponse.status})`)
    } else {
      console.log(`❌ Stream direto falhou (${streamResponse.status})`)
      
      // 3. Testar via proxy
      const proxyUrl = `http://localhost:3000/api/stream-proxy?url=${encodeURIComponent(streamUrl)}`
      const proxyResponse = await fetch(proxyUrl, { method: 'HEAD' })
      
      if (proxyResponse.ok) {
        console.log(`✅ Stream via proxy funcionando (${proxyResponse.status})`)
      } else {
        console.log(`❌ Stream via proxy falhou (${proxyResponse.status})`)
      }
    }
    
    return true
  } catch (error) {
    console.log(`❌ Erro ao testar: ${error}`)
    return false
  }
}

async function main() {
  console.log('🏃 Testando canais ESPN...')
  console.log('═'.repeat(50))
  
  let successCount = 0
  
  for (const channel of ESPN_CHANNELS) {
    const success = await testChannel(channel.id, channel.name)
    if (success) successCount++
  }
  
  console.log('\n' + '═'.repeat(50))
  console.log(`📊 Resultado: ${successCount}/${ESPN_CHANNELS.length} canais funcionando`)
  console.log('═'.repeat(50))
}

main().catch(console.error)