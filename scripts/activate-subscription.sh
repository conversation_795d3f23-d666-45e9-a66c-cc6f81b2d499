#!/bin/bash

echo "🚀 Script de Ativação de Assinatura NewSpports"
echo "=============================================="
echo ""

# Cores para output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Função para verificar se endpoint está disponível
check_endpoint() {
    local url=$1
    local response=$(curl -s -o /dev/null -w "%{http_code}" "$url")
    if [ "$response" = "200" ] || [ "$response" = "401" ]; then
        return 0
    else
        return 1
    fi
}

# Aguardar deploy
echo "⏳ Aguardando deploy finalizar..."
echo "   Isso pode levar 2-3 minutos..."
echo ""

# Verificar se os endpoints estão disponíveis
max_attempts=20
attempt=1

while [ $attempt -le $max_attempts ]; do
    echo -ne "\r🔄 Tentativa $attempt de $max_attempts..."
    
    if check_endpoint "https://newspports.com/api/test-subscription"; then
        echo -e "\n${GREEN}✅ Deploy finalizado!${NC}"
        break
    fi
    
    if [ $attempt -eq $max_attempts ]; then
        echo -e "\n${RED}❌ Timeout aguardando deploy${NC}"
        exit 1
    fi
    
    attempt=$((attempt + 1))
    sleep 10
done

echo ""
echo "📋 Passo 1: Buscando usuário..."
echo "--------------------------------"

# Buscar usuário
USER_RESPONSE=$(curl -s "https://newspports.com/api/find-user")
echo "$USER_RESPONSE" | jq .

echo ""
echo "💳 Passo 2: Ativando assinatura Premium..."
echo "----------------------------------------"

# Ativar assinatura
ACTIVATION_RESPONSE=$(curl -s -X POST "https://newspports.com/api/update-profile" \
  -H "Content-Type: application/json" \
  -H "x-secret-key: update-profile-manual-2025")

echo "$ACTIVATION_RESPONSE" | jq .

# Verificar se foi sucesso
if echo "$ACTIVATION_RESPONSE" | grep -q '"success":true'; then
    echo ""
    echo -e "${GREEN}🎉 SUCESSO! Assinatura ativada!${NC}"
    echo ""
    echo "✅ Próximos passos:"
    echo "   1. Acesse: https://newspports.com/browse"
    echo "   2. Clique em qualquer canal"
    echo "   3. O vídeo deve tocar sem pedir pagamento!"
    echo ""
    echo "📅 Sua assinatura é válida por 1 mês a partir de agora"
else
    echo ""
    echo -e "${RED}❌ Erro ao ativar assinatura${NC}"
    echo "   Verifique os logs acima para mais detalhes"
fi

echo ""
echo "🔍 Passo 3: Verificando status final..."
echo "---------------------------------------"

# Verificar status
sleep 2
FINAL_STATUS=$(curl -s "https://newspports.com/api/test-subscription")
echo "$FINAL_STATUS" | jq .