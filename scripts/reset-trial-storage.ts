#!/usr/bin/env tsx

// Script para resetar o trial storage e corrigir problemas

console.log('🧹 Script de Reset do Trial Storage\n')

// Criar um HTML que executa JavaScript no navegador
const html = `
<!DOCTYPE html>
<html>
<head>
  <title>Reset Trial Storage</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 800px;
      margin: 50px auto;
      padding: 20px;
      background: #1a1a1a;
      color: white;
    }
    .success { color: #4ade80; }
    .error { color: #f87171; }
    .info { color: #60a5fa; }
    button {
      background: #ef4444;
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 5px;
      cursor: pointer;
      font-size: 16px;
      margin: 10px 5px;
    }
    button:hover {
      background: #dc2626;
    }
    pre {
      background: #262626;
      padding: 15px;
      border-radius: 5px;
      overflow-x: auto;
    }
  </style>
</head>
<body>
  <h1>🔧 Reset Trial Storage - NewSpports</h1>
  
  <h2>Estado Atual do Trial</h2>
  <pre id="current-state"></pre>
  
  <h2>Ações Disponíveis</h2>
  <button onclick="resetTrial()">🔄 Resetar Trial Completo</button>
  <button onclick="clearLocalStorage()">🗑️ Limpar Todo LocalStorage</button>
  <button onclick="fixTrialState()">🔧 Corrigir Estado do Trial</button>
  <button onclick="startNewTrial()">🚀 Iniciar Novo Trial</button>
  
  <h2>Log de Ações</h2>
  <pre id="log"></pre>
  
  <script>
    function log(message, type = 'info') {
      const logEl = document.getElementById('log');
      const time = new Date().toLocaleTimeString();
      const colorClass = type === 'error' ? 'error' : type === 'success' ? 'success' : 'info';
      logEl.innerHTML += \`<span class="\${colorClass}">[\${time}] \${message}</span>\\n\`;
    }
    
    function showCurrentState() {
      const trialData = localStorage.getItem('trial-storage');
      const stateEl = document.getElementById('current-state');
      
      if (trialData) {
        try {
          const parsed = JSON.parse(trialData);
          stateEl.textContent = JSON.stringify(parsed, null, 2);
          
          // Analisar estado
          const state = parsed.state;
          if (!state.startTime) {
            log('⚠️ Sem startTime - novo usuário ou estado resetado', 'info');
          } else {
            const elapsed = Math.floor((Date.now() - state.startTime) / 1000);
            const minutes = Math.floor(elapsed / 60);
            const seconds = elapsed % 60;
            log(\`⏱️ Trial iniciado há \${minutes}m \${seconds}s\`, 'info');
            
            if (state.isExpired) {
              log('❌ Trial expirado', 'error');
            } else if (state.timeRemaining > 0) {
              log(\`✅ Trial ativo - \${state.timeRemaining}s restantes\`, 'success');
            }
          }
        } catch (e) {
          stateEl.textContent = 'Erro ao parsear dados: ' + e.message;
        }
      } else {
        stateEl.textContent = 'Nenhum dado de trial encontrado no localStorage';
        log('ℹ️ Nenhum trial storage encontrado', 'info');
      }
    }
    
    function resetTrial() {
      log('🔄 Resetando trial...', 'info');
      
      const newState = {
        state: {
          startTime: null,
          timeRemaining: 300, // 5 minutos
          isExpired: false,
          hasSeenOffer: false,
          sessionId: null,
          trialId: null,
          isLoading: false
        },
        version: 0
      };
      
      localStorage.setItem('trial-storage', JSON.stringify(newState));
      log('✅ Trial resetado com sucesso!', 'success');
      showCurrentState();
    }
    
    function clearLocalStorage() {
      if (confirm('Tem certeza? Isso removerá TODOS os dados do localStorage!')) {
        log('🗑️ Limpando todo localStorage...', 'info');
        localStorage.clear();
        log('✅ LocalStorage limpo!', 'success');
        showCurrentState();
      }
    }
    
    function fixTrialState() {
      log('🔧 Corrigindo estado do trial...', 'info');
      
      const trialData = localStorage.getItem('trial-storage');
      if (!trialData) {
        log('❌ Nenhum dado para corrigir', 'error');
        return;
      }
      
      try {
        const parsed = JSON.parse(trialData);
        const state = parsed.state;
        
        // Corrigir inconsistências
        if (!state.startTime && state.isExpired) {
          log('Corrigindo: isExpired sem startTime', 'info');
          state.isExpired = false;
          state.timeRemaining = 300;
        }
        
        if (state.timeRemaining > 0 && state.isExpired) {
          log('Corrigindo: timeRemaining > 0 mas isExpired', 'info');
          state.isExpired = false;
        }
        
        if (state.startTime) {
          const elapsed = Math.floor((Date.now() - state.startTime) / 1000);
          if (elapsed >= 300) {
            log('Corrigindo: trial deveria estar expirado', 'info');
            state.isExpired = true;
            state.timeRemaining = 0;
          } else {
            const remaining = 300 - elapsed;
            log(\`Corrigindo: tempo restante para \${remaining}s\`, 'info');
            state.timeRemaining = remaining;
            state.isExpired = false;
          }
        }
        
        localStorage.setItem('trial-storage', JSON.stringify(parsed));
        log('✅ Estado corrigido!', 'success');
        showCurrentState();
      } catch (e) {
        log('❌ Erro ao corrigir: ' + e.message, 'error');
      }
    }
    
    function startNewTrial() {
      log('🚀 Iniciando novo trial...', 'info');
      
      const newState = {
        state: {
          startTime: Date.now(),
          timeRemaining: 300, // 5 minutos
          isExpired: false,
          hasSeenOffer: false,
          sessionId: \`session_\${Date.now()}_\${Math.random().toString(36).substr(2, 9)}\`,
          trialId: null,
          isLoading: false
        },
        version: 0
      };
      
      localStorage.setItem('trial-storage', JSON.stringify(newState));
      log('✅ Novo trial iniciado!', 'success');
      log('📺 Agora você pode assistir por 5 minutos', 'success');
      showCurrentState();
    }
    
    // Mostrar estado atual ao carregar
    showCurrentState();
  </script>
</body>
</html>
`

// Salvar arquivo HTML
import fs from 'fs'
import path from 'path'

const filePath = path.join(process.cwd(), 'reset-trial.html')
fs.writeFileSync(filePath, html)

console.log('✅ Arquivo criado: reset-trial.html')
console.log('\n📝 Instruções:')
console.log('1. Abra o arquivo no navegador: file://' + filePath)
console.log('2. Use os botões para resetar ou corrigir o trial')
console.log('3. Recarregue o site após fazer as alterações')
console.log('\n⚠️ IMPORTANTE: Execute isso no mesmo navegador/domínio onde está o problema!')