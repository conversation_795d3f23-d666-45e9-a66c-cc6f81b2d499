#!/usr/bin/env tsx

async function testChannelAPI() {
  const baseUrl = 'http://localhost:3000'
  const testChannels = [
    '30AGolfKingdom.us',
    'iptv-30AGolfKingdom.us',
    'ACCDigitalNetwork.us',
    'Adjarasport1.ge',
    'AntenaSport.hr', // Este não existe na API
    'ESPNews.us',
    'ESPNU.us'
  ]
  
  console.log('🔍 Testing Channel API\n')
  console.log(`Base URL: ${baseUrl}\n`)
  
  for (const channelId of testChannels) {
    console.log(`\nTesting: ${channelId}`)
    console.log('-'.repeat(50))
    
    try {
      const url = `${baseUrl}/api/iptv/channel/${channelId}`
      console.log(`URL: ${url}`)
      
      const response = await fetch(url)
      const data = await response.json()
      
      console.log(`Status: ${response.status}`)
      console.log(`Success: ${data.success}`)
      
      if (data.success && data.data) {
        console.log(`Channel name: ${data.data.name}`)
        console.log(`Channel ID: ${data.data.id}`)
        console.log(`URL: ${data.data.url?.substring(0, 80)}...`)
        console.log(`Has headers: ${!!data.data.headers}`)
        console.log(`Requires proxy: ${data.data.requiresProxy}`)
      } else {
        console.log(`Error: ${data.error}`)
      }
    } catch (error: any) {
      console.error(`Request failed: ${error.message}`)
    }
  }
  
  console.log('\n\n=== Testing /api/iptv-simple ===')
  console.log('-'.repeat(50))
  
  try {
    const response = await fetch(`${baseUrl}/api/iptv-simple`)
    const data = await response.json()
    
    console.log(`Status: ${response.status}`)
    console.log(`Success: ${data.success}`)
    console.log(`Total channels: ${data.total}`)
    
    if (data.data && data.data.length > 0) {
      console.log('\nFirst 5 channels:')
      data.data.slice(0, 5).forEach((ch: any, i: number) => {
        console.log(`\n${i + 1}. ${ch.name || ch.channel}`)
        console.log(`   ID: ${ch.channel || ch.uniqueId}`)
        console.log(`   URL: ${ch.url?.substring(0, 60)}...`)
      })
    }
  } catch (error: any) {
    console.error(`Request failed: ${error.message}`)
  }
}

testChannelAPI()