#!/usr/bin/env node

/**
 * Script para resetar as quotas de API no localStorage
 * Execute este script para limpar os limites e permitir novas requisições
 */

console.log('🔄 Resetando quotas de API...')

// Simula o ambiente do navegador para executar no Node.js
if (typeof localStorage === 'undefined') {
  console.log('⚠️  Este script deve ser executado no console do navegador!')
  console.log('\n📋 Copie e cole o seguinte código no console do navegador:\n')
  console.log(`
// Limpar quotas de API
localStorage.removeItem('sports-api-quotas');
console.log('✅ Quotas de API resetadas com sucesso!');

// Verificar se foi removido
if (!localStorage.getItem('sports-api-quotas')) {
  console.log('✅ Confirmado: quotas foram limpas');
} else {
  console.log('❌ Erro: quotas ainda existem');
}

// Recarregar a página para aplicar as mudanças
location.reload();
  `)
} else {
  // Se executado no navegador
  localStorage.removeItem('sports-api-quotas');
  console.log('✅ Quotas de API resetadas com sucesso!');
  location.reload();
}