#!/bin/bash

echo "=== TESTE DO SISTEMA STRIPE NO LOCALHOST ==="
echo ""

# Cores para output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Base URL
BASE_URL="http://localhost:3000"

echo -e "${YELLOW}1. Verificando se o servidor está rodando...${NC}"
curl -s -o /dev/null -w "%{http_code}" $BASE_URL > /tmp/server_check.txt
SERVER_STATUS=$(cat /tmp/server_check.txt)

if [ "$SERVER_STATUS" = "200" ]; then
    echo -e "${GREEN}✓ Servidor rodando${NC}"
else
    echo -e "${RED}✗ Servidor não está respondendo em $BASE_URL${NC}"
    exit 1
fi

echo ""
echo -e "${YELLOW}2. Testando endpoint de debug do Stripe...${NC}"
echo "Acesse: $BASE_URL/api/debug/stripe-status"
echo ""

echo -e "${YELLOW}3. Para testar o webhook localmente:${NC}"
echo ""
echo "a) Instale o Stripe CLI:"
echo "   brew install stripe/stripe-cli/stripe"
echo ""
echo "b) Faça login no Stripe:"
echo "   stripe login"
echo ""
echo "c) Encaminhe webhooks para localhost:"
echo "   stripe listen --forward-to localhost:3000/api/stripe/webhook"
echo ""
echo "d) Em outro terminal, dispare um evento de teste:"
echo "   stripe trigger checkout.session.completed"
echo ""

echo -e "${YELLOW}4. Verificando estrutura do banco de dados...${NC}"
echo ""
echo "Execute estas queries no Supabase SQL Editor:"
echo ""
cat << 'EOF'
-- Verificar se as tabelas foram criadas
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('stripe_subscriptions', 'stripe_webhook_events', 'payment_history')
ORDER BY table_name;

-- Verificar campos nas tabelas de perfil
SELECT column_name, data_type, is_nullable
FROM information_schema.columns
WHERE table_schema = 'public' 
AND table_name IN ('profiles', 'stack_profiles')
AND column_name LIKE '%stripe%' OR column_name LIKE '%subscription%'
ORDER BY table_name, column_name;

-- Verificar funções criadas
SELECT routine_name, routine_type
FROM information_schema.routines
WHERE routine_schema = 'public'
AND routine_name IN ('sync_stripe_subscription', 'process_stripe_webhook');
EOF

echo ""
echo -e "${YELLOW}5. Para monitorar logs em tempo real:${NC}"
echo "   npm run dev"
echo ""
echo "   Procure por logs com prefixo [STRIPE-WEBHOOK]"
echo ""

echo -e "${YELLOW}6. URLs úteis para teste:${NC}"
echo "   - Debug Stripe: $BASE_URL/api/debug/stripe-status"
echo "   - Página de assinatura: $BASE_URL/subscription"
echo "   - Browse (verificar badge premium): $BASE_URL/browse"
echo ""

echo -e "${GREEN}=== TESTE FINALIZADO ===${NC}"