import { premiumFootballChannels } from '../src/lib/premium-football-channels'

// ANSI color codes
const colors = {
  reset: '\x1b[0m',
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  gray: '\x1b[90m',
  cyan: '\x1b[36m',
  magenta: '\x1b[35m'
}

interface TestResult {
  channelId: string
  channelName: string
  category: string
  streamUrl: string
  quality: string
  status: 'working' | 'error'
  error?: string
  responseTime?: number
  headers?: Record<string, string>
  contentType?: string
}

async function testStream(url: string, headers?: Record<string, string>): Promise<{
  working: boolean
  error?: string
  responseTime?: number
  headers?: Record<string, string>
  contentType?: string
}> {
  const startTime = Date.now()
  
  try {
    const controller = new AbortController()
    const timeout = setTimeout(() => controller.abort(), 10000) // 10 second timeout
    
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        ...headers
      },
      signal: controller.signal
    })
    
    clearTimeout(timeout)
    const responseTime = Date.now() - startTime
    
    const contentType = response.headers.get('content-type') || ''
    const responseHeaders: Record<string, string> = {}
    response.headers.forEach((value, key) => {
      responseHeaders[key] = value
    })
    
    if (response.ok) {
      // Check if it's a valid stream format
      const validContentTypes = [
        'application/vnd.apple.mpegurl',
        'application/x-mpegurl',
        'video/mp2t',
        'video/mp4',
        'application/octet-stream',
        'text/html' // Some servers return HTML but still work
      ]
      
      const isValidContentType = validContentTypes.some(type => contentType.includes(type)) || contentType === ''
      
      if (isValidContentType || response.status === 200) {
        return { 
          working: true, 
          responseTime,
          headers: responseHeaders,
          contentType
        }
      } else {
        return { 
          working: false, 
          error: `Invalid content type: ${contentType}`,
          responseTime,
          headers: responseHeaders,
          contentType
        }
      }
    } else {
      return { 
        working: false, 
        error: `HTTP ${response.status}: ${response.statusText}`,
        responseTime,
        headers: responseHeaders,
        contentType
      }
    }
  } catch (error) {
    const responseTime = Date.now() - startTime
    if (error instanceof Error) {
      if (error.name === 'AbortError') {
        return { working: false, error: 'Timeout (10s)', responseTime }
      }
      return { working: false, error: error.message, responseTime }
    }
    return { working: false, error: 'Unknown error', responseTime }
  }
}

async function testPremiumFootballChannels() {
  console.log(`\n${colors.cyan}═══════════════════════════════════════════════════════════════${colors.reset}`)
  console.log(`${colors.cyan}     TESTANDO CANAIS PREMIUM DE FUTEBOL - NEWSPPORTS${colors.reset}`)
  console.log(`${colors.cyan}═══════════════════════════════════════════════════════════════${colors.reset}\n`)
  
  const targetChannels = [
    'real-madrid-tv',
    'barca-tv',
    'sevilla-fc-tv',
    'las-palmas-tv',
    'teledeporte',
    'esport3'
  ]
  
  const results: TestResult[] = []
  const channelsToTest = premiumFootballChannels.filter(ch => targetChannels.includes(ch.id))
  
  console.log(`${colors.blue}Canais a testar:${colors.reset} ${channelsToTest.length}`)
  console.log(`${colors.blue}Foco em:${colors.reset} ${targetChannels.join(', ')}\n`)
  
  for (const channel of channelsToTest) {
    console.log(`${colors.yellow}▶ Testando:${colors.reset} ${channel.name} (${channel.category})`)
    
    for (const stream of channel.streams) {
      const testResult = await testStream(stream.url, stream.headers)
      
      const result: TestResult = {
        channelId: channel.id,
        channelName: channel.name,
        category: channel.category,
        streamUrl: stream.url,
        quality: stream.quality || 'SD',
        status: testResult.working ? 'working' : 'error',
        error: testResult.error,
        responseTime: testResult.responseTime,
        headers: testResult.headers,
        contentType: testResult.contentType
      }
      
      results.push(result)
      
      if (testResult.working) {
        console.log(`  ${colors.green}✓${colors.reset} ${stream.quality || 'SD'}: Funcionando`)
        console.log(`    ${colors.gray}Tempo de resposta: ${testResult.responseTime}ms${colors.reset}`)
        console.log(`    ${colors.gray}Content-Type: ${testResult.contentType}${colors.reset}`)
      } else {
        console.log(`  ${colors.red}✗${colors.reset} ${stream.quality || 'SD'}: ${testResult.error}`)
      }
    }
    
    console.log('')
  }
  
  // Summary
  console.log(`${colors.cyan}═══════════════════════════════════════════════════════════════${colors.reset}`)
  console.log(`${colors.cyan}                         RESUMO${colors.reset}`)
  console.log(`${colors.cyan}═══════════════════════════════════════════════════════════════${colors.reset}\n`)
  
  const workingChannels = results.filter(r => r.status === 'working')
  const errorChannels = results.filter(r => r.status === 'error')
  
  console.log(`${colors.green}✓ Funcionando:${colors.reset} ${workingChannels.length} streams`)
  console.log(`${colors.red}✗ Com erro:${colors.reset} ${errorChannels.length} streams\n`)
  
  // Group by channel
  const channelGroups = new Map<string, TestResult[]>()
  results.forEach(result => {
    if (!channelGroups.has(result.channelId)) {
      channelGroups.set(result.channelId, [])
    }
    channelGroups.get(result.channelId)!.push(result)
  })
  
  console.log(`${colors.blue}Status por Canal:${colors.reset}`)
  channelGroups.forEach((channelResults, channelId) => {
    const channel = premiumFootballChannels.find(ch => ch.id === channelId)!
    const working = channelResults.filter(r => r.status === 'working').length
    const total = channelResults.length
    
    const statusIcon = working > 0 ? colors.green + '✓' : colors.red + '✗'
    const statusText = working > 0 ? colors.green : colors.red
    
    console.log(`${statusIcon}${colors.reset} ${channel.name}: ${statusText}${working}/${total} streams funcionando${colors.reset}`)
    
    if (working === 0) {
      channelResults.forEach(result => {
        console.log(`   ${colors.gray}└─ ${result.error}${colors.reset}`)
      })
    }
  })
  
  // Working channels details
  if (workingChannels.length > 0) {
    console.log(`\n${colors.green}Canais Funcionando:${colors.reset}`)
    workingChannels.forEach(result => {
      console.log(`  • ${result.channelName} (${result.quality})`)
      console.log(`    ${colors.gray}URL: ${result.streamUrl.substring(0, 60)}...${colors.reset}`)
      console.log(`    ${colors.gray}Tempo: ${result.responseTime}ms${colors.reset}`)
    })
  }
  
  // Save results to file
  const report = {
    timestamp: new Date().toISOString(),
    totalTested: results.length,
    working: workingChannels.length,
    error: errorChannels.length,
    channels: Array.from(channelGroups.entries()).map(([channelId, channelResults]) => {
      const channel = premiumFootballChannels.find(ch => ch.id === channelId)!
      return {
        id: channelId,
        name: channel.name,
        category: channel.category,
        streams: channelResults.map(r => ({
          url: r.streamUrl,
          quality: r.quality,
          status: r.status,
          error: r.error,
          responseTime: r.responseTime,
          contentType: r.contentType
        }))
      }
    })
  }
  
  console.log(`\n${colors.cyan}Relatório salvo em:${colors.reset} premium-football-channels-test-results.json`)
  const fs = await import('fs/promises')
  await fs.writeFile('./premium-football-channels-test-results.json', JSON.stringify(report, null, 2))
}

// Run the test
testPremiumFootballChannels().catch(console.error)