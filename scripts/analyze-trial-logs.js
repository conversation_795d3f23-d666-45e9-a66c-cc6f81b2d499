#!/usr/bin/env node

/**
 * Script para análise de logs do sistema de trial
 * Identifica problemas no fluxo de bloqueio após 5 minutos
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 ANÁLISE DE LOGS DO SISTEMA DE TRIAL\n');
console.log('=' .repeat(80));

// Padrões de log importantes
const logPatterns = {
  trialStore: /\[TRIAL-STORE\]/,
  trialTimer: /\[TRIAL-TIMER\]/,
  trialPlayer: /\[TRIAL-PLAYER\]/,
  videoPlayer: /\[VIDEO-PLAYER\]/,
  accessStore: /\[ACCESS-STORE\]/,
  
  // Eventos críticos
  trialStart: /startTrial called|Starting NEW TRIAL/,
  trialExpired: /TRIAL EXPIRED|Trial expired/,
  blockDecision: /BLOCK DECISION|shouldBlock/,
  accessCheck: /CHECK ACCESS|checkAccess called/,
  
  // Estados
  startTime: /startTime[:\s]*(\d+|null)/,
  timeRemaining: /timeRemaining[:\s]*(\d+)/,
  isExpired: /isExpired[:\s]*(true|false)/,
  hasAccess: /hasAccess[:\s]*(true|false)/,
  isBlocked: /isBlocked[:\s]*(true|false)/
};

// Analisar problemas comuns
function analyzeIssues(logs) {
  const issues = [];
  
  // 1. Verificar se trial está iniciando
  const trialStartLogs = logs.filter(log => logPatterns.trialStart.test(log));
  if (trialStartLogs.length === 0) {
    issues.push({
      severity: 'CRITICAL',
      issue: 'Trial não está sendo iniciado',
      detail: 'Nenhum log de "startTrial called" encontrado'
    });
  }
  
  // 2. Verificar se timer está funcionando
  const timerLogs = logs.filter(log => logPatterns.trialTimer.test(log));
  if (timerLogs.length === 0) {
    issues.push({
      severity: 'CRITICAL',
      issue: 'Timer não está executando',
      detail: 'Nenhum log [TRIAL-TIMER] encontrado'
    });
  }
  
  // 3. Verificar se bloqueio está ocorrendo
  const blockLogs = logs.filter(log => logPatterns.blockDecision.test(log));
  const blockedLogs = blockLogs.filter(log => /shouldBlock[:\s]*true/.test(log));
  if (blockedLogs.length === 0) {
    issues.push({
      severity: 'CRITICAL',
      issue: 'Bloqueio não está sendo aplicado',
      detail: 'Nenhum log com shouldBlock: true encontrado'
    });
  }
  
  // 4. Verificar se access check está interferindo
  const accessLogs = logs.filter(log => logPatterns.accessStore.test(log));
  const hasAccessTrue = accessLogs.filter(log => /hasAccess[:\s]*true/.test(log));
  if (hasAccessTrue.length > 0 && !hasAccessTrue.some(log => /accessType[:\s]*assinatura/.test(log))) {
    issues.push({
      severity: 'WARNING',
      issue: 'Access store pode estar dando acesso incorreto',
      detail: 'hasAccess: true encontrado sem ser assinatura'
    });
  }
  
  // 5. Verificar se startTime está sendo definido
  const startTimeLogs = logs.filter(log => /startTime[:\s]*null/.test(log));
  if (startTimeLogs.length > 10) {
    issues.push({
      severity: 'ERROR',
      issue: 'startTime permanece null',
      detail: `${startTimeLogs.length} logs com startTime: null`
    });
  }
  
  return issues;
}

// Extrair timeline de eventos
function extractTimeline(logs) {
  const timeline = [];
  
  logs.forEach((log, index) => {
    // Extrair timestamp se disponível
    const timestampMatch = log.match(/(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}.\d{3}Z)/);
    const timestamp = timestampMatch ? timestampMatch[1] : `Line ${index + 1}`;
    
    // Eventos importantes
    if (logPatterns.trialStart.test(log)) {
      timeline.push({ timestamp, event: 'TRIAL_START', detail: log.trim() });
    }
    if (logPatterns.trialExpired.test(log)) {
      timeline.push({ timestamp, event: 'TRIAL_EXPIRED', detail: log.trim() });
    }
    if (logPatterns.blockDecision.test(log)) {
      const shouldBlock = /shouldBlock[:\s]*true/.test(log);
      timeline.push({ 
        timestamp, 
        event: shouldBlock ? 'BLOCKING_USER' : 'NOT_BLOCKING', 
        detail: log.trim() 
      });
    }
    if (/checkAccessAndTrial triggered/.test(log)) {
      timeline.push({ timestamp, event: 'ACCESS_CHECK_START', detail: log.trim() });
    }
  });
  
  return timeline;
}

// Analisar fluxo de usuário
function analyzeUserFlow(logs) {
  const flow = {
    trialInitiated: false,
    timerStarted: false,
    timerExpired: false,
    blockApplied: false,
    hasAccessChecked: false,
    incorrectAccess: false
  };
  
  logs.forEach(log => {
    if (logPatterns.trialStart.test(log)) flow.trialInitiated = true;
    if (/Timer update/.test(log) && logPatterns.trialTimer.test(log)) flow.timerStarted = true;
    if (/TRIAL EXPIRED/.test(log)) flow.timerExpired = true;
    if (/shouldBlock[:\s]*true/.test(log)) flow.blockApplied = true;
    if (/CHECK ACCESS COMPLETED/.test(log)) flow.hasAccessChecked = true;
    if (/hasAccess[:\s]*true/.test(log) && !/assinatura/.test(log)) flow.incorrectAccess = true;
  });
  
  return flow;
}

// Função principal de análise
function analyzeLogs(logContent) {
  const logs = logContent.split('\n').filter(line => line.trim());
  
  console.log(`\n📊 ESTATÍSTICAS GERAIS:`);
  console.log(`- Total de linhas: ${logs.length}`);
  console.log(`- Logs [TRIAL-STORE]: ${logs.filter(l => logPatterns.trialStore.test(l)).length}`);
  console.log(`- Logs [TRIAL-TIMER]: ${logs.filter(l => logPatterns.trialTimer.test(l)).length}`);
  console.log(`- Logs [TRIAL-PLAYER]: ${logs.filter(l => logPatterns.trialPlayer.test(l)).length}`);
  console.log(`- Logs [VIDEO-PLAYER]: ${logs.filter(l => logPatterns.videoPlayer.test(l)).length}`);
  console.log(`- Logs [ACCESS-STORE]: ${logs.filter(l => logPatterns.accessStore.test(l)).length}`);
  
  // Analisar problemas
  console.log(`\n🚨 PROBLEMAS IDENTIFICADOS:`);
  const issues = analyzeIssues(logs);
  if (issues.length === 0) {
    console.log('✅ Nenhum problema crítico identificado');
  } else {
    issues.forEach(issue => {
      const icon = issue.severity === 'CRITICAL' ? '❌' : issue.severity === 'ERROR' ? '⚠️' : '⚡';
      console.log(`${icon} [${issue.severity}] ${issue.issue}`);
      console.log(`   → ${issue.detail}`);
    });
  }
  
  // Timeline de eventos
  console.log(`\n📅 TIMELINE DE EVENTOS PRINCIPAIS:`);
  const timeline = extractTimeline(logs);
  timeline.slice(-10).forEach(event => {
    const icon = {
      'TRIAL_START': '🎬',
      'TRIAL_EXPIRED': '⏰',
      'BLOCKING_USER': '🚫',
      'NOT_BLOCKING': '✅',
      'ACCESS_CHECK_START': '🔍'
    }[event.event] || '📌';
    console.log(`${icon} ${event.timestamp} - ${event.event}`);
  });
  
  // Análise do fluxo
  console.log(`\n🔄 ANÁLISE DO FLUXO DO USUÁRIO:`);
  const flow = analyzeUserFlow(logs);
  console.log(`- Trial iniciado: ${flow.trialInitiated ? '✅' : '❌'}`);
  console.log(`- Timer iniciado: ${flow.timerStarted ? '✅' : '❌'}`);
  console.log(`- Timer expirado: ${flow.timerExpired ? '✅' : '❌'}`);
  console.log(`- Bloqueio aplicado: ${flow.blockApplied ? '✅' : '❌'}`);
  console.log(`- Access verificado: ${flow.hasAccessChecked ? '✅' : '❌'}`);
  console.log(`- Acesso incorreto: ${flow.incorrectAccess ? '⚠️ SIM' : '✅ NÃO'}`);
  
  // Diagnóstico final
  console.log(`\n🔍 DIAGNÓSTICO FINAL:`);
  if (!flow.trialInitiated) {
    console.log('❌ PROBLEMA CRÍTICO: Trial não está sendo iniciado quando usuário clica no canal');
    console.log('   → Verificar se checkAccessAndTrial está sendo chamado no VideoPlayer');
    console.log('   → Verificar se startTrial está sendo chamado para usuários sem acesso');
  } else if (!flow.timerStarted) {
    console.log('❌ PROBLEMA CRÍTICO: Timer não está sendo executado');
    console.log('   → Verificar useTrialTimer hook');
    console.log('   → Verificar se startTime está sendo definido corretamente');
  } else if (!flow.timerExpired && flow.timerStarted) {
    console.log('⚠️ Timer iniciou mas não expirou ainda (pode ser normal se < 5 min)');
  } else if (flow.timerExpired && !flow.blockApplied) {
    console.log('❌ PROBLEMA CRÍTICO: Timer expirou mas bloqueio não foi aplicado');
    console.log('   → Verificar lógica de bloqueio no VideoPlayer');
    console.log('   → Verificar se hasAccess está sendo incorretamente definido');
  } else if (flow.incorrectAccess) {
    console.log('⚠️ PROBLEMA: Sistema pode estar dando acesso incorreto');
    console.log('   → Verificar checkAccess no access.store.ts');
    console.log('   → Verificar RPC check_user_access_unified no Supabase');
  } else {
    console.log('✅ Fluxo parece estar funcionando corretamente');
  }
  
  // Recomendações
  console.log(`\n💡 RECOMENDAÇÕES:`);
  console.log('1. Adicionar mais logs no início do fluxo (quando usuário clica no canal)');
  console.log('2. Verificar se localStorage está interferindo com o estado inicial');
  console.log('3. Adicionar logs no momento exato do clique para rastrear o início do trial');
  console.log('4. Verificar se há condições que impedem startTrial de ser chamado');
  console.log('5. Testar com localStorage limpo (aba anônima)');
}

// Executar análise
const logFile = process.argv[2];
if (logFile && fs.existsSync(logFile)) {
  console.log(`\n📄 Analisando arquivo: ${logFile}`);
  const content = fs.readFileSync(logFile, 'utf8');
  analyzeLogs(content);
} else {
  console.log(`\n📋 INSTRUÇÕES DE USO:`);
  console.log('1. Abra o site em uma aba anônima');
  console.log('2. Abra o console do navegador (F12)');
  console.log('3. Limpe o console');
  console.log('4. Clique em um canal para assistir');
  console.log('5. Aguarde 5-6 minutos');
  console.log('6. Copie todos os logs do console');
  console.log('7. Salve em um arquivo .txt');
  console.log('8. Execute: node scripts/analyze-trial-logs.js <arquivo.txt>');
  
  console.log(`\n🔍 LOGS IMPORTANTES PARA PROCURAR:`);
  console.log('- [TRIAL-STORE] startTrial called');
  console.log('- [TRIAL-TIMER] Timer update');
  console.log('- [TRIAL-PLAYER] CHECK ACCESS AND TRIAL START');
  console.log('- [TRIAL-PLAYER] BLOCK DECISION');
  console.log('- [VIDEO-PLAYER] Component rendered');
  console.log('- [ACCESS-STORE] CHECK ACCESS CALLED');
}