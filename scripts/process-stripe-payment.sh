#!/bin/bash

# Script para processar pagamento do Stripe manualmente
# Uso: ./scripts/process-stripe-payment.sh <customer_id> <subscription_id> <user_email>

if [ -z "$1" ] || [ -z "$2" ] || [ -z "$3" ]; then
    echo "❌ Por favor, forneça todos os parâmetros"
    echo "Uso: $0 <customer_id> <subscription_id> <user_email>"
    echo ""
    echo "Exemplo:"
    echo "$0 cus_123456789 sub_987654321 <EMAIL>"
    exit 1
fi

CUSTOMER_ID=$1
SUBSCRIPTION_ID=$2
USER_EMAIL=$3
API_URL="${NEXT_PUBLIC_APP_URL:-http://localhost:3000}"

echo "🔄 Processando pagamento do Stripe"
echo "=================================="
echo "Customer ID: $CUSTOMER_ID"
echo "Subscription ID: $SUBSCRIPTION_ID"
echo "User Email: $USER_EMAIL"
echo "API URL: $API_URL"
echo ""

# Criar payload simulando o webhook do Stripe
WEBHOOK_PAYLOAD=$(cat << EOF
{
  "id": "evt_test_webhook",
  "object": "event",
  "api_version": "2023-10-16",
  "created": $(date +%s),
  "data": {
    "object": {
      "id": "cs_test_123",
      "object": "checkout.session",
      "mode": "subscription",
      "customer": "$CUSTOMER_ID",
      "subscription": "$SUBSCRIPTION_ID",
      "payment_status": "paid",
      "status": "complete"
    }
  },
  "livemode": false,
  "pending_webhooks": 1,
  "request": {
    "id": null,
    "idempotency_key": null
  },
  "type": "checkout.session.completed"
}
EOF
)

echo "📝 Enviando evento para o webhook..."
echo ""

# Enviar para o endpoint do webhook
curl -X POST "$API_URL/api/stripe/webhook" \
  -H "Content-Type: application/json" \
  -H "stripe-signature: test_signature" \
  -d "$WEBHOOK_PAYLOAD" \
  -w "\nHTTP Status: %{http_code}\n"

echo ""
echo ""
echo "🔍 Verificando status da assinatura..."
echo ""

# Aguardar processamento
sleep 2

# Verificar status da assinatura
curl -s "$API_URL/api/stripe/subscription-status?email=$USER_EMAIL" | jq .

echo ""
echo ""
echo "✅ Processo concluído!"
echo ""
echo "📋 Próximos passos:"
echo "1. Verifique os logs do servidor para erros"
echo "2. Confirme no banco de dados se os campos foram atualizados"
echo "3. Teste fazendo login com o usuário"