#!/bin/bash

echo "🚀 Testando Trial Timer System..."
echo "================================"

# Função para tirar screenshot
take_screenshot() {
    local name=$1
    local desc=$2
    echo "📸 Tirando screenshot: $desc"
    screencapture -x "trial-timer-tests/${name}.png"
    echo "✅ Salvo em: trial-timer-tests/${name}.png"
    echo ""
}

# Criar diretório para screenshots
mkdir -p trial-timer-tests

# Limpar localStorage via console do navegador
echo "🧹 Limpando dados de trial anterior..."
echo "Por favor, abra o navegador em http://localhost:3001"
echo "Abra o console (F12) e execute: localStorage.removeItem('trial-storage')"
echo "Pressione Enter quando concluído..."
read

# Teste 1: Timer inicial
echo "📺 TESTE 1: Timer Inicial (5:00)"
echo "Acesse: http://localhost:3001/watch/real-madrid-tv"
echo "Verifique se o timer aparece mostrando 'Trial: 5:00'"
echo "Pressione Enter para tirar screenshot..."
read
take_screenshot "1-timer-inicial" "Timer inicial mostrando 5:00"

# Teste 2: Timer contando
echo "📺 TESTE 2: Timer Contando"
echo "Clique no botão play e aguarde 15 segundos"
echo "O timer deve estar contando regressivamente"
echo "Pressione Enter para tirar screenshot..."
read
take_screenshot "2-timer-contando" "Timer em contagem regressiva"

# Teste 3: Persistência após reload
echo "📺 TESTE 3: Persistência após Reload"
echo "Recarregue a página (F5)"
echo "O timer deve continuar de onde parou, NÃO reiniciar para 5:00"
echo "Pressione Enter para tirar screenshot..."
read
take_screenshot "3-persistencia-reload" "Timer persistente após reload"

# Teste 4: Timer em múltiplos canais
echo "📺 TESTE 4: Múltiplos Canais"
echo "Acesse: http://localhost:3001/watch/teledeporte"
echo "O timer deve continuar contando, não reiniciar"
echo "Pressione Enter para tirar screenshot..."
read
take_screenshot "4-multiplos-canais" "Timer consistente entre canais"

# Teste 5: Warning < 60 segundos
echo "📺 TESTE 5: Warning (< 1:00)"
echo "NOTA: Este teste pode demorar alguns minutos"
echo "Aguarde até o timer ficar abaixo de 1:00"
echo "O timer deve ficar vermelho e pulsar"
echo "Pressione Enter quando o timer estiver < 1:00..."
read
take_screenshot "5-timer-warning" "Timer em modo warning (vermelho pulsante)"

# Teste 6: Expiração
echo "📺 TESTE 6: Trial Expirado"
echo "Aguarde o timer chegar a 0:00"
echo "Deve aparecer um overlay de conversão"
echo "Pressione Enter quando o trial expirar..."
read
take_screenshot "6-trial-expirado" "Overlay de trial expirado"

echo "✅ TESTE CONCLUÍDO!"
echo "Screenshots salvos em: trial-timer-tests/"
echo ""
echo "📊 RESUMO DOS TESTES:"
echo "1. Timer inicial (5:00) - Verificar se aparece corretamente"
echo "2. Timer contando - Verificar contagem regressiva"
echo "3. Persistência - Timer não reinicia após reload"
echo "4. Múltiplos canais - Timer consistente entre canais"
echo "5. Warning < 1:00 - Animação vermelha pulsante"
echo "6. Expiração - Overlay de conversão aparece"

# Listar screenshots
echo ""
echo "📁 Screenshots criados:"
ls -la trial-timer-tests/