const { createClient } = require('@supabase/supabase-js')
require('dotenv').config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing required environment variables')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function applyMigration() {
  console.log('🚀 Aplicando migração para configuração do app móvel...')
  
  try {
    // Primeiro, vamos tentar criar a tabela usando inserção direta
    // Se a tabela não existir, isso falhará, mas não importa
    const { data: existingConfig, error: checkError } = await supabase
      .from('mobile_app_config')
      .select('*')
      .limit(1)
    
    if (checkError && checkError.code === '42P01') {
      console.log('❌ Tabela mobile_app_config não existe.')
      console.log('\n📝 Por favor, execute o seguinte SQL no Supabase Dashboard:')
      console.log('\n=== COPIE E COLE NO SQL EDITOR DO SUPABASE ===\n')
      console.log(`-- Criar tabela para configurações do app móvel
CREATE TABLE IF NOT EXISTS public.mobile_app_config (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  android_url TEXT NOT NULL,
  ios_url TEXT,
  qr_code_url TEXT,
  tutorial_video_url TEXT,
  tutorial_video_file TEXT,
  android_badge_url TEXT DEFAULT 'https://play.google.com/intl/en_us/badges/static/images/badges/es_badge_web_generic.png',
  ios_badge_url TEXT DEFAULT 'https://developer.apple.com/assets/elements/badges/download-on-the-app-store.svg',
  ios_available BOOLEAN DEFAULT false,
  android_available BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Inserir configuração padrão
INSERT INTO public.mobile_app_config (
  android_url,
  ios_url,
  qr_code_url
) VALUES (
  'https://play.google.com/store/apps/details?id=com.streamplus.espana',
  '#',
  'https://play.google.com/store/apps/details?id=com.streamplus.espana'
);

-- Habilitar RLS
ALTER TABLE public.mobile_app_config ENABLE ROW LEVEL SECURITY;

-- Política para leitura pública
CREATE POLICY "Mobile app config readable by all" ON public.mobile_app_config
  FOR SELECT USING (true);

-- Política para edição (temporariamente permitindo todos)
CREATE POLICY "Mobile app config editable by all" ON public.mobile_app_config
  FOR ALL USING (true);

-- Função para atualizar timestamp
CREATE OR REPLACE FUNCTION public.update_mobile_app_config_timestamp()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger para atualizar timestamp
CREATE TRIGGER update_mobile_app_config_timestamp
BEFORE UPDATE ON public.mobile_app_config
FOR EACH ROW
EXECUTE FUNCTION public.update_mobile_app_config_timestamp();`)
      
      console.log('\n=== FIM DO SQL ===\n')
      console.log('📍 Passos:')
      console.log('1. Acesse: https://supabase.com/dashboard/project/bgeecdlhnwkgdujoyont/sql')
      console.log('2. Cole o SQL acima')
      console.log('3. Clique em "Run"')
      console.log('4. Execute este script novamente para verificar')
      return
    }
    
    if (existingConfig && existingConfig.length > 0) {
      console.log('✅ Tabela mobile_app_config já existe!')
      console.log('📱 Configuração atual:')
      console.log(`   - Android URL: ${existingConfig[0].android_url}`)
      console.log(`   - iOS URL: ${existingConfig[0].ios_url}`)
      console.log(`   - QR Code URL: ${existingConfig[0].qr_code_url}`)
      console.log(`   - Android disponível: ${existingConfig[0].android_available}`)
      console.log(`   - iOS disponível: ${existingConfig[0].ios_available}`)
    } else {
      console.log('⚠️ Tabela existe mas está vazia. Inserindo configuração padrão...')
      
      const { data, error } = await supabase
        .from('mobile_app_config')
        .insert({
          android_url: 'https://play.google.com/store/apps/details?id=com.streamplus.espana',
          ios_url: '#',
          qr_code_url: 'https://play.google.com/store/apps/details?id=com.streamplus.espana'
        })
        .select()
      
      if (error) {
        console.error('❌ Erro ao inserir configuração:', error.message)
      } else {
        console.log('✅ Configuração padrão inserida com sucesso!')
      }
    }
    
    // Verificar bucket de storage
    console.log('\n📦 Verificando bucket de storage...')
    const { data: buckets, error: bucketError } = await supabase
      .storage
      .listBuckets()
    
    if (bucketError) {
      console.error('❌ Erro ao listar buckets:', bucketError.message)
    } else {
      const tutorialBucket = buckets.find(b => b.id === 'tutorial-videos')
      if (tutorialBucket) {
        console.log('✅ Bucket tutorial-videos já existe!')
      } else {
        console.log('⚠️ Bucket tutorial-videos não existe.')
        console.log('   Execute o seguinte SQL no Supabase para criar:')
        console.log(`
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'tutorial-videos',
  'tutorial-videos',
  true,
  104857600,
  ARRAY['video/mp4', 'video/webm', 'video/ogg', 'video/quicktime']
);`)
      }
    }
    
  } catch (error) {
    console.error('❌ Erro geral:', error)
  }
}

applyMigration()