#!/bin/bash

echo "🔄 Ativando assinatura premium manualmente..."
echo "=========================================="

# Email do usuário
EMAIL="<EMAIL>"
SECRET_KEY="activate-premium-2025"
API_URL="https://newspports.com/api/activate-premium"

echo "📧 Email: $EMAIL"
echo "🔗 URL: $API_URL"
echo ""

# Fazer a requisição
echo "📡 Enviando requisição..."
RESPONSE=$(curl -s -X POST "$API_URL" \
  -H "Content-Type: application/json" \
  -H "x-secret-key: $SECRET_KEY" \
  -d "{\"email\": \"$EMAIL\"}")

# Verificar se a resposta é JSON válido
if echo "$RESPONSE" | jq . >/dev/null 2>&1; then
  echo "✅ Resposta recebida:"
  echo "$RESPONSE" | jq .
else
  echo "❌ Erro: Resposta não é JSON válido"
  echo "Resposta bruta:"
  echo "$RESPONSE"
fi

echo ""
echo "📝 Próximos passos:"
echo "1. Faça logout e login novamente no site"
echo "2. Limpe o cache do navegador (Ctrl+Shift+R)"
echo "3. Acesse um canal premium para testar"