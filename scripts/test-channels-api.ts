#!/usr/bin/env node

// Script simples para testar canais via API

async function testChannel(channelId: string) {
  try {
    const response = await fetch(`http://localhost:3000/api/iptv/channel/${channelId}`);
    const data = await response.json();
    
    if (response.ok && data.url) {
      console.log(`✅ ${channelId}: OK - ${data.url}`);
      return { channelId, status: 'ok', url: data.url };
    } else {
      console.log(`❌ ${channelId}: FAILED - ${data.error || 'No URL'}`);
      return { channelId, status: 'failed', error: data.error };
    }
  } catch (error) {
    console.log(`❌ ${channelId}: ERROR - ${error}`);
    return { channelId, status: 'error', error: String(error) };
  }
}

// Lista de canais para testar (baseada no arquivo channel-test-results.json)
const channels = [
  'real-madrid-tv',
  'sevilla-fc-tv',
  'teledeporte',
  'laliga-ea-sports-1',
  'laliga-hypermotion',
  'premier-league-1',
  'movistar-liga-de-campeones',
  '30AGolfKingdom.us',
  'ACCDigitalNetwork.us',
  'Adjarasport1.ge',
  'ADOTV.bj',
  'Africa24Sport.fr',
  'AfroSportNigeria.ng',
  'AntenaSport.hr',
  'AntenaSport.ro',
  'Arryadia.ma',
  'AstrahanRuSport.ru',
  'BahrainSports1.bh',
  'BahrainSports2.bh',
  'Belarus5.by',
  'BellatorMMA.us',
  'BilliardTV.us',
  'CanaldoInter.br',
  'CBSSportsNetworkUSA.us',
  'CTSport.cz',
  'DongNaiTV2.vn',
  'DubaiRacing.ae'
];

async function testAllChannels() {
  console.log(`Testing ${channels.length} channels...\n`);
  
  const results = [];
  for (const channelId of channels) {
    const result = await testChannel(channelId);
    results.push(result);
    // Pequeno delay entre requisições
    await new Promise(resolve => setTimeout(resolve, 500));
  }
  
  console.log('\n=== SUMMARY ===');
  console.log(`Total: ${results.length}`);
  console.log(`OK: ${results.filter(r => r.status === 'ok').length}`);
  console.log(`Failed: ${results.filter(r => r.status === 'failed').length}`);
  console.log(`Error: ${results.filter(r => r.status === 'error').length}`);
  
  // Salvar resultados
  const fs = require('fs');
  fs.writeFileSync(
    'api-test-results.json',
    JSON.stringify({
      timestamp: new Date().toISOString(),
      results,
      summary: {
        total: results.length,
        ok: results.filter(r => r.status === 'ok').length,
        failed: results.filter(r => r.status === 'failed').length,
        error: results.filter(r => r.status === 'error').length
      }
    }, null, 2)
  );
  
  console.log('\nResults saved to api-test-results.json');
}

testAllChannels().catch(console.error);