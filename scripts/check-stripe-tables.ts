import { createClient } from '@/lib/supabase/server'

async function checkStripeTables() {
  console.log('🔍 Verificando estrutura das tabelas do Stripe...\n')
  
  const supabase = await createClient()
  
  try {
    // Verificar estrutura da tabela profiles
    const { data: columns, error: columnsError } = await supabase
      .from('information_schema.columns' as any)
      .select('column_name, data_type')
      .eq('table_schema', 'public')
      .eq('table_name', 'profiles')
      .order('ordinal_position')
    
    if (columnsError) {
      console.error('❌ Erro ao verificar colunas:', columnsError)
      return
    }
    
    console.log('📋 Colunas da tabela profiles:')
    console.log('================================')
    
    const stripeColumns = [
      'stripe_customer_id',
      'stripe_subscription_id',
      'subscription_status',
      'subscription_current_period_end',
      'subscription_cancel_at_period_end',
      'subscription_tier',
      'trial_end'
    ]
    
    const foundColumns: string[] = []
    const missingColumns: string[] = []
    
    columns?.forEach(col => {
      console.log(`- ${col.column_name} (${col.data_type})`)
      if (stripeColumns.includes(col.column_name)) {
        foundColumns.push(col.column_name)
      }
    })
    
    // Verificar colunas faltantes
    stripeColumns.forEach(col => {
      if (!columns?.find(c => c.column_name === col)) {
        missingColumns.push(col)
      }
    })
    
    console.log('\n✅ Colunas do Stripe encontradas:')
    foundColumns.forEach(col => console.log(`  - ${col}`))
    
    if (missingColumns.length > 0) {
      console.log('\n❌ Colunas do Stripe faltando:')
      missingColumns.forEach(col => console.log(`  - ${col}`))
      
      console.log('\n⚠️  ATENÇÃO: Execute a migração 006_add_stripe_fields.sql para adicionar as colunas faltantes!')
    } else {
      console.log('\n✅ Todas as colunas do Stripe estão presentes!')
    }
    
    // Verificar se tabela payment_history existe
    const { data: paymentTable, error: paymentError } = await supabase
      .from('information_schema.tables' as any)
      .select('table_name')
      .eq('table_schema', 'public')
      .eq('table_name', 'payment_history')
      .single()
    
    if (paymentError || !paymentTable) {
      console.log('\n❌ Tabela payment_history não encontrada!')
      console.log('⚠️  Execute a migração 006_add_stripe_fields.sql para criar a tabela!')
    } else {
      console.log('\n✅ Tabela payment_history existe!')
    }
    
  } catch (error) {
    console.error('❌ Erro geral:', error)
  }
}

// Executar verificação
checkStripeTables().catch(console.error)