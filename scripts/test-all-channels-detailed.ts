import axios from 'axios'
import * as HLS from 'hls-parser'
import { premiumFootballChannels } from '../src/lib/premium-football-channels'
import { channelFallbacks, getAllFallbackUrls } from '../src/lib/channel-fallbacks'
import { promises as fs } from 'fs'
import path from 'path'

interface TestResult {
  id: string
  name: string
  status: 'success' | 'error'
  url?: string
  error?: string
  type: 'premium' | 'iptv' | 'fallback'
  details?: {
    httpStatus?: number
    errorType?: string
    headers?: Record<string, string>
    manifest?: {
      variants?: number
      segments?: number
    }
  }
}

interface DetailedResults {
  timestamp: string
  summary: {
    total: number
    success: number
    error: number
    byType: {
      premium: { total: number; success: number; error: number }
      iptv: { total: number; success: number; error: number }
      fallback: { total: number; success: number; error: number }
    }
    byError: Record<string, number>
  }
  results: TestResult[]
  recommendations: string[]
}

const IPTV_API_BASE = 'https://iptv-org.github.io/api'

async function testStream(url: string, headers?: Record<string, string>): Promise<{ success: boolean; error?: string; details?: any }> {
  try {
    const response = await axios.get(url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Accept': '*/*',
        ...headers
      },
      timeout: 10000,
      validateStatus: (status) => status < 500
    })

    if (response.status !== 200) {
      return {
        success: false,
        error: `HTTP ${response.status}`,
        details: { httpStatus: response.status }
      }
    }

    // Try to parse as HLS
    try {
      const manifest = HLS.parse(response.data)
      return {
        success: true,
        details: {
          httpStatus: 200,
          manifest: {
            variants: manifest.variants?.length || 0,
            segments: manifest.segments?.length || 0
          }
        }
      }
    } catch {
      // Not HLS but still a valid response
      return { success: true, details: { httpStatus: 200 } }
    }
  } catch (error: any) {
    let errorType = 'Unknown'
    let errorMessage = error.message

    if (error.code === 'ECONNABORTED') {
      errorType = 'Timeout'
      errorMessage = 'Request timeout after 10 seconds'
    } else if (error.code === 'ENOTFOUND') {
      errorType = 'DNS'
      errorMessage = 'Domain not found'
    } else if (error.response) {
      errorType = `HTTP ${error.response.status}`
      errorMessage = `HTTP ${error.response.status} error`
    } else if (error.message.includes('CORS')) {
      errorType = 'CORS'
      errorMessage = 'CORS policy blocking request'
    }

    return {
      success: false,
      error: errorMessage,
      details: { errorType }
    }
  }
}

async function testIPTVChannel(channelId: string): Promise<TestResult> {
  try {
    const response = await axios.get(`${IPTV_API_BASE}/channels/${channelId}.json`)
    const channel = response.data

    if (!channel.url) {
      return {
        id: channelId,
        name: channel.name,
        status: 'error',
        error: 'No URL provided',
        type: 'iptv'
      }
    }

    const testResult = await testStream(channel.url)

    return {
      id: channelId,
      name: channel.name,
      status: testResult.success ? 'success' : 'error',
      url: testResult.success ? channel.url : undefined,
      error: testResult.error,
      type: 'iptv',
      details: testResult.details
    }
  } catch (error: any) {
    return {
      id: channelId,
      name: channelId,
      status: 'error',
      error: `API error: ${error.message}`,
      type: 'iptv'
    }
  }
}

async function testPremiumChannel(channel: typeof premiumFootballChannels[0]): Promise<TestResult> {
  let lastError: string = ''
  let lastDetails: any = {}

  for (const stream of channel.streams) {
    const testResult = await testStream(stream.url, stream.headers)
    
    if (testResult.success) {
      return {
        id: channel.id,
        name: channel.name,
        status: 'success',
        url: stream.url,
        type: 'premium',
        details: testResult.details
      }
    }

    lastError = testResult.error || 'Unknown error'
    lastDetails = testResult.details
  }

  return {
    id: channel.id,
    name: channel.name,
    status: 'error',
    error: lastError,
    type: 'premium',
    details: lastDetails
  }
}

async function testFallbackChannel(channelId: string): Promise<TestResult | null> {
  const fallbackUrls = getAllFallbackUrls(channelId)
  
  if (fallbackUrls.length === 0) {
    return null
  }

  let lastError: string = ''
  let lastDetails: any = {}

  for (const fallback of fallbackUrls) {
    const testResult = await testStream(fallback.url, fallback.headers)
    
    if (testResult.success) {
      return {
        id: channelId,
        name: `${channelId} (Fallback)`,
        status: 'success',
        url: fallback.url,
        type: 'fallback',
        details: testResult.details
      }
    }

    lastError = testResult.error || 'Unknown error'
    lastDetails = testResult.details
  }

  return {
    id: channelId,
    name: `${channelId} (Fallback)`,
    status: 'error',
    error: lastError,
    type: 'fallback',
    details: lastDetails
  }
}

async function loadIPTVChannels(): Promise<string[]> {
  try {
    // Load channels from working channels list
    const workingChannelsData = await fs.readFile(
      path.join(process.cwd(), 'working-channels-list.json'),
      'utf8'
    )
    const workingChannels = JSON.parse(workingChannelsData)
    
    // Also try to get sports channels from API
    const response = await axios.get(`${IPTV_API_BASE}/channels.json`)
    const sportsChannels = response.data
      .filter((ch: any) => ch.categories?.some((cat: string) => 
        ['sports', 'news'].includes(cat.toLowerCase())
      ))
      .map((ch: any) => ch.id)
      .slice(0, 20)
    
    // Combine working channels and sports channels
    const combinedChannels = [
      ...workingChannels.map((ch: any) => ch.id),
      ...sportsChannels
    ]
    
    // Remove duplicates and limit to 50
    return [...new Set(combinedChannels)].slice(0, 50)
  } catch (error) {
    console.error('Error loading channels:', error)
    return []
  }
}

async function generateRecommendations(results: TestResult[]): Promise<string[]> {
  const recommendations: string[] = []
  const errorCounts: Record<string, number> = {}
  
  results.forEach(result => {
    if (result.status === 'error' && result.details?.errorType) {
      errorCounts[result.details.errorType] = (errorCounts[result.details.errorType] || 0) + 1
    }
  })

  if (errorCounts['CORS'] > 5) {
    recommendations.push(
      '🔴 CORS Issues: Many channels are blocked by CORS. Consider implementing a proxy server for these streams.'
    )
  }

  if (errorCounts['HTTP 403'] > 3) {
    recommendations.push(
      '🟡 Access Forbidden: Several channels return 403 errors. These may require authentication or specific headers.'
    )
  }

  if (errorCounts['Timeout'] > 5) {
    recommendations.push(
      '🟡 Timeout Issues: Many streams are timing out. Consider increasing timeout limits or testing network connectivity.'
    )
  }

  const successRate = results.filter(r => r.status === 'success').length / results.length
  if (successRate < 0.5) {
    recommendations.push(
      '🔴 Low Success Rate: Less than 50% of channels are working. Consider updating stream sources or implementing more fallbacks.'
    )
  }

  const premiumSuccess = results.filter(r => r.type === 'premium' && r.status === 'success').length
  const premiumTotal = results.filter(r => r.type === 'premium').length
  if (premiumTotal > 0 && premiumSuccess / premiumTotal < 0.7) {
    recommendations.push(
      '🟡 Premium Channels: Some premium channels are failing. Check if the streams require authentication or have changed URLs.'
    )
  }

  return recommendations
}

async function main() {
  console.log('🔍 Testing all channels in NewSpports platform...\n')
  
  const results: TestResult[] = []
  
  // Test Premium Channels
  console.log('📺 Testing Premium Football Channels...')
  for (const channel of premiumFootballChannels) {
    process.stdout.write(`Testing ${channel.name}... `)
    const result = await testPremiumChannel(channel)
    results.push(result)
    console.log(result.status === 'success' ? '✅' : `❌ ${result.error}`)
  }
  
  // Test IPTV Channels
  console.log('\n📡 Testing IPTV-org Channels...')
  const iptvChannels = await loadIPTVChannels()
  
  for (const channelId of iptvChannels) {
    process.stdout.write(`Testing ${channelId}... `)
    const result = await testIPTVChannel(channelId)
    results.push(result)
    
    // If IPTV channel fails, test fallback
    if (result.status === 'error') {
      const fallbackResult = await testFallbackChannel(channelId)
      if (fallbackResult) {
        results.push(fallbackResult)
        if (fallbackResult.status === 'success') {
          console.log(`❌ (but fallback works ✅)`)
        } else {
          console.log(`❌ ${result.error}`)
        }
      } else {
        console.log(`❌ ${result.error}`)
      }
    } else {
      console.log('✅')
    }
  }
  
  // Generate summary
  const errorTypes: Record<string, number> = {}
  results.forEach(result => {
    if (result.status === 'error' && result.details?.errorType) {
      errorTypes[result.details.errorType] = (errorTypes[result.details.errorType] || 0) + 1
    }
  })
  
  const summary: DetailedResults = {
    timestamp: new Date().toISOString(),
    summary: {
      total: results.length,
      success: results.filter(r => r.status === 'success').length,
      error: results.filter(r => r.status === 'error').length,
      byType: {
        premium: {
          total: results.filter(r => r.type === 'premium').length,
          success: results.filter(r => r.type === 'premium' && r.status === 'success').length,
          error: results.filter(r => r.type === 'premium' && r.status === 'error').length
        },
        iptv: {
          total: results.filter(r => r.type === 'iptv').length,
          success: results.filter(r => r.type === 'iptv' && r.status === 'success').length,
          error: results.filter(r => r.type === 'iptv' && r.status === 'error').length
        },
        fallback: {
          total: results.filter(r => r.type === 'fallback').length,
          success: results.filter(r => r.type === 'fallback' && r.status === 'success').length,
          error: results.filter(r => r.type === 'fallback' && r.status === 'error').length
        }
      },
      byError: errorTypes
    },
    results,
    recommendations: await generateRecommendations(results)
  }
  
  // Save detailed results
  await fs.writeFile(
    path.join(process.cwd(), 'channel-test-detailed-results.json'),
    JSON.stringify(summary, null, 2)
  )
  
  // Print summary
  console.log('\n📊 Test Summary:')
  console.log(`Total channels tested: ${summary.summary.total}`)
  console.log(`✅ Success: ${summary.summary.success} (${(summary.summary.success / summary.summary.total * 100).toFixed(1)}%)`)
  console.log(`❌ Error: ${summary.summary.error} (${(summary.summary.error / summary.summary.total * 100).toFixed(1)}%)`)
  
  console.log('\n📈 By Type:')
  console.log(`Premium: ${summary.summary.byType.premium.success}/${summary.summary.byType.premium.total}`)
  console.log(`IPTV: ${summary.summary.byType.iptv.success}/${summary.summary.byType.iptv.total}`)
  console.log(`Fallback: ${summary.summary.byType.fallback.success}/${summary.summary.byType.fallback.total}`)
  
  console.log('\n🚨 Error Types:')
  Object.entries(summary.summary.byError).forEach(([type, count]) => {
    console.log(`${type}: ${count}`)
  })
  
  console.log('\n💡 Recommendations:')
  summary.recommendations.forEach(rec => console.log(rec))
  
  console.log('\n✅ Results saved to channel-test-detailed-results.json')
}

main().catch(console.error)