import { createClient } from '@supabase/supabase-js'
import * as dotenv from 'dotenv'

// Carregar variáveis de ambiente
dotenv.config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Variáveis de ambiente não encontradas!')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
})

async function applyStripeMigration() {
  console.log('🔄 Aplicando migração do Stripe...\n')
  
  try {
    // Primeiro, verificar estrutura atual
    console.log('📋 Verificando estrutura atual da tabela profiles...')
    
    const { data: checkColumns, error: checkError } = await supabase
      .rpc('get_table_columns', {
        schema_name: 'public',
        table_name: 'profiles'
      })
    
    if (checkError) {
      console.log('⚠️  Não foi possível verificar colunas existentes')
      console.log('Continuando com a migração...\n')
    } else if (checkColumns) {
      console.log('Colunas existentes:', checkColumns.map((c: any) => c.column_name).join(', '))
    }
    
    // Aplicar migração
    console.log('\n🚀 Aplicando migração...')
    
    const migrationSQL = `
      -- Adicionar campos do Stripe ao perfil
      ALTER TABLE profiles ADD COLUMN IF NOT EXISTS stripe_customer_id TEXT UNIQUE;
      ALTER TABLE profiles ADD COLUMN IF NOT EXISTS stripe_subscription_id TEXT;
      ALTER TABLE profiles ADD COLUMN IF NOT EXISTS subscription_status TEXT;
      ALTER TABLE profiles ADD COLUMN IF NOT EXISTS subscription_current_period_end TIMESTAMPTZ;
      ALTER TABLE profiles ADD COLUMN IF NOT EXISTS subscription_cancel_at_period_end BOOLEAN DEFAULT FALSE;
      ALTER TABLE profiles ADD COLUMN IF NOT EXISTS trial_end TIMESTAMPTZ;
      
      -- Criar tabela de histórico de pagamentos se não existir
      CREATE TABLE IF NOT EXISTS payment_history (
        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
        user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
        stripe_invoice_id TEXT UNIQUE,
        amount DECIMAL(10, 2) NOT NULL,
        currency TEXT NOT NULL DEFAULT 'eur',
        status TEXT NOT NULL CHECK (status IN ('paid', 'failed', 'pending', 'refunded')),
        paid_at TIMESTAMPTZ,
        failed_at TIMESTAMPTZ,
        refunded_at TIMESTAMPTZ,
        created_at TIMESTAMPTZ DEFAULT now(),
        updated_at TIMESTAMPTZ DEFAULT now()
      );
      
      -- Criar índices
      CREATE INDEX IF NOT EXISTS idx_profiles_stripe_customer_id ON profiles(stripe_customer_id);
      CREATE INDEX IF NOT EXISTS idx_profiles_subscription_status ON profiles(subscription_status);
      CREATE INDEX IF NOT EXISTS idx_payment_history_user_id ON payment_history(user_id);
      CREATE INDEX IF NOT EXISTS idx_payment_history_status ON payment_history(status);
      
      -- Adicionar RLS
      ALTER TABLE payment_history ENABLE ROW LEVEL SECURITY;
      
      -- Política para usuários verem seu próprio histórico
      DROP POLICY IF EXISTS "Users can view own payment history" ON payment_history;
      CREATE POLICY "Users can view own payment history" ON payment_history
        FOR SELECT USING (auth.uid() = user_id);
      
      -- Política para sistema inserir pagamentos
      DROP POLICY IF EXISTS "System can insert payments" ON payment_history;
      CREATE POLICY "System can insert payments" ON payment_history
        FOR INSERT WITH CHECK (true);
    `
    
    // Executar cada comando SQL separadamente
    const commands = migrationSQL
      .split(';')
      .map(cmd => cmd.trim())
      .filter(cmd => cmd.length > 0)
    
    for (const command of commands) {
      console.log(`\n⚙️  Executando: ${command.substring(0, 60)}...`)
      
      const { error } = await supabase.rpc('exec_sql', {
        query: command + ';'
      }).single()
      
      if (error) {
        // Tentar executar diretamente se RPC falhar
        const { error: directError } = await supabase
          .from('_sql')
          .insert({ query: command + ';' })
        
        if (directError) {
          console.error(`❌ Erro: ${directError.message}`)
        } else {
          console.log('✅ Comando executado com sucesso')
        }
      } else {
        console.log('✅ Comando executado com sucesso')
      }
    }
    
    console.log('\n🎉 Migração concluída!')
    
    // Verificar resultado
    console.log('\n📊 Verificando resultado...')
    
    const { data: profiles, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .limit(1)
    
    if (!profileError && profiles && profiles.length > 0) {
      const profile = profiles[0]
      console.log('\n✅ Colunas do Stripe disponíveis:')
      
      const stripeColumns = [
        'stripe_customer_id',
        'stripe_subscription_id', 
        'subscription_status',
        'subscription_current_period_end',
        'subscription_cancel_at_period_end',
        'subscription_tier'
      ]
      
      stripeColumns.forEach(col => {
        if (col in profile) {
          console.log(`  ✅ ${col}`)
        } else {
          console.log(`  ❌ ${col} (não encontrada)`)
        }
      })
    }
    
  } catch (error) {
    console.error('❌ Erro geral:', error)
  }
}

// Executar migração
applyStripeMigration().catch(console.error)