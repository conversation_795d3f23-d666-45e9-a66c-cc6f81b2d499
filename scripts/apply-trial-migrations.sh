#!/bin/bash

echo "🚀 Aplicando migrações do sistema de trial..."
echo ""

# Cores para output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Verificar se as variáveis de ambiente estão definidas
if [ -z "$NEXT_PUBLIC_SUPABASE_URL" ] || [ -z "$SUPABASE_SERVICE_ROLE_KEY" ]; then
    echo -e "${RED}❌ Erro: Variáveis de ambiente não definidas${NC}"
    echo "Por favor, certifique-se de que NEXT_PUBLIC_SUPABASE_URL e SUPABASE_SERVICE_ROLE_KEY estão definidas"
    exit 1
fi

# Extrair o project ID da URL
PROJECT_ID=$(echo $NEXT_PUBLIC_SUPABASE_URL | sed 's/https:\/\/\([^.]*\).*/\1/')
echo "📦 Project ID: $PROJECT_ID"

# Lista de migrações para aplicar
MIGRATIONS=(
    "005_trial_session_function.sql"
    "028_fix_trial_system_user_based.sql"
)

echo ""
echo "📋 Migrações a serem aplicadas:"
for migration in "${MIGRATIONS[@]}"; do
    echo "   - $migration"
done

echo ""
echo -e "${YELLOW}⚠️  ATENÇÃO: Isso irá modificar o banco de dados!${NC}"
read -p "Deseja continuar? (y/N) " -n 1 -r
echo ""

if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "❌ Operação cancelada"
    exit 1
fi

echo ""
echo "🔧 Aplicando migrações..."

for migration in "${MIGRATIONS[@]}"; do
    echo ""
    echo "📄 Aplicando: $migration"
    
    if [ -f "supabase/migrations/$migration" ]; then
        # Usar psql para aplicar a migração
        RESPONSE=$(curl -s -X POST \
            "${NEXT_PUBLIC_SUPABASE_URL}/rest/v1/rpc/query" \
            -H "apikey: ${SUPABASE_SERVICE_ROLE_KEY}" \
            -H "Authorization: Bearer ${SUPABASE_SERVICE_ROLE_KEY}" \
            -H "Content-Type: application/json" \
            -d @- << EOF
{
    "query": $(cat supabase/migrations/$migration | jq -Rs .)
}
EOF
        )
        
        if [ $? -eq 0 ]; then
            echo -e "${GREEN}✅ $migration aplicada com sucesso${NC}"
        else
            echo -e "${RED}❌ Erro ao aplicar $migration${NC}"
            echo "Resposta: $RESPONSE"
        fi
    else
        echo -e "${RED}❌ Arquivo não encontrado: supabase/migrations/$migration${NC}"
    fi
done

echo ""
echo "🎉 Processo concluído!"
echo ""
echo "📝 Próximos passos:"
echo "1. Teste o sistema de trial em um navegador limpo"
echo "2. Verifique se o trial está sendo salvo no banco"
echo "3. Tente acessar com o mesmo usuário em outro navegador"
echo ""
echo "🔍 Para verificar se as funções foram criadas, execute:"
echo "   SELECT routine_name FROM information_schema.routines WHERE routine_schema = 'public';"