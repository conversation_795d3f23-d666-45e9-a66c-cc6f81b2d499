-- Script para verificar logs e status do Stripe
-- Execute cada bloco no Supabase SQL Editor

-- 1. Ver últimos eventos de webhook
SELECT 
    event_id,
    event_type,
    status,
    error_message,
    created_at
FROM stripe_webhook_events
ORDER BY created_at DESC
LIMIT 10;

-- 2. Ver apenas eventos de erro
SELECT 
    event_id,
    event_type,
    error_message,
    payload->>'id' as stripe_event_id,
    created_at
FROM stripe_webhook_events
WHERE status = 'error'
ORDER BY created_at DESC
LIMIT 10;

-- 3. Ver assinaturas ativas
SELECT 
    s.user_id,
    s.stripe_customer_id,
    s.stripe_subscription_id,
    s.status,
    s.current_period_end,
    p.email,
    p.subscription_tier,
    p.subscription_status
FROM stripe_subscriptions s
LEFT JOIN profiles p ON p.id = s.user_id
WHERE s.status = 'active'
ORDER BY s.created_at DESC;

-- 4. Ver histórico de pagamentos
SELECT 
    ph.user_id,
    ph.stripe_invoice_id,
    ph.amount,
    ph.currency,
    ph.status,
    ph.paid_at,
    p.email
FROM payment_history ph
LEFT JOIN profiles p ON p.id = ph.user_id
ORDER BY ph.paid_at DESC
LIMIT 10;

-- 5. Verificar usuários com problema de sincronização
-- (tem stripe_customer_id mas não tem subscription ativa)
SELECT 
    p.id,
    p.email,
    p.stripe_customer_id,
    p.stripe_subscription_id,
    p.subscription_tier,
    p.subscription_status,
    p.subscription_current_period_end,
    CASE 
        WHEN p.subscription_current_period_end > NOW() THEN 'Should have access'
        ELSE 'Expired'
    END as access_status
FROM profiles p
WHERE p.stripe_customer_id IS NOT NULL
AND (p.subscription_tier != 'premium' OR p.subscription_status != 'active')
ORDER BY p.updated_at DESC;

-- 6. Debug completo de um usuário específico (substitua o email)
WITH user_data AS (
    SELECT id FROM profiles WHERE email = 'SEU_EMAIL_AQUI'
)
SELECT 
    'Profile' as source,
    p.subscription_tier,
    p.subscription_status,
    p.subscription_current_period_end,
    p.stripe_customer_id,
    p.stripe_subscription_id
FROM profiles p
WHERE p.id = (SELECT id FROM user_data)
UNION ALL
SELECT 
    'Stack Profile' as source,
    sp.subscription_tier,
    sp.subscription_status,
    sp.subscription_current_period_end,
    sp.stripe_customer_id,
    sp.stripe_subscription_id
FROM stack_profiles sp
WHERE sp.id = (SELECT id FROM user_data)
UNION ALL
SELECT 
    'Stripe Subscription' as source,
    s.status as subscription_tier,
    s.status as subscription_status,
    s.current_period_end as subscription_current_period_end,
    s.stripe_customer_id,
    s.stripe_subscription_id
FROM stripe_subscriptions s
WHERE s.user_id = (SELECT id FROM user_data);