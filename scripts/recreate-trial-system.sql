-- <PERSON><PERSON><PERSON> completo para recriar o sistema de trial
-- Execute este script no Supabase SQL Editor

-- 1. Remover tabela e funções antigas (se existirem)
DROP FUNCTION IF EXISTS public.get_or_create_trial_session CASCADE;
DROP FUNCTION IF EXISTS public.expire_trial_session CASCADE;
DROP TABLE IF EXISTS public.trial_sessions CASCADE;

-- 2. <PERSON><PERSON><PERSON> tabela trial_sessions com estrutura completa
CREATE TABLE public.trial_sessions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  session_id UUID UNIQUE NOT NULL DEFAULT gen_random_uuid(),
  user_id TEXT, -- Para vincular ao usuário autenticado
  trial_start TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  trial_end TIMESTAMPTZ NOT NULL,
  trial_duration_minutes INTEGER DEFAULT 5,
  ip_address TEXT,
  user_agent TEXT,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- 3. <PERSON><PERSON><PERSON> índices
CREATE INDEX idx_trial_sessions_session_id ON public.trial_sessions(session_id);
CREATE INDEX idx_trial_sessions_trial_end ON public.trial_sessions(trial_end);
CREATE INDEX idx_trial_sessions_user_id ON public.trial_sessions(user_id);
CREATE INDEX idx_trial_sessions_user_created ON public.trial_sessions(user_id, created_at DESC);

-- 4. Habilitar RLS
ALTER TABLE public.trial_sessions ENABLE ROW LEVEL SECURITY;

-- 5. Criar políticas
CREATE POLICY "Allow anonymous insert" ON public.trial_sessions
  FOR INSERT TO anon, authenticated
  WITH CHECK (true);

CREATE POLICY "Allow read own session" ON public.trial_sessions
  FOR SELECT TO anon, authenticated
  USING (
    -- Sessão anônima pode ler por session_id
    (session_id::text = current_setting('request.headers', true)::json->>'x-trial-session-id'
     OR session_id::text = current_setting('request.cookies', true)::json->>'trial_session_id')
    -- Usuário autenticado pode ler suas próprias sessões
    OR (auth.uid()::text = user_id)
  );

-- 6. Criar função principal
CREATE OR REPLACE FUNCTION public.get_or_create_trial_session(
  p_user_id TEXT DEFAULT NULL,
  p_session_id UUID DEFAULT NULL
)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_session_id UUID;
  v_trial_id UUID;
  v_trial_start TIMESTAMPTZ;
  v_trial_end TIMESTAMPTZ;
  v_now TIMESTAMPTZ := NOW();
  v_trial_duration INTERVAL := INTERVAL '5 minutes';
  v_is_expired BOOLEAN;
  v_time_remaining INTEGER;
  v_existing_session RECORD;
BEGIN
  -- Se tem user_id, buscar trial mais recente do usuário
  IF p_user_id IS NOT NULL THEN
    SELECT 
      id,
      session_id,
      trial_start,
      trial_end,
      (NOW() > trial_end) as is_expired
    INTO v_existing_session
    FROM public.trial_sessions
    WHERE user_id = p_user_id
    ORDER BY created_at DESC
    LIMIT 1;
    
    -- Se encontrou sessão do usuário
    IF FOUND THEN
      -- Se não expirou há mais de 24 horas, usar a existente
      IF v_existing_session.trial_end > (NOW() - INTERVAL '24 hours') THEN
        v_trial_id := v_existing_session.id;
        v_session_id := v_existing_session.session_id;
        v_trial_start := v_existing_session.trial_start;
        v_trial_end := v_existing_session.trial_end;
        
        -- Calcular tempo restante
        v_is_expired := v_now > v_trial_end;
        v_time_remaining := GREATEST(0, EXTRACT(EPOCH FROM (v_trial_end - v_now))::INTEGER);
        
        -- Retornar sessão existente
        RETURN jsonb_build_object(
          'trial_id', v_trial_id,
          'session_id', v_session_id,
          'trial_start', v_trial_start,
          'trial_end', v_trial_end,
          'is_expired', v_is_expired,
          'time_remaining', v_time_remaining,
          'trial_duration_minutes', 5,
          'source', 'existing_user_trial'
        );
      END IF;
    END IF;
  END IF;
  
  -- Se tem session_id, buscar por ele (usuários anônimos)
  IF p_session_id IS NOT NULL AND p_user_id IS NULL THEN
    SELECT 
      id,
      session_id,
      trial_start,
      trial_end
    INTO v_trial_id, v_session_id, v_trial_start, v_trial_end
    FROM public.trial_sessions
    WHERE session_id = p_session_id;
    
    IF FOUND THEN
      -- Calcular se expirou e tempo restante
      v_is_expired := v_now > v_trial_end;
      v_time_remaining := GREATEST(0, EXTRACT(EPOCH FROM (v_trial_end - v_now))::INTEGER);
      
      -- Retornar sessão existente
      RETURN jsonb_build_object(
        'trial_id', v_trial_id,
        'session_id', v_session_id,
        'trial_start', v_trial_start,
        'trial_end', v_trial_end,
        'is_expired', v_is_expired,
        'time_remaining', v_time_remaining,
        'trial_duration_minutes', 5,
        'source', 'existing_session_trial'
      );
    END IF;
  END IF;
  
  -- Se chegou aqui, criar nova sessão
  v_session_id := COALESCE(p_session_id, gen_random_uuid());
  v_trial_start := v_now;
  v_trial_end := v_now + v_trial_duration;
  
  INSERT INTO public.trial_sessions (
    session_id,
    user_id,
    trial_start,
    trial_end,
    trial_duration_minutes,
    ip_address,
    user_agent
  ) VALUES (
    v_session_id,
    p_user_id,
    v_trial_start,
    v_trial_end,
    5,
    current_setting('request.headers', true)::json->>'x-forwarded-for',
    current_setting('request.headers', true)::json->>'user-agent'
  )
  RETURNING id INTO v_trial_id;
  
  -- Retornar nova sessão
  RETURN jsonb_build_object(
    'trial_id', v_trial_id,
    'session_id', v_session_id,
    'trial_start', v_trial_start,
    'trial_end', v_trial_end,
    'is_expired', false,
    'time_remaining', EXTRACT(EPOCH FROM v_trial_duration)::INTEGER,
    'trial_duration_minutes', 5,
    'source', 'new_trial'
  );
END;
$$;

-- 7. Criar função para expirar trial
CREATE OR REPLACE FUNCTION public.expire_trial_session(
  p_user_id TEXT DEFAULT NULL,
  p_session_id UUID DEFAULT NULL
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Atualizar por user_id se fornecido
  IF p_user_id IS NOT NULL THEN
    UPDATE public.trial_sessions
    SET trial_end = NOW()
    WHERE user_id = p_user_id
      AND trial_end > NOW();
    RETURN FOUND;
  END IF;
  
  -- Senão, atualizar por session_id
  IF p_session_id IS NOT NULL THEN
    UPDATE public.trial_sessions
    SET trial_end = NOW()
    WHERE session_id = p_session_id
      AND trial_end > NOW();
    RETURN FOUND;
  END IF;
  
  RETURN FALSE;
END;
$$;

-- 8. Adicionar comentários
COMMENT ON TABLE public.trial_sessions IS 'Armazena sessões de trial de 5 minutos para usuários';
COMMENT ON FUNCTION public.get_or_create_trial_session IS 
'Gerencia sessões de trial de 5 minutos. Para usuários autenticados, vincula ao user_id para evitar múltiplos trials. Para anônimos, usa session_id. Permite novo trial após 24 horas.';
COMMENT ON FUNCTION public.expire_trial_session IS 'Expira uma sessão de trial manualmente';

-- 9. Verificar se tudo foi criado
SELECT 
  'Tabela trial_sessions' as item,
  EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'trial_sessions') as existe
UNION ALL
SELECT 
  'Função get_or_create_trial_session' as item,
  EXISTS (SELECT 1 FROM information_schema.routines WHERE routine_name = 'get_or_create_trial_session') as existe
UNION ALL
SELECT 
  'Função expire_trial_session' as item,
  EXISTS (SELECT 1 FROM information_schema.routines WHERE routine_name = 'expire_trial_session') as existe;