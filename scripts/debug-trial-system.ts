#!/usr/bin/env node

/**
 * Script para debugar o sistema de trial
 * Simula o fluxo de um novo usuário e monitora o estado do trial
 */

import { chromium } from 'playwright'

async function debugTrialSystem() {
  const browser = await chromium.launch({
    headless: false,
    devtools: true
  })
  
  const context = await browser.newContext()
  const page = await context.newPage()
  
  // Habilitar logs do console
  page.on('console', msg => {
    const text = msg.text()
    if (text.includes('[TRIAL-')) {
      console.log(`🔍 ${text}`)
    }
  })
  
  // Limpar localStorage para simular novo usuário
  await page.goto('http://localhost:3000')
  await page.evaluate(() => {
    console.log('🧹 Limpando localStorage...')
    localStorage.clear()
    sessionStorage.clear()
  })
  
  // Recarregar para garantir estado limpo
  await page.reload()
  await page.waitForTimeout(2000)
  
  // Verificar estado inicial do localStorage
  const initialState = await page.evaluate(() => {
    const trialStorage = localStorage.getItem('trial-storage')
    return {
      hasTrialStorage: !!trialStorage,
      trialStorage: trialStorage ? JSON.parse(trialStorage) : null,
      allKeys: Object.keys(localStorage)
    }
  })
  
  console.log('📦 Estado inicial do localStorage:', JSON.stringify(initialState, null, 2))
  
  // Navegar para uma página de canal
  console.log('🎬 Navegando para página de canal...')
  await page.goto('http://localhost:3000/watch/es_la1_hd')
  
  // Aguardar player carregar
  await page.waitForTimeout(5000)
  
  // Verificar estado após navegação
  const afterNavigationState = await page.evaluate(() => {
    const trialStorage = localStorage.getItem('trial-storage')
    return {
      hasTrialStorage: !!trialStorage,
      trialStorage: trialStorage ? JSON.parse(trialStorage) : null,
      timestamp: Date.now()
    }
  })
  
  console.log('📦 Estado após navegação:', JSON.stringify(afterNavigationState, null, 2))
  
  // Monitorar mudanças no localStorage a cada 5 segundos
  let previousState = afterNavigationState
  const monitorInterval = setInterval(async () => {
    const currentState = await page.evaluate(() => {
      const trialStorage = localStorage.getItem('trial-storage')
      return {
        hasTrialStorage: !!trialStorage,
        trialStorage: trialStorage ? JSON.parse(trialStorage) : null,
        timestamp: Date.now()
      }
    })
    
    // Comparar com estado anterior
    if (JSON.stringify(currentState?.trialStorage) !== JSON.stringify(previousState?.trialStorage)) {
      console.log('🔄 Mudança detectada no localStorage:')
      console.log('  Anterior:', JSON.stringify(previousState?.trialStorage?.state, null, 2))
      console.log('  Atual:', JSON.stringify(currentState?.trialStorage?.state, null, 2))
    }
    
    // Verificar se trial expirou prematuramente
    if (currentState?.trialStorage?.state?.isExpired && !previousState?.trialStorage?.state?.isExpired) {
      console.log('❌ TRIAL EXPIROU!')
      console.log('  startTime:', currentState.trialStorage.state.startTime)
      console.log('  timeRemaining:', currentState.trialStorage.state.timeRemaining)
      
      // Calcular tempo decorrido
      if (currentState.trialStorage.state.startTime) {
        const elapsed = (Date.now() - currentState.trialStorage.state.startTime) / 1000
        console.log('  Tempo decorrido:', elapsed, 'segundos')
        
        if (elapsed < 300) {
          console.log('  ⚠️  PROBLEMA DETECTADO: Trial expirou antes de 5 minutos!')
        }
      }
    }
    
    previousState = currentState
  }, 5000)
  
  // Aguardar 1 minuto para observar comportamento
  console.log('⏰ Monitorando por 1 minuto...')
  await page.waitForTimeout(60000)
  
  clearInterval(monitorInterval)
  
  // Estado final
  const finalState = await page.evaluate(() => {
    const trialStorage = localStorage.getItem('trial-storage')
    return {
      hasTrialStorage: !!trialStorage,
      trialStorage: trialStorage ? JSON.parse(trialStorage) : null,
      timestamp: Date.now()
    }
  })
  
  console.log('📦 Estado final:', JSON.stringify(finalState, null, 2))
  
  // Manter navegador aberto para análise manual
  console.log('🔍 Navegador permanecerá aberto para análise manual. Pressione Ctrl+C para fechar.')
}

// Executar debug
debugTrialSystem().catch(console.error)