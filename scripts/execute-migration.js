#!/usr/bin/env node

const { createClient } = require('@supabase/supabase-js')
const fs = require('fs')
const path = require('path')

// Suas credenciais
const SUPABASE_URL = 'https://bgeecdlhnwkgdujoyont.supabase.co'
const SUPABASE_SERVICE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJnZWVjZGxobndrZ2R1am95b250Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MjkwMDEwMSwiZXhwIjoyMDY4NDc2MTAxfQ.0bS2U7LgWgop_pdD4XYLqQ_V0SefmUq0y1XXdpZwbic'

// Criar cliente Supabase
const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
})

// Função para executar SQL
async function executeSQL(sql, description) {
  console.log(`\n🔧 ${description}...`)
  
  try {
    const { data, error } = await supabase.rpc('exec_sql', { sql })
    
    if (error) {
      // Se RPC não funcionar, tenta direto
      const response = await fetch(`${SUPABASE_URL}/rest/v1/rpc/exec`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'apikey': SUPABASE_SERVICE_KEY,
          'Authorization': `Bearer ${SUPABASE_SERVICE_KEY}`
        },
        body: JSON.stringify({ query: sql })
      })
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${await response.text()}`)
      }
    }
    
    console.log(`✅ ${description} - Concluído`)
    return true
  } catch (error) {
    console.error(`❌ ${description} - Erro:`, error.message)
    return false
  }
}

// Migração SQL
const migrationSteps = [
  {
    description: 'Habilitando extensões',
    sql: `
      CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
      CREATE EXTENSION IF NOT EXISTS "pgcrypto";
    `
  },
  {
    description: 'Criando tipos customizados',
    sql: `
      DO $$ 
      BEGIN
        IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'channel_category') THEN
          CREATE TYPE channel_category AS ENUM ('sports', 'esports', 'entertainment', 'news', 'kids');
        END IF;
        IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'subscription_tier') THEN
          CREATE TYPE subscription_tier AS ENUM ('free', 'basic', 'premium', 'vip');
        END IF;
        IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'content_rating') THEN
          CREATE TYPE content_rating AS ENUM ('G', 'PG', 'PG-13', 'R', 'NC-17');
        END IF;
      END$$;
    `
  },
  {
    description: 'Criando tabela profiles',
    sql: `
      CREATE TABLE IF NOT EXISTS profiles (
        id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
        username TEXT UNIQUE,
        full_name TEXT,
        avatar_url TEXT,
        subscription_tier subscription_tier DEFAULT 'free',
        subscription_expires_at TIMESTAMPTZ,
        trial_used BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMPTZ DEFAULT NOW(),
        updated_at TIMESTAMPTZ DEFAULT NOW()
      );
    `
  },
  {
    description: 'Criando tabela channels',
    sql: `
      CREATE TABLE IF NOT EXISTS channels (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        name TEXT NOT NULL,
        slug TEXT UNIQUE NOT NULL,
        description TEXT,
        logo_url TEXT,
        banner_url TEXT,
        stream_url TEXT NOT NULL,
        fallback_urls TEXT[] DEFAULT '{}',
        category channel_category NOT NULL,
        is_premium BOOLEAN DEFAULT FALSE,
        required_tier subscription_tier DEFAULT 'free',
        is_active BOOLEAN DEFAULT TRUE,
        viewer_count INTEGER DEFAULT 0,
        metadata JSONB DEFAULT '{}',
        created_at TIMESTAMPTZ DEFAULT NOW(),
        updated_at TIMESTAMPTZ DEFAULT NOW()
      );
    `
  },
  {
    description: 'Criando tabela viewing_sessions',
    sql: `
      CREATE TABLE IF NOT EXISTS viewing_sessions (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
        channel_id UUID REFERENCES channels(id) ON DELETE CASCADE,
        started_at TIMESTAMPTZ DEFAULT NOW(),
        ended_at TIMESTAMPTZ,
        duration_seconds INTEGER,
        quality TEXT,
        ip_address INET,
        user_agent TEXT,
        created_at TIMESTAMPTZ DEFAULT NOW()
      );
    `
  },
  {
    description: 'Criando tabela app_verification_tokens',
    sql: `
      CREATE TABLE IF NOT EXISTS app_verification_tokens (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
        token TEXT UNIQUE NOT NULL,
        used BOOLEAN DEFAULT FALSE,
        used_at TIMESTAMPTZ,
        expires_at TIMESTAMPTZ NOT NULL,
        platform TEXT CHECK (platform IN ('ios', 'android')),
        created_at TIMESTAMPTZ DEFAULT NOW()
      );
    `
  },
  {
    description: 'Criando tabela favorites',
    sql: `
      CREATE TABLE IF NOT EXISTS favorites (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
        channel_id UUID REFERENCES channels(id) ON DELETE CASCADE,
        created_at TIMESTAMPTZ DEFAULT NOW(),
        UNIQUE(user_id, channel_id)
      );
    `
  },
  {
    description: 'Criando tabela channel_schedule',
    sql: `
      CREATE TABLE IF NOT EXISTS channel_schedule (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        channel_id UUID REFERENCES channels(id) ON DELETE CASCADE,
        title TEXT NOT NULL,
        description TEXT,
        starts_at TIMESTAMPTZ NOT NULL,
        ends_at TIMESTAMPTZ NOT NULL,
        is_live BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMPTZ DEFAULT NOW(),
        updated_at TIMESTAMPTZ DEFAULT NOW()
      );
    `
  },
  {
    description: 'Criando índices',
    sql: `
      CREATE INDEX IF NOT EXISTS idx_channels_category ON channels(category);
      CREATE INDEX IF NOT EXISTS idx_channels_is_active ON channels(is_active);
      CREATE INDEX IF NOT EXISTS idx_channels_slug ON channels(slug);
      CREATE INDEX IF NOT EXISTS idx_viewing_sessions_user_id ON viewing_sessions(user_id);
      CREATE INDEX IF NOT EXISTS idx_viewing_sessions_channel_id ON viewing_sessions(channel_id);
      CREATE INDEX IF NOT EXISTS idx_viewing_sessions_started_at ON viewing_sessions(started_at);
      CREATE INDEX IF NOT EXISTS idx_app_verification_tokens_token ON app_verification_tokens(token);
      CREATE INDEX IF NOT EXISTS idx_app_verification_tokens_expires_at ON app_verification_tokens(expires_at);
      CREATE INDEX IF NOT EXISTS idx_favorites_user_id ON favorites(user_id);
      CREATE INDEX IF NOT EXISTS idx_channel_schedule_channel_id ON channel_schedule(channel_id);
      CREATE INDEX IF NOT EXISTS idx_channel_schedule_starts_at ON channel_schedule(starts_at);
    `
  },
  {
    description: 'Criando função update_updated_at',
    sql: `
      CREATE OR REPLACE FUNCTION update_updated_at_column()
      RETURNS TRIGGER AS $$
      BEGIN
        NEW.updated_at = NOW();
        RETURN NEW;
      END;
      $$ language 'plpgsql';
    `
  },
  {
    description: 'Criando triggers',
    sql: `
      DROP TRIGGER IF EXISTS update_profiles_updated_at ON profiles;
      CREATE TRIGGER update_profiles_updated_at BEFORE UPDATE ON profiles
        FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
      
      DROP TRIGGER IF EXISTS update_channels_updated_at ON channels;
      CREATE TRIGGER update_channels_updated_at BEFORE UPDATE ON channels
        FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
      
      DROP TRIGGER IF EXISTS update_channel_schedule_updated_at ON channel_schedule;
      CREATE TRIGGER update_channel_schedule_updated_at BEFORE UPDATE ON channel_schedule
        FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
    `
  },
  {
    description: 'Inserindo canais demo',
    sql: `
      INSERT INTO channels (name, slug, description, logo_url, stream_url, category, is_premium, required_tier) VALUES
        ('LaLiga TV', 'laliga-tv', 'Todos los partidos de LaLiga en directo', '/logos/laliga.png', 'https://demo-stream.com/laliga.m3u8', 'sports', true, 'premium'),
        ('Champions League', 'champions-league', 'Los mejores partidos de Europa', '/logos/champions.png', 'https://demo-stream.com/champions.m3u8', 'sports', true, 'premium'),
        ('Formula 1', 'formula-1', 'Todas las carreras de F1 en directo', '/logos/f1.png', 'https://demo-stream.com/f1.m3u8', 'sports', true, 'vip'),
        ('ESPN Deportes', 'espn-deportes', 'Lo mejor del deporte internacional', '/logos/espn.png', 'https://demo-stream.com/espn.m3u8', 'sports', false, 'basic'),
        ('eSports Arena', 'esports-arena', 'Torneos de League of Legends, CS:GO y más', '/logos/esports.png', 'https://demo-stream.com/esports.m3u8', 'esports', false, 'free')
      ON CONFLICT (slug) DO NOTHING;
    `
  },
  {
    description: 'Habilitando RLS',
    sql: `
      ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
      ALTER TABLE channels ENABLE ROW LEVEL SECURITY;
      ALTER TABLE viewing_sessions ENABLE ROW LEVEL SECURITY;
      ALTER TABLE app_verification_tokens ENABLE ROW LEVEL SECURITY;
      ALTER TABLE favorites ENABLE ROW LEVEL SECURITY;
      ALTER TABLE channel_schedule ENABLE ROW LEVEL SECURITY;
    `
  },
  {
    description: 'Criando políticas RLS',
    sql: `
      -- Profiles policies
      CREATE POLICY "Public profiles are viewable by everyone" ON profiles
        FOR SELECT USING (true);
      
      CREATE POLICY "Users can update own profile" ON profiles
        FOR UPDATE USING ((SELECT auth.uid()) = id);
      
      CREATE POLICY "Users can insert own profile" ON profiles
        FOR INSERT WITH CHECK ((SELECT auth.uid()) = id);
      
      -- Channels policies
      CREATE POLICY "Channels are viewable by everyone" ON channels
        FOR SELECT USING (is_active = true);
      
      -- Viewing sessions policies
      CREATE POLICY "Users can view own sessions" ON viewing_sessions
        FOR SELECT USING ((SELECT auth.uid()) = user_id);
      
      CREATE POLICY "Users can create own sessions" ON viewing_sessions
        FOR INSERT WITH CHECK ((SELECT auth.uid()) = user_id);
      
      CREATE POLICY "Users can update own sessions" ON viewing_sessions
        FOR UPDATE USING ((SELECT auth.uid()) = user_id);
      
      -- App verification tokens policies
      CREATE POLICY "Users can view own tokens" ON app_verification_tokens
        FOR SELECT USING ((SELECT auth.uid()) = user_id);
      
      CREATE POLICY "Users can create own tokens" ON app_verification_tokens
        FOR INSERT WITH CHECK ((SELECT auth.uid()) = user_id);
      
      -- Favorites policies
      CREATE POLICY "Users can view own favorites" ON favorites
        FOR SELECT USING ((SELECT auth.uid()) = user_id);
      
      CREATE POLICY "Users can create own favorites" ON favorites
        FOR INSERT WITH CHECK ((SELECT auth.uid()) = user_id);
      
      CREATE POLICY "Users can delete own favorites" ON favorites
        FOR DELETE USING ((SELECT auth.uid()) = user_id);
      
      -- Channel schedule policies
      CREATE POLICY "Schedule is viewable by everyone" ON channel_schedule
        FOR SELECT USING (true);
    `
  },
  {
    description: 'Criando função handle_new_user',
    sql: `
      CREATE OR REPLACE FUNCTION handle_new_user()
      RETURNS TRIGGER AS $$
      BEGIN
        INSERT INTO public.profiles (id, full_name, avatar_url)
        VALUES (
          NEW.id,
          NEW.raw_user_meta_data->>'full_name',
          NEW.raw_user_meta_data->>'avatar_url'
        )
        ON CONFLICT (id) DO NOTHING;
        RETURN NEW;
      END;
      $$ LANGUAGE plpgsql SECURITY DEFINER;
      
      DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
      CREATE TRIGGER on_auth_user_created
        AFTER INSERT ON auth.users
        FOR EACH ROW EXECUTE FUNCTION handle_new_user();
    `
  }
]

// Executar migração
async function runMigration() {
  console.log('🚀 Iniciando migração do banco de dados...')
  console.log(`📌 Supabase URL: ${SUPABASE_URL}`)
  
  let successCount = 0
  let errorCount = 0
  
  for (const step of migrationSteps) {
    const success = await executeSQL(step.sql, step.description)
    if (success) {
      successCount++
    } else {
      errorCount++
    }
  }
  
  console.log('\n📊 Resumo da migração:')
  console.log(`   ✅ Sucesso: ${successCount}`)
  console.log(`   ❌ Erros: ${errorCount}`)
  
  if (errorCount === 0) {
    console.log('\n🎉 Migração concluída com sucesso!')
    console.log('\n📋 Próximos passos:')
    console.log('1. Configure as variáveis de ambiente no Railway')
    console.log('2. Faça o deploy do projeto')
    console.log('3. Teste o endpoint /api/health')
  } else {
    console.log('\n⚠️  Migração concluída com alguns erros')
    console.log('   Verifique o Supabase Dashboard para mais detalhes')
  }
}

// Executar
runMigration().catch(console.error)