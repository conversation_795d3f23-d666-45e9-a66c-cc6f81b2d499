#!/bin/bash

# Script para aplicar migrations manualmente no Supabase

echo "🚀 Aplicando migrations no Supabase..."

# Verificar se as variáveis de ambiente estão definidas
if [ -z "$SUPABASE_DB_URL" ]; then
  echo "❌ Erro: SUPABASE_DB_URL não está definida"
  echo "Execute: export SUPABASE_DB_URL='postgresql://postgres:[password]@[host]:[port]/postgres'"
  exit 1
fi

# Aplicar migrations
echo "📦 Aplicando migration 025_fix_app_activation.sql..."
psql "$SUPABASE_DB_URL" -f supabase/migrations/025_fix_app_activation.sql

echo "📦 Aplicando migration 026_fix_check_user_access_ambiguity.sql..."
psql "$SUPABASE_DB_URL" -f supabase/migrations/026_fix_check_user_access_ambiguity.sql

echo "✅ Migrations aplicadas com sucesso!"

# Verificar se as tabelas foram criadas
echo ""
echo "🔍 Verificando tabelas criadas..."
psql "$SUPABASE_DB_URL" -c "SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name IN ('anonymous_access', 'activation_logs');"