#!/usr/bin/env tsx

import { fetchIPTVStreams, fetchIPTVChannels } from '../src/lib/iptv-org-server'
import { isSelectedSportsChannel } from '../src/lib/selected-sports-channels'
import { getFixedChannelId } from '../src/lib/channel-fixes'

async function debugChannelMapping() {
  console.log('🔍 Debug Channel Mapping\n')
  
  try {
    // Fetch data
    console.log('Fetching IPTV data...')
    const [streams, channels] = await Promise.all([
      fetchIPTVStreams(),
      fetchIPTVChannels()
    ])
    
    console.log(`Total streams: ${streams.length}`)
    console.log(`Total channels: ${channels.length}\n`)
    
    // Create channel map
    const channelsMap = new Map()
    channels.forEach(ch => channelsMap.set(ch.id, ch))
    
    // Filter sports streams
    const sportsStreams = streams.filter(stream => {
      const channel = channelsMap.get(stream.channel)
      return channel?.categories?.includes('sports')
    })
    
    console.log(`Sports streams: ${sportsStreams.length}\n`)
    
    // Get selected streams
    const selectedStreams = sportsStreams.filter(stream => 
      isSelectedSportsChannel(stream.channel)
    )
    
    console.log(`Selected sports channels: ${selectedStreams.length}\n`)
    
    // Look for specific channels
    const testChannels = [
      '30AGolfKingdom.us',
      'ACCDigitalNetwork.us',
      'Adjarasport1.ge',
      'AntenaSport.hr'
    ]
    
    console.log('=== Testing specific channels ===\n')
    
    for (const testId of testChannels) {
      console.log(`\nTesting: ${testId}`)
      console.log('-'.repeat(50))
      
      // Find in streams
      const stream = streams.find(s => s.channel === testId)
      const channel = channelsMap.get(testId)
      
      console.log(`Found in streams: ${!!stream}`)
      console.log(`Found in channels: ${!!channel}`)
      
      if (stream) {
        console.log(`Stream URL: ${stream.url}`)
        console.log(`Stream has referrer: ${!!stream.http_referrer}`)
      }
      
      if (channel) {
        console.log(`Channel name: ${channel.name}`)
        console.log(`Channel categories: ${channel.categories?.join(', ')}`)
      }
      
      // Test selection
      console.log(`Is selected: ${isSelectedSportsChannel(testId)}`)
      
      // Test channel fix
      const fixed = getFixedChannelId(testId)
      console.log(`Fixed channel ID: ${fixed}`)
      console.log(`ID changed: ${fixed !== testId}`)
      
      // Test with iptv- prefix
      const withPrefix = `iptv-${testId}`
      const fixedWithPrefix = getFixedChannelId(withPrefix)
      console.log(`Fixed with prefix: ${fixedWithPrefix}`)
    }
    
    // List all Golf channels
    console.log('\n\n=== All Golf channels ===')
    console.log('-'.repeat(50))
    
    const golfChannels = streams.filter(s => 
      s.channel?.toLowerCase().includes('golf') ||
      s.channel?.includes('30A')
    )
    
    golfChannels.forEach(stream => {
      const channel = channelsMap.get(stream.channel)
      console.log(`\nChannel ID: ${stream.channel}`)
      console.log(`Name: ${channel?.name || 'N/A'}`)
      console.log(`URL: ${stream.url}`)
      console.log(`Is selected: ${isSelectedSportsChannel(stream.channel)}`)
    })
    
  } catch (error) {
    console.error('Error:', error)
  }
}

debugChannelMapping()