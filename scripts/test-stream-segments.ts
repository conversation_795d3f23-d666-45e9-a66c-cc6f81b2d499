// Using native fetch in Node.js 18+

interface SegmentTest {
  channel: string
  url: string
  manifestOk: boolean
  segmentsOk: boolean
  errors: string[]
  workingWithProxy?: boolean
}

const extractSegmentUrls = (m3u8Content: string, baseUrl: string): string[] => {
  const lines = m3u8Content.split('\n')
  const segments: string[] = []
  
  for (const line of lines) {
    const trimmed = line.trim()
    if (trimmed && !trimmed.startsWith('#') && (trimmed.includes('.ts') || trimmed.includes('.m3u8'))) {
      let segmentUrl: string
      
      if (trimmed.startsWith('http://') || trimmed.startsWith('https://')) {
        segmentUrl = trimmed
      } else if (trimmed.startsWith('/')) {
        const urlObj = new URL(baseUrl)
        segmentUrl = `${urlObj.protocol}//${urlObj.host}${trimmed}`
      } else {
        const baseWithoutFile = baseUrl.substring(0, baseUrl.lastIndexOf('/'))
        segmentUrl = `${baseWithoutFile}/${trimmed}`
      }
      
      segments.push(segmentUrl)
    }
  }
  
  return segments
}

const testStreamSegments = async (name: string, url: string): Promise<SegmentTest> => {
  console.log(`\n🔍 Testing segments for: ${name}`)
  
  const result: SegmentTest = {
    channel: name,
    url,
    manifestOk: false,
    segmentsOk: false,
    errors: []
  }
  
  try {
    // Test manifest
    const manifestResponse = await fetch(url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      }
    })
    
    if (!manifestResponse.ok) {
      result.errors.push(`Manifest failed: HTTP ${manifestResponse.status}`)
      console.log(`  ❌ Manifest failed: HTTP ${manifestResponse.status}`)
      return result
    }
    
    result.manifestOk = true
    console.log(`  ✅ Manifest loaded`)
    
    const manifestContent = await manifestResponse.text()
    const segments = extractSegmentUrls(manifestContent, url)
    
    console.log(`  📦 Found ${segments.length} segments/playlists`)
    
    // Test first few segments
    const testSegments = segments.slice(0, 3)
    let workingSegments = 0
    
    for (const segUrl of testSegments) {
      try {
        const segResponse = await fetch(segUrl, {
          method: 'HEAD',
          headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Referer': url,
            'Origin': new URL(url).origin
          }
        })
        
        if (segResponse.ok) {
          workingSegments++
          console.log(`    ✅ Segment OK`)
        } else {
          console.log(`    ❌ Segment failed: HTTP ${segResponse.status}`)
          result.errors.push(`Segment ${segResponse.status}: ${segUrl}`)
        }
      } catch (error) {
        console.log(`    ❌ Segment error: ${error}`)
        result.errors.push(`Segment error: ${error}`)
      }
    }
    
    result.segmentsOk = workingSegments > 0
    
    // If segments fail, test with proxy
    if (!result.segmentsOk) {
      console.log(`  🔄 Testing with proxy...`)
      
      const proxyUrl = `http://localhost:3000/api/advanced-proxy?url=${encodeURIComponent(url)}`
      const proxyResponse = await fetch(proxyUrl)
      
      if (proxyResponse.ok) {
        result.workingWithProxy = true
        console.log(`    ✅ Works with proxy!`)
      } else {
        console.log(`    ❌ Proxy also failed`)
      }
    }
    
  } catch (error) {
    result.errors.push(`General error: ${error}`)
    console.log(`  ❌ Error: ${error}`)
  }
  
  return result
}

const main = async () => {
  console.log('🚀 Starting segment test...\n')
  
  // Channels that show as "working" but have playback issues
  const channels = [
    { name: 'Abu Dhabi Sports 1', url: 'https://vo-live-media.cdb.cdn.orange.com/Content/Channel/AbuDhabiSportsChannel1/HLS/index.m3u8' },
    { name: 'Abu Dhabi Sports 2', url: 'https://vo-live-media.cdb.cdn.orange.com/Content/Channel/AbuDhabiSportsChannel2/HLS/index.m3u8' },
    { name: 'ACC Digital Network', url: 'https://raycom-accdn-firetv.amagi.tv/playlist.m3u8' },
    { name: 'Alkass Five', url: 'https://liveakgr.alkassdigital.net/hls/live/2097037/Alkass5azq/master.m3u8' },
    { name: '30A Golf Kingdom', url: 'https://30a-tv.com/feeds/vidaa/golf.m3u8' },
  ]
  
  const results: SegmentTest[] = []
  
  for (const channel of channels) {
    const result = await testStreamSegments(channel.name, channel.url)
    results.push(result)
    await new Promise(resolve => setTimeout(resolve, 1000))
  }
  
  // Summary
  console.log('\n\n📊 SEGMENT TEST SUMMARY')
  console.log('='.repeat(80))
  
  const fullyWorking = results.filter(r => r.manifestOk && r.segmentsOk)
  const manifestOnly = results.filter(r => r.manifestOk && !r.segmentsOk)
  const needProxy = results.filter(r => r.workingWithProxy)
  
  console.log(`✅ Fully working: ${fullyWorking.length}/${results.length}`)
  console.log(`📄 Manifest OK but segments fail: ${manifestOnly.length}`)
  console.log(`🔄 Work with proxy: ${needProxy.length}`)
  
  if (manifestOnly.length > 0) {
    console.log('\n⚠️  CHANNELS WITH SEGMENT ISSUES:')
    manifestOnly.forEach(r => {
      console.log(`  - ${r.channel}`)
      r.errors.slice(0, 2).forEach(err => {
        console.log(`    ${err}`)
      })
      if (r.workingWithProxy) {
        console.log(`    ✅ Solution: Use proxy`)
      }
    })
  }
  
  // Save results
  const fs = await import('fs')
  fs.writeFileSync('segment-test-results.json', JSON.stringify(results, null, 2))
  console.log('\n💾 Results saved to segment-test-results.json')
}

main().catch(console.error)