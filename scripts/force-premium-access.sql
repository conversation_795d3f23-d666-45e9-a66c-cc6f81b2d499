-- <PERSON><PERSON><PERSON> para forçar acesso premium manualmente
-- Use APENAS para testes ou correções emergenciais

-- 1. <PERSON><PERSON>, encontre o usuário pelo email
SELECT 
    id,
    email,
    subscription_tier,
    subscription_status,
    subscription_current_period_end,
    stripe_customer_id,
    stripe_subscription_id
FROM profiles
WHERE email = 'EMAIL_DO_USUARIO_AQUI';

-- 2. Para dar acesso premium por 30 dias (substitua USER_ID pelo ID encontrado acima)
UPDATE profiles
SET 
    subscription_tier = 'premium',
    subscription_status = 'active',
    subscription_current_period_end = NOW() + INTERVAL '30 days',
    updated_at = NOW()
WHERE id = 'USER_ID_AQUI';

-- 3. Se o usuário usa Stack Auth, atualize também a stack_profiles
UPDATE stack_profiles
SET 
    subscription_tier = 'premium',
    subscription_status = 'active',
    subscription_current_period_end = NOW() + INTERVAL '30 days',
    updated_at = NOW()
WHERE id = 'USER_ID_AQUI' OR email = 'EMAIL_DO_USUARIO_AQUI';

-- 4. Verificar o resultado
SELECT 
    'profiles' as table_name,
    id,
    email,
    subscription_tier,
    subscription_status,
    subscription_current_period_end
FROM profiles
WHERE id = 'USER_ID_AQUI'
UNION ALL
SELECT 
    'stack_profiles' as table_name,
    id,
    email,
    subscription_tier,
    subscription_status,
    subscription_current_period_end
FROM stack_profiles
WHERE id = 'USER_ID_AQUI' OR email = 'EMAIL_DO_USUARIO_AQUI';

-- 5. Para criar um registro na stripe_subscriptions (se necessário)
INSERT INTO stripe_subscriptions (
    user_id,
    stripe_customer_id,
    stripe_subscription_id,
    status,
    current_period_start,
    current_period_end,
    cancel_at_period_end
) VALUES (
    'USER_ID_AQUI',
    'cus_manual_' || gen_random_uuid()::text,
    'sub_manual_' || gen_random_uuid()::text,
    'active',
    NOW(),
    NOW() + INTERVAL '30 days',
    false
) ON CONFLICT (user_id) DO UPDATE SET
    status = 'active',
    current_period_start = NOW(),
    current_period_end = NOW() + INTERVAL '30 days',
    cancel_at_period_end = false,
    updated_at = NOW();

-- 6. Chamar a função de sincronização (se existir)
SELECT sync_stripe_subscription(
    'USER_ID_AQUI',
    'cus_manual_test',
    'sub_manual_test',
    'active',
    (NOW() + INTERVAL '30 days')::text,
    false,
    NULL,
    NULL
);