-- Script para ativar premium diretamente no banco
-- Execute este script no Supabase SQL Editor

-- 1. Ativar premium para o email específico
UPDATE profiles
SET 
  subscription_tier = 'premium',
  subscription_status = 'active',
  subscription_current_period_end = NOW() + INTERVAL '30 days',
  stripe_customer_id = COALESCE(stripe_customer_id, 'cus_manual_direct'),
  stripe_subscription_id = COALESCE(stripe_subscription_id, 'sub_manual_direct'),
  updated_at = NOW()
WHERE email = '<EMAIL>';

-- 2. Se não existir, criar o perfil
INSERT INTO profiles (
  id,
  email,
  subscription_tier,
  subscription_status,
  subscription_current_period_end,
  stripe_customer_id,
  stripe_subscription_id,
  created_at,
  updated_at
)
SELECT 
  id,
  email,
  'premium',
  'active',
  NOW() + INTERVAL '30 days',
  'cus_manual_direct',
  'sub_manual_direct',
  NOW(),
  NOW()
FROM auth.users 
WHERE email = '<EMAIL>'
AND NOT EXISTS (
  SELECT 1 FROM profiles WHERE email = '<EMAIL>'
);

-- 3. Verificar resultado
SELECT 
  id,
  email,
  subscription_tier,
  subscription_status,
  subscription_current_period_end,
  subscription_current_period_end > NOW() as is_active,
  stripe_customer_id,
  stripe_subscription_id
FROM profiles 
WHERE email = '<EMAIL>';

-- 4. Testar função check_user_access
SELECT * FROM check_user_access(
  (SELECT id FROM profiles WHERE email = '<EMAIL>')
);