#!/usr/bin/env tsx

// Script para testar e debugar o sistema de trial timer

async function testTrialStore() {
  console.log('🔍 Testando Trial Store...\n')
  
  // Simular armazenamento do Zustand
  const mockStore = {
    startTime: null as number | null,
    timeRemaining: 300, // 5 minutos
    isExpired: false,
    hasSeenOffer: false,
    sessionId: null as string | null,
    trialId: null as string | null,
  }
  
  console.log('Estado inicial:', mockStore)
  
  // Simular início do trial
  console.log('\n🚀 Simulando início do trial...')
  mockStore.startTime = Date.now()
  mockStore.sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  
  console.log('Estado após iniciar:', {
    ...mockStore,
    startTime: new Date(mockStore.startTime).toISOString()
  })
  
  // Simular passagem de tempo
  console.log('\n⏱️ Simulando passagem de 2 minutos...')
  const elapsed = 120 // 2 minutos em segundos
  mockStore.timeRemaining = Math.max(0, 300 - elapsed)
  
  console.log('Estado após 2 minutos:', mockStore)
  
  // Simular expiração
  console.log('\n⏱️ Simulando expiração do trial...')
  mockStore.timeRemaining = 0
  mockStore.isExpired = true
  
  console.log('Estado após expiração:', mockStore)
  
  // Verificar condições de reset
  console.log('\n🔄 Verificando condições de reset...')
  const hoursSinceStart = mockStore.startTime ? (Date.now() - mockStore.startTime) / (1000 * 60 * 60) : 0
  const canReset = hoursSinceStart > 24
  
  console.log('Horas desde o início:', hoursSinceStart.toFixed(2))
  console.log('Pode resetar (24h+)?', canReset)
  
  // Simular LocalStorage
  console.log('\n💾 Simulando LocalStorage...')
  const localStorageKey = 'trial-storage'
  const storageData = {
    state: mockStore,
    version: 0
  }
  
  console.log('Dados que seriam salvos:', JSON.stringify(storageData, null, 2))
  
  // Simular rehydrate
  console.log('\n💧 Simulando rehydrate do Zustand...')
  if (!mockStore.startTime) {
    console.log('❌ Sem startTime - novo usuário detectado')
    console.log('✅ Configurando estado inicial limpo')
    mockStore.timeRemaining = 300
    mockStore.isExpired = false
    mockStore.hasSeenOffer = false
  } else {
    const elapsed = Math.floor((Date.now() - mockStore.startTime) / 1000)
    console.log('Tempo decorrido desde o início:', elapsed, 'segundos')
    
    if (elapsed >= 300 && !mockStore.isExpired) {
      console.log('⚠️ Trial expirou enquanto offline')
      mockStore.isExpired = true
      mockStore.timeRemaining = 0
    } else if (elapsed < 300 && !mockStore.isExpired) {
      const remaining = Math.max(0, 300 - elapsed)
      console.log('✅ Trial ainda ativo, tempo restante:', remaining, 'segundos')
      mockStore.timeRemaining = remaining
    }
  }
  
  console.log('\nEstado final após rehydrate:', mockStore)
  
  // Verificar problemas comuns
  console.log('\n🔍 Verificando problemas comuns...')
  const problems = []
  
  if (!mockStore.startTime && mockStore.isExpired) {
    problems.push('❌ Estado inválido: isExpired=true mas sem startTime')
  }
  
  if (mockStore.timeRemaining > 0 && mockStore.isExpired) {
    problems.push('❌ Estado inválido: timeRemaining > 0 mas isExpired=true')
  }
  
  if (!mockStore.sessionId) {
    problems.push('⚠️ Sem sessionId - será gerado no próximo acesso')
  }
  
  if (problems.length === 0) {
    console.log('✅ Nenhum problema detectado')
  } else {
    problems.forEach(p => console.log(p))
  }
  
  // Sugestões de correção
  console.log('\n💡 Sugestões para correção:')
  console.log('1. Garantir que startTrial() seja chamado quando startTime é null')
  console.log('2. Verificar se o componente TrialTimer está montado corretamente')
  console.log('3. Adicionar logs no useTrialTimer hook')
  console.log('4. Verificar se o localStorage está sendo limpo corretamente')
  console.log('5. Garantir que o estado inicial seja consistente')
}

// Executar teste
testTrialStore().catch(console.error)