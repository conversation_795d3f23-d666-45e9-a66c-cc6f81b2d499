-- Script para ativar assinatura premium manualmente
-- Execute este script no SQL Editor do Supabase

-- 1. <PERSON><PERSON>, vamos buscar o usuário
SELECT id, email FROM auth.users WHERE email = '<EMAIL>';

-- 2. Vamos ver se o perfil existe
SELECT * FROM profiles WHERE id = '590b3ae5-92f2-47d2-91a6-8c29c23dc9d4';

-- 3. <PERSON><PERSON><PERSON> ou atualizar o perfil com assinatura premium
INSERT INTO profiles (
  id,
  email,
  subscription_tier,
  subscription_status,
  subscription_current_period_end,
  stripe_customer_id,
  stripe_subscription_id,
  created_at,
  updated_at
) VALUES (
  '590b3ae5-92f2-47d2-91a6-8c29c23dc9d4',
  '<EMAIL>',
  'premium',
  'active',
  NOW() + INTERVAL '30 days',
  'cus_manual_' || extract(epoch from now())::text,
  'sub_manual_' || extract(epoch from now())::text,
  NOW(),
  NOW()
) ON CONFLICT (id) DO UPDATE SET
  subscription_tier = 'premium',
  subscription_status = 'active',
  subscription_current_period_end = NOW() + INTERVAL '30 days',
  stripe_customer_id = COALESCE(profiles.stripe_customer_id, 'cus_manual_' || extract(epoch from now())::text),
  stripe_subscription_id = COALESCE(profiles.stripe_subscription_id, 'sub_manual_' || extract(epoch from now())::text),
  updated_at = NOW();

-- 4. Verificar se funcionou
SELECT 
  id,
  email,
  subscription_tier,
  subscription_status,
  subscription_current_period_end,
  subscription_current_period_end > NOW() as is_active,
  stripe_customer_id,
  stripe_subscription_id
FROM profiles 
WHERE id = '590b3ae5-92f2-47d2-91a6-8c29c23dc9d4';

-- 5. Testar a função check_user_access
SELECT * FROM check_user_access('590b3ae5-92f2-47d2-91a6-8c29c23dc9d4');