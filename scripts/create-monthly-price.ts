#!/usr/bin/env tsx

import 'dotenv/config'

// Te<PERSON>rariam<PERSON> definir as chaves se não estiverem no ambiente
const STRIPE_SECRET_KEY = process.env.STRIPE_SECRET_KEY || '***********************************************************************************************************'

async function createMonthlyPrice() {
  try {
    console.log('🔧 Criando preço mensal recorrente no Stripe...\n')

    const response = await fetch('https://api.stripe.com/v1/prices', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${STRIPE_SECRET_KEY}`,
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        'product': 'prod_SlBxDaGOJCF0ZT',
        'unit_amount': '2000', // $20.00 em centavos
        'currency': 'usd',
        'recurring[interval]': 'month',
        'recurring[interval_count]': '1',
        'nickname': 'NewSpports Premium Monthly',
        'metadata[description]': 'Assinatura mensal NewSpports Premium',
      }),
    })

    if (!response.ok) {
      const error = await response.text()
      throw new Error(`Stripe API error: ${error}`)
    }

    const price = await response.json()

    console.log('\n✅ Preço mensal criado com sucesso!')
    console.log('   ID:', price.id)
    console.log('   Valor:', `$${price.unit_amount / 100} USD`)
    console.log('   Intervalo:', price.recurring?.interval)
    console.log('   Tipo:', price.type)

    // Atualizar produto com preço padrão
    const updateResponse = await fetch(`https://api.stripe.com/v1/products/prod_SlBxDaGOJCF0ZT`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${STRIPE_SECRET_KEY}`,
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        'default_price': price.id,
      }),
    })

    if (updateResponse.ok) {
      console.log('\n✅ Preço definido como padrão do produto')
    }

    console.log('\n📝 IMPORTANTE: Atualize o Railway com este ID:')
    console.log(`STRIPE_PRICE_ID=${price.id}`)
    console.log('\nE também no seu .env.local!')

    return price.id

  } catch (error) {
    console.error('❌ Erro ao criar preço:', error)
    process.exit(1)
  }
}

// Executar
createMonthlyPrice()