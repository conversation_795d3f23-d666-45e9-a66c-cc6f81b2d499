// Script para buscar URLs alternativas para canais de futebol

const alternativeStreams = {
  'barca-tv': [
    'https://fcbstream.s.llnwi.net/fcbstream/HD/fcbstream.m3u8',
    'https://live.fcbcdn.com/hls/live/2027222/rmtv-es-web/master.m3u8',
    'http://*************/s27/04.m3u8',
    'https://live.barcatv.fcbarcelona.com/hls/live/2003188/rmtv-cat-web/master.m3u8'
  ],
  'las-palmas-tv': [
    'https://5940924978228.streamlock.net/8081/8081/master.m3u8',
    'https://tv.radiocanarias.com/live/rc_720/index.m3u8',
    'https://cdn21.fractalmedia.es/live319/hls/laspalmas/high/index.m3u8'
  ],
  'esport3': [
    'https://directes-tv-es.ccma.cat/live-origin/esport3-hls/master.m3u8',
    'https://directes-tv-int.ccma.cat/live-origin/esport3-hls/master.m3u8'
  ],
  'teledeporte': [
    'https://ztnr.rtve.es/ztnr/1694255.m3u8',
    'https://rtvelivestream-clnx.rtve.es/rtvesec/tdp/tdp_main.m3u8'
  ]
}

// ANSI color codes
const colors = {
  reset: '\x1b[0m',
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  gray: '\x1b[90m',
  cyan: '\x1b[36m'
}

async function testStream(url: string): Promise<{ working: boolean; error?: string; responseTime?: number }> {
  const startTime = Date.now()
  
  try {
    const controller = new AbortController()
    const timeout = setTimeout(() => controller.abort(), 8000) // 8 second timeout
    
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'application/vnd.apple.mpegurl, application/x-mpegurl, video/mp2t, video/mp4, */*',
        'Accept-Language': 'es-ES,es;q=0.9,en;q=0.8',
        'Origin': 'https://www.newspports.com',
        'Referer': 'https://www.newspports.com/'
      },
      signal: controller.signal
    })
    
    clearTimeout(timeout)
    const responseTime = Date.now() - startTime
    
    if (response.ok) {
      return { working: true, responseTime }
    } else {
      return { 
        working: false, 
        error: `HTTP ${response.status}: ${response.statusText}`,
        responseTime
      }
    }
  } catch (error) {
    const responseTime = Date.now() - startTime
    if (error instanceof Error) {
      if (error.name === 'AbortError') {
        return { working: false, error: 'Timeout', responseTime }
      }
      return { working: false, error: error.message, responseTime }
    }
    return { working: false, error: 'Unknown error', responseTime }
  }
}

async function searchAlternativeStreams() {
  console.log(`\n${colors.cyan}═══════════════════════════════════════════════════════════════${colors.reset}`)
  console.log(`${colors.cyan}     BUSCANDO STREAMS ALTERNATIVOS - NEWSPPORTS${colors.reset}`)
  console.log(`${colors.cyan}═══════════════════════════════════════════════════════════════${colors.reset}\n`)
  
  const workingStreams: Record<string, string[]> = {}
  
  for (const [channelId, urls] of Object.entries(alternativeStreams)) {
    console.log(`${colors.yellow}▶ Testando alternativas para:${colors.reset} ${channelId}`)
    workingStreams[channelId] = []
    
    for (const url of urls) {
      const result = await testStream(url)
      
      if (result.working) {
        console.log(`  ${colors.green}✓${colors.reset} ${url.substring(0, 50)}... (${result.responseTime}ms)`)
        workingStreams[channelId].push(url)
      } else {
        console.log(`  ${colors.red}✗${colors.reset} ${url.substring(0, 50)}... - ${result.error}`)
      }
    }
    
    console.log('')
  }
  
  // Summary
  console.log(`${colors.cyan}═══════════════════════════════════════════════════════════════${colors.reset}`)
  console.log(`${colors.cyan}                    STREAMS FUNCIONANDO${colors.reset}`)
  console.log(`${colors.cyan}═══════════════════════════════════════════════════════════════${colors.reset}\n`)
  
  for (const [channelId, urls] of Object.entries(workingStreams)) {
    if (urls.length > 0) {
      console.log(`${colors.green}✓ ${channelId}:${colors.reset}`)
      urls.forEach(url => {
        console.log(`   ${colors.gray}${url}${colors.reset}`)
      })
    } else {
      console.log(`${colors.red}✗ ${channelId}: Nenhuma alternativa funcionando${colors.reset}`)
    }
  }
}

// Run the search
searchAlternativeStreams().catch(console.error)