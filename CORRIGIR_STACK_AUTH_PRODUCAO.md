# 🔧 CORRIGIR STACK AUTH EM PRODUÇÃO

## 🚨 PROBLEMA IDENTIFICADO

O código atual está usando **apenas usuários de teste** e não está conectado ao Stack Auth real em produção!

## 🎯 SOLUÇÃO IMEDIATA

### PASSO 1: Verificar Variáveis no Railway

1. **Entre no Railway Dashboard**
2. **Vá em Variables**
3. **Verifique se estas variáveis existem E estão corretas**:

```bash
NEXT_PUBLIC_STACK_PROJECT_ID=prj_[seu-id-real]
NEXT_PUBLIC_STACK_PUBLISHABLE_CLIENT_KEY=pck_[sua-key-real]
STACK_SECRET_SERVER_KEY=ssk_[sua-key-real]
```

⚠️ **IMPORTANTE**: O valor `prj_123456789` no erro é um exemplo! Você precisa usar o ID REAL do seu projeto Stack Auth!

### PASSO 2: <PERSON><PERSON>ar as <PERSON><PERSON> Corre<PERSON> do Stack Auth

1. **Acesse**: https://app.stack-auth.com
2. **Entre no seu projeto**
3. **Vá em Settings > API Keys**
4. **Copie EXATAMENTE**:
   - Project ID (começa com `prj_`)
   - Publishable Client Key (começa com `pck_`)
   - Secret Server Key (começa com `ssk_`)

### PASSO 3: Atualizar no Railway

1. **Para cada variável no Railway**:
   - Clique no lápis para editar
   - Cole o valor EXATO do Stack Auth
   - Clique em Save

2. **EXEMPLO CORRETO**:
```
NEXT_PUBLIC_STACK_PROJECT_ID=prj_a1b2c3d4e5f6
NEXT_PUBLIC_STACK_PUBLISHABLE_CLIENT_KEY=pck_m988zs8n54pjh8rjgdv70b9crgspcpy3vq0zwwgy72pbr
STACK_SECRET_SERVER_KEY=ssk_x9y8z7w6v5u4t3s2r1q0
```

### PASSO 4: Configurar URLs no Stack Auth

1. **No Stack Auth Dashboard**
2. **Vá em Settings > URLs**
3. **Configure**:
   - App URL: `https://newspports-production.up.railway.app`
   - Sign-in URL: `https://newspports-production.up.railway.app/login`
   - OAuth Redirect URL: `https://newspports-production.up.railway.app/handler/oauth-callback`

### PASSO 5: Configurar OAuth Providers

1. **No Stack Auth, vá em Authentication > OAuth Providers**
2. **Para Google (e outros que usar)**:
   - Enable: ON
   - Client ID: [seu-google-client-id]
   - Client Secret: [seu-google-client-secret]
   - Redirect URL será mostrado - copie!

### PASSO 6: Atualizar Google Cloud Console

1. **Acesse**: https://console.cloud.google.com
2. **APIs & Services > Credentials**
3. **Edite seu OAuth 2.0 Client**
4. **Adicione em Authorized redirect URIs**:
   ```
   https://api.stack-auth.com/api/v1/auth/oauth/callback/google?after_callback_redirect_to=https%3A%2F%2Fnewspports-production.up.railway.app%2Fhandler%2Foauth-callback
   ```

### PASSO 7: Fazer Deploy

1. **No Railway, clique em "Redeploy"**
2. **Ou faça um commit**:
   ```bash
   git commit --allow-empty -m "Fix: Stack Auth production config"
   git push
   ```

## 🧪 TESTAR

1. **Acesse seu site**: https://newspports-production.up.railway.app
2. **Tente fazer login com Google**
3. **Abra o Console (F12)** e veja se há erros

## 🐛 DEBUGGING

### Se ainda der erro:

1. **No Console do browser (F12)**, execute:
```javascript
console.log({
  projectId: process.env.NEXT_PUBLIC_STACK_PROJECT_ID,
  publishableKey: process.env.NEXT_PUBLIC_STACK_PUBLISHABLE_CLIENT_KEY
})
```

2. **Se retornar undefined**:
   - As variáveis não estão chegando
   - Limpe o cache no Railway
   - Faça novo deploy

3. **Se retornar valores errados**:
   - Verifique se copiou corretamente do Stack Auth
   - Não use exemplos como `prj_123456789`

## 📝 CHECKLIST FINAL

- [ ] Variáveis com valores REAIS do Stack Auth (não exemplos)
- [ ] URLs configuradas no Stack Auth
- [ ] OAuth redirect configurado no Google Cloud
- [ ] Deploy feito após mudanças
- [ ] Cache limpo se necessário

## 🆘 AINDA COM PROBLEMAS?

Envie:
1. Screenshot das variáveis no Railway (esconda as keys)
2. Screenshot do erro no console
3. URL exata onde o erro acontece