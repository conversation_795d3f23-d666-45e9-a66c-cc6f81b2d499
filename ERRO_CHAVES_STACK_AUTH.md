# 🚨 ERRO CRÍTICO: Chaves Stack Auth Incorretas

## ❌ O QUE ESTÁ ERRADO:

Você colocou a MESMA chave nas duas variáveis:
```
NEXT_PUBLIC_STACK_PUBLISHABLE_CLIENT_KEY = pck_m988zs8n54pjh8rjgdv70b9crgspcpy3vq0zwwgy72pbr
STACK_SECRET_SERVER_KEY = pck_m988zs8n54pjh8rjgdv70b9crgspcpy3vq0zwwgy72pbr
```

**ISSO ESTÁ ERRADO!** São chaves DIFERENTES!

## ✅ COMO DEVE SER:

```
NEXT_PUBLIC_STACK_PUBLISHABLE_CLIENT_KEY = pck_... (Publishable Key)
STACK_SECRET_SERVER_KEY = ssk_... (Secret Key - começa com ssk_)
```

## 🔍 ALÉM DISSO, ESTÁ FALTANDO:

```
NEXT_PUBLIC_STACK_PROJECT_ID = ???
```

Você não mencionou esta variável, mas ela é ESSENCIAL!

## 📋 PASSO A PASSO CORRETO:

### 1. Entre no Stack Auth Dashboard
https://app.stack-auth.com

### 2. Vá em Settings > API Keys

### 3. Você verá 3 chaves diferentes:

1. **Project ID** 
   - Começa com: `prj_`
   - Exemplo: `prj_a1b2c3d4e5f6`
   - Use em: `NEXT_PUBLIC_STACK_PROJECT_ID`

2. **Publishable Client Key**
   - Começa com: `pck_`
   - Exemplo: `pck_m988zs8n54pjh8rjgdv70b9crgspcpy3vq0zwwgy72pbr`
   - Use em: `NEXT_PUBLIC_STACK_PUBLISHABLE_CLIENT_KEY`

3. **Secret Server Key**
   - Começa com: `ssk_`
   - Exemplo: `ssk_x9y8z7w6v5u4t3s2r1q0p9o8n7m6l5k4`
   - Use em: `STACK_SECRET_SERVER_KEY`

## 🔧 CORREÇÃO NO RAILWAY:

### 1. Adicione/Corrija estas 3 variáveis:

```bash
# 1. PROJECT ID (você não tem essa!)
NEXT_PUBLIC_STACK_PROJECT_ID=prj_[copie do Stack Auth]

# 2. PUBLISHABLE KEY (você já tem)
NEXT_PUBLIC_STACK_PUBLISHABLE_CLIENT_KEY=pck_m988zs8n54pjh8rjgdv70b9crgspcpy3vq0zwwgy72pbr

# 3. SECRET KEY (você tem a ERRADA!)
STACK_SECRET_SERVER_KEY=ssk_[copie do Stack Auth - começa com ssk_]
```

## 📸 VISUAL DO STACK AUTH DASHBOARD:

No Stack Auth, você verá algo assim:

```
API Keys
────────────────────────────────────
Project ID:              prj_abc123def456
Publishable Client Key:  pck_m988zs8n54pjh8rjgdv70b9crgspcpy3vq0zwwgy72pbr
Secret Server Key:       ssk_xyz789ghi012jkl345mno678pqr901stu234vwx567
```

## ⚠️ IMPORTANTE:

1. **pck_** = Publishable (pública, pode expor)
2. **ssk_** = Secret (privada, NUNCA expor)
3. **prj_** = Project ID (identificador do projeto)

## 🎯 AÇÃO IMEDIATA:

1. Volte ao Stack Auth Dashboard
2. Copie a **Secret Server Key** (começa com `ssk_`)
3. Copie o **Project ID** (começa com `prj_`)
4. Atualize no Railway:
   - Adicione `NEXT_PUBLIC_STACK_PROJECT_ID`
   - Corrija `STACK_SECRET_SERVER_KEY`
5. Faça novo deploy

## 🧪 TESTE RÁPIDO:

Após corrigir, no console do browser (F12):
```javascript
console.log('Project ID:', process.env.NEXT_PUBLIC_STACK_PROJECT_ID)
// Deve mostrar: prj_algumacoisaaqui

console.log('Publishable Key:', process.env.NEXT_PUBLIC_STACK_PUBLISHABLE_CLIENT_KEY)  
// Deve mostrar: pck_m988zs8n54pjh8rjgdv70b9crgspcpy3vq0zwwgy72pbr
```

Se mostrar `undefined` ou `prj_123456789`, ainda está errado!