# 🍪 RESOLVER ERRO: Inner OAuth cookie not found

## 🔍 CAUSA DO ERRO:

Este erro acontece quando:
1. O cookie de sessão OAuth é perdido
2. Problema com HTTPS/HTTP
3. Configuração de cookies incorreta
4. Domínio não corresponde

## 🛠️ SOLUÇÕES:

### 1️⃣ **LIMPAR DADOS DO NAVEGADOR**

1. Abra o DevTools (F12)
2. Vá em Application/Storage
3. Clear Site Data
4. Tente fazer login novamente

### 2️⃣ **VERIFICAR HTTPS**

Certifique-se de acessar com HTTPS:
- ✅ https://newspports.com
- ❌ http://newspports.com

### 3️⃣ **ATUALIZAR CONFIGURAÇÃO NO CÓDIGO**

Crie um arquivo para corrigir cookies em produção:

```typescript
// src/app/api/auth/[...stack]/route.ts
import { StackHandler } from "@stackframe/stack";
import { stackServerApp } from "@/lib/stack-server";

export const runtime = "nodejs";

const handler = StackHandler({
  app: stackServerApp,
  // Configuração de cookies para produção
  cookieOptions: {
    secure: true, // Força HTTPS
    sameSite: 'lax', // Permite redirecionamentos OAuth
    domain: '.newspports.com', // Permite subdomínios
    path: '/',
    httpOnly: true
  }
});

export { handler as GET, handler as POST };
```

### 4️⃣ **VERIFICAR VARIÁVEIS NO RAILWAY**

Confirme que está assim:
```bash
NEXT_PUBLIC_APP_URL=https://newspports.com
NODE_ENV=production
```

### 5️⃣ **ADICIONAR MIDDLEWARE DE COOKIES**

```typescript
// src/middleware.ts
import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

export function middleware(request: NextRequest) {
  const response = NextResponse.next();
  
  // Configurar headers para cookies em produção
  if (process.env.NODE_ENV === 'production') {
    response.headers.set(
      'Set-Cookie',
      `Path=/; Secure; SameSite=Lax; Domain=.newspports.com`
    );
  }
  
  return response;
}

export const config = {
  matcher: ['/api/auth/:path*', '/handler/:path*']
};
```

## 🚀 SOLUÇÃO RÁPIDA:

### Tente nesta ordem:

1. **Use uma aba anônima/privada**
2. **Acesse direto**: https://newspports.com/login
3. **Não use o botão voltar do navegador durante o login**
4. **Complete o login sem interrupções**

## 🔧 SOLUÇÃO DEFINITIVA:

### Vamos adicionar um handler específico:

```typescript
// src/app/handler/oauth-callback/route.ts
import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    // Pegar código OAuth
    const searchParams = request.nextUrl.searchParams;
    const code = searchParams.get('code');
    const state = searchParams.get('state');
    
    if (!code) {
      return NextResponse.redirect(
        new URL('/login?error=oauth_failed', request.url)
      );
    }
    
    // Redirecionar para Stack Auth handler
    const stackUrl = new URL('/api/auth/callback', request.url);
    stackUrl.searchParams.set('code', code);
    if (state) stackUrl.searchParams.set('state', state);
    
    return NextResponse.redirect(stackUrl);
    
  } catch (error) {
    console.error('OAuth callback error:', error);
    return NextResponse.redirect(
      new URL('/login?error=oauth_error', request.url)
    );
  }
}
```

## ✅ CHECKLIST DE RESOLUÇÃO:

- [ ] Limpar cookies/cache do navegador
- [ ] Usar HTTPS (não HTTP)
- [ ] Testar em aba anônima
- [ ] Verificar NEXT_PUBLIC_APP_URL
- [ ] Não interromper o fluxo de login

## 💡 DICA PRO:

Se continuar com erro, adicione este debug:

```javascript
// No console do navegador (F12)
document.cookie.split(';').forEach(c => console.log(c.trim()));
```

Isso mostra todos os cookies - procure por algum do Stack Auth.