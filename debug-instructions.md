# 🔍 Instruções de Debug - StreamPlus España

## Teste Rápido do Player

### 1. Abra http://localhost:3000/browse

### 2. No console do browser (F12), execute:

```javascript
// Ver canais disponíveis
const channels = document.querySelectorAll('a[href^="/watch/iptv-"]');
console.log(`Encontrados ${channels.length} canais IPTV`);

// Pegar o primeiro canal
if (channels.length > 0) {
  const firstChannel = channels[0];
  const channelId = firstChannel.href.split('/watch/')[1];
  console.log(`Primeiro canal: ${channelId}`);
  
  // Navegar para o canal
  window.location.href = firstChannel.href;
}
```

### 3. Após carregar a página do player, execute:

```javascript
// Ver logs completos do que aconteceu
const logs = window.__logger.getHistory();

// Filtrar logs relevantes
const relevantLogs = logs.filter(log => 
  ['player', 'stream', 'iptv'].includes(log.tag)
);

console.log('=== LOGS RELEVANTES ===');
relevantLogs.forEach(log => {
  console.log(`[${log.tag}] ${log.message}`, log.data || '');
});

// Procurar especificamente pela URL do stream
const urlLogs = logs.filter(log => 
  log.message.includes('url') || 
  log.message.includes('URL') || 
  log.message.includes('mounted') ||
  log.message.includes('Loading HLS')
);

console.log('\n=== URLs NO PLAYER ===');
urlLogs.forEach(log => {
  if (log.data?.url || log.data?.streamUrl || log.data?.channelUrl) {
    console.log(log.message, {
      url: log.data.url || log.data.streamUrl || log.data.channelUrl
    });
  }
});
```

### 4. Testar canal específico que sabemos que funciona:

```javascript
// Testar San Marino RTV Sport
window.location.href = '/watch/iptv-SanMarinoRTVSport.sm';

// Após carregar, verificar:
setTimeout(() => {
  const recentLogs = window.__logger.getHistory().slice(-20);
  const playerLog = recentLogs.find(log => 
    log.tag === 'player' && log.message.includes('UniversalPlayer mounted')
  );
  
  if (playerLog) {
    console.log('Player recebeu:', playerLog.data);
  }
}, 2000);
```

## O que procurar nos logs:

1. **[stream] Watch page loaded** - Página carregou
2. **[stream] Fetching IPTV channel data** - Buscando dados
3. **[stream] IPTV channel data loaded** - Dados carregados com URL
4. **[stream] Final channel data** - Dados finais sendo usados
5. **[player] UniversalPlayer mounted** - Player iniciado com streamUrl
6. **[player] Loading HLS source** - HLS carregando a URL

## Se ainda mostrar vídeo mock:

Procure por:
- `streamUrl: "https://commondatastorage.googleapis.com..."` - Isso é o fallback
- `useFallback: true` - Player usando fallback

## URLs de teste conhecidas:

```javascript
// Estes canais funcionam:
const workingChannels = [
  'iptv-SanMarinoRTVSport.sm',
  'iptv-TurkmenistanSport.tm', 
  'iptv-TSN3.ca',
  'iptv-SOSKanalPlus.rs'
];

// Testar um por vez:
window.location.href = `/watch/${workingChannels[0]}`;
```