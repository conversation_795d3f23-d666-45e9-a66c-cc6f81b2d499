<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Upload Button</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-2xl mx-auto bg-white p-6 rounded-lg shadow">
        <h1 class="text-2xl font-bold mb-6">Test de Botón de Upload</h1>
        
        <!-- Test 1: Bot<PERSON> Simple -->
        <div class="mb-8">
            <h2 class="text-lg font-semibold mb-4">Test 1: <PERSON><PERSON><PERSON> Simple</h2>
            <input type="file" id="test1" accept="video/*" class="block w-full mb-4">
        </div>

        <!-- Test 2: Botón con label -->
        <div class="mb-8">
            <h2 class="text-lg font-semibold mb-4">Test 2: Bot<PERSON> con Label</h2>
            <label for="test2" class="bg-blue-500 text-white px-4 py-2 rounded cursor-pointer hover:bg-blue-600 inline-block">
                Seleccionar Video (Label)
            </label>
            <input type="file" id="test2" accept="video/*" style="display: none;">
        </div>

        <!-- Test 3: Botón con JavaScript -->
        <div class="mb-8">
            <h2 class="text-lg font-semibold mb-4">Test 3: Botón con JavaScript</h2>
            <button id="jsButton" class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600">
                Seleccionar Video (JS)
            </button>
            <input type="file" id="jsInput" accept="video/*" style="display: none;">
        </div>

        <!-- Test 4: Div con input transparente -->
        <div class="mb-8">
            <h2 class="text-lg font-semibold mb-4">Test 4: Input Transparente</h2>
            <div class="relative inline-block">
                <button class="bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600 pointer-events-none">
                    Seleccionar Video (Transparente)
                </button>
                <input 
                    type="file" 
                    accept="video/*" 
                    class="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                    onchange="handleFileSelect(event, 'test4')"
                >
            </div>
        </div>

        <!-- Test 5: Click area amplia -->
        <div class="mb-8">
            <h2 class="text-lg font-semibold mb-4">Test 5: Área de Click Amplia</h2>
            <div class="relative border-2 border-dashed border-gray-300 p-8 text-center">
                <p class="text-gray-600">Click aquí para seleccionar video</p>
                <input 
                    type="file" 
                    accept="video/*" 
                    class="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                    onchange="handleFileSelect(event, 'test5')"
                >
            </div>
        </div>

        <!-- Resultados -->
        <div id="results" class="mt-8 p-4 bg-gray-100 rounded">
            <h3 class="font-semibold mb-2">Resultados:</h3>
            <div id="resultsContent"></div>
        </div>
    </div>

    <script>
        // Test 3: JavaScript button
        document.getElementById('jsButton').addEventListener('click', function() {
            document.getElementById('jsInput').click();
        });

        document.getElementById('jsInput').addEventListener('change', function(e) {
            handleFileSelect(e, 'test3');
        });

        // Handle file selection
        function handleFileSelect(event, testName) {
            const file = event.target.files[0];
            if (file) {
                const results = document.getElementById('resultsContent');
                results.innerHTML += `<p><strong>${testName}:</strong> ${file.name} (${(file.size / 1024 / 1024).toFixed(2)}MB)</p>`;
            }
        }

        // Add listeners for other tests
        document.getElementById('test1').addEventListener('change', (e) => handleFileSelect(e, 'test1'));
        document.getElementById('test2').addEventListener('change', (e) => handleFileSelect(e, 'test2'));
    </script>
</body>
</html>