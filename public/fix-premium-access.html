<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fix Premium Access - NewSpports</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #0f172a;
            color: #e2e8f0;
            padding: 20px;
            max-width: 600px;
            margin: 0 auto;
        }
        .container {
            background-color: #1e293b;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
            text-align: center;
        }
        h1 {
            color: #f59e0b;
            margin-bottom: 20px;
        }
        .status {
            margin: 20px 0;
            padding: 20px;
            background-color: #0f172a;
            border-radius: 8px;
            font-family: monospace;
            font-size: 14px;
        }
        button {
            background-color: #10b981;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            margin: 10px;
            transition: background-color 0.2s;
        }
        button:hover {
            background-color: #059669;
        }
        .success {
            color: #10b981;
        }
        .error {
            color: #ef4444;
        }
        .info {
            color: #3b82f6;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Fix Premium Access</h1>
        <p>Este script vai corrigir o acesso premium para usuários Stack Auth</p>
        
        <div id="status" class="status">
            Pronto para iniciar...
        </div>
        
        <button onclick="fixAccess()">🚀 Corrigir Acesso Premium</button>
        <button onclick="testAccess()">🔍 Testar Acesso</button>
        <button onclick="goToPlayer()">🎬 Ir para o Player</button>
    </div>

    <script>
        function log(message, type = 'info') {
            const status = document.getElementById('status');
            const entry = document.createElement('div');
            entry.className = type;
            entry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            status.appendChild(entry);
            status.scrollTop = status.scrollHeight;
        }

        async function fixAccess() {
            log('🔄 Iniciando correção de acesso...', 'info');
            
            // 1. Limpar cache local
            log('🧹 Limpando cache local...', 'info');
            localStorage.removeItem('trial-storage');
            localStorage.removeItem('access-storage');
            
            // 2. Buscar informações do usuário
            log('👤 Buscando informações do usuário...', 'info');
            try {
                const meResponse = await fetch('/api/me');
                const userData = await meResponse.json();
                
                if (!userData.id) {
                    log('❌ Usuário não autenticado!', 'error');
                    log('Por favor, faça login primeiro', 'error');
                    return;
                }
                
                log(`✅ Usuário encontrado: ${userData.email}`, 'success');
                log(`📍 Stack ID: ${userData.id}`, 'info');
                
                // 3. Ativar premium se necessário
                log('🔍 Verificando status premium...', 'info');
                const checkResponse = await fetch('/api/check-access', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ userId: userData.id })
                });
                
                const accessData = await checkResponse.json();
                
                if (!accessData.has_access) {
                    log('⚠️ Premium não ativo, ativando agora...', 'info');
                    
                    const activateResponse = await fetch('/api/activate-premium-stack', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            userId: userData.id,
                            email: userData.email,
                            tier: 'premium',
                            months: 1
                        })
                    });
                    
                    const activateData = await activateResponse.json();
                    
                    if (activateData.success) {
                        log('✅ Premium ativado com sucesso!', 'success');
                        log(`📅 Expira em: ${new Date(activateData.expiresAt).toLocaleDateString('pt-BR')}`, 'info');
                    } else {
                        log(`❌ Erro ao ativar: ${activateData.error}`, 'error');
                        return;
                    }
                } else {
                    log('✅ Premium já está ativo!', 'success');
                    log(`📅 Expira em: ${new Date(accessData.expires_at).toLocaleDateString('pt-BR')}`, 'info');
                }
                
                // 4. Forçar atualização do access store
                log('🔄 Atualizando access store...', 'info');
                
                // Criar um novo access store com o estado correto
                const newAccessState = {
                    state: {
                        hasAccess: true,
                        accessType: 'assinatura',
                        expiresAt: accessData.expires_at || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
                        lastCheck: new Date().toISOString(),
                        isLoading: false
                    },
                    version: 0
                };
                
                localStorage.setItem('access-storage', JSON.stringify(newAccessState));
                log('✅ Access store atualizado!', 'success');
                
                // 5. Limpar trial expirado
                log('🧹 Resetando trial...', 'info');
                const trialState = {
                    state: {
                        hasStarted: false,
                        startTime: null,
                        isExpired: false,
                        canReset: true
                    },
                    version: 0
                };
                localStorage.setItem('trial-storage', JSON.stringify(trialState));
                log('✅ Trial resetado!', 'success');
                
                log('🎉 CORREÇÃO CONCLUÍDA!', 'success');
                log('Recarregue a página do player para aplicar as mudanças', 'info');
                
            } catch (error) {
                log(`❌ Erro: ${error.message}`, 'error');
            }
        }

        async function testAccess() {
            log('🔍 Testando acesso...', 'info');
            
            // Verificar localStorage
            const accessStorage = localStorage.getItem('access-storage');
            const trialStorage = localStorage.getItem('trial-storage');
            
            if (accessStorage) {
                const access = JSON.parse(accessStorage);
                log(`📊 Access Store: hasAccess=${access.state.hasAccess}, type=${access.state.accessType}`, 'info');
            }
            
            if (trialStorage) {
                const trial = JSON.parse(trialStorage);
                log(`⏱️ Trial Store: isExpired=${trial.state.isExpired}`, 'info');
            }
            
            // Verificar API
            try {
                const meResponse = await fetch('/api/me');
                const userData = await meResponse.json();
                
                if (userData.id) {
                    const checkResponse = await fetch('/api/check-access', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ userId: userData.id })
                    });
                    
                    const accessData = await checkResponse.json();
                    log(`🔐 API Check: hasAccess=${accessData.has_access}`, accessData.has_access ? 'success' : 'error');
                }
            } catch (error) {
                log(`❌ Erro ao testar: ${error.message}`, 'error');
            }
        }

        function goToPlayer() {
            window.location.href = '/watch/espn';
        }

        // Auto-executar ao carregar
        window.addEventListener('load', () => {
            log('🎬 NewSpports - Fix Premium Access', 'info');
            log('Clique em "Corrigir Acesso Premium" para começar', 'info');
        });
    </script>
</body>
</html>