<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Simple - NewSpports</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto p-6 max-w-4xl">
        <h1 class="text-3xl font-bold mb-8">Panel Administrativo Simplificado</h1>
        
        <!-- Check Auth -->
        <div id="authStatus" class="mb-4 p-4 bg-yellow-100 rounded">
            Verificando autenticación...
        </div>

        <!-- Login Form -->
        <div id="loginForm" class="hidden mb-8 bg-white p-6 rounded-lg shadow">
            <h2 class="text-xl font-semibold mb-4">Iniciar <PERSON></h2>
            <input type="email" id="email" placeholder="Email" value="<EMAIL>" class="w-full p-2 border rounded mb-4">
            <input type="password" id="password" placeholder="Contraseña" value="62845_Madhouse" class="w-full p-2 border rounded mb-4">
            <button onclick="login()" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                Entrar
            </button>
        </div>

        <!-- Admin Panel -->
        <div id="adminPanel" class="hidden">
            <!-- Mobile Config -->
            <div class="bg-white p-6 rounded-lg shadow mb-6">
                <h2 class="text-xl font-semibold mb-4">Configuración de App Móvil</h2>
                
                <div class="mb-4">
                    <label class="block mb-2">URL Android:</label>
                    <input type="text" id="androidUrl" class="w-full p-2 border rounded">
                </div>
                
                <div class="mb-4">
                    <label class="block mb-2">URL iOS:</label>
                    <input type="text" id="iosUrl" class="w-full p-2 border rounded">
                </div>
                
                <div class="mb-4">
                    <label class="block mb-2">URL QR Code:</label>
                    <input type="text" id="qrUrl" class="w-full p-2 border rounded">
                </div>
                
                <button onclick="saveConfig()" class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600">
                    Guardar Configuración
                </button>
            </div>

            <!-- Video Upload -->
            <div class="bg-white p-6 rounded-lg shadow">
                <h2 class="text-xl font-semibold mb-4">Video Tutorial</h2>
                
                <div id="currentVideo" class="mb-4 hidden">
                    <p class="text-green-600 mb-2">Video actual:</p>
                    <a id="videoLink" href="#" target="_blank" class="text-blue-500 hover:underline"></a>
                    <button onclick="deleteVideo()" class="ml-4 text-red-500 hover:underline">Eliminar</button>
                </div>
                
                <div class="mb-4">
                    <input type="file" id="videoFile" accept="video/*" class="mb-4">
                    <button onclick="uploadVideo()" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                        Subir Video
                    </button>
                </div>
                
                <div id="uploadStatus" class="hidden mt-4 p-4 rounded"></div>
            </div>

            <button onclick="logout()" class="mt-6 bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600">
                Cerrar Sesión
            </button>
        </div>
    </div>

    <script>
        // Check auth status on load
        window.onload = async function() {
            const response = await fetch('/api/admin-auth');
            const data = await response.json();
            
            if (data.isAuthenticated) {
                showAdminPanel();
            } else {
                document.getElementById('authStatus').textContent = 'No autenticado. Por favor inicia sesión.';
                document.getElementById('authStatus').className = 'mb-4 p-4 bg-red-100 rounded';
                document.getElementById('loginForm').classList.remove('hidden');
            }
        }

        async function login() {
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            const response = await fetch('/api/admin-auth', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ email, password })
            });
            
            if (response.ok) {
                showAdminPanel();
            } else {
                alert('Credenciales incorrectas');
            }
        }

        async function showAdminPanel() {
            document.getElementById('authStatus').textContent = 'Autenticado correctamente';
            document.getElementById('authStatus').className = 'mb-4 p-4 bg-green-100 rounded';
            document.getElementById('loginForm').classList.add('hidden');
            document.getElementById('adminPanel').classList.remove('hidden');
            
            // Load current config
            const response = await fetch('/api/admin/mobile-config');
            const config = await response.json();
            
            document.getElementById('androidUrl').value = config.android_url || '';
            document.getElementById('iosUrl').value = config.ios_url || '';
            document.getElementById('qrUrl').value = config.qr_code_url || '';
            
            if (config.tutorial_video_file) {
                document.getElementById('currentVideo').classList.remove('hidden');
                document.getElementById('videoLink').href = config.tutorial_video_file;
                document.getElementById('videoLink').textContent = config.tutorial_video_file;
            }
        }

        async function saveConfig() {
            const config = {
                android_url: document.getElementById('androidUrl').value,
                ios_url: document.getElementById('iosUrl').value,
                qr_code_url: document.getElementById('qrUrl').value
            };
            
            const response = await fetch('/api/admin/mobile-config', {
                method: 'PUT',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(config)
            });
            
            if (response.ok) {
                alert('Configuración guardada correctamente');
            } else {
                alert('Error al guardar configuración');
            }
        }

        async function uploadVideo() {
            const fileInput = document.getElementById('videoFile');
            const file = fileInput.files[0];
            
            if (!file) {
                alert('Por favor selecciona un archivo');
                return;
            }
            
            if (file.size > 104857600) {
                alert('El archivo es muy grande. Máximo 100MB.');
                return;
            }
            
            const formData = new FormData();
            formData.append('file', file);
            
            const statusDiv = document.getElementById('uploadStatus');
            statusDiv.classList.remove('hidden');
            statusDiv.className = 'mt-4 p-4 rounded bg-blue-100';
            statusDiv.textContent = 'Subiendo video...';
            
            try {
                const response = await fetch('/api/admin/upload-video', {
                    method: 'POST',
                    body: formData
                });
                
                if (response.ok) {
                    const data = await response.json();
                    statusDiv.className = 'mt-4 p-4 rounded bg-green-100';
                    statusDiv.textContent = 'Video subido correctamente: ' + data.url;
                    
                    // Update current video display
                    document.getElementById('currentVideo').classList.remove('hidden');
                    document.getElementById('videoLink').href = data.url;
                    document.getElementById('videoLink').textContent = data.url;
                    
                    // Clear file input
                    fileInput.value = '';
                } else {
                    const error = await response.json();
                    statusDiv.className = 'mt-4 p-4 rounded bg-red-100';
                    statusDiv.textContent = 'Error: ' + (error.error || 'Error al subir el video');
                }
            } catch (error) {
                statusDiv.className = 'mt-4 p-4 rounded bg-red-100';
                statusDiv.textContent = 'Error de conexión';
            }
        }

        async function deleteVideo() {
            if (!confirm('¿Estás seguro de eliminar el video?')) return;
            
            const response = await fetch('/api/admin/upload-video', {
                method: 'DELETE'
            });
            
            if (response.ok) {
                document.getElementById('currentVideo').classList.add('hidden');
                alert('Video eliminado');
            } else {
                alert('Error al eliminar el video');
            }
        }

        async function logout() {
            await fetch('/api/admin-auth', { method: 'DELETE' });
            window.location.reload();
        }
    </script>
</body>
</html>