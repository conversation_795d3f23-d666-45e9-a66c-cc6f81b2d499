<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste do Sistema de Trial</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #0a0a0a;
            color: #fff;
        }
        .container {
            background: #1a1a1a;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
        }
        h1 {
            color: #fff;
            margin-bottom: 10px;
        }
        .info {
            background: #2a2a2a;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #3b82f6;
        }
        .status {
            background: #2a2a2a;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .success {
            border-left: 4px solid #10b981;
        }
        .error {
            border-left: 4px solid #ef4444;
        }
        .warning {
            border-left: 4px solid #f59e0b;
        }
        button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            font-size: 16px;
            cursor: pointer;
            margin: 5px;
            transition: background 0.2s;
        }
        button:hover {
            background: #2563eb;
        }
        button:disabled {
            background: #4b5563;
            cursor: not-allowed;
        }
        .reset-btn {
            background: #ef4444;
        }
        .reset-btn:hover {
            background: #dc2626;
        }
        .timer {
            font-size: 48px;
            font-weight: bold;
            text-align: center;
            margin: 20px 0;
            font-family: 'Courier New', monospace;
        }
        .expired {
            color: #ef4444;
        }
        .active {
            color: #10b981;
        }
        .instructions {
            background: #1e293b;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .instructions h3 {
            margin-top: 0;
            color: #f59e0b;
        }
        .instructions ol {
            margin: 10px 0;
            padding-left: 20px;
        }
        .instructions li {
            margin: 8px 0;
            line-height: 1.6;
        }
        .log {
            background: #111;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 20px;
        }
        .log-entry {
            margin: 2px 0;
            padding: 2px 0;
        }
        .browser-info {
            background: #374151;
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
            font-size: 14px;
        }
        pre {
            background: #000;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Teste do Sistema de Trial</h1>
        <p>Esta página testa se o trial está funcionando corretamente por usuário (não por navegador)</p>

        <div class="browser-info">
            <strong>Navegador atual:</strong> <span id="browserInfo"></span><br>
            <strong>Timestamp:</strong> <span id="timestamp"></span>
        </div>

        <div class="instructions">
            <h3>📋 Como testar:</h3>
            <ol>
                <li>Faça login com sua conta</li>
                <li>Clique em "Verificar Trial" para ver o status atual</li>
                <li>Acesse um canal no site para iniciar o trial (se ainda não iniciou)</li>
                <li>Abra esta mesma página em outro navegador (Chrome, Firefox, Safari, etc)</li>
                <li>Faça login com a MESMA conta</li>
                <li>Verifique se o tempo continua de onde parou (não deve resetar para 5:00)</li>
                <li>Quando expirar em um navegador, deve estar expirado em todos</li>
            </ol>
        </div>

        <div class="status" id="statusDiv">
            <p>Clique em "Verificar Trial" para começar o teste</p>
        </div>

        <div class="timer" id="timerDiv">
            --:--
        </div>

        <div style="text-align: center; margin: 20px 0;">
            <button onclick="checkTrial()">🔍 Verificar Trial</button>
            <button onclick="goToChannel()">📺 Ir para Canal</button>
            <button onclick="resetTrial()" class="reset-btn">🗑️ Resetar Trial (Teste)</button>
            <button onclick="clearLog()">🧹 Limpar Log</button>
        </div>

        <div class="info">
            <h3>ℹ️ Informações importantes:</h3>
            <ul>
                <li>O trial agora é vinculado ao <strong>usuário</strong>, não ao navegador</li>
                <li>Usa o banco de dados Supabase para persistir o estado</li>
                <li>Tempo de trial: 5 minutos (300 segundos)</li>
                <li>Após 24 horas, um novo trial pode ser iniciado</li>
            </ul>
        </div>

        <div class="log" id="logDiv">
            <div class="log-entry">Log iniciado...</div>
        </div>
    </div>

    <script>
        // Detectar navegador
        function getBrowserInfo() {
            const ua = navigator.userAgent;
            let browser = "Unknown";
            
            if (ua.includes("Firefox")) browser = "Firefox";
            else if (ua.includes("Chrome")) browser = "Chrome";
            else if (ua.includes("Safari") && !ua.includes("Chrome")) browser = "Safari";
            else if (ua.includes("Edge")) browser = "Edge";
            
            return `${browser} - ${navigator.platform}`;
        }

        // Atualizar info do navegador
        document.getElementById('browserInfo').textContent = getBrowserInfo();
        document.getElementById('timestamp').textContent = new Date().toLocaleString('pt-BR');

        // Adicionar entrada no log
        function addLog(message, type = 'info') {
            const logDiv = document.getElementById('logDiv');
            const entry = document.createElement('div');
            entry.className = 'log-entry';
            const time = new Date().toLocaleTimeString('pt-BR');
            entry.innerHTML = `<span style="color: #6b7280">[${time}]</span> ${message}`;
            logDiv.appendChild(entry);
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        // Limpar log
        function clearLog() {
            document.getElementById('logDiv').innerHTML = '<div class="log-entry">Log limpo...</div>';
        }

        // Verificar trial
        async function checkTrial() {
            addLog('🔍 Verificando status do trial...');
            const statusDiv = document.getElementById('statusDiv');
            const timerDiv = document.getElementById('timerDiv');
            
            try {
                const response = await fetch('/api/debug/test-trial');
                const data = await response.json();
                
                if (!response.ok) {
                    if (response.status === 401) {
                        statusDiv.className = 'status error';
                        statusDiv.innerHTML = `
                            <h3>❌ Não autenticado</h3>
                            <p>Por favor, faça login para testar o sistema de trial</p>
                            <button onclick="window.location.href='/login'">Fazer Login</button>
                        `;
                        timerDiv.textContent = '--:--';
                        addLog('❌ Usuário não autenticado', 'error');
                    } else {
                        throw new Error(data.error || 'Erro ao verificar trial');
                    }
                    return;
                }
                
                // Atualizar status
                const trial = data.current_trial;
                const isExpired = trial.is_expired;
                
                statusDiv.className = `status ${isExpired ? 'error' : 'success'}`;
                statusDiv.innerHTML = `
                    <h3>${isExpired ? '⏰ Trial Expirado' : '✅ Trial Ativo'}</h3>
                    <p><strong>Usuário:</strong> ${data.user.email}</p>
                    <p><strong>ID do Usuário:</strong> ${data.user.id}</p>
                    <p><strong>Trial ID:</strong> ${trial.trial_id}</p>
                    <p><strong>Tempo Restante:</strong> ${trial.formatted_time} (${trial.time_remaining}s)</p>
                    <p><strong>Status:</strong> ${isExpired ? 'Expirado' : 'Ativo'}</p>
                    <p><strong>Total de Trials:</strong> ${data.all_user_trials.length}</p>
                `;
                
                // Atualizar timer
                timerDiv.className = `timer ${isExpired ? 'expired' : 'active'}`;
                timerDiv.textContent = trial.formatted_time;
                
                // Log detalhado
                addLog(`✅ Trial verificado: ${trial.formatted_time} restantes`, 'success');
                addLog(`User ID: ${data.user.id}`, 'info');
                addLog(`Trial ID: ${trial.trial_id}`, 'info');
                addLog(`Expirado: ${isExpired ? 'Sim' : 'Não'}`, isExpired ? 'error' : 'success');
                
                // Mostrar histórico se houver
                if (data.all_user_trials.length > 0) {
                    addLog(`📊 Histórico de trials: ${data.all_user_trials.length} sessões`, 'info');
                }
                
            } catch (error) {
                statusDiv.className = 'status error';
                statusDiv.innerHTML = `
                    <h3>❌ Erro</h3>
                    <p>${error.message}</p>
                `;
                timerDiv.textContent = '--:--';
                addLog(`❌ Erro: ${error.message}`, 'error');
            }
        }

        // Ir para canal
        function goToChannel() {
            addLog('📺 Redirecionando para o canal...', 'info');
            window.open('/browse', '_blank');
        }

        // Resetar trial (apenas para teste)
        async function resetTrial() {
            if (!confirm('Tem certeza que deseja resetar todos os trials deste usuário? (Apenas para teste)')) {
                return;
            }
            
            addLog('🗑️ Resetando trials...', 'warning');
            
            try {
                const response = await fetch('/api/debug/test-trial', {
                    method: 'POST'
                });
                const data = await response.json();
                
                if (!response.ok) {
                    throw new Error(data.error || 'Erro ao resetar trial');
                }
                
                addLog('✅ Trials resetados com sucesso!', 'success');
                setTimeout(() => checkTrial(), 1000);
                
            } catch (error) {
                addLog(`❌ Erro ao resetar: ${error.message}`, 'error');
            }
        }

        // Auto-refresh a cada 5 segundos
        let autoRefreshInterval;
        function toggleAutoRefresh() {
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
                autoRefreshInterval = null;
                addLog('⏸️ Auto-refresh desativado', 'info');
            } else {
                autoRefreshInterval = setInterval(() => {
                    addLog('🔄 Auto-refresh...', 'info');
                    checkTrial();
                }, 5000);
                addLog('▶️ Auto-refresh ativado (5s)', 'info');
            }
        }

        // Adicionar botão de auto-refresh
        const buttonsDiv = document.querySelector('div[style*="text-align: center"]');
        const autoRefreshBtn = document.createElement('button');
        autoRefreshBtn.textContent = '🔄 Auto-Refresh';
        autoRefreshBtn.onclick = toggleAutoRefresh;
        buttonsDiv.appendChild(autoRefreshBtn);

        // Verificar ao carregar
        window.addEventListener('load', () => {
            addLog('🚀 Página carregada', 'info');
            addLog(`🌐 Navegador: ${getBrowserInfo()}`, 'info');
            checkTrial();
        });
    </script>
</body>
</html>