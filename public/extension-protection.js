// Proteção contra erros de extensões de carteira cripto
// Este script deve ser carregado ANTES de qualquer outro script

(function() {
  'use strict';
  
  console.log('🛡️ Iniciando proteção contra erros de extensões...');
  
  // Cria um wrapper seguro para window.ethereum se necessário
  if (typeof window !== 'undefined') {
    // Salva referência original se existir
    const originalEthereum = window.ethereum;
    
    // Cria um Proxy protetor
    if (originalEthereum) {
      try {
        window.ethereum = new Proxy(originalEthereum, {
          get(target, prop) {
            try {
              const value = target[prop];
              // Se for uma função, retorna uma versão segura
              if (typeof value === 'function') {
                return function(...args) {
                  try {
                    return value.apply(target, args);
                  } catch (error) {
                    console.warn('⚠️ Erro em ethereum.' + prop + ':', error);
                    return undefined;
                  }
                };
              }
              return value;
            } catch (error) {
              console.warn('⚠️ Erro ao acessar ethereum.' + prop + ':', error);
              return undefined;
            }
          }
        });
      } catch (e) {
        console.warn('⚠️ Não foi possível criar proxy para ethereum:', e);
      }
    }
    
    // Intercepta erros globalmente
    const originalError = window.onerror;
    window.onerror = function(message, source, lineno, colno, error) {
      // Ignora erros de extensões
      if (source && source.includes('extension://')) {
        console.warn('🔌 Erro de extensão ignorado:', {
          message: message,
          source: source
        });
        return true; // Previne propagação
      }
      
      // Ignora erros específicos de Trust Wallet
      if (message && message.includes("Cannot read properties of null (reading 'type')") && 
          source && source.includes('inpage.js')) {
        console.warn('🔌 Erro específico de Trust Wallet ignorado');
        return true; // Previne propagação
      }
      
      // Chama handler original se existir
      if (originalError) {
        return originalError.call(window, message, source, lineno, colno, error);
      }
      
      return false;
    };
    
    // Intercepta promises rejeitadas
    window.addEventListener('unhandledrejection', function(event) {
      const error = event.reason;
      
      // Ignora erros de extensões
      if (error && error.stack && error.stack.includes('extension://')) {
        console.warn('🔌 Promise rejeitada por extensão ignorada:', error);
        event.preventDefault();
        return;
      }
      
      // Ignora erros específicos
      if (error && error.message && 
          error.message.includes("Cannot read properties of null (reading 'type')")) {
        console.warn('🔌 Erro específico de type null ignorado em promise');
        event.preventDefault();
        return;
      }
    });
    
    console.log('✅ Proteção contra erros de extensões ativada');
  }
})();