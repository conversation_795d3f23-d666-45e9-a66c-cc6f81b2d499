<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste Premium - NewSpports</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #0f172a;
            color: #e2e8f0;
            padding: 20px;
            max-width: 800px;
            margin: 0 auto;
        }
        .container {
            background-color: #1e293b;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
        }
        h1 {
            color: #f59e0b;
            margin-bottom: 20px;
        }
        button {
            background-color: #ef4444;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            margin: 10px 10px 10px 0;
            transition: background-color 0.2s;
        }
        button:hover {
            background-color: #dc2626;
        }
        button:disabled {
            background-color: #6b7280;
            cursor: not-allowed;
        }
        .success {
            background-color: #10b981;
        }
        .success:hover {
            background-color: #059669;
        }
        #output {
            background-color: #0f172a;
            border: 1px solid #334155;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 14px;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .log-entry {
            margin: 5px 0;
            padding: 5px;
            border-radius: 4px;
        }
        .log-success {
            color: #10b981;
        }
        .log-error {
            color: #ef4444;
        }
        .log-info {
            color: #3b82f6;
        }
        .log-warning {
            color: #f59e0b;
        }
        .section {
            margin: 20px 0;
            padding: 20px;
            background-color: #1e293b;
            border-radius: 8px;
            border: 1px solid #334155;
        }
        .command-box {
            background-color: #0f172a;
            border: 1px solid #334155;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Teste de Acesso Premium - NewSpports</h1>
        
        <div class="section">
            <h2>📊 Status Atual</h2>
            <div id="currentStatus">Carregando...</div>
        </div>

        <div class="section">
            <h2>🚀 Ações Disponíveis</h2>
            
            <button onclick="checkPremiumStatus()">
                🔍 Verificar Status Premium
            </button>
            
            <button onclick="clearTrialAndCheck()">
                🧹 Limpar Trial e Verificar
            </button>
            
            <button onclick="activatePremium()" class="success">
                ✅ Ativar Premium Manual
            </button>
            
            <button onclick="forceUpdateAccess()">
                🔄 Forçar Atualização de Acesso
            </button>
            
            <button onclick="clearOutput()">
                🗑️ Limpar Console
            </button>
        </div>

        <div class="section">
            <h2>📝 Console de Saída</h2>
            <div id="output"></div>
        </div>

        <div class="section">
            <h2>🎯 Comando Manual (se necessário)</h2>
            <p>Se os botões não funcionarem, copie e cole este comando no console do navegador (F12):</p>
            <div class="command-box">
fetch('/api/stripe/activate-subscription', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    email: userEmail || '<EMAIL>',
    tier: 'premium',
    months: 1
  })
}).then(r => r.json()).then(console.log)
            </div>
        </div>
    </div>

    <script>
        // Função para adicionar log ao output
        function addLog(message, type = 'info') {
            const output = document.getElementById('output');
            const entry = document.createElement('div');
            entry.className = `log-entry log-${type}`;
            const timestamp = new Date().toLocaleTimeString('pt-BR');
            entry.textContent = `[${timestamp}] ${message}`;
            output.appendChild(entry);
            output.scrollTop = output.scrollHeight;
        }

        // Limpar output
        function clearOutput() {
            document.getElementById('output').innerHTML = '';
            addLog('Console limpo', 'info');
        }

        // Variável global para armazenar email do usuário
        let userEmail = '<EMAIL>';
        
        // Verificar status ao carregar
        async function checkCurrentStatus() {
            try {
                const statusDiv = document.getElementById('currentStatus');
                
                // Verificar localStorage
                const trialStore = localStorage.getItem('trial-storage');
                const accessStore = localStorage.getItem('access-storage');
                
                let html = '<ul>';
                
                if (trialStore) {
                    const trial = JSON.parse(trialStore);
                    html += `<li>🕐 Trial: ${trial.state.isExpired ? '❌ Expirado' : '✅ Ativo'}</li>`;
                }
                
                if (accessStore) {
                    const access = JSON.parse(accessStore);
                    html += `<li>🔐 Acesso: ${access.state.hasAccess ? '✅ ' + access.state.accessType : '❌ Sem acesso'}</li>`;
                }
                
                // Verificar API
                const response = await fetch('/api/stripe/subscription-status');
                if (response.ok) {
                    const data = await response.json();
                    html += `<li>💳 Assinatura: ${data.hasActiveSubscription ? '✅ Ativa' : '❌ Inativa'}</li>`;
                    if (data.userId) {
                        html += `<li>👤 User ID: ${data.userId}</li>`;
                    }
                }
                
                // Buscar email real do usuário
                const emailResponse = await fetch('/api/get-my-email');
                if (emailResponse.ok) {
                    const emailData = await emailResponse.json();
                    if (emailData.email) {
                        userEmail = emailData.email;
                        html += `<li>📧 Email: ${emailData.email}</li>`;
                    }
                }
                
                html += '</ul>';
                statusDiv.innerHTML = html;
                
            } catch (error) {
                document.getElementById('currentStatus').innerHTML = '❌ Erro ao verificar status';
            }
        }

        // Verificar status premium
        async function checkPremiumStatus() {
            addLog('🔍 Verificando status premium...', 'info');
            
            try {
                // Verificar status da assinatura
                const statusResponse = await fetch('/api/stripe/subscription-status');
                const statusData = await statusResponse.json();
                
                addLog('📊 Status da assinatura recebido', 'info');
                
                if (statusData.hasActiveSubscription) {
                    addLog('✅ ASSINATURA PREMIUM ATIVA!', 'success');
                    addLog(`  - Tier: ${statusData.subscriptionTier}`, 'success');
                    addLog(`  - Status: ${statusData.subscriptionStatus}`, 'success');
                    addLog(`  - Expira em: ${new Date(statusData.subscriptionEnd).toLocaleString('pt-BR')}`, 'success');
                    addLog(`  - User ID: ${statusData.userId}`, 'info');
                } else {
                    addLog('❌ SEM ASSINATURA PREMIUM ATIVA', 'error');
                }
                
                // Verificar debug do premium
                addLog('🔍 Executando debug detalhado...', 'info');
                const debugResponse = await fetch(`/api/debug-premium?email=${encodeURIComponent(userEmail)}`);
                const debugData = await debugResponse.json();
                
                if (debugData.access_check) {
                    addLog('🔐 Resultado do check_user_access:', 'info');
                    addLog(`  - has_access: ${debugData.access_check.has_access}`, debugData.access_check.has_access ? 'success' : 'error');
                    addLog(`  - access_type: ${debugData.access_check.access_type || 'null'}`, 'info');
                    addLog(`  - expires_at: ${debugData.access_check.expires_at || 'null'}`, 'info');
                }
                
                if (debugData.recommendations) {
                    addLog('📝 Recomendações:', 'warning');
                    debugData.recommendations.forEach(rec => addLog(`  - ${rec}`, 'warning'));
                }
                
                checkCurrentStatus();
                
            } catch (error) {
                addLog(`❌ Erro ao verificar status: ${error.message}`, 'error');
            }
        }

        // Limpar trial e verificar
        async function clearTrialAndCheck() {
            addLog('🧹 Limpando trial do localStorage...', 'info');
            
            localStorage.removeItem('trial-storage');
            localStorage.removeItem('trial-store');
            
            addLog('✅ Trial removido do localStorage', 'success');
            
            // Verificar access store
            const accessStore = localStorage.getItem('access-storage');
            if (accessStore) {
                const parsed = JSON.parse(accessStore);
                addLog('🔐 Access Store encontrado:', 'info');
                addLog(`  - hasAccess: ${parsed.state.hasAccess}`, parsed.state.hasAccess ? 'success' : 'warning');
                addLog(`  - accessType: ${parsed.state.accessType || 'null'}`, 'info');
            } else {
                addLog('❌ Access Store não encontrado', 'warning');
            }
            
            // Verificar status
            await checkPremiumStatus();
        }

        // Variável para armazenar userId
        let currentUserId = null;
        
        // Ativar premium manualmente
        async function activatePremium() {
            addLog('🚀 Ativando premium manualmente...', 'info');
            
            // Primeiro, obter o userId do Stack Auth
            if (!currentUserId) {
                const statusResponse = await fetch('/api/stripe/subscription-status');
                if (statusResponse.ok) {
                    const statusData = await statusResponse.json();
                    currentUserId = statusData.userId;
                }
            }
            
            try {
                // Usar o novo endpoint que funciona com Stack Auth
                const response = await fetch('/api/activate-premium-stack', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        userId: currentUserId,
                        email: userEmail,
                        tier: 'premium',
                        months: 1
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    addLog('✅ PREMIUM ATIVADO COM SUCESSO!', 'success');
                    addLog(`  - Message: ${data.message}`, 'success');
                    if (data.profile) {
                        addLog(`  - Tier: ${data.profile.subscription_tier}`, 'success');
                        addLog(`  - Status: ${data.profile.subscription_status}`, 'success');
                        addLog(`  - Expira em: ${new Date(data.profile.subscription_current_period_end).toLocaleString('pt-BR')}`, 'success');
                    }
                    
                    // Aguardar um pouco e forçar atualização
                    setTimeout(() => {
                        forceUpdateAccess();
                    }, 1000);
                    
                } else {
                    addLog(`❌ Erro ao ativar: ${data.message || data.error}`, 'error');
                    if (data.details) {
                        addLog(`  - Detalhes: ${data.details}`, 'error');
                    }
                }
                
            } catch (error) {
                addLog(`❌ Erro na requisição: ${error.message}`, 'error');
            }
        }

        // Forçar atualização do access store
        async function forceUpdateAccess() {
            addLog('🔄 Forçando atualização do access store...', 'info');
            
            try {
                // Tentar primeiro com /api/me (Stack Auth)
                let userId = null;
                let authType = 'unknown';
                
                try {
                    const meResponse = await fetch('/api/me');
                    if (meResponse.ok) {
                        const meData = await meResponse.json();
                        if (meData.id) {
                            userId = meData.id;
                            authType = 'stack';
                            addLog(`✅ Stack Auth ID encontrado: ${userId}`, 'success');
                        }
                    }
                } catch (e) {
                    console.log('Erro ao buscar Stack Auth:', e);
                }
                
                // Se não encontrou via Stack, tentar Supabase
                if (!userId) {
                    const statusResponse = await fetch('/api/stripe/subscription-status');
                    const statusData = await statusResponse.json();
                    
                    if (statusData.userId) {
                        userId = statusData.userId;
                        authType = statusData.authType || 'supabase';
                        addLog(`✅ User ID encontrado via status: ${userId} (${authType})`, 'success');
                    }
                }
                
                if (!userId) {
                    addLog('❌ User ID não encontrado em nenhuma fonte', 'error');
                    addLog('💡 Tente fazer login novamente', 'warning');
                    return;
                }
                
                addLog(`👤 User ID: ${userId}`, 'info');
                addLog(`🔐 Auth Type: ${authType}`, 'info');
                
                // Chamar check-access
                const checkResponse = await fetch('/api/check-access', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ userId: userId })
                });
                
                if (checkResponse.ok) {
                    const accessData = await checkResponse.json();
                    addLog('✅ Access check response recebido', 'success');
                    addLog(`  - has_access: ${accessData.has_access}`, accessData.has_access ? 'success' : 'error');
                    addLog(`  - access_type: ${accessData.access_type || 'null'}`, 'info');
                    
                    // Atualizar localStorage se tem acesso
                    if (accessData.has_access) {
                        const newAccessState = {
                            state: {
                                hasAccess: true,
                                accessType: accessData.access_type,
                                expiresAt: accessData.expires_at,
                                lastCheck: new Date().toISOString(),
                                isLoading: false
                            },
                            version: 0
                        };
                        localStorage.setItem('access-storage', JSON.stringify(newAccessState));
                        addLog('✅ Access store atualizado no localStorage', 'success');
                        addLog('🎉 Recarregue a página (F5) e tente assistir um canal!', 'success');
                    }
                } else {
                    addLog('❌ Erro ao verificar acesso', 'error');
                }
                
                checkCurrentStatus();
                
            } catch (error) {
                addLog(`❌ Erro: ${error.message}`, 'error');
            }
        }

        // Verificar status ao carregar a página
        window.onload = () => {
            checkCurrentStatus();
            addLog('🎬 NewSpports - Teste de Premium carregado', 'info');
            addLog('👆 Use os botões acima para testar seu acesso premium', 'info');
        };
    </script>
</body>
</html>