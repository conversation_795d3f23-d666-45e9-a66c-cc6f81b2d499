// Script de diagnóstico para problemas de autenticação
// Execute no console do navegador: copy(await fetch('/debug-auth.js').then(r=>r.text())); eval(paste)

(function() {
  console.clear();
  console.log('%c🔍 DIAGNÓSTICO DE AUTENTICAÇÃO STACK AUTH', 'font-size: 20px; color: #00ff00; font-weight: bold');
  console.log('=====================================\n');
  
  // 1. Verificar extensões
  console.group('📦 1. Extensões do Navegador');
  const extensions = {
    metamask: window.ethereum,
    trustwallet: window.trustwallet,
    phantom: window.phantom || window.solana,
    coinbase: window.coinbaseWalletExtension,
    binance: window.BinanceChain,
    brave: window.brave?.wallet
  };
  
  Object.entries(extensions).forEach(([name, value]) => {
    if (value) {
      console.log(`✅ ${name}: detectado`, value);
    }
  });
  console.groupEnd();
  
  // 2. Verificar Stack Auth
  console.group('🔐 2. Stack Auth');
  console.log('Window.__STACK_AUTH__:', window.__STACK_AUTH__);
  console.log('Cookies:', document.cookie);
  
  // Verifica localStorage
  const stackKeys = Object.keys(localStorage).filter(key => 
    key.includes('stack') || key.includes('auth')
  );
  console.log('LocalStorage (Stack):', stackKeys.map(key => ({
    key,
    value: localStorage.getItem(key)
  })));
  console.groupEnd();
  
  // 3. Verificar erros recentes
  console.group('❌ 3. Erros Recentes');
  
  // Captura novos erros
  const errors = [];
  const originalError = window.onerror;
  window.onerror = function(msg, source, line, col, error) {
    errors.push({
      type: 'error',
      message: msg,
      source,
      line,
      col,
      error,
      timestamp: new Date().toISOString()
    });
    
    console.error('Erro capturado:', {
      message: msg,
      source,
      error
    });
    
    if (originalError) {
      return originalError(msg, source, line, col, error);
    }
    return false;
  };
  
  window.addEventListener('unhandledrejection', function(event) {
    errors.push({
      type: 'unhandledrejection',
      reason: event.reason,
      promise: event.promise,
      timestamp: new Date().toISOString()
    });
    
    console.error('Promise rejeitada:', event.reason);
  });
  
  console.log('Monitorando erros... (erros aparecerão aqui)');
  console.groupEnd();
  
  // 4. Testar Stack Auth
  console.group('🧪 4. Teste de Stack Auth');
  
  // Tenta acessar o Stack Auth
  try {
    // Verifica se há algum objeto Stack no window
    const stackObjects = Object.keys(window).filter(key => 
      key.toLowerCase().includes('stack')
    );
    console.log('Objetos Stack no window:', stackObjects);
    
    // Verifica elementos do DOM relacionados ao Stack
    const stackElements = document.querySelectorAll('[data-stack], [class*="stack"]');
    console.log('Elementos Stack no DOM:', stackElements.length);
    
  } catch (e) {
    console.error('Erro ao testar Stack Auth:', e);
  }
  console.groupEnd();
  
  // 5. Análise do erro específico
  console.group('🎯 5. Análise do Erro "Cannot read properties of null"');
  
  // Procura por objetos que possam ter propriedade 'type'
  const checkObject = (obj, path = 'window') => {
    if (!obj || typeof obj !== 'object') return;
    
    try {
      Object.keys(obj).forEach(key => {
        try {
          if (key === 'type' && obj[key] === null) {
            console.warn(`Encontrado: ${path}.${key} = null`);
          }
        } catch (e) {
          // Ignora erros de acesso
        }
      });
    } catch (e) {
      // Ignora erros
    }
  };
  
  // Verifica objetos suspeitos
  ['ethereum', 'web3', 'trustwallet'].forEach(name => {
    if (window[name]) {
      checkObject(window[name], `window.${name}`);
    }
  });
  
  console.groupEnd();
  
  // 6. Comandos úteis
  console.group('💡 6. Comandos Úteis');
  console.log('window.getExtensionErrors() - Ver erros de extensões');
  console.log('localStorage.clear() - Limpar localStorage');
  console.log('document.cookie = "" - Limpar cookies');
  console.groupEnd();
  
  console.log('\n=====================================');
  console.log('📊 Diagnóstico concluído. Monitore o console para novos erros.');
  
  // Retorna função para parar monitoramento
  window.stopAuthDebug = () => {
    window.onerror = originalError;
    console.log('🛑 Monitoramento parado');
  };
  
  console.log('Use window.stopAuthDebug() para parar o monitoramento');
})();