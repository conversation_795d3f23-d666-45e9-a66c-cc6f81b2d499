<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste Premium Stack Auth - NewSpports</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #0f172a;
            color: #e2e8f0;
            padding: 20px;
            max-width: 800px;
            margin: 0 auto;
        }
        .container {
            background-color: #1e293b;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
        }
        h1 {
            color: #f59e0b;
            margin-bottom: 20px;
        }
        button {
            background-color: #ef4444;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            margin: 10px 10px 10px 0;
            transition: background-color 0.2s;
        }
        button:hover {
            background-color: #dc2626;
        }
        button:disabled {
            background-color: #6b7280;
            cursor: not-allowed;
        }
        .success {
            background-color: #10b981;
        }
        .success:hover {
            background-color: #059669;
        }
        #output {
            background-color: #0f172a;
            border: 1px solid #334155;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 14px;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .log-entry {
            margin: 5px 0;
            padding: 5px;
            border-radius: 4px;
        }
        .log-success {
            color: #10b981;
        }
        .log-error {
            color: #ef4444;
        }
        .log-info {
            color: #3b82f6;
        }
        .log-warning {
            color: #f59e0b;
        }
        .section {
            margin: 20px 0;
            padding: 20px;
            background-color: #1e293b;
            border-radius: 8px;
            border: 1px solid #334155;
        }
        .user-info {
            background-color: #0f172a;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
        }
        .info-item {
            margin: 5px 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .status-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        .status-active {
            background-color: #10b981;
            color: white;
        }
        .status-inactive {
            background-color: #6b7280;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Teste Premium - Stack Auth (OAuth)</h1>
        
        <div class="section">
            <h2>👤 Informações do Usuário</h2>
            <div id="userInfo" class="user-info">
                <div class="info-item">
                    <strong>Status:</strong> <span id="authStatus">Verificando...</span>
                </div>
                <div class="info-item">
                    <strong>Email:</strong> <span id="userEmail">-</span>
                </div>
                <div class="info-item">
                    <strong>Stack ID:</strong> <span id="stackId">-</span>
                </div>
                <div class="info-item">
                    <strong>Premium:</strong> <span id="premiumStatus">-</span>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🚀 Ações Rápidas</h2>
            
            <button onclick="refreshUserInfo()" class="success">
                🔄 Atualizar Informações
            </button>
            
            <button onclick="activatePremium()" class="success">
                ✅ Ativar Premium (1 mês)
            </button>
            
            <button onclick="checkAccessDirectly()">
                🔍 Verificar Acesso Direto
            </button>
            
            <button onclick="clearLocalStorage()">
                🧹 Limpar Cache Local
            </button>
            
            <button onclick="testPlayer()">
                🎬 Testar Player
            </button>
            
            <button onclick="clearOutput()">
                🗑️ Limpar Console
            </button>
        </div>

        <div class="section">
            <h2>📝 Console de Debug</h2>
            <div id="output"></div>
        </div>
    </div>

    <script>
        let currentUser = null;

        // Função para adicionar log ao output
        function addLog(message, type = 'info') {
            const output = document.getElementById('output');
            const entry = document.createElement('div');
            entry.className = `log-entry log-${type}`;
            const timestamp = new Date().toLocaleTimeString('pt-BR');
            entry.textContent = `[${timestamp}] ${message}`;
            output.appendChild(entry);
            output.scrollTop = output.scrollHeight;
        }

        // Limpar output
        function clearOutput() {
            document.getElementById('output').innerHTML = '';
            addLog('Console limpo', 'info');
        }

        // Verificar se usuário está autenticado via Stack
        async function checkStackAuth() {
            try {
                // Importar Stack dynamicamente
                const { stackServerApp } = await import('https://unpkg.com/@stackframe/stack@2.8.22/dist/index.js');
                
                // Verificar usuário atual
                const user = await stackServerApp.getUser();
                return user;
            } catch (error) {
                // Se não conseguir importar ou obter usuário, tentar via API
                return null;
            }
        }

        // Atualizar informações do usuário
        async function refreshUserInfo() {
            addLog('🔄 Atualizando informações do usuário...', 'info');
            
            try {
                // Buscar informações do Stack Auth via API
                const response = await fetch('/api/me');
                
                if (response.ok) {
                    const data = await response.json();
                    currentUser = data;
                    
                    // Atualizar UI
                    document.getElementById('authStatus').innerHTML = '<span class="status-badge status-active">AUTENTICADO</span>';
                    document.getElementById('userEmail').textContent = data.email || 'Não informado';
                    document.getElementById('stackId').textContent = data.id || 'Não encontrado';
                    
                    addLog(`✅ Usuário encontrado: ${data.email}`, 'success');
                    addLog(`📍 Stack ID: ${data.id}`, 'info');
                    
                    // Verificar status premium
                    await checkPremiumStatus(data.id);
                    
                } else {
                    document.getElementById('authStatus').innerHTML = '<span class="status-badge status-inactive">NÃO AUTENTICADO</span>';
                    addLog('❌ Usuário não autenticado', 'error');
                    addLog('💡 Faça login primeiro em https://newspports.com', 'warning');
                }
                
            } catch (error) {
                addLog(`❌ Erro ao buscar usuário: ${error.message}`, 'error');
                document.getElementById('authStatus').innerHTML = '<span class="status-badge status-inactive">ERRO</span>';
            }
        }

        // Verificar status premium
        async function checkPremiumStatus(userId) {
            if (!userId) {
                addLog('⚠️ User ID não fornecido', 'warning');
                return;
            }
            
            try {
                const response = await fetch('/api/check-access', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ userId })
                });
                
                const data = await response.json();
                
                if (data.has_access) {
                    document.getElementById('premiumStatus').innerHTML = 
                        `<span class="status-badge status-active">ATIVO até ${new Date(data.expires_at).toLocaleDateString('pt-BR')}</span>`;
                    addLog(`✅ Premium ATIVO - Expira em: ${new Date(data.expires_at).toLocaleDateString('pt-BR')}`, 'success');
                } else {
                    document.getElementById('premiumStatus').innerHTML = 
                        '<span class="status-badge status-inactive">INATIVO</span>';
                    addLog('❌ Premium INATIVO', 'warning');
                }
                
            } catch (error) {
                addLog(`❌ Erro ao verificar premium: ${error.message}`, 'error');
            }
        }

        // Ativar premium
        async function activatePremium() {
            if (!currentUser) {
                addLog('❌ Faça login primeiro!', 'error');
                return;
            }
            
            addLog('🚀 Ativando premium...', 'info');
            
            try {
                const response = await fetch('/api/activate-premium-stack', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        userId: currentUser.id,
                        email: currentUser.email,
                        tier: 'premium',
                        months: 1
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    addLog('✅ PREMIUM ATIVADO COM SUCESSO!', 'success');
                    addLog(`📅 Expira em: ${new Date(data.expiresAt).toLocaleDateString('pt-BR')}`, 'info');
                    
                    // Atualizar UI
                    setTimeout(() => refreshUserInfo(), 1000);
                    
                    // Limpar caches
                    clearLocalStorage();
                    
                } else {
                    addLog(`❌ Erro ao ativar: ${data.error}`, 'error');
                    if (data.details) {
                        addLog(`📋 Detalhes: ${data.details}`, 'error');
                    }
                }
                
            } catch (error) {
                addLog(`❌ Erro na requisição: ${error.message}`, 'error');
            }
        }

        // Verificar acesso diretamente
        async function checkAccessDirectly() {
            if (!currentUser) {
                addLog('❌ Faça login primeiro!', 'error');
                return;
            }
            
            addLog(`🔍 Verificando acesso para ID: ${currentUser.id}`, 'info');
            
            try {
                const response = await fetch('/api/check-access', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ userId: currentUser.id })
                });
                
                const data = await response.json();
                addLog('📊 Resposta do check-access:', 'info');
                addLog(JSON.stringify(data, null, 2), 'info');
                
            } catch (error) {
                addLog(`❌ Erro: ${error.message}`, 'error');
            }
        }

        // Limpar localStorage
        function clearLocalStorage() {
            addLog('🧹 Limpando cache local...', 'info');
            
            // Limpar apenas items relacionados ao trial e acesso
            localStorage.removeItem('trial-storage');
            localStorage.removeItem('access-storage');
            
            addLog('✅ Cache limpo!', 'success');
            addLog('💡 Recarregue a página para aplicar as mudanças', 'warning');
        }

        // Testar player
        function testPlayer() {
            addLog('🎬 Abrindo player...', 'info');
            window.open('/watch/espn', '_blank');
        }

        // Inicializar ao carregar
        window.addEventListener('load', () => {
            addLog('🎬 NewSpports - Teste Premium Stack Auth', 'info');
            addLog('👆 Use os botões acima para testar', 'info');
            
            // Verificar usuário automaticamente
            refreshUserInfo();
        });
    </script>
</body>
</html>