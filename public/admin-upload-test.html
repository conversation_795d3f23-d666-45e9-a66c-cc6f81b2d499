<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Upload Admin - NewSpports</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            margin-bottom: 30px;
        }
        .upload-area {
            border: 2px dashed #ccc;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            background-color: #fafafa;
            margin-bottom: 20px;
        }
        .file-input {
            display: none;
        }
        .upload-button {
            background-color: #dc2626;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            font-size: 16px;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        .upload-button:hover {
            background-color: #b91c1c;
        }
        .upload-button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .status {
            margin-top: 20px;
            padding: 15px;
            border-radius: 6px;
            display: none;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.loading {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
            display: block;
        }
        .file-info {
            margin-top: 10px;
            font-size: 14px;
            color: #666;
        }
        .progress {
            width: 100%;
            height: 20px;
            background-color: #e0e0e0;
            border-radius: 10px;
            margin-top: 20px;
            overflow: hidden;
            display: none;
        }
        .progress-bar {
            height: 100%;
            background-color: #dc2626;
            width: 0%;
            transition: width 0.3s;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎥 Upload de Vídeo Tutorial - Admin</h1>
        
        <div class="upload-area">
            <p>Arrastra un video aquí o haz clic para seleccionar</p>
            <input type="file" id="videoFile" class="file-input" accept="video/mp4,video/webm,video/ogg,video/quicktime">
            <button class="upload-button" onclick="document.getElementById('videoFile').click()">
                Seleccionar Video
            </button>
            <div class="file-info" id="fileInfo"></div>
        </div>

        <div class="progress" id="progress">
            <div class="progress-bar" id="progressBar"></div>
        </div>

        <div class="status" id="status"></div>

        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee;">
            <p style="color: #666; font-size: 14px;">
                <strong>Formatos aceptados:</strong> MP4, WebM, OGG, MOV<br>
                <strong>Tamaño máximo:</strong> 100MB<br>
                <strong>Cookie requerida:</strong> admin-auth=true
            </p>
        </div>
    </div>

    <script>
        // Verificar si tiene cookie de admin
        if (document.cookie.indexOf('admin-auth=true') === -1) {
            alert('⚠️ No tienes permisos de administrador. Por favor, inicia sesión primero en /admin-login');
        }

        const fileInput = document.getElementById('videoFile');
        const fileInfo = document.getElementById('fileInfo');
        const status = document.getElementById('status');
        const progress = document.getElementById('progress');
        const progressBar = document.getElementById('progressBar');

        fileInput.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                fileInfo.textContent = `Archivo seleccionado: ${file.name} (${(file.size / 1024 / 1024).toFixed(2)} MB)`;
                uploadFile(file);
            }
        });

        // Drag and drop
        const uploadArea = document.querySelector('.upload-area');
        
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.style.borderColor = '#dc2626';
            uploadArea.style.backgroundColor = '#fee';
        });

        uploadArea.addEventListener('dragleave', (e) => {
            e.preventDefault();
            uploadArea.style.borderColor = '#ccc';
            uploadArea.style.backgroundColor = '#fafafa';
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.style.borderColor = '#ccc';
            uploadArea.style.backgroundColor = '#fafafa';
            
            const file = e.dataTransfer.files[0];
            if (file && file.type.startsWith('video/')) {
                fileInput.files = e.dataTransfer.files;
                fileInfo.textContent = `Archivo seleccionado: ${file.name} (${(file.size / 1024 / 1024).toFixed(2)} MB)`;
                uploadFile(file);
            } else {
                showStatus('Por favor, selecciona un archivo de video válido', 'error');
            }
        });

        function uploadFile(file) {
            if (file.size > 104857600) {
                showStatus('Error: El archivo es demasiado grande. Máximo 100MB.', 'error');
                return;
            }

            const formData = new FormData();
            formData.append('file', file);

            showStatus('Subiendo video...', 'loading');
            progress.style.display = 'block';

            const xhr = new XMLHttpRequest();

            xhr.upload.addEventListener('progress', (e) => {
                if (e.lengthComputable) {
                    const percentComplete = (e.loaded / e.total) * 100;
                    progressBar.style.width = percentComplete + '%';
                }
            });

            xhr.addEventListener('load', function() {
                if (xhr.status === 200) {
                    const response = JSON.parse(xhr.responseText);
                    showStatus(`✅ Video subido correctamente! URL: ${response.url}`, 'success');
                    progressBar.style.width = '100%';
                    
                    // Mostrar detalles del response
                    console.log('Respuesta completa:', response);
                    
                    // Limpiar después de 2 segundos
                    setTimeout(() => {
                        fileInput.value = '';
                        fileInfo.textContent = '';
                        progress.style.display = 'none';
                        progressBar.style.width = '0%';
                    }, 2000);
                } else {
                    const error = JSON.parse(xhr.responseText);
                    showStatus(`Error: ${error.error || 'Error al subir el video'}`, 'error');
                }
            });

            xhr.addEventListener('error', function() {
                showStatus('Error de conexión al subir el video', 'error');
            });

            xhr.open('POST', '/api/admin/upload-video');
            xhr.send(formData);
        }

        function showStatus(message, type) {
            status.textContent = message;
            status.className = 'status ' + type;
            status.style.display = 'block';
            
            if (type !== 'loading') {
                setTimeout(() => {
                    status.style.display = 'none';
                }, 5000);
            }
        }
    </script>
</body>
</html>