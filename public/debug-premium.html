<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Premium - NewSpports</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #0f172a;
            color: #e2e8f0;
            padding: 20px;
            max-width: 1200px;
            margin: 0 auto;
        }
        .container {
            background-color: #1e293b;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
        }
        h1 {
            color: #f59e0b;
            margin-bottom: 20px;
        }
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 20px;
        }
        .section {
            background-color: #0f172a;
            border: 1px solid #334155;
            border-radius: 8px;
            padding: 20px;
        }
        .section h3 {
            color: #3b82f6;
            margin-top: 0;
        }
        pre {
            background-color: #000;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
            margin: 10px 0;
        }
        button {
            background-color: #10b981;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            margin: 5px;
            transition: background-color 0.2s;
        }
        button:hover {
            background-color: #059669;
        }
        .error {
            color: #ef4444;
        }
        .success {
            color: #10b981;
        }
        .warning {
            color: #f59e0b;
        }
        .status-item {
            margin: 10px 0;
            padding: 10px;
            background-color: #1e293b;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Debug Premium Access</h1>
        
        <div class="grid">
            <div class="section">
                <h3>📊 LocalStorage Status</h3>
                <div id="localStorage"></div>
            </div>
            
            <div class="section">
                <h3>👤 User Info</h3>
                <div id="userInfo"></div>
            </div>
            
            <div class="section">
                <h3>🔐 Access Check</h3>
                <div id="accessCheck"></div>
            </div>
            
            <div class="section">
                <h3>💾 Database Status</h3>
                <div id="dbStatus"></div>
            </div>
        </div>
        
        <div style="margin-top: 20px; text-align: center;">
            <button onclick="runFullDebug()">🔄 Run Full Debug</button>
            <button onclick="forceFixPremium()">🛠️ Force Fix Premium</button>
            <button onclick="clearAllAndReset()">🧹 Clear All & Reset</button>
            <button onclick="window.location.href='/watch/premium-real-madrid-tv'">🎬 Test Player</button>
        </div>
        
        <div class="section" style="margin-top: 20px;">
            <h3>📝 Debug Log</h3>
            <pre id="debugLog"></pre>
        </div>
    </div>

    <script>
        const log = (message, type = 'info') => {
            const debugLog = document.getElementById('debugLog');
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'error' ? '#ef4444' : type === 'success' ? '#10b981' : '#3b82f6';
            debugLog.innerHTML += `<span style="color: ${color}">[${timestamp}] ${message}</span>\n`;
            debugLog.scrollTop = debugLog.scrollHeight;
        };

        async function checkLocalStorage() {
            log('Checking localStorage...');
            const section = document.getElementById('localStorage');
            
            const items = {
                'trial-storage': localStorage.getItem('trial-storage'),
                'access-storage': localStorage.getItem('access-storage'),
                'auth-storage': localStorage.getItem('auth-storage')
            };
            
            let html = '';
            for (const [key, value] of Object.entries(items)) {
                if (value) {
                    try {
                        const parsed = JSON.parse(value);
                        html += `<div class="status-item">
                            <strong>${key}:</strong>
                            <pre>${JSON.stringify(parsed, null, 2)}</pre>
                        </div>`;
                        
                        // Log specific issues
                        if (key === 'trial-storage' && parsed.state?.isExpired) {
                            log(`⚠️ Trial is marked as expired`, 'warning');
                        }
                        if (key === 'access-storage' && !parsed.state?.hasAccess) {
                            log(`❌ Access storage shows no access`, 'error');
                        }
                    } catch (e) {
                        html += `<div class="status-item error">${key}: Invalid JSON</div>`;
                    }
                } else {
                    html += `<div class="status-item warning">${key}: Not found</div>`;
                }
            }
            
            section.innerHTML = html;
        }

        async function checkUserInfo() {
            log('Fetching user info...');
            const section = document.getElementById('userInfo');
            
            try {
                // Try Stack Auth first
                const meResponse = await fetch('/api/me');
                if (meResponse.ok) {
                    const userData = await meResponse.json();
                    section.innerHTML = `
                        <div class="status-item success">
                            <strong>Stack Auth User</strong>
                            <p>ID: ${userData.id}</p>
                            <p>Email: ${userData.email}</p>
                            <p>Auth Methods: ${userData.authMethods?.join(', ') || 'N/A'}</p>
                        </div>
                    `;
                    log(`✅ Stack Auth user found: ${userData.id}`, 'success');
                    return userData.id;
                }
            } catch (e) {
                log('Stack Auth check failed', 'error');
            }
            
            // Try Supabase
            try {
                const statusResponse = await fetch('/api/stripe/subscription-status');
                if (statusResponse.ok) {
                    const data = await statusResponse.json();
                    section.innerHTML += `
                        <div class="status-item">
                            <strong>Subscription Status</strong>
                            <p>Has Access: ${data.hasAccess ? '✅' : '❌'}</p>
                            <p>Is Subscribed: ${data.isSubscribed ? '✅' : '❌'}</p>
                            <p>User ID: ${data.userId || 'Not found'}</p>
                        </div>
                    `;
                    return data.userId;
                }
            } catch (e) {
                log('Subscription status check failed', 'error');
            }
            
            section.innerHTML = '<div class="status-item error">No user found!</div>';
            return null;
        }

        async function checkAccess(userId) {
            if (!userId) {
                log('No user ID to check access', 'error');
                return;
            }
            
            log(`Checking access for user: ${userId}`);
            const section = document.getElementById('accessCheck');
            
            try {
                const response = await fetch('/api/check-access', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ userId })
                });
                
                const data = await response.json();
                
                section.innerHTML = `
                    <div class="status-item ${data.has_access ? 'success' : 'error'}">
                        <strong>Access Check Result</strong>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    </div>
                `;
                
                if (data.has_access) {
                    log(`✅ User has access: ${data.access_type}`, 'success');
                } else {
                    log(`❌ User has NO access`, 'error');
                }
                
                return data;
            } catch (e) {
                section.innerHTML = `<div class="status-item error">Error: ${e.message}</div>`;
                log(`Error checking access: ${e.message}`, 'error');
            }
        }

        async function checkDatabase(userId) {
            log('Checking database status...');
            const section = document.getElementById('dbStatus');
            
            try {
                // Check if user exists in database
                const response = await fetch('/api/debug-premium', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ userId })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    section.innerHTML = `
                        <div class="status-item">
                            <strong>Database Records</strong>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                }
            } catch (e) {
                section.innerHTML = `<div class="status-item error">Database check failed</div>`;
            }
        }

        async function runFullDebug() {
            log('=== Starting Full Debug ===', 'info');
            
            // 1. Check localStorage
            await checkLocalStorage();
            
            // 2. Get user info
            const userId = await checkUserInfo();
            
            if (userId) {
                // 3. Check access
                await checkAccess(userId);
                
                // 4. Check database
                await checkDatabase(userId);
            }
            
            log('=== Debug Complete ===', 'info');
        }

        async function forceFixPremium() {
            log('🛠️ Starting force fix...', 'warning');
            
            // Get user ID
            const userId = await checkUserInfo();
            if (!userId) {
                log('❌ Cannot fix without user ID', 'error');
                return;
            }
            
            // 1. Clear bad localStorage
            log('Clearing localStorage...');
            localStorage.removeItem('trial-storage');
            localStorage.removeItem('access-storage');
            
            // 2. Check/Activate premium
            log('Checking premium status...');
            const accessData = await checkAccess(userId);
            
            if (!accessData?.has_access) {
                log('Activating premium...');
                try {
                    const activateResponse = await fetch('/api/activate-premium-stack', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            userId: userId,
                            email: '<EMAIL>',
                            tier: 'premium',
                            months: 1
                        })
                    });
                    
                    const result = await activateResponse.json();
                    if (result.success) {
                        log('✅ Premium activated!', 'success');
                    } else {
                        log(`❌ Activation failed: ${result.error}`, 'error');
                    }
                } catch (e) {
                    log(`❌ Error activating: ${e.message}`, 'error');
                }
            }
            
            // 3. Force correct localStorage state
            log('Setting correct localStorage state...');
            
            const correctAccessState = {
                state: {
                    hasAccess: true,
                    accessType: 'assinatura',
                    expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
                    lastCheck: new Date().toISOString(),
                    isLoading: false
                },
                version: 0
            };
            
            const correctTrialState = {
                state: {
                    hasStarted: false,
                    startTime: null,
                    timeRemaining: 300,
                    isExpired: false,
                    canReset: true
                },
                version: 0
            };
            
            localStorage.setItem('access-storage', JSON.stringify(correctAccessState));
            localStorage.setItem('trial-storage', JSON.stringify(correctTrialState));
            
            log('✅ Force fix complete! Reload the page.', 'success');
            
            setTimeout(() => {
                window.location.reload();
            }, 2000);
        }

        async function clearAllAndReset() {
            log('🧹 Clearing all data...', 'warning');
            
            // Clear all localStorage
            const keys = Object.keys(localStorage);
            keys.forEach(key => {
                if (key.includes('trial') || key.includes('access') || key.includes('auth')) {
                    localStorage.removeItem(key);
                    log(`Removed: ${key}`);
                }
            });
            
            log('✅ All data cleared! Please login again.', 'success');
            
            setTimeout(() => {
                window.location.href = '/login';
            }, 2000);
        }

        // Auto-run debug on load
        window.addEventListener('load', () => {
            runFullDebug();
        });
    </script>
</body>
</html>