const fs = require('fs');

// Canais que já estão funcionando (informados pelo usuário)
const workingChannels = [
  '30A Golg <PERSON>',
  'ACC Digital Network',
  'Adjarasport1',
  'ADO TV',
  'Africa 24 Sport',
  'Afro Sport Nigeria',
  'antena sport',
  'Arryadia',
  'Astrahan.Ru sport',
  'Bahrain Sports 1',
  'Bahrain Sports 2',
  'Belarus-5',
  'Bellator MMA',
  'Billiard TV',
  'Canal do Inter',
  'CBS Sports Network USA',
  'CT Sport',
  'Dong Nai TV2',
  'Dubai Racing',
  'Dubai Racing 2',
  'Dubai Racing 3',
  'Dubai Sports 1',
  'Dubai Sports 2',
  'Dubai Sports 3',
  'Eleven Sports 1'
];

// Normalizar nome para comparação
function normalizeName(name) {
  return name.toLowerCase()
    .replace(/\s+/g, ' ')
    .replace(/[^\w\s]/g, '')
    .trim();
}

// Ler arquivo com todos os canais
const allChannels = JSON.parse(fs.readFileSync('all-sports-channels.json', 'utf8'));

// Filtrar canais que precisam ser testados
const channelsToTest = allChannels.filter(channel => {
  const channelName = normalizeName(channel.name);
  return !workingChannels.some(working => {
    const workingName = normalizeName(working);
    return channelName === workingName || 
           channelName.includes(workingName) || 
           workingName.includes(channelName);
  });
});

console.log(`Total de canais de esportes: ${allChannels.length}`);
console.log(`Canais já funcionando: ${workingChannels.length}`);
console.log(`Canais para testar: ${channelsToTest.length}`);

// Salvar lista de canais para testar
fs.writeFileSync('channels-to-test.json', JSON.stringify(channelsToTest, null, 2));

// Também criar uma lista simplificada apenas com nomes
const channelNames = channelsToTest.map(ch => ch.name).sort();
fs.writeFileSync('channels-to-test-names.txt', channelNames.join('\n'));

console.log('\nArquivos criados:');
console.log('- channels-to-test.json (lista completa)');
console.log('- channels-to-test-names.txt (apenas nomes)');