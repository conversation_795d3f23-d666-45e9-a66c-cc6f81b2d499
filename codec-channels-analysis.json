{"summary": {"total": 87, "working": 87, "broken": 0}, "results": [{"id": "30AGolfKingdom.us", "name": "30A Golf Kingdom", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.4d401f,mp4a.40.2", "streamUrl": "https://30a-tv.com/feeds/vidaa/golf.m3u8"}, {"id": "Adjarasport1.ge", "name": "Adjarasport 1", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.4d401e,mp4a.40.2", "streamUrl": "https://live20.bozztv.com/dvrfl05/gin-adjara/index.m3u8"}, {"id": "AfroSportNigeria.ng", "name": "AfroSport Nigeria", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.4d401f,mp4a.40.2", "streamUrl": "https://newproxy3.vidivu.tv/vidivu_afrosport/index.m3u8"}, {"id": "AlkassFive.qa", "name": "Alkass Five", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.640020,mp4a.40.2", "streamUrl": "https://liveakgr.alkassdigital.net/hls/live/2097037/Alkass5azq/master.m3u8"}, {"id": "AlkassFour.qa", "name": "Alkass Four", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.640020,mp4a.40.2", "streamUrl": "https://liveakgr.alkassdigital.net/hls/live/2097037/Alkass4azq/master.m3u8"}, {"id": "AlkassOne.qa", "name": "Alkass One", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.640020,mp4a.40.2", "streamUrl": "https://liveakgr.alkassdigital.net/hls/live/2097037/Alkass1muytrdc/master.m3u8"}, {"id": "AlkassSHOOF.qa", "name": "Alkass SHOOF", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.64002a,mp4a.40.2", "streamUrl": "https://liveakgr.alkassdigital.net/hls/live/2097037/Alkass6Shoof1ab/master.m3u8"}, {"id": "AlkassSix.qa", "name": "Alkass Six", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.64002a,mp4a.40.2", "streamUrl": "https://liveakgr.alkassdigital.net/hls/live/2097037/Alkass6buzay/master.m3u8"}, {"id": "AlkassThree.qa", "name": "Alkass Three", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.64002a,mp4a.40.2", "streamUrl": "https://liveakgr.alkassdigital.net/hls/live/2097037/Alkass3vakazq/master.m3u8"}, {"id": "AlkassTwo.qa", "name": "Alkass Two", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.64001f,mp4a.40.2", "streamUrl": "https://liveakgr.alkassdigital.net/hls/live/2097037/Alkass2hefazq/master.m3u8"}, {"id": "AntenaSport.ro", "name": "AntenaSport", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.4d401f,mp4a.40.2", "streamUrl": "https://stream1.antenaplay.ro/as/asrolive1/playlist.m3u8"}, {"id": "CBSSportsGolazoNetwork.us", "name": "CBS Sports Golazo Network", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.64001f,mp4a.40.2", "streamUrl": "https://dai.google.com/linear/hls/event/GxrCGmwST0ixsrc_QgB6qw/master.m3u8"}, {"id": "CBSSportsHQ.us", "name": "CBS Sports HQ", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.64001f,mp4a.40.2", "streamUrl": "http://cfd-v4-service-channel-stitcher-use1-1.prd.pluto.tv/stitch/hls/channel/5e9f2c05172a0f0007db4786/master.m3u8?appName=web&appVersion=unknown&clientTime=0&deviceDNT=0&deviceId=6c27b8f5-30d3-11ef-9cf5-e9ddff8ff496&deviceMake=Chrome&deviceModel=web&deviceType=web&deviceVersion=unknown&includeExtendedEvents=false&serverSideAds=false&sid=3a15356b-8467-4ae7-b2f6-9c8467fcf41d"}, {"id": "ESPNews.us", "name": "ESPNews", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.640028,mp4a.40.2", "streamUrl": "https://fl3.moveonjoy.com/ESPN_NEWS/index.m3u8"}, {"id": "ESPNU.us", "name": "ESPNU", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.640028,mp4a.40.2", "streamUrl": "https://fl3.moveonjoy.com/ESPN_U/index.m3u8"}, {"id": "FanDuelSportsNetwork.us", "name": "FanDuel Sports Network", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.4d4020,mp4a.40.2", "streamUrl": "https://fl3.moveonjoy.com/PAC_12/index.m3u8"}, {"id": "FanDuelTV.us", "name": "FanDuel TV", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.4d401f,mp4a.40.2", "streamUrl": "https://fl3.moveonjoy.com/TVG/index.m3u8"}, {"id": "FIFAPlus.pl", "name": "FIFA+", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.640029,mp4a.40.2", "streamUrl": "https://a62dad94.wurl.com/master/f36d25e7e52f1ba8d7e56eb859c636563214f541/UmFrdXRlblRWLWV1X0ZJRkFQbHVzRW5nbGlzaF9ITFM/playlist.m3u8"}, {"id": "FITE247.us", "name": "FITE 24/7", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.640029,mp4a.40.2", "streamUrl": "https://d3d85c7qkywguj.cloudfront.net/scheduler/scheduleMaster/263.m3u8"}, {"id": "Football.ru", "name": "Football", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.4d0020,mp4a.40.2", "streamUrl": "http://31.148.48.15/Futbol_HD/index.m3u8"}, {"id": "FoxDeportes.us", "name": "Fox Deportes", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.640028,mp4a.40.2", "streamUrl": "https://apollo.production-public.tubi.io/live/fox-sports-espanol.m3u8"}, {"id": "FuboSportsNetwork.us", "name": "Fubo Sports Network", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.64001f,mp4a.40.2", "streamUrl": "https://apollo.production-public.tubi.io/live/ac-fubo.m3u8"}, {"id": "FUELTV.at", "name": "FUEL TV", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.4d4028,mp4a.40.2", "streamUrl": "https://amg01074-fueltv-fueltvemeaen-rakuten-b6j62.amagi.tv/hls/amagi_hls_data_rakutenAA-fueltvemeaen/CDN/master.m3u8"}, {"id": "GamePlus.ca", "name": "Game+", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.4d401f,mp4a.40.2", "streamUrl": "https://a-cdn.klowdtv.com/live2/fntsy_720p/playlist.m3u8"}, {"id": "GloryKickboxing.us", "name": "Glory Kickboxing", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.640029,mp4a.40.2", "streamUrl": "https://6f972d29.wurl.com/master/f36d25e7e52f1ba8d7e56eb859c636563214f541/UmFrdXRlblRWLWV1X0dsb3J5S2lja2JveGluZ19ITFM/playlist.m3u8"}, {"id": "HorseTV.it", "name": "Horse TV", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.4d401f,mp4a.40.2", "streamUrl": "https://a-cdn.klowdtv.com/live2/horsetv_720p/playlist.m3u8"}, {"id": "InsightTV.nl", "name": "Insight TV", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.77.30,mp4a.40.2", "streamUrl": "https://insighttv-samsungau.amagi.tv/playlist.m3u8"}, {"id": "InterTV.it", "name": "Inter TV", "error": "Incompatible codecs", "status": "potentially_working", "codec": "mp4a.40.2,avc1.4D401F", "streamUrl": "https://ilglobotv-live.akamaized.net/channels/InterTV/Live.m3u8"}, {"id": "IRIB3.ir", "name": "IRIB 3", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.4d4028,mp4a.40.2", "streamUrl": "https://lenz.splus.ir/PLTV/88888888/224/3221226868/index.m3u8"}, {"id": "ITVDeportes.mx", "name": "ITV Deportes", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.64001f,mp4a.40.2", "streamUrl": "https://thm-it-roku.otteravision.com/thm/it/it.m3u8"}, {"id": "K19.at", "name": "K19", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.640028,mp4a.40.2", "streamUrl": "https://1853185335.rsc.cdn77.org/K192/tv/playlist.m3u8"}, {"id": "KCMNLD6.us", "name": "KCMN-LD6", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.4d4028,mp4a.40.2", "streamUrl": "https://cdn-uw2-prod.tsv2.amagi.tv/linear/amg02873-kravemedia-mtrspt1-distrotv/playlist.m3u8"}, {"id": "KHLPrime.ru", "name": "KHL Prime", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.4d0020,mp4a.40.2", "streamUrl": "http://194.143.148.28:8080/KHL_HD/index.m3u8"}, {"id": "KozoomTV.fr", "name": "Kozoom TV", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.4d401e,mp4a.40.2", "streamUrl": "https://streams2.sofast.tv/v1/master/611d79b11b77e2f571934fd80ca1413453772ac7/fdd6f243-f971-4a1a-9510-97ac01d6b37f/manifest.m3u8"}, {"id": "KSASports1.sa", "name": "KSA Sports 1", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.4d401f,mp4a.40.2", "streamUrl": "https://live20.bozztv.com/gin-36bay3/ga-ksaports1/index.m3u8"}, {"id": "LacrosseTV.us", "name": "Lacrosse TV", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.64001f, mp4a.40.2", "streamUrl": "https://1840769862.rsc.cdn77.org/FTF/LSN_SCTE.m3u8"}, {"id": "MadeinBOTV.it", "name": "MadeinBO TV", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.640028,mp4a.40.2", "streamUrl": "https://srvx1.selftv.video/dmchannel/live/playlist.m3u8"}, {"id": "MatchArena.ru", "name": "Match! Arena", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.4d001e,mp4a.40.2", "streamUrl": "http://194.143.148.28:8080/MatchArena/index.m3u8"}, {"id": "MAVTV.us", "name": "MAVTV", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.640020,mp4a.40.2", "streamUrl": "https://fl3.moveonjoy.com/MAV_TV/index.m3u8"}, {"id": "MAVTVSelect.us", "name": "MAVTV Select", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.4d4028,mp4a.40.2", "streamUrl": "https://d3h07n6l1exhds.cloudfront.net/v1/master/3722c60a815c199d9c0ef36c5b73da68a62b09d1/cc-0z2yyo4dxctc7/playlist.m3u8"}, {"id": "MLB.us", "name": "MLB", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.64001f,mp4a.40.2", "streamUrl": "http://cfd-v4-service-channel-stitcher-use1-1.prd.pluto.tv/stitch/hls/channel/5e66968a70f34c0007d050be/master.m3u8?appName=web&appVersion=unknown&clientTime=0&deviceDNT=0&deviceId=6c29dbd3-30d3-11ef-9cf5-e9ddff8ff496&deviceMake=Chrome&deviceModel=web&deviceType=web&deviceVersion=unknown&includeExtendedEvents=false&serverSideAds=false&sid=0f1d3baf-2f1b-47d7-9966-3e92a98f1c8a"}, {"id": "MLBNetwork.us", "name": "MLB Network", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.640020,mp4a.40.2", "streamUrl": "https://fl3.moveonjoy.com/MLB_NETWORK/index.m3u8"}, {"id": "MMATVcom.ru", "name": "MMA-TV.com", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.4d4028,mp4a.40.2", "streamUrl": "https://streams2.sofast.tv/vglive-sk-462904/playlist.m3u8"}, {"id": "MoreThanSportsTV.de", "name": "More Than Sports TV", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.4d4028,mp4a.40.2", "streamUrl": "https://mts1.iptv-playoutcenter.de/mts/mts-web/playlist.m3u8"}, {"id": "MSG.us", "name": "MSG", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.640020,mp4a.40.2", "streamUrl": "https://fl3.moveonjoy.com/MSG/index.m3u8"}, {"id": "NauticalChannel.it", "name": "Nautical Channel", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.4d401f,mp4a.40.2", "streamUrl": "https://a-cdn.klowdtv.com/live2/nautical_720p/playlist.m3u8"}, {"id": "NFLChannel.us", "name": "NFL Channel", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.64001f,mp4a.40.2", "streamUrl": "http://cfd-v4-service-channel-stitcher-use1-1.prd.pluto.tv/stitch/hls/channel/5ced7d5df64be98e07ed47b6/master.m3u8?appName=web&appVersion=unknown&clientTime=0&deviceDNT=0&deviceId=6c2a5105-30d3-11ef-9cf5-e9ddff8ff496&deviceMake=Chrome&deviceModel=web&deviceType=web&deviceVersion=unknown&includeExtendedEvents=false&serverSideAds=false&sid=6477ef85-3680-442c-94d8-27197903b1f6"}, {"id": "NFLNetwork.us", "name": "NFL Network", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.640028,mp4a.40.2", "streamUrl": "https://fl3.moveonjoy.com/NFL_NETWORK/index.m3u8"}, {"id": "NFLRedZone.us", "name": "NFL RedZone", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.640028,mp4a.40.2", "streamUrl": "https://fl1.moveonjoy.com/NFL_RedZone/index.m3u8"}, {"id": "NHLNetwork.us", "name": "NHL Network", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.640028,mp4a.40.2", "streamUrl": "https://nhl-firetv.amagi.tv/playlist.m3u8"}, {"id": "NitroCircus.us", "name": "Nitro Circus", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.640028,mp4a.40.2", "streamUrl": "https://amg13231-actve-amg13231c1-rakuten-us-5604.playouts.now.amagi.tv/playlist.m3u8"}, {"id": "ONFootball.vn", "name": "ON Football", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.42c01f,mp4a.40.2", "streamUrl": "http://dvrfl05.bozztv.com/vch_vchannel11/index.m3u8"}, {"id": "PBRRidePass.us", "name": "PBR RidePass", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.64001f,mp4a.40.2", "streamUrl": "http://cfd-v4-service-channel-stitcher-use1-1.prd.pluto.tv/stitch/hls/channel/60d39387706fe50007fda8e8/master.m3u8?appName=web&appVersion=unknown&clientTime=0&deviceDNT=0&deviceId=6c2a9f21-30d3-11ef-9cf5-e9ddff8ff496&deviceMake=Chrome&deviceModel=web&deviceType=web&deviceVersion=unknown&includeExtendedEvents=false&serverSideAds=false&sid=f7275d4e-aa8e-4e8d-9d5e-6a5665bf8190"}, {"id": "PGATour.us", "name": "PGA Tour", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.64001f,mp4a.40.2", "streamUrl": "http://cfd-v4-service-channel-stitcher-use1-1.prd.pluto.tv/stitch/hls/channel/5de94dacb394a300099fa22a/master.m3u8?appName=web&appVersion=unknown&clientTime=0&deviceDNT=0&deviceId=6c2ac630-30d3-11ef-9cf5-e9ddff8ff496&deviceMake=Chrome&deviceModel=web&deviceType=web&deviceVersion=unknown&includeExtendedEvents=false&serverSideAds=false&sid=6c2b7359-0375-4f34-996b-4fb9429ead78"}, {"id": "PokerNightInAmerica.us", "name": "Poker Night In America", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.640029,mp4a.40.2", "streamUrl": "https://d2njbreu8qyfxo.cloudfront.net/scheduler/scheduleMaster/216.m3u8"}, {"id": "PokerGo.us", "name": "PokerGo", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.64001f,mp4a.40.2", "streamUrl": "http://cfd-v4-service-channel-stitcher-use1-1.prd.pluto.tv/stitch/hls/channel/5fc54366b04b2300072e31af/master.m3u8?appName=web&appVersion=unknown&clientTime=0&deviceDNT=0&deviceId=6c2b6274-30d3-11ef-9cf5-e9ddff8ff496&deviceMake=Chrome&deviceModel=web&deviceType=web&deviceVersion=unknown&includeExtendedEvents=false&serverSideAds=false&sid=a68d07ef-a794-43f8-a287-3bd7f27d909b"}, {"id": "RacingAmerica.us", "name": "Racing America", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.640028,mp4a.40.2", "streamUrl": "https://livetv-fa.tubi.video/racing-america/playlist.m3u8"}, {"id": "RealMadridTV.es", "name": "Real Madrid TV", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.640033,mp4a.40.5", "streamUrl": "https://rmtv.akamaized.net/hls/live/2043153/rmtv-es-web/master.m3u8"}, {"id": "RedBullTV.at", "name": "Red Bull TV", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.64001f,mp4a.40.2", "streamUrl": "https://3ea22335.wurl.com/master/f36d25e7e52f1ba8d7e56eb859c636563214f541/UmFrdXRlblRWLWdiX1JlZEJ1bGxUVl9ITFM/playlist.m3u8"}, {"id": "RightNowTV.us", "name": "Right Now TV", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.64001f,mp4a.40.2", "streamUrl": "https://a-cdn.klowdtv.com/live2/rightnowtv_720p/playlist.m3u8"}, {"id": "RTILa3.ci", "name": "RTI La 3", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.640029,mp4a.40.2", "streamUrl": "http://69.64.57.208/la3/index.m3u8"}, {"id": "SharjahSports.ae", "name": "Sharjah Sports", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.4d401f,mp4a.40.2", "streamUrl": "https://svs.itworkscdn.net/smc4sportslive/smc4.smil/playlist.m3u8"}, {"id": "SOSKanalPlus.rs", "name": "SOS Kanal Plus", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.4d001f,mp4a.40.2", "streamUrl": "https://53be5ef2d13aa.streamlock.net/soskanalplus/soskanalplus.stream/playlist.m3u8"}, {"id": "SportsGrid.us", "name": "SportsGrid", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.64001e,mp4a.40.2", "streamUrl": "https://sportsgrid-tribal.amagi.tv/playlist.m3u8"}, {"id": "SportsmanChannel.us", "name": "Sportsman Channel", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.640020,mp4a.40.2", "streamUrl": "https://fl3.moveonjoy.com/SPORTSMAN_CHANNEL/index.m3u8"}, {"id": "SportsNetNewYork.us", "name": "SportsNet New York", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.640028,mp4a.40.2", "streamUrl": "https://fl3.moveonjoy.com/SNY/index.m3u8"}, {"id": "SSCActionWaleed.sa", "name": "SSC Action Waleed", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.4D400B,mp4a.40.2", "streamUrl": "https://shls-live-event2-prod-dub.shahid.net/out/v1/0456ede1a39145d98b3d8c8062ddc998/index.m3u8"}, {"id": "StarSports1Tamil.in", "name": "Star Sports 1 Tamil", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.42c028,mp4a.40.2", "streamUrl": "https://live20.bozztv.com/akamaissh101/ssh101/vboxsungosttamil/playlist.m3u8"}, {"id": "SwerveSports.us", "name": "Swerve Sports", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.640028,mp4a.40.2", "streamUrl": "https://linear-253.frequency.stream/dist/glewedtv/253/hls/master/playlist.m3u8"}, {"id": "talkSPORT.uk", "name": "talkSPORT", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.64001f, mp4a.40.2", "streamUrl": "https://af7a8b4e.wurl.com/master/f36d25e7e52f1ba8d7e56eb859c636563214f541/TEctZ2JfdGFsa1NQT1JUX0hMUw/playlist.m3u8"}, {"id": "TDMSports.mo", "name": "TDM Sports", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.64002a,mp4a.40.2", "streamUrl": "https://live3.tdm.com.mo/ch4/sport_ch4.live/playlist.m3u8"}, {"id": "TeletrakTV.cl", "name": "Teletrak TV", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.42001f,mp4a.40.2", "streamUrl": "https://unlimited6-cl.dps.live/sportinghd/sportinghd.smil/playlist.m3u8"}, {"id": "TelewebionSport.ir", "name": "Telewebion Sport", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.4d4015,mp4a.40.2", "streamUrl": "https://ncdn.telewebion.com/twsport/live/playlist.m3u8"}, {"id": "TelewebionSport2.ir", "name": "Telewebion Sport 2", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.4d4015,mp4a.40.2", "streamUrl": "https://ncdn.telewebion.com/twsport2/live/playlist.m3u8"}, {"id": "TelewebionSport3.ir", "name": "Telewebion Sport 3", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.4d4015,mp4a.40.2", "streamUrl": "https://ncdn.telewebion.com/twsport3/live/playlist.m3u8"}, {"id": "TennisChannel.us", "name": "Tennis Channel", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.4d4028,mp4a.40.2", "streamUrl": "https://cdn-ue1-prod.tsv2.amagi.tv/linear/amg01444-tennischannelth-tennischannelnl-samsungnl/playlist.m3u8"}, {"id": "TRSport.it", "name": "TR Sport", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.77.30,mp4a.40.2", "streamUrl": "https://livetr.teleromagna.it/mia/live/playlist.m3u8"}, {"id": "TraceSportStars.fr", "name": "Trace Sport Stars", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.4d4028,mp4a.40.2", "streamUrl": "https://lightning-tracesport-samsungau.amagi.tv/playlist.m3u8"}, {"id": "TSNTheOcho.ca", "name": "TSN The Ocho", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.4d4028,mp4a.40.2", "streamUrl": "https://d3pnbvng3bx2nj.cloudfront.net/v1/master/3722c60a815c199d9c0ef36c5b73da68a62b09d1/cc-rds8g35qfqrnv/TSN_The_Ocho.m3u8"}, {"id": "TSN1.ca", "name": "TSN1", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.640020,mp4a.40.2", "streamUrl": "https://fl5.moveonjoy.com/TSN_1/index.m3u8"}, {"id": "TSN2.ca", "name": "TSN2", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.4d401f,mp4a.40.2", "streamUrl": "https://fl5.moveonjoy.com/TSN_2/index.m3u8"}, {"id": "TSN3.ca", "name": "TSN3", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.4d4020,mp4a.40.2", "streamUrl": "https://fl5.moveonjoy.com/TSN_3/index.m3u8"}, {"id": "TSN5.ca", "name": "TSN5", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.640020,mp4a.40.2", "streamUrl": "https://fl5.moveonjoy.com/TSN_5/index.m3u8"}, {"id": "TVRISport.id", "name": "TVRI Sport", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.4d4015,mp4a.40.2", "streamUrl": "https://ott-balancer.tvri.go.id/live/eds/SportHD/hls/SportHD.m3u8"}, {"id": "Unbeaten.us", "name": "Unbeaten", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.4d401e,mp4a.40.2", "streamUrl": "https://unbeaten-tcl.amagi.tv/playlist.m3u8"}, {"id": "W14DKD5.us", "name": "W14DK-D5", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.100.40,mp4a.40.2", "streamUrl": "https://2-fss-2.streamhoster.com/pl_118/204972-2205186-1/playlist.m3u8"}, {"id": "WorldofFreesports.de", "name": "World of Freesports", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.4d401e,mp4a.40.2", "streamUrl": "https://mainstreammedia-worldoffreesportsintl-rakuten.amagi.tv/playlist.m3u8"}], "potentiallyWorking": [{"id": "30AGolfKingdom.us", "name": "30A Golf Kingdom", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.4d401f,mp4a.40.2", "streamUrl": "https://30a-tv.com/feeds/vidaa/golf.m3u8"}, {"id": "Adjarasport1.ge", "name": "Adjarasport 1", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.4d401e,mp4a.40.2", "streamUrl": "https://live20.bozztv.com/dvrfl05/gin-adjara/index.m3u8"}, {"id": "AfroSportNigeria.ng", "name": "AfroSport Nigeria", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.4d401f,mp4a.40.2", "streamUrl": "https://newproxy3.vidivu.tv/vidivu_afrosport/index.m3u8"}, {"id": "AlkassFive.qa", "name": "Alkass Five", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.640020,mp4a.40.2", "streamUrl": "https://liveakgr.alkassdigital.net/hls/live/2097037/Alkass5azq/master.m3u8"}, {"id": "AlkassFour.qa", "name": "Alkass Four", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.640020,mp4a.40.2", "streamUrl": "https://liveakgr.alkassdigital.net/hls/live/2097037/Alkass4azq/master.m3u8"}, {"id": "AlkassOne.qa", "name": "Alkass One", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.640020,mp4a.40.2", "streamUrl": "https://liveakgr.alkassdigital.net/hls/live/2097037/Alkass1muytrdc/master.m3u8"}, {"id": "AlkassSHOOF.qa", "name": "Alkass SHOOF", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.64002a,mp4a.40.2", "streamUrl": "https://liveakgr.alkassdigital.net/hls/live/2097037/Alkass6Shoof1ab/master.m3u8"}, {"id": "AlkassSix.qa", "name": "Alkass Six", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.64002a,mp4a.40.2", "streamUrl": "https://liveakgr.alkassdigital.net/hls/live/2097037/Alkass6buzay/master.m3u8"}, {"id": "AlkassThree.qa", "name": "Alkass Three", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.64002a,mp4a.40.2", "streamUrl": "https://liveakgr.alkassdigital.net/hls/live/2097037/Alkass3vakazq/master.m3u8"}, {"id": "AlkassTwo.qa", "name": "Alkass Two", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.64001f,mp4a.40.2", "streamUrl": "https://liveakgr.alkassdigital.net/hls/live/2097037/Alkass2hefazq/master.m3u8"}, {"id": "AntenaSport.ro", "name": "AntenaSport", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.4d401f,mp4a.40.2", "streamUrl": "https://stream1.antenaplay.ro/as/asrolive1/playlist.m3u8"}, {"id": "CBSSportsGolazoNetwork.us", "name": "CBS Sports Golazo Network", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.64001f,mp4a.40.2", "streamUrl": "https://dai.google.com/linear/hls/event/GxrCGmwST0ixsrc_QgB6qw/master.m3u8"}, {"id": "CBSSportsHQ.us", "name": "CBS Sports HQ", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.64001f,mp4a.40.2", "streamUrl": "http://cfd-v4-service-channel-stitcher-use1-1.prd.pluto.tv/stitch/hls/channel/5e9f2c05172a0f0007db4786/master.m3u8?appName=web&appVersion=unknown&clientTime=0&deviceDNT=0&deviceId=6c27b8f5-30d3-11ef-9cf5-e9ddff8ff496&deviceMake=Chrome&deviceModel=web&deviceType=web&deviceVersion=unknown&includeExtendedEvents=false&serverSideAds=false&sid=3a15356b-8467-4ae7-b2f6-9c8467fcf41d"}, {"id": "ESPNews.us", "name": "ESPNews", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.640028,mp4a.40.2", "streamUrl": "https://fl3.moveonjoy.com/ESPN_NEWS/index.m3u8"}, {"id": "ESPNU.us", "name": "ESPNU", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.640028,mp4a.40.2", "streamUrl": "https://fl3.moveonjoy.com/ESPN_U/index.m3u8"}, {"id": "FanDuelSportsNetwork.us", "name": "FanDuel Sports Network", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.4d4020,mp4a.40.2", "streamUrl": "https://fl3.moveonjoy.com/PAC_12/index.m3u8"}, {"id": "FanDuelTV.us", "name": "FanDuel TV", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.4d401f,mp4a.40.2", "streamUrl": "https://fl3.moveonjoy.com/TVG/index.m3u8"}, {"id": "FIFAPlus.pl", "name": "FIFA+", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.640029,mp4a.40.2", "streamUrl": "https://a62dad94.wurl.com/master/f36d25e7e52f1ba8d7e56eb859c636563214f541/UmFrdXRlblRWLWV1X0ZJRkFQbHVzRW5nbGlzaF9ITFM/playlist.m3u8"}, {"id": "FITE247.us", "name": "FITE 24/7", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.640029,mp4a.40.2", "streamUrl": "https://d3d85c7qkywguj.cloudfront.net/scheduler/scheduleMaster/263.m3u8"}, {"id": "Football.ru", "name": "Football", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.4d0020,mp4a.40.2", "streamUrl": "http://31.148.48.15/Futbol_HD/index.m3u8"}, {"id": "FoxDeportes.us", "name": "Fox Deportes", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.640028,mp4a.40.2", "streamUrl": "https://apollo.production-public.tubi.io/live/fox-sports-espanol.m3u8"}, {"id": "FuboSportsNetwork.us", "name": "Fubo Sports Network", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.64001f,mp4a.40.2", "streamUrl": "https://apollo.production-public.tubi.io/live/ac-fubo.m3u8"}, {"id": "FUELTV.at", "name": "FUEL TV", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.4d4028,mp4a.40.2", "streamUrl": "https://amg01074-fueltv-fueltvemeaen-rakuten-b6j62.amagi.tv/hls/amagi_hls_data_rakutenAA-fueltvemeaen/CDN/master.m3u8"}, {"id": "GamePlus.ca", "name": "Game+", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.4d401f,mp4a.40.2", "streamUrl": "https://a-cdn.klowdtv.com/live2/fntsy_720p/playlist.m3u8"}, {"id": "GloryKickboxing.us", "name": "Glory Kickboxing", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.640029,mp4a.40.2", "streamUrl": "https://6f972d29.wurl.com/master/f36d25e7e52f1ba8d7e56eb859c636563214f541/UmFrdXRlblRWLWV1X0dsb3J5S2lja2JveGluZ19ITFM/playlist.m3u8"}, {"id": "HorseTV.it", "name": "Horse TV", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.4d401f,mp4a.40.2", "streamUrl": "https://a-cdn.klowdtv.com/live2/horsetv_720p/playlist.m3u8"}, {"id": "InsightTV.nl", "name": "Insight TV", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.77.30,mp4a.40.2", "streamUrl": "https://insighttv-samsungau.amagi.tv/playlist.m3u8"}, {"id": "InterTV.it", "name": "Inter TV", "error": "Incompatible codecs", "status": "potentially_working", "codec": "mp4a.40.2,avc1.4D401F", "streamUrl": "https://ilglobotv-live.akamaized.net/channels/InterTV/Live.m3u8"}, {"id": "IRIB3.ir", "name": "IRIB 3", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.4d4028,mp4a.40.2", "streamUrl": "https://lenz.splus.ir/PLTV/88888888/224/3221226868/index.m3u8"}, {"id": "ITVDeportes.mx", "name": "ITV Deportes", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.64001f,mp4a.40.2", "streamUrl": "https://thm-it-roku.otteravision.com/thm/it/it.m3u8"}, {"id": "K19.at", "name": "K19", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.640028,mp4a.40.2", "streamUrl": "https://1853185335.rsc.cdn77.org/K192/tv/playlist.m3u8"}, {"id": "KCMNLD6.us", "name": "KCMN-LD6", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.4d4028,mp4a.40.2", "streamUrl": "https://cdn-uw2-prod.tsv2.amagi.tv/linear/amg02873-kravemedia-mtrspt1-distrotv/playlist.m3u8"}, {"id": "KHLPrime.ru", "name": "KHL Prime", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.4d0020,mp4a.40.2", "streamUrl": "http://194.143.148.28:8080/KHL_HD/index.m3u8"}, {"id": "KozoomTV.fr", "name": "Kozoom TV", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.4d401e,mp4a.40.2", "streamUrl": "https://streams2.sofast.tv/v1/master/611d79b11b77e2f571934fd80ca1413453772ac7/fdd6f243-f971-4a1a-9510-97ac01d6b37f/manifest.m3u8"}, {"id": "KSASports1.sa", "name": "KSA Sports 1", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.4d401f,mp4a.40.2", "streamUrl": "https://live20.bozztv.com/gin-36bay3/ga-ksaports1/index.m3u8"}, {"id": "LacrosseTV.us", "name": "Lacrosse TV", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.64001f, mp4a.40.2", "streamUrl": "https://1840769862.rsc.cdn77.org/FTF/LSN_SCTE.m3u8"}, {"id": "MadeinBOTV.it", "name": "MadeinBO TV", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.640028,mp4a.40.2", "streamUrl": "https://srvx1.selftv.video/dmchannel/live/playlist.m3u8"}, {"id": "MatchArena.ru", "name": "Match! Arena", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.4d001e,mp4a.40.2", "streamUrl": "http://194.143.148.28:8080/MatchArena/index.m3u8"}, {"id": "MAVTV.us", "name": "MAVTV", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.640020,mp4a.40.2", "streamUrl": "https://fl3.moveonjoy.com/MAV_TV/index.m3u8"}, {"id": "MAVTVSelect.us", "name": "MAVTV Select", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.4d4028,mp4a.40.2", "streamUrl": "https://d3h07n6l1exhds.cloudfront.net/v1/master/3722c60a815c199d9c0ef36c5b73da68a62b09d1/cc-0z2yyo4dxctc7/playlist.m3u8"}, {"id": "MLB.us", "name": "MLB", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.64001f,mp4a.40.2", "streamUrl": "http://cfd-v4-service-channel-stitcher-use1-1.prd.pluto.tv/stitch/hls/channel/5e66968a70f34c0007d050be/master.m3u8?appName=web&appVersion=unknown&clientTime=0&deviceDNT=0&deviceId=6c29dbd3-30d3-11ef-9cf5-e9ddff8ff496&deviceMake=Chrome&deviceModel=web&deviceType=web&deviceVersion=unknown&includeExtendedEvents=false&serverSideAds=false&sid=0f1d3baf-2f1b-47d7-9966-3e92a98f1c8a"}, {"id": "MLBNetwork.us", "name": "MLB Network", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.640020,mp4a.40.2", "streamUrl": "https://fl3.moveonjoy.com/MLB_NETWORK/index.m3u8"}, {"id": "MMATVcom.ru", "name": "MMA-TV.com", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.4d4028,mp4a.40.2", "streamUrl": "https://streams2.sofast.tv/vglive-sk-462904/playlist.m3u8"}, {"id": "MoreThanSportsTV.de", "name": "More Than Sports TV", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.4d4028,mp4a.40.2", "streamUrl": "https://mts1.iptv-playoutcenter.de/mts/mts-web/playlist.m3u8"}, {"id": "MSG.us", "name": "MSG", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.640020,mp4a.40.2", "streamUrl": "https://fl3.moveonjoy.com/MSG/index.m3u8"}, {"id": "NauticalChannel.it", "name": "Nautical Channel", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.4d401f,mp4a.40.2", "streamUrl": "https://a-cdn.klowdtv.com/live2/nautical_720p/playlist.m3u8"}, {"id": "NFLChannel.us", "name": "NFL Channel", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.64001f,mp4a.40.2", "streamUrl": "http://cfd-v4-service-channel-stitcher-use1-1.prd.pluto.tv/stitch/hls/channel/5ced7d5df64be98e07ed47b6/master.m3u8?appName=web&appVersion=unknown&clientTime=0&deviceDNT=0&deviceId=6c2a5105-30d3-11ef-9cf5-e9ddff8ff496&deviceMake=Chrome&deviceModel=web&deviceType=web&deviceVersion=unknown&includeExtendedEvents=false&serverSideAds=false&sid=6477ef85-3680-442c-94d8-27197903b1f6"}, {"id": "NFLNetwork.us", "name": "NFL Network", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.640028,mp4a.40.2", "streamUrl": "https://fl3.moveonjoy.com/NFL_NETWORK/index.m3u8"}, {"id": "NFLRedZone.us", "name": "NFL RedZone", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.640028,mp4a.40.2", "streamUrl": "https://fl1.moveonjoy.com/NFL_RedZone/index.m3u8"}, {"id": "NHLNetwork.us", "name": "NHL Network", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.640028,mp4a.40.2", "streamUrl": "https://nhl-firetv.amagi.tv/playlist.m3u8"}, {"id": "NitroCircus.us", "name": "Nitro Circus", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.640028,mp4a.40.2", "streamUrl": "https://amg13231-actve-amg13231c1-rakuten-us-5604.playouts.now.amagi.tv/playlist.m3u8"}, {"id": "ONFootball.vn", "name": "ON Football", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.42c01f,mp4a.40.2", "streamUrl": "http://dvrfl05.bozztv.com/vch_vchannel11/index.m3u8"}, {"id": "PBRRidePass.us", "name": "PBR RidePass", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.64001f,mp4a.40.2", "streamUrl": "http://cfd-v4-service-channel-stitcher-use1-1.prd.pluto.tv/stitch/hls/channel/60d39387706fe50007fda8e8/master.m3u8?appName=web&appVersion=unknown&clientTime=0&deviceDNT=0&deviceId=6c2a9f21-30d3-11ef-9cf5-e9ddff8ff496&deviceMake=Chrome&deviceModel=web&deviceType=web&deviceVersion=unknown&includeExtendedEvents=false&serverSideAds=false&sid=f7275d4e-aa8e-4e8d-9d5e-6a5665bf8190"}, {"id": "PGATour.us", "name": "PGA Tour", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.64001f,mp4a.40.2", "streamUrl": "http://cfd-v4-service-channel-stitcher-use1-1.prd.pluto.tv/stitch/hls/channel/5de94dacb394a300099fa22a/master.m3u8?appName=web&appVersion=unknown&clientTime=0&deviceDNT=0&deviceId=6c2ac630-30d3-11ef-9cf5-e9ddff8ff496&deviceMake=Chrome&deviceModel=web&deviceType=web&deviceVersion=unknown&includeExtendedEvents=false&serverSideAds=false&sid=6c2b7359-0375-4f34-996b-4fb9429ead78"}, {"id": "PokerNightInAmerica.us", "name": "Poker Night In America", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.640029,mp4a.40.2", "streamUrl": "https://d2njbreu8qyfxo.cloudfront.net/scheduler/scheduleMaster/216.m3u8"}, {"id": "PokerGo.us", "name": "PokerGo", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.64001f,mp4a.40.2", "streamUrl": "http://cfd-v4-service-channel-stitcher-use1-1.prd.pluto.tv/stitch/hls/channel/5fc54366b04b2300072e31af/master.m3u8?appName=web&appVersion=unknown&clientTime=0&deviceDNT=0&deviceId=6c2b6274-30d3-11ef-9cf5-e9ddff8ff496&deviceMake=Chrome&deviceModel=web&deviceType=web&deviceVersion=unknown&includeExtendedEvents=false&serverSideAds=false&sid=a68d07ef-a794-43f8-a287-3bd7f27d909b"}, {"id": "RacingAmerica.us", "name": "Racing America", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.640028,mp4a.40.2", "streamUrl": "https://livetv-fa.tubi.video/racing-america/playlist.m3u8"}, {"id": "RealMadridTV.es", "name": "Real Madrid TV", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.640033,mp4a.40.5", "streamUrl": "https://rmtv.akamaized.net/hls/live/2043153/rmtv-es-web/master.m3u8"}, {"id": "RedBullTV.at", "name": "Red Bull TV", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.64001f,mp4a.40.2", "streamUrl": "https://3ea22335.wurl.com/master/f36d25e7e52f1ba8d7e56eb859c636563214f541/UmFrdXRlblRWLWdiX1JlZEJ1bGxUVl9ITFM/playlist.m3u8"}, {"id": "RightNowTV.us", "name": "Right Now TV", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.64001f,mp4a.40.2", "streamUrl": "https://a-cdn.klowdtv.com/live2/rightnowtv_720p/playlist.m3u8"}, {"id": "RTILa3.ci", "name": "RTI La 3", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.640029,mp4a.40.2", "streamUrl": "http://69.64.57.208/la3/index.m3u8"}, {"id": "SharjahSports.ae", "name": "Sharjah Sports", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.4d401f,mp4a.40.2", "streamUrl": "https://svs.itworkscdn.net/smc4sportslive/smc4.smil/playlist.m3u8"}, {"id": "SOSKanalPlus.rs", "name": "SOS Kanal Plus", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.4d001f,mp4a.40.2", "streamUrl": "https://53be5ef2d13aa.streamlock.net/soskanalplus/soskanalplus.stream/playlist.m3u8"}, {"id": "SportsGrid.us", "name": "SportsGrid", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.64001e,mp4a.40.2", "streamUrl": "https://sportsgrid-tribal.amagi.tv/playlist.m3u8"}, {"id": "SportsmanChannel.us", "name": "Sportsman Channel", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.640020,mp4a.40.2", "streamUrl": "https://fl3.moveonjoy.com/SPORTSMAN_CHANNEL/index.m3u8"}, {"id": "SportsNetNewYork.us", "name": "SportsNet New York", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.640028,mp4a.40.2", "streamUrl": "https://fl3.moveonjoy.com/SNY/index.m3u8"}, {"id": "SSCActionWaleed.sa", "name": "SSC Action Waleed", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.4D400B,mp4a.40.2", "streamUrl": "https://shls-live-event2-prod-dub.shahid.net/out/v1/0456ede1a39145d98b3d8c8062ddc998/index.m3u8"}, {"id": "StarSports1Tamil.in", "name": "Star Sports 1 Tamil", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.42c028,mp4a.40.2", "streamUrl": "https://live20.bozztv.com/akamaissh101/ssh101/vboxsungosttamil/playlist.m3u8"}, {"id": "SwerveSports.us", "name": "Swerve Sports", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.640028,mp4a.40.2", "streamUrl": "https://linear-253.frequency.stream/dist/glewedtv/253/hls/master/playlist.m3u8"}, {"id": "talkSPORT.uk", "name": "talkSPORT", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.64001f, mp4a.40.2", "streamUrl": "https://af7a8b4e.wurl.com/master/f36d25e7e52f1ba8d7e56eb859c636563214f541/TEctZ2JfdGFsa1NQT1JUX0hMUw/playlist.m3u8"}, {"id": "TDMSports.mo", "name": "TDM Sports", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.64002a,mp4a.40.2", "streamUrl": "https://live3.tdm.com.mo/ch4/sport_ch4.live/playlist.m3u8"}, {"id": "TeletrakTV.cl", "name": "Teletrak TV", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.42001f,mp4a.40.2", "streamUrl": "https://unlimited6-cl.dps.live/sportinghd/sportinghd.smil/playlist.m3u8"}, {"id": "TelewebionSport.ir", "name": "Telewebion Sport", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.4d4015,mp4a.40.2", "streamUrl": "https://ncdn.telewebion.com/twsport/live/playlist.m3u8"}, {"id": "TelewebionSport2.ir", "name": "Telewebion Sport 2", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.4d4015,mp4a.40.2", "streamUrl": "https://ncdn.telewebion.com/twsport2/live/playlist.m3u8"}, {"id": "TelewebionSport3.ir", "name": "Telewebion Sport 3", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.4d4015,mp4a.40.2", "streamUrl": "https://ncdn.telewebion.com/twsport3/live/playlist.m3u8"}, {"id": "TennisChannel.us", "name": "Tennis Channel", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.4d4028,mp4a.40.2", "streamUrl": "https://cdn-ue1-prod.tsv2.amagi.tv/linear/amg01444-tennischannelth-tennischannelnl-samsungnl/playlist.m3u8"}, {"id": "TRSport.it", "name": "TR Sport", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.77.30,mp4a.40.2", "streamUrl": "https://livetr.teleromagna.it/mia/live/playlist.m3u8"}, {"id": "TraceSportStars.fr", "name": "Trace Sport Stars", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.4d4028,mp4a.40.2", "streamUrl": "https://lightning-tracesport-samsungau.amagi.tv/playlist.m3u8"}, {"id": "TSNTheOcho.ca", "name": "TSN The Ocho", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.4d4028,mp4a.40.2", "streamUrl": "https://d3pnbvng3bx2nj.cloudfront.net/v1/master/3722c60a815c199d9c0ef36c5b73da68a62b09d1/cc-rds8g35qfqrnv/TSN_The_Ocho.m3u8"}, {"id": "TSN1.ca", "name": "TSN1", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.640020,mp4a.40.2", "streamUrl": "https://fl5.moveonjoy.com/TSN_1/index.m3u8"}, {"id": "TSN2.ca", "name": "TSN2", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.4d401f,mp4a.40.2", "streamUrl": "https://fl5.moveonjoy.com/TSN_2/index.m3u8"}, {"id": "TSN3.ca", "name": "TSN3", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.4d4020,mp4a.40.2", "streamUrl": "https://fl5.moveonjoy.com/TSN_3/index.m3u8"}, {"id": "TSN5.ca", "name": "TSN5", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.640020,mp4a.40.2", "streamUrl": "https://fl5.moveonjoy.com/TSN_5/index.m3u8"}, {"id": "TVRISport.id", "name": "TVRI Sport", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.4d4015,mp4a.40.2", "streamUrl": "https://ott-balancer.tvri.go.id/live/eds/SportHD/hls/SportHD.m3u8"}, {"id": "Unbeaten.us", "name": "Unbeaten", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.4d401e,mp4a.40.2", "streamUrl": "https://unbeaten-tcl.amagi.tv/playlist.m3u8"}, {"id": "W14DKD5.us", "name": "W14DK-D5", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.100.40,mp4a.40.2", "streamUrl": "https://2-fss-2.streamhoster.com/pl_118/204972-2205186-1/playlist.m3u8"}, {"id": "WorldofFreesports.de", "name": "World of Freesports", "error": "Incompatible codecs", "status": "potentially_working", "codec": "avc1.4d401e,mp4a.40.2", "streamUrl": "https://mainstreammedia-worldoffreesportsintl-rakuten.amagi.tv/playlist.m3u8"}]}