// Execute este código no console quando estiver em uma página de player
// Exemplo: http://localhost:3000/watch/iptv-Belarus5.by

console.log('=== VERIFICANDO PLAYER ===');

// 1. Buscar logs do player
const logs = window.__logger.getHistory('player');
console.log('\n📺 Total de logs do player:', logs.length);

// 2. Mostrar logs importantes
const importantLogs = logs.filter(log => 
  log.message.includes('mounted') || 
  log.message.includes('state updated') ||
  log.message.includes('Play button') ||
  log.message.includes('ready') ||
  log.message.includes('HLS') ||
  log.message.includes('error')
);

console.log('\n🔍 Logs importantes:');
importantLogs.forEach(log => {
  console.log(`[${log.level}] ${log.message}`);
  if (log.data) {
    console.log('  Data:', log.data);
  }
});

// 3. Verificar elementos de vídeo
const videos = document.querySelectorAll('video');
console.log('\n🎥 Elementos de vídeo encontrados:', videos.length);

videos.forEach((video, index) => {
  console.log(`\nVideo ${index + 1}:`);
  console.log('  src:', video.src || 'Nenhum');
  console.log('  currentSrc:', video.currentSrc || 'Nenhum');
  console.log('  readyState:', video.readyState);
  console.log('  networkState:', video.networkState);
  console.log('  paused:', video.paused);
  console.log('  duration:', video.duration);
  console.log('  error:', video.error);
});

// 4. Verificar se HLS.js está presente
console.log('\n📦 HLS.js disponível?', typeof Hls !== 'undefined');

// 5. Tentar encontrar instância do ReactPlayer
const reactPlayerWrapper = document.querySelector('div[style*="width: 100%; height: 100%"]');
console.log('\n⚛️ ReactPlayer wrapper encontrado?', !!reactPlayerWrapper);

// 6. Sugestões
console.log('\n💡 AÇÕES SUGERIDAS:');
console.log('1. Se não há logs do player, recarregue a página');
console.log('2. Clique no botão de play (se visível)');
console.log('3. Verifique novos logs com: window.__logger.getHistory("player").slice(-5)');
console.log('4. Se o vídeo não tocar, tente outro canal conhecido:');
console.log('   window.location.href = "/watch/iptv-SanMarinoRTVSport.sm"');