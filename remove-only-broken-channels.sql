-- Remover apenas canais quebrados após testes completos
-- Gerado em: 2025-07-26T09:06:35.179Z
-- Total: 134 canais

BEGIN;

DELETE FROM channels WHERE external_id = 'AbuDhabiSports1.ae';
DELETE FROM channels WHERE external_id = 'AbuDhabiSports2.ae';
DELETE FROM channels WHERE external_id = 'ACCNetwork.us';
DELETE FROM channels WHERE external_id = 'ACISportTV.it';
DELETE FROM channels WHERE external_id = 'ActionSports.us';
DELETE FROM channels WHERE external_id = 'AdventureSportsTV.us';
DELETE FROM channels WHERE external_id = 'Afizzionados.mx';
DELETE FROM channels WHERE external_id = 'ArenaSport1.ba';
DELETE FROM channels WHERE external_id = 'ATG.se';
DELETE FROM channels WHERE external_id = 'AwapaSportsTV.sv';
DELETE FROM channels WHERE external_id = 'BarcaTV.es';
DELETE FROM channels WHERE external_id = 'beINSportsHaber.tr';
DELETE FROM channels WHERE external_id = 'beINSPORTSXTRA.us';
DELETE FROM channels WHERE external_id = 'BEKSports.us';
DELETE FROM channels WHERE external_id = 'BlueSport2.ch';
DELETE FROM channels WHERE external_id = 'CampusLore.us';
DELETE FROM channels WHERE external_id = 'CanalPlusSport2.cz';
DELETE FROM channels WHERE external_id = 'CCTV5.cn';
DELETE FROM channels WHERE external_id = 'CCTVBilliards.cn';
DELETE FROM channels WHERE external_id = 'CCTVStormFootball.cn';
DELETE FROM channels WHERE external_id = 'CDNDeportes.do';
DELETE FROM channels WHERE external_id = 'ChampionTV.co';
DELETE FROM channels WHERE external_id = 'ColimdoTV.do';
DELETE FROM channels WHERE external_id = 'Combate.br';
DELETE FROM channels WHERE external_id = 'CRTVSportsandEntertainment.cm';
DELETE FROM channels WHERE external_id = 'DAZNCombat.uk';
DELETE FROM channels WHERE external_id = 'DongNaiTV2.vn';
DELETE FROM channels WHERE external_id = 'DuniaGamesTV.id';
DELETE FROM channels WHERE external_id = 'EDGEsport.uk';
DELETE FROM channels WHERE external_id = 'eGGNetwork.my';
DELETE FROM channels WHERE external_id = 'ElevenSports2.pl';
DELETE FROM channels WHERE external_id = 'ElevenSports3.pl';
DELETE FROM channels WHERE external_id = 'Equidia.fr';
DELETE FROM channels WHERE external_id = 'ERTSports1.gr';
DELETE FROM channels WHERE external_id = 'ERTSports2.gr';
DELETE FROM channels WHERE external_id = 'ESR24x7eSportsNetwork.us';
DELETE FROM channels WHERE external_id = 'FastFunBox.nl';
DELETE FROM channels WHERE external_id = 'FBTV.tr';
DELETE FROM channels WHERE external_id = 'Fight.us';
DELETE FROM channels WHERE external_id = 'FightNetwork.ca';
DELETE FROM channels WHERE external_id = 'FightNight.za';
DELETE FROM channels WHERE external_id = 'Fite.us';
DELETE FROM channels WHERE external_id = 'FLSport.gy';
DELETE FROM channels WHERE external_id = 'FoxSports.mx';
DELETE FROM channels WHERE external_id = 'FoxSports2.us';
DELETE FROM channels WHERE external_id = 'Futbol.tj';
DELETE FROM channels WHERE external_id = 'FUTV.cr';
DELETE FROM channels WHERE external_id = 'GameChannel.cn';
DELETE FROM channels WHERE external_id = 'GolfChannel.cz';
DELETE FROM channels WHERE external_id = 'GolfChannel.us';
DELETE FROM channels WHERE external_id = 'GolfNetwork.jp';
DELETE FROM channels WHERE external_id = 'HipodromodelasAmericas.mx';
DELETE FROM channels WHERE external_id = 'HTSporTV.tr';
DELETE FROM channels WHERE external_id = 'ImpactPlus.us';
DELETE FROM channels WHERE external_id = 'IMPACTWrestling.us';
DELETE FROM channels WHERE external_id = 'JiangsuSportsLeisureChannel.cn';
DELETE FROM channels WHERE external_id = 'JOJSport.sk';
DELETE FROM channels WHERE external_id = 'JordanSport.jo';
DELETE FROM channels WHERE external_id = 'JordanSport2.jo';
DELETE FROM channels WHERE external_id = 'KTVSport.kw';
DELETE FROM channels WHERE external_id = 'KTVSportPlus.kw';
DELETE FROM channels WHERE external_id = 'LEquipe.fr';
DELETE FROM channels WHERE external_id = 'Loversenykozvetites.hu';
DELETE FROM channels WHERE external_id = 'LuchaLibreAAA.us';
DELETE FROM channels WHERE external_id = 'MNetSport.mk';
DELETE FROM channels WHERE external_id = 'M4Sport.hu';
DELETE FROM channels WHERE external_id = 'Match4.hu';
DELETE FROM channels WHERE external_id = 'MNBSport.mn';
DELETE FROM channels WHERE external_id = 'MonterricoTV.pe';
DELETE FROM channels WHERE external_id = 'MSGPlus.us';
DELETE FROM channels WHERE external_id = 'MultivisionSports.gt';
DELETE FROM channels WHERE external_id = 'NBATV.us';
DELETE FROM channels WHERE external_id = 'NBATVCanada.ca';
DELETE FROM channels WHERE external_id = 'NBCSportsBayArea.us';
DELETE FROM channels WHERE external_id = 'NBCSportsBoston.us';
DELETE FROM channels WHERE external_id = 'NBCSportsPhiladelphia.us';
DELETE FROM channels WHERE external_id = 'NHRATV.us';
DELETE FROM channels WHERE external_id = 'NTVSpor.tr';
DELETE FROM channels WHERE external_id = 'OmanSportsTV.om';
DELETE FROM channels WHERE external_id = 'OMONOIATV.cy';
DELETE FROM channels WHERE external_id = 'OneGolf.pk';
DELETE FROM channels WHERE external_id = 'OvacionTV.pe';
DELETE FROM channels WHERE external_id = 'Pac12Insider.us';
DELETE FROM channels WHERE external_id = 'PlutoTVDeportes.us';
DELETE FROM channels WHERE external_id = 'PlutoTVExtreme.us';
DELETE FROM channels WHERE external_id = 'PlutoTVSport.us';
DELETE FROM channels WHERE external_id = 'PlutoTVSports.us';
DELETE FROM channels WHERE external_id = 'PTVSports.pk';
DELETE FROM channels WHERE external_id = 'QazSport.kz';
DELETE FROM channels WHERE external_id = 'Racingcom.au';
DELETE FROM channels WHERE external_id = 'RadioTurrialbaTVSports.cr';
DELETE FROM channels WHERE external_id = 'RaiSport.it';
DELETE FROM channels WHERE external_id = 'RealitateaSportiva.ro';
DELETE FROM channels WHERE external_id = 'RTASport.af';
DELETE FROM channels WHERE external_id = 'SanMarinoRTVSport.sm';
DELETE FROM channels WHERE external_id = 'SKITV.ch';
DELETE FROM channels WHERE external_id = 'SkySport1.nz';
DELETE FROM channels WHERE external_id = 'SkySport2.nz';
DELETE FROM channels WHERE external_id = 'SkySport3.nz';
DELETE FROM channels WHERE external_id = 'SkySport4.nz';
DELETE FROM channels WHERE external_id = 'SkySport5.nz';
DELETE FROM channels WHERE external_id = 'SkySport6.nz';
DELETE FROM channels WHERE external_id = 'SkySport7.nz';
DELETE FROM channels WHERE external_id = 'SMGFootballChannel.cn';
DELETE FROM channels WHERE external_id = 'SonySportsTen5.in';
DELETE FROM channels WHERE external_id = 'SportenFrance.fr';
DELETE FROM channels WHERE external_id = 'Sportitalia.it';
DELETE FROM channels WHERE external_id = 'Sportitalia24.it';
DELETE FROM channels WHERE external_id = 'SportitaliaMotori.it';
DELETE FROM channels WHERE external_id = 'SportitaliaSolocalcio.it';
DELETE FROM channels WHERE external_id = 'SportsConnect.za';
DELETE FROM channels WHERE external_id = 'SportsTV.tr';
DELETE FROM channels WHERE external_id = 'Stadium.us';
DELETE FROM channels WHERE external_id = 'StarSports1.in';
DELETE FROM channels WHERE external_id = 'Strongman.us';
DELETE FROM channels WHERE external_id = 'SuperSportRugby.za';
DELETE FROM channels WHERE external_id = 'TSports7.th';
DELETE FROM channels WHERE external_id = 'Teledeporte.es';
DELETE FROM channels WHERE external_id = 'TenSportsPakistan.pk';
DELETE FROM channels WHERE external_id = 'TigoSports.cr';
DELETE FROM channels WHERE external_id = 'TigoSports.gt';
DELETE FROM channels WHERE external_id = 'TRTSpor.tr';
DELETE FROM channels WHERE external_id = 'TSN4.ca';
DELETE FROM channels WHERE external_id = 'TurfMovil.cl';
DELETE FROM channels WHERE external_id = 'TurkmenistanSport.tm';
DELETE FROM channels WHERE external_id = 'TVMaticFight.br';
DELETE FROM channels WHERE external_id = 'TVQSports.gt';
DELETE FROM channels WHERE external_id = 'UDTV.es';
DELETE FROM channels WHERE external_id = 'UnbeatenEsports.us';
DELETE FROM channels WHERE external_id = 'VSportVinter.se';
DELETE FROM channels WHERE external_id = 'VarzeshTV.ir';
DELETE FROM channels WHERE external_id = 'vijuPlusSport.ru';
DELETE FROM channels WHERE external_id = 'VinxTV.es';
DELETE FROM channels WHERE external_id = 'ZoyTVSports1.bo';

COMMIT;