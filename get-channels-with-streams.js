const fs = require('fs');

async function getChannelsWithStreams() {
  console.log('Obtendo canais com streams disponíveis...');
  
  try {
    // Obter todos os streams esportivos
    const response = await fetch('http://localhost:3000/api/iptv-org/streams?limit=5000');
    const data = await response.json();
    const streams = data.data || [];
    
    console.log(`Total de streams encontrados: ${streams.length}`);
    
    // Criar mapa de canais que têm streams
    const channelsWithStreams = {};
    
    streams.forEach(stream => {
      const channelId = stream.channel || stream.uniqueId;
      if (!channelsWithStreams[channelId]) {
        channelsWithStreams[channelId] = {
          id: channelId,
          name: stream.name,
          streams: []
        };
      }
      channelsWithStreams[channelId].streams.push({
        url: stream.url,
        quality: stream.quality || 'unknown'
      });
    });
    
    // Carregar nossa lista de canais para testar
    const channelsToTest = JSON.parse(fs.readFileSync('channels-to-test.json', 'utf8'));
    
    // Canais que já estão funcionando (informados pelo usuário)
    const workingChannelNames = [
      '30A Golg Kigdom',
      'ACC Digital Network',
      'Adjarasport1',
      'ADO TV',
      'Africa 24 Sport',
      'Afro Sport Nigeria',
      'antena sport',
      'Arryadia',
      'Astrahan.Ru sport',
      'Bahrain Sports 1',
      'Bahrain Sports 2',
      'Belarus-5',
      'Bellator MMA',
      'Billiard TV',
      'Canal do Inter',
      'CBS Sports Network USA',
      'CT Sport',
      'Dong Nai TV2',
      'Dubai Racing',
      'Dubai Racing 2',
      'Dubai Racing 3',
      'Dubai Sports 1',
      'Dubai Sports 2',
      'Dubai Sports 3',
      'Eleven Sports 1'
    ];
    
    // Filtrar canais que:
    // 1. Têm streams disponíveis
    // 2. Não estão na lista de canais já funcionando
    const testableChannels = [];
    const noStreamChannels = [];
    
    channelsToTest.forEach(channel => {
      const hasStream = channelsWithStreams[channel.id];
      const isWorking = workingChannelNames.some(name => 
        channel.name.toLowerCase().includes(name.toLowerCase()) ||
        name.toLowerCase().includes(channel.name.toLowerCase())
      );
      
      if (!isWorking) {
        if (hasStream) {
          testableChannels.push({
            ...channel,
            streams: hasStream.streams
          });
        } else {
          noStreamChannels.push(channel);
        }
      }
    });
    
    console.log(`\nResumo:`);
    console.log(`- Canais já funcionando (ignorados): ${workingChannelNames.length}`);
    console.log(`- Canais com streams para testar: ${testableChannels.length}`);
    console.log(`- Canais sem streams (para remover): ${noStreamChannels.length}`);
    
    // Salvar resultados
    fs.writeFileSync('testable-channels.json', JSON.stringify(testableChannels, null, 2));
    fs.writeFileSync('no-stream-channels.json', JSON.stringify(noStreamChannels, null, 2));
    
    // Criar lista simplificada dos canais sem stream para remover
    const channelsToRemoveIds = noStreamChannels.map(ch => ch.id);
    fs.writeFileSync('channels-to-remove-no-stream.json', JSON.stringify(channelsToRemoveIds, null, 2));
    
    console.log('\nArquivos criados:');
    console.log('- testable-channels.json (canais com streams para testar)');
    console.log('- no-stream-channels.json (canais sem streams)');
    console.log('- channels-to-remove-no-stream.json (IDs para remover)');
    
    // Mostrar alguns exemplos
    console.log('\nExemplos de canais para testar:');
    testableChannels.slice(0, 5).forEach((ch, idx) => {
      console.log(`${idx + 1}. ${ch.name} (${ch.id}) - ${ch.streams.length} streams`);
    });
    
  } catch (error) {
    console.error('Erro:', error);
  }
}

getChannelsWithStreams();