{"total": 87, "reason": "Codecs são suportados, erro pode ser corrigível", "channels": [{"id": "30AGolfKingdom.us", "name": "30A Golf Kingdom"}, {"id": "Adjarasport1.ge", "name": "Adjarasport 1"}, {"id": "AfroSportNigeria.ng", "name": "AfroSport Nigeria"}, {"id": "AlkassFive.qa", "name": "Alkass Five"}, {"id": "AlkassFour.qa", "name": "Alkass Four"}, {"id": "AlkassOne.qa", "name": "Alkass One"}, {"id": "AlkassSHOOF.qa", "name": "Alkass SHOOF"}, {"id": "AlkassSix.qa", "name": "Alkass Six"}, {"id": "AlkassThree.qa", "name": "Alkass Three"}, {"id": "AlkassTwo.qa", "name": "Alkass Two"}, {"id": "AntenaSport.ro", "name": "AntenaSport"}, {"id": "CBSSportsGolazoNetwork.us", "name": "CBS Sports Golazo Network"}, {"id": "CBSSportsHQ.us", "name": "CBS Sports HQ"}, {"id": "ESPNews.us", "name": "ESPNews"}, {"id": "ESPNU.us", "name": "ESPNU"}, {"id": "FanDuelSportsNetwork.us", "name": "FanDuel Sports Network"}, {"id": "FanDuelTV.us", "name": "FanDuel TV"}, {"id": "FIFAPlus.pl", "name": "FIFA+"}, {"id": "FITE247.us", "name": "FITE 24/7"}, {"id": "Football.ru", "name": "Football"}, {"id": "FoxDeportes.us", "name": "Fox Deportes"}, {"id": "FuboSportsNetwork.us", "name": "Fubo Sports Network"}, {"id": "FUELTV.at", "name": "FUEL TV"}, {"id": "GamePlus.ca", "name": "Game+"}, {"id": "GloryKickboxing.us", "name": "Glory Kickboxing"}, {"id": "HorseTV.it", "name": "Horse TV"}, {"id": "InsightTV.nl", "name": "Insight TV"}, {"id": "InterTV.it", "name": "Inter TV"}, {"id": "IRIB3.ir", "name": "IRIB 3"}, {"id": "ITVDeportes.mx", "name": "ITV Deportes"}, {"id": "K19.at", "name": "K19"}, {"id": "KCMNLD6.us", "name": "KCMN-LD6"}, {"id": "KHLPrime.ru", "name": "KHL Prime"}, {"id": "KozoomTV.fr", "name": "Kozoom TV"}, {"id": "KSASports1.sa", "name": "KSA Sports 1"}, {"id": "LacrosseTV.us", "name": "Lacrosse TV"}, {"id": "MadeinBOTV.it", "name": "MadeinBO TV"}, {"id": "MatchArena.ru", "name": "Match! Arena"}, {"id": "MAVTV.us", "name": "MAVTV"}, {"id": "MAVTVSelect.us", "name": "MAVTV Select"}, {"id": "MLB.us", "name": "MLB"}, {"id": "MLBNetwork.us", "name": "MLB Network"}, {"id": "MMATVcom.ru", "name": "MMA-TV.com"}, {"id": "MoreThanSportsTV.de", "name": "More Than Sports TV"}, {"id": "MSG.us", "name": "MSG"}, {"id": "NauticalChannel.it", "name": "Nautical Channel"}, {"id": "NFLChannel.us", "name": "NFL Channel"}, {"id": "NFLNetwork.us", "name": "NFL Network"}, {"id": "NFLRedZone.us", "name": "NFL RedZone"}, {"id": "NHLNetwork.us", "name": "NHL Network"}, {"id": "NitroCircus.us", "name": "Nitro Circus"}, {"id": "ONFootball.vn", "name": "ON Football"}, {"id": "PBRRidePass.us", "name": "PBR RidePass"}, {"id": "PGATour.us", "name": "PGA Tour"}, {"id": "PokerNightInAmerica.us", "name": "Poker Night In America"}, {"id": "PokerGo.us", "name": "PokerGo"}, {"id": "RacingAmerica.us", "name": "Racing America"}, {"id": "RealMadridTV.es", "name": "Real Madrid TV"}, {"id": "RedBullTV.at", "name": "Red Bull TV"}, {"id": "RightNowTV.us", "name": "Right Now TV"}, {"id": "RTILa3.ci", "name": "RTI La 3"}, {"id": "SharjahSports.ae", "name": "Sharjah Sports"}, {"id": "SOSKanalPlus.rs", "name": "SOS Kanal Plus"}, {"id": "SportsGrid.us", "name": "SportsGrid"}, {"id": "SportsmanChannel.us", "name": "Sportsman Channel"}, {"id": "SportsNetNewYork.us", "name": "SportsNet New York"}, {"id": "SSCActionWaleed.sa", "name": "SSC Action Waleed"}, {"id": "StarSports1Tamil.in", "name": "Star Sports 1 Tamil"}, {"id": "SwerveSports.us", "name": "Swerve Sports"}, {"id": "talkSPORT.uk", "name": "talkSPORT"}, {"id": "TDMSports.mo", "name": "TDM Sports"}, {"id": "TeletrakTV.cl", "name": "Teletrak TV"}, {"id": "TelewebionSport.ir", "name": "Telewebion Sport"}, {"id": "TelewebionSport2.ir", "name": "Telewebion Sport 2"}, {"id": "TelewebionSport3.ir", "name": "Telewebion Sport 3"}, {"id": "TennisChannel.us", "name": "Tennis Channel"}, {"id": "TRSport.it", "name": "TR Sport"}, {"id": "TraceSportStars.fr", "name": "Trace Sport Stars"}, {"id": "TSNTheOcho.ca", "name": "TSN The Ocho"}, {"id": "TSN1.ca", "name": "TSN1"}, {"id": "TSN2.ca", "name": "TSN2"}, {"id": "TSN3.ca", "name": "TSN3"}, {"id": "TSN5.ca", "name": "TSN5"}, {"id": "TVRISport.id", "name": "TVRI Sport"}, {"id": "Unbeaten.us", "name": "Unbeaten"}, {"id": "W14DKD5.us", "name": "W14DK-D5"}, {"id": "WorldofFreesports.de", "name": "World of Freesports"}]}