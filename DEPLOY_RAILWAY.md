# 🚀 Deploy StreamPlus España no Railway

## 📋 Pré-requisitos

1. Conta no [Railway](https://railway.app)
2. Banco de dados Supabase configurado
3. Repositório no GitHub

## 🔧 Passo 1: Configurar Banco de Dados no Supabase

### Opção A: Via SQL Editor (Recomendado)

1. <PERSON><PERSON> o [Supabase Dashboard](https://supabase.com/dashboard)
2. Vá em **SQL Editor**
3. Cole e execute o seguinte SQL:

```sql
-- Copie todo o conteúdo do arquivo:
-- supabase/migrations/001_initial_schema_simplified.sql
```

### Opção B: Via Terminal (Avançado)

Se você tem acesso direto ao banco:

```bash
psql "postgresql://postgres:<EMAIL>:5432/postgres" -f supabase/migrations/001_initial_schema_simplified.sql
```

## 🔑 Passo 2: Configurar Variáveis de Ambiente no Railway

1. No Railway, vá em **Variables**
2. <PERSON> as variáveis do arquivo `.env.railway`
3. **IMPORTANTE**: Atualize estas variáveis com seus valores reais:
   - `JWT_SECRET` - Gere uma chave segura (mínimo 32 caracteres)
   - `NEXT_PUBLIC_STREAM_URL` - URL do seu stream principal
   - `NEXT_PUBLIC_FALLBACK_STREAM_URL` - URL do stream de backup
   - `SPORTS_API_KEY` - Sua chave da API-Sports (se tiver)

### Gerando JWT_SECRET seguro:

```bash
# No terminal, execute:
openssl rand -base64 32
```

## 🚂 Passo 3: Deploy no Railway

### Via GitHub (Recomendado)

1. Faça commit e push do código:
```bash
git add .
git commit -m "Preparar para deploy no Railway"
git push origin main
```

2. No Railway:
   - Clique em **New Project**
   - Selecione **Deploy from GitHub repo**
   - Escolha o repositório `streamplus-espana`
   - Railway detectará automaticamente que é um projeto Next.js

### Via Railway CLI

```bash
# Instalar Railway CLI
npm install -g @railway/cli

# Login
railway login

# Deploy
railway up
```

## ✅ Passo 4: Verificar Deploy

Após o deploy:

1. **Teste o Health Check**:
   ```
   https://seu-app.railway.app/api/health
   ```

2. **Verifique as APIs de Esportes**:
   ```
   https://seu-app.railway.app/api/sports/api-status
   ```

3. **Acesse a aplicação**:
   ```
   https://seu-app.railway.app
   ```

## 🔍 Troubleshooting

### Erro: "Database connection failed"
- Verifique as variáveis SUPABASE_URL e SUPABASE_SERVICE_ROLE_KEY
- Confirme que as tabelas foram criadas no Supabase

### Erro: "Build failed"
- Verifique os logs no Railway
- Certifique-se que todas as dependências estão no package.json
- Execute `pnpm build` localmente para testar

### Erro: "Sports API not working"
- Verifique se as API keys estão corretas
- Confirme os limites de rate das APIs
- Use `/api/sports/reset-quotas` se necessário

## 📊 Monitoramento

### Logs em Tempo Real
```bash
railway logs
```

### Métricas
- CPU e memória no dashboard do Railway
- Logs de API em `/api/sports/rotation-status`

## 🎯 Próximos Passos

1. **Configurar Domínio Customizado**:
   - Em Settings > Domains no Railway
   - Adicione seu domínio personalizado

2. **Configurar SSL**:
   - Railway fornece SSL automaticamente

3. **Configurar Backups**:
   - Configure backups automáticos no Supabase

4. **Monitoramento**:
   - Configure alertas no Railway
   - Integre com ferramentas de monitoramento

## 📝 Variáveis de Ambiente Completas

```env
# Supabase (já configuradas)
NEXT_PUBLIC_SUPABASE_URL=https://bgeecdlhnwkgdujoyont.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJnZWVjZGxobndrZ2R1am95b250Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI5MDAxMDEsImV4cCI6MjA2ODQ3NjEwMX0.LxkXyK3TFhbV1zKaG3Hny3NcGq-tGRfHnJkCvjFEP_s
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJnZWVjZGxobndrZ2R1am95b250Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MjkwMDEwMSwiZXhwIjoyMDY4NDc2MTAxfQ.0bS2U7LgWgop_pdD4XYLqQ_V0SefmUq0y1XXdpZwbic

# App URLs
NEXT_PUBLIC_APP_URL=https://streamplus-espana.up.railway.app
NEXT_PUBLIC_ANDROID_APP_URL=https://play.google.com/store/apps/details?id=com.streamplus
NEXT_PUBLIC_IOS_APP_URL=https://apps.apple.com/app/streamplus/id123456789

# Streaming (ATUALIZE COM SUAS URLs)
NEXT_PUBLIC_STREAM_URL=https://demo-stream.com/default.m3u8
NEXT_PUBLIC_FALLBACK_STREAM_URL=https://demo-stream.com/fallback.m3u8

# Auth (GERE UMA CHAVE SEGURA)
JWT_SECRET=gere-uma-chave-super-secreta-com-openssl-rand-base64-32

# Stack Auth (OPCIONAL - se usar Stack Auth)
NEXT_PUBLIC_STACK_PROJECT_ID=seu-stack-project-id
NEXT_PUBLIC_STACK_PUBLISHABLE_CLIENT_KEY=sua-stack-publishable-key
STACK_SECRET_SERVER_KEY=sua-stack-secret-key

# Sports APIs
SPORTS_API_KEY=sua-api-sports-key
ALLSPORTDB_API_KEY=20d8be22-0948-4131-ae61-37dd981028bd

# Node
NODE_ENV=production
```

## 🎉 Pronto!

Seu StreamPlus España está pronto para deploy! Após seguir todos os passos, você terá:

- ✅ Plataforma de streaming funcionando
- ✅ Sistema de trial de 5 minutos
- ✅ Integração com 12 APIs de esportes
- ✅ AllSportsDB para eventos ao vivo
- ✅ Sistema de autenticação completo
- ✅ Deep linking para apps móveis

Para suporte ou dúvidas, consulte a documentação em `/docs`.