const fs = require('fs');
const path = require('path');

console.log('🧹 Iniciando remoção de canais quebrados...\n');

// 1. Carregar canais sem streams (1915 canais)
const noStreamChannels = JSON.parse(fs.readFileSync('no-stream-channels.json', 'utf8'));
console.log(`📊 Canais sem streams para remover: ${noStreamChannels.length}`);

// 2. Carregar resultado dos testes (se existir)
let brokenChannels = [];
if (fs.existsSync('iptv-test-report.json')) {
  const testReport = JSON.parse(fs.readFileSync('iptv-test-report.json', 'utf8'));
  brokenChannels = testReport.channels.broken || [];
  console.log(`📊 Canais quebrados nos testes: ${brokenChannels.length}`);
}

// 3. Criar lista única de IDs para remover
const channelsToRemove = new Set();

// Adicionar canais sem streams
noStreamChannels.forEach(channel => {
  channelsToRemove.add(channel.id);
});

// Adicionar canais quebrados
brokenChannels.forEach(channel => {
  channelsToRemove.add(channel.id);
});

console.log(`\n🎯 Total de canais únicos para remover: ${channelsToRemove.size}`);

// 4. Criar arquivo SQL para remover canais do banco de dados
const sqlCommands = [];
sqlCommands.push('-- Script para remover canais quebrados do banco de dados');
sqlCommands.push('-- Gerado em: ' + new Date().toISOString());
sqlCommands.push('');
sqlCommands.push('BEGIN;');
sqlCommands.push('');

// Deletar canais da tabela channels
sqlCommands.push('-- Remover canais da tabela channels');
channelsToRemove.forEach(channelId => {
  sqlCommands.push(`DELETE FROM channels WHERE external_id = '${channelId}';`);
});

sqlCommands.push('');
sqlCommands.push('COMMIT;');
sqlCommands.push('');
sqlCommands.push(`-- Total de canais removidos: ${channelsToRemove.size}`);

// Salvar arquivo SQL
fs.writeFileSync('remove-broken-channels.sql', sqlCommands.join('\n'));
console.log('✅ Arquivo SQL criado: remove-broken-channels.sql');

// 5. Criar lista de canais funcionando que devem ser mantidos
console.log('\n📋 Verificando canais que devem ser mantidos...');

// Canais informados como funcionando pelo usuário
const userWorkingChannels = [
  '30A Golg Kigdom',
  'ACC Digital Network',
  'Adjarasport1',
  'ADO TV',
  'Africa 24 Sport',
  'Afro Sport Nigeria',
  'antena sport',
  'Arryadia',
  'Astrahan.Ru sport',
  'Bahrain Sports 1',
  'Bahrain Sports 2',
  'Belarus-5',
  'Bellator MMA',
  'Billiard TV',
  'Canal do Inter',
  'CBS Sports Network USA',
  'CT Sport',
  'Dong Nai TV2',
  'Dubai Racing',
  'Dubai Racing 2',
  'Dubai Racing 3',
  'Dubai Sports 1',
  'Dubai Sports 2',
  'Dubai Sports 3',
  'Eleven Sports 1'
];

// 6. Criar arquivo de canais para manter/adicionar
const channelsToKeep = {
  userReported: userWorkingChannels,
  tested: [],
  total: userWorkingChannels.length
};

fs.writeFileSync('channels-to-keep.json', JSON.stringify(channelsToKeep, null, 2));
console.log('✅ Arquivo de canais para manter criado: channels-to-keep.json');

// 7. Criar resumo final
const summary = {
  timestamp: new Date().toISOString(),
  statistics: {
    totalSportsChannels: 2198,
    channelsWithoutStreams: noStreamChannels.length,
    channelsBrokenInTests: brokenChannels.length,
    totalToRemove: channelsToRemove.size,
    channelsToKeep: channelsToKeep.total
  },
  actions: {
    sqlFileCreated: 'remove-broken-channels.sql',
    keepFileCreated: 'channels-to-keep.json'
  },
  nextSteps: [
    '1. Execute o arquivo SQL no banco de dados Supabase',
    '2. Adicione os canais da lista channels-to-keep.json ao sistema',
    '3. Teste os canais mantidos para garantir funcionamento',
    '4. Implemente correções para canais com erros corrigíveis'
  ]
};

fs.writeFileSync('channel-cleanup-summary.json', JSON.stringify(summary, null, 2));

console.log('\n📊 RESUMO DA LIMPEZA:');
console.log(`- Canais sem streams: ${noStreamChannels.length}`);
console.log(`- Canais quebrados: ${brokenChannels.length}`);
console.log(`- Total para remover: ${channelsToRemove.size}`);
console.log(`- Canais para manter: ${channelsToKeep.total}`);

console.log('\n✅ Processo concluído!');
console.log('\n📝 Próximos passos:');
summary.nextSteps.forEach((step, idx) => {
  console.log(`   ${step}`);
});

// 8. Criar script para adicionar canais funcionando
const addWorkingChannelsScript = `
// Script para adicionar canais funcionando ao sistema
const workingChannels = ${JSON.stringify(userWorkingChannels, null, 2)};

// TODO: Implementar lógica para adicionar estes canais ao sistema
// 1. Buscar informações completas de cada canal
// 2. Verificar se já existem no banco
// 3. Adicionar os que não existem
// 4. Atualizar os que já existem

console.log('Canais para adicionar/verificar:', workingChannels.length);
`;

fs.writeFileSync('add-working-channels.js', addWorkingChannelsScript);
console.log('\n✅ Script para adicionar canais funcionando criado: add-working-channels.js');