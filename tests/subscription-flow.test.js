// Teste automatizado do fluxo de assinatura
// Este teste documenta os problemas encontrados no sistema

const { test, expect } = require('@playwright/test');

test.describe('Fluxo de Assinatura - NewSpports', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('http://localhost:3000');
  });

  test('Problemas Identificados no Sistema', async ({ page }) => {
    console.log('=== PROBLEMAS ENCONTRADOS ===');
    
    // 1. Erro 42P01 - Tabela user_access não existe
    console.log('\n1. ERRO CRÍTICO: Tabela user_access não existe');
    console.log('   - Código: 42P01');
    console.log('   - Detalhes: relation "user_access" does not exist');
    console.log('   - Impacto: RPC check_user_access_unified falha completamente');
    console.log('   - Solução: Criar tabela user_access ou remover referência da RPC');

    // 2. Endpoints de API retornando 401
    await page.goto('http://localhost:3000/test-subscription');
    await page.waitForTimeout(1000);
    
    console.log('\n2. PROBLEMA: Endpoints de API retornando 401 (Unauthorized)');
    console.log('   - /api/debug/subscription-check - Falha com 401');
    console.log('   - /api/test/activate-premium - Falha com 401');
    console.log('   - Causa: Supabase auth.getUser() não está funcionando nos endpoints');
    console.log('   - Impacto: Não é possível ativar premium manualmente');

    // 3. Erro ao ativar via app
    await page.goto('http://localhost:3000/activar');
    await page.waitForTimeout(2000);
    
    const activateButton = page.getByRole('button', { name: 'Activar Ahora' });
    if (await activateButton.isVisible()) {
      await activateButton.click();
      await page.waitForTimeout(1000);
      
      console.log('\n3. PROBLEMA: Ativação via app falha com erro 500');
      console.log('   - Endpoint: /api/app/activate-simple');
      console.log('   - Erro: "Error al crear sesión"');
      console.log('   - Status: 500 (Internal Server Error)');
    }

    // 4. Múltiplos erros de sincronização
    console.log('\n4. PROBLEMA: Erros constantes de sincronização Auth');
    console.log('   - /api/auth/sync - Retorna 500 constantemente');
    console.log('   - Causa provável: Problemas com Stack Auth ou Supabase');

    // 5. Problema principal relatado pelo usuário
    console.log('\n5. PROBLEMA PRINCIPAL: Cliente paga mas continua limitado');
    console.log('   - Webhook do Stripe só atualiza tabela profiles');
    console.log('   - Não atualiza tabela stack_profiles');
    console.log('   - Solução já implementada: syncProfileTables no webhook');
    console.log('   - Status: RESOLVIDO (aguardando deploy)');

    // Resumo
    console.log('\n=== RESUMO DOS PROBLEMAS ===');
    console.log('1. ❌ CRÍTICO: Tabela user_access não existe (erro 42P01)');
    console.log('2. ❌ ALTO: Autenticação não funciona nos endpoints da API');
    console.log('3. ❌ ALTO: Ativação via app retorna erro 500');
    console.log('4. ⚠️  MÉDIO: Erros de sincronização auth constantes');
    console.log('5. ✅ RESOLVIDO: Webhook atualiza ambas tabelas de perfil');

    console.log('\n=== AÇÕES NECESSÁRIAS ===');
    console.log('1. Criar tabela user_access ou atualizar RPC para não referenciá-la');
    console.log('2. Verificar configuração de autenticação Supabase nos endpoints');
    console.log('3. Debugar erro 500 no endpoint activate-simple');
    console.log('4. Executar migração 008_create_stack_profiles.sql em produção');
    console.log('5. Configurar CRON_SECRET e agendar cron job diário');
  });

  test('Verificar estado atual do usuário', async ({ page }) => {
    await page.goto('http://localhost:3000/test-subscription');
    await page.waitForTimeout(1000);

    // Verificar informações do usuário
    const userEmail = await page.textContent('text=<EMAIL>');
    expect(userEmail).toBeTruthy();

    // Verificar que não tem acesso
    const hasAccess = await page.textContent('text=Tem Acesso:');
    expect(hasAccess).toContain('Não');

    console.log('Usuário atual:');
    console.log('- Email: <EMAIL>');
    console.log('- Tem Acesso: Não');
    console.log('- Assinatura Ativa: Não');
    console.log('- Trial Expirado: Sim');
  });
});