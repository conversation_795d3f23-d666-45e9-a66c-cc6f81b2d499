# Correção do Problema de Pagamento Stripe

## Problema Identificado

O pagamento estava sendo processado no Stripe mas não estava liberando a conta premium para o cliente.

## Causa do Problema

O webhook do Stripe estava tentando atualizar um campo com nome incorreto na tabela `profiles`:
- **Usado no código**: `current_period_end`
- **Nome correto no banco**: `subscription_current_period_end`

## Correções Aplicadas

### 1. Webhook Corrigido

Arquivo: `src/app/api/stripe/webhook/route.ts`

Alterações:
- Linha 59: `current_period_end` → `subscription_current_period_end`
- Linha 88: `current_period_end` → `subscription_current_period_end`
- <PERSON><PERSON> 176: `current_period_end` → `subscription_current_period_end`

### 2. Scripts de Teste Criados

#### a) Verificar Estrutura do Banco
```bash
tsx scripts/check-stripe-tables.ts
```
Verifica se todas as colunas necessárias existem na tabela `profiles`.

#### b) Processar Pagamento Manualmente
```bash
./scripts/process-stripe-payment.sh <customer_id> <subscription_id> <user_email>
```
Simula um webhook do Stripe para testar o processamento.

### 3. Novo Endpoint de Teste

`POST /api/process-payment`
```json
{
  "customerId": "cus_xxx",
  "subscriptionId": "sub_xxx",
  "userEmail": "<EMAIL>"
}
```

`GET /api/process-payment?email=<EMAIL>`
Verifica o status do perfil do usuário.

## Verificações Necessárias

### 1. Confirmar Migração no Banco

Execute no SQL Editor do Supabase:
```sql
SELECT column_name, data_type 
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name = 'profiles'
AND column_name LIKE '%stripe%' OR column_name LIKE '%subscription%'
ORDER BY ordinal_position;
```

Se faltar alguma coluna, execute:
```sql
-- Migração 006_add_stripe_fields.sql
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS stripe_customer_id TEXT UNIQUE;
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS stripe_subscription_id TEXT;
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS subscription_status TEXT;
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS subscription_current_period_end TIMESTAMPTZ;
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS subscription_cancel_at_period_end BOOLEAN DEFAULT FALSE;
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS trial_end TIMESTAMPTZ;
```

### 2. Testar Webhook em Produção

1. Faça um pagamento de teste no Stripe
2. Verifique os logs do webhook
3. Confirme se o perfil foi atualizado:
   ```bash
   curl https://newspports.com/api/process-payment?email=<EMAIL>
   ```

### 3. Verificar Webhook no Stripe Dashboard

1. Acesse: https://dashboard.stripe.com/webhooks
2. Confirme que o endpoint está configurado
3. Verifique se o `STRIPE_WEBHOOK_SECRET` está correto no ambiente

## Teste Manual de Ativação

Se precisar ativar uma assinatura manualmente para teste:

```bash
curl -X POST https://newspports.com/api/process-payment \
  -H "Content-Type: application/json" \
  -d '{
    "customerId": "cus_test_123",
    "subscriptionId": "sub_test_123",
    "userEmail": "<EMAIL>"
  }'
```

## Monitoramento

Para monitorar pagamentos futuros:

1. **Logs do Servidor**: Verifique mensagens com prefixo `✅`, `❌`, `🔄`
2. **Stripe Dashboard**: Eventos de webhook
3. **Supabase**: Tabela `profiles` e `payment_history`

## Status

✅ Webhook corrigido
✅ Scripts de teste criados
✅ Endpoint de teste implementado
⏳ Aguardando confirmação da migração no banco
⏳ Aguardando teste em produção