-- ⚠️ EXECUTAR URGENTE NO SUPABASE SQL EDITOR ⚠️
-- ERRO CRÍTICO: A função RPC está com erro de ambiguidade
-- Isso está impedindo TODOS os usuários de acessar conteúdo premium!

-- 1. <PERSON><PERSON>, verificar se a Fernanda realmente tem premium
SELECT 
  id,
  email,
  subscription_tier,
  subscription_status,
  subscription_current_period_end,
  subscription_current_period_end > NOW() as is_valid
FROM stack_profiles 
WHERE email = '<EMAIL>';

-- 2. Corrigir a função RPC com erro de ambiguidade
DROP FUNCTION IF EXISTS check_user_access_unified(TEXT);

CREATE OR REPLACE FUNCTION check_user_access_unified(p_user_id TEXT)
RETURNS TABLE (
  has_access BOOLEAN,
  access_type VARCHAR,
  expires_at TIMESTAMP WITH TIME ZONE,
  subscription_tier VARCHAR
) AS $$
BEGIN
  -- Primeiro verificar na tabela stack_profiles (usu<PERSON><PERSON><PERSON> Auth)
  IF EXISTS (
    SELECT 1 
    FROM stack_profiles sp
    WHERE sp.id = p_user_id 
      AND sp.subscription_tier = 'premium'
      AND sp.subscription_status = 'active'
      AND sp.subscription_current_period_end > NOW()
  ) THEN
    RETURN QUERY
    SELECT 
      true AS has_access,
      'assinatura'::VARCHAR AS access_type,
      sp.subscription_current_period_end AS expires_at,
      sp.subscription_tier::VARCHAR AS subscription_tier
    FROM stack_profiles sp
    WHERE sp.id = p_user_id
    LIMIT 1;
    RETURN;
  END IF;

  -- Tentar converter para UUID e verificar na tabela profiles (Supabase Auth)
  BEGIN
    IF EXISTS (
      SELECT 1 
      FROM profiles p
      WHERE p.id = p_user_id::UUID 
        AND p.subscription_tier = 'premium'
        AND p.subscription_status = 'active'
        AND p.subscription_current_period_end > NOW()
    ) THEN
      RETURN QUERY
      SELECT 
        true AS has_access,
        'assinatura'::VARCHAR AS access_type,
        p.subscription_current_period_end AS expires_at,
        p.subscription_tier::VARCHAR AS subscription_tier
      FROM profiles p
      WHERE p.id = p_user_id::UUID
      LIMIT 1;
      RETURN;
    END IF;
  EXCEPTION
    WHEN invalid_text_representation THEN
      -- ID não é UUID válido, continuar
      NULL;
  END;

  -- Verificar na tabela user_access
  BEGIN
    IF EXISTS (
      SELECT 1 
      FROM user_access ua
      WHERE ua.user_id = p_user_id::UUID 
        AND ua.ativo_ate > NOW()
    ) THEN
      RETURN QUERY
      SELECT 
        true AS has_access,
        ua.tipo::VARCHAR AS access_type,
        ua.ativo_ate AS expires_at,
        'premium'::VARCHAR AS subscription_tier
      FROM user_access ua
      WHERE ua.user_id = p_user_id::UUID 
        AND ua.ativo_ate > NOW()
      ORDER BY ua.ativo_ate DESC
      LIMIT 1;
      RETURN;
    END IF;
  EXCEPTION
    WHEN invalid_text_representation THEN
      -- ID não é UUID válido, continuar
      NULL;
  END;

  -- Se não tem nenhum acesso
  RETURN QUERY
  SELECT 
    false AS has_access,
    NULL::VARCHAR AS access_type,
    NULL::TIMESTAMP WITH TIME ZONE AS expires_at,
    'free'::VARCHAR AS subscription_tier;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 3. Testar a função corrigida com a Fernanda
SELECT * FROM check_user_access_unified('f8367e51-b191-42bc-9b35-1f3ba2648985');

-- RESULTADO ESPERADO:
-- has_access | access_type | expires_at                   | subscription_tier
-- true       | assinatura  | 2025-08-28 05:11:46.035+00  | premium

-- 4. Se ainda não funcionar, executar update direto
-- UPDATE stack_profiles 
-- SET 
--   subscription_tier = 'premium',
--   subscription_status = 'active',
--   subscription_current_period_end = NOW() + INTERVAL '30 days'
-- WHERE email = '<EMAIL>';

-- APÓS EXECUTAR, PEDIR PARA A FERNANDA:
-- 1. Limpar o localStorage: localStorage.clear()
-- 2. Recarregar a página (Ctrl+F5)
-- 3. Fazer login novamente