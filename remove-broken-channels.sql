-- Script para remover canais de esportes que não funcionam
-- Total: 165 canais
-- Gerado em: 2025-07-26T09:28:16.960Z

BEGIN;


-- Removendo canais 1 a 50
DELETE FROM channels
WHERE id IN (
  'ACLCornholeTV.us',
  'AlfaSport.cy',
  'beINSPORTSXTRAenEspanol.us',
  'CBCSport.az',
  'Esport3.es',
  'FastSports.am',
  'GTVSportsPlus.gh',
  'HardKnocks.ca',
  'HTVSports.vn',
  'MUTV.uk',
  'OlympicChannel.es',
  'SSport2.tr',
  'ShandongTVSportsChannel.cn',
  'SkyRacing1.au',
  'SkyRacing2.au',
  'SkyThoroughbredCentral.au',
  'StartTriumf.ru',
  'TabiiSpor6.tr',
  'TigoSports.sv',
  'TJKTV.tr',
  'TJKTV2.tr',
  'TRTSporYildiz.tr',
  'TVRSport.ro',
  'TVSBowlingNetwork.us',
  'TVSBoxing.us',
  'TVSClassicSports.us',
  'TVSSports.us',
  'TVSSportsBureau.us',
  'TVSTurbo.us',
  'TVSWomenSports.us',
  'WorldPokerTour.us',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined'
);

-- Removendo canais 51 a 100
DELETE FROM channels
WHERE id IN (
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined'
);

-- Removendo canais 101 a 150
DELETE FROM channels
WHERE id IN (
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined'
);

-- Removendo canais 151 a 165
DELETE FROM channels
WHERE id IN (
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined',
  'undefined'
);

-- Verificar quantos canais foram removidos
SELECT COUNT(*) as canais_removidos 
FROM channels 
WHERE id IN ('ACLCornholeTV.us','AlfaSport.cy','beINSPORTSXTRAenEspanol.us','CBCSport.az','Esport3.es','FastSports.am','GTVSportsPlus.gh','HardKnocks.ca','HTVSports.vn','MUTV.uk','OlympicChannel.es','SSport2.tr','ShandongTVSportsChannel.cn','SkyRacing1.au','SkyRacing2.au','SkyThoroughbredCentral.au','StartTriumf.ru','TabiiSpor6.tr','TigoSports.sv','TJKTV.tr','TJKTV2.tr','TRTSporYildiz.tr','TVRSport.ro','TVSBowlingNetwork.us','TVSBoxing.us','TVSClassicSports.us','TVSSports.us','TVSSportsBureau.us','TVSTurbo.us','TVSWomenSports.us','WorldPokerTour.us','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined','undefined');

COMMIT;

-- Estatísticas após remoção
SELECT 
  COUNT(*) as total_canais_restantes,
  COUNT(CASE WHEN category = 'sports' THEN 1 END) as canais_esportes_restantes
FROM channels;
