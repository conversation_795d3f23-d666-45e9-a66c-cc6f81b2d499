[{"id": "30AGolfKingdom.us", "name": "30A Golf Kingdom", "url": "https://30a-tv.com/feeds/vidaa/golf.m3u8", "status": "working", "statusCode": 200, "needsProxy": false, "directAccess": true, "proxyAccess": true, "responseTime": 666, "suggestion": "Canal funcionando corretamente"}, {"id": "ACCDigitalNetwork.us", "name": "ACC Digital Network", "url": "https://raycom-accdn-firetv.amagi.tv/playlist.m3u8", "status": "working", "statusCode": 200, "needsProxy": false, "directAccess": true, "proxyAccess": true, "responseTime": 564, "suggestion": "Canal funcionando corretamente"}, {"id": "Adjarasport1.ge", "name": "Adjarasport 1", "url": "https://live20.bozztv.com/dvrfl05/gin-adjara/index.m3u8", "status": "working", "statusCode": 200, "needsProxy": false, "directAccess": true, "proxyAccess": true, "responseTime": 452, "suggestion": "Canal funcionando corretamente"}, {"id": "ADOTV.bj", "name": "ADO TV", "url": "https://strhls.streamakaci.tv/ortb/ortb2-multi/playlist.m3u8", "status": "working", "statusCode": 200, "needsProxy": false, "directAccess": true, "proxyAccess": true, "responseTime": 947, "suggestion": "Canal funcionando corretamente"}, {"id": "Africa24Sport.fr", "name": "Africa 24 Sport", "url": "https://africa24.vedge.infomaniak.com/livecast/ik:africa24sport/manifest.m3u8", "status": "working", "statusCode": 200, "needsProxy": false, "directAccess": true, "proxyAccess": true, "responseTime": 4556, "suggestion": "Canal funcionando corretamente"}, {"id": "AfroSportNigeria.ng", "name": "AfroSport Nigeria", "url": "https://newproxy3.vidivu.tv/vidivu_afrosport/index.m3u8", "status": "working", "statusCode": 200, "needsProxy": false, "directAccess": true, "proxyAccess": true, "responseTime": 992, "suggestion": "Canal funcionando corretamente"}, {"id": "AntenaSport.ro", "name": "AntenaSport", "url": "https://stream1.antenaplay.ro/as/asrolive1/playlist.m3u8", "status": "working", "statusCode": 200, "needsProxy": false, "directAccess": true, "proxyAccess": true, "responseTime": 1007, "suggestion": "Canal funcionando corretamente"}, {"id": "Arryadia.ma", "name": "<PERSON><PERSON><PERSON>", "url": "https://cdn.live.easybroadcast.io/abr_corp/73_arryadia_k2tgcj0/corp/73_arryadia_k2tgcj0_480p/chunks_dvr.m3u8", "status": "working", "statusCode": 200, "needsProxy": false, "directAccess": true, "proxyAccess": true, "responseTime": 730, "suggestion": "Canal funcionando corretamente"}, {"id": "AstrahanRuSport.ru", "name": "Astrahan.Ru Sport", "url": "https://streaming.astrakhan.ru/astrakhanrusporthd/playlist.m3u8", "status": "working", "statusCode": 200, "needsProxy": false, "directAccess": true, "proxyAccess": true, "responseTime": 2311, "suggestion": "Canal funcionando corretamente"}, {"id": "BahrainSports1.bh", "name": "Bahrain Sports 1", "url": "https://5c7b683162943.streamlock.net/live/ngrp:sportsone_all/playlist.m3u8", "status": "working", "statusCode": 200, "needsProxy": false, "directAccess": true, "proxyAccess": true, "responseTime": 1365, "suggestion": "Canal funcionando corretamente"}, {"id": "BahrainSports2.bh", "name": "Bahrain Sports 2", "url": "https://5c7b683162943.streamlock.net/live/ngrp:bahrainsportstwo_all/playlist.m3u8", "status": "working", "statusCode": 200, "needsProxy": false, "directAccess": true, "proxyAccess": true, "responseTime": 1271, "suggestion": "Canal funcionando corretamente"}, {"id": "Belarus5.by", "name": "Belarus-5", "url": "https://ngtrk.dc.beltelecom.by/ngtrk/smil:belarus5.smil/playlist.m3u8", "status": "working", "statusCode": 200, "needsProxy": false, "directAccess": true, "proxyAccess": true, "responseTime": 1327, "suggestion": "Canal funcionando corretamente"}, {"id": "BellatorMMA.us", "name": "Bellator MMA", "url": "http://cfd-v4-service-channel-stitcher-use1-1.prd.pluto.tv/stitch/hls/channel/5ebc8688f3697d00072f7cf8/master.m3u8?appName=web&appVersion=unknown&clientTime=0&deviceDNT=0&deviceId=6c26f5a6-30d3-11ef-9cf5-e9ddff8ff496&deviceMake=Chrome&deviceModel=web&deviceType=web&deviceVersion=unknown&includeExtendedEvents=false&serverSideAds=false&sid=0e97a69e-3355-4217-b6fb-8952f0ad1803", "status": "working", "statusCode": 200, "needsProxy": false, "directAccess": true, "proxyAccess": true, "responseTime": 402, "suggestion": "Canal funcionando corretamente"}, {"id": "BilliardTV.us", "name": "Billiard TV", "url": "https://1621590671.rsc.cdn77.org/HLS/BILLIARDTV.m3u8", "status": "working", "statusCode": 200, "needsProxy": false, "directAccess": true, "proxyAccess": true, "responseTime": 328, "suggestion": "Canal funcionando corretamente"}, {"id": "CanaldoInter.br", "name": "Canal do Inter", "url": "https://video01.soultv.com.br/internacional/internacional/playlist.m3u8", "status": "working", "statusCode": 200, "needsProxy": false, "directAccess": true, "proxyAccess": true, "responseTime": 125, "suggestion": "Canal funcionando corretamente"}, {"id": "CBSSportsNetworkUSA.us", "name": "CBS Sports Network USA", "url": "https://fl3.moveonjoy.com/CBS_SPORTS_NETWORK/index.m3u8", "status": "working", "statusCode": 200, "needsProxy": false, "directAccess": true, "proxyAccess": true, "responseTime": 462, "suggestion": "Canal funcionando corretamente"}, {"id": "CTSport.cz", "name": "CT Sport", "url": "http://88.212.15.19/live/test_ctsport_25p/playlist.m3u8", "status": "working", "statusCode": 200, "needsProxy": false, "directAccess": true, "proxyAccess": true, "responseTime": 408, "suggestion": "Canal funcionando corretamente"}, {"id": "DongNaiTV2.vn", "name": "Dong Nai TV 2", "url": "http://118.107.85.4:1935/live/smil:DNTV2.smil/playlist.m3u8", "status": "working", "statusCode": 200, "needsProxy": false, "directAccess": true, "proxyAccess": true, "responseTime": 695, "suggestion": "Canal funcionando corretamente"}, {"id": "DubaiRacing2.ae", "name": "Dubai Racing 2", "url": "https://dmithrvllta.cdn.mgmlcdn.com/dubairacing/smil:dubairacing.smil/chunklist.m3u8", "status": "working", "statusCode": 200, "needsProxy": false, "directAccess": true, "proxyAccess": true, "responseTime": 553, "suggestion": "Canal funcionando corretamente"}, {"id": "DubaiRacing3.ae", "name": "Dubai Racing 3", "url": "https://dmithrvllta.cdn.mgmlcdn.com/dubaimubasher/smil:dubaimubasher.smil/chunklist.m3u8", "status": "working", "statusCode": 200, "needsProxy": false, "directAccess": true, "proxyAccess": true, "responseTime": 686, "suggestion": "Canal funcionando corretamente"}, {"id": "DubaiRacing.ae", "name": "Dubai Racing", "url": "https://dmisdracta.cdn.mgmlcdn.com/events/smil:events.stream.smil/chunklist.m3u8", "status": "working", "statusCode": 200, "needsProxy": false, "directAccess": true, "proxyAccess": true, "responseTime": 1098, "suggestion": "Canal funcionando corretamente"}, {"id": "DubaiSports1.ae", "name": "Dubai Sports 1", "url": "https://dmidspta.cdn.mgmlcdn.com/dubaisports/smil:dubaisports.stream.smil/chunklist.m3u8", "status": "working", "statusCode": 200, "needsProxy": false, "directAccess": true, "proxyAccess": true, "responseTime": 210, "suggestion": "Canal funcionando corretamente"}, {"id": "DubaiSports2.ae", "name": "Dubai Sports 2", "url": "https://dmitwlvvll.cdn.mgmlcdn.com/dubaisportshd/smil:dubaisportshd.smil/chunklist.m3u8", "status": "working", "statusCode": 200, "needsProxy": false, "directAccess": true, "proxyAccess": true, "responseTime": 1008, "suggestion": "Canal funcionando corretamente"}, {"id": "DubaiSports3.ae", "name": "Dubai Sports 3", "url": "https://dmitwlvvll.cdn.mgmlcdn.com/dubaisportshd5/smil:dubaisportshd5.smil/chunklist.m3u8", "status": "working", "statusCode": 200, "needsProxy": false, "directAccess": true, "proxyAccess": true, "responseTime": 711, "suggestion": "Canal funcionando corretamente"}, {"id": "ElevenSports1.pl", "name": "Eleven Sports 1", "url": "http://9b129915.akadatel.com/iptv/83GA6FAV4DPTPQ/20068/index.m3u8", "status": "404", "statusCode": 404, "needsProxy": true, "directAccess": false, "proxyAccess": true, "responseTime": 903, "suggestion": "URL do canal não existe mais. Necessário encontrar URL alternativa ou remover canal."}, {"id": "ESPNews.us", "name": "ESPNews", "url": "https://fl3.moveonjoy.com/ESPN_NEWS/index.m3u8", "status": "working", "statusCode": 200, "needsProxy": false, "directAccess": true, "proxyAccess": true, "responseTime": 388, "suggestion": "Canal funcionando corretamente"}, {"id": "ESPNU.us", "name": "ESPNU", "url": "https://fl3.moveonjoy.com/ESPN_U/index.m3u8", "status": "working", "statusCode": 200, "needsProxy": false, "directAccess": true, "proxyAccess": true, "responseTime": 389, "suggestion": "Canal funcionando corretamente"}, {"id": "FanDuelSportsNetwork.us", "name": "FanDuel Sports Network", "url": "https://fl3.moveonjoy.com/PAC_12/index.m3u8", "status": "working", "statusCode": 200, "needsProxy": false, "directAccess": true, "proxyAccess": true, "responseTime": 396, "suggestion": "Canal funcionando corretamente"}, {"id": "FanDuelTV.us", "name": "FanDuel TV", "url": "https://fl3.moveonjoy.com/TVG/index.m3u8", "status": "working", "statusCode": 200, "needsProxy": false, "directAccess": true, "proxyAccess": true, "responseTime": 402, "suggestion": "Canal funcionando corretamente"}, {"id": "FIFAPlus.pl", "name": "FIFA+", "url": "https://a62dad94.wurl.com/master/f36d25e7e52f1ba8d7e56eb859c636563214f541/UmFrdXRlblRWLWV1X0ZJRkFQbHVzRW5nbGlzaF9ITFM/playlist.m3u8", "status": "404", "statusCode": 404, "needsProxy": true, "directAccess": false, "proxyAccess": true, "responseTime": 690, "suggestion": "URL do canal não existe mais. Necessário encontrar URL alternativa ou remover canal."}, {"id": "FITE247.us", "name": "FITE 24/7", "url": "https://d3d85c7qkywguj.cloudfront.net/scheduler/scheduleMaster/263.m3u8", "status": "404", "statusCode": 404, "needsProxy": true, "directAccess": false, "proxyAccess": true, "responseTime": 511, "suggestion": "URL do canal não existe mais. Necessário encontrar URL alternativa ou remover canal."}, {"id": "GloryKickboxing.us", "name": "Glory Kickboxing", "url": "https://6f972d29.wurl.com/master/f36d25e7e52f1ba8d7e56eb859c636563214f541/UmFrdXRlblRWLWV1X0dsb3J5S2lja2JveGluZ19ITFM/playlist.m3u8", "status": "404", "statusCode": 404, "needsProxy": true, "directAccess": false, "proxyAccess": true, "responseTime": 437, "suggestion": "URL do canal não existe mais. Necessário encontrar URL alternativa ou remover canal."}, {"id": "InsightTV.nl", "name": "Insight TV", "url": "https://insighttv-samsungau.amagi.tv/playlist.m3u8", "status": "working", "statusCode": 200, "needsProxy": false, "directAccess": true, "proxyAccess": true, "responseTime": 556, "suggestion": "Canal funcionando corretamente"}, {"id": "InterTV.it", "name": "Inter TV", "url": "https://ilglobotv-live.akamaized.net/channels/InterTV/Live.m3u8", "status": "404", "statusCode": 404, "needsProxy": true, "directAccess": false, "proxyAccess": true, "responseTime": 951, "suggestion": "URL do canal não existe mais. Necessário encontrar URL alternativa ou remover canal."}, {"id": "IRIB3.ir", "name": "IRIB 3", "url": "https://lenz.splus.ir/PLTV/88888888/224/3221226868/index.m3u8", "status": "other_error", "statusCode": 400, "needsProxy": true, "directAccess": false, "proxyAccess": true, "responseTime": 1569, "suggestion": "Erro desconhecido. Verificar logs para mais detalhes."}, {"id": "ITVDeportes.mx", "name": "ITV Deportes", "url": "https://thm-it-roku.otteravision.com/thm/it/it.m3u8", "status": "working", "statusCode": 200, "needsProxy": false, "directAccess": true, "proxyAccess": true, "responseTime": 323, "suggestion": "Canal funcionando corretamente"}, {"id": "K19.at", "name": "K19", "url": "https://1853185335.rsc.cdn77.org/K192/tv/playlist.m3u8", "status": "working", "statusCode": 200, "needsProxy": false, "directAccess": true, "proxyAccess": true, "responseTime": 829, "suggestion": "Canal funcionando corretamente"}, {"id": "KHLPrime.ru", "name": "KHL Prime", "url": "http://194.143.148.28:8080/KHL_HD/index.m3u8", "status": "working", "statusCode": 200, "needsProxy": false, "directAccess": true, "proxyAccess": true, "responseTime": 631, "suggestion": "Canal funcionando corretamente"}, {"id": "KSASports1.sa", "name": "KSA Sports 1", "url": "https://live20.bozztv.com/gin-36bay3/ga-ksaports1/index.m3u8", "status": "working", "statusCode": 200, "needsProxy": false, "directAccess": true, "proxyAccess": true, "responseTime": 799, "suggestion": "Canal funcionando corretamente"}, {"id": "LacrosseTV.us", "name": "Lacrosse TV", "url": "https://1840769862.rsc.cdn77.org/FTF/LSN_SCTE.m3u8", "status": "working", "statusCode": 200, "needsProxy": false, "directAccess": true, "proxyAccess": true, "responseTime": 643, "suggestion": "Canal funcionando corretamente"}, {"id": "MadeinBOTV.it", "name": "MadeinBO TV", "url": "https://srvx1.selftv.video/dmchannel/live/playlist.m3u8", "status": "working", "statusCode": 200, "needsProxy": false, "directAccess": true, "proxyAccess": true, "responseTime": 1065, "suggestion": "Canal funcionando corretamente"}, {"id": "MatchArena.ru", "name": "Match! Arena", "url": "http://194.143.148.28:8080/MatchArena/index.m3u8", "status": "working", "statusCode": 200, "needsProxy": false, "directAccess": true, "proxyAccess": true, "responseTime": 636, "suggestion": "Canal funcionando corretamente"}, {"id": "MAVTVSelect.us", "name": "MAVTV Select", "url": "https://d3h07n6l1exhds.cloudfront.net/v1/master/3722c60a815c199d9c0ef36c5b73da68a62b09d1/cc-0z2yyo4dxctc7/playlist.m3u8", "status": "404", "statusCode": 404, "needsProxy": true, "directAccess": false, "proxyAccess": true, "responseTime": 584, "suggestion": "URL do canal não existe mais. Necessário encontrar URL alternativa ou remover canal."}, {"id": "MLBNetwork.us", "name": "MLB Network", "url": "https://fl3.moveonjoy.com/MLB_NETWORK/index.m3u8", "status": "working", "statusCode": 200, "needsProxy": false, "directAccess": true, "proxyAccess": true, "responseTime": 442, "suggestion": "Canal funcionando corretamente"}, {"id": "MoreThanSportsTV.de", "name": "More Than Sports TV", "url": "https://mts1.iptv-playoutcenter.de/mts/mts-web/playlist.m3u8", "status": "working", "statusCode": 200, "needsProxy": false, "directAccess": true, "proxyAccess": true, "responseTime": 2458, "suggestion": "Canal funcionando corretamente"}, {"id": "MSG.us", "name": "MSG", "url": "https://fl3.moveonjoy.com/MSG/index.m3u8", "status": "working", "statusCode": 200, "needsProxy": false, "directAccess": true, "proxyAccess": true, "responseTime": 396, "suggestion": "Canal funcionando corretamente"}, {"id": "NFLNetwork.us", "name": "NFL Network", "url": "https://fl3.moveonjoy.com/NFL_NETWORK/index.m3u8", "status": "working", "statusCode": 200, "needsProxy": false, "directAccess": true, "proxyAccess": true, "responseTime": 390, "suggestion": "Canal funcionando corretamente"}, {"id": "NFLRedZone.us", "name": "NFL RedZone", "url": "https://fl1.moveonjoy.com/NFL_RedZone/index.m3u8", "status": "working", "statusCode": 200, "needsProxy": false, "directAccess": true, "proxyAccess": true, "responseTime": 602, "suggestion": "Canal funcionando corretamente"}, {"id": "NHLNetwork.us", "name": "NHL Network", "url": "https://nhl-firetv.amagi.tv/playlist.m3u8", "status": "working", "statusCode": 200, "needsProxy": false, "directAccess": true, "proxyAccess": true, "responseTime": 611, "suggestion": "Canal funcionando corretamente"}, {"id": "NitroCircus.us", "name": "Nitro Circus", "url": "https://amg13231-actve-amg13231c1-rakuten-us-5604.playouts.now.amagi.tv/playlist.m3u8", "status": "working", "statusCode": 200, "needsProxy": false, "directAccess": true, "proxyAccess": true, "responseTime": 472, "suggestion": "Canal funcionando corretamente"}, {"id": "ONFootball.vn", "name": "ON Football", "url": "http://dvrfl05.bozztv.com/vch_vchannel11/index.m3u8", "status": "working", "statusCode": 200, "needsProxy": false, "directAccess": true, "proxyAccess": true, "responseTime": 1521, "suggestion": "Canal funcionando corretamente"}, {"id": "RacingAmerica.us", "name": "Racing America", "url": "https://livetv-fa.tubi.video/racing-america/playlist.m3u8", "status": "working", "statusCode": 200, "needsProxy": false, "directAccess": true, "proxyAccess": true, "responseTime": 462, "suggestion": "Canal funcionando corretamente"}, {"id": "RedBullTV.at", "name": "Red Bull TV", "url": "https://3ea22335.wurl.com/master/f36d25e7e52f1ba8d7e56eb859c636563214f541/UmFrdXRlblRWLWdiX1JlZEJ1bGxUVl9ITFM/playlist.m3u8", "status": "404", "statusCode": 404, "needsProxy": true, "directAccess": false, "proxyAccess": true, "responseTime": 1615, "suggestion": "URL do canal não existe mais. Necessário encontrar URL alternativa ou remover canal."}, {"id": "SOSKanalPlus.rs", "name": "SOS Kanal Plus", "url": "https://53be5ef2d13aa.streamlock.net/soskanalplus/soskanalplus.stream/playlist.m3u8", "status": "working", "statusCode": 200, "needsProxy": false, "directAccess": true, "proxyAccess": true, "responseTime": 1382, "suggestion": "Canal funcionando corretamente"}, {"id": "SportsGrid.us", "name": "SportsGrid", "url": "https://sportsgrid-tribal.amagi.tv/playlist.m3u8", "status": "working", "statusCode": 200, "needsProxy": false, "directAccess": true, "proxyAccess": true, "responseTime": 457, "suggestion": "Canal funcionando corretamente"}, {"id": "SportsmanChannel.us", "name": "Sportsman Channel", "url": "https://fl3.moveonjoy.com/SPORTSMAN_CHANNEL/index.m3u8", "status": "working", "statusCode": 200, "needsProxy": false, "directAccess": true, "proxyAccess": true, "responseTime": 526, "suggestion": "Canal funcionando corretamente"}, {"id": "SportsNetNewYork.us", "name": "SportsNet New York", "url": "https://fl3.moveonjoy.com/SNY/index.m3u8", "status": "working", "statusCode": 200, "needsProxy": false, "directAccess": true, "proxyAccess": true, "responseTime": 405, "suggestion": "Canal funcionando corretamente"}, {"id": "StarSports1Tamil.in", "name": "Star Sports 1 Tamil", "url": "https://live20.bozztv.com/akamaissh101/ssh101/vboxsungosttamil/playlist.m3u8", "status": "working", "statusCode": 200, "needsProxy": false, "directAccess": true, "proxyAccess": true, "responseTime": 412, "suggestion": "Canal funcionando corretamente"}, {"id": "SwerveSports.us", "name": "Swerve Sports", "url": "https://linear-253.frequency.stream/dist/glewedtv/253/hls/master/playlist.m3u8", "status": "working", "statusCode": 200, "needsProxy": false, "directAccess": true, "proxyAccess": true, "responseTime": 435, "suggestion": "Canal funcionando corretamente"}, {"id": "talkSPORT.uk", "name": "talkSPORT", "url": "https://af7a8b4e.wurl.com/master/f36d25e7e52f1ba8d7e56eb859c636563214f541/TEctZ2JfdGFsa1NQT1JUX0hMUw/playlist.m3u8", "status": "404", "statusCode": 404, "needsProxy": true, "directAccess": false, "proxyAccess": true, "responseTime": 441, "suggestion": "URL do canal não existe mais. Necessário encontrar URL alternativa ou remover canal."}, {"id": "TDMSports.mo", "name": "TDM Sports", "url": "https://live3.tdm.com.mo/ch4/sport_ch4.live/playlist.m3u8", "status": "working", "statusCode": 200, "needsProxy": false, "directAccess": true, "proxyAccess": true, "responseTime": 4331, "suggestion": "Canal funcionando corretamente"}, {"id": "TraceSportStars.fr", "name": "Trace Sport Stars", "url": "https://lightning-tracesport-samsungau.amagi.tv/playlist.m3u8", "status": "working", "statusCode": 200, "needsProxy": false, "directAccess": true, "proxyAccess": true, "responseTime": 649, "suggestion": "Canal funcionando corretamente"}, {"id": "TRSport.it", "name": "TR Sport", "url": "https://livetr.teleromagna.it/mia/live/playlist.m3u8", "status": "working", "statusCode": 200, "needsProxy": false, "directAccess": true, "proxyAccess": true, "responseTime": 1400, "suggestion": "Canal funcionando corretamente"}, {"id": "TSN1.ca", "name": "TSN1", "url": "https://fl5.moveonjoy.com/TSN_1/index.m3u8", "status": "404", "statusCode": 404, "needsProxy": true, "directAccess": false, "proxyAccess": true, "responseTime": 522, "suggestion": "URL do canal não existe mais. Necessário encontrar URL alternativa ou remover canal."}, {"id": "TSN2.ca", "name": "TSN2", "url": "https://fl5.moveonjoy.com/TSN_2/index.m3u8", "status": "working", "statusCode": 200, "needsProxy": false, "directAccess": true, "proxyAccess": true, "responseTime": 392, "suggestion": "Canal funcionando corretamente"}, {"id": "TSN3.ca", "name": "TSN3", "url": "https://fl5.moveonjoy.com/TSN_3/index.m3u8", "status": "working", "statusCode": 200, "needsProxy": false, "directAccess": true, "proxyAccess": true, "responseTime": 431, "suggestion": "Canal funcionando corretamente"}, {"id": "TSN5.ca", "name": "TSN5", "url": "https://fl5.moveonjoy.com/TSN_5/index.m3u8", "status": "working", "statusCode": 200, "needsProxy": false, "directAccess": true, "proxyAccess": true, "responseTime": 394, "suggestion": "Canal funcionando corretamente"}, {"id": "TSNTheOcho.ca", "name": "TSN The Ocho", "url": "https://d3pnbvng3bx2nj.cloudfront.net/v1/master/3722c60a815c199d9c0ef36c5b73da68a62b09d1/cc-rds8g35qfqrnv/TSN_The_Ocho.m3u8", "status": "404", "statusCode": 404, "needsProxy": true, "directAccess": false, "proxyAccess": true, "responseTime": 614, "suggestion": "URL do canal não existe mais. Necessário encontrar URL alternativa ou remover canal."}]