// Script para debug da página HLS Test
// Execute no console quando estiver em /hls-test

console.clear();
console.log('🔍 DEBUG HLS TEST\n');

// 1. Verificar todos os vídeos na página
const videos = document.querySelectorAll('video');
console.log(`📺 Total de vídeos: ${videos.length}\n`);

videos.forEach((video, index) => {
    const container = video.closest('[class*="space-y-4"]');
    const title = container?.querySelector('h2')?.textContent || 'Sem título';
    const url = container?.querySelector('[class*="font-mono"]')?.textContent || 'Sem URL';
    
    console.log(`\n=== VÍDEO ${index + 1}: ${title} ===`);
    console.log(`URL original: ${url}`);
    console.log(`URL no player: ${video.src || 'VAZIO'}`);
    console.log(`Estado:`);
    console.log(`- readyState: ${video.readyState} (0=nada, 1=metadata, 2=dados atuais, 3=dados futuros, 4=suficiente)`);
    console.log(`- networkState: ${video.networkState} (0=vazio, 1=idle, 2=loading, 3=sem fonte)`);
    console.log(`- paused: ${video.paused}`);
    console.log(`- duration: ${video.duration || 'Desconhecida'}`);
    console.log(`- buffered: ${video.buffered.length > 0 ? video.buffered.end(0) : 0} segundos`);
    
    if (video.error) {
        console.error(`❌ ERRO: ${video.error.message} (código: ${video.error.code})`);
        console.log('Códigos de erro:');
        console.log('1 = MEDIA_ERR_ABORTED');
        console.log('2 = MEDIA_ERR_NETWORK');
        console.log('3 = MEDIA_ERR_DECODE');
        console.log('4 = MEDIA_ERR_SRC_NOT_SUPPORTED');
    } else {
        console.log('✅ Sem erros de vídeo');
    }
    
    // Verificar se está usando proxy
    if (video.src && video.src.includes('/api/proxy')) {
        console.log('🔄 Usando proxy para CORS');
        const proxyUrl = new URL(video.src, window.location.origin);
        const originalUrl = proxyUrl.searchParams.get('url');
        console.log(`URL decodificada: ${originalUrl}`);
    }
});

// 2. Verificar requisições na memória
console.log('\n\n📡 VERIFICANDO HLS.js:');
if (window.Hls) {
    console.log('✅ HLS.js está carregado');
    
    // Tentar acessar instâncias HLS através do React
    const hlsPlayers = [];
    document.querySelectorAll('[class*="rounded-lg"]').forEach(el => {
        const reactKey = Object.keys(el).find(key => key.startsWith('__react'));
        if (reactKey && el[reactKey]) {
            // Navegar pela árvore do React para encontrar HLS
            let node = el[reactKey];
            while (node) {
                if (node.memoizedProps?.hls || node.stateNode?.hls) {
                    hlsPlayers.push(node.memoizedProps?.hls || node.stateNode?.hls);
                    break;
                }
                node = node.child || node.sibling || node.return;
            }
        }
    });
    
    console.log(`Instâncias HLS encontradas: ${hlsPlayers.length}`);
} else {
    console.log('❌ HLS.js não está carregado');
}

// 3. Testar manualmente o segundo vídeo
console.log('\n\n🧪 TESTE MANUAL DO SEGUNDO VÍDEO:');
if (videos.length > 1) {
    const video = videos[1];
    const container = video.closest('[class*="space-y-4"]');
    const url = container?.querySelector('[class*="font-mono"]')?.textContent;
    
    console.log('Tentando reproduzir manualmente...');
    console.log('URL:', url);
    
    // Tentar play
    video.play().then(() => {
        console.log('✅ Play iniciado com sucesso!');
    }).catch(err => {
        console.error('❌ Erro ao dar play:', err.message);
        console.log('Possíveis causas:');
        console.log('- Stream offline ou com geoblocking');
        console.log('- URL expirada');
        console.log('- Formato não suportado');
        console.log('- Problema de CORS não resolvido pelo proxy');
    });
}

// 4. Sugestões
console.log('\n\n💡 AÇÕES SUGERIDAS:');
console.log('1. Para testar um stream específico que não funciona:');
console.log('   - Copie a URL do stream');
console.log('   - Teste em outro player online como: https://www.hlsplayer.net/');
console.log('2. Se funcionar em outro player mas não aqui:');
console.log('   - Pode ser problema de headers específicos');
console.log('   - O stream pode requerer autenticação');
console.log('3. Tente clicar no botão play de cada vídeo manualmente');
console.log('4. Verifique se há bloqueadores de anúncios ou extensões interferindo');

// 5. Verificar se há algum stream funcionando
const workingVideos = Array.from(videos).filter(v => v.readyState >= 3 && !v.error);
console.log(`\n\n✅ Vídeos funcionando: ${workingVideos.length} de ${videos.length}`);