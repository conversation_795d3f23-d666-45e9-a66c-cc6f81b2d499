-- Sistema de segurança para link fixo
-- 1. Tabela de fingerprints de dispositivos
CREATE TABLE device_fingerprints (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  fingerprint TEXT NOT NULL UNIQUE,
  device_info JSONB NOT NULL,
  first_seen TIM<PERSON><PERSON><PERSON> WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  last_seen TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  total_activations INTEGER DEFAULT 0,
  is_blocked BOOLEAN DEFAULT FALSE,
  blocked_reason TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 2. Tabela de ativações diárias
CREATE TABLE daily_activations (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  activation_date DATE DEFAULT CURRENT_DATE,
  fingerprint_id UUID REFERENCES device_fingerprints(id),
  ip_address INET NOT NULL,
  user_agent TEXT,
  location JSONB,
  success BOOLEAN DEFAULT TRUE,
  failure_reason TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  UNIQUE(activation_date, fingerprint_id)
);

-- 3. Tabela de IPs suspeitos
CREATE TABLE suspicious_ips (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  ip_address INET NOT NULL UNIQUE,
  reason TEXT NOT NULL,
  blocked_until TIMESTAMP WITH TIME ZONE,
  total_attempts INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 4. Tabela de padrões suspeitos
CREATE TABLE suspicious_patterns (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  pattern_type TEXT NOT NULL, -- 'rapid_activation', 'multiple_devices', 'vpn_usage', etc
  fingerprint_id UUID REFERENCES device_fingerprints(id),
  ip_address INET,
  details JSONB,
  severity INTEGER DEFAULT 1, -- 1-10
  resolved BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 5. Configuração de limites
CREATE TABLE security_config (
  key TEXT PRIMARY KEY,
  value JSONB NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Inserir configurações padrão
INSERT INTO security_config (key, value) VALUES
  ('daily_activation_limit', '{"limit": 10}'),
  ('hourly_activation_limit', '{"limit": 3}'),
  ('max_devices_per_ip', '{"limit": 5}'),
  ('suspicious_pattern_threshold', '{"threshold": 5}'),
  ('auto_block_threshold', '{"threshold": 10}');

-- Índices para performance
CREATE INDEX idx_device_fingerprints_fingerprint ON device_fingerprints(fingerprint);
CREATE INDEX idx_device_fingerprints_blocked ON device_fingerprints(is_blocked);
CREATE INDEX idx_daily_activations_date ON daily_activations(activation_date);
CREATE INDEX idx_daily_activations_fingerprint ON daily_activations(fingerprint_id);
CREATE INDEX idx_daily_activations_ip ON daily_activations(ip_address);
CREATE INDEX idx_suspicious_ips_ip ON suspicious_ips(ip_address);
CREATE INDEX idx_suspicious_patterns_type ON suspicious_patterns(pattern_type);
CREATE INDEX idx_suspicious_patterns_fingerprint ON suspicious_patterns(fingerprint_id);

-- Função para verificar limite diário
CREATE OR REPLACE FUNCTION check_daily_activation_limit(
  p_fingerprint TEXT,
  p_ip_address INET
) RETURNS JSONB AS $$
DECLARE
  v_fingerprint_id UUID;
  v_daily_count INTEGER;
  v_hourly_count INTEGER;
  v_daily_limit INTEGER;
  v_hourly_limit INTEGER;
  v_is_blocked BOOLEAN;
BEGIN
  -- Buscar ID do fingerprint
  SELECT id, is_blocked INTO v_fingerprint_id, v_is_blocked
  FROM device_fingerprints
  WHERE fingerprint = p_fingerprint;
  
  -- Se dispositivo está bloqueado
  IF v_is_blocked THEN
    RETURN jsonb_build_object(
      'allowed', false,
      'reason', 'device_blocked'
    );
  END IF;
  
  -- Buscar limites
  SELECT (value->>'limit')::INTEGER INTO v_daily_limit
  FROM security_config
  WHERE key = 'daily_activation_limit';
  
  SELECT (value->>'limit')::INTEGER INTO v_hourly_limit
  FROM security_config
  WHERE key = 'hourly_activation_limit';
  
  -- Contar ativações diárias
  SELECT COUNT(*) INTO v_daily_count
  FROM daily_activations
  WHERE fingerprint_id = v_fingerprint_id
    AND activation_date = CURRENT_DATE
    AND success = true;
  
  -- Contar ativações na última hora
  SELECT COUNT(*) INTO v_hourly_count
  FROM daily_activations
  WHERE fingerprint_id = v_fingerprint_id
    AND created_at > CURRENT_TIMESTAMP - INTERVAL '1 hour'
    AND success = true;
  
  -- Verificar limites
  IF v_daily_count >= v_daily_limit THEN
    RETURN jsonb_build_object(
      'allowed', false,
      'reason', 'daily_limit_exceeded',
      'limit', v_daily_limit,
      'count', v_daily_count
    );
  END IF;
  
  IF v_hourly_count >= v_hourly_limit THEN
    RETURN jsonb_build_object(
      'allowed', false,
      'reason', 'hourly_limit_exceeded',
      'limit', v_hourly_limit,
      'count', v_hourly_count
    );
  END IF;
  
  -- Verificar IP suspeito
  IF EXISTS (
    SELECT 1 FROM suspicious_ips
    WHERE ip_address = p_ip_address
    AND (blocked_until IS NULL OR blocked_until > CURRENT_TIMESTAMP)
  ) THEN
    RETURN jsonb_build_object(
      'allowed', false,
      'reason', 'suspicious_ip'
    );
  END IF;
  
  RETURN jsonb_build_object(
    'allowed', true,
    'daily_count', v_daily_count,
    'hourly_count', v_hourly_count
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Função para registrar ativação
CREATE OR REPLACE FUNCTION register_activation(
  p_fingerprint TEXT,
  p_device_info JSONB,
  p_ip_address INET,
  p_user_agent TEXT,
  p_location JSONB DEFAULT NULL
) RETURNS UUID AS $$
DECLARE
  v_fingerprint_id UUID;
  v_activation_id UUID;
BEGIN
  -- Criar ou atualizar fingerprint
  INSERT INTO device_fingerprints (fingerprint, device_info)
  VALUES (p_fingerprint, p_device_info)
  ON CONFLICT (fingerprint) DO UPDATE
  SET 
    last_seen = CURRENT_TIMESTAMP,
    total_activations = device_fingerprints.total_activations + 1
  RETURNING id INTO v_fingerprint_id;
  
  -- Registrar ativação
  INSERT INTO daily_activations (
    fingerprint_id,
    ip_address,
    user_agent,
    location
  ) VALUES (
    v_fingerprint_id,
    p_ip_address,
    p_user_agent,
    p_location
  )
  ON CONFLICT (activation_date, fingerprint_id) DO UPDATE
  SET created_at = CURRENT_TIMESTAMP
  RETURNING id INTO v_activation_id;
  
  -- Verificar padrões suspeitos
  PERFORM check_suspicious_patterns(v_fingerprint_id, p_ip_address);
  
  RETURN v_activation_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Função para detectar padrões suspeitos
CREATE OR REPLACE FUNCTION check_suspicious_patterns(
  p_fingerprint_id UUID,
  p_ip_address INET
) RETURNS VOID AS $$
DECLARE
  v_device_count INTEGER;
  v_recent_ips INTEGER;
  v_rapid_activations INTEGER;
BEGIN
  -- Verificar múltiplos dispositivos no mesmo IP
  SELECT COUNT(DISTINCT fingerprint_id) INTO v_device_count
  FROM daily_activations
  WHERE ip_address = p_ip_address
    AND activation_date >= CURRENT_DATE - INTERVAL '7 days';
  
  IF v_device_count > 5 THEN
    INSERT INTO suspicious_patterns (
      pattern_type,
      fingerprint_id,
      ip_address,
      details,
      severity
    ) VALUES (
      'multiple_devices_same_ip',
      p_fingerprint_id,
      p_ip_address,
      jsonb_build_object('device_count', v_device_count),
      LEAST(v_device_count / 2, 10)
    );
  END IF;
  
  -- Verificar mudanças rápidas de IP
  SELECT COUNT(DISTINCT ip_address) INTO v_recent_ips
  FROM daily_activations
  WHERE fingerprint_id = p_fingerprint_id
    AND created_at > CURRENT_TIMESTAMP - INTERVAL '1 hour';
  
  IF v_recent_ips > 3 THEN
    INSERT INTO suspicious_patterns (
      pattern_type,
      fingerprint_id,
      details,
      severity
    ) VALUES (
      'rapid_ip_changes',
      p_fingerprint_id,
      jsonb_build_object('ip_count', v_recent_ips),
      LEAST(v_recent_ips * 2, 10)
    );
  END IF;
  
  -- Verificar ativações muito rápidas
  SELECT COUNT(*) INTO v_rapid_activations
  FROM daily_activations
  WHERE fingerprint_id = p_fingerprint_id
    AND created_at > CURRENT_TIMESTAMP - INTERVAL '5 minutes';
  
  IF v_rapid_activations > 2 THEN
    INSERT INTO suspicious_patterns (
      pattern_type,
      fingerprint_id,
      details,
      severity
    ) VALUES (
      'rapid_activations',
      p_fingerprint_id,
      jsonb_build_object('count', v_rapid_activations),
      LEAST(v_rapid_activations * 3, 10)
    );
  END IF;
  
  -- Auto-bloquear se muitos padrões suspeitos
  IF (
    SELECT SUM(severity)
    FROM suspicious_patterns
    WHERE fingerprint_id = p_fingerprint_id
      AND resolved = false
      AND created_at > CURRENT_TIMESTAMP - INTERVAL '24 hours'
  ) >= 10 THEN
    UPDATE device_fingerprints
    SET 
      is_blocked = true,
      blocked_reason = 'auto_blocked_suspicious_activity'
    WHERE id = p_fingerprint_id;
  END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Função principal para verificar acesso via link fixo
CREATE OR REPLACE FUNCTION verify_fixed_link_access(
  p_fingerprint TEXT,
  p_device_info JSONB,
  p_ip_address INET,
  p_user_agent TEXT,
  p_location JSONB DEFAULT NULL
) RETURNS JSONB AS $$
DECLARE
  v_check_result JSONB;
  v_activation_id UUID;
  v_session_id UUID;
  v_expires_at TIMESTAMP WITH TIME ZONE;
BEGIN
  -- Verificar limites
  v_check_result := check_daily_activation_limit(p_fingerprint, p_ip_address);
  
  IF NOT (v_check_result->>'allowed')::BOOLEAN THEN
    RETURN v_check_result;
  END IF;
  
  -- Registrar ativação
  v_activation_id := register_activation(
    p_fingerprint,
    p_device_info,
    p_ip_address,
    p_user_agent,
    p_location
  );
  
  -- Criar sessão anônima
  v_session_id := uuid_generate_v4();
  v_expires_at := CURRENT_TIMESTAMP + INTERVAL '30 days';
  
  INSERT INTO anonymous_access_sessions (
    session_id,
    ip_address,
    user_agent,
    expires_at,
    metadata
  ) VALUES (
    v_session_id,
    p_ip_address,
    p_user_agent,
    v_expires_at,
    jsonb_build_object(
      'fingerprint', p_fingerprint,
      'activation_id', v_activation_id,
      'device_info', p_device_info
    )
  );
  
  RETURN jsonb_build_object(
    'success', true,
    'session_id', v_session_id,
    'expires_at', v_expires_at,
    'activation_id', v_activation_id
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- RLS Policies
ALTER TABLE device_fingerprints ENABLE ROW LEVEL SECURITY;
ALTER TABLE daily_activations ENABLE ROW LEVEL SECURITY;
ALTER TABLE suspicious_ips ENABLE ROW LEVEL SECURITY;
ALTER TABLE suspicious_patterns ENABLE ROW LEVEL SECURITY;
ALTER TABLE security_config ENABLE ROW LEVEL SECURITY;

-- Apenas service role pode acessar essas tabelas
CREATE POLICY "Service role only" ON device_fingerprints
  FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Service role only" ON daily_activations
  FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Service role only" ON suspicious_ips
  FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Service role only" ON suspicious_patterns
  FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Service role only" ON security_config
  FOR ALL USING (auth.role() = 'service_role');