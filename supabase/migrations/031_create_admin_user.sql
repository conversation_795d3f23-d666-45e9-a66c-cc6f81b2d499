-- C<PERSON>r função para configurar usu<PERSON>rio admin
CREATE OR REPLACE FUNCTION public.setup_admin_user()
RETURNS void AS $$
DECLARE
  admin_user_id UUID;
BEGIN
  -- Buscar o ID do usuário <EMAIL> se existir
  SELECT id INTO admin_user_id
  FROM auth.users
  WHERE email = '<EMAIL>'
  LIMIT 1;
  
  -- Se o usuário admin existir, garantir que tem role admin
  IF admin_user_id IS NOT NULL THEN
    -- Inserir ou atualizar o perfil
    INSERT INTO public.user_profiles (id, email, name, role, created_at, updated_at)
    VALUES (
      admin_user_id,
      '<EMAIL>',
      'Administrador',
      'admin',
      NOW(),
      NOW()
    )
    ON CONFLICT (id) DO UPDATE
    SET role = 'admin',
        updated_at = NOW();
  END IF;
END;
$$ LANGUAGE plpgsql;

-- Executar a função
SELECT public.setup_admin_user();

-- <PERSON><PERSON><PERSON><PERSON> le<PERSON><EMAIL> como admin também
CREATE OR REPLACE FUNCTION public.add_admin_by_email(admin_email TEXT)
RETURNS void AS $$
DECLARE
  user_id UUID;
BEGIN
  -- Buscar o ID do usuário pelo email
  SELECT id INTO user_id
  FROM auth.users
  WHERE email = admin_email
  LIMIT 1;
  
  -- Se o usuário existir, garantir que tem role admin
  IF user_id IS NOT NULL THEN
    -- Inserir ou atualizar o perfil
    INSERT INTO public.user_profiles (id, email, name, role, created_at, updated_at)
    VALUES (
      user_id,
      admin_email,
      'Administrador',
      'admin',
      NOW(),
      NOW()
    )
    ON CONFLICT (id) DO UPDATE
    SET role = 'admin',
        updated_at = NOW();
  END IF;
END;
$$ LANGUAGE plpgsql;

-- Adicionar <EMAIL> como admin
SELECT public.add_admin_by_email('<EMAIL>');