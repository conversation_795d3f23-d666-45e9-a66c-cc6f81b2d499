-- StreamPlus España Database Schema
-- Execute this in Supabase SQL Editor

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Create profiles table (extends auth.users)
CREATE TABLE IF NOT EXISTS profiles (
    id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    username TEXT UNIQUE,
    full_name TEXT,
    avatar_url TEXT,
    subscription_tier TEXT DEFAULT 'free' CHECK (subscription_tier IN ('free', 'basic', 'premium', 'vip')),
    subscription_expires_at TIMESTAMPTZ,
    trial_used BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create channels table
CREATE TABLE IF NOT EXISTS channels (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL,
    slug TEXT UNIQUE NOT NULL,
    description TEXT,
    logo_url TEXT,
    banner_url TEXT,
    stream_url TEXT NOT NULL,
    fallback_urls TEXT[] DEFAULT '{}',
    category TEXT NOT NULL CHECK (category IN ('sports', 'esports', 'entertainment', 'news', 'kids')),
    is_premium BOOLEAN DEFAULT FALSE,
    required_tier TEXT DEFAULT 'free' CHECK (required_tier IN ('free', 'basic', 'premium', 'vip')),
    is_active BOOLEAN DEFAULT TRUE,
    viewer_count INTEGER DEFAULT 0,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create viewing_sessions table
CREATE TABLE IF NOT EXISTS viewing_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
    channel_id UUID REFERENCES channels(id) ON DELETE CASCADE,
    started_at TIMESTAMPTZ DEFAULT NOW(),
    ended_at TIMESTAMPTZ,
    duration_seconds INTEGER,
    quality TEXT,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create app_verification_tokens table
CREATE TABLE IF NOT EXISTS app_verification_tokens (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
    token TEXT UNIQUE NOT NULL,
    used BOOLEAN DEFAULT FALSE,
    used_at TIMESTAMPTZ,
    expires_at TIMESTAMPTZ NOT NULL,
    platform TEXT CHECK (platform IN ('ios', 'android')),
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create favorites table
CREATE TABLE IF NOT EXISTS favorites (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
    channel_id UUID REFERENCES channels(id) ON DELETE CASCADE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(user_id, channel_id)
);

-- Create channel_schedule table
CREATE TABLE IF NOT EXISTS channel_schedule (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    channel_id UUID REFERENCES channels(id) ON DELETE CASCADE,
    title TEXT NOT NULL,
    description TEXT,
    starts_at TIMESTAMPTZ NOT NULL,
    ends_at TIMESTAMPTZ NOT NULL,
    is_live BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_channels_category ON channels(category);
CREATE INDEX IF NOT EXISTS idx_channels_is_active ON channels(is_active);
CREATE INDEX IF NOT EXISTS idx_channels_slug ON channels(slug);
CREATE INDEX IF NOT EXISTS idx_viewing_sessions_user_id ON viewing_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_viewing_sessions_channel_id ON viewing_sessions(channel_id);
CREATE INDEX IF NOT EXISTS idx_viewing_sessions_started_at ON viewing_sessions(started_at);
CREATE INDEX IF NOT EXISTS idx_app_verification_tokens_token ON app_verification_tokens(token);
CREATE INDEX IF NOT EXISTS idx_app_verification_tokens_expires_at ON app_verification_tokens(expires_at);
CREATE INDEX IF NOT EXISTS idx_favorites_user_id ON favorites(user_id);
CREATE INDEX IF NOT EXISTS idx_channel_schedule_channel_id ON channel_schedule(channel_id);
CREATE INDEX IF NOT EXISTS idx_channel_schedule_starts_at ON channel_schedule(starts_at);

-- Create update timestamp function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
DROP TRIGGER IF EXISTS update_profiles_updated_at ON profiles;
CREATE TRIGGER update_profiles_updated_at BEFORE UPDATE ON profiles
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_channels_updated_at ON channels;
CREATE TRIGGER update_channels_updated_at BEFORE UPDATE ON channels
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_channel_schedule_updated_at ON channel_schedule;
CREATE TRIGGER update_channel_schedule_updated_at BEFORE UPDATE ON channel_schedule
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert some demo channels
INSERT INTO channels (name, slug, description, logo_url, stream_url, category, is_premium, required_tier) VALUES
    ('LaLiga TV', 'laliga-tv', 'Todos los partidos de LaLiga en directo', '/logos/laliga.png', 'https://demo-stream.com/laliga.m3u8', 'sports', true, 'premium'),
    ('Champions League', 'champions-league', 'Los mejores partidos de Europa', '/logos/champions.png', 'https://demo-stream.com/champions.m3u8', 'sports', true, 'premium'),
    ('Formula 1', 'formula-1', 'Todas las carreras de F1 en directo', '/logos/f1.png', 'https://demo-stream.com/f1.m3u8', 'sports', true, 'vip'),
    ('ESPN Deportes', 'espn-deportes', 'Lo mejor del deporte internacional', '/logos/espn.png', 'https://demo-stream.com/espn.m3u8', 'sports', false, 'basic'),
    ('eSports Arena', 'esports-arena', 'Torneos de League of Legends, CS:GO y más', '/logos/esports.png', 'https://demo-stream.com/esports.m3u8', 'esports', false, 'free')
ON CONFLICT (slug) DO NOTHING;

-- Enable RLS
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE channels ENABLE ROW LEVEL SECURITY;
ALTER TABLE viewing_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE app_verification_tokens ENABLE ROW LEVEL SECURITY;
ALTER TABLE favorites ENABLE ROW LEVEL SECURITY;
ALTER TABLE channel_schedule ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Public profiles are viewable by everyone" ON profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON profiles;
DROP POLICY IF EXISTS "Users can insert own profile" ON profiles;
DROP POLICY IF EXISTS "Channels are viewable by everyone" ON channels;
DROP POLICY IF EXISTS "Users can view own sessions" ON viewing_sessions;
DROP POLICY IF EXISTS "Users can create own sessions" ON viewing_sessions;
DROP POLICY IF EXISTS "Users can update own sessions" ON viewing_sessions;
DROP POLICY IF EXISTS "Users can view own tokens" ON app_verification_tokens;
DROP POLICY IF EXISTS "Users can create own tokens" ON app_verification_tokens;
DROP POLICY IF EXISTS "Users can view own favorites" ON favorites;
DROP POLICY IF EXISTS "Users can create own favorites" ON favorites;
DROP POLICY IF EXISTS "Users can delete own favorites" ON favorites;
DROP POLICY IF EXISTS "Schedule is viewable by everyone" ON channel_schedule;

-- Create RLS policies
CREATE POLICY "Public profiles are viewable by everyone" ON profiles
    FOR SELECT USING (true);

CREATE POLICY "Users can update own profile" ON profiles
    FOR UPDATE USING ((SELECT auth.uid()) = id);

CREATE POLICY "Users can insert own profile" ON profiles
    FOR INSERT WITH CHECK ((SELECT auth.uid()) = id);

CREATE POLICY "Channels are viewable by everyone" ON channels
    FOR SELECT USING (is_active = true);

CREATE POLICY "Users can view own sessions" ON viewing_sessions
    FOR SELECT USING ((SELECT auth.uid()) = user_id);

CREATE POLICY "Users can create own sessions" ON viewing_sessions
    FOR INSERT WITH CHECK ((SELECT auth.uid()) = user_id);

CREATE POLICY "Users can update own sessions" ON viewing_sessions
    FOR UPDATE USING ((SELECT auth.uid()) = user_id);

CREATE POLICY "Users can view own tokens" ON app_verification_tokens
    FOR SELECT USING ((SELECT auth.uid()) = user_id);

CREATE POLICY "Users can create own tokens" ON app_verification_tokens
    FOR INSERT WITH CHECK ((SELECT auth.uid()) = user_id);

CREATE POLICY "Users can view own favorites" ON favorites
    FOR SELECT USING ((SELECT auth.uid()) = user_id);

CREATE POLICY "Users can create own favorites" ON favorites
    FOR INSERT WITH CHECK ((SELECT auth.uid()) = user_id);

CREATE POLICY "Users can delete own favorites" ON favorites
    FOR DELETE USING ((SELECT auth.uid()) = user_id);

CREATE POLICY "Schedule is viewable by everyone" ON channel_schedule
    FOR SELECT USING (true);

-- Create function to handle new user creation
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.profiles (id, full_name, avatar_url)
    VALUES (
        NEW.id,
        NEW.raw_user_meta_data->>'full_name',
        NEW.raw_user_meta_data->>'avatar_url'
    )
    ON CONFLICT (id) DO NOTHING;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for new user creation
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION handle_new_user();

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO postgres, anon, authenticated, service_role;
GRANT ALL ON ALL TABLES IN SCHEMA public TO postgres, service_role;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO postgres, service_role;
GRANT ALL ON ALL ROUTINES IN SCHEMA public TO postgres, service_role;

-- For anonymous and authenticated users
GRANT SELECT ON channels TO anon, authenticated;
GRANT SELECT ON channel_schedule TO anon, authenticated;
GRANT SELECT, INSERT, UPDATE ON profiles TO authenticated;
GRANT SELECT, INSERT, UPDATE ON viewing_sessions TO authenticated;
GRANT SELECT, INSERT ON app_verification_tokens TO authenticated;
GRANT SELECT, INSERT, DELETE ON favorites TO authenticated;

-- Success message
DO $$
BEGIN
    RAISE NOTICE 'Database setup completed successfully!';
END $$;