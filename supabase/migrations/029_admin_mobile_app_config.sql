-- Criar tabela para configurações do app móvel
CREATE TABLE IF NOT EXISTS public.mobile_app_config (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  android_url TEXT NOT NULL,
  ios_url TEXT,
  qr_code_url TEXT, -- URL que o QR code deve apontar
  tutorial_video_url TEXT, -- URL do vídeo tutorial (YouTube, etc)
  tutorial_video_file TEXT, -- Path do arquivo de vídeo no storage
  android_badge_url TEXT DEFAULT 'https://play.google.com/intl/en_us/badges/static/images/badges/es_badge_web_generic.png',
  ios_badge_url TEXT DEFAULT 'https://developer.apple.com/assets/elements/badges/download-on-the-app-store.svg',
  ios_available BOOLEAN DEFAULT false,
  android_available BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Inserir configuração padrão
INSERT INTO public.mobile_app_config (
  android_url,
  ios_url,
  qr_code_url
) VALUES (
  'https://play.google.com/store/apps/details?id=com.streamplus.espana',
  '#',
  'https://play.google.com/store/apps/details?id=com.streamplus.espana'
);

-- Criar RLS policies
ALTER TABLE public.mobile_app_config ENABLE ROW LEVEL SECURITY;

-- Política para leitura pública
CREATE POLICY "Mobile app config readable by all" ON public.mobile_app_config
  FOR SELECT USING (true);

-- Política para edição apenas por admins
CREATE POLICY "Mobile app config editable by admins only" ON public.mobile_app_config
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.user_profiles
      WHERE user_profiles.id = auth.uid()
      AND user_profiles.role = 'admin'
    )
  );

-- Função para atualizar timestamp
CREATE OR REPLACE FUNCTION public.update_mobile_app_config_timestamp()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger para atualizar timestamp
CREATE TRIGGER update_mobile_app_config_timestamp
BEFORE UPDATE ON public.mobile_app_config
FOR EACH ROW
EXECUTE FUNCTION public.update_mobile_app_config_timestamp();