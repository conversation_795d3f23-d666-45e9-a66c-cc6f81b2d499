-- Corri<PERSON><PERSON> erro de coluna ambígua na função check_user_access_unified
DROP FUNCTION IF EXISTS check_user_access_unified(TEXT);

CREATE OR REPLACE FUNCTION check_user_access_unified(p_user_id TEXT)
RETURNS TABLE (
  has_access BOOLEAN,
  access_type VARCHAR,
  expires_at TIMESTAMP WITH TIME ZONE,
  subscription_tier VARCHAR
) AS $$
DECLARE
  v_has_access BOOLEAN := false;
  v_access_type VARCHAR := NULL;
  v_expires_at TIMESTAMP WITH TIME ZONE := NULL;
  v_subscription_tier VARCHAR := 'free';
BEGIN
  -- Log de entrada
  RAISE NOTICE '[CHECK_ACCESS] Iniciando verificação para user_id: %', p_user_id;

  -- Primeiro verificar na tabela stack_profiles (usuários Stack Auth)
  SELECT 
    true,
    'assinatura'::VARCHAR,
    sp.subscription_current_period_end,
    sp.subscription_tier::VARCHAR
  INTO v_has_access, v_access_type, v_expires_at, v_subscription_tier
  FROM stack_profiles sp
  WHERE sp.id = p_user_id 
    AND sp.subscription_tier = 'premium'
    AND sp.subscription_status = 'active'
    AND sp.subscription_current_period_end > NOW()
  LIMIT 1;

  IF v_has_access THEN
    RAISE NOTICE '[CHECK_ACCESS] Acesso encontrado em stack_profiles';
    RETURN QUERY SELECT v_has_access, v_access_type, v_expires_at, v_subscription_tier;
    RETURN;
  END IF;

  -- Tentar converter para UUID e verificar na tabela profiles (Supabase Auth)
  BEGIN
    SELECT 
      true,
      'assinatura'::VARCHAR,
      p.subscription_current_period_end,
      p.subscription_tier::VARCHAR
    INTO v_has_access, v_access_type, v_expires_at, v_subscription_tier
    FROM profiles p
    WHERE p.id = p_user_id::UUID 
      AND p.subscription_tier = 'premium'
      AND p.subscription_status = 'active'
      AND p.subscription_current_period_end > NOW()
    LIMIT 1;

    IF v_has_access THEN
      RAISE NOTICE '[CHECK_ACCESS] Acesso encontrado em profiles';
      RETURN QUERY SELECT v_has_access, v_access_type, v_expires_at, v_subscription_tier;
      RETURN;
    END IF;
  EXCEPTION
    WHEN invalid_text_representation THEN
      RAISE NOTICE '[CHECK_ACCESS] ID não é UUID válido para profiles: %', p_user_id;
  END;

  -- Se não tem premium, verificar na tabela user_access
  BEGIN
    SELECT 
      true,
      ua.tipo::VARCHAR,
      ua.ativo_ate,
      'premium'::VARCHAR
    INTO v_has_access, v_access_type, v_expires_at, v_subscription_tier
    FROM user_access ua
    WHERE ua.user_id = p_user_id::UUID 
      AND ua.ativo_ate > NOW()
    ORDER BY ua.ativo_ate DESC
    LIMIT 1;

    IF v_has_access THEN
      RAISE NOTICE '[CHECK_ACCESS] Acesso encontrado em user_access';
      RETURN QUERY SELECT v_has_access, v_access_type, v_expires_at, v_subscription_tier;
      RETURN;
    END IF;
  EXCEPTION
    WHEN invalid_text_representation THEN
      RAISE NOTICE '[CHECK_ACCESS] ID não é UUID válido para user_access: %', p_user_id;
  END;

  -- Se não tem nenhum acesso
  RAISE NOTICE '[CHECK_ACCESS] Nenhum acesso encontrado para user_id: %', p_user_id;
  RETURN QUERY SELECT 
    false,
    NULL::VARCHAR,
    NULL::TIMESTAMP WITH TIME ZONE,
    'free'::VARCHAR;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Adicionar comentário
COMMENT ON FUNCTION check_user_access_unified IS 'Função que verifica acesso unificado sem ambiguidade de colunas - v2';

-- Garantir que as políticas RLS estejam corretas para as tabelas relacionadas
ALTER TABLE anonymous_access ENABLE ROW LEVEL SECURITY;

-- Política mais permissiva para anonymous_access
DROP POLICY IF EXISTS "System can manage anonymous access" ON anonymous_access;
CREATE POLICY "Allow all operations on anonymous access" ON anonymous_access
  FOR ALL USING (true) WITH CHECK (true);