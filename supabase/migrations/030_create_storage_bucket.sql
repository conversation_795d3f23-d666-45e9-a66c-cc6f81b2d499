-- Criar bucket para vídeos tutoriais
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'tutorial-videos',
  'tutorial-videos',
  true, -- Público para permitir visualização
  104857600, -- 100MB limit
  ARRAY['video/mp4', 'video/webm', 'video/ogg', 'video/quicktime']
)
ON CONFLICT (id) DO NOTHING;

-- Criar políticas de storage
CREATE POLICY "Vídeos tutoriais são públicos para visualização" 
ON storage.objects FOR SELECT 
USING (bucket_id = 'tutorial-videos');

CREATE POLICY "Apenas admins podem fazer upload de vídeos tutoriais" 
ON storage.objects FOR INSERT 
WITH CHECK (
  bucket_id = 'tutorial-videos' AND
  EXISTS (
    SELECT 1 FROM public.user_profiles
    WHERE user_profiles.id = auth.uid()
    AND user_profiles.role = 'admin'
  )
);

CREATE POLICY "Apenas admins podem deletar vídeos tutoriais" 
ON storage.objects FOR DELETE 
USING (
  bucket_id = 'tutorial-videos' AND
  EXISTS (
    SELECT 1 FROM public.user_profiles
    WHERE user_profiles.id = auth.uid()
    AND user_profiles.role = 'admin'
  )
);