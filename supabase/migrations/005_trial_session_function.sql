-- <PERSON><PERSON>r tabela de trial_sessions se não existir
CREATE TABLE IF NOT EXISTS public.trial_sessions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  session_id UUID UNIQUE NOT NULL,
  trial_start TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  trial_end TIMESTAMPTZ NOT NULL,
  ip_address TEXT,
  user_agent TEXT,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Criar índices
CREATE INDEX IF NOT EXISTS idx_trial_sessions_session_id ON public.trial_sessions(session_id);
CREATE INDEX IF NOT EXISTS idx_trial_sessions_trial_end ON public.trial_sessions(trial_end);

-- RLS
ALTER TABLE public.trial_sessions ENABLE ROW LEVEL SECURITY;

-- Política para permitir inserção anônima
CREATE POLICY "Allow anonymous insert" ON public.trial_sessions
  FOR INSERT TO anon, authenticated
  WITH CHECK (true);

-- Política para permitir leitura da própria sessão
CREATE POLICY "Allow read own session" ON public.trial_sessions
  FOR SELECT TO anon, authenticated
  USING (
    session_id::text = current_setting('request.headers', true)::json->>'x-trial-session-id'
    OR session_id::text = current_setting('request.cookies', true)::json->>'trial_session_id'
  );

-- Criar função para gerenciar sessões de trial
CREATE OR REPLACE FUNCTION public.get_or_create_trial_session(
  p_session_id UUID DEFAULT NULL
)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_session_id UUID;
  v_trial_start TIMESTAMPTZ;
  v_trial_end TIMESTAMPTZ;
  v_now TIMESTAMPTZ := NOW();
  v_trial_duration INTERVAL := INTERVAL '5 minutes';
  v_is_expired BOOLEAN;
  v_time_remaining INTEGER;
BEGIN
  -- Se não foi fornecido session_id, gerar um novo
  IF p_session_id IS NULL THEN
    v_session_id := gen_random_uuid();
  ELSE
    v_session_id := p_session_id;
  END IF;

  -- Buscar sessão existente
  SELECT 
    trial_start,
    trial_end
  INTO v_trial_start, v_trial_end
  FROM public.trial_sessions
  WHERE session_id = v_session_id;

  -- Se não existe, criar nova sessão
  IF NOT FOUND THEN
    v_trial_start := v_now;
    v_trial_end := v_now + v_trial_duration;
    
    INSERT INTO public.trial_sessions (
      session_id,
      trial_start,
      trial_end,
      ip_address,
      user_agent
    ) VALUES (
      v_session_id,
      v_trial_start,
      v_trial_end,
      current_setting('request.headers', true)::json->>'x-forwarded-for',
      current_setting('request.headers', true)::json->>'user-agent'
    )
    ON CONFLICT (session_id) DO UPDATE SET
      trial_start = EXCLUDED.trial_start,
      trial_end = EXCLUDED.trial_end,
      updated_at = NOW();
  END IF;

  -- Calcular se expirou e tempo restante
  v_is_expired := v_now > v_trial_end;
  v_time_remaining := GREATEST(0, EXTRACT(EPOCH FROM (v_trial_end - v_now))::INTEGER);

  -- Retornar dados da sessão
  RETURN jsonb_build_object(
    'session_id', v_session_id,
    'trial_start', v_trial_start,
    'trial_end', v_trial_end,
    'is_expired', v_is_expired,
    'time_remaining', v_time_remaining,
    'trial_duration_minutes', 5
  );
END;
$$;