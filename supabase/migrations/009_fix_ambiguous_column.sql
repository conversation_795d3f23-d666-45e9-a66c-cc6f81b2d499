-- Corrigir referências ambíguas na função check_user_access_unified
DROP FUNCTION IF EXISTS check_user_access_unified(TEXT);

CREATE OR REPLACE FUNCTION check_user_access_unified(p_user_id TEXT)
RETURNS TABLE (
  has_access BOOLEAN,
  access_type VARCHAR,
  expires_at TIMESTAMP WITH TIME ZONE,
  subscription_tier VARCHAR
) AS $$
BEGIN
  -- Log de entrada (comentado em produção)
  -- RAISE NOTICE 'check_user_access_unified called for user: %', p_user_id;

  -- Primeiro verificar na tabela stack_profiles (usu<PERSON><PERSON><PERSON>ack Auth)
  IF EXISTS (
    SELECT 1 FROM stack_profiles sp
    WHERE sp.id = p_user_id 
    AND sp.subscription_tier = 'premium'
    AND sp.subscription_status = 'active'
    AND sp.subscription_current_period_end > NOW()
  ) THEN
    RETURN QUERY
    SELECT 
      true as has_access,
      'assinatura'::VARCHAR as access_type,
      sp.subscription_current_period_end as expires_at,
      sp.subscription_tier::VARCHAR
    FROM stack_profiles sp
    WHERE sp.id = p_user_id;
    RETURN;
  END IF;

  -- Tentar converter para UUID e verificar na tabela profiles (Supabase Auth)
  BEGIN
    IF EXISTS (
      SELECT 1 FROM profiles p
      WHERE p.id = p_user_id::UUID 
      AND p.subscription_tier = 'premium'
      AND p.subscription_status = 'active'
      AND p.subscription_current_period_end > NOW()
    ) THEN
      RETURN QUERY
      SELECT 
        true as has_access,
        'assinatura'::VARCHAR as access_type,
        p.subscription_current_period_end as expires_at,
        p.subscription_tier::VARCHAR
      FROM profiles p
      WHERE p.id = p_user_id::UUID;
      RETURN;
    END IF;
  EXCEPTION
    WHEN invalid_text_representation THEN
      -- Não é um UUID válido, continuar
      NULL;
  END;

  -- Se não tem premium, verificar na tabela user_access
  BEGIN
    IF EXISTS (
      SELECT 1 FROM user_access ua
      WHERE ua.user_id = p_user_id::UUID 
      AND ua.ativo_ate > NOW()
    ) THEN
      RETURN QUERY
      SELECT 
        true as has_access,
        ua.tipo::VARCHAR as access_type,
        ua.ativo_ate as expires_at,
        'premium'::VARCHAR as subscription_tier
      FROM user_access ua
      WHERE ua.user_id = p_user_id::UUID
      ORDER BY ua.ativo_ate DESC
      LIMIT 1;
      RETURN;
    END IF;
  EXCEPTION
    WHEN invalid_text_representation THEN
      -- Não é um UUID válido, continuar
      NULL;
  END;

  -- Se não tem nenhum acesso
  RETURN QUERY SELECT 
    false as has_access,
    NULL::VARCHAR as access_type,
    NULL::TIMESTAMP WITH TIME ZONE as expires_at,
    'free'::VARCHAR as subscription_tier;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Criar tabela de logs para debug
CREATE TABLE IF NOT EXISTS access_check_logs (
  id SERIAL PRIMARY KEY,
  user_id TEXT,
  has_access BOOLEAN,
  access_type VARCHAR,
  checked_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  metadata JSONB
);

-- Criar índice para performance
CREATE INDEX IF NOT EXISTS idx_access_check_logs_user_id ON access_check_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_access_check_logs_checked_at ON access_check_logs(checked_at);

-- Função wrapper com logging para debug
CREATE OR REPLACE FUNCTION check_user_access_unified_with_log(p_user_id TEXT)
RETURNS TABLE (
  has_access BOOLEAN,
  access_type VARCHAR,
  expires_at TIMESTAMP WITH TIME ZONE,
  subscription_tier VARCHAR
) AS $$
DECLARE
  v_has_access BOOLEAN;
  v_access_type VARCHAR;
  v_expires_at TIMESTAMP WITH TIME ZONE;
  v_subscription_tier VARCHAR;
BEGIN
  -- Chamar a função original
  SELECT * INTO v_has_access, v_access_type, v_expires_at, v_subscription_tier
  FROM check_user_access_unified(p_user_id);
  
  -- Logar o resultado
  INSERT INTO access_check_logs (user_id, has_access, access_type, metadata)
  VALUES (
    p_user_id, 
    v_has_access, 
    v_access_type,
    jsonb_build_object(
      'expires_at', v_expires_at,
      'subscription_tier', v_subscription_tier,
      'timestamp', NOW()
    )
  );
  
  -- Retornar o resultado
  RETURN QUERY SELECT v_has_access, v_access_type, v_expires_at, v_subscription_tier;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Comentário explicativo
COMMENT ON FUNCTION check_user_access_unified IS 'Função corrigida que verifica acesso de usuários Stack Auth e Supabase Auth sem ambiguidade de colunas';