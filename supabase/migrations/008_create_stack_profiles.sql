-- <PERSON><PERSON>r tabela específica para perfis Stack Auth (sem foreign key para auth.users)
CREATE TABLE IF NOT EXISTS stack_profiles (
  id TEXT PRIMARY KEY, -- Stack Auth user ID
  email TEXT,
  full_name TEXT,
  avatar_url TEXT,
  subscription_tier subscription_tier DEFAULT 'free',
  subscription_status TEXT DEFAULT 'inactive',
  subscription_current_period_end TIMESTAMPTZ,
  subscription_start_date TIMESTAMPTZ,
  subscription_cancel_at_period_end BOOLEAN DEFAULT FALSE,
  stripe_customer_id TEXT UNIQUE,
  stripe_subscription_id TEXT,
  trial_end TIMESTAMPTZ,
  trial_used BOOLEAN DEFAULT FALSE,
  app_verified BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- <PERSON><PERSON>r índices
CREATE INDEX IF NOT EXISTS idx_stack_profiles_email ON stack_profiles(email);
CREATE INDEX IF NOT EXISTS idx_stack_profiles_stripe_customer_id ON stack_profiles(stripe_customer_id);
CREATE INDEX IF NOT EXISTS idx_stack_profiles_subscription_status ON stack_profiles(subscription_status);

-- Habilitar RLS
ALTER TABLE stack_profiles ENABLE ROW LEVEL SECURITY;

-- Políticas de segurança
CREATE POLICY "Stack profiles are viewable by everyone" ON stack_profiles
  FOR SELECT USING (true);

CREATE POLICY "Stack profiles can be created by system" ON stack_profiles
  FOR INSERT WITH CHECK (true);

CREATE POLICY "Stack profiles can be updated by system" ON stack_profiles
  FOR UPDATE USING (true);

-- Função unificada para verificar acesso (Stack + Supabase)
CREATE OR REPLACE FUNCTION check_user_access_unified(p_user_id TEXT)
RETURNS TABLE (
  has_access BOOLEAN,
  access_type VARCHAR,
  expires_at TIMESTAMP WITH TIME ZONE,
  subscription_tier VARCHAR
) AS $$
BEGIN
  -- Primeiro verificar na tabela stack_profiles (usuários Stack Auth)
  IF EXISTS (
    SELECT 1 FROM stack_profiles 
    WHERE id = p_user_id 
    AND (
      -- Premium ativo
      (subscription_tier = 'premium' AND subscription_status = 'active' AND subscription_current_period_end > NOW())
      OR
      -- App verificado com período ativo
      (app_verified = true AND subscription_current_period_end > NOW())
    )
  ) THEN
    RETURN QUERY
    SELECT 
      true as has_access,
      CASE 
        WHEN app_verified = true AND subscription_tier != 'premium' THEN 'app_verified'::VARCHAR
        ELSE 'assinatura'::VARCHAR
      END as access_type,
      subscription_current_period_end as expires_at,
      subscription_tier::VARCHAR
    FROM stack_profiles
    WHERE id = p_user_id;
    RETURN;
  END IF;

  -- Tentar converter para UUID e verificar na tabela profiles (Supabase Auth)
  BEGIN
    IF EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = p_user_id::UUID 
      AND (
        -- Premium ativo
        (subscription_tier = 'premium' AND subscription_status = 'active' AND subscription_current_period_end > NOW())
        OR
        -- App verificado com período ativo
        (app_verified = true AND subscription_current_period_end > NOW())
      )
    ) THEN
      RETURN QUERY
      SELECT 
        true as has_access,
        CASE 
          WHEN app_verified = true AND subscription_tier != 'premium' THEN 'app_verified'::VARCHAR
          ELSE 'assinatura'::VARCHAR
        END as access_type,
        subscription_current_period_end as expires_at,
        subscription_tier::VARCHAR
      FROM profiles
      WHERE id = p_user_id::UUID;
      RETURN;
    END IF;
  EXCEPTION
    WHEN invalid_text_representation THEN
      -- Não é um UUID válido, continuar
      NULL;
  END;

  -- Se não tem premium, verificar na tabela user_access
  BEGIN
    IF EXISTS (
      SELECT 1 FROM user_access 
      WHERE user_id = p_user_id::UUID 
      AND ativo_ate > NOW()
    ) THEN
      RETURN QUERY
      SELECT 
        true as has_access,
        tipo::VARCHAR as access_type,
        ativo_ate as expires_at,
        'premium'::VARCHAR as subscription_tier
      FROM user_access
      WHERE user_id = p_user_id::UUID
      ORDER BY ativo_ate DESC
      LIMIT 1;
      RETURN;
    END IF;
  EXCEPTION
    WHEN invalid_text_representation THEN
      -- Não é um UUID válido, continuar
      NULL;
  END;

  -- Se não tem nenhum acesso
  RETURN QUERY SELECT 
    false as has_access,
    NULL::VARCHAR as access_type,
    NULL::TIMESTAMP WITH TIME ZONE as expires_at,
    'free'::VARCHAR as subscription_tier;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Comentários
COMMENT ON TABLE stack_profiles IS 'Perfis de usuários autenticados via Stack Auth (OAuth)';
COMMENT ON FUNCTION check_user_access_unified IS 'Função unificada para verificar acesso de usuários Stack Auth e Supabase Auth';