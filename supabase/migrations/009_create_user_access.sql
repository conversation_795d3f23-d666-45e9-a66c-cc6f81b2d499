-- Criar tabela user_access para acessos temporários (cupons, promoções, etc)
CREATE TABLE IF NOT EXISTS user_access (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  tipo VARCHAR(50) NOT NULL DEFAULT 'promocao',
  ativo_ate TIMESTAMPTZ NOT NULL,
  criado_em TIMESTAMPTZ DEFAULT NOW(),
  atualizado_em TIMESTAMPTZ DEFAULT NOW(),
  metadata JSONB DEFAULT '{}'::jsonb
);

-- Criar índices
CREATE INDEX IF NOT EXISTS idx_user_access_user_id ON user_access(user_id);
CREATE INDEX IF NOT EXISTS idx_user_access_ativo_ate ON user_access(ativo_ate);
CREATE INDEX IF NOT EXISTS idx_user_access_tipo ON user_access(tipo);

-- Habilitar RLS
ALTER TABLE user_access ENABLE ROW LEVEL SECURITY;

-- Políticas de segurança
CREATE POLICY "User access viewable by user" ON user_access
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "User access can be created by system" ON user_access
  FOR INSERT WITH CHECK (true);

CREATE POLICY "User access can be updated by system" ON user_access
  FOR UPDATE USING (true);

-- Comentários
COMMENT ON TABLE user_access IS 'Acessos temporários concedidos aos usuários (cupons, promoções, etc)';
COMMENT ON COLUMN user_access.tipo IS 'Tipo de acesso: promocao, cupom, teste, etc';
COMMENT ON COLUMN user_access.ativo_ate IS 'Data/hora até quando o acesso é válido';