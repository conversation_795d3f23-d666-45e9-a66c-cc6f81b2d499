-- Adici<PERSON>r coluna email à tabela profiles
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS email TEXT;

-- Criar índice para busca por email
CREATE INDEX IF NOT EXISTS idx_profiles_email ON profiles(email);

-- Adicionar data de início da assinatura se não existir
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS subscription_start_date TIMESTAMPTZ;

-- Comentário explicativo
COMMENT ON COLUMN profiles.email IS 'Email do usuário para sincronização com Stack Auth e Stripe';
COMMENT ON COLUMN profiles.subscription_start_date IS 'Data de início da assinatura atual';