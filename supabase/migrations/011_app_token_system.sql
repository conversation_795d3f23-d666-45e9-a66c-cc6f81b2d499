-- C<PERSON>r tabela para tokens únicos do app
CREATE TABLE IF NOT EXISTS app_tokens (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  token TEXT UNIQUE NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  used BOOLEAN DEFAULT FALSE,
  used_at TIMESTAMPTZ,
  used_by_user_id TEXT,
  ip_address TEXT,
  user_agent TEXT,
  platform VARCHAR(20),
  app_version VARCHAR(20),
  expires_at TIMESTAMPTZ DEFAULT NOW() + INTERVAL '1 hour',
  metadata JSONB
);

-- Índices para performance
CREATE INDEX idx_app_tokens_token ON app_tokens(token);
CREATE INDEX idx_app_tokens_expires_at ON app_tokens(expires_at);
CREATE INDEX idx_app_tokens_used ON app_tokens(used);
CREATE INDEX idx_app_tokens_created_at ON app_tokens(created_at);

-- RLS
ALTER TABLE app_tokens ENABLE ROW LEVEL SECURITY;

-- Política para permitir que o sistema gerencie tokens
CREATE POLICY "System can manage app tokens" ON app_tokens
  FOR ALL USING (true);

-- Função para limpar tokens expirados (executar periodicamente)
CREATE OR REPLACE FUNCTION cleanup_expired_app_tokens()
RETURNS INTEGER AS $$
DECLARE
  deleted_count INTEGER;
BEGIN
  DELETE FROM app_tokens
  WHERE expires_at < NOW() AND used = FALSE;
  
  GET DIAGNOSTICS deleted_count = ROW_COUNT;
  RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Atualizar função check_user_access_unified para incluir tokens do app
DROP FUNCTION IF EXISTS check_user_access_unified(TEXT);

CREATE OR REPLACE FUNCTION check_user_access_unified(p_user_id TEXT)
RETURNS TABLE (
  has_access BOOLEAN,
  access_type VARCHAR,
  expires_at TIMESTAMP WITH TIME ZONE,
  subscription_tier VARCHAR
) AS $$
BEGIN
  -- Log de entrada (comentado em produção)
  -- RAISE NOTICE 'check_user_access_unified called for user: %', p_user_id;

  -- Primeiro verificar na tabela stack_profiles (usuários Stack Auth)
  IF EXISTS (
    SELECT 1 FROM stack_profiles sp
    WHERE sp.id = p_user_id 
    AND (
      -- Premium ativo
      (sp.subscription_tier = 'premium' AND sp.subscription_status = 'active' AND sp.subscription_current_period_end > NOW())
      OR
      -- App verificado com período ativo (sistema antigo - manter compatibilidade)
      (sp.app_verified = true AND sp.subscription_current_period_end > NOW())
    )
  ) THEN
    RETURN QUERY
    SELECT 
      true as has_access,
      CASE 
        WHEN sp.app_verified = true AND sp.subscription_tier != 'premium' THEN 'app_token'::VARCHAR
        ELSE 'assinatura'::VARCHAR
      END as access_type,
      sp.subscription_current_period_end as expires_at,
      sp.subscription_tier::VARCHAR
    FROM stack_profiles sp
    WHERE sp.id = p_user_id;
    RETURN;
  END IF;

  -- Tentar converter para UUID e verificar na tabela profiles (Supabase Auth)
  BEGIN
    IF EXISTS (
      SELECT 1 FROM profiles p
      WHERE p.id = p_user_id::UUID 
      AND (
        -- Premium ativo
        (p.subscription_tier = 'premium' AND p.subscription_status = 'active' AND p.subscription_current_period_end > NOW())
        OR
        -- App verificado com período ativo (sistema antigo - manter compatibilidade)
        (p.app_verified = true AND p.subscription_current_period_end > NOW())
      )
    ) THEN
      RETURN QUERY
      SELECT 
        true as has_access,
        CASE 
          WHEN p.app_verified = true AND p.subscription_tier != 'premium' THEN 'app_token'::VARCHAR
          ELSE 'assinatura'::VARCHAR
        END as access_type,
        p.subscription_current_period_end as expires_at,
        p.subscription_tier::VARCHAR
      FROM profiles p
      WHERE p.id = p_user_id::UUID;
      RETURN;
    END IF;
  EXCEPTION
    WHEN invalid_text_representation THEN
      -- Não é um UUID válido, continuar
      NULL;
  END;

  -- Se não tem premium ou app_verified, verificar na tabela user_access
  BEGIN
    IF EXISTS (
      SELECT 1 FROM user_access ua
      WHERE ua.user_id = p_user_id::UUID 
      AND ua.ativo_ate > NOW()
    ) THEN
      RETURN QUERY
      SELECT 
        true as has_access,
        ua.tipo::VARCHAR as access_type,
        ua.ativo_ate as expires_at,
        'premium'::VARCHAR as subscription_tier
      FROM user_access ua
      WHERE ua.user_id = p_user_id::UUID
      ORDER BY ua.ativo_ate DESC
      LIMIT 1;
      RETURN;
    END IF;
  EXCEPTION
    WHEN invalid_text_representation THEN
      -- Não é um UUID válido, continuar
      NULL;
  END;

  -- Se não tem nenhum acesso
  RETURN QUERY SELECT 
    false as has_access,
    NULL::VARCHAR as access_type,
    NULL::TIMESTAMP WITH TIME ZONE as expires_at,
    'free'::VARCHAR as subscription_tier;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Tabela para rate limiting
CREATE TABLE IF NOT EXISTS app_token_rate_limit (
  ip_address TEXT PRIMARY KEY,
  attempts INTEGER DEFAULT 1,
  first_attempt_at TIMESTAMPTZ DEFAULT NOW(),
  last_attempt_at TIMESTAMPTZ DEFAULT NOW()
);

-- Índice para limpeza
CREATE INDEX idx_app_token_rate_limit_last_attempt ON app_token_rate_limit(last_attempt_at);

-- Função para verificar rate limit
CREATE OR REPLACE FUNCTION check_app_token_rate_limit(p_ip_address TEXT)
RETURNS BOOLEAN AS $$
DECLARE
  v_attempts INTEGER;
  v_first_attempt TIMESTAMPTZ;
BEGIN
  -- Buscar tentativas do IP
  SELECT attempts, first_attempt_at INTO v_attempts, v_first_attempt
  FROM app_token_rate_limit
  WHERE ip_address = p_ip_address;
  
  -- Se não existe registro, permitir
  IF NOT FOUND THEN
    INSERT INTO app_token_rate_limit (ip_address) VALUES (p_ip_address);
    RETURN TRUE;
  END IF;
  
  -- Se primeira tentativa foi há mais de 1 hora, resetar contador
  IF v_first_attempt < NOW() - INTERVAL '1 hour' THEN
    UPDATE app_token_rate_limit
    SET attempts = 1, first_attempt_at = NOW(), last_attempt_at = NOW()
    WHERE ip_address = p_ip_address;
    RETURN TRUE;
  END IF;
  
  -- Se menos de 5 tentativas na última hora, permitir
  IF v_attempts < 5 THEN
    UPDATE app_token_rate_limit
    SET attempts = attempts + 1, last_attempt_at = NOW()
    WHERE ip_address = p_ip_address;
    RETURN TRUE;
  END IF;
  
  -- Caso contrário, bloquear
  RETURN FALSE;
END;
$$ LANGUAGE plpgsql;

-- Comentários
COMMENT ON TABLE app_tokens IS 'Tokens únicos para verificação de download do app';
COMMENT ON COLUMN app_tokens.token IS 'Token UUID único';
COMMENT ON COLUMN app_tokens.used IS 'Se o token já foi utilizado';
COMMENT ON COLUMN app_tokens.used_by_user_id IS 'ID do usuário que usou o token';
COMMENT ON COLUMN app_tokens.expires_at IS 'Data de expiração do token';
COMMENT ON FUNCTION cleanup_expired_app_tokens IS 'Remove tokens expirados não utilizados';
COMMENT ON FUNCTION check_app_token_rate_limit IS 'Verifica rate limit por IP (max 5 tentativas/hora)';