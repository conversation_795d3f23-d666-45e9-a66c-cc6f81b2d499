-- <PERSON><PERSON><PERSON> tabela para códigos de verificação do app
CREATE TABLE IF NOT EXISTS app_verification_codes (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id TEXT NOT NULL,
  code VARCHAR(10) NOT NULL,
  platform VARCHAR(20),
  expires_at TIMESTAMPTZ NOT NULL,
  used BOOLEAN DEFAULT FALSE,
  used_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Índices para performance
CREATE INDEX idx_app_verification_codes_user_id ON app_verification_codes(user_id);
CREATE INDEX idx_app_verification_codes_code ON app_verification_codes(code);
CREATE INDEX idx_app_verification_codes_expires_at ON app_verification_codes(expires_at);

-- Adicionar campos de verificação do app nas tabelas de perfil
ALTER TABLE profiles 
ADD COLUMN IF NOT EXISTS app_verified BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS app_verified_at TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS app_platform VARCHAR(20);

ALTER TABLE stack_profiles 
ADD COLUMN IF NOT EXISTS app_verified BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS app_verified_at TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS app_platform VARCHAR(20);

-- Atualizar função check_user_access_unified para incluir app_verified
DROP FUNCTION IF EXISTS check_user_access_unified(TEXT);

CREATE OR REPLACE FUNCTION check_user_access_unified(p_user_id TEXT)
RETURNS TABLE (
  has_access BOOLEAN,
  access_type VARCHAR,
  expires_at TIMESTAMP WITH TIME ZONE,
  subscription_tier VARCHAR
) AS $$
BEGIN
  -- Log de entrada (comentado em produção)
  -- RAISE NOTICE 'check_user_access_unified called for user: %', p_user_id;

  -- Primeiro verificar na tabela stack_profiles (usuários Stack Auth)
  IF EXISTS (
    SELECT 1 FROM stack_profiles sp
    WHERE sp.id = p_user_id 
    AND (
      -- Premium ativo
      (sp.subscription_tier = 'premium' AND sp.subscription_status = 'active' AND sp.subscription_current_period_end > NOW())
      OR
      -- App verificado com período ativo
      (sp.app_verified = true AND sp.subscription_current_period_end > NOW())
    )
  ) THEN
    RETURN QUERY
    SELECT 
      true as has_access,
      CASE 
        WHEN sp.app_verified = true THEN 'app_verified'::VARCHAR
        ELSE 'assinatura'::VARCHAR
      END as access_type,
      sp.subscription_current_period_end as expires_at,
      sp.subscription_tier::VARCHAR
    FROM stack_profiles sp
    WHERE sp.id = p_user_id;
    RETURN;
  END IF;

  -- Tentar converter para UUID e verificar na tabela profiles (Supabase Auth)
  BEGIN
    IF EXISTS (
      SELECT 1 FROM profiles p
      WHERE p.id = p_user_id::UUID 
      AND (
        -- Premium ativo
        (p.subscription_tier = 'premium' AND p.subscription_status = 'active' AND p.subscription_current_period_end > NOW())
        OR
        -- App verificado com período ativo
        (p.app_verified = true AND p.subscription_current_period_end > NOW())
      )
    ) THEN
      RETURN QUERY
      SELECT 
        true as has_access,
        CASE 
          WHEN p.app_verified = true THEN 'app_verified'::VARCHAR
          ELSE 'assinatura'::VARCHAR
        END as access_type,
        p.subscription_current_period_end as expires_at,
        p.subscription_tier::VARCHAR
      FROM profiles p
      WHERE p.id = p_user_id::UUID;
      RETURN;
    END IF;
  EXCEPTION
    WHEN invalid_text_representation THEN
      -- Não é um UUID válido, continuar
      NULL;
  END;

  -- Se não tem premium ou app_verified, verificar na tabela user_access
  BEGIN
    IF EXISTS (
      SELECT 1 FROM user_access ua
      WHERE ua.user_id = p_user_id::UUID 
      AND ua.ativo_ate > NOW()
    ) THEN
      RETURN QUERY
      SELECT 
        true as has_access,
        ua.tipo::VARCHAR as access_type,
        ua.ativo_ate as expires_at,
        'premium'::VARCHAR as subscription_tier
      FROM user_access ua
      WHERE ua.user_id = p_user_id::UUID
      ORDER BY ua.ativo_ate DESC
      LIMIT 1;
      RETURN;
    END IF;
  EXCEPTION
    WHEN invalid_text_representation THEN
      -- Não é um UUID válido, continuar
      NULL;
  END;

  -- Se não tem nenhum acesso
  RETURN QUERY SELECT 
    false as has_access,
    NULL::VARCHAR as access_type,
    NULL::TIMESTAMP WITH TIME ZONE as expires_at,
    'free'::VARCHAR as subscription_tier;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Atualizar a função com logging também
DROP FUNCTION IF EXISTS check_user_access_unified_with_log(TEXT);

CREATE OR REPLACE FUNCTION check_user_access_unified_with_log(p_user_id TEXT)
RETURNS TABLE (
  has_access BOOLEAN,
  access_type VARCHAR,
  expires_at TIMESTAMP WITH TIME ZONE,
  subscription_tier VARCHAR
) AS $$
DECLARE
  v_has_access BOOLEAN;
  v_access_type VARCHAR;
  v_expires_at TIMESTAMP WITH TIME ZONE;
  v_subscription_tier VARCHAR;
BEGIN
  -- Chamar a função original
  SELECT * INTO v_has_access, v_access_type, v_expires_at, v_subscription_tier
  FROM check_user_access_unified(p_user_id);
  
  -- Logar o resultado
  INSERT INTO access_check_logs (user_id, has_access, access_type, metadata)
  VALUES (
    p_user_id, 
    v_has_access, 
    v_access_type,
    jsonb_build_object(
      'expires_at', v_expires_at,
      'subscription_tier', v_subscription_tier,
      'timestamp', NOW(),
      'source', 'check_user_access_unified_with_log'
    )
  );
  
  -- Retornar o resultado
  RETURN QUERY SELECT v_has_access, v_access_type, v_expires_at, v_subscription_tier;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- RLS para tabela de códigos de verificação
ALTER TABLE app_verification_codes ENABLE ROW LEVEL SECURITY;

-- Política para permitir que o sistema crie e leia códigos
CREATE POLICY "System can manage verification codes" ON app_verification_codes
  FOR ALL USING (true);

-- Comentários
COMMENT ON TABLE app_verification_codes IS 'Códigos de verificação para usuários que baixaram o app';
COMMENT ON COLUMN profiles.app_verified IS 'Se o usuário verificou através do app mobile';
COMMENT ON COLUMN profiles.app_verified_at IS 'Data/hora da verificação do app';
COMMENT ON COLUMN profiles.app_platform IS 'Plataforma do app (ios/android)';
COMMENT ON COLUMN stack_profiles.app_verified IS 'Se o usuário verificou através do app mobile';
COMMENT ON COLUMN stack_profiles.app_verified_at IS 'Data/hora da verificação do app';
COMMENT ON COLUMN stack_profiles.app_platform IS 'Plataforma do app (ios/android)';