-- <PERSON><PERSON><PERSON> tabel<PERSON> para acessos anônimos via app token
CREATE TABLE IF NOT EXISTS anonymous_access (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  session_id TEXT UNIQUE NOT NULL,
  token_id UUID REFERENCES app_tokens(id),
  activated_at TIMESTAMPTZ DEFAULT NOW(),
  expires_at TIMESTAMPTZ NOT NULL,
  ip_address TEXT,
  platform VARCHAR(20),
  last_access_at TIMESTAMPTZ DEFAULT NOW(),
  access_count INTEGER DEFAULT 1,
  metadata JSONB
);

-- Índices
CREATE INDEX idx_anonymous_access_session_id ON anonymous_access(session_id);
CREATE INDEX idx_anonymous_access_expires_at ON anonymous_access(expires_at);
CREATE INDEX idx_anonymous_access_token_id ON anonymous_access(token_id);

-- RLS
ALTER TABLE anonymous_access ENABLE ROW LEVEL SECURITY;

-- Política
CREATE POLICY "System can manage anonymous access" ON anonymous_access
  FOR ALL USING (true);

-- Função para verificar acesso anônimo
CREATE OR REPLACE FUNCTION check_anonymous_access(p_session_id TEXT)
RETURNS TABLE (
  has_access BOOLEAN,
  expires_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    CASE WHEN aa.expires_at > NOW() THEN true ELSE false END as has_access,
    aa.expires_at
  FROM anonymous_access aa
  WHERE aa.session_id = p_session_id
  LIMIT 1;
  
  -- Se não encontrou, retornar sem acesso
  IF NOT FOUND THEN
    RETURN QUERY SELECT false as has_access, NULL::TIMESTAMP WITH TIME ZONE as expires_at;
  END IF;
  
  -- Atualizar último acesso
  UPDATE anonymous_access
  SET last_access_at = NOW(), access_count = access_count + 1
  WHERE session_id = p_session_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Função para limpar acessos expirados
CREATE OR REPLACE FUNCTION cleanup_expired_anonymous_access()
RETURNS INTEGER AS $$
DECLARE
  deleted_count INTEGER;
BEGIN
  DELETE FROM anonymous_access
  WHERE expires_at < NOW() - INTERVAL '7 days'; -- Manter por 7 dias após expirar para logs
  
  GET DIAGNOSTICS deleted_count = ROW_COUNT;
  RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Comentários
COMMENT ON TABLE anonymous_access IS 'Acessos anônimos ativados via token do app';
COMMENT ON COLUMN anonymous_access.session_id IS 'ID único da sessão anônima';
COMMENT ON COLUMN anonymous_access.token_id IS 'Token que gerou este acesso';
COMMENT ON FUNCTION check_anonymous_access IS 'Verifica se uma sessão anônima tem acesso válido';