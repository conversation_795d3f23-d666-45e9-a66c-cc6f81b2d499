-- <PERSON><PERSON><PERSON><PERSON> tabela anonymous_access para não exigir token_id
ALTER TABLE anonymous_access 
ALTER COLUMN token_id DROP NOT NULL;

-- <PERSON><PERSON><PERSON> tabela activation_logs se não existir
CREATE TABLE IF NOT EXISTS activation_logs (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id),
  device_id TEXT,
  ip_address TEXT,
  user_agent TEXT,
  activation_type TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  metadata JSONB
);

-- Índices
CREATE INDEX IF NOT EXISTS idx_activation_logs_user_id ON activation_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_activation_logs_created_at ON activation_logs(created_at);
CREATE INDEX IF NOT EXISTS idx_activation_logs_activation_type ON activation_logs(activation_type);

-- RLS
ALTER TABLE activation_logs ENABLE ROW LEVEL SECURITY;

-- Política para permitir inserções do sistema
CREATE POLICY "System can insert activation logs" ON activation_logs
  FOR INSERT WITH CHECK (true);

-- Política para usuários verem seus próprios logs
CREATE POLICY "Users can view own logs" ON activation_logs
  FOR SELECT USING (auth.uid() = user_id);

-- Comentários
COMMENT ON TABLE activation_logs IS 'Logs de ativação de assinaturas';
COMMENT ON COLUMN activation_logs.activation_type IS 'Tipo de ativação: app_authenticated, simple_link, manual, etc';