-- Create mobile_app_config table
CREATE TABLE IF NOT EXISTS public.mobile_app_config (
  id text PRIMARY KEY DEFAULT 'default',
  android_url text,
  ios_url text,
  qr_code_url text,
  tutorial_video_url text,
  tutorial_video_file text,
  android_badge_url text DEFAULT 'https://play.google.com/intl/en_us/badges/static/images/badges/es_badge_web_generic.png',
  ios_badge_url text DEFAULT 'https://developer.apple.com/assets/elements/badges/download-on-the-app-store.svg',
  ios_available boolean DEFAULT false,
  android_available boolean DEFAULT true,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now()
);

-- Insert default configuration
INSERT INTO public.mobile_app_config (
  id,
  android_url,
  ios_url,
  qr_code_url,
  android_available,
  ios_available
) VALUES (
  'default',
  'https://play.google.com/store/apps/details?id=com.streamplus.espana',
  '#',
  'https://play.google.com/store/apps/details?id=com.streamplus.espana',
  true,
  false
) ON CONFLICT (id) DO NOTHING;

-- Add RLS policies
ALTER TABLE public.mobile_app_config ENABLE ROW LEVEL SECURITY;

-- Allow public read access
CREATE POLICY "Public can read mobile app config" ON public.mobile_app_config
  FOR SELECT USING (true);

-- Only service role can update
CREATE POLICY "Service role can update mobile app config" ON public.mobile_app_config
  FOR UPDATE USING (auth.role() = 'service_role');

-- Create function to update updated_at
CREATE OR REPLACE FUNCTION update_mobile_app_config_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to auto-update updated_at
CREATE TRIGGER update_mobile_app_config_timestamp
  BEFORE UPDATE ON public.mobile_app_config
  FOR EACH ROW
  EXECUTE FUNCTION update_mobile_app_config_updated_at();