// Script para debug do carregamento infinito
// Execute no console quando estiver na página com problema

console.clear();
console.log('🔍 DEBUG DE CARREGAMENTO\n');

// 1. Verificar se há indicadores de carregamento
const loadingIndicators = document.querySelectorAll('[class*="loading"], [class*="spinner"], [class*="Loader"], [class*="animate-spin"]');
console.log(`⏳ Indicadores de carregamento encontrados: ${loadingIndicators.length}`);
if (loadingIndicators.length > 0) {
    loadingIndicators.forEach((el, i) => {
        console.log(`  ${i+1}. Classe: ${el.className}`);
        console.log(`     Visível: ${el.offsetParent !== null}`);
    });
}

// 2. Verificar elementos de vídeo
const videos = document.querySelectorAll('video');
console.log(`\n🎥 Elementos <video>: ${videos.length}`);
if (videos.length > 0) {
    videos.forEach((video, i) => {
        console.log(`\nVídeo ${i+1}:`);
        console.log('- src:', video.src || 'VAZIO');
        console.log('- readyState:', video.readyState);
        console.log('- networkState:', video.networkState);
        console.log('- error:', video.error);
    });
}

// 3. Verificar requisições de rede pendentes
console.log('\n🌐 VERIFICAR NA ABA NETWORK:');
console.log('1. Abra a aba Network (F12)');
console.log('2. Procure por requisições pendentes ou falhadas');
console.log('3. Filtro: m3u8, ts, mp4');

// 4. Verificar mensagens de erro
const errorElements = document.querySelectorAll('[class*="error"], [class*="Error"]');
console.log(`\n❌ Elementos de erro: ${errorElements.length}`);
if (errorElements.length > 0) {
    errorElements.forEach((el, i) => {
        if (el.textContent.trim()) {
            console.log(`  ${i+1}. ${el.textContent.trim()}`);
        }
    });
}

// 5. Estado do player
console.log('\n📊 ESTADO DO PLAYER:');
console.log('URL atual:', window.location.href);
console.log('Canal ID:', window.location.pathname.split('/').pop());

// 6. Verificar logs
if (window.__logger) {
    console.log('\n📋 ÚLTIMOS LOGS:');
    const recentLogs = window.__logger.getHistory().slice(-10);
    recentLogs.forEach(log => {
        console.log(`[${log.level}] ${log.tag}: ${log.message}`);
    });
} else {
    console.log('\n⚠️ Logger não disponível');
}

// 7. Tentar encontrar o componente React
console.log('\n⚛️ COMPONENTES REACT:');
const reactFiberKey = Object.keys(document.querySelector('#__next') || {}).find(key => key.startsWith('__react'));
if (reactFiberKey) {
    console.log('✅ React fiber encontrado');
} else {
    console.log('❌ React fiber não encontrado');
}

// 8. Performance
console.log('\n⚡ PERFORMANCE:');
const resources = performance.getEntriesByType('resource').filter(r => r.name.includes('.m3u8') || r.name.includes('.ts'));
console.log(`Recursos de streaming: ${resources.length}`);
resources.forEach(r => {
    console.log(`- ${r.name.split('/').pop()}: ${r.duration.toFixed(0)}ms`);
});

console.log('\n💡 AÇÕES SUGERIDAS:');
console.log('1. Se está carregando infinitamente, recarregue: location.reload()');
console.log('2. Verifique o console do servidor: pode haver erro de CSP');
console.log('3. Tente outro canal para comparar');
console.log('4. Execute: window.location.href = "/hls-test" para testar player isolado');