#!/bin/bash

echo "🚀 StreamPlus España - Modo Debug"
echo "================================="

# Verificar se o servidor já está rodando
if lsof -Pi :3000 -sTCP:LISTEN -t >/dev/null ; then
    echo "⚠️  Servidor já está rodando na porta 3000"
    echo "📌 Parando servidor existente..."
    lsof -ti:3000 | xargs kill -9 2>/dev/null || true
    sleep 2
fi

# Configurar variáveis de ambiente
export DEBUG=true
export NEXT_PUBLIC_DEBUG=true
export NODE_ENV=development

echo "✅ Variáveis de ambiente configuradas:"
echo "   DEBUG=$DEBUG"
echo "   NEXT_PUBLIC_DEBUG=$NEXT_PUBLIC_DEBUG"
echo "   NODE_ENV=$NODE_ENV"

# Limpar cache se existir
if [ -d ".next" ]; then
    echo "🧹 Limpando cache do Next.js..."
    rm -rf .next
fi

echo ""
echo "🔧 Iniciando servidor de desenvolvimento..."
echo "📡 Acesse: http://localhost:3000"
echo ""
echo "🧪 Páginas de teste disponíveis:"
echo "   - http://localhost:3000/test-player (Player de teste)"
echo "   - http://localhost:3000/debug (Console de debug)"
echo "   - http://localhost:3000/browse (Canais IPTV)"
echo ""
echo "💡 Dicas de debug:"
echo "   1. Abra o Console do navegador (F12)"
echo "   2. Execute: cat debug-player-direct.js"
echo "   3. Cole o conteúdo no console"
echo ""

# Iniciar servidor
pnpm dev