// Execute isso no console agora para verificar o player

console.log('🔍 VERIFICANDO PLAYER...\n');

// 1. Ver logs do player
const playerLogs = window.__logger.getHistory('player');
console.log('📺 Total de logs do player:', playerLogs.length);

// Mostrar últimos logs
console.log('\nÚltimos logs do player:');
playerLogs.slice(-10).forEach(log => {
    console.log(`[${log.level}] ${log.message}`, log.data || '');
});

// 2. Verificar elemento de vídeo
const videos = document.querySelectorAll('video');
console.log('\n🎥 Elementos de vídeo:', videos.length);

if (videos.length > 0) {
    const video = videos[0];
    console.log('\nEstado do vídeo:');
    console.log('- src:', video.src);
    console.log('- currentSrc:', video.currentSrc);
    console.log('- readyState:', video.readyState, '(0=nada, 4=pronto)');
    console.log('- paused:', video.paused);
    console.log('- muted:', video.muted);
    console.log('- error:', video.error);
    
    // 3. Tentar tocar
    if (video.paused && video.src) {
        console.log('\n▶️ Tentando tocar o vídeo...');
        video.play().then(() => {
            console.log('✅ Vídeo tocando!');
        }).catch(err => {
            console.log('❌ Erro:', err.message);
            console.log('💡 Tente clicar no botão de play na tela');
        });
    }
}

// 4. Procurar botão de play
const playButton = document.querySelector('button svg.fill-white')?.closest('button');
if (playButton) {
    console.log('\n🎮 Botão de play encontrado!');
    console.log('Clique nele ou execute: document.querySelector("button svg.fill-white").closest("button").click()');
} else {
    console.log('\n⚠️ Botão de play não encontrado');
}

// 5. Verificar se há overlay de trial
const trialOverlay = document.querySelector('[class*="trial"]');
if (trialOverlay) {
    console.log('\n⏱️ Trial overlay detectado - pode estar bloqueando o player');
}